<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.wosai.upay</groupId>
        <artifactId>merchant-contract-job</artifactId>
        <version>4.31.8-SNAPSHOT</version>
    </parent>


    <modelVersion>4.0.0</modelVersion>
    <artifactId>merchant-contract-job-war</artifactId>
    <groupId>com.wosai.upay</groupId>
    <version>4.31.8-SNAPSHOT</version>
    <name>merchant-contract-job-war</name>
    <description>merchant-contract-job-war</description>
    <packaging>jar</packaging>

    <properties>
        <nextgen.version>2.0-SNAPSHOT</nextgen.version>
        <!--        springboot引入的是3.14.9 oss引入的是4.10.0，强制升级一下-->
        <okhttp3.version>4.10.0</okhttp3.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wosai.trade</groupId>
                <artifactId>manage-api</artifactId>
                <version>1.13.9</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <dependencies>

        <dependency>
            <groupId>com.wosai.trade</groupId>
            <artifactId>manage-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
            <version>2.2.4-alpha</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>pay-business-open-api</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.sales</groupId>
                    <artifactId>sales-system-gateway-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-transaction-api</artifactId>
            <version>1.8.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-poi-api</artifactId>
            <version>1.54.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.sales</groupId>
                    <artifactId>sales-system-gateway-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>crm-databus</artifactId>
                    <groupId>com.wosai.sales</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.app</groupId>
            <artifactId>merchant-user-api</artifactId>
            <version>1.1.7-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>bouncycastle</groupId>
                    <artifactId>bcprov-jdk15</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>aop-gateway-api</artifactId>
            <version>1.8.34</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-annotation</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.pub</groupId>
            <artifactId>alipay-authinto-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alipay</groupId>
                    <artifactId>alipay-sdk</artifactId>
                </exclusion>
            </exclusions>
            <version>1.8-rc1</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.bsm</groupId>
            <artifactId>finance-backend-api</artifactId>
            <version>1.0.7-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-wallet-api</artifactId>
            <version>1.1.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>shouqianba-withdraw-service-api</artifactId>
            <version>1.1.8</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-databus-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.risk</groupId>
            <artifactId>shouqianba-risk-api</artifactId>
            <version>1.8.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-annotation</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.risk</groupId>
            <artifactId>risk-disposal-api</artifactId>
            <version>1.0.4-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.common</groupId>
                    <artifactId>wosai-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>tk.mybatis</groupId>
                    <artifactId>mapper-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!-- 监控 -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>bank-info-api</artifactId>
            <version>1.4.13</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay.bank</groupId>
            <artifactId>merchant-bank-service-api</artifactId>
            <version>1.14.5</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-annotation</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>merchant-contract-job-api</artifactId>
                    <groupId>com.wosai.upay</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>apollo-client</artifactId>
            <version>2.2.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>vault-sdk</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--mysql-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.alibaba/druid -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.1.10</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.avro/avro -->
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro</artifactId>
            <version>1.8.2</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-service-api</artifactId>
            <version>0.3.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>merchant-contract-job-api</artifactId>
            <version>4.31.8-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>merchant-center-api</artifactId>
                    <groupId>com.wosai.mc</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
            <version>3.9.29</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>notice-api</artifactId>
            <version>1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>app-push-api</artifactId>
            <version>1.1.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.nextgen</groupId>
                    <artifactId>rest-helper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>app-backend-api</artifactId>
            <version>1.1.26-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.nextgen</groupId>
                    <artifactId>rest-helper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>business-log-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sp</groupId>
            <artifactId>business-logstash-api</artifactId>
            <version>1.18.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-extension</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-validation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>upay-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>shouqianba-merchant-api</artifactId>
            <version>1.5.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.operation</groupId>
            <artifactId>merchant-activity-api</artifactId>
            <version>1.0.7-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>merchant-contract-api</artifactId>
                    <groupId>com.wosai.upay</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliyun.dts</groupId>
            <artifactId>dts-subscribe-sdk</artifactId>
            <version>4.6.27.12.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>kafka-avro-serializer</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4.4</version>
        </dependency>
        <dependency>
            <groupId>com.jcabi</groupId>
            <artifactId>jcabi-aspects</artifactId>
            <version>0.22.2</version>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.3.2</version>
        </dependency>


        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>remit-gateway-api</artifactId>
            <version>0.0.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-side-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>0.9.10</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>wosai-database-instrumentation-springboot</artifactId>
            <version>5.0.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>apollo-client</artifactId>
                    <groupId>com.ctrip.framework.apollo</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.aliyun.openservices</groupId>-->
        <!--            <artifactId>aliyun-log</artifactId>-->
        <!--            <version>0.6.7</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>com.google.protobuf</groupId>-->
        <!--                    <artifactId>protobuf-java</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>fastjson</artifactId>-->
        <!--                    <groupId>com.alibaba</groupId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>customer-relation-api</artifactId>
            <version>1.1.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.sales</groupId>
                    <artifactId>sales-system-gateway-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>merchant-business-open-api</artifactId>
                    <groupId>com.wosai.sales</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>merchant-audit-api</artifactId>
            <version>1.1.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>merchant-enrolment-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliyun.oss</groupId>
                    <artifactId>aliyun-sdk-oss</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.oss</groupId>
                    <artifactId>oss-service-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>boss-circle-user-api</artifactId>
            <version>3.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-annotation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-extension</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>core-crypto-api</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>core-crypto-mybatis-sdk</artifactId>
            <version>0.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis.spring.boot</groupId>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.app</groupId>
            <artifactId>authcode-api</artifactId>
            <version>0.1.0.x-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-validation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.common</groupId>
                    <artifactId>wosai-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.197</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.wosai.mc</groupId>
            <artifactId>merchant-center-api</artifactId>
            <version>1.14.25</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>cua-common</artifactId>
                    <groupId>com.shouqianba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.data</groupId>
            <artifactId>crow-api</artifactId>
            <version>0.0.37</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>6.6</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>logging-api</artifactId>
            <version>1.4.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-logback-1.x</artifactId>
            <version>1.10.0</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-metrics</artifactId>
            <version>1.1.8</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-trace</artifactId>
            <version>1.1.15</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.task</groupId>
            <artifactId>sp-task-api</artifactId>
            <version>1.6.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-plus-extension</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.biz</groupId>
            <artifactId>merchant-level-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>upay-grayscale-api</artifactId>
            <version>1.0.4-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>aliyun-sdk-log</artifactId>
            <version>1.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>agreement-manage-api</artifactId>
            <version>1.1.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.cua</groupId>
            <artifactId>brand-business-api</artifactId>
            <version>1.3.13</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-databus-api</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-databus</artifactId>
            <version>1.1.8</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-databus-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>campus-center-api</artifactId>
            <version>1.3.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.market</groupId>
                    <artifactId>merchant-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.12.3</version>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.8.1</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.3.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.jsqlparser</groupId>
                    <artifactId>jsqlparser</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.assistant</groupId>
            <artifactId>sales-system-backend-api</artifactId>
            <version>0.14.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>crm-todo-api</artifactId>
            <version>0.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.risk</groupId>
            <artifactId>sp-databus-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <artifactId>lakala-proxy-api</artifactId>
            <groupId>com.wosai.upay</groupId>
            <version>1.2.14</version>
        </dependency>


        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>merchant-contract-access-api</artifactId>
            <version>1.7.16</version>
            <exclusions>
                <exclusion>
                    <artifactId>cua-common</artifactId>
                    <groupId>com.shouqianba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>sales-system-gateway-api</artifactId>
                    <groupId>com.wosai.sales</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>dc-service-api</artifactId>
            <version>1.9.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-gateway-api</artifactId>
            <version>1.0.0</version>
        </dependency>


        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>1.62</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.62</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.6</version>
        </dependency>

        <!--        中投科信提供的demo中的包,手动上传到maven仓库中-->
        <dependency>
            <groupId>com.cfca</groupId>
            <artifactId>sadk</artifactId>
            <version>3.2.1.3</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.operator</groupId>
            <artifactId>jupiter-api</artifactId>
            <version>1.3.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.cua</groupId>
            <artifactId>kafka-msg-entity</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
    </dependencies>


    <build>
        <finalName>merchant-contract-job</finalName>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>
                <executions>
                    <execution>
                        <id>jacoco-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <excludes>
                                <exclude>**/model/**.class</exclude>
                                <exclude>com/wosai/upay/job/model</exclude>
                                <exclude>**/mapper/**.class</exclude>
                                <exclude>com/wosai/upay/job/mapper</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>post-unit-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <excludes>
                                <exclude>**/model/**.class</exclude>
                                <exclude>com/wosai/upay/job/model</exclude>
                                <exclude>**/mapper/**.class</exclude>
                                <exclude>com/wosai/upay/job/mapper</exclude>
                                <exclude>**/exception/**.class</exclude>
                                <exclude>com/wosai/upay/job/exception</exclude>
                                <exclude>**/avro/**.class</exclude>
                                <exclude>com/wosai/upay/job/avro</exclude>
                                <exclude>**/config/**.class</exclude>
                                <exclude>com/wosai/upay/job/config</exclude>
                                <exclude>**/enume/**.class</exclude>
                                <exclude>com/wosai/upay/job/enume</exclude>
                                <exclude>**/monitor/**.class</exclude>
                                <exclude>com/wosai/upay/job/monitor</exclude>
                                <exclude>**/dao/impl/**.class</exclude>
                                <exclude>com/wosai/upay/job/dao/impl</exclude>
                            </excludes>
                            <dataFile>target/jacoco.exec</dataFile>
                            <outputDirectory>target/jacoco-ut</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>utf-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <!-- avro-maven-pluxgin -->
            <plugin>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro-maven-plugin</artifactId>
                <version>1.8.2</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>schema</goal>
                        </goals>
                        <configuration>
                            <sourceDirectory>${project.basedir}/src/main/resources/avro/</sourceDirectory>
                            <outputDirectory>${project.basedir}/src/main/java</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>5.1.46</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.wosai.upay.job.Application</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>wosai-logging-maven-plugin</artifactId>
                <version>1.2.0-SNAPSHOT</version>
                <configuration>
                    <turboFilters>
                        <turboFilter>
                            <filterClass>com.wosai.upay.job.util.TraceLogFilter</filterClass>
                        </turboFilter>
                    </turboFilters>
                    <enableCallerData>true</enableCallerData>
                    <!-- 本地 Pattern 需要自己配置，线上JSON自带有tid -->
                    <patternLayout>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%tid] %-5level %logger{36}.%M - %msg%n
                    </patternLayout>
                    <profiles>
                        <profile>
                            <name>vpc,prod</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_JSON</ref> <!-- 以JSON格式输出到控制台，适用于线上ACK -->
                            </references>
                        </profile>
                        <profile>
                            <name>beta,beta_copy</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_JSON</ref>
                            </references>
                        </profile>
                        <profile>
                            <name>default,dev,beta</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_PATTERN</ref> <!-- 以字符串格式化形式输出到控制台，即标准输出，适用于本地开发 -->
                            </references>
                        </profile>
                    </profiles>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-logback-spring</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!--<distributionManagement>-->
    <!--<repository>-->
    <!--<id>deployment</id>-->
    <!--<name>Internal Releases</name>-->
    <!--<url>http://maven.wosai-inc.com/nexus/content/repositories/releases/</url>-->
    <!--</repository>-->
    <!--<snapshotRepository>-->
    <!--<id>deployment</id>-->
    <!--<name>Internal Snapshots</name>-->
    <!--<url>http://maven.wosai-inc.com/nexus/content/repositories/snapshots/</url>-->
    <!--</snapshotRepository>-->
    <!--</distributionManagement>-->
</project>