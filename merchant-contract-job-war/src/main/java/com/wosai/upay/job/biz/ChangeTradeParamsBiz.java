package com.wosai.upay.job.biz;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.DeleteStatusEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.service.WxStoreService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.core.bean.request.TradeExtConfigQueryRequest;
import com.wosai.upay.core.bean.response.TradeExtConfigQueryResponse;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.acquirer.IAcquirerBiz;
import com.wosai.upay.job.externalservice.coreb.TradeConfigClient;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.biz.provider.McProviderBiz;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.wosai.upay.job.service.TaskResultServiceImpl.BELONG_LKLV3;

/**
 * 切换交易参数处理类
 *
 * <AUTHOR>
 * @date 2021-04-09
 */
@Component
@Slf4j
public class ChangeTradeParamsBiz {

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private WxStoreService wxStoreService;
    @Autowired
    SubBizParamsBiz subBizParamsBiz;
    @Autowired
    private McProviderBiz mcProviderBiz;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private TradeConfigClient tradeConfigClient;

    /**
     * 切换交易参数
     *
     * @param merchantProviderParamsId
     * @param fee
     * @param sync                     是否使用同步方式配置appid
     * @return
     */
    public boolean changeTradeParams(String merchantProviderParamsId, String fee, boolean sync, String tradeAppId) {
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPrimaryKey(merchantProviderParamsId);
        return changeTradeParams(merchantProviderParams, fee, sync, tradeAppId);
    }

    /**
     * 切换交易参数
     *
     * @param merchantProviderParams
     * @param fee
     * @param sync                   是否使用同步方式配置appid
     * @param tradeAppId             业务方
     * @return
     */
    public boolean changeTradeParams(MerchantProviderParams merchantProviderParams, String fee, boolean sync, String tradeAppId) {
        if (Objects.nonNull(merchantProviderParams) && merchantProviderParams.paramsIsDisabled()) {
            log.error("支付源商户号已被禁用,无法切换交易参数 merchantSn:{}, payMerchantId:{}", merchantProviderParams.getMerchant_sn(), merchantProviderParams.getPay_merchant_id());
            throw new ContractBizException("支付源商户号已被禁用");
        }
        BasicProvider provider = providerFactory.getProvider(String.valueOf(merchantProviderParams.getProvider()));
        if (provider != null) {
            boolean result = provider.changeTradeParams(merchantProviderParams, fee, sync, tradeAppId);
            final MerchantProviderParams providerParams = Optional.ofNullable(merchantProviderParams).orElseGet(MerchantProviderParams::new);
            final String merchantSn = providerParams.getMerchant_sn();
            final String payMerchantId = providerParams.getPay_merchant_id();
            if (Objects.equals(tradeAppId, subBizParamsBiz.getPayTradeAppId())
                    && !StringUtils.isEmpty(merchantSn) && !StringUtils.isEmpty(payMerchantId)
                    && providerParams.getPayway().equals(PaywayEnum.WEIXIN.getValue())) {
                CompletableFuture.runAsync(() -> wxStoreService.bindNewSubMchId(merchantSn, payMerchantId));
            }
            //终端绑定,门店绑定
            if (BELONG_LKLV3.contains(merchantProviderParams.getProvider())) {
                provider = providerFactory.getProvider(String.valueOf(ProviderEnum.PROVIDER_LAKALA_V3.getValue()));
                merchantProviderParams.setProvider(ProviderEnum.PROVIDER_LAKALA_V3.getValue());
            }
            log.info("切换交易参数商户:{},开始同步终端和门店", merchantSn);
            BasicProvider finalProvider = provider;
            CompletableFuture.runAsync(() -> finalProvider.createProviderTerminal(merchantSn, merchantProviderParams.getProvider()));
            return result;
        }
        return false;
    }

    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private TerminalService terminalService;

    @Autowired
    private StoreService storeService;


    /**
     * 获取商户拉卡拉终端号（最多查询10000个）
     *
     * @param merchantSn 商户号
     * @param merchantId 商户id
     * @return 终端号集合
     */
    public Set<String> listPartLklTerminalNos(String merchantSn, String merchantId) {
        Set<String> lklTerminalNos = new HashSet<>();
        getLklTerminalIdsByMerchantSn(merchantSn).ifPresent(lklTerminalNos::add);
        lklTerminalNos.addAll(listLklTerminalIdsByStoreSns(merchantId));
        lklTerminalNos.addAll(listLklTerminalIdsByTerminalSns(merchantId));
        return lklTerminalNos;
    }

    private Optional<String> getLklTerminalIdsByMerchantSn(String merchantSn) {
        TradeExtConfigQueryRequest req = new TradeExtConfigQueryRequest();
        req.setSn(merchantSn);
        req.setProvider(ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        req.setSnType(TradeExtConfigQueryRequest.SN_TYPE_MERCHANT);
        TradeExtConfigQueryResponse response = tradeConfigService.queryTradeExtConfig(req);
        if (Objects.nonNull(response) && Objects.nonNull(response.getContent())
                && org.apache.commons.lang3.StringUtils.isNotBlank(response.getContent().getTermId())) {
            return Optional.of(response.getContent().getTermId());
        }
        return Optional.empty();
    }

    private Set<String> listLklTerminalIdsByTerminalSns(String merchantId) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(10000);
        pageInfo.setPage(1);
        HashMap<String, Object> terminalReqMap = Maps.newHashMap();
        terminalReqMap.put("merchant_id", merchantId);
        terminalReqMap.put("deleted", DeleteStatusEnum.NO_DELETED.getValue());
        ListResult terminalsResult = terminalService.findTerminals(pageInfo, terminalReqMap);
        List<String> terminalSns = Lists.newArrayList();
        if (Objects.nonNull(terminalsResult) && CollectionUtils.isNotEmpty(terminalsResult.getRecords())) {
            for (Map record : terminalsResult.getRecords()) {
                terminalSns.add(MapUtils.getString(record, "sn"));
            }
        }
        terminalSns = terminalSns.stream().filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(terminalSns)) {
            return Collections.emptySet();
        }
        Set<String> rspTerminalNos = new HashSet<>();
        for (String sn : terminalSns) {
            TradeExtConfigQueryRequest req = new TradeExtConfigQueryRequest();
            req.setSn(sn);
            req.setProvider(ProviderEnum.PROVIDER_LAKALA_V3.getValue());
            req.setSnType(TradeExtConfigQueryRequest.SN_TYPE_TERMINAL);
            TradeExtConfigQueryResponse response = tradeConfigService.queryTradeExtConfig(req);
            if (Objects.nonNull(response) && Objects.nonNull(response.getContent())
                    && org.apache.commons.lang3.StringUtils.isNotBlank(response.getContent().getTermId())) {
                rspTerminalNos.add(response.getContent().getTermId());
            }
        }
        return rspTerminalNos;
    }


    private Set<String> listLklTerminalIdsByStoreSns(String merchantId) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(10000);
        pageInfo.setPage(1);
        HashMap<String, Object> storeReqMap = Maps.newHashMap();
        storeReqMap.put("merchant_id", merchantId);
        storeReqMap.put("deleted", DeleteStatusEnum.NO_DELETED.getValue());
        ListResult stores = storeService.findStores(pageInfo, storeReqMap);
        List<String> storeSns = Lists.newArrayList();
        if (Objects.nonNull(stores) && CollectionUtils.isNotEmpty(stores.getRecords())) {
            for (Map record : stores.getRecords()) {
                storeSns.add(MapUtils.getString(record, "sn"));
            }
        }
        storeSns = storeSns.stream().filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeSns)) {
            return Collections.emptySet();
        }
        Set<String> rspTerminalNos = new HashSet<>();
        for (String storeSn : storeSns) {
            TradeExtConfigQueryRequest req = new TradeExtConfigQueryRequest();
            req.setSn(storeSn);
            req.setProvider(ProviderEnum.PROVIDER_LAKALA_V3.getValue());
            req.setSnType(TradeExtConfigQueryRequest.SN_TYPE_STORE);
            TradeExtConfigQueryResponse response = tradeConfigService.queryTradeExtConfig(req);
            if (Objects.nonNull(response) && Objects.nonNull(response.getContent())
                    && org.apache.commons.lang3.StringUtils.isNotBlank(response.getContent().getTermId())) {
                rspTerminalNos.add(response.getContent().getTermId());
            }
        }
        return rspTerminalNos;
    }

    /// endregion 以上代码 用完即删

    public boolean openSmartTradeParams(MerchantProviderParams merchantProviderParams, String fee, boolean sync, String tradeAppId) {
        BasicProvider provider = providerFactory.getProvider(String.valueOf(merchantProviderParams.getProvider()));
        if (Objects.isNull(provider)) {
            return false;
        }
        final String merchantSn = merchantProviderParams.getMerchant_sn();
        //同一时间只能有一个间连通道
        subBizParamsBiz.checkOnlyOneIndirect(merchantSn,
                merchantProviderParams.getProvider());
        final boolean openResult = provider.openSmartTradeParams(merchantProviderParams, fee, sync, tradeAppId);
        if(openResult) {
            //设置清算通道
            try {
                final String acquirer = mcProviderBiz.getAcquirerByProvider(String.valueOf(merchantProviderParams.getProvider()));
                final MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
                tradeConfigClient.updateClearProviderByAcquirer(merchant.getId(), acquirer);
            } catch (Exception e) {
                log.error("清算通道设置失败 商户号:{}",merchantSn,e);
                throw new CommonPubBizException("清算通道设置失败");
            }
        }
        return openResult;
    }


}
