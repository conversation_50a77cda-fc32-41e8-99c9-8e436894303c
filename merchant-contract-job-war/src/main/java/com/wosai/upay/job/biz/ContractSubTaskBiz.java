package com.wosai.upay.job.biz;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.mapper.MultiProviderContractEventMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractSubTaskReq;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.model.eventmsg.EventMsg;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.wosai.upay.job.service.ContractEventServiceImpl.AUDIT_PLATFORM;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/2/4 11:06 上午
 **/

@Component
@Slf4j
public class ContractSubTaskBiz {

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractEventMapper eventMapper;

    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    ComposeAcquirerBiz acquirerBiz;

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    private MultiProviderContractEventMapper multiEventMapper;
    @Autowired
    private SubBizParamsBiz subBizParamsBiz;

    @Autowired
    private RuleContext ruleContext;

    //umb-CF3000053368 为测试环境商户号
    private static final List<String> umbRecommendMerchant = Lists.newArrayList("umb-CF2002323280", "umb-CF2002338720","umb-CF3000053368");

    /**
     * 判断是否要更改银行卡状态
     *
     * @param task
     * @param subTask
     * @return true->需要修改; false->不需要
     */
    public Boolean changeBankStatus(ContractTask task, ContractSubTask subTask) {
        final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(task.getId());
        //银行直连的时候呀不需要改变原有间连银行卡的状态
        if (Objects.nonNull(bankDirectApply)) {
            return umbRecommendMerchant.contains(subTask.getRule_group_id());
        }
        //小微升级V3版本不改变银行卡状态
        if(StrUtil.contains(task.getRule_group_id(), ContractRuleConstants.RULE_GROUP_MICROUPGRADE)) {
            return false;
        }
        // 判断这个是不是结算账户信息变更，而且是从结算账户审批调用refreshMerchantBankAccount生成的event
        ContractEvent contractEvent = eventMapper.selectByTaskId(task.getId());
        if (contractEvent != null && contractEvent.getEvent_type() != null && ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == contractEvent.getEvent_type()) {
            EventMsg eventMsg = JSON.parseObject(contractEvent.getEvent_msg(), EventMsg.class);
            if (eventMsg != null && AUDIT_PLATFORM.equals(eventMsg.getPlatform())) {
                return false;
            }
        }
        boolean changeAcquirerTask = WosaiStringUtils.isNotEmpty(task.getRule_group_id()) && task.getRule_group_id().contains(ContractRuleConstants.CHANGE_ACQUIRER_RULE_GROUP_FEATURE);
        String taskAcquirer = getAcquirerByRuleGroupId(subTask.getRule_group_id());
        boolean sameAcquirer = StringUtils.equalsIgnoreCase(taskAcquirer, acquirerBiz.getMerchantAcquirer(task.getMerchant_sn()));
        if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(task.getType()) && !changeAcquirerTask && checkByMultiEvent(task.getMerchant_sn(), task.getId())) {
            return true;
        } else if (ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equals(task.getType())) {
            if (changeAcquirerTask) {
                return false;
            }
            if (subBizParamsBiz.isInSubBiz(task.getMerchant_sn(), taskAcquirer)) {
                return true;
            }
            return sameAcquirer;
        }
        return false;
    }

    private String getAcquirerByRuleGroupId(String ruleGroupId) {
        try {
            RuleGroup ruleGroup = ruleContext.getRuleGroup(ruleGroupId.replace("change2", ""));
            return ruleGroup.getAcquirer();
        } catch (Exception e) {
            log.error("contractSubTaskBiz getAcquirerByRuleGroupId error, ruleGroupId: {}", ruleGroupId, e);
            return ProviderUtil.switchRuleGroupIdToAcquirer(ruleGroupId);
        }
    }

    private boolean checkByMultiEvent(String merchantSn, Long id) {
        MultiProviderContractEvent event = multiEventMapper.selectMultiEventByMerchantSnAndTaskId(merchantSn, id);
        // 如果 多通道入网事件存在，并且状态已经成功，这个时候不用去改银行卡状态了
        if (id != null && event != null && event.getStatus() == MultiProviderContractEvent.STATUS_SUCCESS) {
            return false;
        }
        return true;
    }

    public Boolean changeBankStatusById(Long taskId, Long subTaskId) {
        if (Objects.isNull(taskId) || Objects.isNull(subTaskId)) {
            return false;
        }
        ContractTask task = contractTaskMapper.selectByPrimaryKey(taskId);
        ContractSubTask subTask = contractSubTaskMapper.selectByPrimaryKey(subTaskId);
        if (Objects.isNull(task) || Objects.isNull(subTask)) {
            return false;
        }
        return changeBankStatus(task, subTask);
    }

    /**
     * 获取商户入网收单机构的成功记录
     *
     * @return
     */
    public ContractSubTask getAcquirerSubTask(String merchantSn, String channel) {
        List<ContractSubTask> subTasks = contractSubTaskMapper.findTaskByRecord(new ContractSubTaskReq().setMerchantSn(merchantSn).setChannel(channel).setStatus(TaskStatus.SUCCESS.getVal()));
        if (StringUtil.listEmpty(subTasks)) {
            return null;
        }
        return subTasks.get(0);
    }

}