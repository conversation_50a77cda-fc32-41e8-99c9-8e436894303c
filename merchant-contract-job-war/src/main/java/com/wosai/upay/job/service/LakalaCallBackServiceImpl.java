package com.wosai.upay.job.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.IdentificationTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.Constants.PreAuthApplyConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.CallBackConstants;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.acquirer.COPCallBackNotice;
import com.wosai.upay.job.providers.PayForProvider;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.Utils;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.enume.LklPicTypeV3;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import com.wosai.upay.side.service.GeneralRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * @Author: jerry
 * @date: 2019/4/10 18:09
 * @Description:拉卡拉回调服务实现(merchant_contract调用)
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class LakalaCallBackServiceImpl implements LakalaCallBackService {

    private final static Logger LOGGER = LoggerFactory.getLogger(LakalaCallBackServiceImpl.class);

    //回调信息
    public static final String CONTRACT_CALLBACK_MSG = "callback_msg";

    public static final String NO_COMMIT = "NO_COMMIT"; //未提交
    public static final String COMMIT = "COMMIT";     //已提交
    public static final String COMMIT_FAIL = "COMMIT_FAIL";     // 提交失败
    public static final String MANUAL_AUDIT = "MANUAL_AUDIT";       // 转人工审核
    public static final String REVIEW_ING = "REVIEW_ING";       // 审核中
    public static final String WAIT_FOR_CONTRACT = "WAIT_FOR_CONTACT";     // 审核通过
    public static final String INNER_CHECK_REJECTED = "INNER_CHECK_REJECTED";   // 审核驳回

    private static List<String> repayForTaskType = Arrays.asList(
            ProviderUtil.CONTRACT_TYPE_INSERT,
            ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT,
            ProviderUtil.CONTRACT_TYPE_UPDATE_BUSINESS_LICENSE
    );
    private static String BANK_CARD_FAILED = "结算卡验证失败";
    public static final String WORK_FLOW_NO = "workFlowNo";
    public static final String REJECTED = "REJECTED";
    public static final String SUCCESS = "SUCCESS";


    @Autowired
    PayForProvider payForProvider;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    GeneralRuleService generalRuleService;
    @Autowired
    TaskResultService taskResultService;
    @Autowired
    MerchantService merchantService;

    @Autowired
    ComposeAcquirerBiz acquirerBiz;

    @Autowired
    ChatBotUtil chatBotUtil;
    @Autowired
    private BlackListBiz blackListBiz;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Value("${lkl.v3.channelno}")
    private String lklv3Channel;

    @Autowired
    TradeConfigService tradeConfigService;
    @Autowired
    MerchantProviderParamsBiz merchantProviderParamsBiz;

    @Autowired
    LklV3Service lklV3Service;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    LklV3ShopTermBiz lklV3ShopTermBiz;

    @Autowired
    private AddAffectStatusSuccessTaskCountBiz addAffectStatusSuccessTaskCountBiz;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    ContractSubTaskBiz contractSubTaskBiz;

    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;
    @Autowired
    private RedisLock redisLock;

    @Autowired
    private UpdateTradeParamsBiz updateTradeParamsBiz;

    @Autowired
    private LklPayMerchantBiz lklPayMerchantBiz;
    @Autowired
    TerminalService terminalService;

    @Autowired
    PreAuthApplyMapper preAuthApplyMapper;

    @Autowired
    private ForeignCardMapper foreignCardMapper;

    @Value("${lklMobilePos.devCode}")
    private String lklMobilePosDevCode;

    @Override
    public ContractSubTask querySubTaskByContractId(String contractId) {
        return contractSubTaskMapper.selectByContractId(contractId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateContractSubTask(boolean isSuccess, String contractId, Map callbackMsg) {
        ContractSubTask contractSubTask = contractSubTaskMapper.selectByContractId(contractId);
        LOGGER.info("contract sub_task lkl result {} contractId {} callbackMsg {}", isSuccess, contractId, callbackMsg);
        if (contractSubTask == null) {
            return false;
        }
        Map resp = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
        callbackMsg.put("callback_time", System.currentTimeMillis());
        ArrayList<Map> callBack = Lists.newArrayList(callbackMsg);
        resp.put(CONTRACT_CALLBACK_MSG, callBack);
        String result = "拉卡拉回调失败";
        int resultStatus = TaskStatus.FAIL.getVal();
        if (isSuccess) {
            result = "拉卡拉回调成功";
            resultStatus = TaskStatus.SUCCESS.getVal();
        }
        int updateResult = contractSubTaskMapper.setSubTaskResult(TaskStatus.PROGRESSING.getVal(), resultStatus, JSON.toJSONString(resp), result, contractSubTask.getId());
        LOGGER.info(" contractId {} updateResult {} ", contractId, updateResult);
        if (updateResult < 1) {
            return false;
        }
        handleAfterSubTaskGetResult(contractSubTask, isSuccess, callBack);
        if (!isSuccess) {
            String memo = BeanUtil.getPropString(callBack, "contractMemo");
            syncBlacklistIfNeed(contractSubTask, memo);
        }
        return true;
    }

    private void updateContractSubTaskV2(boolean isSuccess, String contractId, Map callbackMsg, String contractMemo) {
        ContractSubTask contractSubTask = contractSubTaskMapper.selectByContractId(contractId);
        LOGGER.info("contract sub_task lkl result {} contractId {} callbackMsg {}", isSuccess, contractId, callbackMsg);
        if (Objects.isNull(contractSubTask)) {
            return;
        }
        Map resp = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
        callbackMsg.put("callback_time", System.currentTimeMillis());
        ArrayList<Map> callBack = Lists.newArrayList(callbackMsg);
        resp.put(CONTRACT_CALLBACK_MSG, callBack);
        contractSubTaskMapper.updateByPrimaryKey(
                new ContractSubTask().setId(contractSubTask.getId())
                        .setStatus(isSuccess ? TaskStatus.SUCCESS.getVal() : TaskStatus.FAIL.getVal())
                        .setResponse_body(JSON.toJSONString(resp))
                        .setResult(isSuccess ? "拉卡拉回调成功" : "拉卡拉回调失败")
        );
        Long taskId = contractSubTask.getP_task_id();
        if (contractSubTask.getStatus_influ_p_task() == 1) {
            if (isSuccess) {
                addAffectStatusSuccessTaskCountBiz.addAffectStatusSuccessTaskCount(taskId);
                contractSubTaskMapper.setEnableScheduleByDepId(contractSubTask.getId());
                ContractTask task = contractTaskMapper.selectByPrimaryKey(taskId);
                if (!task.getStatus().equals(TaskStatus.PROGRESSING.getVal())) {
                    taskResultService.changeStatusAndResultV2(taskId, contractSubTask.getId(), task.getStatus(), null, false);
                }
            } else {
                Map reMsg = CollectionUtil.hashMap("channel", ProviderUtil.LKL_CALLBACK_CHANNEL, "message", callBack, "result", contractMemo);
                taskResultService.changeStatusAndResultV2(taskId, contractSubTask.getId(), TaskStatus.FAIL.getVal(), JSON.toJSONString(reMsg), false);
                //lkl回调 : 影响主任务状态的子任务,也向风控抛出工单 + 钉钉通知
                syncBlacklistIfNeed(contractSubTask, contractMemo);
            }
        } else {
            if (isSuccess) {
                contractSubTaskMapper.setEnableScheduleByDepId(contractSubTask.getId());
            } else {
                // 如果有子任务依赖于 拉卡拉的这条子任务，并且该子任务是影响主任务状态的 需要将总任务置为失败
                List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectByDependTaskId(contractSubTask.getId());
                if (WosaiCollectionUtils.isNotEmpty(contractSubTasks) && contractSubTasks.stream().anyMatch(r -> r.getStatus_influ_p_task() == 1)) {
                    Map reMsg = CollectionUtil.hashMap("channel", ProviderUtil.LKL_CALLBACK_CHANNEL, "message", callBack, "result", contractMemo);
                    taskResultService.changeStatusAndResultV2(taskId, contractSubTask.getId(), TaskStatus.FAIL.getVal(), JSON.toJSONString(reMsg), false);
                }
                syncBlacklistIfNeed(contractSubTask, contractMemo);
            }

        }
    }

    //向风控抛出工单 + ding (如有需要)
    private void syncBlacklistIfNeed(ContractSubTask contractSubTask, String contractMemo) {
        if (!StringUtils.isEmpty(contractMemo)) {
            blackListBiz.syncBlacklist(contractSubTask, contractSubTask.getMerchant_sn(), null, contractMemo);
        }
    }

    /**
     * 子任务获取到结果后 通用的后续逻辑处理方法
     * 后续抽出其它都调用这个通用方法
     */
    private void handleAfterSubTaskGetResult(ContractSubTask subTask, boolean isSuccess, ArrayList<Map> errorMsg) {
        if (subTask.getStatus_influ_p_task() != 1) {
            return;
        }
        Long taskId = subTask.getP_task_id();
        //子任务获取到最终结果后,更新父任务状态
        if (isSuccess) {
            addAffectStatusSuccessTaskCountBiz.addAffectStatusSuccessTaskCount(taskId);
            contractSubTaskMapper.setEnableScheduleByDepId(subTask.getId());
            ContractTask task = contractTaskMapper.selectByPrimaryKey(taskId);
            if (!task.getStatus().equals(TaskStatus.PROGRESSING.getVal())) {
                taskResultService.changeStatusAndResultV2(taskId, subTask.getId(), task.getStatus(), null, false);
            }
        } else {
            String result = MapUtils.getString(errorMsg.get(0), LakalaBusinessFileds.CONTRACT_MEMO);
            Map reMsg = CollectionUtil.hashMap("channel", ProviderUtil.LKL_CALLBACK_CHANNEL, "message", errorMsg, "result", result);
            taskResultService.changeStatusAndResultV2(taskId, subTask.getId(), TaskStatus.FAIL.getVal(), JSON.toJSONString(reMsg), false);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long savePicTaskByContract(String merchantSn, Map<String, Object> request) {
        List<ContractTask> contractTaskList = contractTaskMapper.selectTaskTodoByMerchantSn(merchantSn);
        if (CollectionUtils.isEmpty(contractTaskList)) {
            return 0L;
        }
        ContractSubTask contractSubTask = new ContractSubTask()
                .setStatus(3)
                .setRule_group_id(ProviderUtil.switchAcquirerToRuleGroupId(acquirerBiz.getMerchantAcquirer(merchantSn)))
                .setResult(JSON.toJSONString(request))
                .setMerchant_sn(merchantSn)
                .setP_task_id(contractTaskList.get(0).getId())
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_ATTACHMENT_UPLOADING)
                .setSchedule_status(ScheduleEnum.SCHEDULE_DISABLE.getValue())
                .setRequest_body(JSON.toJSONString(request));

        int count = contractSubTaskMapper.insert(contractSubTask);
        if (count > 0) {
            return contractSubTask.getId();
        }
        return 0L;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long savePicTaskByRisk(String merchantSn, Map<String, Object> request, Integer status) {
        LOGGER.info("savePicTaskByRisk merchantSn{} request{} status{}", merchantSn, request, status);
        Map result = Maps.newHashMap();
        if (TaskStatus.FAIL.getVal().equals(status)) {
            result.put("channel", ProviderUtil.LKL_PROVIDER_CHANNEL);
            result.put("message", "附件上传失败");
        }
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, devCode);
        ContractTask contractTask = new ContractTask().setType("附件上传")
                .setMerchant_sn(merchantSn).setEvent_context(JSON.toJSONString(request))
                .setStatus(status).setAffect_sub_task_count(0).setRule_group_id(ProviderUtil.switchAcquirerToRuleGroupId(acquirerBiz.getMerchantAcquirer(merchantSn)))
                .setAffect_status_success_task_count(0).setMerchant_name(BeanUtil.getPropString(merchant, Merchant.NAME)).setResult(JSON.toJSONString(result));

//        int count = contractTaskMapper.insert(contractTask);
        // TODO 插入contract_task表并根据type和status判断是否发送消息到神策
        final Integer count = contractTaskBiz.insert(contractTask);
        if (count > 0) {
            return contractTask.getId();
        }
        return 0L;
    }


    @Override
    public Boolean savePicTaskByRiskV3(String merchantSn, LklPicTypeV3 type, String url) {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, devCode);
        if (Objects.isNull(merchant)) {
            return false;
        }
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        if (Objects.isNull(acquirerParams)) {
            return false;
        }
        String merInnerNo = acquirerParams.getPay_merchant_id();
        ContractSubTask acquirerSubTask = contractSubTaskBiz.getAcquirerSubTask(merchantSn, AcquirerTypeEnum.LKL_V3.getValue());
        if (Objects.isNull(acquirerSubTask) || !acquirerSubTask.getStatus().equals(TaskStatus.SUCCESS.getVal())) {
            return false;
        }
        String contractId = acquirerSubTask.getContract_id();
        Map context = CollectionUtil.hashMap(CommonModel.MERCHANT_SN, merchantSn, LakalaConstant.MERINNERNO, merInnerNo, LakalaConstant.CONTRACTID, contractId, CommonModel.TYPE, type, CommonModel.DATA, url);
        // CUA-10635 商户名称截取40个字符
        ContractTask task = new ContractTask().setMerchant_sn(merchantSn).setMerchant_name(Utils.substring(merchant.getName(), 40)).setType(ProviderUtil.CONTRACT_TYPE_ATTACH_UPLOAD).setStatus(0)
                .setRule_group_id(McConstant.RULE_GROUP_LKLV3).setAffect_sub_task_count(1).setAffect_status_success_task_count(0).setEvent_context(JSON.toJSONString(context));
        contractTaskMapper.insert(task);
        ContractSubTask subTask = new ContractSubTask().setP_task_id(task.getId()).setMerchant_sn(merchantSn)
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_FILE_SUPPLY).setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue()).setSchedule_dep_task_id(0L)
                .setStatus(0).setStatus_influ_p_task(1).setContract_rule(McConstant.ACQUIRER_LKLV3).setRule_group_id(McConstant.ACQUIRER_LKLV3);
        contractSubTaskMapper.insert(subTask);
        return null;
    }

    @Override
    public Map contractV3CallBack(Map callbackMsg) {
        //cop回调通知
        if (callbackMsg.containsKey(WORK_FLOW_NO)) {
            //其中REJECTED和SUCCESS是终态
            COPCallBackNotice copCallBackNotice = JSONObject.parseObject(JSONObject.toJSONString(callbackMsg), COPCallBackNotice.class);
            String workFlowNo = copCallBackNotice.getWorkFlowNo();
            return CollectionUtil.hashMap("code", "SUCCESS", "message", String.format("workFlowNo+[%s]信息正确返回", workFlowNo));
        }
        Map result = MapUtils.getMap(callbackMsg, "data");
        if (MapUtils.isEmpty(result)) {
            result = MapUtils.getMap(callbackMsg, "respData");
        }
        String message = MapUtils.getString(result, "contractMemo");
        String contractId = MapUtils.getString(result, LakalaConstant.CONTRACTID);
        String contractStatus = MapUtils.getString(result, "contractStatus");
        ContractSubTask subTask = contractSubTaskMapper.selectByContractId(contractId);
        if (subTask == null) {
            return CollectionUtil.hashMap("code", "SUCCESS", "message", String.format("contractId+[%s]不存在", contractId));
        }

        //lkl可能出现推送重复消息 因此只记录状态为[等待回调]的subtask
        if (!SubTaskStatus.isWaiting(subTask.getStatus())) {
            return CollectionUtil.hashMap("code", "FAIL", "message", String.format("contractId+[%s]信息重复返回", contractId));
        }
        // 回调结果和查询结果可能会同时进入这个方法，在这里根据contractId和status加锁，防止处理两遍
        String key = String.format("contractV3CallBack-%s-%s", contractId, contractStatus);
        if (!redisLock.lock(key, key, 1L)) {
            return CollectionUtil.hashMap("code", "FAIL", "message", String.format("contractId+[%s]信息重复返回", contractId));
        }
        ContractTask task = contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id());
        Integer subType = subTask.getTask_type();
        Map context = JSON.parseObject(task.getEvent_context(), Map.class);
        if (NO_COMMIT.equalsIgnoreCase(contractStatus) || MANUAL_AUDIT.equalsIgnoreCase(contractStatus) || COMMIT.equalsIgnoreCase(contractStatus) || REVIEW_ING.equalsIgnoreCase(contractStatus)) {
            // 审核超过一个月设为失败
            if (subTask.getCreate_at().before(CommonUtil.getLastMonth())) {
                updateContractSubTaskV2(false, contractId, callbackMsg, message);
            } else {
                // 审核中
                delaySubTask(subTask, message);
            }
        } else if (COMMIT_FAIL.equalsIgnoreCase(contractStatus) || INNER_CHECK_REJECTED.equalsIgnoreCase(contractStatus) || INNER_CHECK_REJECTED.equalsIgnoreCase(contractStatus)) {
            reConsider(task, subTask, callbackMsg, message);
        } else if (WAIT_FOR_CONTRACT.equalsIgnoreCase(contractStatus)) {
            //成功
            if (ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(subType) ||
                    ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(subType) ||
                    ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE.equals(subType) ||
                    ProviderUtil.SUB_TASK_TASK_TYPE_STATUS_UPDATE.equals(subType) ||
                    ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(subType) ||
                    ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(subType)
                    || ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(subType)
            ) {
                updateContractSubTaskV2(true, contractId, callbackMsg, message);
                return CollectionUtil.hashMap("code", "SUCCESS", "message", String.format("contractId+[%s]信息正确返回", contractId));
            }
            List<Map> terms = (List) MapUtils.getObject(result, "termDatas");
            final String requestBody = subTask.getRequest_body();
            if (!StringUtils.isEmpty(requestBody)) {
                final Map requestMap = JSONObject.parseObject(requestBody, Map.class);
                Optional.ofNullable(requestMap)
                        .filter(map -> map.containsKey(LakalaConstant.TERMDATA) || map.containsKey("devSerialNo"))
                        .map(map -> {
                            //收钱吧终端主键
                            String devSerialNo;
                            devSerialNo = (String) BeanUtil.getNestedProperty(map, "termData.devSerialNo");
                            if (!StringUtils.isEmpty(devSerialNo)) {
                                return devSerialNo;
                            }
                            //收钱吧门店号
                            return BeanUtil.getPropString(map, "devSerialNo");
                        })
                        .ifPresent(devSerialNo -> {
                            terms.stream()
                                    .filter(term -> !term.containsKey("devSerialNo") && !StringUtils.isEmpty(devSerialNo))
                                    .forEach(term -> term.put("devSerialNo", devSerialNo));
                        });
            }
            Map term = terms.get(0);
            String shopId = MapUtils.getString(term, LakalaConstant.SHOPID);
            LklV3Term v3Term = JSON.parseObject(JSON.toJSONString(term), LklV3Term.class);
            Boolean verifyResult = true;
            //判断是否是简易智能新pos
            Boolean simpleSuperPos = Boolean.FALSE;
            if (context.containsKey("terminalId")) {
                final String terminalId = BeanUtil.getPropString(context, "terminalId");
                Map terminal = terminalService.getTerminalByTerminalId(terminalId);
                final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
                List<String> simpleSuperPosList = Lists.newArrayList(applicationApolloConfig.getSimpleSuperPos());
                simpleSuperPos = simpleSuperPosList.contains(vendorAppAppid);
            }
            //手机POS
            Boolean mobilePos = Boolean.FALSE;
            if (context.containsKey("terminalId") &&
                    StrUtil.contains(MapUtils.getString(context, "terminalId"), MobilePosServiceImpl.LKL_MOBILE_POS_TERMINAL_ID)) {
                mobilePos = Boolean.TRUE;
            }
            //apple pay专用终端
            Boolean applePayTerm = Boolean.FALSE;
            if (context.containsKey("terminalId") &&
                    StrUtil.contains(MapUtils.getString(context, "terminalId"), MobilePosServiceImpl.LKL_B2B_CASHIER_DESK_CB)) {
                applePayTerm = Boolean.TRUE;
            }

            String merInnerNo = MapUtils.getString(result, LakalaConstant.MERINNERNO);
            String merCupNo = MapUtils.getString(result, LakalaConstant.MERCUPNO);
            if (ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(subType)) {
                //小微升级的商户单独处理,终端绑定什么的后续在触发
                if (StrUtil.contains(task.getRule_group_id(), ContractRuleConstants.RULE_GROUP_MICROUPGRADE)) {
                    updateContractSubTaskV2(true, contractId, callbackMsg, message);
                    return CollectionUtil.hashMap("code", "SUCCESS", "message", String.format("contractId+[%s]信息正确返回", contractId));
                }

                if (StringUtil.isEmpty(merCupNo) || StringUtil.isEmpty(merInnerNo)) {
                    chatBotUtil.sendMessageToContractWarnChatBot(String.format("lklv3商户入网返回，contractId[%s]返回参数异常:%s,%s", contractId, merInnerNo, merCupNo));
                    return CollectionUtil.hashMap("code", "FAIL", "message", String.format("contractId[%s]没有返回商户号", contractId));
                }
                MerchantProviderParams channelParams = merchantProviderParamsMapper.getChannelParams(lklv3Channel, merInnerNo);
                boolean switcher = applicationApolloConfig.getLklV3CheckMultiMerchantId();
                if (switcher) {
                    if (Objects.nonNull(channelParams)) {
                        chatBotUtil.sendMessageToContractWarnChatBot(String.format("lklv3入网返回商户号重复，contractId[%s],merInnerNo[%s]", contractId, merInnerNo));
                        return CollectionUtil.hashMap("code", "FAIL", "message", String.format("contractId+[%s]商户号返回重复", contractId));
                    }
                    //拉卡拉进件成功处理
                    LakalaCallBackServiceImpl proxy = (LakalaCallBackServiceImpl) AopContext.currentProxy();
                    proxy.handleContractLklSuccess(task, subTask, v3Term, merCupNo, merInnerNo);
                } else {
                    if (Objects.isNull(channelParams)) {
                        //拉卡拉进件成功处理
                        LakalaCallBackServiceImpl proxy = (LakalaCallBackServiceImpl) AopContext.currentProxy();
                        proxy.handleContractLklSuccess(task, subTask, v3Term, merCupNo, merInnerNo);
                    }
                }
                createMerchantContractResultSubTask(task, subTask);
                lklV3ShopTermBiz.createDefaultStore(shopId, merInnerNo, v3Term, subTask.getId(), subTask.getMerchant_sn());
                String merchantId = (String) BeanUtil.getNestedProperty(context, "merchant.id");
                updateTradeParamsBiz.updateLklTradeParams(merchantId, CollectionUtil.hashMap("lakala_merc_id", merCupNo, "lakala_term_id", v3Term.getTermNo()));
                verifyResult = lklV3ShopTermBiz.verifyShop(shopId, v3Term, subTask.getId(), merInnerNo);
            } else if (ProviderUtil.SUB_TASK_TASK_TYPE_ADD_SHOP.equals(subType) || ProviderUtil.SUB_TASK_TASK_TYPE_BRAND_ADD_SHOP.equals(subType)) {
                verifyResult = lklV3ShopTermBiz.verifyShop(shopId, v3Term, subTask.getId(), merInnerNo);
            } else if (ProviderUtil.SUB_TASK_TASK_TYPE_ADD_TERM.equals(subType) && simpleSuperPos) {
                verifyResult = lklV3ShopTermBiz.verifySimpleSuperPos(shopId, terms, subTask.getId());
                PreAuthApply preAuth = Optional.ofNullable(preAuthApplyMapper.selectLKlByMerchantSn(subTask.getMerchant_sn())).orElseGet(PreAuthApply::new);
                //开通成功
                if (Objects.equals(preAuth.getStatus(), PreAuthApplyConstant.Status.SUCCESS)) {
                    //拉卡拉返回终端数据
                    final List<QueryLklV3MerchantResponse.RespData.TermData> termDataList = JSONObject.parseArray(JSONObject.toJSONString(terms), QueryLklV3MerchantResponse.RespData.TermData.class);
                    final QueryLklV3MerchantResponse.RespData.TermData termData = termDataList.get(0);
                    final String devSerialNo = termData.getDevSerialNo();
                    Map terminal = terminalService.getTerminalByTerminalId(devSerialNo);

                    //拉卡拉返回的终端号
                    String termNo = termData.getTermNo();
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(termNo)) {
                        try {
                            //收钱吧设备号
                            String deviceFingerprint = BeanUtil.getPropString(terminal, Terminal.DEVICE_FINGERPRINT);
                            ContractResponse response = lklV3Service.createFlowWithPreAuthSpecial(subTask.getMerchant_sn(), termNo, deviceFingerprint);
                            //预授权cop推送
                        } catch (Exception exception) {
                            log.error("createFlowWithPreAuthSpecial error", exception);
                        }
                    }
                }
            } else if (ProviderUtil.SUB_TASK_TASK_TYPE_ADD_TERM.equals(subType) && mobilePos) {
                verifyResult = lklV3ShopTermBiz.verifyMobilePos(shopId, terms, subTask.getId());
            } else if (ProviderUtil.SUB_TASK_TASK_TYPE_ADD_TERM.equals(subType) && applePayTerm) {
                String auditId = BeanUtil.getPropString(context, "auditId");
                verifyResult = lklV3ShopTermBiz.verifyApplePayTerm(shopId, terms, subTask, merCupNo, auditId);
            } else if (ProviderUtil.SUB_TASK_TASK_TYPE_ADD_TERM.equals(subType)) {
                verifyResult = lklV3ShopTermBiz.verifyTerm(shopId, v3Term, subTask.getId());
            }
            if (verifyResult) {
                updateContractSubTaskV2(true, contractId, callbackMsg, message);
            } else {
                chatBotUtil.sendMessageToContractWarnChatBot(String.format("lklv3商户入网返回，找不到增商增终任务:%s", contractId));
            }
        }
        return CollectionUtil.hashMap("code", "SUCCESS", "message", String.format("contractId+[%s]信息正确返回", contractId));
    }


    private void delaySubTask(ContractSubTask subTask, String message) {
        ContractSubTask update = new ContractSubTask().setId(subTask.getId()).setPriority(DateUtils.addMinutes(new Date(), 5));
        if (StringUtil.isNotEmpty(message)) {
            update.setResult(message);
        }
        contractSubTaskMapper.updateByPrimaryKey(update);
    }

    /**
     * 当
     * [新增商户入网]||[银行卡变更]时
     * 且
     * 失败原因为 [结算卡验证失败]
     *
     * @param contractTask
     * @param contractSubTask
     */
    private void reConsider(ContractTask contractTask, ContractSubTask contractSubTask, Map callbackMsg, String message) {
        String type = contractTask.getType();
        String contractId = contractSubTask.getContract_id();
        Map context = contractTask.getEventContext();
        if (!repayForTaskType.contains(type) ||
                SubTaskStatus.RECONSIDER.getVal().equals(contractSubTask.getStatus()) ||
                CallBackConstants.RE_CONTRACT.equals(contractTask.getResult())) {
            //手机POS失败
            if (context.containsKey("terminalId") &&
                    StrUtil.contains(MapUtils.getString(context, "terminalId"), MobilePosServiceImpl.LKL_MOBILE_POS_TERMINAL_ID)) {
                //开通中的手机POS置为失败
                final ForeignCard foreignCard = foreignCardMapper.selectByMerchantSnAndCode(contractTask.getMerchant_sn(), lklMobilePosDevCode);
                if (Objects.nonNull(foreignCard) && Objects.equals(foreignCard.getStatus(), ForeignCard.STATUS_PROCESS)) {
                    foreignCardMapper.updateByPrimaryKeySelective(new ForeignCard().setId(foreignCard.getId()).setStatus(ForeignCard.STATUS_FAIL));
                }
            }
            updateContractSubTaskV2(false, contractId, callbackMsg, message);
            return;
        }
        Map bankAccount = MapUtils.getMap(context, ParamContextBiz.KEY_BANK_ACCOUNT);
        Integer idType = MapUtils.getInteger(bankAccount, MerchantBankAccount.ID_TYPE);
        Integer accountType = MapUtils.getInteger(bankAccount, MerchantBankAccount.TYPE);
        ContractSubTask update = new ContractSubTask()
                .setId(contractSubTask.getId())
                .setResult(message)
                .setPriority(DateUtils.addMinutes(new Date(), 5));
        // 对私非身份证
        boolean personalNonIdCard = accountType.equals(BankAccountTypeEnum.PERSONAL.getValue()) && !idType.equals(IdentificationTypeEnum.PRC_ID_CARD.getValue());
        boolean mchNameCanNotChange = applicationApolloConfig.isMchNameCanNotChangeReConsider() && message.contains("命中商户名称不支持变更") && contractSubTask.getStatus_influ_p_task() == 1;
       // 复议
        if (personalNonIdCard || mchNameCanNotChange) {
            LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
            ContractResponse response = lklV3Service.reconsiderMerchant(contractSubTask.getContract_id(), lklV3Param);
            if (response.isSuccess()) {
                update.setStatus(SubTaskStatus.RECONSIDER.getVal());
                contractSubTaskMapper.updateByPrimaryKey(update);
            } else {
                updateContractSubTaskV2(false, contractId, callbackMsg, response.getMessage());
            }
        } else if (message.contains(BANK_CARD_FAILED) && contractSubTask.getStatus_influ_p_task() == 1) {
            payForProvider.produceTask(contractTask.getEventContext(), contractSubTask);
            update.setStatus(SubTaskStatus.REPAYFOR.getVal());
            contractSubTaskMapper.updateByPrimaryKey(update);
        } else {
            updateContractSubTaskV2(false, contractId, callbackMsg, message);
        }
    }

    /**
     * 拉卡拉进件成功后处理
     * 保存providerParams
     * 保存providerTerminal
     * 调用交易
     *
     * @param task
     * @param subTask
     * @param v3Term
     * @param merCupNo
     * @param merInnerNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleContractLklSuccess(ContractTask task, ContractSubTask subTask, LklV3Term v3Term, String merCupNo, String merInnerNo) {
        merchantProviderParamsBiz.saveAcquirerParams(task.getMerchant_sn(), lklv3Channel, ProviderEnum.PROVIDER_LAKALA_V3.getValue(), merCupNo, merInnerNo, subTask.getContract_rule(), subTask.getRule_group_id());
//        //拉卡拉新接口
//        boolean newLklInterface = grayMerchantSnBiz.newLklInterface(task.getMerchant_sn());
//        if (newLklInterface) {
        //关联收单机构终端ID
        providerTerminalBiz.merchantConnectionProviderTerminal(task.getMerchant_sn(), v3Term.getTermNo(), merInnerNo, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
//        }

        lklPayMerchantBiz.createMerRelation(task.getMerchant_sn(), merInnerNo);
    }

    public void createMerchantContractResultSubTask(ContractTask task, ContractSubTask subTask) {
        ContractSubTask queryResultSubTask = new ContractSubTask()
                .setDefault_channel(0)
                .setChange_config(0)
                .setPayway(PaywayEnum.ACQUIRER.getValue())
                .setMerchant_sn(task.getMerchant_sn())
                .setChannel(ProviderUtil.LKL_V3_PROVIDER_CHANNEL)
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UNION_MER_QUERY)
                .setP_task_id(task.getId())
                .setSchedule_dep_task_id(subTask.getId())
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setContract_rule(McConstant.RULE_GROUP_LKLV3)
                .setRule_group_id(McConstant.RULE_GROUP_LKLV3)
                .setPriority(getSubTaskPriority());
        contractSubTaskMapper.insert(queryResultSubTask);
    }

    private Date getSubTaskPriority() {
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        instance.add(Calendar.MINUTE, 5);
        return instance.getTime();
    }

}
