package com.wosai.upay.job.biz.acquirer;

import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.constant.ContractRuleConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 切换收单机构前置检查配置
 *
 * <AUTHOR>
 * @date 2020-08-02
 */
@Component
public class AcquirerChangeCheckBiz {

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    /**
     * 全局开关
     */
    public static final String ALL = "all";
    /**
     * 银行卡检查开关
     */
    public static final String BANK_ACCOUNT_STATUS = "bank-account-status";
    /**
     * 提现检查开关
     */
    public static final String WITHDRAW = "withdraw";
    /**
     * 分账检查开关
     */
    public static final String SHARING = "sharing";

    /**
     * 预授权检查开关
     */
    public static final String DEPOSIT = "deposit";

    /**
     * 临时检查开关
     */
    public static final String TMP_CHECK = "tmp-check";

    /**
     * 检查开关是否打开
     *
     * @param key
     * @return
     */
    public boolean isCheck(String key) {
        Map checkConfig = applicationApolloConfig.getChangeAcquirerCheck();
        return BeanUtil.getPropBoolean(checkConfig, key, true);
    }


    /**
     * 判断是否为切换收单机构
     *
     * @param ruleGroupId
     * @return true -> 是; false -> 不是
     */

    public Boolean acquirerChange(String ruleGroupId) {
        if (StringUtil.isNotEmpty(ruleGroupId) && ruleGroupId.contains(ContractRuleConstants.CHANGE_ACQUIRER_RULE_GROUP_FEATURE)) {
            return true;
        }
        return false;
    }
}
