package com.wosai.upay.job.adapter.apollo.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 商户保活相关 apollo配置
 * <AUTHOR>
 * @date 2025/8/15
 */
@Data
public class KeepAliveConfigModel {

    /**
     * 需要保活的通道
     */
    private List<Integer> keepAliveProviders;
    /**
     * 需要保活的支付源
     */
    private List<Integer> keepAlivePayways;
    /**
     * 最大需要保活的通道数量
     */
    private Integer maxKeepAliveProvidersSize;
    /**
     * 初始保活延迟时间配置
     * key: provider_payway value:天数
     * 配置一个 default，防止取不到
     */
    private Map<String, Integer> periodDelayDay;
    /**
     * 最大延迟时间配置
     */
    private Map<String, Integer> maxDelayDay;
}
