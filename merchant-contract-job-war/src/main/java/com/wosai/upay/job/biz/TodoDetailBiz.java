package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.sales.core.service.UserService;
import com.wosai.sales.merchant.business.model.AppInfoModel;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.sales.todo.constant.LinkObjectEnum;
import com.wosai.sales.todo.constant.NoticeTypeEnum;
import com.wosai.sales.todo.service.TodoDetailService;
import com.wosai.sales.todo.vo.*;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.refactor.dao.LklEcApplyDAO;
import com.wosai.upay.job.refactor.model.entity.LklEcApplyDO;
import entity.common.UserEs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.wosai.upay.job.refactor.service.impl.LklEcApplyServiceImpl.FORMATTER;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/1/3 09:48
 */
@Component
@Slf4j
public class TodoDetailBiz {
    @Resource
    private TodoDetailService todoDetailService;

    @Autowired
    private AopBiz aopBiz;

    @Autowired
    private MerchantService merchantService;

    @Resource
    private LklEcApplyDAO lklEcApplyDAO;

    @Value("${lkl.pos.todo.appUrl}")
    private String posAppUrl;

    @Value("${lkl.foreign.todo.appUrl}")
    private String foreignAppUrl;

    @Autowired
    private CommonAppInfoService commonAppInfoService;

    @Value("${lkl.pos.appId}")
    private String posAppId;

    @Value("${lkl.foreign.appId}")
    private String foreignAppId;


    @Resource
    private UserService userService;


    /**
     * 拉卡拉刷卡业务审核通过创建待办
     * @return
     */
    public String addLKlBankPos(String merchantSn,String expiredTm) {
        try {
            final MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);
            final String merchantId = merchantInfo.getId();
            final Map<String, String> appInfo = getAppInfo(merchantId, posAppId);
            final String userId = BeanUtil.getPropString(appInfo, "user_id");
            final TodoAddReq todoAddReq = new TodoAddReq();
            todoAddReq.setTitle("刷卡业务审核通过，请尽快完成商户签约!");
            todoAddReq.setDescription(String.format("商户%s拉卡拉一体化刷卡已审核通过，请尽快从商户详情-业务开通管理中，继续完成后续业务开通。",merchantSn));
            todoAddReq.setSceneCode("lakala_integrated_card_payment");
            todoAddReq.setPlatformCode("crm");
            final Creator creator = new Creator();
            creator.setId("lakala_integrated_card_payment");
            creator.setName("拉卡拉一体化刷卡");
            todoAddReq.setCreator(creator);
            final LinkObject linkObject = new LinkObject();
            linkObject.setLinkObject(LinkObjectEnum.MERCHANT.getCode());
            linkObject.setLinkObjectSn(merchantSn);
            todoAddReq.setLinkObject(linkObject);
            LocalDateTime localDateTime = LocalDateTime.parse(expiredTm, FORMATTER);
            final long epochMilli = localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

            todoAddReq.setExpiredTm(epochMilli);
            final NoticeTm noticeTm = new NoticeTm();
            noticeTm.setNoticeTm(2);
            noticeTm.setNoticeType(NoticeTypeEnum.HOUR.getCode());

            todoAddReq.setNoticeTm(noticeTm);
            final Executor executor = new Executor();
            log.info("addLKlBankPos merchantSn:{} userId:{}",merchantSn,userId);
            if(StringUtils.isEmpty(userId)) {
                UserEs userEs = aopBiz.getMaintainUser(merchantId);
                executor.setId(userEs.getId());
                executor.setName(userEs.getLinkman());
            }else {
                executor.setId(userId);
                final Map simpleUser = userService.getSimpleUser(userId);
                executor.setName(BeanUtil.getPropString(simpleUser,"linkman"));
            }
            todoAddReq.setExecutors(Lists.newArrayList(executor));
            log.info("addLKlBankPos merchantSn:{},req:{}",merchantSn, JSONObject.toJSONString(todoAddReq));
            final String id = todoDetailService.add(todoAddReq);
            log.info("addLKlBankPos merchantSn:{},resp:{}",merchantSn,id);
            return id;
        } catch (Exception e) {
           log.error("addLKlBankPos error",e);
        }
        return null;
    }



    /**
     * 拉卡拉外卡业务审核通过创建待办
     * @return
     */
    public String addLKlForeignCard(String merchantSn,String expiredTm) {
        try {
            final MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);
            final String merchantId = merchantInfo.getId();
            final Map<String, String> appInfo = getAppInfo(merchantId, foreignAppId);
            final String userId = BeanUtil.getPropString(appInfo, "user_id");

            final TodoAddReq todoAddReq = new TodoAddReq();
            todoAddReq.setTitle("外卡业务审核通过，请尽快完成商户签约!");
            todoAddReq.setDescription(String.format("商户%s拉卡拉一体化外卡已审核通过，请尽快从商户详情-业务开通管理中，继续完成后续业务开通。",merchantSn));
            todoAddReq.setSceneCode("lakala_integrated_external_card");
            todoAddReq.setPlatformCode("crm");
            final Creator creator = new Creator();
            creator.setId("lakala_integrated_external_card");
            creator.setName("拉卡拉一体化外卡");
            todoAddReq.setCreator(creator);
            final LinkObject linkObject = new LinkObject();
            linkObject.setLinkObject(LinkObjectEnum.MERCHANT.getCode());
            linkObject.setLinkObjectSn(merchantSn);
            todoAddReq.setLinkObject(linkObject);
            LocalDateTime localDateTime = LocalDateTime.parse(expiredTm, FORMATTER);
            final long epochMilli = localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            todoAddReq.setExpiredTm(epochMilli);
            final NoticeTm noticeTm = new NoticeTm();
            noticeTm.setNoticeTm(2);
            noticeTm.setNoticeType(NoticeTypeEnum.HOUR.getCode());

            todoAddReq.setNoticeTm(noticeTm);
            final Executor executor = new Executor();
            log.info("addLKlForeignCard userId:",userId);
            if(StringUtils.isEmpty(userId)) {
                UserEs userEs = aopBiz.getMaintainUser(merchantId);
                executor.setId(userEs.getId());
                executor.setName(userEs.getLinkman());
            }else {
                executor.setId(userId);
                final Map simpleUser = userService.getSimpleUser(userId);
                executor.setName(BeanUtil.getPropString(simpleUser,"linkman"));
            }
            todoAddReq.setExecutors(Lists.newArrayList(executor));
            log.info("addLKlForeignCard merchantSn:{},req:{}",merchantSn, JSONObject.toJSONString(todoAddReq));
            final String id = todoDetailService.add(todoAddReq);
            log.info("addLKlForeignCard merchantSn:{},resp:{}",merchantSn,id);
            return id;
        } catch (Exception e) {
            log.error("addLKlForeignCard error",e);
        }
        return null;
    }


    /**
     * 取消待办
     * @return
     */
    public void cancel(String id) {
        try {
           if(!StringUtils.isEmpty(id)) {
               todoDetailService.cancel(id);
               log.info("cancel success id:{}",id);
           }
        } catch (Exception e) {
            log.error("cancel error ",e);
        }
    }


    public void cancelByMerchantSn(String merchantSn,String devCode) {
        try {
            final Optional<LklEcApplyDO> apply = lklEcApplyDAO.getApply(merchantSn, devCode);
            //不存在
            if(!apply.isPresent()) {
                return;
            }
            final LklEcApplyDO ecApplyDO = apply.get();
            //取消BD的待办
            final String toDOid = BeanUtil.getPropString(ecApplyDO.getExtraMap(), LklEcApplyDO.ExtraMapConst.TO_DO_ID);
            if(!StringUtils.isEmpty(toDOid)) {
                todoDetailService.cancel(toDOid);
                log.info("cancel success id:{}",toDOid);
            }
        } catch (Exception e) {
            log.error("cancel error ",e);
        }
    }


    public Map<String,String> getAppInfo(String merchantId, String appId) {
        ListResult listResult = commonAppInfoService.findAppInfos(new PageInfo(1, 10),
                CollectionUtil.hashMap(AppInfoModel.MERCHANT_ID, merchantId
                        , AppInfoModel.APP_ID, appId));
        List<Map> records = listResult.getRecords();
        if(CollectionUtils.isEmpty(records)) {
            return null;
        }
        final String userId = BeanUtil.getPropString(records.get(0), "user_id");
        final String id = BeanUtil.getPropString(records.get(0), "id");
        return CollectionUtil.hashMap("user_id",userId,"id",id);
    }
}
