package com.wosai.upay.job.xxljob.direct.multievent;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MultiProviderContractEventMapper;
import com.wosai.upay.job.mapper.PayWayConfigChangeMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.model.PayWayConfigChange;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

import static com.wosai.upay.job.util.StringUtil.formatDate;

/**
 * xxl_job_desc: MultiEvent-插入不影响主任务的通道切换任务
 * 如果某个task任务成功了，要去设置默认通道 <p>
 * 一个线程设置默认通道进入到了{@link com.wosai.upay.job.service.TaskResultServiceImpl#setDefaultProvider(ContractTask, String, int, MultiProviderContractEvent)} 中 <p>
 * 一个线程执行不影响主任务的子任务进入到了 {@link com.wosai.upay.job.handlers.ContractSubTaskHandler#successHandle(ContractTask, ContractSubTask, Map, ContractResponse)}中 <p>
 * 可能会出现不影响主任务的子任务没有插入切换通道的任务，此定时任务就是为了补偿这种情况
 *
 * <AUTHOR>
 * @date 2025/4/27
 */
@Slf4j
@Component("MultiEventInsertNoInfluChangeJobHandler")
public class MultiEventInsertNoInfluChangeJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private MultiProviderContractEventMapper multiEventMapper;
    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private PayWayConfigChangeMapper payWayConfigChangeMapper;


    @Override
    public String getLockKey() {
        return "MultiEventInsertNoInfluChangeJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            final List<MultiProviderContractEvent> events = multiEventMapper.selectSuccessEvents(formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
            for (MultiProviderContractEvent event : events) {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                try {
                    // 1. 获取默认通道对应的contract_task.id
                    long setDefaultTaskId;
                    if (event.getSecondary_task_id() == null) {
                        setDefaultTaskId = event.getPrimary_task_id();
                    } else {
                        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(event.getMerchant_sn());
                        if (ProviderUtil.switchRuleGroupIdToAcquirer(event.getPrimary_group_id()).equals(contractStatus.getAcquirer())) {
                            setDefaultTaskId = event.getPrimary_task_id();
                        } else {
                            setDefaultTaskId = event.getSecondary_task_id();
                        }
                    }
                    // 2. 查询不影响主任务并且开通成功的子任务
                    List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectNetInNoInfluenceSubTasksByPTaskId(setDefaultTaskId);
                    for (ContractSubTask subTask : contractSubTasks) {
                        PayWayConfigChange payWayConfigChange = payWayConfigChangeMapper.selectConfigChange(subTask.getMerchant_sn(), subTask.getChannel(), subTask.getPayway());
                        // 如果不存在该切通道任务 && 该subTask可以切 && subTask返回报文不为空
                        if (payWayConfigChange == null && subTask.getChange_config() != null && subTask.getChange_config() == 1 && WosaiStringUtils.isNotEmpty(subTask.getResponse_body())) {
                            if (ProviderUtil.TONGLIAN_V2_CHANNEL.equalsIgnoreCase(subTask.getChannel()) && PaywayEnum.UNIONPAY.getValue().equals(subTask.getPayway())) {
                                //tonglianv2 云闪付无子商户号
                                return;
                            }
                            PayWayConfigChange insertConfig = new PayWayConfigChange().
                                    setBody(subTask.getResponse_body())
                                    .setMerchant_sn(subTask.getMerchant_sn())
                                    .setPayway(subTask.getPayway())
                                    .setChannel(subTask.getChannel());
                            payWayConfigChangeMapper.insertSelective(insertConfig);
                            log.info("不影响主任务的子任务切换通道任务补偿成功: {} {}", subTask.getMerchant_sn(), subTask.getPayway());
                        }
                    }
                } catch (Exception e) {
                    log.error("insertNoInfluencePaywayChange process error", e);
                    chatBotUtil.sendMessageToContractWarnChatBot(event.getMerchant_sn() + "不影响主任务的子任务切换通道任务补偿处理异常 " + ExceptionUtil.getThrowableMsg(e));
                }
            }
        } catch (Exception e) {
            log.error("insertNoInfluencePaywayChange process error", e);
            chatBotUtil.sendMessageToContractWarnChatBot("不影响主任务的子任务切换通道任务补偿处理异常 " + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
