package com.wosai.upay.job.biz.acquirer;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.application.RuleContractRequest;
import com.wosai.upay.job.service.ContractApplicationService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 收单机构切换到拉卡拉
 *
 * <AUTHOR>
 * @date 2020-04-26
 */
@Component("lkl-AcquirerChangeBiz")
public class ChangeToLklBiz extends AbstractIndirectAcquirerChangeBiz {

    @Autowired
    protected ContractApplicationService contractApplicationService;

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_LAKALA.getValue();
    }


    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_LKL_RULE_GROUP;
    }

    @Override
    protected List<MerchantProviderParams> getDefaultChangeParams(McAcquirerChange change) {
        // 查找支付宝、云闪付、翼支付交易参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderIn(Arrays.asList(ProviderEnum.PROVIDER_UION_OPEN.getValue(), ProviderEnum.PROVIDER_NUCC.getValue(), ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue()))
                .andPaywayNotIn(Arrays.asList(PaywayEnum.ACQUIRER.getValue(), PaywayEnum.WEIXIN.getValue()))
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);

        // 微信交易参数
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        wxExample.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue())
                .andChannel_noIn(Arrays.asList("36TB4213315","36TB4213341","36002013293","36T01224211","36TB4213553","36TB4213567"))
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        wxExample.setOrderByClause("ctime desc");
        List<MerchantProviderParams> wxParams = paramsMapper.selectByExampleWithBLOBs(wxExample);
        if (WosaiCollectionUtils.isEmpty(wxParams)) {
            // 重新报备一个
            CommonResult result = contractApplicationService.contractByRule(
                    new RuleContractRequest()
                            .setRule(ContractRuleConstants.LKL_NORMAL_WEIXIN_RULE)
                            .setMerchantSn(change.getMerchant_sn())
                            .setPlat("changeAcquirer")
                            .setReContract(true)
                            .setConfigParam(false)
            );
            if (!result.isSuccess()) {
                throw new ContractBizException("缺少可用微信子商户号");
            }
            wxParams = paramsMapper.selectByExampleWithBLOBs(wxExample);
            if (WosaiCollectionUtils.isEmpty(wxParams)) {
                throw new ContractBizException("缺少可用微信子商户号");
            }
            params.addAll(wxParams);
            return params;
        }

        // 有多个微信交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams wxParam : wxParams) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(wxParam.getAuth_status())) {
                params.add(wxParam);
                return params;
            }
        }
        params.add(wxParams.get(0));
        return params;
    }

    @Override
    protected void sourceAcquirerPostBiz(McAcquirerChange change) {
        super.sourceAcquirerPostBiz(change);
        super.invalidJdPay(change.getMerchant_id(),change.getTarget_acquirer(), change.getMerchant_sn());
    }
}
