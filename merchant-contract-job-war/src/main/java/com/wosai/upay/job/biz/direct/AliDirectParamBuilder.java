package com.wosai.upay.job.biz.direct;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.mapper.IndustryCodeV2Mapper;
import com.wosai.upay.job.model.DO.AliDirectApply;
import com.wosai.upay.job.model.direct.AliDirectReq;
import com.wosai.upay.merchant.contract.model.directPay.AlipayOpenAgentCreateReq;
import com.wosai.upay.merchant.contract.model.directPay.AlipayOpenAgentFacetofaceSignReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 用于构建支付宝向contract提交的参数
 *
 * <AUTHOR>
 * @date 2020/12/24
 */
@Component
public class AliDirectParamBuilder {

    @Autowired
    private IndustryCodeV2Mapper mapper;

    public AlipayOpenAgentCreateReq buildCreateParam(AliDirectApply apply) {
        AliDirectReq aliDirectReq = JSON.parseObject(apply.getForm_body(), AliDirectReq.class);
        AlipayOpenAgentCreateReq createReq = new AlipayOpenAgentCreateReq();
        AlipayOpenAgentCreateReq.ContactModel contactModel = new AlipayOpenAgentCreateReq.ContactModel();
        //设置联系人信息
        contactModel.setContactName(aliDirectReq.getContact_info().getContact_name());
        contactModel.setContactMobile(aliDirectReq.getContact_info().getContact_phone());
        contactModel.setContactEmail(aliDirectReq.getContact_info().getContact_email());
        createReq.setContactInfo(contactModel);
        //设置支付宝账号
        createReq.setAccount(aliDirectReq.getApp_info().getAccount());
        return createReq;
    }

    public AlipayOpenAgentFacetofaceSignReq buildFaceToFaceSignParam(AliDirectApply apply, Map contextParam) {
        MerchantBusinessLicenseInfo licenseInfo = ((JSONObject)contextParam.get(ParamContextBiz.KEY_BUSINESS_LICENCE)).toJavaObject(MerchantBusinessLicenseInfo.class);
        StotreExtInfoAndPictures pictures = ((JSONObject)contextParam.get(ParamContextBiz.KEY_PICTURES)).toJavaObject(StotreExtInfoAndPictures.class);
        MerchantInfo merchantInfo = ((JSONObject)contextParam.get(ParamContextBiz.KEY_MERCHANT)).toJavaObject(MerchantInfo.class);
        AliDirectReq aliDirectReq = JSON.parseObject(apply.getForm_body(), AliDirectReq.class);
        AlipayOpenAgentFacetofaceSignReq signReq = new AlipayOpenAgentFacetofaceSignReq();
        signReq.setBatchNo(apply.getBatch_no());
        signReq.setSignAndAuth(true);
        signReq.setIndustry(merchantInfo.getIndustry());
        signReq.setSpecialLicensePicUrl(licenseInfo.getTradeLicense());
        signReq.setRate(aliDirectReq.getApp_info().getFee_rate());
        signReq.setBusinessLicenseNo(licenseInfo.getNumber());
        signReq.setBusinessLicensePicUrl(licenseInfo.getPhoto());
        signReq.setBusinessLicenseAuthPicUrl(licenseInfo.getLetter_of_authorization());
        signReq.setLicenseValidity(licenseInfo.getValidity());
        signReq.setShopScenePicUrl(pictures.getIndoorMaterialPhoto().getUrl());
        signReq.setShopSignBoardPicUrl(pictures.getBrandPhoto().getUrl());
        signReq.setShopName(WosaiStringUtils.isNotEmpty(merchantInfo.getBusiness_name()) ? merchantInfo.getBusiness_name() : merchantInfo.getName());
        signReq.setDistrictCode(merchantInfo.getDistrict_code());
        signReq.setDetailAddress(merchantInfo.getStreet_address());
        signReq.setLongitude(merchantInfo.getLongitude());
        signReq.setLatitude(merchantInfo.getLatitude());
        return signReq;
    }
}
