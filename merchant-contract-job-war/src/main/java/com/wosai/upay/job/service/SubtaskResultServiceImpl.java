package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WeixinAuthApplyBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.handlers.ProviderMerchantResultHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MchAuthApplyMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.TaskMchMapper;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractSubTaskReq;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.MchAuthInfo;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.model.RuleItem;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: lishuangqiang
 * @Date: 2019/4/1
 * @Description:
 */
@Service
@AutoJsonRpcServiceImpl
public class SubtaskResultServiceImpl implements SubtaskResultService {

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    ComposeAcquirerBiz acquirerBiz;

    @Autowired
    WeixinAuthApplyBiz weixinAuthApplyBiz;
    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    ProviderMerchantResultHandler providerMerchantResultHandler;
    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Override
    public ContractSubTask getSubtasksByPtaskIdAndChannel(Long p_task_id, String channel) {
        return contractSubTaskMapper.getSubtasksByPtaskIdAndChannel(p_task_id, channel);
    }


    @Override
    public List<ContractSubTask> getSubtasksByMerchantAndPayway(String merchant_sn, Long p_task_id) {
        return contractSubTaskMapper.getSubtasksByMerchantAndPayway(merchant_sn, p_task_id);

    }


    @Override
    public ContractSubTask selectByPrimaryKey(Long id) {
        return contractSubTaskMapper.selectByPrimaryKey(id);
    }

    @Override
    public MchAuthInfo getAuthApplyByTaskId(Long takId) {
        return weixinAuthApplyBiz.getMchAuthApplyByTaskId(takId);
    }

    @Override
    public ContractSubTask getAcquireSubTask(Long pTaskId) {
        List<ContractSubTask> subTasks = contractSubTaskMapper.getAcquireSubTask(pTaskId);
        if (WosaiCollectionUtils.isEmpty(subTasks)) {
            return null;
        }
        if (subTasks.size() == 1) {
            return subTasks.get(0);
        }

        String acquirer = getContractTaskAcquirerByTaskId(pTaskId);
        if (AcquirerTypeEnum.FU_YOU.getValue().equals(acquirer)){
            subTasks = subTasks.stream().filter(x -> x.getContract_rule().equals(AcquirerTypeEnum.FU_YOU.getValue())).collect(Collectors.toList());
            if (WosaiCollectionUtils.isEmpty(subTasks)) {
                return null;
            }
            if (subTasks.size() == 1) {
                return subTasks.get(0);
            }
        }
        if (AcquirerTypeEnum.PAB.getValue().equals(acquirer)){
            subTasks = subTasks.stream().filter(x -> x.getContract_rule().equals(AcquirerTypeEnum.PAB.getValue())).collect(Collectors.toList());
            if (WosaiCollectionUtils.isEmpty(subTasks)) {
                return null;
            }
            if (subTasks.size() == 1) {
                return subTasks.get(0);
            }
        }

        //https://furcas.shouqianba.com/workdetail?id=2023122610340875
        //有两个独立subtask,营业执照更新成功,银行卡代付待回填金额,有时会返回 营业执照更新成功 的subTask,导致crm端无法展示回填金额页面,整个task卡住,改为找一个未成功subtask返回
        Optional<ContractSubTask> first = subTasks.stream().filter(sub -> WosaiStringUtils.equals(acquirer, sub.getChannel()) && sub.getStatus() != 5).findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        //---
        for (ContractSubTask subTask : subTasks) {
            if (acquirer.equals(subTask.getChannel())) {
                return subTask;
            }
        }
        return null;
    }

    private String getContractTaskAcquirerByTaskId(Long taskId) {
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
        if (WosaiStringUtils.isEmpty(contractTask.getRule_group_id())) {
            return acquirerBiz.getMerchantAcquirer(contractTask.getMerchant_sn());
        }
        RuleGroup ruleGroup = ruleContext.getRuleGroup(contractTask.getRule_group_id());
        if (WosaiStringUtils.isNotEmpty(ruleGroup.getAcquirer())) {
            return ruleGroup.getAcquirer();
        } else {
            // 处理通过切换收单机构新增入网的商户
            final List<RuleItem> rules = ruleGroup.getRules();
            if (rules == null || rules.isEmpty()) {
                return acquirerBiz.getMerchantAcquirer(contractTask.getMerchant_sn());
            }
            return rules.get(0).getContractRule().getAcquirer();
        }
    }

    @Override
    public List<ContractSubTask> getContractSubTask(ContractSubTaskReq req) {
        return contractSubTaskMapper.findTasksByMerchantAndPayway(req);
    }

    @Override
    public ContractSubTask getProviderMerchantContractResult(String merchantSn, String contractRule) {
        ContractRule contractRuleDO = ruleContext.getContractRule(contractRule);
        if (Objects.isNull(contractRuleDO)) {
            return null;
        }
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(contractRuleDO.getAcquirer());
        if (Objects.isNull(mcAcquirerDO)) {
            return null;
        }
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, Integer.valueOf(mcAcquirerDO.getProvider()));
        ContractSubTask subTask = contractSubTaskMapper.getProviderQuerySubTask(merchantSn, contractRule);
        if (Objects.isNull(acquirerParams) || Objects.isNull(subTask)) {
            return null;
        }
        BasicProvider provider = providerFactory.getProviderByRule(subTask.getContract_rule());
        com.wosai.upay.merchant.contract.model.ContractResponse response = provider.queryMerchantContractResult(acquirerParams.getProvider_merchant_id());
        syncMerchantContractResult(response, subTask);
        return contractSubTaskMapper.selectByPrimaryKey(subTask.getId());
    }

    private void syncMerchantContractResult(com.wosai.upay.merchant.contract.model.ContractResponse response, ContractSubTask subTask) {
        SubTaskStatus subTaskStatus = SubTaskStatus.toStatus(subTask.getStatus());
        Boolean remoteResult = response.isSuccess();
        Boolean localResult = SubTaskStatus.isSuccess(subTaskStatus);
        if (!remoteResult.equals(localResult)) {
            providerMerchantResultHandler.handleResult(response, subTask);
        }
    }

}
