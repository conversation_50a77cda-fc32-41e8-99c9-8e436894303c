package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSON;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.bank.AccountVerifyEvent;
import com.wosai.upay.bank.model.enume.AccountApplyStatus;
import com.wosai.upay.job.biz.PayForResultBiz;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.PayForTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.PayForTask;
import com.wosai.upay.job.util.JacksonHelperUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Description:账户核验结果事件 其他数据总线事件
 * <AUTHOR>
 * Date 2019/12/24 3:51 下午
 **/
@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class AccountVerifyConsumer extends AbstractDataBusConsumer {

    @Autowired
    private PayForTaskMapper payForTaskMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    private static final String ACCOUNT_VERIFY_TOPIC = "databus.event.bank.account.allin";


    @Autowired
    PayForResultBiz payForResultBiz;

    @KafkaListener(topics = {ACCOUNT_VERIFY_TOPIC}, containerFactory = "dataBusKafkaListenerContainerFactory")
    @Transactional(rollbackFor = Exception.class)
    public void consume(ConsumerRecord<String, GenericRecord> record) {
        handle(record);
    }

    @Override
    protected void doHandleEvent(AbstractEvent event) {
        log.info("start handling  event : {}", JacksonHelperUtils.toJsonString(event));
        if (event instanceof AccountVerifyEvent) {
            AccountVerifyEvent accountVerifyEvent = (AccountVerifyEvent) event;
            String requestNo = accountVerifyEvent.getBusinessId();
            List<PayForTask> payForTasks = payForTaskMapper.selectByRequestFlowNo(requestNo);
            if (CollectionUtils.isEmpty(payForTasks)) {
                log.info("{} 账户核验事件忽略", event);
                return;
            }
            payForTasks.forEach(payFor -> {
                try {
                    ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(payFor.getSub_task_id());
                    if (AccountApplyStatus.SUCCESS.getStatus().equals(accountVerifyEvent.getVerifyStatus())) {
                        payForResultBiz.successHandle(contractSubTask, payFor.getId());
                    } else if (AccountApplyStatus.FAIL.getStatus().equals(accountVerifyEvent.getVerifyStatus())) {
                        payForResultBiz.failHandler(contractSubTask, payFor.getId(), AccountApplyStatus.FAIL.getStatus());
                    } else if (AccountApplyStatus.PAYMENT_FAIL.getStatus().equals(accountVerifyEvent.getVerifyStatus())) {
                        payForResultBiz.failHandler(contractSubTask, payFor.getId(), AccountApplyStatus.PAYMENT_FAIL.getStatus());
                    } else if (AccountApplyStatus.BANK_CHARGEBACK_FAIL.getStatus().equals(accountVerifyEvent.getVerifyStatus())) {
                        payForResultBiz.failHandler(contractSubTask, payFor.getId(), AccountApplyStatus.BANK_CHARGEBACK_FAIL.getStatus());
                    } else if (AccountApplyStatus.WAIT_FOR_VERIFY.getStatus().equals(accountVerifyEvent.getVerifyStatus())) {
                        //do nothing
                        payForResultBiz.needVeriyHandle(contractSubTask, payFor.getId());
                    }
                } catch (Exception e) {
                    log.error("handle AccountVerifyEvent error {}", JSON.toJSONString(payFor), e);
                }
            });

        }
    }
}
