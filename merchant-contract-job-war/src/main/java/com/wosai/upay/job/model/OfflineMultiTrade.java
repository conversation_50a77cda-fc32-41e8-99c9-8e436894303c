package com.wosai.upay.job.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
public class OfflineMultiTrade {
    private Long id;

    private String merchant_sn;

    private String bank_merchant_sn;

    private String provider;

    private String trade_app_id;

    private String terminal_id;

    private Date ctime;

    private Date mtime;

    private String form_body;

    private String extra;

    public Map<String,Object> getExtraMap() {
        return JSONObject.parseObject(extra,Map.class);
    }
}