package com.wosai.upay.job.biz.keepalive.validation.engine;


import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveValidationScenarioEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidationResult;

/**
 * 保活规则校验引擎接口
 */
public interface KeepAliveValidateEngine {

    /**
     * 执行所有适用的规则
     *
     * @param context  商户上下文
     * @param scenario 执行场景
     * @return 聚合执行结果
     */
    KeepAliveValidationResult validate(KeepAliveValidateContext context, KeepAliveValidationScenarioEnum scenario);
}