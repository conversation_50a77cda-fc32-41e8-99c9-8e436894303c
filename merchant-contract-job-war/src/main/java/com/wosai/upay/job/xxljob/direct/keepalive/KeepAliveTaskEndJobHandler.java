package com.wosai.upay.job.xxljob.direct.keepalive;

import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.upay.job.biz.keepalive.KeepAliveTaskBiz;
import com.wosai.upay.job.refactor.dao.ProviderParamsKeepaliveTaskDAO;
import com.wosai.upay.job.refactor.model.bo.KeepAliveTaskResultBO;
import com.wosai.upay.job.refactor.model.entity.ProviderParamsKeepaliveTaskDO;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/23
 */
@Slf4j
@Component("KeepAliveTaskEndJobHandler")
public class KeepAliveTaskEndJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private KeepAliveTaskBiz keepAliveTaskBiz;

    @Autowired
    private ProviderParamsKeepaliveTaskDAO taskDAO;

    @Override
    public String getLockKey() {
        return "KeepAliveTaskEndJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        LocalDate endDate = LocalDate.now().minusDays(1);
        log.info("开始关闭保活任务，批次大小: {}，日期：{}", param.getBatchSize(), endDate);
        // 查询保活中且保活结束日期为指定日期的任务
        List<ProviderParamsKeepaliveTaskDO> providerParamsKeepaliveTaskDOS = taskDAO.selectActiveTasksByEndDate(endDate, param.getBatchSize());
        log.info("查询到 {} 个待关闭的保活任务", providerParamsKeepaliveTaskDOS.size());

        for (ProviderParamsKeepaliveTaskDO providerParamsKeepaliveTaskDO : providerParamsKeepaliveTaskDOS) {
            try {
                log.info("开始关闭保活任务，任务ID: {}, 商户号: {}",
                        providerParamsKeepaliveTaskDO.getTaskId(), providerParamsKeepaliveTaskDO.getMerchantSn());

                KeepAliveTaskStartJobHandler.KeepAliveTaskStartContext context = new KeepAliveTaskStartJobHandler.KeepAliveTaskStartContext();
                context.setTaskDO(providerParamsKeepaliveTaskDO);
                doClose(context);

                log.info("关闭保活任务完成，任务ID: {}", providerParamsKeepaliveTaskDO.getTaskId());
            } catch (Exception e) {
                log.error("处理保活任务关闭异常，任务ID: {}", providerParamsKeepaliveTaskDO.getTaskId(), e);
            }
        }

        log.info("保活任务关闭完成");
    }

    @Timed("商户保活-关闭任务")
    private void doClose(KeepAliveTaskStartJobHandler.KeepAliveTaskStartContext context) {
        ProviderParamsKeepaliveTaskDO taskDO = context.getTaskDO();
        try {
            // 设置任务状态为失败并记录日志
            KeepAliveTaskResultBO resultBO = KeepAliveTaskResultBO.createExecuteFailedResultFromSchedule("KeepAliveTaskEndJobHandler", "未发生交易");
            keepAliveTaskBiz.reCreateKeepAliveTasksWhenFail(taskDO, resultBO);
        } catch (Exception e) {
            // 记录异常日志并更新任务状态
            String errorMessage = String.format("关闭保活任务失败，任务ID: %d，异常信息: %s", taskDO.getTaskId(), e.getMessage());
            log.error(errorMessage, e);
            // 更新任务状态为失败并记录异常信息
            KeepAliveTaskResultBO errorResultBO = KeepAliveTaskResultBO.createScheduleSystemErrorResult("KeepAliveTaskEndJobHandler", errorMessage);
            keepAliveTaskBiz.reCreateKeepAliveTasksWhenFail(taskDO, errorResultBO);
        }
    }
}
