package com.wosai.upay.job.biz.bankDirect.commonImportPreparation;

import org.apache.poi.ss.formula.functions.T;
import scala.annotation.meta.param;

import java.util.List;
import java.util.Map;

/**
 * 导入数据预处理接口
 * 用于处理需要与收单机构进行交互获取信息的场景
 *
 */
public interface ImportPreSetup {

    /**
     * 判断指定机构是否需要预处理
     *
     * @param provider 机构标识
     * @return true如果需要预处理,false如果不需要
     */
    boolean needSetup(String provider);

    /**
     * 与收单机构进行交互获取信息
     *
     * @param param 输入参数
     * @return 处理后的结果
     */
    Map setup(List<String> param);
}
