package com.wosai.upay.job.handlers;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.core.exception.CoreMerchantNotExistsException;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.enume.LklPicTypeV3;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * @Description: 附件上传
 * 目前只接入了 lklv3
 * <AUTHOR>
 * @Date 2021/4/23 5:02 下午
 **/
@Component
@Order(98)
public class SupplyFileSubTaskHandler extends AbstractSubTaskHandler {

    @Autowired
    LklV3Service lklV3Service;

    @Autowired
    ContractTaskBiz contractTaskBiz;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Override
    public boolean supports(ContractTask task, ContractSubTask subTask) {
        if (ProviderUtil.CONTRACT_TYPE_ATTACH_UPLOAD.equalsIgnoreCase(task.getType()) && McConstant.ACQUIRER_LKLV3.equalsIgnoreCase(task.getRule_group_id())) {
            return true;
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doHandle(ContractTask task, ContractSubTask subTask) {
        Map context = JSON.parseObject(task.getEvent_context(), Map.class);
        String type = MapUtils.getString(context, CommonModel.TYPE);
        ContractResponse response = lklV3Service.uploadFileSupply(
                MapUtils.getString(context, LakalaConstant.CONTRACTID),
                MapUtils.getString(context, LakalaConstant.MERINNERNO),
                LklPicTypeV3.toPic(type),
                MapUtils.getString(context, CommonModel.DATA),
                contractParamsBiz.buildContractParamsByContractSubTask(subTask, LklV3Param.class));
        handleResult(response, subTask);
    }


    @Override
    protected void handleError(ContractTask task, ContractSubTask subTask, Exception e) throws Exception {
        //暂时先照抄
        if (e instanceof CoreMerchantNotExistsException) {
            ContractTask updateValue = new ContractTask()
                    .setId(task.getId())
                    .setStatus(6)
                    .setResult(null);
            contractTaskBiz.update(updateValue);
        }
        throw e;
    }


}