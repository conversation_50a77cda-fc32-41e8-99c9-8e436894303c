package com.wosai.upay.job.biz.acquirer;


import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component("zjtlcb-biz")
@Slf4j
public class ZjtlcbAcquireBiz implements IAcquirerBiz{

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private RuleContext ruleContext;

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_ZJTLCB;
    }

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        throw new ContractBizException("商户收单机构目前在浙江泰隆银行，不支持此操作");
    }

    @Override
    public String getNormalWxRule() {
        return ContractRuleConstants.ZJTLCB_NORMAL_WEIXIN_RULE;
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        Optional<MerchantProviderParamsDO> unionParam = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, ProviderEnum.PROVIDER_ZJTLCB.getValue(), PaywayEnum.UNIONPAY.getValue());
        if (unionParam.isPresent()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                    .retry(false)
                    .build();
        }
        ContractTask netInTask = getNetInTask(merchantSn, AcquirerTypeEnum.ZJTLCB.getValue());
        if (Objects.nonNull(netInTask) && (TaskStatus.PROGRESSING.getVal().equals(netInTask.getStatus()) || TaskStatus.PENDING.getVal().equals(netInTask.getStatus()))) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .message("开通中，请稍后重试")
                    .retry(true)
                    .build();
        }
        if (Objects.nonNull(netInTask) && TaskStatus.FAIL.getVal().equals(netInTask.getStatus())) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                    .message("开通失败")
                    .retry(false)
                    .build();
        }
        return UnionPayOpenStatusQueryResp.builder()
                .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                .message("暂时无法开通，联系销售支持")
                .retry(false)
                .build();
    }

    private ContractTask getNetInTask(String merchantSn, String acquirer) {
        List<ContractTask> contractTasks = contractTaskMapper.getContractsBySnAndType(merchantSn, ProviderUtil.CONTRACT_TYPE_INSERT);
        for (ContractTask contractTask : contractTasks) {
            RuleGroup ruleGroup = ruleContext.getRuleGroup(contractTask.getRule_group_id());
            if (ruleGroup.getAcquirer().equals(acquirer)) {
                return contractTask;
            }
        }
        return null;
    }
}
