package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.service.TaskResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Description: （包括主动查询/被动）回调任务处理
 * <AUTHOR>
 * @Date: 2021/3/16 4:49 下午
 */
@Component
@Slf4j
public class CallBackBiz {
    @Autowired
    private TaskResultService taskResultService;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private AddAffectStatusSuccessTaskCountBiz addAffectStatusSuccessTaskCountBiz;


    public void handleAfterSubTask(ContractSubTask subTask, boolean isSuccess, Object errorMsg) {
        if (subTask.getStatus_influ_p_task() != 1) {
            return;
        }
        Long taskId = subTask.getP_task_id();
        //子任务获取到最终结果后,更新父任务状态
        if (isSuccess) {
            addAffectStatusSuccessTaskCountBiz.addAffectStatusSuccessTaskCount(taskId);
            contractSubTaskMapper.setEnableScheduleByDepId(subTask.getId());
            ContractTask task = contractTaskMapper.selectByPrimaryKey(taskId);
            if (!task.getStatus().equals(TaskStatus.PROGRESSING.getVal())) {
                taskResultService.changeStatusAndResultV2(taskId, subTask.getId(), task.getStatus(), null, false);
            }
        } else {
            Map reMsg = CollectionUtil.hashMap("channel", subTask.getChannel(), "message", errorMsg, "result", "任务执行失败");
            taskResultService.changeStatusAndResultV2(taskId, subTask.getId(), TaskStatus.FAIL.getVal(), JSON.toJSONString(reMsg), false);
        }
    }


}
