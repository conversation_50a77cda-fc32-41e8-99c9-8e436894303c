package com.wosai.upay.job.refactor.biz.acquirer.lkl;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.merchant.contract.service.LakalaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * lkl商户信息处理
 *
 * <AUTHOR>
 * @date 2024/7/25 10:15
 */
@Component
@Slf4j
public class LklMerchantInfoProcessor {

    @Resource
    private LakalaService lakalaService;

    /**
     * 获取商户帐户信息
     * lkl基本废弃，所有并没有维护返回体，直接返回了map
     *
     * @param merchantSn 商户号
     * @return 商户帐户信息
     */
    public Optional<Map<String, Object>> getMerchantInfo(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Optional.empty();
        }
        try {
            Map<String, Object> lklMerchantBankAccountMap = lakalaService.queryMerchant(merchantSn);
            return Optional.ofNullable(lklMerchantBankAccountMap);
        } catch (Exception e) {
            log.error("获取lkl商户帐户信息异常, merchantSn: {}", merchantSn, e);
            return Optional.empty();
        }
    }

    /**
     * 获取商户银行卡号
     *
     * @param merchantSn 商户号
     * @return 商户银行卡号
     */
    public Optional<String> getMerchantBankAccountNo(String merchantSn) {
        try {
            return getMerchantInfo(merchantSn)
                    .map(lklMerchantBankAccountMap -> (String) BeanUtil.getNestedProperty(lklMerchantBankAccountMap, "message.accountNo"));
        } catch (Exception e) {
            log.error("获取lkl商户银行卡号异常, merchantSn: {}", merchantSn, e);
            return Optional.empty();
        }
    }
}
