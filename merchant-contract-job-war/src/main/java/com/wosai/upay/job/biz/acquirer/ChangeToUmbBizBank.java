package com.wosai.upay.job.biz.acquirer;

import cn.hutool.core.util.StrUtil;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.externalservice.tag.TagClient;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component("umb-AcquirerChangeBiz")
public class ChangeToUmbBizBank extends AbstractBankDirectAcquirerChangeBiz {
    @Value("${umb_dev_code}")
    public String umbDevCode;

    @Value("${umb_trade_combo_id}")
    private long umbTradeComboId;

    @Autowired
    private TagClient tagClient;

    @Override
    protected String getDevCode(String acquirer) {
        return umbDevCode;
    }

    @Override
    protected long getDefaultComboId(String acquirer) {
        return umbTradeComboId;
    }

    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_UMB_RULE_GROUP;
    }

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_UMB.getValue();
    }

    @Override
    protected void targetAcquirerPostBiz(McAcquirerChange change) {
        super.targetAcquirerPostBiz(change);
        String merchantId = change.getMerchant_id();
        if (StrUtil.isBlank(merchantId)) {
            log.error("切换收单机构到中投科信之后打标签失败,McAcquirerChange中merchantId为空.{}", change);
        }
        tagClient.addUmbTag(merchantId);
    }
}
