package com.wosai.upay.job.biz.bankDirect;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.enume.BankDirectApplyViewStatusEnum;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ViewProcess;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.model.psbc.ModifySupportingMaterialsRequest;
import com.wosai.upay.job.model.psbc.SelfAuditRejectRequest;
import com.wosai.upay.job.providers.PsbcProvider;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.PsbcParam;
import com.wosai.upay.merchant.contract.service.PsbcService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.wosai.upay.job.providers.PsbcProvider.replaceHttp;


/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/4/26 09:24
 */
@Component
public class PsbcDirectBiz extends AbstractBankDirectApplyBiz {

    @Value("${psbc_business_dev_code}")
    public String psbcBusinessDevCode;

    @Autowired
    private PsbcService psbcService;

    @Autowired
    MerchantService merchantService;

    @Autowired
    private PsbcProvider psbcProvider;

    @Autowired
    MerchantBusinessLicenseService merchantBusinessLicenseService;
    //只找正数小数
    private static final Pattern compile = Pattern.compile("0\\.[0-9]*");


    @Override
    public String getDevCode() {
        //TODO 邮储的dec_code
        return psbcBusinessDevCode;
    }

    @Override
    protected Map<String, Object> completeContext(Map<String, Object> paramContext, BankDirectReq bankDirectReq) {
        final Map formBody = JSONObject.parseObject(bankDirectReq.getForm_body(), Map.class);
        //TODO  放入企业商户线下协议照片
        paramContext.put("protocol_image", BeanUtil.getPropString(formBody, "protocol_image"));
        //TODO 放入费率
        final List config = JSONObject.parseObject(BeanUtil.getPropString(formBody, "merchant_config"), List.class);
        List copy = copy(config);
        //特殊处理一下,阶梯费率时,只上报一个最大的
        List<Map> rate = ((List<Map>) copy).stream().map(map -> {
            // 新的阶梯费率的格式
            Object ladderFeeRates = map.get("ladder_fee_rates");
            if (ladderFeeRates instanceof List && WosaiCollectionUtils.isNotEmpty((List<Map>) ladderFeeRates)) {
                map.put("rate", getLadderMaxRate((List<Map>) ladderFeeRates));
            }
            //不是纯数字  类似: 100以下0.25, 100以上0.38
            else if (!compile.matcher((String) map.get("rate")).matches()) {
                map.put("rate", getMaxRate((String) map.get("rate")));
            }
            return map;
        }).collect(Collectors.toList());
        paramContext.put("psbc_feeRate", rate);
        //TODO 放入套餐
        paramContext.put("trade_combo_id", BeanUtil.getPropString(formBody, "trade_combo_id"));
        //放入业务标识
        paramContext.put("dev_code", bankDirectReq.getDev_code());
        //授权书
        paramContext.put("authorization_image", BeanUtil.getPropString(formBody, "authorization_image"));
        //法人签署授权书照片
        paramContext.put("authorization_snapshot", BeanUtil.getPropString(formBody, "authorization_snapshot"));
        //小微和个体户其他辅助照片
        String biz_apply = BeanUtil.getPropString(formBody, "biz_apply");
        if (StringUtils.isNotBlank(biz_apply)) {
            paramContext.put("biz_apply", JSONObject.parseObject(biz_apply, Map.class));
        }
        //公商信息核查补充图片
        paramContext.put("inspectPicCode",BeanUtil.getPropString(formBody,"inspectPicCode"));
        //所属集团商户号
        paramContext.put("upGroupMerId",BeanUtil.getPropString(formBody,"upGroupMerId"));
        //商户所属柜员
        paramContext.put("usrId",BeanUtil.getPropString(formBody,"usrId"));
        //风险评级数组
//        paramContext.put("riskGradeArr",BeanUtil.getPropString(formBody, "riskGradeArr"));
        //放入spa可以识别的费率信息
        final List<Map> list = ((List<Map>) config).stream().map(x -> {
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.WEIXIN.getValue()) {
                String rateText = BeanUtil.getPropString(x, "rate");
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.WECHAT_PAY_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, rateText
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.ALIPAY.getValue()) {
                String rateText = BeanUtil.getPropString(x, "rate");
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.ALIPAY_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, rateText
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.UNIONPAY.getValue()) {
                String rateText = BeanUtil.getPropString(x, "rate");
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_DEBIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, rateText
                );
            }
            return null;
        }).filter(x -> Objects.nonNull(x)).collect(Collectors.toList());
        paramContext.putIfAbsent(ParamContextBiz.MERCHANT_FEE_RATES, list);
        return paramContext;
    }

    private String getLadderMaxRate(List<Map> ladderFeeRates) {
        List<Double> rates = ladderFeeRates.stream().map(r -> Double.valueOf(BeanUtil.getPropString(r, "rate"))).sorted().collect(Collectors.toList());
        return String.valueOf(rates.get(rates.size()-1));
    }

    /**
     * 只展示最大的费率
     *
     * @param rateText
     * @return
     */
    private String getMaxRate(String rateText) {
        Matcher matcher = compile.matcher(rateText);
        String result = null;
        while (matcher.find()) {
            if (result == null) {
                result = matcher.group(0);
                continue;
            }
            double temp = Double.parseDouble(result);
            double nextRate = Double.parseDouble(matcher.group(0));
            if (nextRate > temp) {
                result = matcher.group(0);
            }
        }
        return result;
    }

    private List<Map> copy(List<Map> source) {
        List<Map> result = new ArrayList();

        for (Map map : source) {
            Map copyMap = new HashMap();
            copyMap.putAll(map);
            result.add(copyMap);
        }
        return result;
    }

    @Override
    protected String chooseGroupByContextAndDevCode(Map<String, Object> context, String devCode) {
        return McConstant.RULE_GROUP_PSBC;
    }

    @Override
    protected Integer getBankRef(String dev_code) {
        return BankDirectApplyRefEnum.PSBC.getValue();
    }

    @Override
    protected String getAcquire() {
        return AcquirerTypeEnum.PSBC.getValue();
    }

    @Override
    protected Integer getProvider() {
        return ProviderEnum.PROVIDER_PSBC.getValue();
    }

    /**
     * 撤回处理
     *
     * @param merchantSn
     * @param request
     * @return
     */
    @Override
    public com.wosai.upay.merchant.contract.model.ContractResponse doReject(String merchantSn, SelfAuditRejectRequest request, ContractSubTask contractSubTask) {
        return psbcService.outMerInfoAddRevocation(merchantSn, request.getDescription());
    }

    /**
     * 入网任务置为失败后 收单机构的处理
     */
    @Override
    public void rejectPostHandle(ContractSubTask contractSubTask) {
        //将交易参数逻辑删除
        //获取收单机构进件参数
        String merchantProviderParamsId = psbcProvider.getMerchantProviderParamsId(contractSubTask);
        //逻辑删除收单机构进件任务,下次可以继续进件
        psbcProvider.setDel(merchantProviderParamsId);
    }


    @Override
    public List<ViewProcess> initViewProcess(String merchantSn) {
        final List<ViewProcess> viewProcesses = preViewProcess(getAcquire());
        if (CollectionUtils.isEmpty(viewProcesses)) {
            return Lists.newArrayList();
        }
        List<ViewProcess> list;
        //邮储的对公商户不需要签约流程
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, getDevCode());
        //营业执照类型
        final Integer licenseType = license.getType();
        //对私商户
        if (Lists.newArrayList(BusinessLicenseTypeEnum.MICRO.getValue(), BusinessLicenseTypeEnum.INDIVIDUAL.getValue()).contains(licenseType)) {
            list = viewProcesses.stream().filter(x -> !Lists.newArrayList(BankDirectApplyViewStatusEnum.DISTRIBUTED_AUDITING.getValue(), BankDirectApplyViewStatusEnum.AUTHING_ENTERPRISE.getValue()).contains(x.getViewStatus())).collect(Collectors.toList());
        } else {
            //对公商户
            list = viewProcesses.stream().filter(x -> !Lists.newArrayList(BankDirectApplyViewStatusEnum.SIGNING.getValue(), BankDirectApplyViewStatusEnum.SIGNED_AUDITING.getValue(), BankDirectApplyViewStatusEnum.AUTHING.getValue()).contains(x.getViewStatus())).collect(Collectors.toList());
        }
        //设置微信图片地址链接
        list.stream().forEach(x -> {
            if (Objects.equals(x.getExtra(), Boolean.TRUE)) {
                final String imageUrl = getWXImageUrl(getAcquire(), getProvider());
                x.setExtraMessage(imageUrl);
                x.setAliMessage(replaceHttp("https://images.wosaimg.com/43/94c324ceebb13328dd8d980818e6d3f4f57756.png"));
            }
        });
        return list;
    }

    @Override
    public com.wosai.upay.job.model.ContractResponse modifySupportingMaterials(ModifySupportingMaterialsRequest req) {
        final com.wosai.upay.job.model.ContractResponse result = new com.wosai.upay.job.model.ContractResponse();
        final String merchantSn = req.getMerchantSn();
        final Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        //添加最新照片
        paramContext.put("biz_apply", CollectionUtil.hashMap("private_other", Joiner.on(";").join(req.getPicList())));
        final ContractRule contractRule = ruleContext.getContractRule("psbc");
        final ContractChannel contractChannel = contractRule.getContractChannel();
        PsbcParam psbcParam = psbcProvider.buildParam(contractChannel, null, PsbcParam.class);
        final ContractResponse response = psbcService.updateMerchant(paramContext, psbcParam);
        return response.isSuccess() ? result.setSuccess(Boolean.TRUE).setMsg("修改成功") : result.setMsg(response.getMessage());
    }
}
