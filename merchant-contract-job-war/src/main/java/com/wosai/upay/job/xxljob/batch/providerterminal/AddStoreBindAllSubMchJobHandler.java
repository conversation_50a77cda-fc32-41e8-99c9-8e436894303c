package com.wosai.upay.job.xxljob.batch.providerterminal;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.mapper.ProviderTerminalTaskMapper;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import com.wosai.upay.job.model.dto.ProviderTerminalContext;
import com.wosai.upay.job.repository.ProviderTerminalTaskRepository;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.wosai.upay.job.constant.ProviderTerminalConstants.FAIL;
import static com.wosai.upay.job.constant.ProviderTerminalConstants.SUCCESS;

/**
 * xxl_job_desc: 终端-新增门店绑定所有子商户号
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("AddStoreBindAllSubMchJobHandler")
public class AddStoreBindAllSubMchJobHandler extends AbstractBatchJobHandler<ProviderTerminalTask> {

    @Autowired
    private ProviderTerminalTaskMapper taskMapper;
    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private ProviderTerminalTaskRepository taskRepository;

    private static final String BOUND_TERMINAL = "bound_terminal_";


    @Override
    public List<ProviderTerminalTask> queryTaskItems(BatchJobParam param) {
        long currentTimeMillis = System.currentTimeMillis();
        String date = StringUtil.formatDate(currentTimeMillis - param.getQueryTime());
        String nowDate = StringUtil.formatDate(currentTimeMillis);
        return taskMapper.selectMerchantSnByPriorityAndType(date,
                nowDate,
                ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                param.getBatchSize());
    }

    @Override
    public String getLockKey(ProviderTerminalTask providerTerminalTask) {
        return "AddStoreBindAllSubMchJobHandler:" + providerTerminalTask.getId();
    }

    @Override
    public void doHandleSingleData(ProviderTerminalTask providerTerminalTask) {
        //1，再次查询
        ProviderTerminalTask task = taskMapper.selectByPrimaryKey(providerTerminalTask.getId());
        //2，判断状态
        if (task.getStatus() == SUCCESS || task.getStatus() == FAIL) {
            return;
        }
        ProviderTerminalContext context = JSONObject.parseObject(providerTerminalTask.getContext(), ProviderTerminalContext.class);
        String haikeCacheKey = BOUND_TERMINAL + providerTerminalTask.getMerchant_sn();
        if (context != null && context.getProvider() != null && ProviderEnum.PROVIDER_HAIKE.getValue().intValue() == context.getProvider()) {
            if (!redisLock.lock(haikeCacheKey, haikeCacheKey, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
                return;
            }
        }
        try {
            //处理task
            providerTerminalBiz.boundTerminal(task);
        } catch (ContractBizException e) {
            //业务异常
            log.error("处理子商户号绑定终端task业务异常 taskId:{} 商户号:{}", task.getId(), task.getMerchant_sn(), e);
            taskRepository.updateTaskStatusById(task.getId(), FAIL, e.getMessage());
        } catch (Exception e) {
            log.error("处理子商户号绑定终端task系统异常 taskId:{} 商户号:{}", task.getId(), task.getMerchant_sn(), e);
        } finally {
            if (context != null && context.getProvider() != null && ProviderEnum.PROVIDER_HAIKE.getValue().intValue() == context.getProvider()) {
                redisLock.unlock(haikeCacheKey, haikeCacheKey);
            }
        }
    }
}
