package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.mapper.McAcquirerChangeMapper;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.McAcquirerChangeExample;
import com.wosai.upay.job.model.acquirer.AcquirerChangeSaveDTO;
import com.wosai.upay.job.model.acquirer.ProcessDetail;
import com.wosai.upay.job.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020-04-23
 */
@Repository
public class AcquirerChangeDao {

    @Autowired
    private McAcquirerChangeMapper acquirerChangeMapper;

    private static final int DEFAULT_MAX_LIMIT_PAGE_SIZE = 500;

    public McAcquirerChange save(AcquirerChangeSaveDTO dto) {
        final String merchantSn = dto.getMerchantSn();
        McAcquirerChange change = new McAcquirerChange();
        change.setApply_id(UUID.randomUUID().toString());
        change.setMerchant_sn(merchantSn);
        change.setMerchant_id(dto.getMerchantId());
        change.setSource_acquirer(dto.getSourceAcquirer());
        change.setTarget_acquirer(dto.getTargetAcquirer());
        change.setMemo("申请成功");
        change.setImmediately(dto.getImmediately());
        Map extra;
        if (StringUtils.isEmpty(dto.getExtra())) {
            extra = new HashMap();
        } else {
            extra = CommonUtil.string2Map(dto.getExtra());
            if (Objects.isNull(extra)) {
                extra = new HashMap();
            }
        }
        extra.put("tradeAppId", dto.getTradeAppId());
        extra.put("forceChange", dto.getForceChange());
        final Boolean cancelable = dto.getCancelable();
        if (Objects.nonNull(cancelable)) {
            extra.put("cancel_able", cancelable);
        }
        change.setExtra(CommonUtil.map2String(extra));
        acquirerChangeMapper.insertSelective(change);
        return getLatestUnFinishedApply(merchantSn);
    }

    public McAcquirerChange save(String merchantSn, String merchantId, String sourceAcquirer, String targetAcquirer, boolean immediately, String tradeAppId, boolean forceChange) {
        McAcquirerChange change = new McAcquirerChange();
        change.setApply_id(UUID.randomUUID().toString());
        change.setMerchant_sn(merchantSn);
        change.setMerchant_id(merchantId);
        change.setSource_acquirer(sourceAcquirer);
        change.setTarget_acquirer(targetAcquirer);
        change.setMemo("申请成功");
        change.setImmediately(immediately);
        Map extra = CollectionUtil.hashMap(
                "tradeAppId", tradeAppId,
                "forceChange", forceChange
        );
        change.setExtra(CommonUtil.map2String(extra));
        acquirerChangeMapper.insertSelective(change);
        return getLatestUnFinishedApply(merchantSn);
    }

    /**
     * 获取最近的一次未完成的申请
     *
     * @param merchantSn
     * @return
     */
    public McAcquirerChange getLatestUnFinishedApply(String merchantSn) {
        McAcquirerChangeExample example = new McAcquirerChangeExample();
        example.or().andMerchant_snEqualTo(merchantSn);
        example.setOrderByClause("create_at desc");
        List<McAcquirerChange> changes = acquirerChangeMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isEmpty(changes)) {
            return null;
        } else {
            McAcquirerChange change = changes.get(0);
            if (change.getStatus() == AcquirerChangeStatus.SUCCESS ||
                    change.getStatus() == AcquirerChangeStatus.FAIL) {
                return null;
            } else {
                return change;
            }
        }
    }

    /**
     * 获取最近的一次的申请
     *
     * @param merchantSn
     * @return
     */
    public McAcquirerChange getLatestApply(String merchantSn, String targetAcquirer) {
        McAcquirerChangeExample example = new McAcquirerChangeExample();
        example.or().andMerchant_snEqualTo(merchantSn).andTarget_acquirerEqualTo(targetAcquirer);
        example.setOrderByClause("create_at desc");
        List<McAcquirerChange> changes = acquirerChangeMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isEmpty(changes)) {
            return null;
        } else {
            return changes.get(0);
        }
    }

    public McAcquirerChange getLatestSuccessApply(String merchantSn, String sourceAcquirer) {
        McAcquirerChangeExample example = new McAcquirerChangeExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andSource_acquirerEqualTo(sourceAcquirer)
                .andStatusEqualTo(AcquirerChangeStatus.SUCCESS);
        example.setOrderByClause("id desc");
        PageHelper.startPage(1, 1);
        List<McAcquirerChange> changes = acquirerChangeMapper.selectByExampleWithBLOBs(example);
        return WosaiCollectionUtils.isEmpty(changes) ? null : changes.get(0);
    }

    public McAcquirerChange getAcquirerChangeByApplyId(String applyId) {
        McAcquirerChangeExample example = new McAcquirerChangeExample();
        example.or().andApply_idEqualTo(applyId);
        List<McAcquirerChange> changes = acquirerChangeMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isEmpty(changes)) {
            return null;
        } else {
            return changes.get(0);
        }
    }

    public List<McAcquirerChange> getChangeApplies(int status, int queryLimit, long queryTime) {
        Date date = new Date(System.currentTimeMillis() - queryTime);
        McAcquirerChangeExample example = new McAcquirerChangeExample();
        example.or().andStatusEqualTo(status)
                .andUpdate_atGreaterThanOrEqualTo(date);
        example.setOrderByClause("update_at asc");
        PageHelper.startPage(1, queryLimit);
        return acquirerChangeMapper.selectByExampleWithBLOBs(example);
    }

    public List<McAcquirerChange> getDayChangeApplies(int queryLimit, long queryTime) {
        Date date = new Date(System.currentTimeMillis() - queryTime);
        McAcquirerChangeExample example = new McAcquirerChangeExample();
        example.or()
                .andStatusIn(Arrays.asList(
                        AcquirerChangeStatus.PENDING,
                        AcquirerChangeStatus.CONTRACTING,
                        AcquirerChangeStatus.CONTRACTED,
                        AcquirerChangeStatus.SYNC_FEE
                ))
                .andUpdate_atGreaterThanOrEqualTo(date);
        example.or()
                .andStatusIn(Arrays.asList(AcquirerChangeStatus.SYNC_FEE_SUCCESS, AcquirerChangeStatus.CHANGE_ACQUIRER_STATUS_SUCCESS))
                .andImmediatelyEqualTo(true)
                .andUpdate_atGreaterThanOrEqualTo(date);

        example.setOrderByClause("update_at asc");
        PageHelper.startPage(1, queryLimit);
        return acquirerChangeMapper.selectByExampleWithBLOBs(example);
    }

    public List<McAcquirerChange> getNightChangeApplies(int queryLimit, long queryTime) {
        Date date = new Date(System.currentTimeMillis() - queryTime);
        McAcquirerChangeExample example = new McAcquirerChangeExample();
        example.or()
                .andStatusIn(Arrays.asList(
                        AcquirerChangeStatus.PENDING,
                        AcquirerChangeStatus.CONTRACTING,
                        AcquirerChangeStatus.CONTRACTED,
                        AcquirerChangeStatus.SYNC_FEE,
                        AcquirerChangeStatus.SYNC_FEE_SUCCESS,
                        AcquirerChangeStatus.CHANGE_ACQUIRER_STATUS_SUCCESS
                ))
                .andUpdate_atGreaterThanOrEqualTo(date);

        example.setOrderByClause("update_at asc");
        PageHelper.startPage(1, queryLimit);
        return acquirerChangeMapper.selectByExampleWithBLOBs(example);
    }

    public List<McAcquirerChange> getImmediatelyChangeApplies(List<Integer> status, int queryLimit, long queryTime, Boolean immediately) {
        Date date = new Date(System.currentTimeMillis() - queryTime);
        McAcquirerChangeExample example = new McAcquirerChangeExample();
        if (Objects.isNull(immediately)) {
            immediately = Boolean.FALSE;
        }
        example.or().andStatusIn(status)
                .andUpdate_atGreaterThanOrEqualTo(date).andImmediatelyEqualTo(immediately);
        example.setOrderByClause("update_at asc");
        PageHelper.startPage(1, queryLimit);
        return acquirerChangeMapper.selectByExampleWithBLOBs(example);
    }

    public void updateStatus(McAcquirerChange change, int status, String memo) {
        updateStatusWitExtra(change, status, memo, null);
    }

    public void updateStatusWitExtra(McAcquirerChange change, int status, String memo, Map appendExtra) {
        String process = change.getProcess();
        List<ProcessDetail> processDetails;
        if (WosaiStringUtils.isNotEmpty(process)) {
            processDetails = JSON.parseArray(process, ProcessDetail.class);
        } else {
            processDetails = new ArrayList<>();
        }

        ProcessDetail processDetail = new ProcessDetail(status, memo, System.currentTimeMillis());
        processDetails.add(processDetail);
        process = JSON.toJSONString(processDetails);
        change.setProcess(process);

        McAcquirerChange updateValue = new McAcquirerChange();
        updateValue.setId(change.getId());
        updateValue.setStatus(status);
        updateValue.setMemo(memo);
        updateValue.setProcess(process);

        if (WosaiMapUtils.isNotEmpty(appendExtra)) {
            Map extra = CommonUtil.string2Map(change.getExtra());
            extra.putAll(appendExtra);
            updateValue.setExtra(CommonUtil.map2String(extra));
        }

        acquirerChangeMapper.updateByPrimaryKeySelective(updateValue);
    }

    public void appendExtra(McAcquirerChange change, Map appendExtra) {
        McAcquirerChange updateValue = new McAcquirerChange();
        updateValue.setId(change.getId());
        Map extra = CommonUtil.string2Map(change.getExtra());
        extra.putAll(appendExtra);
        updateValue.setExtra(CommonUtil.map2String(extra));
        acquirerChangeMapper.updateByPrimaryKeySelective(updateValue);
    }


    /**
     * 获取商户成功的收单机构切换记录
     *
     * @param merchantSn 商户号
     * @return 收单机构切换记录
     */
    public List<McAcquirerChange> listSuccessChangeByMerchantSn(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        McAcquirerChangeExample example = new McAcquirerChangeExample();
        example.or().andMerchant_snEqualTo(merchantSn).andStatusEqualTo(AcquirerChangeStatus.SUCCESS);
        example.setOrderByClause("id desc");
        PageHelper.startPage(1, DEFAULT_MAX_LIMIT_PAGE_SIZE);
        return acquirerChangeMapper.selectByExampleWithBLOBs(example);
    }


}
