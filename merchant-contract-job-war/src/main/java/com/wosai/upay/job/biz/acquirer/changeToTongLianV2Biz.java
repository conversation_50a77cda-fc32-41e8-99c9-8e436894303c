package com.wosai.upay.job.biz.acquirer;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.WeixinAppidConfig;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.weixin.SubdevConfigResp;
import com.wosai.upay.merchant.contract.service.TongLianService;
import com.wosai.upay.merchant.contract.service.WeixinService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: changeToTongLianV2Biz
 * <AUTHOR>
 * @Date 2023/6/16 17:50
 **/

@Component("tonglianV2-AcquirerChangeBiz")
@Slf4j
public class changeToTongLianV2Biz extends AbstractIndirectAcquirerChangeBiz{

    @Autowired
    TongLianService tongLianService;

    @Autowired
    WeixinService weixinService;

    @Autowired
    private IMerchantService iMerchantService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Override
    public String getContractGroup(String merchantSn) {
        String merchantPath = getMerchantPath(merchantSn);
        if (WosaiStringUtils.isNotEmpty(merchantPath) && applicationApolloConfig.getSdTonglianV2Path().stream().anyMatch(merchantPath::startsWith)) {
            return ContractRuleConstants.CHANGE_TO_TONGLIANV2_SD_RULE_GROUP;
        }
        return ContractRuleConstants.CHANGE_TO_TONGLIANV2_RULE_GROUP;
    }

    @Override
    protected List<MerchantProviderParams> getDefaultChangeParams(McAcquirerChange change) {
        // 查找支付宝、云闪付、翼支付交易参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue())
                .andPaywayNotIn(Arrays.asList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.ACQUIRER.getValue()))
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        params.add(getAliParams(change));
        params.add(getWxParams(change));
        return params;
    }


    /**
     * 获取支付宝参数
     *
     * @param change
     * @return
     */
    private MerchantProviderParams getAliParams(McAcquirerChange change) {
        //获取最新的支付宝参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(getProviderCode(change.getTarget_acquirer()))
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        params = params.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            return params.get(0);
        }
        throw new CommonPubBizException(String.format("商户号:%s,没有找到支付宝参数", change.getMerchant_sn()));
    }

    /**
     * 获取微信参数
     *
     * @param change
     * @return
     */
    private MerchantProviderParams getWxParams(McAcquirerChange change) {
        // 先获取新渠道的微信参数
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        wxExample.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(getProviderCode(change.getTarget_acquirer()))
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        wxExample.setOrderByClause("ctime desc");
        List<MerchantProviderParams> wxParams = paramsMapper.selectByExampleWithBLOBs(wxExample);
        wxParams = wxParams.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(wxParams)) {
            throw new ContractBizException("缺少可用微信子商户号");
        }

        // 有多个微信交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams wxParam : wxParams) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(wxParam.getAuth_status())) {
                return wxParam;
            }
        }
        return wxParams.get(0);
    }


    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_TONGLIAN_V2.getValue();
    }

    private WeixinConfig getConfig(SubdevConfigResp subdevConfigResp, String merchantSn) {
        WeixinConfig weixinConfig = new WeixinConfig();
        weixinConfig.setWeixinMchId(getWeixinMchId(merchantSn));
        weixinConfig.setPayAuthPath(subdevConfigResp.getJsapi_path_list());
        List<WeixinAppidConfig> appidConfigs = subdevConfigResp.getAppid_config_list().stream().map(appidConfig -> {
            WeixinAppidConfig config = new WeixinAppidConfig();
            config.setSub_appid(appidConfig.getSub_appid());
            config.setSubscribe_appid(appidConfig.getSubscribe_appid());
            return config;
        }).collect(Collectors.toList());
        weixinConfig.setAppidConfigs(appidConfigs);
        return weixinConfig;
    }

    private String getWeixinMchId(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = paramsMapper.selectByExample(example);
        if (WosaiCollectionUtils.isEmpty(params)) {
            throw new CommonPubBizException("未找到微信子商户号");
        } else {
            return params.get(0).getPay_merchant_id();
        }
    }

    /**
     * 获取微信间连在用参数
     *
     * @param merchantSn
     * @return
     */
    private MerchantProviderParams getWeixinInUseParams(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andProviderNotEqualTo(ProviderEnum.WEI_XIN.getValue())
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = paramsMapper.selectByExample(example);
        return WosaiCollectionUtils.isNotEmpty(params) ? params.get(0) : null;
    }

    private String getMerchantPath(String merchantSn) {
        String organizationId = BeanUtil.getPropString(iMerchantService.getMerchantBySn(merchantSn), "organization_id");
        if (StringUtils.isBlank(organizationId)) {
            return StringUtils.EMPTY;
        }
        return MapUtils.getString(organizationService.getOrganization(organizationId), "path");
    }

}