package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.model.Request;
import com.wosai.upay.job.Constants.ConstantsEvent;
import com.wosai.upay.job.biz.BusinessRuleBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.PendingTasks;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: lishuangqiang
 * @Date: 2019/3/29
 * @Description:
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class SelfHelpNetInEventServiceImpl implements SelfHelpNetInEventService {
    @Autowired
    private ContractEventMapper contractEventMapper;

    @Autowired
    private ContractEventService contractEventService;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    BusinessRuleBiz businessRuleBiz;

    @Autowired
    private ComposeAcquirerBiz acquirerBiz;

    @Autowired
    private RuleContext ruleContext;


    /**
     * 功能描述:
     *
     * @auther: lishuangqiang
     * @date: 2019/3/29 9:28
     * @param: [merchantSn, 商户号 optType,事件类型 message ,事件内容]
     * @return: void
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveSelfHelpNetInEvent(String merchantSn, int optType, Map<String, Object> message, List dataList, Map source) {
        // 入网直接调用contractEventService的接口
        if (ContractEvent.OPT_TYPE_NET_IN == optType) {
            ContractEvent contractEvent = contractEventService.saveContractEventV3(new NewMchNetInReq().setMerchantSn(merchantSn).setPlatform("CRM"));
            return contractEvent.getId();
        }
        if (ContractEvent.OPT_TYPE_FAIL == optType) {
            ContractEvent contractEvent = contractEventService.saveFailEventV2(new NewMchNetInReq().setMerchantSn(merchantSn).setPlatform(BeanUtil.getPropString(source, Request.KEY_PLATFORM)).setFailMsg(BeanUtil.getPropString(source, "fail_memo")));
            return contractEvent.getId();
        }

        // 入网，判断间连收单机构
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus == null) {
            NetInRuleGroups ruleGroups = businessRuleBiz.getRuleGroupId(merchantSn);
            RuleGroup ruleGroup = ruleContext.getRuleGroup(ruleGroups.getPrimaryRuleGroupId());
            if (ruleGroup == null) {
                throw new CommonPubBizException("规则组不存在");
            }
            contractStatus = new ContractStatus()
                    .setStatus(ContractStatus.STATUS_PENDING)
                    .setMerchant_sn(merchantSn)
                    .setAcquirer(ruleGroup.getAcquirer());
            contractStatusMapper.insertSelective(contractStatus);
        }

        return doSaveEvent(merchantSn, optType, message, dataList, source, contractStatus);
    }


    /**
     * 老流程
     *
     * @return
     */
    private Long doSaveEvent(String merchantSn, int optType, Map<String, Object> message, List dataList, Map source, ContractStatus contractStatus) {
        String ruleGroupId;
        if (optType == ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS) {
            ruleGroupId = "hxb".equals(contractStatus.getAcquirer()) ? "hxb" : acquirerBiz.getAcquirerDefaultRuleGroup(contractStatus.getAcquirer());
        } else {
            ruleGroupId = acquirerBiz.getAcquirerDefaultRuleGroup(contractStatus.getAcquirer());
        }

        ContractEvent contractEvent = new ContractEvent();
        String tableName = BeanUtil.getPropString(message, DTSBean.KEY_TABLE_NAME);
        Map event_msg = CollectionUtil.hashMap(ConstantsEvent.EVENT_TYPE_TABLE_NAME, tableName,
                ConstantsEvent.EVENT_TYPE_OPY_TYPE, optType,
                ConstantsEvent.EVENT_TYPE_MSG, dataList,
                ConstantsEvent.EVENT_TYPE_SOURCE, source);
        String event_msgs = JSON.toJSONString(event_msg);
        contractEvent.setMerchant_sn(merchantSn);
        contractEvent.setEvent_type(optType);
        contractEvent.setEvent_msg(event_msgs);
        contractEvent.setRule_group_id(ruleGroupId);
        contractEventMapper.insertSelective(contractEvent);


        return contractEvent.getId();
    }


    @Override
    public ContractEvent selectSelfHelpNetInEventBymerchantSnAndtaskId(String merchant_sn, int task_id) {
        return contractEventMapper.selectSelfHelpNetInEventBymerchantSnAndtaskId(merchant_sn, task_id);
    }


    @Override
    public int updateByPrimaryKeySelective(ContractEvent record) {
        return contractEventMapper.updateByPrimaryKeySelective(record);
    }


}
