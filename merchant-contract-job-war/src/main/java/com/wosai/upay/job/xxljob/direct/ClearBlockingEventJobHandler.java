package com.wosai.upay.job.xxljob.direct;

import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.Constants.ConstantsEvent;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.wosai.upay.job.util.StringUtil.formatDate;

/**
 * xxl_job_desc: 处理阻塞事件
 * <AUTHOR>
 * @date 2025/4/11
 */
@Slf4j
@Component("ClearBlockingEventJobHandler")
public class ClearBlockingEventJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractEventMapper contractEventMapper;
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public String getLockKey() {
        return "ClearBlockingEventJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        SqlSession sqlSession = null;
        try {
            final Integer queryLimit = param.getBatchSize();
            long currentTime = System.currentTimeMillis();
            //扫描截止时间
            final long startTime = currentTime - param.getStartTime();
            //扫描开始时间
            final long endTime = currentTime - param.getEndTime();
            List<ContractEvent> events = contractEventMapper.selectForBlocking(
                    formatDate(startTime),
                    formatDate(endTime),
                    queryLimit);
            if (CollectionUtils.isEmpty(events) || Objects.isNull(events.get(0))) {
                return;
            }
            //批量更新event创建时间让过期任务重新被扫描
            //使用批量执行器
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            ContractEventMapper contractEventMapper = sqlSession.getMapper(ContractEventMapper.class);
            events.forEach(event -> {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                event.setResult(JSON.toJSONString(CollectionUtil.hashMap(ConstantsEvent.EVENT_UPDATE, new Date(), ConstantsEvent.EVENT_CREATE, event.getCreate_at())));
                //修改event创建时间以生成任务
                contractEventMapper.updateCreateAt(event.getId(), event.getResult());
            });
            sqlSession.commit();
        } catch (Exception e) {
            if (Objects.nonNull(sqlSession)) {
                sqlSession.rollback();
            }
            log.error("clearBlockingEvent error", e);
            chatBotUtil.sendMessageToContractWarnChatBot("定时更新未处理事件处理异常 " + ExceptionUtil.getThrowableMsg(e));
        } finally {
            if (Objects.nonNull(sqlSession)) {
                sqlSession.close();
            }
        }
    }
}
