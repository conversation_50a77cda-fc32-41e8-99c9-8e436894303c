package com.wosai.upay.job.biz.acquirer;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.constant.ContractRuleConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/7/1 2:55 下午
 */
@Component("cgb-AcquirerChangeBiz")
public class ChangeToCgbBizBank extends AbstractBankDirectAcquirerChangeBiz {
    @Value("${cgb_dev_code}")
    public String cgbDevCode;

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_CGB.getValue();
    }

    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_CGB_RULE_GROUP;
    }

    @Override
    public String getDevCode(String acquirer) {
        return cgbDevCode;
    }

    @Override
    protected long getDefaultComboId(String acquirer) {
        return 90;
    }

}
