package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.PreAuthApply;
import org.apache.ibatis.annotations.Select;

public interface PreAuthApplyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PreAuthApply record);

    int insertSelective(PreAuthApply record);

    PreAuthApply selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PreAuthApply record);

    int updateByPrimaryKeyWithBLOBs(PreAuthApply record);

    int updateByPrimaryKey(PreAuthApply record);

    @Select("select * from pre_auth_apply where merchant_sn=#{merchantSn} and (dev_code = 'lkl' or dev_code = '')  order by update_at desc limit 1")
    PreAuthApply selectLKlByMerchantSn(String merchantSn);

    /**
     * 根据商户号和devCode获取对应收单机构的预授权
     * @param merchantSn
     * @param devCode
     * @return
     */
    @Select("select * from pre_auth_apply where merchant_sn=#{merchantSn} and dev_code=#{devCode} order by update_at desc limit 1")
    PreAuthApply selectPreAuthApply(String merchantSn,String devCode);
}