package com.wosai.upay.job.monitor;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.AbstractMatcherFilter;
import ch.qos.logback.core.spi.FilterReply;

public class MonitorFilter extends AbstractMatcherFilter<ILoggingEvent> {


    @Override
    public FilterReply decide(ILoggingEvent event) {
        if (!isStarted()) {
            return FilterReply.NEUTRAL;
        }
        if ("com.wosai.upay.job.monitor.MonitorLog".equalsIgnoreCase(event.getLoggerName())) {
            return onMatch;
        } else {
            return onMismatch;
        }
    }


}
