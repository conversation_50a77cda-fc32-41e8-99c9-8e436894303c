package com.wosai.upay.job.consumer;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.job.model.DataChangeConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 *
 * <AUTHOR>
 * @date 2018/9/29
 */
@Component
public class DataSyncHandler implements InitializingBean {

    private final static Logger log = LoggerFactory.getLogger(DataSyncHandler.class);

    private static Config config = ConfigService.getAppConfig();

    public List<String> merchantConfigChangeFields = Arrays.asList(
            MerchantConfig.B2C_FEE_RATE,
            MerchantConfig.C2B_FEE_RATE,
            MerchantConfig.WAP_FEE_RATE,
            MerchantConfig.MINI_FEE_RATE);

    public List<String> merchantBankAccountChangeFields = Arrays.asList(
            "type",
            "holder",
            "number",
            "branch_name",
            "identity",
            "legal_person_name",
            "bank_name"
    );
    public List<String> merchantBankAccountChangeFieldsTwo = Arrays.asList(
            "id_validity",
            "legal_person_register_no"
    );

    public List<String> merchantChangeToLklFields = Lists.newArrayList(
            Merchant.NAME,
            Merchant.BUSINESS_NAME,
            Merchant.INDUSTRY,
            Merchant.PROVINCE,
            Merchant.CITY,
            Merchant.DISTRICT,
            Merchant.STREET_ADDRESS,
            Merchant.CONTACT_CELLPHONE,
            Merchant.STATUS
    );
    public static String[] merchantChangeToLklFieldsArray = {Merchant.NAME,
            Merchant.BUSINESS_NAME,
            Merchant.INDUSTRY,
            Merchant.PROVINCE,
            Merchant.CITY,
            Merchant.DISTRICT,
            Merchant.STREET_ADDRESS,
            Merchant.CONTACT_CELLPHONE,
            Merchant.STATUS};

    public Map needToSyncMerchantFeerateChange(int payway, Map<String, Object> dataChange, List changeDataToLklFields) {
        boolean isNetIn = false;
        Map judgeMsg = new HashMap();
        List dataChangeList = new ArrayList();
        //支付方式支付宝、微信、京东金融、QQ钱包的费率更新需要同步至拉卡拉 新增云闪付费率同步
        if (payway == PaywayEnum.ALIPAY.getValue() || payway == PaywayEnum.WEIXIN.getValue() || payway == PaywayEnum.JD_WALLET.getValue() || payway == PaywayEnum.QQ_WALLET.getValue() || payway == PaywayEnum.UNIONPAY.getValue()) {
            for (String key : dataChange.keySet()) {
                if (changeDataToLklFields.contains(key)) {
                    dataChangeList.add(key);
                    isNetIn = true;
                }
            }
        }
        judgeMsg.put("isNetIn", isNetIn);
        judgeMsg.put("dataChangeList", dataChangeList);
        return judgeMsg;
    }

    public void setMerchantConfigChangeFields(List<String> merchantConfigChangeFields) {
        this.merchantConfigChangeFields = merchantConfigChangeFields;
    }

    public void setMerchantBankAccountChangeFieldsTwo(List<String> merchantBankAccountChangeFieldsTwo) {
        this.merchantBankAccountChangeFieldsTwo = merchantBankAccountChangeFieldsTwo;
    }

    public void setMerchantBankAccountChangeFields(List<String> merchantBankAccountChangeFields) {
        this.merchantBankAccountChangeFields = merchantBankAccountChangeFields;
    }


    public void setMerchantChangeToLklFields(List<String> merchantChangeToLklFields) {
        this.merchantChangeToLklFields = merchantChangeToLklFields;
    }

    @Override
    public String toString() {
        return "DataSyncHandler{" +
                "merchantConfigChangeFields=" + merchantConfigChangeFields +
                ", merchantBankAccountChangeFields=" + merchantBankAccountChangeFields +
                ", merchantBankAccountChangeFieldsTwo=" + merchantBankAccountChangeFieldsTwo +
                ", merchantChangeToLklFields=" + merchantChangeToLklFields +
                '}';
    }

    /**
     * 监听变更字段
     */
    private static final String APOLLO_FIELDS_KEY = "data_change";

    @Override
    public void afterPropertiesSet() throws Exception {
        config.addChangeListener(changeEvent -> {
            Set<String> changedKeys = changeEvent.changedKeys();
            if (changedKeys.contains(APOLLO_FIELDS_KEY)) {
                DataChangeConfig dataChangeConfig = JSON.parseObject(config.getProperty(APOLLO_FIELDS_KEY, null), DataChangeConfig.class);
                if (dataChangeConfig != null) {
                    log.info("dataChangeConfig {}", dataChangeConfig);
                    changeFromApollo(dataChangeConfig);
                }
            }
        });
    }


    private void changeFromApollo(DataChangeConfig dataChangeConfig) {
        if (!CollectionUtils.isEmpty(dataChangeConfig.getMerchant())) {
            setMerchantChangeToLklFields(dataChangeConfig.getMerchant());
        }
        if (!CollectionUtils.isEmpty(dataChangeConfig.getAccount())) {
            setMerchantBankAccountChangeFields(dataChangeConfig.getAccount());
        }
        if (!CollectionUtils.isEmpty(dataChangeConfig.getBasicBank())) {
            setMerchantBankAccountChangeFieldsTwo(dataChangeConfig.getBasicBank());
        }
        if (!CollectionUtils.isEmpty(dataChangeConfig.getFeeRate())) {
            setMerchantConfigChangeFields(dataChangeConfig.getFeeRate());
        }
    }
}
