package com.wosai.upay.job.model;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.BoundHashOperations;

import java.util.HashMap;
import java.util.Map;

/**
 * 报备规则
 *
 * <AUTHOR>
 * @date 2019-07-04
 */
@Data
public class ContractRule {

    private String rule;

    private String name;

    private Integer payway;

    private String provider;

    private String acquirer;

    private ContractChannel contractChannel;

    private Integer status;

    private Integer type;

    private Map<String, Object> metadata;

    private Integer retry;

    private Boolean is_default;

    private Boolean is_insert;

    private Boolean is_insert_influ_ptask;

    private Boolean is_update;

    private Boolean is_update_influ_ptask;

    @JsonIgnore
    public int getDefault() {
        return boolean2int(is_default);
    }

    @JsonIgnore
    public int getInsert() {
        return boolean2int(is_insert);
    }

    @JsonIgnore
    public int getInsertInfluPtask() {
        return boolean2int(is_insert_influ_ptask);
    }

    @JsonIgnore
    public int getUpdate() {
        return boolean2int(is_update);
    }

    @JsonIgnore
    public int getUpdateInfluPtask() {
        return boolean2int(is_update_influ_ptask);
    }

    @JsonIgnore
    public String getChannelNo() {
        if (contractChannel == null) {
            throw new IllegalArgumentException(rule + "channel为空");
        }
        return contractChannel.getChannel_no();
    }

    private int boolean2int(boolean bool) {
        return bool ? 1 : 0;
    }


    public static ContractRule fromMcContractRule(McContractRuleDO mcContractRule, final BoundHashOperations<String, String, ContractChannel> hashOperations) {
        ContractRule contractRule = new ContractRule();
        BeanUtils.copyProperties(mcContractRule, contractRule);
        Map metadata = JSON.parseObject(mcContractRule.getMetadata(), Map.class);
        contractRule.setMetadata(metadata == null ? new HashMap<>(0) : metadata);
        contractRule.setContractChannel(hashOperations.get(mcContractRule.getChannel()));
        contractRule.setIs_default(mcContractRule.getIsDefault());
        contractRule.setIs_insert(mcContractRule.getIsInsert());
        contractRule.setIs_insert_influ_ptask(mcContractRule.getIsInsertInfluPtask());
        contractRule.setIs_update(mcContractRule.getIsUpdate());
        contractRule.setIs_update_influ_ptask(mcContractRule.getIsUpdateInfluPtask());
        return contractRule;
    }
}
