package com.wosai.upay.job.biz.acquirer;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.application.RuleContractRequest;
import com.wosai.upay.job.service.ContractApplicationService;
import com.wosai.upay.job.util.TongLianUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.WeixinAppidConfig;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.weixin.SubdevConfigResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 收单机构切换到通联
 *
 * <AUTHOR>
 * @date 2020-04-26
 */
@Component("tonglian-AcquirerChangeBiz")
@Slf4j
public class ChangeToTongLianBiz extends AbstractIndirectAcquirerChangeBiz {

    @Autowired
    private MerchantBankService merchantBankService;

    @Autowired
    private TongLianUtil tongLianUtil;

    @Autowired
    private ContractApplicationService contractApplicationService;

    @Override
    protected void acquirerSpecialCheck(String merchantSn, String merchantId, String sourceAcquirer, String targetAcquirer, boolean immediately) {
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", merchantId, "default_status", MerchantBankAccountPre.DEFAULT_STATUS_TRUE));
        if (listResult == null || WosaiCollectionUtils.isEmpty(listResult.getRecords())) {
            throw new CommonPubBizException("获取商户卡信息为空 merchantSn:" + merchantSn + "merchantId:" + merchantId);
        }

        Map bankAccount = listResult.getRecords().get(0);

        String bankName = BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.BANK_NAME);
        if (!tongLianUtil.isTongLianSupportBank(bankName)) {
            throw new CommonPubBizException(String.format("商户 %s 银行卡 %s 通联不支持，不允许切换", merchantSn, bankName));
        }
    }

    /**
     * 获取微信间连在用参数
     *
     * @param merchantSn
     * @return
     */
    private MerchantProviderParams getWeixinInUseParams(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andProviderNotEqualTo(ProviderEnum.WEI_XIN.getValue())
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = paramsMapper.selectByExample(example);
        return WosaiCollectionUtils.isNotEmpty(params) ? params.get(0) : null;
    }

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_TONGLIAN.getValue();
    }

    private WeixinConfig getConfig(SubdevConfigResp subdevConfigResp, String merchantSn) {
        WeixinConfig weixinConfig = new WeixinConfig();
        weixinConfig.setWeixinMchId(getWeixinMchId(merchantSn));
        weixinConfig.setPayAuthPath(subdevConfigResp.getJsapi_path_list());
        List<WeixinAppidConfig> appidConfigs = subdevConfigResp.getAppid_config_list().stream().map(appidConfig -> {
            WeixinAppidConfig config = new WeixinAppidConfig();
            config.setSub_appid(appidConfig.getSub_appid());
            config.setSubscribe_appid(appidConfig.getSubscribe_appid());
            return config;
        }).collect(Collectors.toList());
        weixinConfig.setAppidConfigs(appidConfigs);
        return weixinConfig;
    }

    private String getWeixinMchId(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = paramsMapper.selectByExample(example);
        if (WosaiCollectionUtils.isEmpty(params)) {
            throw new CommonPubBizException("未找到微信子商户号");
        } else {
            return params.get(0).getPay_merchant_id();
        }
    }

    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_TONGLIAN_RULE_GROUP;
    }

    @Override
    protected List<MerchantProviderParams> getDefaultChangeParams(McAcquirerChange change) {
        //云闪付、翼支付交易参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN.getValue())
                .andPaywayNotIn(Arrays.asList(PaywayEnum.ACQUIRER.getValue(), PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()))
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        //支付宝交易参数
        MerchantProviderParams aliParams = getAliParams(change);
        params.add(aliParams);
        // 微信交易参数
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        wxExample.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN.getValue())
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        wxExample.setOrderByClause("ctime desc");
        List<MerchantProviderParams> wxParams = paramsMapper.selectByExampleWithBLOBs(wxExample);
        if (WosaiCollectionUtils.isEmpty(wxParams)) {
            // 重新报备一个
            CommonResult result = contractApplicationService.contractByRule(
                    new RuleContractRequest()
                            .setRule(ContractRuleConstants.TONGLIAN_NORMAL_WEIXIN_RULE)
                            .setMerchantSn(change.getMerchant_sn())
                            .setPlat("changeAcquirer")
                            .setReContract(true)
                            .setConfigParam(false)
            );
            if (!result.isSuccess()) {
                throw new ContractBizException("缺少可用微信子商户号");
            }
            wxParams = paramsMapper.selectByExampleWithBLOBs(wxExample);
            if (WosaiCollectionUtils.isEmpty(wxParams)) {
                throw new ContractBizException("缺少可用微信子商户号");
            }
            params.addAll(wxParams);
            return params;
        }

        // 有多个微信交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams wxParam : wxParams) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(wxParam.getAuth_status())) {
                params.add(wxParam);
                return params;
            }
        }
        params.add(wxParams.get(0));

        return params;
    }


    /**
     * 获取支付宝参数
     * @param change
     * @return
     */
    private MerchantProviderParams getAliParams(McAcquirerChange change) {
        //获取最新的支付宝参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN.getValue())
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            return params.get(0);
        }
        throw new CommonPubBizException(String.format("商户号:%s,没有找到支付宝参数",change.getMerchant_sn()));
    }
}
