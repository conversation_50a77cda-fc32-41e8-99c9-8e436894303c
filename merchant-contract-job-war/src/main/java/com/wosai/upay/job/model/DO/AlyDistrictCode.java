package com.wosai.upay.job.model.DO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class AlyDistrictCode {
    private Long id;

    private String name;

    private String code;

    private String parent_code;

    private Date create_at;

    private Date update_at;

    private Long version;
}