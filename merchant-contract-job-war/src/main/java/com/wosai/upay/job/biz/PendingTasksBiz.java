package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.merchant.config.FeeRateEvent;
import com.wosai.databus.event.merchant.config.MerchantAppConfigChangeEvent;
import com.wosai.upay.job.mapper.PendingTasksMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.DO.PendingTasks;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/5/31 7:19 下午
 **/
@Component
public class PendingTasksBiz {

    @Autowired
    PendingTasksMapper mapper;

    public List<PendingTasks> selectByMerchantSnAndEventType(String merchantSn,String eventType){
        return mapper.selectByMerchantSnAndEventType(merchantSn,eventType);
    }

    public void insertFeeRateTask(FeeRateEvent feeRateEvent,String merchantSn) {
        PendingTasks pendingTasks = new PendingTasks()
                .setSn(merchantSn)
                .setEvent_type(PendingTasks.TYPE_FEERATE)
                .setEvent_msg(JSON.toJSONString(feeRateEvent))
                .setStatus(PendingTasks.STATUS_PENDING);
        mapper.insert(pendingTasks);
    }

    public void insertImmediatelyFeeRateTask(FeeRateEvent feeRateEvent,String merchantSn) {
        PendingTasks pendingTasks = new PendingTasks()
                .setSn(merchantSn)
                .setEvent_type(PendingTasks.TYPE_NOW_FEERATE)
                .setEvent_msg(JSON.toJSONString(feeRateEvent))
                .setStatus(PendingTasks.STATUS_PENDING);
        mapper.insert(pendingTasks);
    }

    public void insertAppFeeRateTask(MerchantAppConfigChangeEvent merchantAppConfigChangeEvent, String merchantSn) {
        PendingTasks pendingTasks = new PendingTasks()
                .setSn(merchantSn)
                .setEvent_type(PendingTasks.TYPE_APP_FEERATE)
                .setEvent_msg(JSON.toJSONString(merchantAppConfigChangeEvent))
                .setStatus(PendingTasks.STATUS_PENDING);
        mapper.insert(pendingTasks);
    }

    public void insertUpdateBankAccountMessage(ContractSubTask subTask) {
        PendingTasks pendingTasks = new PendingTasks()
                .setSn(subTask.getMerchant_sn())
                .setEvent_type(PendingTasks.TYPE_UPDATE_BANK_ACCOUNT_MESSAGE)
                .setEvent_msg(JSON.toJSONString(CollectionUtil.hashMap("subTaskId", subTask.getId())))
                .setStatus(PendingTasks.STATUS_PENDING)
                        .setCreate_at(DateUtils.addSeconds(new Date(), 10));
        mapper.insert(pendingTasks);
    }

    public void delayUpdateBankAccount(PendingTasks pendingTasks) {
        PendingTasks update = new PendingTasks()
                .setId(pendingTasks.getId())
                .setCreate_at(DateUtils.addSeconds(new Date(), 10));
        mapper.updateByPrimaryKeySelective(update);
    }

    public void finishUpdateBankAccount(PendingTasks pendingTasks) {
        PendingTasks update = new PendingTasks()
                .setId(pendingTasks.getId())
                .setStatus(PendingTasks.STATUS_SUCCESS);
        mapper.updateByPrimaryKeySelective(update);
    }



    public void insertWithDrawTask(List<String> withdrawList,String merchantSn) {
        PendingTasks pendingTasks = new PendingTasks()
                .setSn(merchantSn)
                .setEvent_type(PendingTasks.TYPE_WITHDRAW_QUERY)
                .setEvent_msg(JSON.toJSONString(withdrawList))
                .setStatus(PendingTasks.STATUS_PENDING);
        mapper.insert(pendingTasks);
    }

}
