package com.wosai.upay.job.xxljob.direct.antshop;

import com.wosai.upay.job.Constants.AntShopTaskConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.AntShopBiz;
import com.wosai.upay.job.model.DO.AntShopTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * xxl_job_desc: 蚂蚁门店-查询代运营授权结果
 * <AUTHOR>
 * @date 2025/4/11
 */
@Slf4j
@Component("AntShopQueryOperationApplyJobHandler")
public class AntShopQueryOperationApplyJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private AntShopBiz antShopBiz;
    @Autowired
    private ChatBotUtil chatBotUtil;


    @Override
    public String getLockKey() {
        return "AntShopQueryOperationApplyJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        List<AntShopTask> antShopTasks = antShopBiz.getAntShopTasks(
                Collections.singletonList(AntShopTaskConstant.TaskStatus.ALREADY_OPERATION_APPLY), Arrays.asList(AntShopTaskConstant.BusinessType.SCAN_CODE, AntShopTaskConstant.BusinessType.OTHER), param.getBatchSize(), param.getQueryTime()
        );
        //查询0-已提交任务的等级
        antShopTasks.forEach(antShopTask -> {
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                antShopBiz.queryOperationApplyResult(antShopTask);
            } catch (Exception e) {
                log.error("商户号:{} 查询代运营操作结果异常", antShopTask.getMerchant_sn(), e);
                antShopTask.setStatus(AntShopTaskConstant.TaskStatus.FAIL).setDescription("查询代运营操作结果异常");
                antShopBiz.updateAntShopTask(antShopTask);
                chatBotUtil.sendMessageToContractWarnChatBot(String.format("业务:%s,查询代运营操作结果异常,商户号:%s,异常信息:%s", antShopBiz.getBusiness(antShopTask.getBusiness_type()), antShopTask.getMerchant_sn(), e.getMessage()));
            }
        });
    }
}
