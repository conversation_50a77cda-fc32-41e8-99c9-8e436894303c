package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.wosai.upay.job.Constants.ApproveConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.changeAcquirerApprove.ChangeAcquirerApproveDTO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * xxl_job_desc: BatchTask-单个仅入网任务
 * 单个仅入网
 *
 * <AUTHOR>
 * @date 2025/4/17
 */
@Slf4j
@Component("SingleOnlyContractJobHandler")
public class SingleOnlyContractJobHandler extends AbstractMcBatchTaskJobHandler {
    @Override
    public String getLockKey() {
        return "SingleOnlyContractJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        McBatchTaskExample mcBatchTaskExample = new McBatchTaskExample();
        mcBatchTaskExample.or().andStatusEqualTo(0).andEffect_timeLessThanOrEqualTo(new Date()).andTypeEqualTo(12);
        List<McBatchTask> list = mcBatchTaskMapper.selectByExampleWithBLOBs(mcBatchTaskExample);
        list.forEach(mcBatchTask -> {
            Map extra = Maps.newHashMap();
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                final String payload = mcBatchTask.getPayload();
                extra = CommonUtil.string2Map(payload);
                final ChangeAcquirerApproveDTO approveDTO = objectMapper.convertValue(MapUtils.getMap(extra, ApproveConstant.APPROVE_INFO), ChangeAcquirerApproveDTO.class);
                final String merchantSn = approveDTO.getMerchantSn();
                //收单机构
                final String target = approveDTO.getTarget();
                //生成入网任务
                final Long applyId = doApplyOnlyContract(merchantSn, target);
                extra.put(ApproveConstant.EVENT_ID, applyId);
                mcBatchTaskMapper.updateByPrimaryKeySelective(
                        new McBatchTask()
                                .setStatus(1)
                                .setId(mcBatchTask.getId())
                                .setPayload(JSONObject.toJSONString(extra))
                                .setResult("处理中")
                );
            } catch (Exception e) {
                log.error("handleSingleOnlyContract error mc_batch_task {}  exception", mcBatchTask.getId(), e);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(3).setId(mcBatchTask.getId()).setResult(ExceptionUtil.getThrowableMsg(e)));
                callBack(extra, ExceptionUtil.getThrowableMsg(e), AUDIT_EXECUTE_FAIL);
            }
        });
    }
}
