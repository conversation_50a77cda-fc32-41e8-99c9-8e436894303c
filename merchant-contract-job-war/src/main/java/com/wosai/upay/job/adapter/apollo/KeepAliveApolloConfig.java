package com.wosai.upay.job.adapter.apollo;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveConfigModel;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/28
 */
@Component
@Data
public class KeepAliveApolloConfig {

    private Map<String, List<KeepAliveCheckRuleConfig>> keepAliveCheckRules;

    @ApolloJsonValue("${keep_alive_config}")
    private KeepAliveConfigModel keepAliveConfigModel;

    // 缓存过滤和排序后的规则配置
    private final Map<String, List<KeepAliveCheckRuleConfig>> cachedSortedRules = new ConcurrentHashMap<>();


    @ApolloJsonValue("${keep_alive_check_rules}")
    public void setKeepAliveCheckRules(Map<String, List<KeepAliveCheckRuleConfig>> keepAliveCheckRules) {
        this.keepAliveCheckRules = keepAliveCheckRules;
        // 当配置更新时，重新计算并缓存过滤和排序后的规则
        refreshCachedRules();
    }

    /**
     * 刷新缓存的规则配置
     */
    private void refreshCachedRules() {
        if (keepAliveCheckRules != null) {
            for (Map.Entry<String, List<KeepAliveCheckRuleConfig>> entry : keepAliveCheckRules.entrySet()) {
                String scenario = entry.getKey();
                List<KeepAliveCheckRuleConfig> ruleConfigs = entry.getValue();

                // 过滤需要校验的并按优先级排序规则
                List<KeepAliveCheckRuleConfig> sortedRules = new ArrayList<>(ruleConfigs);
                sortedRules = sortedRules.stream()
                        .filter(KeepAliveCheckRuleConfig::isNeedValidation)
                        .sorted(Comparator.comparingInt(KeepAliveCheckRuleConfig::getPriority))
                        .collect(Collectors.toList());

                cachedSortedRules.put(scenario, sortedRules);
            }
        }
    }
}
