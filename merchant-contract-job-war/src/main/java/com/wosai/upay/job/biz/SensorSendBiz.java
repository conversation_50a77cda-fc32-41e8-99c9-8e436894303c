package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.bank.model.MerchantBankAccount;
import com.wosai.upay.bank.model.MerchantBusinessLicense;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.avro.ContractAction;
import com.wosai.upay.job.avro.MerchantBindBank;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/10
 */
@Component
@Slf4j
public class SensorSendBiz {

    @Resource(name = "kafkaTemplate")
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Autowired
    private BankBusinessLicenseService bankBusinessLicenseService;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private ComposeAcquirerBiz acquirerBiz;

    @Lazy
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    @Qualifier("sensorSendThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor sensorSendThreadPoolTaskExecutor;

    @Value("${sensor.topic.bind_bank}")
    private String topic;

    //TODO 间连扫码进件topic
    @Value("${sensor.topic.contract_action}")
    private String contractActionTopic;

    public static final String TRS_MEMO = "账户验证不通过，请引导bd或商户按以下步骤核对：1、检查银行账号和开户人姓名是否正确；2、检查开户人姓名与身份证号是否正确；3、更换一张其他银行卡。";

    public void sendMessageToSensor(String merchantId, String merchantSn, int status, String description) {
        sensorSendThreadPoolTaskExecutor.execute(() -> send(merchantId, merchantSn, status, description));
    }

    private void send(String merchantId, String merchantSn, int status, String description) {
        try {
            Object message = buildMessage(merchantId, merchantSn, status, description);
            kafkaTemplate.send(topic, message);
            log.info("topic:{},merchantSn:{},message:{}", topic, merchantSn, message);
        } catch (Exception e) {
            log.error("sendToSensor topic:{} merchantSn:{}", topic, merchantSn, e);
        }
    }

    private Object buildMessage(String merchantId, String merchantSn, int status, String description) {
        MerchantBankAccount bankAccount = bankBusinessLicenseService.getMerchantBankAccountByMerchantId(merchantId);
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);

        int bankType = this.getType(bankAccount);
        MerchantBindBank merchantBindBank = new MerchantBindBank();
        merchantBindBank.setMerchantId(merchantId);
        merchantBindBank.setMerchantSn(merchantSn);
        merchantBindBank.setBankType(bankType);
        merchantBindBank.setBankName(bankAccount.getBank_name());
        merchantBindBank.setStatus(status);
        merchantBindBank.setMessage(status == ContractStatus.STATUS_BIZ_FAIL ? description : "入网成功");
        merchantBindBank.setCtime(contractStatus.getCreate_at().getTime());
        merchantBindBank.setMtime(contractStatus.getUpdate_at().getTime());
        return merchantBindBank;
    }

    private int getType(MerchantBankAccount merchantBankAccount) {
        //对公账户
        if (BankAccountTypeEnum.PUBLIC.getValue().equals(merchantBankAccount.getType())) {
            return BankAccountTypeEnum.PUBLIC.getValue();
        } else {
            MerchantBusinessLicense license = bankBusinessLicenseService.getBusinessLicenseByMerchantId(merchantBankAccount.getMerchant_id());
            if (license != null && license.getLegal_person_id_number() != null && license.getLegal_person_id_number().equals(merchantBankAccount.getIdentity())) {
                return BankAccountTypeEnum.PERSONAL.getValue();
            } else {
                return 3;
            }
        }
    }

    /**
     * 批量发送消息
     *
     * @param contractTasks
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 14:01 2020/11/9
     */
    public void sendContractTaskMessage2Sensor(List<ContractTask> contractTasks) {
        if (CollectionUtils.isEmpty(contractTasks)) {
            return;
        }
        final Set<String> merchantSnSet = contractTasks.stream().map(ContractTask::getMerchant_sn).collect(Collectors.toSet());
        //查询merchantSn集合对应的商户
        final PageInfo pageInfo = new PageInfo();
        pageInfo.setPage(1);
        pageInfo.setPageSize(contractTasks.size());
        final ListResult listResult = merchantService.findMerchants(pageInfo, CollectionUtil.hashMap("merchant_sns", Lists.newArrayList(merchantSnSet)));
        log.info("sendContractTaskMessage2Sensor listResult:{}", listResult);
        final List<Map> records = listResult.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        //商户号与商户Id对应关系Map
        final Map<String, String> snMerchantIdMap = records.stream().collect(Collectors.toMap(merchant -> MapUtil.getString(merchant, "sn"), merchant -> MapUtil.getString(merchant, "id")));
        log.info("sendContractTaskMessage2Sensor snMerchantIdMap:{}", snMerchantIdMap);
        //实际发送消息
        doSend(contractTasks, snMerchantIdMap);
    }


    /**
     * 实际发消息实现
     *
     * @param contractTasks   任务集合
     * @param snMerchantIdMap 商户号与商户Id对应关系Map
     */
    private void doSend(List<ContractTask> contractTasks, Map<String, String> snMerchantIdMap) {
        try {
            //构建消息体
            List<Object> messages = buildContractActionMessages(contractTasks, snMerchantIdMap);
            //多线程处理
            messages.stream().forEach(message -> CompletableFuture.runAsync(() -> {
                log.info("sendContractActionToSensor topic:{}  message:{}", contractActionTopic, message);
                try {
                    kafkaTemplate.send(contractActionTopic, message);
                } catch (Exception exception) {
                    log.error("sendContractActionKafkaTemplate exception:{}", exception);
                }
            }, sensorSendThreadPoolTaskExecutor));
        } catch (Exception e) {
            log.error("sendContractActionToSensor topic:{}  e:{}", contractActionTopic, e);
        }
    }

    /**
     * 设置message和trsMemo
     *
     * @param contractTask
     * @param contractAction
     * @return
     */
    public ContractAction completeContractAction(ContractTask contractTask, ContractAction contractAction) {
        if (Objects.isNull(contractTask.getStatus()) || Objects.equals(contractTask.getStatus(), TaskStatus.PENDING.getVal())) {//0 或者 null 待处理
            contractAction.setStatus("提交");
            contractAction.setMessage(null);
            contractAction.setTrsMemo(null);
        } else if (TaskStatus.SUCCESS.getVal().equals(contractTask.getStatus())) {//5处理成功
            contractAction.setStatus("成功");
            contractAction.setTrsMemo(null);
            final String successResult = contractTask.getResult();
            if (Objects.nonNull(successResult) && successResult.contains("message")) {
                final Map map = JSON.parseObject(successResult, Map.class);
                contractAction.setMessage(MapUtil.getString(map, "message"));
            } else if (Objects.nonNull(successResult) && successResult.contains("result")) {
                final Map map = JSON.parseObject(successResult, Map.class);
                contractAction.setMessage(MapUtil.getString(map, "result"));
            } else {
                contractAction.setMessage(contractTask.getResult());
            }
        } else if (TaskStatus.FAIL.getVal().equals(contractTask.getStatus())) {//6处理失败
            contractAction.setStatus("失败");
            contractAction.setTrsMemo(TRS_MEMO);
            final String result = contractTask.getResult();
            if (StringUtils.isEmpty(result)) {
                contractAction.setMessage(null);
            } else {
                final Map map = JSON.parseObject(result, Map.class);
                //返回信息
                final String channel = MapUtil.getString(map, "channel");
                final String message = MapUtil.getString(map, "message");
                if (Objects.equals(channel, "lkl_callback")) {
                    final List<Map> mapList = JSONObject.parseArray(message, Map.class);
                    final String contractMemo = mapList.parallelStream().map(item -> {
                        Map data = MapUtils.getMap(item, "data");
                        if (MapUtils.isEmpty(data)) {
                            data = MapUtils.getMap(item, "respData");
                        }
                        return MapUtils.getString(data, "contractMemo");
                    }).collect(Collectors.joining(";"));
                    contractAction.setMessage(contractMemo);
                } else {
                    contractAction.setMessage(message);
                }
            }
        }
        return contractAction;
    }

    /**
     * 消息组装
     *
     * @param contractTasks   任务
     * @param snMerchantIdMap 商户号与商户Id对应关系Ma
     * @return List<Object> 消息集合
     */
    private List<Object> buildContractActionMessages(List<ContractTask> contractTasks, Map<String, String> snMerchantIdMap) {
        return contractTasks.parallelStream().map(contractTask -> {
            final ContractAction contractAction = new ContractAction();
            contractAction.setMerchantSn(contractTask.getMerchant_sn());
            contractAction.setMerchantId(MapUtil.getString(snMerchantIdMap, contractTask.getMerchant_sn()));
            contractAction.setCtime(Objects.isNull(contractTask.getCreate_at()) ? null : DateFormatUtils.format(contractTask.getCreate_at(), "yyyy-MM-dd HH:mm:ss"));
            contractAction.setMtime(Objects.isNull(contractTask.getUpdate_at()) ? null : DateFormatUtils.format(contractTask.getUpdate_at(), "yyyy-MM-dd HH:mm:ss"));
            contractAction.setType(contractTask.getType());
            //提示文案转换
            completeContractAction(contractTask, contractAction);
            final String acquirerNameByAcquire = getAcquireName(contractTask);
            contractAction.setProvider(acquirerNameByAcquire);
            return contractAction;
        }).filter(x -> Objects.nonNull(x.getStatus())).collect(Collectors.toList());
    }

    /**
     * <AUTHOR>
     * @Description: 获取任务对应收单机构的名称
     * @time 09:44
     */
    private String getAcquireName(ContractTask contractTask) {
        String acquirer = null;
        //任务所属规则组id
        String ruleGroupId = contractTask.getRule_group_id();
        if (WosaiStringUtils.isEmpty(ruleGroupId)) {
            acquirer = acquirerBiz.getMerchantAcquirer(contractTask.getMerchant_sn());
        } else {
            RuleGroup ruleGroup = ruleContext.getRuleGroup(ruleGroupId.replace("change2", ""));
            acquirer = ruleGroup.getAcquirer();
        }
        //acquirer对应的中文名称
        return mcAcquirerDAO.getAcquirerName(acquirer);
    }
}
