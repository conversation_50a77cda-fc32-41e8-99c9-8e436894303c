package com.wosai.upay.job.adapter.apollo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shouqianba.cua.enums.contract.FuYouContractSettleTypeEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveConfigModel;
import com.wosai.upay.job.adapter.apollo.model.MailConfigModel;
import com.wosai.upay.job.refactor.model.bo.BankErrorRepairSolutionMappingBO;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.service.WeixinFeeRateActivityServiceImpl.OFFLINE_EDU_TRAIN;
import static com.wosai.upay.job.service.WeixinFeeRateActivityServiceImpl.SCHOOL_CANTEEN;

/**
 * namespace为application用的配置
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApplicationApolloConfig {

    private final Config config = ConfigService.getConfig("application");
    private final ObjectMapper objectMapper = new ObjectMapper();

    public int getAppModuleWhiteType() {
        return config.getIntProperty("app_module_white_type", 0);
    }

    public List<String> getWeixinActivity() {
        return getList("weixin_activity", "[]");
    }

    public List<String> getBlacklistWords() {
        return getList("blacklist-words", "[\"黑名单\",\"商户存在风险\"]");
    }

    public Map getAgentAppidConfig() {
        return getMap("agentAppidConfig", null);
    }

    public Map getQldAppidConfig() {
        return getMap("qldAppidConfig", null);
    }

    public Map getAntShopIndustryMcc() {
        return getMap("ant_shop_industry_mcc", "");
    }

    public Map getAcFees() {
        return getMap("ac_fees", "{}");
    }

    public Map getAlyThirdCategory() {
        return getMap("aly_third_category", "{}");
    }

    public Map getBlueSeaSupply() {
        return getMap("blueSea_supply", JSON.toJSONString(BlueSeaConstant.SUPPLY_ID));
    }

    public Map getWechatGoldRoute() {
        return getMap("wechat-gold-route", null);
    }

    public Map getPrimaryContractTimeOut() {
        return getMap("primary_contract_time_out", "{}");
    }

    public Map getCcbDecpCity() {
        return getMap("ccb_decp_city", null);
    }

    public Map getAppIdSubBiz() {
        return getMap("appId_subBiz", "{}");
    }

    public Map getWeixinOnlineSettlementId() {
        return getMap("weixin_online_settlement_id", "{}");
    }

    public Map getChangeAcquirerCheck() {
        return getMap("change-acquirer-check", "{}");
    }

    public Map getFuyouBusinessMapping() {
        return getMap("fuyou_business_mapping", "{}");
    }

    public Map getAliDirectIndustry() {
        return getMap("ali_direct_industry", null);
    }

    public Map getProcessingDirectMessage() {
        return getMap("processing_direct_message", null);
    }

    public Map getProcessingContractMessage() {
        return getMap("processing_contract_message", null);
    }

    public Map getFuyouTerminalMode() {
        return getMap("fuyou_terminal_mode", "{}");
    }

    public Map getUmsToLkl() {
        return getMap("ums_to_lkl", null);
    }

    public Map getMccConverter() {
        return getMap("mcc_converter", "{}");
    }

    public Map getSameTermDelayTime() {
        return getMap("same_term_delay_time", "{\"1028\":1000}");
    }

    public Map getReBindTerminalForbiddenTime() {
        return getMap("reBindTerminal_forbidden_time", "{}");
    }

    public Map getChannelAuthUrl() {
        return getMap("channel_auth_url", "{}");
    }

    public Map getFuyouRate() {
        return getMap("fuyou_rate_mapping", "{}");
    }


    public Map<String, String> getPayLaterIndustryFee() {
        return getMap("pay_later_industry_fee", null);
    }

    public List<String> getPayLaterForbidWords() {
        return getList("pay_later_forbid_words", "[]");
    }

    public Map getTradeCombIds() {
        return getMap("trade_comb_ids", JSONObject.toJSONString(CollectionUtil.hashMap(SCHOOL_CANTEEN, -1L, OFFLINE_EDU_TRAIN, -2L)));
    }

    public Map<String, String> getReflectCodeMap() {
        return getMap("reflectCodeMap", "{}");
    }

    public List<String> getSqbDistrictCode() {
        return getList("sqbDistrictCode", "[]");
    }

    public Boolean getAutoReceiptAppid() {
        return config.getBooleanProperty("auto-receipt-appid", true);
    }

    public Map<String, String> getHxOrgNoAppId() {
        return getMap("hx_orgNo_appId", "{}");
    }

    public String getHxParentServerOrgno() {
        return config.getProperty("hx-parentServerorgno", "");
    }

    public Map<String, String> getErrorLogQueryRequest() {
        return getMap("error_log_query_request", "");
    }

    public Boolean getIcbcWxConfig() {
        return config.getBooleanProperty("icbc_wx_config", Boolean.TRUE);
    }

    public List<String> getSpecialIndustry() {
        return getList("special_industry", "[]");
    }

    public List<String> getForbidPay() {
        return getList("forbid-pay", "[]");
    }

    public int getAuthAuditTime() {
        return config.getIntProperty("auth_audit_time", 10);
    }

    public String getTipsForApplyingAddMinutes() {
        return config.getProperty("tips_for_applying_add_minutes", null);
    }

    public List<String> getBlueseaTerminalVenderAppAPPid() {
        return getList("bluesea_terminal_vender_app_APPid", "[\"2019121800002436\", \"2019090300001975\"]");
    }

    public List<String> getBlueseaPcTerminalVenderAppAPPid() {
        return getList("bluesea_pc_terminal_vender_app_APPid", "[\"2017030600000083\", \"2019032900021744\", \"2017030600000083\", \"2019091800002007\"]");
    }

    @Deprecated
    public List<String> getSpecialVendorAppAppid() {
        return getList("special_vendor_app_appid", "[\"2018111100000001\",\"2016111100000033\",\"2019072500001878\",\"2019072500001879\",\"2019012900001333\"]");
    }

    /**
     * 需要按小终端报备的终端类型
     *
     * @return
     */
    public List<String> getTerminalLevelVendorAppAppid() {
        return getList("terminal_level_vendor_app_appid", "[\"2022071100004879\",\"2022110300005136\",\"2025031700008740\",\"2024022100006650\",\"2024022100006651\"]");
    }

    public List<String> getSimpleSuperPos() {
        return getList("simple_super_pos", "[\"2022071100004879\",\"2022110300005136\"]");
    }

    public List<String> getFySimpleSuperPos() {
        return getList("fy_simple_super_pos", "[\"2024022100006650\",\"2024022100006651\"]");
    }

    public List<String> getTlV2SimpleSuperPos() {
        return getList("tlV2_simple_super_pos", "[\"20250***********\"]");
    }

    public List<String> getOnlineVendorAppids() {
        return getList("online_vendor_appids", "[]");
    }

    public List<String> getReContractWx() {
        return getList("reContractWx", "[\"lkl\", \"haike\"]");
    }

    public List<String> getCcbDecpOldIds() {
        return getList("ccb_decp_old_ids", "[]");
    }

    /**
     * 需要将参数写入merchant_config,其他名称业务写入merchant_app_config
     *
     * @return
     */

    public List<String> getMultiBusinessWhitelist() {
        return getList("multi_business_whitelist", "[]");
    }

    public List<String> getAcquireOrder() {
        return getList("acquire_order", "[\"haike\",\"lklV3\",\"fuyou\"]");
    }

    public List<Integer> getChangeAll() {
        return getList("change_all", "[1020,1032,1033,1017,1016,1037,1038,1034]");
    }

    public List<Map> getWxChannelActivityIndustry() {
        return getList("wx-channel-activity-industry", "[]");
    }

    public List<String> getIsvOrgPath() {
        return getList("isv-org-path", "[\"00069\",\"00052\"]");
    }

    public List<Map> getChangeAcquirerBizCheck() {
        return getList("change-acquirer-biz-check", "[]");
    }

    public List<String> getUmsForbidOrg() {
        return getList("ums-forbid-org", "[\"00069\",\"00052\"]");
    }

    public List<String> getIgnoreBankPreCheck() {
        return getList("ignore_bankPre_check", "[\"SGJWMX7JFINP\"]");
    }

    public List<String> getLklCallBack() {
        return getList("lklCallBack", "[]");
    }

    public List<String> getSkipDingMsg() {
        return getList("skip-ding-msg", "[]");
    }

    public String getMiniOrganizationId() {
        return config.getProperty("mini-organization-id", "");
    }

    public String getRuleGroup() {
        return config.getProperty("rule-group", "");
    }

    public String getMerchantLklNew() {
        return config.getProperty("merchant_lkl_new", "");
    }

    public String getPayTradeAppId() {
        return config.getProperty("payTradeAppId", "支付业务");
    }

    public String getT9TradeAppId() {
        return config.getProperty("t9TradeAppId", "刷卡收款");
    }

    public String getBankTradeAppId() {
        return config.getProperty("bankTradeAppId", "银行合作");
    }

    public String getOnlinePayment() {
        return config.getProperty("onlinePayment", "线上");
    }

    public String getCrossCityPayment() {
        return config.getProperty("crossCityPayment", "跨城收款");
    }

    public String getMobilePos() {
        return config.getProperty("mobilePos", "手机POS");
    }

    public String getWeixinIndirectIndustry() {
        return config.getProperty("weixin_indirect_industry", "{}");
    }

    public String getNotHandleCombo() {
        return config.getProperty("not-handle-combo", "");
    }

    public String getLklV3AddTermType() {
        return config.getProperty("lklV3_add_term_type", "50.20.10.11.60.118.119.120.40.30");
    }

    public String getMonitorWarn() {
        return config.getProperty("monitor_warn", null);
    }

    public String getQueryTime() {
        return config.getProperty("query_time", null);
    }

    public String getQueryLimit() {
        return config.getProperty("query_limit", null);
    }

    public String getDynamicCron() {
        return config.getProperty("dynamic_cron", "0 0/10 * * * ?");
    }

    public String getCheckUmsLkl() {
        return config.getProperty("check_ums_lkl", "N");
    }

    public int getGrayScaleLklNew() {
        return config.getIntProperty("gray_scale_lkl_new", 30);
    }

    public int getDelayTerminalTask() {
        return config.getIntProperty("delay_terminal_task", 40);
    }

    public int getAcquirerChangeScheduleSemaphore() {
        return config.getIntProperty("acquirer-change-schedule-semaphore", 3);
    }

    public int getHxWxAuthCount() {
        return config.getIntProperty("hx_wx_auth_count", 200);
    }

    public int getQueryUnionOpenLimit() {
        return config.getIntProperty("query_union_open_limit", ScheduleUtil.DEFAULT_QUERY_LIMIT);
    }

    public long getQueryUnionOpenTime() {
        return config.getLongProperty("query_union_open_time", ScheduleUtil.DEFAULT_THREE_HOURS_MILLIS_QUERY);
    }

    public int getDynamicLimit() {
        return config.getIntProperty("dynamic_limit", 50);
    }

    public long getHxBindTaskDelay() {
        return config.getLongProperty("hx_bind_task_delay", 1000L);
    }

    public long getDynamicRedis() {
        return config.getLongProperty("dynamic_redis", 5 * 60L);
    }

    public boolean getChannelGroupV2Flag() {
        return config.getBooleanProperty("getChannelGroupV2Flag", true);
    }

    public boolean getNingboMapping() {
        return config.getBooleanProperty("ningbo_mapping", false);
    }

    public boolean getChangeToLklBizCheckPreCreate() {
        return config.getBooleanProperty("ChangeToLklBiz-checkPreCreate", false);
    }

    public List<String> getSybPreAuth() {
        return getList("tl_pre_auth_industry", "[\"7011\",\"5962\",\"4722\",\"4121\"]");
    }


    private List getList(String key, String defaultValue) {
        try {
            return objectMapper.readValue(config.getProperty(key, defaultValue), List.class);
        } catch (IOException e) {
            log.error("ApolloConfig getList:{},{},{}", key, defaultValue, e);
            return new ArrayList<>();
        }
    }

    private Map getMap(String key, String defaultValue) {
        try {
            return objectMapper.readValue(config.getProperty(key, defaultValue), Map.class);
        } catch (IOException e) {
            log.error("ApolloConfig getMap:{},{},{}", key, defaultValue, e);
            return new HashMap();
        }
    }


    public boolean getCheckAliAuth() {
        return config.getBooleanProperty("check_ali_Auth", false);
    }

    public boolean getAllMerchantLklOld() {
        return config.getBooleanProperty("all_merchant_lkl_old", true);
    }

    public boolean getAllMerchantLklNew() {
        return config.getBooleanProperty("all_merchant_lkl_new", false);
    }

    public boolean getTradeAppSwitch() {
        return config.getBooleanProperty("tradeApp_switch", Boolean.TRUE);
    }

    public boolean getNingboMicro() {
        return config.getBooleanProperty("ningbo-micro", true);
    }

    public boolean getUseSpecialChannel() {
        return config.getBooleanProperty("use_special_channel", Boolean.TRUE);
    }

    public boolean getSyncFeeRate() {
        return config.getBooleanProperty("sync-feerate", true);
    }

    public boolean getIsDelayCreateFeeRateTask() {
        return config.getBooleanProperty("isDelayCreateFeeRateTask", true);
    }

    public boolean getHandleSettlementIdAfterChangeWxParam() {
        return config.getBooleanProperty("handle-settlementId-after-change-wx-param", true);
    }

    public boolean getHandleBizLicenseUpdate() {
        return config.getBooleanProperty("handleBizLicenseUpdate", false);
    }

    /**
     * lklv3 增网增终是否开启
     *
     * @return
     */
    public boolean getLklV3ShopTermSwitch() {
        return config.getBooleanProperty("lklv3_shop_term_switch", false);
    }

    public boolean getPublicPayForRule() {
        return config.getBooleanProperty("publicPayForRule", false);
    }

    public boolean getDirectApplyAliCheckSwitch() {
        return config.getBooleanProperty("direct_apply_ali_check_switch", false);
    }

    public boolean getLklV3CheckMultiMerchantId() {
        return config.getBooleanProperty("lklv3_check_multi_merchantId", true);
    }

    public String getNewBlueSeaAppId() {
        return config.getProperty("alipay-newbluesea-appid", "2015102000490218");
    }

    public String getNewBlueSeaPrivateKey() {
        return config.getProperty("alipay-newbluesea-private-key",
                "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMRwMRyLCIPHg/17PyH+urOvvqbutubkeBbhz6BQ40jE+gth5A/QDyW8BzmuATY/cKDZRaw9xfMTNe9EkaTJz4jzBzEa61VMy9Bw2/ntCOfXzSCuJwDTxknMEK2b0O0VCMSR3lD2XVyf19LxdFrt2PpPSqIwKOCtp8bdeRfDThItAgMBAAECgYEAtcmBmYGM4q77ldO9jA845lqoW8GkH66k98AHLbxNaaVoJKp1rLCFpA0KvrUpx/MTnrcTRB9ylc1cZ02UXuSoGdvCRSzQyGJVSoJsyzEH/M3a1swNJ704LgoDVMYwVKixpuC++vGoo+OcDZMI340rR1LbnaVYjPVtfVlpJ0e91L0CQQDuV0RcHMDjUPdLVbpYUKe4MwR4+5rW7dH7/pmwrYVXBFOxJ1whZXv6c8EajGl8KWEcgO9cAbZNSe0yNu2rjzETAkEA0v4i2qYe2ipSCq1xKnaoHObs8yHM+o3NLo+BiTKhjZe1o/zT+zdypT9kfrKcyHqipg4y/cvwljDm1JwMQ2lXvwJBAMagsZa2W8XVnxIIctDQ/sWStuKAhM0jy0DgMIM+SQZ406qqq6wlYEocF80hQXO5JHZVuaUKxDvrJSMZBb9ZCUMCQFxu94gAdM2w8qY427X1q6qVxEKzkSBHFReLyPz5EGt+hhXkgl2xKemY/wa+aw8tIqpK2C31tV2m6MlF3918ffMCQQCrRsxb2vxJbIFITY1fesOqCDK2KVveAng8eGZ5mZYpkZPpUYNUZhhRklu6t7umcNUsc97nJ06eSaZrd8n+vOM5");
    }

    public String getNewBlueSeaPublicKey() {
        return config.getProperty("alipay-newbluesea-public-key",
                "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDI6d306Q8fIfCOaTXyiUeJHkrIvYISRcc73s3vF1ZT7XN8RNPwJxo8pWaJMmvyTn9N4HQ632qJBVHf8sxHi/fEsraprwCtzvzQETrNRwVxLO5jVmRGi60j8Ue1efIlzPXV9je9mkjzOmdssymZkh2QhUrCmZYI/FCEa3/cNMW0QIDAQAB");
    }

    public String getNewBlueSignType() {
        return config.getProperty("alipay-newbluesea-sign-type", "RSA");
    }

    public Boolean getWechatActivityStatus() {
        return config.getBooleanProperty("wechat_activity_status", Boolean.TRUE);
    }

    public List<String> getSdTonglianV2Path() {
        return getList("sd_tonglianV2_path", "[\"00069\"]");
    }

    /**
     * 获取所有异常返回信息
     *
     * @return
     */
    public Map<String, Map<String, String>> getNewBlueSeaErrorMessage() {
        final List<Map<String, String>> list = getList("new_blue_sea_error_message", "[]");
        return list.stream().collect(Collectors.toMap(x -> x.get("message"), x -> x, (val1, val2) -> val1));
    }

    /**
     * 获取银行支付的支付方式
     *
     * @return
     */
    public Map<String, List<Integer>> getBankSupportPayWay() {
        return (Map<String, List<Integer>>) getMap("bank_support_payWay", "{}");
    }

    /**
     * 获取批量报备最大值
     *
     * @return
     */
    public Integer getBatchThreshold() {
        return config.getIntProperty("batch_threshold", 3000);
    }

    /**
     * 每日批量入网最大值
     *
     * @return
     */
    public Integer getBatchOnlyContract() {
        return config.getIntProperty("batch_onlyContract_threshold", 3000);
    }


    public MailConfigModel getMailConfig() {
        String mailConfig = config.getProperty("mail_config", "{}");
        return JSON.parseObject(mailConfig, MailConfigModel.class);
    }

    /**
     * crm审核中状态展示
     *
     * @return
     */
    public Map<String, List<Map<String, String>>> getBankViewProcess() {
        return getMap("bank_view_process", "{}");
    }

    /**
     * 获取富友测试商户名称关键字
     *
     * @return 测试商户名称关键字
     */
    public String getFuYouSettleTypeTestNameKey() {
        return config.getProperty("fuYouSettleTypeTestNameKey", "CS商户FY");
    }

    /**
     * 获取富友默认结算类型
     *
     * @return 结算类型
     */
    public String getFuYouDefaultContractSettleType() {
        return config.getProperty("fuYouContractSettleType", FuYouContractSettleTypeEnum.MANUAL_SETTLEMENT_D1.getValue().toString());
    }


    public boolean getHaikeStoreTerminalSwitch() {
        return config.getBooleanProperty("haike_store_terminal_switch", false);
    }

    /**
     * 获取银行报错码映射的解决方法配置
     *
     * @return 银行报错码映射的解决方法
     */
    public List<BankErrorRepairSolutionMappingBO> listBankErrorRepairSolutionMapping() {
        String mappingString = config.getProperty("bankNameNameErrorRepairSolutionMapping", "[]");
        return JSON.parseArray(mappingString, BankErrorRepairSolutionMappingBO.class);
    }

    /**
     * 获取可以自动切换到三方的银行列表 ["ccb","psbc","hxb","icbc","pab"]
     *
     * @return 银行列表
     */
    public List<String> listEnableAutoChangeToThirdBanks() {
        return JSON.parseArray(config.getProperty("enableAutoChangeToThirdBanks", "[\"ccb\",\"psbc\",\"hxb\",\"icbc\",\"pab\"]"), String.class);
    }

    /**
     * 获取可以自动切换到三方的城市列表
     *
     * @return 城市列表
     */
    public List<String> listEnableAutoChangeToThirdCitys() {
        return JSON.parseArray(config.getProperty("enableAutoChangeToThirdCitys", "[]"), String.class);
    }

    /**
     * 获取可以自动切换到三方的组织列表
     *
     * @return 机构列表
     */
    public List<String> listEnableAutoChangeToThirdOrgs() {
        return JSON.parseArray(config.getProperty("enableAutoChangeToThirdOrgs", "[\"直营\",\"服务商\",\"线上业务\"]"), String.class);
    }

    public Map getZhimaMerchantAppId() {
        return getMap("zhima_merchant_app_id", "{}");
    }

    /**
     * 获取商户上下文key与表名的映射
     * {
     * "merchant" : "merchant",
     * "merchantBusinessLicense" : "merchant_business_license_info",
     * "bankAccount" : "merchant_bank_account",
     * "bankInfo" : "bank_info"
     * }
     *
     * @return 商户上下文key与表名的映射
     */
    public Map<String, String> getMerchantTableNameToContextKeyMap() {
        String defaultMapping = "{\n" +
                "  \"merchant\" : \"merchant\",\n" +
                "  \"merchant_business_license_info\" : \"merchantBusinessLicense\",\n" +
                "  \"merchant_bank_account\" : \"bankAccount\",\n" +
                "  \"bank_info\" : \"bankInfo\"\n" +
                "}";
        return getMap("merchantContextKeyToTableNameMapping", defaultMapping);
    }


    public Long getMobilePosTradeComboId() {
        return config.getLongProperty("mobile_pos_tradecomboId", 15718L);
    }


    public Boolean getMobilePosOpenFlag() {
        return config.getBooleanProperty("mobile_pos_flag", Boolean.FALSE);
    }

    public Boolean getCommonImportToSystemAcquirerChangeSwitch() {
        return config.getBooleanProperty("commonImportToSystemAcquirerChangeSwitch", Boolean.FALSE);
    }

    /**
     * 获取当前在使用的收单机构规则组
     *
     * @return 收单机构规则组
     */
    public List<String> listInUseDefaultMcRuleGroup() {
        return JSON.parseArray(config.getProperty("inUseAcquirerDefaultMcRuleGroups", "[\"lklorg\",\"fuyou\",\"haike\"]"), String.class);
    }

    /**
     * 获取自动切换到三方需要通知的邮箱id列表
     *
     * @return 机构列表
     */
    public List<Long> listBankAutoChangeThirdNotifyMailIds() {
        return JSON.parseArray(config.getProperty("enableAutoChangeToThirdNotifyMailIds", "[287]"), Long.class);
    }

    /**
     * 获取可以小微升级重新进件的收单机构列表
     *
     * @return 收单机构列表
     */
    public List<String> listEnableMicroLicenseUpgradeReContractedAcquirers() {
        return JSON.parseArray(config.getProperty("enableMicroLicenseUpgradeReContractedAcquirers", "[\"lklV3\",\"haike\"]"), String.class);
    }


    /**
     * 获取V3版本小微升级重新进件的收单机构列表
     *
     * @return 收单机构列表
     */
    public List<String> listV3EnableMicroLicenseUpgradeReContractedAcquirers() {
        return JSON.parseArray(config.getProperty("v3EnableMicroLicenseUpgradeReContractedAcquirers", "[\"haike\",\"fuyou\"]"), String.class);
    }

    public Integer getMicroUpgradeWaitMinutes() {
        return config.getIntProperty("microUpgradeWaitMinutes", 5);
    }

    // todo 临时测试代码
    public boolean getTestRollbackFlag() {
        return config.getBooleanProperty("microTestRollbackFlag", true);
    }

    public boolean getFuyouNewUpdateBankAccountSwitch() {
        return config.getBooleanProperty("fuyou_update_bank_account_switch", false);
    }

    public int getMicroUpgradeBeginHour() {
        return config.getIntProperty("microUpgradeBeginHour", 1);
    }

    public int getMicroUpgradeEndHour() {
        return config.getIntProperty("microUpgradeEndHour", 6);
    }

    public boolean isMchNameCanNotChangeReConsider() {
        return config.getBooleanProperty("isMchNameCanNotChangeReConsider", true);
    }

    /**
     * 获取ccb规则组 根据地区配置
     *
     * @return ccb规则组配置信息
     */
    public Map<String, String> getCcbRuleGroup() {
        return getMap("ccb_rule_group", "{}");
    }

    public List<Map<String, Object>> getBlackRiskKey() {
        return getList("blackRiskKey", "{}");
    }


    public boolean getPabStatusV2() {
        return config.getBooleanProperty("pab_status_v2", Boolean.FALSE);
    }

    public int getLicenseUpdateVerifyAccountExpiredDays() {
        return config.getIntProperty("licenseUpdateVerifyAccountExpiredDays", 60);
    }

    public int getMicroUpgradeBeginHourV2() {
        return config.getIntProperty("microUpgradeBeginHourV2", 1);
    }

    public int getMicroUpgradeEndHourV2() {
        return config.getIntProperty("microUpgradeEndHourV2", 6);
    }

    public boolean isIgnoreTLV2Callback() {
        return config.getBooleanProperty("ignore_tonglian_v2_callback", Boolean.TRUE);
    }

    public boolean getFuyouDoubleAccountSwitch() {
        return config.getBooleanProperty("fuyouDoubleAccountSwitch", true);
    }

    public List<String> listLicenseTaskNotSupportChangeAccountAcquirer() {
        return JSON.parseArray(config.getProperty("licenseTaskNotSupportChangeAccountAcquirer", "[\"fuyou\",\"tonglianV2\",\"umb\"]"), String.class);
    }


    public Set<String> getCmbcProfitSharingMode() {
        final Map map = getCmbcProfitSharingModeInfo();
        if (CollectionUtils.isEmpty(map.keySet())) {
            return Collections.unmodifiableSet(new HashSet<>());
        }
        return map.keySet();
    }


    public Map<String, String> getCmbcProfitSharingModeInfo() {
        final Map map = getMap("cmbc_profit_sharing_mode_info", "{}");
        if (CollectionUtils.isEmpty(map.keySet())) {
            return Collections.unmodifiableMap(new HashMap<>());
        }
        return map;
    }


    public Set<String> getCmbcContractBranch() {
        final Map<String, Map<String, String>> map = getCmbcContractBranchInfo();
        if (CollectionUtils.isEmpty(map.keySet())) {
            return Collections.unmodifiableSet(new HashSet<>());
        }
        return map.keySet();
    }


    public Map<String, Map<String, String>> getCmbcContractBranchInfo() {
        final Map map = getMap("cmbc_contract_branch_info", "{}");
        if (CollectionUtils.isEmpty(map.keySet())) {
            return Collections.unmodifiableMap(new HashMap<>());
        }
        return map;
    }


    public List<String> getBankruleGroupIds() {
        return getList("bank_rule_group_ids", "[]");
    }

    /**
     * 获取结算卡告警开关
     *
     * @return true表示开启结算卡告警，false表示不开启
     */
    public boolean getSettlementCardAlarm() {
        return config.getBooleanProperty("settlement_card_alarm", true);
    }

    /**
     * 获取富友更新商户号和结算账户的开关
     *
     * @return true表示开启，false表示不开启
     */
    public boolean getFuYouEnableUpdateLicenseAndAccountTogether() {
        return config.getBooleanProperty("fuyou_update_license_account_together", true);
    }


    /**
     * v3小微升级开关
     *
     * @return true表示小微升级全部走V3，false表示不走V3
     */
    public boolean getMicroupgradeV3AllOpen() {
        return config.getBooleanProperty("microUpgrade_v3_all_open", false);
    }

    /**
     * micro_upgrade_v3_rule 配置结构
     */
    @Data
    public static class MicroUpgradeV3RuleConfig {
        public String mode;
        public List<String> whitelist;
        public Integer hashMod;
        public List<Integer> hashHit;
        public List<String> orglist; // 新增：组织路径列表
    }

    /**
     * 获取解析后的 micro_upgrade_v3_rule 配置对象
     */
    public MicroUpgradeV3RuleConfig getMicroUpgradeV3RuleConfig() {
        String configStr = null;
        try {
            configStr = config.getProperty("micro_upgrade_v3_rule", null);
            if (StringUtils.isBlank(configStr)) {
                log.warn("Apollo配置项micro_upgrade_v3_rule为空，默认返回none模式");
                MicroUpgradeV3RuleConfig defaultConfig = new MicroUpgradeV3RuleConfig();
                defaultConfig.mode = "none";
                defaultConfig.whitelist = Collections.emptyList();
                defaultConfig.hashMod = null;
                defaultConfig.hashHit = Collections.emptyList();
                defaultConfig.orglist = Collections.emptyList();
                return defaultConfig;
            }
            MicroUpgradeV3RuleConfig ruleConfig = JSON.parseObject(configStr, MicroUpgradeV3RuleConfig.class);
            if (ruleConfig == null || StringUtils.isBlank(ruleConfig.mode)) {
                log.warn("Apollo配置项micro_upgrade_v3_rule解析失败，默认返回none模式");
                MicroUpgradeV3RuleConfig defaultConfig = new MicroUpgradeV3RuleConfig();
                defaultConfig.mode = "none";
                defaultConfig.whitelist = Collections.emptyList();
                defaultConfig.hashMod = null;
                defaultConfig.hashHit = Collections.emptyList();
                defaultConfig.orglist = Collections.emptyList();
                return defaultConfig;
            }
            return ruleConfig;
        } catch (Exception e) {
            log.error("getMicroUpgradeV3RuleConfig异常，配置:{}，默认返回none模式", configStr, e);
            MicroUpgradeV3RuleConfig defaultConfig = new MicroUpgradeV3RuleConfig();
            defaultConfig.mode = "none";
            defaultConfig.whitelist = Collections.emptyList();
            defaultConfig.hashMod = null;
            defaultConfig.hashHit = Collections.emptyList();
            defaultConfig.orglist = Collections.emptyList();
            return defaultConfig;
        }
    }


    public Map<String, String> getUmbIndustryToGroupMap() {
        final Map map = getMap("umb_industry_group_mapping", "{}");
        if (CollectionUtils.isEmpty(map.keySet())) {
            return Collections.unmodifiableMap(new HashMap<>());
        }
        return map;
    }


    /**
     * 获取立即切换的provider集合
     *
     * @return List<String>
     */
    public List<String> getImmediateChangeBank() {
        return JSON.parseArray(config.getProperty("immediateChangeBank", "[\"1044\",\"1046\",\"1047\"]"), String.class);
    }

    public List<String> getAsyncSubAppidRetryMsg() {
        return getList("async_subappid_retry_msg", "[]");
    }

    /**
     * 获取富友云闪付不准入的省份列表
     *
     * @return 省份列表
     */
    public List<String> getFuYouUnionPayForbidProvinces() {
        return getList("fuyou_unionpay_forbid_provinces", "[]");
    }

    /**
     * 获取富友云闪付不准入的城市列表
     *
     * @return 城市列表
     */
    public List<String> getFuYouUnionPayForbidCities() {
        return getList("fuyou_unionpay_forbid_cities", "[]");
    }

    /**
     * 指定错误，可以忽略AT进件失败
     *
     * @return
     */
    public List<String> getPaywayFailMsg() {
        return getList("payway_fail_msg", "[]");
    }

    public List<Integer> getMicroUpgradeIgnroePayways() {
        return getList("micro_upgrade_ignroe_payways", "[]");
    }
}
