package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.shouqianba.cua.enums.status.DisableStatusEnum;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.KeepAliveProviderParamsAndDateCalculator;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveValidationScenarioEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.AuthStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * AT商户号状态规则
 *
 * <AUTHOR>
 * @date 2025/8/28
 */
@Component
public class ATStatusRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private KeepAliveProviderParamsAndDateCalculator calculator;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;


    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            if (KeepAliveValidationScenarioEnum.OPEN_CIRCLE.equals(context.getKeepAliveValidationScenarioEnum())) {
                return checkForOpenCircle(context, ruleConfig);
            } else {
                return checkForPreExecuteCircleOrExecute(context, ruleConfig);
            }

        } catch (Exception e) {
            logger.error("执行AT商户号状态规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    private KeepAliveCheckRuleResult checkForPreExecuteCircleOrExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        String subMchId = context.getTaskDO().getSubMchId();
        Optional<MerchantProviderParamsDO> providerParams = merchantProviderParamsDAO.getMerchantProviderParamsByMerchantSnAndPayMerchantId(context.getMerchantSn(), subMchId);
        if (!providerParams.isPresent()) {
            return createFailureResult(String.format("商户没有找到交易参数信息 %s，不允许执行", subMchId), "AT_STATUS_BLOCKED");
        }
        if (Objects.equals(providerParams.get().getDisableStatus(), DisableStatusEnum.DISABLE.getValue())) {
            return createFailureResult(String.format("商户在AT状态为禁用 %s，不允许执行", subMchId), "AT_STATUS_BLOCKED");
        }
        if (Objects.equals(providerParams.get().getAuthStatus(), AuthStatusEnum.NOT.getValue())) {
            return createFailureResult(String.format("商户在AT状态为未认证 %s，不允许执行", subMchId), "AT_STATUS_BLOCKED");
        }
        return createSuccessResult("商户AT商户号状态正常，通过检查");
    }

    private KeepAliveCheckRuleResult checkForOpenCircle(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = Objects.nonNull(context.getKeepAliveParams()) ? context.getKeepAliveParams() : calculator.calculateKeepAliveParams(context.getMerchantSn());
        for (MerchantProviderParamsDO merchantProviderParamsDO : merchantProviderParamsDOS) {
            if (Objects.equals(merchantProviderParamsDO.getDisableStatus(), DisableStatusEnum.DISABLE.getValue())) {
                return createFailureResult(String.format("商户在AT状态为禁用 %s，不允许执行", merchantProviderParamsDO.getPayMerchantId()), "AT_STATUS_BLOCKED");
            }
            if (Objects.equals(merchantProviderParamsDO.getAuthStatus(), AuthStatusEnum.NOT.getValue())) {
                return createFailureResult(String.format("商户在AT状态为未认证 %s，不允许执行", merchantProviderParamsDO.getPayMerchantId()), "AT_STATUS_BLOCKED");
            }

        }
        context.setKeepAliveParams(merchantProviderParamsDOS);
        return createSuccessResult("商户AT商户号状态正常，通过检查");
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.AT_STATUS;
    }
}
