package com.wosai.upay.job.model.DO;

import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.job.model.dto.ProviderTerminalContext;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
public class ProviderTerminalTask {
    private Long id;

    private String merchant_sn;

    private Integer type;

    private Integer status;

    private Integer retry;

    private Date create_at;

    private Date update_at;

    private Date priority;

    private String context;

    private String result;

    public ProviderTerminalContext getContextInfo() {
        return JSONObject.parseObject(this.context, ProviderTerminalContext.class);
    }

}