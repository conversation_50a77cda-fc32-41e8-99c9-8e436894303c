package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.service.bank.entity.FeeRateSnapshot;
import com.wosai.trade.service.bank.entity.request.GetCurrentSnapshotRequest;
import com.wosai.trade.service.bank.entity.request.RestoreFeeRateSnapshotRequest;
import com.wosai.trade.service.bank.entity.response.CurrentFeeRateSnapshotResponse;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.JdBiz;
import com.wosai.upay.job.biz.acquirePos.AbstractT9HandleService;
import com.wosai.upay.job.biz.acquirePos.FyT9HandleService;
import com.wosai.upay.job.mapper.McAcquirerChangeMapper;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.dto.ProviderDto;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.model.direct.GetDevParamReq;
import com.wosai.upay.service.CrmEdgeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import vo.ApiRequestParam;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description: 间连收单机构调整
 * <AUTHOR>
 * @Date:
 */
@Slf4j
public abstract class AbstractIndirectAcquirerChangeBiz extends AbstractAcquirerChangeBiz {

    @Autowired
    private McProviderDAO mcProviderDAO;

    @Autowired
    private McAcquirerChangeMapper acquirerChangeMapper;

    @Value("${lkl_pso_dev_code}")
    public String lklPsoDevCode;
    @Value("${fy_pso_dev_code}")
    public String fyPsoDevCode;
    @Autowired
    FyT9HandleService fyPosBiz;
    @Autowired
    CrmEdgeService crmEdgeService;

    @Autowired
    private JdBiz jdBiz;
    /**
     * 间连通道切换到银行通道时,保存间连通道的活动和快照信息,这样切换到间连的时候可以恢复
     * https://jira.wosai-inc.com/browse/CUA-6659
     * @param change
     */
    @Override
    protected void sourceAcquirerPostBiz(McAcquirerChange change) {
        //获取最新记录
        change = acquirerChangeMapper.selectByPrimaryKey(change.getId());
        String merchantSn = change.getMerchant_sn();
        List<Integer> payWaySupport ;
        //当前通道支持哪些payway
        if(getProviderCode(change.getTarget_acquirer()) == ProviderEnum.PROVIDER_LAKALA_V3.getValue()) {
            payWaySupport = Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.UNIONPAY.getValue(), PaywayEnum.BESTPAY.getValue());
        }else {
            Optional<McProviderDO> providerOptional =  mcProviderDAO.getByProvider(String.valueOf(getProviderCode(change.getTarget_acquirer())));
            if(!providerOptional.isPresent()) {
                log.info("merchantSn:{},间连通道切换到银行通道没有找到mc_provider表记录",merchantSn);
                return;
            }
            McProviderDO mcProviderDO = providerOptional.get();
            if (StringUtils.isBlank(mcProviderDO.getMetadata())) {
                log.info("merchantSn:{},间连通道切换到银行通道没有找到支持的payway",merchantSn);
                return;
            }
            List<ProviderDto.Payway> paywayList = JSON.parseArray(mcProviderDO.getMetadata(), ProviderDto.Payway.class);
            payWaySupport = paywayList.parallelStream().map(payway -> payway.getPayway()).collect(Collectors.toList());
        }

        GetCurrentSnapshotRequest snapshotRequest = new GetCurrentSnapshotRequest();
        snapshotRequest.setMerchantSn(merchantSn);
        snapshotRequest.setPayWayList(payWaySupport);
        CurrentFeeRateSnapshotResponse snapshotResponse = null;
        try {
            snapshotResponse = bankFeeRateService.getFeeRateSnapshot(snapshotRequest);
            List<FeeRateSnapshot> snapshotList = snapshotResponse.getFeeRateSnapshotList();
            log.info("indirectSourceAcquirerPostBiz merchantSn:{},getFeeRateSnapshot返回:{}",merchantSn, JSONObject.toJSONString(snapshotList));
            // 查找商户间连费率信息快照保存下来
            Map extra = CommonUtil.string2Map(change.getExtra());
            extra.put(INDIRECT_COMBO_SNAPSHOT, snapshotList);

            McAcquirerChange updateValue = new McAcquirerChange();
            updateValue.setId(change.getId());
            updateValue.setExtra(CommonUtil.map2String(extra));
            acquirerChangeMapper.updateByPrimaryKeySelective(updateValue);
        } catch (Exception exception) {
            log.error("indirectSourceAcquirerPostBiz  merchantSn:{},error:{}",merchantSn, exception);
        }
    }

    /**
     * 间连收单机构业务处理
     * @param change
     */
    @Override
    protected void targetAcquirerPostBiz(McAcquirerChange change) {
        //获取最新记录(防止上一步写入extra中的数据被覆盖)
        change = acquirerChangeMapper.selectByPrimaryKey(change.getId());
        String sourceAcquirer = change.getSource_acquirer();
        String merchantSn = change.getMerchant_sn();
        //原收单机构是间连,目标收单机构也是间连就不用设置套餐,这样可以避免查询的是到的是在原来收单机构的套餐快照导致不准确
        if(isThirdPartyOrg(sourceAcquirer)) {
            log.info("targetAcquirerPostBiz  merchantSn:{},sourceAcquirer:{},原收单机构是间连,目标收单机构也是间连就不用设置套餐",merchantSn,sourceAcquirer);
            return;
        }
        // 检查历史切换成功记录
        McAcquirerChange latestSuccessApply = getLastedThirdPartyToBankSuccessAcquirerChange(merchantSn);
        List<FeeRateSnapshot> snapshotList = null;
        if(Objects.nonNull(latestSuccessApply)) {
            Map extra = CommonUtil.string2Map(latestSuccessApply.getExtra());
            snapshotList = (List<FeeRateSnapshot>) WosaiMapUtils.getObject(extra, INDIRECT_COMBO_SNAPSHOT);
        }
        //兼容上线前没有切换记录的数据,如果没有快照就走取消套餐逻辑
        if(CollectionUtils.isEmpty(snapshotList)) {
            log.info("merchantSn:{},indirectComboSnapshot为空",merchantSn);
            AbstractBankDirectAcquirerChangeBiz sourceChangeBiz = null;
            try {
                sourceChangeBiz = applicationContext.getBean(sourceAcquirer + "-AcquirerChangeBiz", AbstractBankDirectAcquirerChangeBiz.class);
            } catch (Exception e) {
                // 找不到对应的处理类并且是银行的话，尝试返回一个 commonBank 的类
                if (isBankOrg(change.getSource_acquirer())) {
                    sourceChangeBiz = applicationContext.getBean("commonBank-AcquirerChangeBiz", AbstractBankDirectAcquirerChangeBiz.class);
                }
                if (sourceChangeBiz == null) {
                    log.error("targetAcquirerPostBiz 获取业务方失败:{}",e);
                    return;
                }
            }
            sourceChangeBiz.cancelCombo(change);
            return;
        }
        //调用交易组恢复在间连的费率套餐
        List<FeeRateSnapshot> finalSnapshotList = snapshotList;
        CompletableFuture.runAsync(() -> {
            try {
                //支付组会校验当前收单机构,由于事务原因所以把这个延迟调用一下
                Thread.sleep(1000);
                RestoreFeeRateSnapshotRequest restoreFeeRateSnapshotRequest = new RestoreFeeRateSnapshotRequest();
                restoreFeeRateSnapshotRequest.setMerchantSn(merchantSn);
                restoreFeeRateSnapshotRequest.setFeeRateSnapshotList(finalSnapshotList);
                bankFeeRateService.restoreFeeRateSnapshot(restoreFeeRateSnapshotRequest);
            } catch (Exception exception) {
                log.error("restoreFeeRateSnapshot merchantSn:{},error:{}", merchantSn, exception);
            }

        });
    }

    /**
     * 获取商户最新的三方切银行成功的记录
     *
     * @param merchantSn 商户号
     * @return 最新的三方切银行记录
     */
    public McAcquirerChange getLastedThirdPartyToBankSuccessAcquirerChange(String merchantSn) {
        List<McAcquirerChange> mcAcquirerChanges = changeDao.listSuccessChangeByMerchantSn(merchantSn);
        List<String> thirdAcquirers = mcAcquirerDAO.listThirdAcquirers();
        return mcAcquirerChanges.stream()
                .filter(t -> thirdAcquirers.contains(t.getSource_acquirer())).max(Comparator.comparing(McAcquirerChange::getCreate_at))
                .orElse(null);
    }

    /**
     * 处理变更信息以调整POS机的费率。
     * @param change 包含商户和目标收单行变更信息的对象。
     * 该方法不返回任何值，但会根据变更信息更新POS机的费率设置。
     */
    private void t9PosFeeRate(McAcquirerChange change) {
        // 获取变更信息中的商户号和目标收单行
        String merchantSn = change.getMerchant_sn();
        String targetAcquirer = change.getTarget_acquirer();
        // 收单行代码映射，用于根据目标收单行获取相应的设备代码
        Map<String, String> acquireDevCodeMap = CollectionUtil.hashMap(
                McConstant.ACQUIRER_LKL, lklPsoDevCode,
                McConstant.ACQUIRER_LKLV3, lklPsoDevCode,
                McConstant.ACQUIRER_FUYOU, fyPsoDevCode
        );
        String devCode = acquireDevCodeMap.get(targetAcquirer);
        if(StringUtils.isEmpty(devCode)) {
           return;
        }
        // 根据商户号获取商户信息
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        //请求参数，以获取设备参数
        final ApiRequestParam param = new ApiRequestParam();
        final GetDevParamReq devParamReq = new GetDevParamReq();
        devParamReq.setDevCode(devCode);
        devParamReq.setMerchantId(merchantId);
        param.setBodyParams(devParamReq);
        // 调用服务获取设备参数
        Map devParam = Optional.ofNullable(crmEdgeService.getDevParam(param)).orElseGet(HashMap::new);
        // 解析设备参数中的费率信息和交易组合ID
        Map feeMap = (Map) devParam.get(AbstractT9HandleService.FEEMAP);
        long tradeComboId = BeanUtil.getPropLong(devParam, AbstractT9HandleService.TRADECOMBO_ID);
        // 如果费率信息为空，则直接返回
        if(MapUtils.isEmpty(feeMap)) {
            return;
        }
        // 调用业务逻辑，更新POS机的费率组合设置
        fyPosBiz.t9PosFeeCombo(feeMap, tradeComboId, merchantSn);
    }


    /**
     * 如果京东钱包交易参数使用的收单机构和目标机构相同,那就保留.否则将京东支付相关参数删除
     * @param merchantId
     * @param targetAcquirer
     * @param merchantSn
     */
    public void invalidJdPay(String merchantId, String targetAcquirer, String merchantSn) {
        jdBiz.openFail(merchantSn,"当前收单机构不支持京东白条");
        //去除京东钱包支付
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.JD_WALLET.getValue());
        final String provider = MapUtils.getString(merchantConfig, MerchantConfig.PROVIDER);
        final Optional<McProviderDO> mcProviderDO = mcProviderDAO.getByProvider(provider);
        //当前京东支付所使用的收单机构和目标机构相同,那就保留.否则将京东支付相关参数删除
        if(Objects.equals(mcProviderDO.map(McProviderDO::getAcquirer).orElse(null),targetAcquirer)) {
            return;
        }
        //更新交易参数 将对应的交易参数信息清空掉
        if (MapUtil.isNotEmpty(merchantConfig)) {
            Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName("provider_trade_params_key");
            String tradeParamsKey = MapUtils.getString(providerTradeParamsKey, provider);
            Map params = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS);
            if(Objects.isNull(params)) {
               return;
            }
            params.remove(tradeParamsKey);
            tradeConfigService.updateMerchantConfig(CollectionUtil.hashMap(
                    MerchantConfig.PARAMS, params,
                    DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID)));
        }
    }
}
