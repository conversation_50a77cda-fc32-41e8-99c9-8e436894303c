package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 支付业务通道范围规则
 *
 * <AUTHOR>
 * @date 2025/8/28
 */
@Component
public class PayProviderScopeRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private TradeConfigService tradeConfigService;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            List<Map> merchantConfigs = tradeConfigService.getMerchantConfigsByMerchantId(context.getMerchantId());
            List<String> providerList = (List<String>) WosaiMapUtils.getObject(ruleConfig.getParams(), "providerList");
            for (Map merchantConfig : merchantConfigs) {
                String provider = WosaiMapUtils.getString(merchantConfig, MerchantConfig.PROVIDER);
                if (WosaiStringUtils.isNotEmpty(provider) && !providerList.contains(provider)) {
                    return createFailureResult(
                            String.format("商户支付业务通道 %s 不在范围中，不允许执行", provider),
                            "PAY_PROVIDER_SCOPE_BLOCKED");
                }
            }
            return createSuccessResult("商户支付业务通道范围规则，通过检查");

        } catch (Exception e) {
            logger.error("执行支付业务通道范围规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.PAY_PROVIDER_SCOPE;
    }
}
