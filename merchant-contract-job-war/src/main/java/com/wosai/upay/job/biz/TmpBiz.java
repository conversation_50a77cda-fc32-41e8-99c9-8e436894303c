package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.ConstantsEvent;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianParam;
import com.wosai.upay.merchant.contract.service.TongLianService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-05-26
 */
@Component
public class TmpBiz {

    @Autowired
    private ParamContextBiz paramContextBiz;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private TongLianService tonglianService;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;


    public CommonResult copyBankToTl(String tlMchSn, String lklMchSn) {
        try {
            Map lklMch = merchantService.getMerchantByMerchantSn(lklMchSn);
            if (WosaiMapUtils.isEmpty(lklMch)) {
                throw new RuntimeException("商户 " + lklMchSn + " 不存在");
            }

            Map lklBankAccount = merchantService.getMerchantBankAccountByMerchantId(BeanUtil.getPropString(lklMch, DaoConstants.ID));
            if (WosaiMapUtils.isEmpty(lklBankAccount)) {
                throw new RuntimeException("商户 " + lklMchSn + " 银行卡不存在");
            }

            ContractEvent event = new ContractEvent();
            event.setEvent_type(ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS);
            Map eventMsg = CollectionUtil.hashMap(
                    ConstantsEvent.EVENT_TYPE_SOURCE, lklBankAccount
            );
            event.setEvent_msg(JSON.toJSONString(eventMsg));

            Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(tlMchSn, event);
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN.getValue(), TongLianParam.class);

            ContractResponse contractResponse = updateMerchantBankAccountToTl(paramContext, tongLianParam, 1);
            if (contractResponse.isSuccess()) {
                return new CommonResult(200, "成功");
            } else {
                return new CommonResult(500, contractResponse.getMessage());
            }
        } catch (Exception e) {
            return new CommonResult(500, e.getMessage());
        }

    }

    private ContractResponse updateMerchantBankAccountToTl(Map<String, Object> paramContext, TongLianParam tongLianParam, int retry) {
        ContractResponse contractResponse = tonglianService.updateMerchantBankAccount(paramContext, tongLianParam);
        if (retry >= 10) {
            return contractResponse;
        }
        // 特殊处理
        if (!StringUtils.isEmpty(contractResponse.getMessage()) && contractResponse.getMessage().contains("相同客户名不允许进件")) {
            String merchantName = BeanUtil.getPropString(paramContext, "merchant.name");
            BeanUtil.setNestedProperty(paramContext, "merchant.name", nextMerchantName(merchantName));
            return updateMerchantBankAccountToTl(paramContext, tongLianParam, retry + 1);
        } else {
            return contractResponse;
        }
    }

    private String nextMerchantName(String merchantName) {
        String digit = "";
        for (int i = merchantName.length() - 1; i >= 0; i--) {
            if (!Character.isDigit(merchantName.charAt(i))) {
                digit = merchantName.substring(i + 1);
                merchantName = merchantName.substring(0, i + 1);
                break;
            }
        }
        if (digit.isEmpty()) {
            return merchantName + "1";
        } else {
            return merchantName + (Integer.valueOf(digit) + 1);
        }
    }

    public boolean isInTmpMch(String merchantSn) {
        MapSqlParameterSource parameters = new MapSqlParameterSource()
                .addValue("merchantSn", merchantSn);
        List tmpMch = namedParameterJdbcTemplate.queryForList("select * from tmp_mch where merchant_sn = :merchantSn", parameters);
        return WosaiCollectionUtils.isNotEmpty(tmpMch);
    }

}
