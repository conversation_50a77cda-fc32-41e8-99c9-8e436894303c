package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.externalservice.customer.CustomerRelationClient;
import com.wosai.upay.job.externalservice.paybusiness.PayBusinessOpenClient;
import com.wosai.upay.job.externalservice.paybusiness.model.PayCombosQueryReq;
import com.wosai.upay.job.mapper.AuthAndComboTaskMapper;
import com.wosai.upay.job.mapper.ChannelActivityMapper;
import com.wosai.upay.job.model.AuthAndComboTask;
import com.wosai.upay.job.model.ChannelActivity;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.util.ThreadLocalUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.model.OpenPayComboReq;
import com.wosai.upay.service.CrmEdgeService;
import entity.common.OrganizationEs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import vo.ApiRequestParam;
import vo.UserVo;

import java.util.*;

/**
 * https://confluence.wosai-inc.com/pages/viewpage.action?pageId=496664645
 * https://jira.wosai-inc.com/browse/CUA-3992
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
@Component
@Slf4j
public class WxSettlementIdChangeBiz {

    @Autowired
    private FeeRateService feeRateService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private CrmEdgeService crmEdgeServcie;

    @Autowired
    private ChannelActivityMapper channelActivityMapper;

    @Autowired
    private CustomerRelationClient customerRelationClient;

    @Autowired
    private PayBusinessOpenClient payBusinessOpenClient;

    /**
     * 间连扫码拓展code
     */
    @Value("${indirect_crm_customer_relation}")
    public String indirectCrmCustomerRelation;

    @Value("${indirect-pay.dev_code}")
    private String indirectPayDevCode;

    @Value("${umb_dev_code}")
    private String umbDevCode;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private AopBiz aopBiz;
    @Autowired
    private AuthAndComboTaskMapper authAndComboTaskMapper;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;
    @Autowired
    @Qualifier("settlementHandleThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor settlementHandleThreadPoolTaskExecutor;


    // 10 是用于风控恢复切换参数所用
    private static final List<Integer> channelActivityStatus = Lists.newArrayList(3, 4, 10);
    //不参与结算Id校验的微信活动
    private static final List<Integer> notCheckSettleIdWechatActivities = Lists.newArrayList(2, 3, 4, 5);

    public boolean checkSettlementId(MerchantProviderParams merchantProviderParams) {
        // 判定是否是高校活动
        ChannelActivity channelActivity = channelActivityMapper.getLatestActivityByMchId(merchantProviderParams.getPay_merchant_id(), 0L);
        if (channelActivity != null && notCheckSettleIdWechatActivities.contains(channelActivity.getType()) && channelActivityStatus.contains(channelActivity.getStatus())) {
            return true;
        }
        if (!PaywayEnum.WEIXIN.getValue().equals(merchantProviderParams.getPayway())) {
            return true;
        }
        if (WosaiStringUtils.isEmpty(merchantProviderParams.getWx_settlement_id())) {
            return true;
        }
        //判断是否是行业变更
        AuthAndComboTask task = authAndComboTaskMapper.getLatestTask(merchantProviderParams.getMerchant_sn());
        if (task != null && WosaiStringUtils.equals(task.getSub_mch_id(), merchantProviderParams.getPay_merchant_id())) {
            return true;
        }
        // 特殊场景不需要校验行业和结算ID是否匹配
        if (!ThreadLocalUtil.getCheckSettlement()) {
            return true;
        }
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantProviderParams.getMerchant_sn(), null);
        Collection<String> settlementIds = getSettlementIds(merchant.getIndustry());
        return WosaiCollectionUtils.isEmpty(settlementIds) || settlementIds.contains(merchantProviderParams.getWx_settlement_id());
    }

    public Collection<String> getSettlementIds(String industryId) {
        Map industryConfigMap = JSONObject.parseObject(JSONObject.toJSONString(industryMappingCommonBiz.getSettlementConfig(industryId)), Map.class);
        if (WosaiMapUtils.isEmpty(industryConfigMap)) {
            return null;
        }
        return industryConfigMap.values();
    }

    public void handleSettlementId(MerchantProviderParams merchantProviderParams) {
        if (!applicationApolloConfig.getHandleSettlementIdAfterChangeWxParam()) {
            return;
        }
        // 独立行业变更不走兜底逻辑
        if (!ThreadLocalUtil.getCheckSettlement()) {
            return;
        }
        settlementHandleThreadPoolTaskExecutor.submit(() -> {
                    try {
                        String result = doHandleSettlementId(merchantProviderParams);
                        log.info("处理结算id 成功 {} {} {}", result, merchantProviderParams.getMerchant_sn(), merchantProviderParams.getId());
                    } catch (Exception e) {
                        log.error("处理结算id 异常 {} {}", merchantProviderParams.getMerchant_sn(), merchantProviderParams.getId(), e);
                    }
                }
        );
    }

    protected String doHandleSettlementId(MerchantProviderParams merchantProviderParams) throws Exception {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantProviderParams.getMerchant_sn(), null);

        List<Map> industryConfigs = applicationApolloConfig.getWxChannelActivityIndustry();
        for (Map config : industryConfigs) {
            if (BeanUtil.getPropString(config, "industry").contains(merchant.getIndustry())) {
                return doHandle(merchantProviderParams, merchant, config);
            }
        }
        return "非特殊行业，不处理";
    }

    protected String doHandle(MerchantProviderParams merchantProviderParams, MerchantInfo merchant, Map config) throws Exception {
        // 判断是否报名微信活动
        int activityType = BeanUtil.getPropInt(config, "activity_type", -1);
        if (activityType > 0) {
            ChannelActivity successEduActivity = channelActivityMapper.getSuccessActivityByMchId(merchantProviderParams.getPay_merchant_id(), activityType);
            if (successEduActivity != null) {
                return "报名成功微信活动，不处理";
            }
        }

        int isvDefaultComboId = BeanUtil.getPropInt(config, "isv_default_combo_id");
        int defaultComboId = BeanUtil.getPropInt(config, "default_combo_id");
        String isvDefaultFee = BeanUtil.getPropString(config, "isv_default_fee");
        String defaultFee = BeanUtil.getPropString(config, "default_fee");
        String industryType = BeanUtil.getPropString(config, "industry_type");
        String noticeRemark = BeanUtil.getPropString(config, "notice_remark");


        OrganizationEs mchIndirectOrg = customerRelationClient.getMchIndirectOrg(merchant.getId());
        //查询中投套餐时,需要传递devCode
        String devCode = null;
        if (ProviderEnum.PROVIDER_UMB.getValue().equals(merchantProviderParams.getProvider())) {
            devCode = umbDevCode;
        }
        List<Map> records = getPayCombos(merchant, mchIndirectOrg, devCode);
        boolean isISV = isISV(mchIndirectOrg);
        long comboId = BeanUtil.getPropLong(records.get(0), "trade_combo_id", isISV ? isvDefaultComboId : defaultComboId);
        // 获取微信套餐列表
        List<ListMchFeeRateResult> listMchFeeRateResults = feeRateService.listMchFeeRates(merchant.getSn());
        if (WosaiCollectionUtils.isNotEmpty(listMchFeeRateResults) &&
                listMchFeeRateResults.stream().anyMatch(feeRate -> Objects.equals(comboId, feeRate.getTradeComboId()))) {
            return "套餐id一致，不处理";
        }

        String fee = isISV ? isvDefaultFee : defaultFee;
        // 设置新套餐
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(merchant.getSn())
                .setAuditSn("行业变更重新设置套餐")
                .setTradeComboId(comboId)
                .setApplyPartialPayway(true)
                .setApplyFeeRateMap(CollectionUtil.hashMap(
                        String.valueOf(PaywayEnum.WEIXIN.getValue()), fee
                ));
        feeRateService.applyFeeRateOne(applyFeeRateRequest);
        // 发通知
        aopBiz.sendNoticeToCrm(merchant.getId(), "NJLH1DXUP3AV", "KIKNVRB6IPBQ", CollectionUtil.hashMap(
                "indursty_name", industryType,
                "fee", fee,
                "merchant_sn", merchant.getSn(),
                "remark", noticeRemark
        ));
        aopBiz.sendNoticeToAdmin(merchant.getId(), indirectPayDevCode, "KEADQNEXFQ7O", CollectionUtil.hashMap(
                "indursty_name", industryType,
                "remark", noticeRemark
        ));
        return "设置新套餐 " + comboId;
    }

    protected List<Map> getPayCombos(MerchantInfo merchant, OrganizationEs mchIndirectOrg, String devCode) {
        // 获取商户当前应该配置的套餐
        List<Map> payCombos = payBusinessOpenClient.queryPayCombos(new PayCombosQueryReq()
                .setIndustryId(merchant.getIndustry())
                .setOrganizationId(mchIndirectOrg.getId()), devCode);
        if (WosaiCollectionUtils.isEmpty(payCombos)) {
            throw new ContractBizException("商户应该配置套餐为空");
        }
        List<Map> records = (List<Map>) payCombos.get(0).get("records");
        return records;
    }


    protected boolean isISV(OrganizationEs mchIndirectOrg) {
        return applicationApolloConfig.getIsvOrgPath().stream()
                .anyMatch(path -> mchIndirectOrg.getPath().startsWith((String) path));
    }

    /**
     * TODO 产品还没有定好规则,需要详细讨论一下
     * https://jira.wosai-inc.com/browse/CUA-6544?client=8729a647-d22f-4993-962e-cf2e327a83be&home=
     * 报备新的微信子商户号，并按组织业务开通规则取配微信费率套餐，在最终通道切换生效微信费率套餐；
     * @param change
     * @return
     */
    public void setCombAfterReContractWx(McAcquirerChange change) {
        try {
            String merchantSn = change.getMerchant_sn();
            //如果当前通道在银行则不设置
            if (isBank(change.getSource_acquirer())) {
                return;
            }
            MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
            OrganizationEs mchIndirectOrg = customerRelationClient.getMchIndirectOrg(merchant.getId());
            //根据商户所属组织获取套餐套餐
            List<Map> records = getPayCombos(merchant, mchIndirectOrg, null);
            long comboId = BeanUtil.getPropLong(records.get(0), "trade_combo_id");
            // 获取当前生效微信套餐列表
            List<ListMchFeeRateResult> listMchFeeRateResults = feeRateService.listMchFeeRates(merchant.getSn());
            if (CollectionUtils.isNotEmpty(listMchFeeRateResults) &&
                    listMchFeeRateResults.stream().anyMatch(feeRate -> Objects.equals(comboId, feeRate.getTradeComboId())
                            && PaywayEnum.WEIXIN.getValue().equals(feeRate.getPayWay()))) {
                log.info("商户号:{},套餐id一致，不处理", merchantSn);
                return;
            }
            Map wxCombo = records.stream()
                    .filter(info -> Objects.equals(BeanUtil.getPropInt(info, "payway"), PaywayEnum.WEIXIN.getValue()))
                    .findFirst().orElse(null);
            //TODO 费率取值未定
            String wxFee = (String) wxCombo.computeIfAbsent("fee_rate_default_value",
                    t -> BeanUtil.getPropString(wxCombo, "fee_rate_min", "0.38"));
            // 设置新套餐
            ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                    .setMerchantSn(merchant.getSn())
                    .setAuditSn("结算ID不匹配重新设置套餐")
                    .setTradeComboId(comboId)
                    .setApplyPartialPayway(true)
                    .setApplyFeeRateMap(CollectionUtil.hashMap(
                            String.valueOf(PaywayEnum.WEIXIN.getValue()), wxFee
                    ));
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
        } catch (Exception e) {
            log.error("setCombAfterReContractWx error {}", e);
        }
    }

    /**
     * 根据商户所属组织+行业获取套餐套餐
     * @param merchantSn
     * @return
     */
    public List<Map> getAllPayCombosByMerchantSn(String merchantSn) {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        List<Map> payCombos = Lists.newArrayList();
        try {
            //商户所在间连组织
            OrganizationEs mchIndirectOrg = customerRelationClient.getMchIndirectOrg(merchant.getId());
            //根据商户所属组织+行业获取套餐套餐
            ApiRequestParam<OpenPayComboReq> comboReq = new ApiRequestParam<>();
            UserVo userVo = new UserVo();
            userVo.setOrganizationId(mchIndirectOrg.getId());
            comboReq.setUser(userVo);
            OpenPayComboReq openPayComboReq = new OpenPayComboReq()
                    .setPlatform("crm_app")
                    .setIndustryId(merchant.getIndustry());
            comboReq.setBodyParams(openPayComboReq);

            // 获取商户当前应该配置的套餐
            payCombos = crmEdgeServcie.getPayCombos(comboReq);
            if (WosaiCollectionUtils.isEmpty(payCombos)) {
                throw new ContractBizException("商户应该配置套餐为空");
            }
        } catch (Exception exception) {
            log.error("getPayCombos,merchantSn:{},error{}", merchantSn, exception);
        }
        return payCombos;
    }

    /**
     * 根据收单机构获取是否银行通道的标识
     *
     * @param acquirer
     * @return 0:非银行通道 1:银行通道
     */
    public Boolean isBank(String acquirer) {
        List indirectAcquireList = mcAcquirerDAO.getIndirectAcquirerList();
        return indirectAcquireList.contains(acquirer) ? Boolean.FALSE : Boolean.TRUE;
    }

}
