package com.wosai.upay.job.biz.paramContext;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreExtService;
import com.wosai.upay.bank.info.api.model.BankInfo;
import com.wosai.upay.bank.info.api.service.BankInfoService;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.Constants.ConstantsEvent;
import com.wosai.upay.job.Constants.MerchantChangeDataConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.direct.AliDirectReq;
import com.wosai.upay.job.model.direct.WeixinDirectReq;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.service.BankCardServiceImpl;
import com.wosai.upay.merchant.audit.api.service.MerchantAuditService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.JdBiz.SUPPORT_PROVIDER_LIST;

/**
 * <AUTHOR>
 * Created by hzq on 19/4/2.
 */
@Service
@Slf4j
public class ParamContextBiz {
    private static final int DEFAULT_TRUE = 1;

    @Autowired
    MerchantService merchantService;

    @Autowired
    BankInfoService bankInfoService;

    @Autowired
    TradeConfigService tradeConfigService;

    @Autowired
    MerchantBankService merchantBankService;
    @Autowired
    BankCardServiceImpl bankCardService;

    @Autowired
    MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private MerchantAuditService auditService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private com.wosai.mc.service.StoreService mcStoreService;

    @Autowired
    private StoreExtService mcStoreExtService;

    @Autowired
    private MerchantBizBankAccountService merchantBizBankAccountService;

    @Autowired
    ComposeAcquirerBiz acquirerBiz;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    MerchantProviderParamsBiz merchantProviderParamsBiz;

    @Autowired
    private ProviderFactory providerFactory;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    public static final String KEY_BUSINESS_LICENCE = "merchantBusinessLicense";
    public static final String KEY_MERCHANT = "merchant";
    public static final String KEY_BANK_ACCOUNT = "bankAccount";
    public static final String KEY_BANK_INFO = "bankInfo";
    public static final String KEY_MERCHANT_AUDIT = "merchantAudit";
    public static final String KEY_WEIXIN_CONTRACT_INFO = "contractInfo";
    public static final String KEY_APP_INFO = "appInfo";
    public static final String KEY_STORE = "store";
    public static final String KEY_PICTURES = "pictures";
    public static final String KEY_BUSINESS_CODE = "businessCode";

    public static final String MERCHANT_FEE_RATES = "merchantFeeRates";
    public static final String SQB_FEE_RATES = "sqbFeeRates";
    public static final String KEY_WEIXIN_SUBDEV_CONFIG_STATUS = "weixinSubdevConfigStatus";
    public static final String PAY_MERCHANT_ID = "pay_merchant_id";
    public static final String BRAND_PAY_MERCHANT_ID = "brand_pay_merchant_id";
    public static final String DEDICATED_PARAMS = "dedicated_params";


    /**
     * -------------------------------------------------------------
     * <p>
     * /**
     * 需要授权的渠道商号
     **/
    public static final String NEDD_AUTH_CHANNEL = "needAuthChannel";

    /**
     * 强制按照小微提交微信
     **/
    public static final String FORCE_MICRO = "forceMicro";
    /**
     * 强制按照走授权逻辑（没有实名）
     **/
    public static final String FORCE_AUTH = "forceAuth";


    public static final String WECHAT_AUTH_ENUM = "wechatAuthAcquirer";


    private static final int[] FEE_PAY_WAY = {
            PaywayEnum.ALIPAY.getValue(),
            PaywayEnum.WEIXIN.getValue(),
            PaywayEnum.UNIONPAY.getValue(),
            PaywayEnum.BESTPAY.getValue(),
            PaywayEnum.JD_WALLET.getValue()
    };

    /**
     * 获取清算行失败，不会抛出异常
     * 开户行信息可能是null
     */
    public Map<String, Object> getParamContextByMerchantSn(String merchantSn) {
        Map<String, Object> contextParam = Maps.newHashMap();
        MerchantInfo merchant = getMerchant(merchantSn);
        String merchantId = merchant.getId();
        contextParam.put(KEY_MERCHANT, JSON.parseObject(JSON.toJSONString(merchant), Map.class));
        Map bankAccount = null;
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap(MerchantBankAccountPre.MERCHANT_ID, merchantId, MerchantBankAccountPre.DEFAULT_STATUS, MerchantBankAccountPre.DEFAULT_STATUS_TRUE));
        if (listResult != null && listResult.getTotal() > 0) {
            bankAccount = listResult.getRecords().get(0);
        }
        if (CollectionUtils.isEmpty(bankAccount)) {
            throw new ContextParamException("获取商户卡信息为空merchantSn:" + merchantSn + "merchantId:" + merchantId);
        }
        contextParam.put(KEY_BANK_ACCOUNT, bankAccount);
        Map<String, Object> bankInfo = getBankInfo(bankAccount);
        if (CollectionUtils.isEmpty(bankInfo)) {
            log.warn("根据商户当前账户信息，查询银行分支行信息失败, merchantSn:{}", merchantSn);
        }
        contextParam.put(KEY_BANK_INFO, bankInfo);
        fillBusinessLicense(merchantId, contextParam);
        return contextParam;
    }

    public Map<String, Object> getNetInParamContextByMerchantSn(String merchantSn) {
        Map<String, Object> contextParam = Maps.newHashMap();
        MerchantInfo merchant = getMerchant(merchantSn);
        String merchantId = merchant.getId();
        //商户信息
        contextParam.put(KEY_MERCHANT, JSON.parseObject(JSON.toJSONString(merchant), Map.class));

        Map bankAccount = null;
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap(MerchantBankAccountPre.MERCHANT_ID, merchantId, MerchantBankAccountPre.DEFAULT_STATUS, MerchantBankAccountPre.DEFAULT_STATUS_TRUE));
        if (listResult != null && listResult.getTotal() > 0) {
            bankAccount = listResult.getRecords().get(0);
        }
        if (CollectionUtils.isEmpty(bankAccount)) {
            throw new ContextParamException("获取商户卡信息为空merchantSn:" + merchantSn + "merchantId:" + merchantId);
        }
        contextParam.put(KEY_BANK_ACCOUNT, bankAccount);
        //银行卡字典
        Map<String, Object> bankInfo = getBankInfo(bankAccount);
        if (CollectionUtils.isEmpty(bankInfo)) {
            throw new ContextParamException("查询银行分支行信息失败，请在线咨询客服转产品处理");
        }
        contextParam.put(KEY_BANK_INFO, bankInfo);

        fillBusinessLicense(merchantId, contextParam);
        return contextParam;
    }

    public MerchantInfo getMerchant(String merchantSn) {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, devCode);
        if (merchant == null) {
            throw new ContextParamException("商户" + merchantSn + "不存在");
        }
        return merchant;
    }

    public Map getBankInfo(Map bankAccount) {
        if (CollectionUtils.isEmpty(bankAccount)) {
            return null;
        }

        // 先用开户行号精确查询
        String openingNumber = MapUtils.getString(bankAccount, MerchantBankAccount.OPENING_NUMBER);
        if (WosaiStringUtils.isNotEmpty(openingNumber)) {
            Map bankInfo = bankInfoService.getBankInfo(CollectionUtil.hashMap(
                    BankInfo.OPENING_NUMBER, openingNumber
            ));
            if (WosaiMapUtils.isNotEmpty(bankInfo)) {
                return bankInfo;
            }
        }

        String branchName = MapUtils.getString(bankAccount, BankInfo.BRANCH_NAME);
        String bankName = MapUtils.getString(bankAccount, BankInfo.BANK_NAME);
        // 精确匹配
        Map bankInfo = bankInfoService.getBankInfo(CollectionUtil.hashMap(
                BankInfo.BANK_NAME_IS, bankName,
                BankInfo.BRANCH_NAME_IS, branchName
        ));

        // 查询不到再模糊匹配一次
        if (WosaiMapUtils.isEmpty(bankInfo)) {
            bankInfo = bankInfoService.getBankInfo(CollectionUtil.hashMap(
                    BankInfo.BANK_NAME, bankName,
                    BankInfo.BRANCH_NAME, branchName
            ));
        }
        return bankInfo;
    }

    private List<String> SqbFeeRateKeys = Arrays.asList(
            MerchantConfig.PAYWAY,
            MerchantConfig.LADDER_STATUS,
            MerchantConfig.FEE_RATE_TYPE,
            MerchantConfig.B2C_FEE_RATE,
            MerchantConfig.C2B_FEE_RATE,
            MerchantConfig.WAP_FEE_RATE,
            TransactionParam.LADDER_FEE_RATES,
            TransactionParam.PARAMS_BANKCARD_FEE,
            TransactionParam.CHANNEL_LADDER_FEE_RATES
    );

    public List<Map> getSqbFeeRates(String merchantId) {
        List<Map<String, Object>> merchantConfigs = tradeConfigService.getAnalyzedMerchantConfigsByPayWayList(merchantId, FEE_PAY_WAY);
        final Map jdConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.JD_WALLET.getValue());
        final Integer merchantConfigProvider = BeanUtil.getPropInt(jdConfig, MerchantConfig.PROVIDER);
        final String miniFeeRate = BeanUtil.getPropString(jdConfig, MerchantConfig.MINI_FEE_RATE);
        return merchantConfigs.stream()
                .filter(config -> {
                    if (Objects.equals(BeanUtil.getPropInt(config, MerchantConfig.PAYWAY), PaywayEnum.JD_WALLET.getValue())) {
                        if (!SUPPORT_PROVIDER_LIST.contains(merchantConfigProvider)) {
                            return Boolean.FALSE;
                        }
                        if (StrUtil.isBlank(miniFeeRate)) {
                            return Boolean.FALSE;
                        }
                    }
                    return Boolean.TRUE;
                })
                .map(map -> Maps.filterKeys(map, SqbFeeRateKeys::contains))
                .collect(Collectors.toList());
    }

    private List<Map<String, Object>> getAppSqbFeeRates(String merchantId, Integer provider) {
        List<Map<String, Object>> merchantBasicConfigs = tradeConfigService.getAnalyzedMerchantConfigsByPayWayList(merchantId, FEE_PAY_WAY);
        List<Map<String, Object>> merchantConfigs = tradeConfigService.getMerchantAppConfigByMerchantIdAndProvider(merchantId, provider);
        log.info("查询多业务费率,merchantId:{},provider:{},result:{}", merchantId, provider, JSONObject.toJSONString(merchantConfigs));
        List<Map<String, Object>> result = new ArrayList<>();
        if (merchantConfigs == null) {
            return merchantBasicConfigs;
        }
        merchantConfigs = merchantConfigs.parallelStream()
                .filter(map -> BeanUtil.getPropInt(map, "provider") == provider).collect(Collectors.toList());
        for (Map<String, Object> feeRate : merchantBasicConfigs) {
            int payway = BeanUtil.getPropInt(feeRate, "payway");
            List<Map<String, Object>> configs = merchantConfigs.parallelStream()
                    .filter(param -> BeanUtil.getPropInt(param, "payway") == payway).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(configs)) {
                result.add(feeRate);
            } else {
                Map<String, Object> config = configs.get(0);
                Map<String, Object> params = WosaiMapUtils.getMap(config, "params");
                for (String key : params.keySet()) {
                    config.put(key, params.get(key));
                }
                if (StringUtil.empty(BeanUtil.getPropString(config, "b2c_fee_rate"))) {
                    if (!StringUtil.empty(BeanUtil.getPropString(config, MerchantConfig.FEE_RATE_TYPE))
                            && MerchantConfig.FEE_RATE_TYPE_LADDER.equals(BeanUtil.getPropString(config, MerchantConfig.FEE_RATE_TYPE))) {
                        result.add(config);
                    } else {
                        result.add(feeRate);
                    }
                } else {
                    result.add(config);
                }
            }
        }
        return result.stream()
                .map(map -> Maps.filterKeys(map, SqbFeeRateKeys::contains))
                .collect(Collectors.toList());
    }


    public void fillFeeRatesByRuleGroupId(Map<String, Object> paramContext, String ruleGroupId) {
        String merchantId = MapUtils.getString(MapUtils.getMap(paramContext, KEY_MERCHANT), DaoConstants.ID);
        BasicProvider provider = providerFactory.getProviderByRuleGroupIdAcquirer(ruleGroupId);
        paramContext.put(MERCHANT_FEE_RATES, provider.getFeeRate(merchantId));
        paramContext.put(SQB_FEE_RATES, getSqbFeeRates(merchantId));
    }


    /**
     * 根据商户号和event构建商户上下文信息
     * 某些event会有商户的最新的变更信息，这部分信息需要覆盖通过接口获取的商户信息(基本信息 营业执照 卡等）
     * event中的表名和context中的表字段映射需要配置(阿波罗) 例如 merchant_business_license_info -> merchantBusinessLicense
     * 内容字段由于需要兼容以前的代码，统一下划线（数据库的字段名）
     *
     * @param merchantSn    商户号
     * @param contractEvent event事件
     * @return 商户上下文信息
     */
    public Map<String, Object> getParamContextBySnAndEvent(String merchantSn, ContractEvent contractEvent) throws ContextParamException {
        Map<String, Object> context = getParamContextByMerchantSn(merchantSn, contractEvent);
        try {
            String eventMsg = contractEvent.getEvent_msg();
            JSONObject eventMsgMap = JSON.parseObject(eventMsg);
            if (MapUtils.isEmpty(eventMsgMap)) {
                return context;
            }
            String lastedDataString = eventMsgMap.getString(ConstantsEvent.EVENT_TYPE_SOURCE);
            String tableName = eventMsgMap.getString(ConstantsEvent.EVENT_TYPE_TABLE_NAME);
            String columnFiledString = eventMsgMap.getString(ConstantsEvent.EVENT_TYPE_MSG);
            if (org.apache.commons.lang3.StringUtils.isAnyBlank(lastedDataString, tableName, columnFiledString)) {
                return context;
            }
            List<String> columnList = JSON.parseArray(columnFiledString, String.class);
            JSONObject lastedDataMap = JSON.parseObject(lastedDataString);
            updateMerchantContextByLastedData(tableName, new HashSet<>(columnList), lastedDataMap, context);
        } catch (Exception e) {
            log.error("根据event更新商户上下文信息失败", e);
        }
        return context;
    }

    private void updateMerchantContextByLastedData(String tableName, Set<String> columnSet,
                                                   Map<String, Object> lastedDataMap,
                                                   Map<String, Object> merchantContext) {
        Map<String, String> tableNameToContextKeyMap = applicationApolloConfig.getMerchantTableNameToContextKeyMap();
        String contextKey = tableNameToContextKeyMap.get(tableName);
        if (org.apache.commons.lang3.StringUtils.isBlank(contextKey)) {
            return;
        }
        lastedDataMap.entrySet().removeIf(entry -> Objects.isNull(entry.getValue())
                || (entry.getValue() instanceof String && org.apache.commons.lang3.StringUtils.isBlank((String) entry.getValue())));
        Map<String, Object> needToUpdateColumnToValueMap = Maps.filterKeys(lastedDataMap, columnSet::contains);
        if (MapUtils.isEmpty(needToUpdateColumnToValueMap)) {
            return;
        }
        Map<String, Object> merchantContextKeyMap = MapUtils.getMap(merchantContext, contextKey);
        if (MapUtils.isNotEmpty(merchantContextKeyMap)) {
            merchantContextKeyMap.putAll(needToUpdateColumnToValueMap);
        }
    }

    private static final List<Integer> needBankInfoEventTypeList = Arrays.asList(
            ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS,
            ContractEvent.OPT_TYPE_NET_IN,
            ContractEvent.OPT_TYPE_NET_CRM_UPDATE
    );
    public Map<String, Object> getParamContextByMerchantSn(String merchantSn, ContractEvent contractEvent) throws ContextParamException {
        boolean needBankInfo = Objects.nonNull(contractEvent) && needBankInfoEventTypeList.contains(contractEvent.getEvent_type());
        return getParamContextByMerchantSn(merchantSn, contractEvent, needBankInfo);
    }


    public Map<String, Object> getParamContextByMerchantSn(String merchantSn, ContractEvent contractEvent, boolean needBankInfo) throws ContextParamException {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, devCode);
        if (merchant == null) {
            throw new ContextParamException("商户" + merchantSn + "不存在");
        }
        String merchantId = merchant.getId();
        Map<String, Object> paramContext = Maps.newHashMap();
        //商户信息
        paramContext.put(KEY_MERCHANT, JSON.parseObject(JSON.toJSONString(merchant), Map.class));
        boolean cardChange = false;
        if (contractEvent != null && ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == contractEvent.getEvent_type()) {
            Map eventMsg = JSON.parseObject(contractEvent.getEvent_msg(), Map.class);
            if (CollectionUtils.isEmpty(eventMsg)) {
                throw new ContextParamException("结算账户变更中ContractEvent中无eventMsg信息");
            }
            Map requestParam = (Map) eventMsg.get(ConstantsEvent.EVENT_TYPE_SOURCE);
            if (!CollectionUtils.isEmpty(requestParam)) { //非dts触发的变更
                cardChange = true;
                paramContext.put("cardRequestParam", requestParam);
            }

        }
        if (contractEvent != null && ContractEvent.OPT_TYPE_UPDATE_MERCHANT_DATA == contractEvent.getEvent_type()) {
            Map eventMsg = JSON.parseObject(contractEvent.getEvent_msg(), Map.class);

            if (MapUtils.getBooleanValue(eventMsg, MerchantChangeDataConstant.RE_CONTRACT, false)) {
                //加入这个标识后,后续在contract服务处理过程中,才会允许多次报备新的微信子商户号
                paramContext.put("type", "1");
                paramContext.put(MerchantChangeDataConstant.RE_CONTRACT, true);
            }
            if (MapUtils.isNotEmpty(eventMsg)) {
                Map requestParam = (Map) eventMsg.get(ConstantsEvent.EVENT_TYPE_SOURCE);
                //把来源保存下,后续发送消息时带上
                paramContext.put(MerchantChangeDataConstant.APPLY_SOURCE, MapUtils.getString(eventMsg, MerchantChangeDataConstant.APPLY_SOURCE));
                if (!CollectionUtils.isEmpty(requestParam)) { //目前该类型应该还没有通过source字段来传入要换卡的数据的
                    cardChange = true;
                    paramContext.put("cardRequestParam", requestParam);
                } else {
                    String bankPreId = MapUtils.getString(eventMsg, MerchantChangeDataConstant.BANK_PRE_ID);
                    if (WosaiStringUtils.isNotEmpty(bankPreId)) {
                        Map bankAccountPre = merchantBankService.getMerchantBankAccountPre(bankPreId);
                        //不是默认卡,则触发换卡操作
                        if (MapUtils.getInteger(bankAccountPre, MerchantBankAccountPre.DEFAULT_STATUS, MerchantBankAccountPre.DEFAULT_STATUS_FALSE) != MerchantBankAccountPre.DEFAULT_STATUS_TRUE) {
                            cardChange = true;
                            paramContext.put("cardRequestParam", bankAccountPre);
                        }
                    }
                }
            }

        }

        Map bankAccount = null;
        if (cardChange) {
            bankAccount = (Map) paramContext.get("cardRequestParam");
            paramContext.put(KEY_BANK_ACCOUNT, bankAccount);
        } else {
            ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                    CollectionUtil.hashMap(MerchantBankAccountPre.MERCHANT_ID, merchantId, MerchantBankAccountPre.DEFAULT_STATUS, DEFAULT_TRUE));
            if (listResult != null && listResult.getTotal() > 0) {
                bankAccount = listResult.getRecords().get(0);
            }
            if (CollectionUtils.isEmpty(bankAccount)) {
                throw new ContextParamException("获取商户卡信息为空merchantSn:" + merchantSn + "merchantId:" + merchantId);
            }
            paramContext.put(KEY_BANK_ACCOUNT, bankAccount);
        }

        //银行卡字典
        Map<String, Object> bankInfo = getBankInfo(bankAccount);
        if (CollectionUtils.isEmpty(bankInfo)) {
            if (cardChange) {
                bankCardService.sendMesaageToBank((Map) paramContext.get("cardRequestParam"), MerchantBankAccount.VERIFY_STATUS_FAIL, merchantSn, "查找不到开户行号与清算行号，请检查商户银行账户银行名称与支行名称填写是否规范");
            }
            if (needBankInfo) {
                throw new ContextParamException("查询银行分支行信息失败，请在线咨询客服转产品处理");
            }
        }
        paramContext.put(KEY_BANK_INFO, bankInfo);

        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);

        contractEvent = Optional.ofNullable(contractEvent).orElseGet(ContractEvent::new);
        String ruleGroupId = contractEvent.getRule_group_id();

        BasicProvider provider = WosaiStringUtils.isEmpty(ruleGroupId) ?
                providerFactory.getProviderByName(acquirer) : providerFactory.getProviderByRuleGroupId(ruleGroupId);
        //通用参数导入的,无相关provider,执行逻辑会异常.直接不放费率参数
        if (provider != null) {
            //参数
            paramContext.put(MERCHANT_FEE_RATES, provider.getFeeRate(merchantId));
        }
        if (contractEvent.getEvent_type() != null && contractEvent.getEvent_msg() != null) {
            if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == contractEvent.getEvent_type() && contractEvent.getEvent_msg().contains("app_id")) {
                Map msg = (Map) JSONObject.parseArray(BeanUtil.getPropString(JSONObject.parse(contractEvent.getEvent_msg()), "msg")).get(0);
                paramContext.put(SQB_FEE_RATES, getAppSqbFeeRates(merchantId, BeanUtil.getPropInt(msg, "provider")));
            } else {
                paramContext.put(SQB_FEE_RATES, getSqbFeeRates(merchantId));
            }
        } else {
            paramContext.put(SQB_FEE_RATES, getSqbFeeRates(merchantId));
        }
        fillBusinessLicense(merchantId, paramContext);
        return paramContext;
    }


    /**
     * 获取商户上下文
     *
     * @param merchantSn
     * @param contractEvent
     * @param preId
     * @return
     * @throws ContextParamException
     */
    public Map<String, Object> getParamContextByMerchantSn(String merchantSn, ContractEvent contractEvent, String preId) throws ContextParamException {
        final Map<String, Object> paramContext = getParamContextByMerchantSn(merchantSn, contractEvent);
        Map bankAccount = merchantBankService.getMerchantBankAccountPre(preId);
        if (CollectionUtils.isEmpty(bankAccount)) {
            throw new ContextParamException("获取商户卡信息为空merchantSn:" + merchantSn + "preId:" + preId);
        }
        paramContext.put(KEY_BANK_ACCOUNT, bankAccount);
        return paramContext;
    }

    /**
     * 补充营业执照
     *
     * @param merchantId
     * @param contextParam
     */
    private void fillBusinessLicense(String merchantId, Map<String, Object> contextParam) {
        MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, devCode);
        contextParam.put(KEY_BUSINESS_LICENCE, JSON.parseObject(JSON.toJSONString(license), Map.class));
    }


    /**
     * 构建微信直连的上下文参数
     *
     * @param weixinDirectReq 请求参数
     * @param needPhotos      是否需要门店照片 线上不需要
     * @return
     */
    public Map<String, Object> buildWeixinParamContext(WeixinDirectReq weixinDirectReq, boolean needPhotos) throws ContextParamException {
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(weixinDirectReq.getMerchant_sn(), weixinDirectReq.getDev_code());
        if (merchantInfo == null) {
            throw new ContextParamException("商户" + weixinDirectReq.getMerchant_sn() + "不存在");
        }
        MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantInfo.getId(), weixinDirectReq.getDev_code());
        if (licenseInfo == null) {
            throw new ContextParamException("商户" + weixinDirectReq.getMerchant_sn() + "营业执照不存在");
        }
        ListResult bizBankAccount = merchantBizBankAccountService.findBizBankAccount(CollectionUtil.hashMap(MerchantBizBankAccount.MERCHANT_ID, merchantInfo.getId(), MerchantBizBankAccount.BIZ, weixinDirectReq.getDev_code()));
        if (bizBankAccount == null || WosaiCollectionUtils.isEmpty(bizBankAccount.getRecords())) {
            throw new ContextParamException("商户" + weixinDirectReq.getMerchant_sn() + "银行卡不存在");
        }
        Map<String, Object> context = new HashMap<String, Object>(7);
        if (needPhotos) {
            List<Map> records = storeService.getStoreListByMerchantId(merchantInfo.getId(), new PageInfo(1, 1, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC))), null).getRecords();
            if (CollectionUtils.isEmpty(records) || MapUtils.isEmpty(records.get(0))) {
                throw new ContextParamException("商户" + weixinDirectReq.getMerchant_sn() + "门店不存在");
            }
            StoreInfo storeInfo = mcStoreService.getStoreById(BeanUtil.getPropString(records.get(0), DaoConstants.ID), weixinDirectReq.getDev_code());
            StotreExtInfoAndPictures pictures = mcStoreExtService.findStoreExtAndPicturesByStoreId(storeInfo.getId(), weixinDirectReq.getDev_code());
            context.put(KEY_STORE, storeInfo);
            context.put(KEY_PICTURES, pictures);
        }
        context.put(KEY_MERCHANT, merchantInfo);
        context.put(KEY_BUSINESS_LICENCE, licenseInfo);
        context.put(KEY_WEIXIN_CONTRACT_INFO, weixinDirectReq.getContact_info());
        context.put(KEY_APP_INFO, weixinDirectReq.getApp_info());
        context.put(KEY_BANK_ACCOUNT, bizBankAccount.getRecords().get(0));
        return context;
    }

    /**
     * 构建支付宝直连的上下文参数
     *
     * @param aliDirectReq 请求参数
     * @return context
     */
    public Map<String, Object> buildAliParamContext(AliDirectReq aliDirectReq) throws ContextParamException {
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(aliDirectReq.getMerchant_sn(), aliDirectReq.getDev_code());
        if (merchantInfo == null) {
            throw new ContextParamException("商户" + aliDirectReq.getMerchant_sn() + "不存在");
        }
        MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantInfo.getId(), aliDirectReq.getDev_code());
        if (licenseInfo == null) {
            throw new ContextParamException("商户" + aliDirectReq.getMerchant_sn() + "营业执照不存在");
        }
        Map<String, Object> context = new HashMap<String, Object>(5);
        List<Map> records = storeService.getStoreListByMerchantId(merchantInfo.getId(), new PageInfo(1, 1, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC))), null).getRecords();
        if (CollectionUtils.isEmpty(records) || MapUtils.isEmpty(records.get(0))) {
            throw new ContextParamException("商户" + aliDirectReq.getMerchant_sn() + "门店不存在");
        }
        StotreExtInfoAndPictures pictures = mcStoreExtService.findStoreExtAndPicturesByStoreId(BeanUtil.getPropString(records.get(0), DaoConstants.ID), aliDirectReq.getDev_code());
        context.put(KEY_PICTURES, pictures);
        context.put(KEY_MERCHANT, merchantInfo);
        context.put(KEY_BUSINESS_LICENCE, licenseInfo);
        context.put(KEY_WEIXIN_CONTRACT_INFO, aliDirectReq.getContact_info());
        context.put(KEY_APP_INFO, aliDirectReq.getApp_info());
        return context;
    }

    /**
     * @param bankDirectReq
     * @return Map<String, Object>
     * <AUTHOR>
     * @Description: 创建基础信息
     * @time 11:15
     */
    public Map<String, Object> buildBankDirectBaseContext(BankDirectReq bankDirectReq) throws ContextParamException {
        final Map<String, Object> contextParam = Maps.newHashMap();
        final String directReqDevCode = bankDirectReq.getDev_code();
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(bankDirectReq.getMerchant_sn(), directReqDevCode);
        if (merchantInfo == null) {
            throw new ContextParamException("商户" + bankDirectReq.getMerchant_sn() + "不存在");
        }
        MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantInfo.getId(), directReqDevCode);
        if (licenseInfo == null) {
            throw new ContextParamException("商户" + bankDirectReq.getMerchant_sn() + "营业执照不存在");
        }
        final ObjectMapper mapper = new ObjectMapper();
        final String formBody = bankDirectReq.getForm_body();
        final Map formMap = JSONObject.parseObject(formBody, Map.class);
        //MerchantBankAccountPre表主键
        final String bankPreId = BeanUtil.getPropString(formMap, BankDirectApplyConstant.BANK_PRE_ID);
        if (StringUtils.isEmpty(bankPreId) && !applicationApolloConfig.getIgnoreBankPreCheck().contains(directReqDevCode)) {
            throw new ContextParamException("商户 " + bankDirectReq.getMerchant_sn() + " preId不存在");
        }
        if (!StringUtils.isEmpty(bankPreId)) {
            final Map bankAccountMap = merchantBankService.getMerchantBankAccountPre(bankPreId);
            Optional.ofNullable(bankAccountMap).orElseThrow(() -> new ContextParamException("商户" + bankDirectReq.getMerchant_sn() + "pre表银行卡不存在"));
            //替换银行卡相关信息
            //在银行账户添加如下信息为了在spa展示
            bankAccountMap.putIfAbsent("legal_person_name", merchantInfo.getLegal_person_name());
            bankAccountMap.putIfAbsent("legal_person_id_number", merchantInfo.getLegal_person_id_number());
            bankAccountMap.putIfAbsent("legal_person_register_no", merchantInfo.getLegal_person_register_no());

            bankAccountMap.putIfAbsent("legal_person_name", licenseInfo.getLegal_person_name());
            bankAccountMap.putIfAbsent("legal_person_id_number", licenseInfo.getLegal_person_id_number());
            bankAccountMap.putIfAbsent("legal_person_register_no", licenseInfo.getNumber());

            contextParam.put(KEY_BANK_ACCOUNT, bankAccountMap);
        }
        contextParam.put(KEY_MERCHANT, mapper.convertValue(merchantInfo, Map.class));
        contextParam.put(KEY_BUSINESS_LICENCE, mapper.convertValue(licenseInfo, Map.class));
        contextParam.put(DEDICATED_PARAMS, formMap.get(DEDICATED_PARAMS));
        return contextParam;
    }

}
