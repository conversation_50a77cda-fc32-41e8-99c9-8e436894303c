package com.wosai.upay.job.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.exception.CommonDataObjectNotExistsException;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.job.Constants.Request;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.mapper.McChannelMapper;
import com.wosai.upay.job.mapper.McRuleGroupMapper;
import com.wosai.upay.job.model.DO.McChannel;
import com.wosai.upay.job.model.DO.McChannelCustom;
import com.wosai.upay.job.model.DO.McRuleGroup;
import com.wosai.upay.job.model.DO.McRuleGroupExample;
import com.wosai.upay.job.model.dto.*;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McChannelDAO;
import com.wosai.upay.job.refactor.dao.McContractRuleDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.model.converter.McAcquirerConverter;
import com.wosai.upay.job.refactor.model.converter.McContractRuleConverter;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McChannelDO;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.util.PageInfoHelper;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.web.api.ListResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ContractManagerServiceImpl
 *
 * <AUTHOR>
 * @date 2019-07-11 09:57
 */
@Service
@AutoJsonRpcServiceImpl
public class ContractManagerServiceImpl implements ContractManagerService {

    @Autowired
    private McProviderDAO mcProviderDAO;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    private McRuleGroupMapper mcRuleGroupMapper;

    @Autowired
    private McContractRuleDAO mcContractRuleDAO;

    @Autowired
    private McChannelMapper mcChannelMapper;

    @Autowired
    private McChannelDAO mcChannelDAO;

    @Autowired
    private RuleContext ruleContext;

    @Override
    public ListResult<RuleGroupDto> findRuleGroupList(PageInfo pageInfo, RuleGroupDto ruleGroupDto) {
        if (ruleGroupDto == null) {
            ruleGroupDto = new RuleGroupDto();
        }

        pageInfo = PageInfoHelper.checkoutPageInfo(pageInfo);
        String orderByClause = StringUtil.orderByListToOrderByClause(pageInfo.getOrderBy(), "create_at desc");

        McRuleGroupExample example = new McRuleGroupExample();
        if (StringUtils.isNotBlank(orderByClause)) {
            example.setOrderByClause(orderByClause);
        }
        McRuleGroupExample.Criteria criteria = example.createCriteria();
        if (ruleGroupDto.getId() != null) {
            criteria.andIdEqualTo(ruleGroupDto.getId());
        }
        if (StringUtils.isNotBlank(ruleGroupDto.getGroup_id())) {
            criteria.andGroup_idEqualTo(ruleGroupDto.getGroup_id());
        }
        if (StringUtils.isNotBlank(ruleGroupDto.getVendor())) {
            criteria.andVendorEqualTo(ruleGroupDto.getVendor());
        }
        if (StringUtils.isNotBlank(ruleGroupDto.getVendor_app())) {
            criteria.andVendor_appEqualTo(ruleGroupDto.getVendor_app());
        }
        if (ruleGroupDto.getStatus() != null) {
            criteria.andStatusEqualTo(ruleGroupDto.getStatus());
        }
        if (StringUtils.isNotBlank(ruleGroupDto.getName())) {
            criteria.andNameLike("%" + ruleGroupDto.getName().trim() + "%");
        }

        PageHelper.startPage(pageInfo.getPage(), pageInfo.getPageSize());
        // 查询结果
        List<McRuleGroup> mcRuleGroupList = this.mcRuleGroupMapper.selectByExampleWithBLOBs(example);
        List<RuleGroupDto> ruleGroupDtoList = new ArrayList<>(mcRuleGroupList.size());
        mcRuleGroupList.forEach(mcRuleGroup -> {
            RuleGroupDto entity = new RuleGroupDto();
            BeanUtils.copyProperties(mcRuleGroup, entity);
            ruleGroupDtoList.add(entity);
        });

        long total = ((Page) mcRuleGroupList).getTotal();
        ListResult<RuleGroupDto> listResult = new ListResult<>(ruleGroupDtoList);
        listResult.setTotal(total);
        return listResult;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public RuleGroupDto addRuleGroup(RuleGroupDto ruleGroupDto) {

        checkRules(ruleGroupDto.getRules());

        ruleGroupDto.setId(null);
        String groupId = StrUtil.isNotBlank(ruleGroupDto.getGroup_id()) ? ruleGroupDto.getGroup_id() : UUID.randomUUID().toString();
        ruleGroupDto.setGroup_id(groupId);

        McRuleGroup mcRuleGroup = new McRuleGroup();
        BeanUtils.copyProperties(ruleGroupDto, mcRuleGroup);
        mcRuleGroup.setRules(JSON.toJSONString(ruleGroupDto.getRules()));
        this.mcRuleGroupMapper.insertSelective(mcRuleGroup);

        RuleGroupDto result = new RuleGroupDto();
        BeanUtils.copyProperties(this.mcRuleGroupMapper.selectByGroupId(groupId), result);

        ruleContext.refresh();

        return result;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public RuleGroupDto modifyRuleGroup(RuleGroupDto ruleGroupDto) {

        checkRules(ruleGroupDto.getRules());

        McRuleGroup mcRuleGroup = new McRuleGroup();
        BeanUtils.copyProperties(ruleGroupDto, mcRuleGroup);
        mcRuleGroup.setId(null);
        if (CollectionUtils.isNotEmpty(ruleGroupDto.getRules())) {
            mcRuleGroup.setRules(JSON.toJSONString(ruleGroupDto.getRules()));
        }
        mcRuleGroup.setUpdate_at(new Date()); // 防止无数据更新，导致 SQL 拼接错误
        int affectedRows = this.mcRuleGroupMapper.updateByGroupIdSelective(mcRuleGroup);
        if (affectedRows == 0) {
            throw new CommonDataObjectNotExistsException("不存在 group_id:" + mcRuleGroup.getGroup_id() + " 该规则组");
        }

        RuleGroupDto result = new RuleGroupDto();
        BeanUtils.copyProperties(this.mcRuleGroupMapper.selectByGroupId(mcRuleGroup.getGroup_id()), result);

        ruleContext.refresh();

        return result;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public RuleGroupDto enableRuleGroup(String groupId, Integer enable) {
        McRuleGroup updateParams = new McRuleGroup();
        updateParams.setGroup_id(groupId);
        updateParams.setStatus(enable);
        int updatedRows = this.mcRuleGroupMapper.updateByGroupIdSelective(updateParams);
        if (updatedRows == 0) {
            throw new CommonDataObjectNotExistsException("不存在该规则组");
        }

        McRuleGroup mcRuleGroup = this.mcRuleGroupMapper.selectByGroupId(groupId);
        RuleGroupDto result = new RuleGroupDto();
        BeanUtils.copyProperties(mcRuleGroup, result);
        ruleContext.refresh();

        return result;
    }

    /**
     * 检查规则组是否符合要求
     *
     * @param rules
     */
    private void checkRules(List<RuleGroupDto.Rule> rules) {
        if (WosaiCollectionUtils.isEmpty(rules)) {
            throw new CommonPubBizException("规则列表不能为空");
        }
        Map<String, RuleGroupDto.Rule> ruleMap = new HashMap<>(rules.size());
        for (RuleGroupDto.Rule rule : rules) {
            if (ruleContext.getContractRule(rule.getContract_rule()) == null) {
                throw new CommonPubBizException("规则 " + rule.getContract_rule() + " 不存在");
            }
            if (ruleMap.containsKey(rule.getContract_rule())) {
                throw new CommonPubBizException("规则重复");
            }
            ruleMap.put(rule.getContract_rule(), rule);
        }

        for (RuleGroupDto.Rule rule : rules) {
            String dependOn = rule.getDepend_on();
            if (WosaiStringUtils.isNotEmpty(dependOn) && !ruleMap.containsKey(dependOn)) {
                throw new CommonPubBizException("规则 " + dependOn + " 不在规则组");
            }
        }
        for (RuleGroupDto.Rule rule : rules) {
            checkCircle(rule, new HashSet<>(), ruleMap);
        }
    }

    /**
     * 检查是否循环依赖
     */
    private void checkCircle(RuleGroupDto.Rule rule, Set<RuleGroupDto.Rule> cache, Map<String, RuleGroupDto.Rule> ruleMap) {
        if (WosaiStringUtils.isEmpty(rule.getDepend_on())) {
            return;
        }
        if (cache.contains(rule)) {
            throw new CommonPubBizException("规则存在循环依赖");
        }
        cache.add(rule);
        checkCircle(ruleMap.get(rule.getDepend_on()), cache, ruleMap);
    }

    @Override
    public RuleGroupDto findRuleGroupDetailByGroupId(String groupId) {
        // 获取规则组信息
        McRuleGroup mcRuleGroup = this.mcRuleGroupMapper.selectByGroupId(groupId);
        if (mcRuleGroup == null) {
            throw new CommonDataObjectNotExistsException("不存在 group_id:" + groupId + " 该规则组");
        }
        List<Map> ruleAndDependOnMapList = JSON.parseArray(mcRuleGroup.getRules(), Map.class);

        // result
        RuleGroupDto result = new RuleGroupDto();
        BeanUtils.copyProperties(mcRuleGroup, result);
        if (ruleAndDependOnMapList == null || ruleAndDependOnMapList.size() == 0) {
            return result;
        }
        Map<String, String> ruleAndDependOnMap = new HashMap<>(ruleAndDependOnMapList.size());
        // key 为 contract_rule，value 为 depend_on
        ruleAndDependOnMapList.forEach(map -> ruleAndDependOnMap.put(MapUtils.getString(map, "contract_rule"), MapUtils.getString(map, "depend_on")));

        // 被依赖规则信息列表
        List<McContractRuleDO> dependOnList = mcContractRuleDAO.getDOByRules(
                ruleAndDependOnMapList.stream()
                        .map(map -> MapUtils.getString(map, "depend_on"))
                        .collect(Collectors.toList())
        );
        // list -> map
        Map<String, McContractRuleDO> mcContractRuleMap = dependOnList.stream()
                .collect(Collectors.toMap(McContractRuleDO::getRule, McContractRule -> McContractRule));

        // 规则信息列表
        List<McContractRuleDO> mcContractRuleList = mcContractRuleDAO.getDOByRules(
                ruleAndDependOnMapList.stream()
                        .map(map -> MapUtils.getString(map, "contract_rule"))
                        .collect(Collectors.toList())
        );

        // 合并数据
        List<ContractRuleCustomDto> contractRuleCustomDtoList = new ArrayList<>(mcContractRuleList.size());
        mcContractRuleList.forEach(mcContractRule -> {
            ContractRuleCustomDto entity = McContractRuleConverter.contractRuleDOToContractRuleCustomDto(mcContractRule);
            McContractRuleDO dependOn = mcContractRuleMap.get(ruleAndDependOnMap.get(entity.getRule()));
            if (dependOn != null) {
                entity.setDepend_on(dependOn.getRule());
                entity.setDepend_on_name(dependOn.getName());
            }

            contractRuleCustomDtoList.add(entity);
        });
        result.setContract_rules(contractRuleCustomDtoList);

        return result;
    }

    @Override
    public ListResult<ContractRuleDto> findContractRuleList(PageInfo pageInfo, ContractRuleDto contractRuleDto) {
        if (contractRuleDto == null) {
            contractRuleDto = new ContractRuleDto();
        }
        pageInfo = PageInfoHelper.checkoutPageInfo(pageInfo);
        Page<McContractRuleDO> mcContractRuleDOPage = mcContractRuleDAO.pageMcContractRule(pageInfo, contractRuleDto);

        // 查询结果
        List<McContractRuleDO> mcContractRuleList = mcContractRuleDOPage.getResult();
        List<ContractRuleDto> contractRuleDtoList = new ArrayList<>(mcContractRuleList.size());
        mcContractRuleList.forEach(mcContractRule ->
                contractRuleDtoList.add(McContractRuleConverter.contractRuleDOToContractRuleCustomDto(mcContractRule))
        );

        ListResult<ContractRuleDto> listResult = new ListResult<>(contractRuleDtoList);
        listResult.setTotal(mcContractRuleDOPage.getTotal());
        return listResult;
    }

    @Override
    public ContractRuleCustomDto findContractRuleDetailByRule(String rule) {
        Optional<McContractRuleDO> ruleDOOptional = mcContractRuleDAO.getDOByRule(rule);
        if (!ruleDOOptional.isPresent()) {
            throw new CommonDataObjectNotExistsException("不存在 rule:" + rule + " 该报备规则");
        }
        ContractRuleCustomDto result = McContractRuleConverter.contractRuleDOToContractRuleCustomDto(ruleDOOptional.get());

        String channel = result.getChannel();
        if (StringUtils.isEmpty(channel)) {
            return result;
        }

        Optional<McChannelDO> mcChannelOptional = mcChannelDAO.getMcChannelByChannel(channel);

        if (!mcChannelOptional.isPresent()) {
            return result;
        }
        McChannelDO mcChannelDO = mcChannelOptional.get();

        result.setChannel_name(mcChannelDO.getName());
        result.setPayway_channel_no(mcChannelDO.getPaywayChannelNo());
        Map acquirerMetaData = JSON.parseObject(mcChannelDO.getAcquirerMetadata());
        if (acquirerMetaData != null) {
            result.setRece_org_no(MapUtils.getString(acquirerMetaData, "rece_org_no"));
            result.setTech_org_no(MapUtils.getString(acquirerMetaData, "tech_org_no"));
        }

        return result;
    }

    @Override
    public ContractRuleDto addContractRule(ContractRuleDto contractRuleDto) {
        // 校验报备渠道是否存在、是否可用
        Optional<McChannelDO> mcChannelOptional = mcChannelDAO.getMcChannelByChannel(contractRuleDto.getChannel());
        if (!mcChannelOptional.isPresent()) {
            throw new CommonDataObjectNotExistsException("不存在该报备渠道");
        }
        McChannelDO mcChannelDO = mcChannelOptional.get();

        McContractRuleDO updateParams = McContractRuleConverter.contractRuleDtoToContractRuleDO(contractRuleDto);
        updateParams.setId(null);
        String rule = contractRuleDto.getChannel();
        updateParams.setRule(rule);
        updateParams.setPayway(mcChannelDO.getPayway());
        updateParams.setProvider(mcChannelDO.getProvider());
        updateParams.setAcquirer(mcChannelDO.getAcquirer());
        // 默认启用规则
        if (updateParams.getStatus() == null) {
            updateParams.setStatus(Request.RULE_STATUS_ENABLE);
        }
        mcContractRuleDAO.insert(updateParams);

        Optional<McContractRuleDO> ruleDOOptional = mcContractRuleDAO.getDOByRule(rule);
        ContractRuleDto resultDto = McContractRuleConverter.contractRuleDOToContractRuleDto(ruleDOOptional.get());

        ruleContext.refresh();

        return resultDto;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public ContractRuleDto modifyContractRule(ContractRuleDto contractRuleDto) {
        McContractRuleDO updateParams = new McContractRuleDO();
        updateParams.setRule(contractRuleDto.getRule());
        // 目前仅支持以下参数更新
        updateParams.setName(contractRuleDto.getName());
        updateParams.setRetry(contractRuleDto.getRetry());
        updateParams.setIsDefault(contractRuleDto.getIs_default());
        updateParams.setIsInsert(contractRuleDto.getIs_insert());
        updateParams.setIsInsertInfluPtask(contractRuleDto.getIs_insert_influ_ptask());
        updateParams.setIsUpdate(contractRuleDto.getIs_update());
        updateParams.setIsUpdateInfluPtask(contractRuleDto.getIs_update_influ_ptask());
        if (MapUtils.isNotEmpty(contractRuleDto.getMetadata())) {
            updateParams.setMetadata(JSON.toJSONString(contractRuleDto.getMetadata()));
        }

        updateParams.setUpdateAt(new Date());
        int affectedRows = mcContractRuleDAO.updateByRule(contractRuleDto.getRule(), updateParams);
        if (affectedRows == 0) {
            throw new CommonDataObjectNotExistsException("不存在 rule:" + updateParams.getRule() + " 该报备规则");
        }

        Optional<McContractRuleDO> ruleDOOptional = mcContractRuleDAO.getDOByRule(contractRuleDto.getRule());
        ContractRuleDto resultDto = McContractRuleConverter.contractRuleDOToContractRuleDto(ruleDOOptional.get());

        ruleContext.refresh();

        return resultDto;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public ContractRuleDto enableContractRule(String rule, Integer enable) {
        int updatedRows = mcContractRuleDAO.updateByRule(rule, new McContractRuleDO().setStatus(enable));
        if (updatedRows == 0) {
            throw new CommonDataObjectNotExistsException("不存在该报备规则");
        }

        Optional<McContractRuleDO> mcContractRuleOptional = mcContractRuleDAO.getDOByRule(rule);
        ContractRuleDto result = McContractRuleConverter.contractRuleDOToContractRuleDto(mcContractRuleOptional.get());
        ruleContext.refresh();
        return result;
    }

    @Override
    public ListResult<ChannelCustomDto> findChannelList(PageInfo pageInfo, ChannelCustomDto channelCustomDto) {
        if (channelCustomDto == null) {
            channelCustomDto = new ChannelCustomDto();
        }
        McChannelCustom mcChannelCustom = new McChannelCustom();
        BeanUtils.copyProperties(channelCustomDto, mcChannelCustom);

        pageInfo = PageInfoHelper.checkoutPageInfo(pageInfo);
        String orderBy = StringUtil.orderByListToOrderByClause(pageInfo.getOrderBy(), "create_at DESC");

        PageHelper.startPage(pageInfo.getPage(), pageInfo.getPageSize(), orderBy);
        List<McChannelCustom> mcChannelCustomList = this.mcChannelMapper.selectByMcChannelCustom(mcChannelCustom);
        List<ChannelCustomDto> channelDtoList = new ArrayList<>(mcChannelCustomList.size());
        mcChannelCustomList.forEach(mcChannel -> {
            ChannelCustomDto entity = new ChannelCustomDto();
            BeanUtils.copyProperties(mcChannel, entity);
            entity.setPrivate_key(null);
            entity.setProvider_metadata(null);
            if (entity.getPayway() != 0) {
                channelDtoList.add(entity);
            }
        });

        long total = ((Page) mcChannelCustomList).getTotal();
        ListResult<ChannelCustomDto> listResult = new ListResult<>(channelDtoList);
        listResult.setTotal(total);
        return listResult;
    }

    @Override
    public ChannelCustomDto findChannelDetailByChannel(String channel) {
        McChannelCustom mcChannelCustom = this.mcChannelMapper.selectChannelCustomByChannel(channel);
        if (mcChannelCustom == null) {
            throw new CommonDataObjectNotExistsException("不存在该报备通过");
        }

        ChannelCustomDto result = new ChannelCustomDto();
        BeanUtils.copyProperties(mcChannelCustom, result);
        result.setProvider_metadata(null);
        result.setAcquirer_metadata(null);
        result.setProvider_metadata_map(JSON.parseObject(mcChannelCustom.getProvider_metadata()));
        Map<String, Object> acquirerMetadataMap = JSON.parseObject(mcChannelCustom.getAcquirer_metadata());
        if (acquirerMetadataMap != null) {
            acquirerMetadataMap.put("acquirer", result.getAcquirer());
            acquirerMetadataMap.put("acquirer_name", result.getAcquirer_name());
        }
        result.setAcquirer_metadata_map(acquirerMetadataMap);
        return result;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public ChannelDto updateChannel(ChannelDto channelDto) {
        McChannel updateParam = new McChannel();
        updateParam.setChannel(channelDto.getChannel());
        // 目前仅支持更新渠道名称
        updateParam.setName(channelDto.getName());
        int updatedRows = this.mcChannelMapper.updateByChannelSelective(updateParam);
        if (updatedRows == 0) {
            throw new CommonDataObjectNotExistsException("要更新的报备渠道不存在");
        }

        McChannel mcChannel = this.mcChannelMapper.selectByChannel(channelDto.getChannel());
        ChannelDto result = new ChannelDto();
        BeanUtils.copyProperties(mcChannel, result);
        result.setProvider_metadata(null);
        result.setAcquirer_metadata(null);
        result.setProvider_metadata_map(JSON.parseObject(mcChannel.getProvider_metadata()));
        result.setAcquirer_metadata_map(JSON.parseObject(mcChannel.getAcquirer_metadata()));

        ruleContext.refresh();

        return result;
    }

    @Override
    public ListResult<AcquirerDto> findAcquirerList(PageInfo pageInfo, AcquirerDto acquirerDto) {
        if (acquirerDto == null) {
            acquirerDto = new AcquirerDto();
        }

        pageInfo = PageInfoHelper.checkoutPageInfo(pageInfo);

        // 查询结果
        Page<McAcquirerDO> mcAcquirerPage = this.mcAcquirerDAO.pageAcquirerList(pageInfo, acquirerDto);
        List<AcquirerDto> acquirerDtoList = new ArrayList<>(mcAcquirerPage.getResult().size());
        mcAcquirerPage.getResult().forEach(mcAcquirer -> acquirerDtoList.add(McAcquirerConverter.toAcquirerDto(mcAcquirer)));
        setProviderDtoList(acquirerDtoList);
        ListResult<AcquirerDto> listResult = new ListResult<>(acquirerDtoList);
        listResult.setTotal(mcAcquirerPage.getTotal());
        return listResult;
    }

    /**
     * 设置支持的provider数据
     */
    private void setProviderDtoList(List<AcquirerDto> acquirerDtoList) {
        acquirerDtoList.forEach(acquirerDto -> {
            if (CollectionUtils.isNotEmpty(acquirerDto.getMetadata())) {
                acquirerDto.getMetadata().forEach(meta -> {
                    String provider = meta.getProvider();
                    if (StringUtils.isNotBlank(provider)) {
                        meta.setProvider_dtos(findProviderList(null, new ProviderDto().setProvider(provider.trim())).getRecords());
                    }
                });
            }
        });
    }

    @Override
    public ListResult<ProviderDto> findProviderList(PageInfo pageInfo, ProviderDto providerDto) {
        if (providerDto == null) {
            providerDto = new ProviderDto();
        }
        //避免空指针
        pageInfo = PageInfoHelper.checkoutPageInfo(pageInfo);

        Page<McProviderDO> mcProviderPage = mcProviderDAO.pageProviderList(pageInfo, providerDto);
        // 查询结果
        List<ProviderDto> providerDtoList = getProviderDtoList(mcProviderPage.getResult());
        ListResult<ProviderDto> listResult = new ListResult<>(providerDtoList);
        listResult.setTotal(mcProviderPage.getTotal());
        return listResult;
    }


    private List<ProviderDto> getProviderDtoList(List<McProviderDO> mcProviderDOList) {
        List<ProviderDto> providerDtoList = new ArrayList<>(mcProviderDOList.size());
        mcProviderDOList.forEach(mcProvider -> {
            ProviderDto entity = new ProviderDto();
            BeanUtils.copyProperties(mcProvider, entity);
            if (StringUtils.isNotBlank(mcProvider.getMetadata())) {
                entity.setMetadata(JSON.parseArray(mcProvider.getMetadata(), ProviderDto.Payway.class));
            }
            providerDtoList.add(entity);
        });
        return providerDtoList;
    }

    @Override
    public boolean refreshRuleContext() {
        ruleContext.refresh();
        return true;
    }
}