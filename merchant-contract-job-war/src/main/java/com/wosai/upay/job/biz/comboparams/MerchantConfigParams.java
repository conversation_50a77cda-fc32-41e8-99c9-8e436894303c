package com.wosai.upay.job.biz.comboparams;

import com.wosai.upay.core.model.TransactionParam;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 构建交易参数用的实体
 * 从报备的merchant_provider_params到core-b的merchant_config
 * 业务规则转换用的实体对象
 * <AUTHOR>
 * Date 2020/6/3 3:21 下午
 **/
@Data
@Accessors(chain = true)
public class MerchantConfigParams {

    /**
     * agent表的name
     **/
    private String agentName;
    /**
     * 支付方式
     **/
    private int payWay;
    /**
     * 结算通道
     **/
    private int provider;
    /**
     * 支付源商户号
     **/
    private String payMerchantId;
    /**
     * 花呗状态
     **/
    private int huaBeiStatus = 1;
    /**
     * 拉卡拉终端号
     **/
    private String lklTermId;
    /**
     * 结算通道商户号
     **/
    private String providerMerchantId;
    /**
     * 费率
     **/
    private String fee;
    /**
     *
     **/
    private String merchantSn;
    /**
     *
     **/
    private String merchantId;
    /**
     * 支付appid
     **/
    private String subAppid;
    /**
     *
     **/
    private String subAppSecret;
    /**
     * 小程序appid
     **/
    private String miniSubAppid;
    /**
     *
     **/
    private String miniSubAppSecret;
    /**
     * 历史问题+特殊 通联交易参数
     **/
    private Map tongLianTradeParams = new HashMap();
    /**
     * 需要修改的二级支付方式费率
     **/
    private List<String> feeColumns;
    /**
     * 需要修改的二级支付方式agentName
     **/
    private List<String> agentColumns;

    public Map getTongLianTradeParams() {
        this.tongLianTradeParams.put(TransactionParam.WEIXIN_MINI_SUB_APP_ID, this.miniSubAppid);
        this.tongLianTradeParams.put(TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, this.miniSubAppSecret);
        return this.tongLianTradeParams;
    }
}
