package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.ContractSubTaskTypeEnum;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/6
 */
@Component("ccb-biz")
@Slf4j
public class CcbAcquireBiz implements IAcquirerBiz {

    @Autowired
    private WechatAuthBiz wechatAuthBiz;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private RuleContext ruleContext;

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_CCB;
    }

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        return null;
    }


    @Override
    public String getNormalWxRule() {
        return ContractRuleConstants.CCB_NORMAL_WEIXIN_RULE;
    }


    @Override
    public WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        String merchantName = wechatAuthBiz.getWechatAuthMerchantName(providerParams.getMerchant_sn());
        return new WxMchInfo().setMchInfo(new MchInfo().setMerchant_name(merchantName));
    }

    @Override
    public AlipayMchInfo getAlipayMchInfo(MerchantProviderParams providerParams) {
        final String merchantSn = providerParams.getMerchant_sn();
        final List<ContractSubTask> subTasks = contractSubTaskMapper.findTasksByMerchantAndPayway(
                new ContractSubTaskReq()
                        .setPayway(PaywayEnum.ACQUIRER.getValue())
                        .setMerchantSn(merchantSn)
                        .setTaskType(ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
                        .setStatus(TaskStatus.SUCCESS.getVal())
        );
        List<ContractSubTask> ccb = subTasks.stream().filter(subTask -> Objects.equals(subTask.getChannel(), ChannelEnum.CCB.getValue()))
                .limit(1)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ccb)){
            throw new CommonPubBizException("找不到线上记录");
        }
        Map requestBody = JSON.parseObject(ccb.get(0).getRequest_body());
        final String name = MapUtils.getString(requestBody, "Acq_Mrch_Chn_Nm");
        return new AlipayMchInfo().setName(name).setSub_merchant_id(providerParams.getPay_merchant_id());
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        Optional<MerchantProviderParamsDO> unionParam = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, ProviderEnum.PROVIDER_CCB.getValue(), PaywayEnum.UNIONPAY.getValue());
        if (unionParam.isPresent()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                    .retry(false)
                    .build();
        }
        ContractTask netInTask = getNetInTask(merchantSn, AcquirerTypeEnum.CCB.getValue());
        if (Objects.nonNull(netInTask) && (TaskStatus.PROGRESSING.getVal().equals(netInTask.getStatus()) || TaskStatus.PENDING.getVal().equals(netInTask.getStatus()))) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .message("开通中，请稍后重试")
                    .retry(true)
                    .build();
        }
        if (Objects.nonNull(netInTask) && TaskStatus.FAIL.getVal().equals(netInTask.getStatus())) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                    .message("开通失败")
                    .retry(false)
                    .build();
        }
        return UnionPayOpenStatusQueryResp.builder()
                .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                .message("暂时无法开通，联系销售支持")
                .retry(false)
                .build();
    }

    private ContractTask getNetInTask(String merchantSn, String acquirer) {
        List<ContractTask> contractTasks = contractTaskMapper.getContractsBySnAndType(merchantSn, ProviderUtil.CONTRACT_TYPE_INSERT);
        for (ContractTask contractTask : contractTasks) {
            RuleGroup ruleGroup = ruleContext.getRuleGroup(contractTask.getRule_group_id());
            if (ruleGroup.getAcquirer().equals(acquirer)) {
                return contractTask;
            }
        }
        return null;
    }
}
