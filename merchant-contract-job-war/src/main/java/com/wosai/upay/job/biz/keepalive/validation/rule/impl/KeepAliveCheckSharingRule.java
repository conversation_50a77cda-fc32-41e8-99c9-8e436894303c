package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.profit.sharing.model.SharingConfig;
import com.wosai.profit.sharing.model.response.SharingConfigInfo;
import com.wosai.profit.sharing.service.SharingConfigService;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 商户分账规则
 * 检查商户是否开通了分账功能
 */
@Component
public class KeepAliveCheckSharingRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private SharingConfigService sharingConfigService;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            // 检查商户是否开通了分账功能
            if (hasSplitAccountFunction(context)) {
                return createFailureResult(
                        "商户开通了分账功能，不允许执行",
                        "MERCHANT_SHARING_BLOCKED"
                );
            }

            return createSuccessResult("商户未开通分账功能，通过检查");
        } catch (Exception e) {
            logger.error("执行商户分账规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    /**
     * 检查商户是否开通了分账功能
     */
    private boolean hasSplitAccountFunction(KeepAliveValidateContext context) {
        SharingConfigInfo sharingConfigInfo = sharingConfigService.getMerchantSharingConfigInfo(context.getMerchantId());
        return sharingConfigInfo != null &&
                (Objects.equals(SharingConfig.STATUS_ENABLED, sharingConfigInfo.getIndirectStatus()) || sharingConfigInfo.isReceiver());
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.MERCHANT_SHARING;
    }
}