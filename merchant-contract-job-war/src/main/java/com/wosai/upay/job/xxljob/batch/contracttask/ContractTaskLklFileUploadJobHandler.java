package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * xxl_job_desc: 进件任务-拉卡拉附件上传
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("ContractTaskLklFileUploadJobHandler")
public class ContractTaskLklFileUploadJobHandler extends AbstractBatchJobHandler<ContractTask> {

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private SubTaskHandlerContext subTaskHandlerContext;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public List<ContractTask> queryTaskItems(BatchJobParam param) {
        return contractTaskMapper.selectPicUploadTo(StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
    }

    @Override
    public String getLockKey(ContractTask contractTask) {
        return "ContractTaskLklFileUploadJobHandler:" + contractTask.getId();
    }

    @Override
    public void doHandleSingleData(ContractTask contractTask) {
        List<ContractSubTask> subTasks = contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus(contractTask.getMerchant_sn(), contractTask.getId(), 1);
        subTasks.forEach(subTask -> {
            try {
                subTaskHandlerContext.handle(contractTask, subTask);
            } catch (Exception e) {
                log.error("uploadPic v3 error", e);
                chatBotUtil.sendMessageToContractWarnChatBot("uploadPic v3 error" + ExceptionUtil.getThrowableMsg(e));
            }
        });
    }
}
