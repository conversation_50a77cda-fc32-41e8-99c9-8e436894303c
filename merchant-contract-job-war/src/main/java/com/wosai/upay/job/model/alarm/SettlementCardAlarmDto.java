package com.wosai.upay.job.model.alarm;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SettlementCardAlarmDto implements Serializable {

    /**
     * 收钱吧商户号
     */
    private String merchantSn;

    /**
     * 收单机构商户号
     */
    private String acquiringMerchantNo;

    /**
     * 收单机构名称
     */
    private String acquiringInstitutionName;

    /**
     * 报错备注
     */
    private String errorRemarks;

    /**
     * 操作流程
     */
    private String operationProcess;
    /**
     * 银行名
     */
    private String bankName;

    /**
     * 开户行名
     */
    private String branchName;

    /**
     * 开户行号
     */
    private String openingNumber;

    /**
     * 结算行号
     */
    private String clearingNumber;
}
