package com.wosai.upay.job.biz.acquirePos;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.operator.dto.request.CallBackRequest;
import com.wosai.operator.service.BusinessOpenService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TerminalConfig;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.Constants.PosConstant;
import com.wosai.upay.job.Constants.PreAuthApplyConstant;
import com.wosai.upay.job.Constants.TradeConstants;
import com.wosai.upay.job.biz.acquirer.IAcquirerBiz;
import com.wosai.upay.job.controller.TongLianV2Controller;
import com.wosai.upay.job.enume.ProviderTerminalBindConfigStatus;
import com.wosai.upay.job.externalservice.coreb.TradeConfigClient;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.AcquirerMerchantDto;
import com.wosai.upay.job.model.dto.acquirePos.InnerBindCheckDTO;
import com.wosai.upay.job.model.dto.acquirePos.PosActiveInfo;
import com.wosai.upay.job.model.dto.acquirePos.PosActiveInfoDTO;
import com.wosai.upay.job.model.dto.acquirePos.enums.FeeType;
import com.wosai.upay.job.model.lklV3Pos.ApplyPosRequest;
import com.wosai.upay.job.refactor.dao.ProviderTerminalBindConfigDAO;
import com.wosai.upay.job.refactor.model.entity.ProviderTerminalBindConfigDO;
import com.wosai.upay.job.util.SybPOSFeeRateUtils;
import com.wosai.upay.merchant.contract.model.LimitResp;
import com.wosai.upay.merchant.contract.model.tlV2.DTO.MchProductDTO;
import com.wosai.upay.merchant.contract.model.tlV2.ProductInfo;
import com.wosai.upay.merchant.contract.model.tlV2.enume.ProductReqTypeEnum;
import com.wosai.upay.merchant.contract.model.tlV2.enume.ProductTypeEnum;
import com.wosai.upay.merchant.contract.model.tlV2.enume.TerminalReqTypeEnum;
import com.wosai.upay.merchant.contract.model.tlV2.enume.TerminalStatusEnum;
import com.wosai.upay.merchant.contract.model.tlV2.response.ProductResponse;
import com.wosai.upay.merchant.contract.model.tlV2.response.TerminalInitKeyResponse;
import com.wosai.upay.merchant.contract.model.tlV2.response.TerminalResponse;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.comboparams.ProviderParamsHandle.SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY;

/**
 * @Description: 通联收银宝
 * @Date 2024/2/26 18:01
 * @Version 1.0
 */

@Component
@Slf4j
public class TlT9HandleService extends AbstractT9HandleService {

    @Autowired
    private TongLianV2Service tongLianV2Service;

    @Autowired
    private TradeConfigClient tradeConfigClient;

    @Autowired
    private RedisTemplate redisTemplate;


    @Autowired
    private StoreService storeService;

    @Autowired
    private ProviderTerminalBindConfigDAO providerTerminalBindConfigDAO;


    @Value("${tl_pos_devCode}")
    public String tlPosDevCode;

    @Value("${tl_foreignCard_devCode}")
    private String tlForeignCardDevCode;


    @Value("${tl_preAuth_devCode}")
    private String tlPreAuthDevCode;


    protected String TERMINAL_ID = "terminal_id";

    protected String TERM_ID = "term_id";

    protected String CUS_ID = "cus_id";

    public static final String Tl_AGENT_NAME = "1035_21_*_false_true_0001";

    public static final String C2B_FEE_RATE = "0.55";

    public static final String SYB_SIGN_URL = "sybSignUrl";

    public static final Integer TONGLIAN_INTERNL_CARD_OPEN = 4;
    public static final Integer TONGLIAN_FOREIGN_CARD_OPEN = 5;
    public static final Integer TONGLIAN_CARD_PRE_AUTH = 6;

    @Resource(name = "iotBusinessOpenService")
    private BusinessOpenService iotBusinessOpenService;


    @Override
    public String choseAcquire() {
        return McConstant.ACQUIRER_TONGLIANV2;
    }



    @Override
    public void innerBindCheck(InnerBindCheckDTO dto) {

        MerchantInfo merchant = merchantService.getMerchantBySn(dto.getMerchantSn(), null);
        String merchantSn = merchant.getSn();
        final String merchantId = merchant.getId();
        List<AcquirerMerchantDto> acquirerMerchantInfo = merchantProviderParamsService.getAcquirerMerchantInfo(merchantId);
        //是否成功进件
        boolean match = acquirerMerchantInfo.parallelStream().anyMatch(info -> info.getAcquirer().contains(McConstant.ACQUIRER_TONGLIANV2));
        if(!match) {
            throw new CommonPubBizException(PosConstant.FAIL_OTHER_ACQUIRE);
        }
        //若POS扫码绑定门店，与商户是否跨省跨直辖市；
        final String storeSn = dto.getStoreSn();
        if(StringUtils.isBlank(storeSn)) {
            throw new CommonPubBizException("缺少商户号");
        }
        final StoreInfo store = storeService.getStoreBySn(storeSn, null);
        final String storeProvince = StrUtil.trim(store.getProvince());
        final String merchantProvince = StrUtil.trim(merchant.getProvince());
        if(!Objects.equals(storeProvince,merchantProvince)) {
            throw new CommonPubBizException("POS扫码绑定门店，与商户跨省或跨直辖市");
        }
        //当前正在使用的收单机构
        ContractStatus contractStatus = Optional.ofNullable(contractStatusService.selectByMerchantSn(merchantSn))
                .orElseGet(ContractStatus::new);
        String acquirer = contractStatus.getAcquirer();
        if(Objects.equals(acquirer,McConstant.ACQUIRER_TONGLIANV2)) {
            List<String> otherAcquireVenderList = factory.otherAcquireVenderList(McConstant.ACQUIRER_TONGLIANV2);
            existOtherT9(merchantId,otherAcquireVenderList);
            return;
        }
        List<String> indirectAcquirerList = mcAcquirerDAO.getIndirectAcquirerList();
        if(indirectAcquirerList.contains(acquirer)) {
            throw new CommonPubBizException(PosConstant.FAIL_OTHER_ACQUIRE);
        }
        //当前在银行通道
        List<String> otherAcquireVenderList = factory.otherAcquireVenderList(McConstant.ACQUIRER_TONGLIANV2);
        existOtherT9(merchantId,otherAcquireVenderList);
        return;
    }

    @Override
    public ContractResponse openPos(ApplyPosRequest request) {
        final ContractResponse response = new ContractResponse();
        final String merchantSn = request.getMerchantSn();
        try {
            //先插入状态信息crm可以立刻查询到
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, request.getDevCode(), DirectStatus.STATUS_PROCESS, null);
            final String formBody = request.getForm_body();
            final Map map = JSONObject.parseObject(formBody, Map.class);
            MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
            final String payMerchantId = acquirerParams.getPay_merchant_id();
            //推送消息
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, request.getDevCode(), DirectStatus.STATUS_SUCCESS, null);
            //保存电子协议
            CompletableFuture.runAsync(()->saveAgreement(merchantSn,BeanUtil.getPropString(map,SYB_SIGN_URL)));
            //TODO 调用交易接口初始化信息
            updateTlCardMerchantConfig(request, map, payMerchantId);
            response.setSuccess(Boolean.TRUE);
        } catch (Exception exception) {
            log.error("applyPos error request:{},exception:{}", JSONObject.toJSONString(request), exception);
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, request.getDevCode(), DirectStatus.STATUS_BIZ_FAIL, exception.getMessage());
            response.setMsg(exception.getMessage());
        }
        return response;
    }

    /**
     * 保存电子协议
     * @param merchantSn
     * @param signPdfUrl
     */
    public void saveAgreement(String merchantSn,String signPdfUrl) {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        agreementBiz.recordAgreementForT9(merchant.getId(),signPdfUrl);
    }


    /**
     * 交易参数
     *
     * @param request
     * @param map
     * @param providerMerchantId 收单机构商户
     */
    public void updateTlCardMerchantConfig(ApplyPosRequest request, Map map, String providerMerchantId) {
        final Map<String, Map> bankcardFee = MapUtils.getMap(map,BANKCARD_FEE);
        final long tradeComboId = BeanUtil.getPropLong(map, TRADECOMBO_ID);
        final String merchantSn = request.getMerchantSn();
        final MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName(SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY);
        String tradeParamKey = BeanUtil.getPropString(providerTradeParamsKey, String.valueOf(PayParamsModel.PROVIDER_TONGLIAN_V2));

        final Map merchantConfigParams = CollectionUtil.hashMap(MerchantConfig.MERCHANT_ID,merchant.getId(),
                MerchantConfig.C2B_STATUS,MerchantConfig.STATUS_OPENED,
                //兜底费率
                MerchantConfig.C2B_FEE_RATE,C2B_FEE_RATE,
                MerchantConfig.C2B_AGENT_NAME, Tl_AGENT_NAME,
                MerchantConfig.PROVIDER,PayParamsModel.PROVIDER_TONGLIAN_V2,
                MerchantConfig.PARAMS,CollectionUtil.hashMap(tradeParamKey, CollectionUtil.hashMap(CUS_ID, providerMerchantId))
                );
        //交易参数管理
        //TODO 调用交易接口初始化信息
        tradeConfigService.updateBankCardMerchantConfig(merchantConfigParams);
        t9PosFeeCombo(bankcardFee, tradeComboId, merchantSn);
        //记录在sub_biz_param表中
        String t9TradeAppId = subBizParamsBiz.getT9TradeAppId();
        subBizParamsBiz.updateSubBizParams(merchantSn, t9TradeAppId, PayParamsModel.PROVIDER_TONGLIAN_V2, new MerchantProviderParams());
    }

    /**
     * 设置T9刷卡费率套餐
     * @param bankcardFee
     * @param tradeComboId
     * @param merchantSn
     */
    public void t9PosFeeCombo(Map<String, Map> bankcardFee, long tradeComboId, String merchantSn) {
        try {
            final ArrayList<Map> list = Lists.newArrayList();
            bankcardFee.forEach((k, v) -> {
                final Map detailMap = CollectionUtil.hashMap("type", String.valueOf(k).toLowerCase(), "fee", BeanUtil.getPropString(v, "fee"));
                final String maxFee = BeanUtil.getPropString(v, "max");
                if (StringUtils.isNotBlank(maxFee)) {
                    detailMap.put("max", maxFee);
                }
                list.add(detailMap);
            });
            Map bankPosMap = CollectionUtil.hashMap("fee_rate_type", "channel", "value", list);

            final Map applyFeeRateMap = CollectionUtil.hashMap(
                    "21", JSONObject.toJSONString(bankPosMap));
            ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest();
            applyFeeRateRequest.setMerchantSn(merchantSn);
            applyFeeRateRequest.setAuditSn("T9业务开通");
            applyFeeRateRequest.setTradeComboId(tradeComboId);
            applyFeeRateRequest.setApplyPartialPayway(Boolean.FALSE);
            applyFeeRateRequest.setApplyFeeRateMap(applyFeeRateMap);
            applyFeeRateRequest.setCheck(Boolean.TRUE);
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
            supportService.removeCachedParams(merchantSn);
        } catch (Exception e) {
            log.error("tl t9PosFeeCombo merchantSn:{}, error:{}",merchantSn,e);
        }
    }

    /**
     * 获取POS机激活信息
     *
     * @param dto 包含POS终端序列号的DTO对象
     * @return 返回PosActiveInfo对象，包含激活后的信息；如果激活失败或无法激活，则返回空信息。
     * @throws CommonPubBizException 如果激活检查不通过或过程出错，则抛出异常。
     */
    @Override
    public PosActiveInfo getPosActiveInfo(PosActiveInfoDTO dto) {
        // 根据传入的终端序列号获取终端信息
        String terminalSn = dto.getTerminalSn();
        Map terminal = terminalService.getTerminalBySn(terminalSn);
        // 从终端信息中提取商家ID，并根据商家ID获取商家信息
        String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
        final MerchantInfo merchant = Optional.ofNullable(merchantService.getMerchantById(merchantId, null))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));
        String merchantSn = merchant.getSn();
        MerchantProviderParams acquirerParams = Optional.ofNullable(merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue()))
                .orElseThrow(() -> new CommonPubBizException("商户收单机构参数不存在"));
        // 查询银行卡开通状态，如果状态不是成功，则抛出异常
        DirectStatus directStatus = Optional.ofNullable(directStatusBiz.getDirectStatusByMerchantSnAndDevCode(merchantSn, tlPosDevCode))
                .orElseGet(DirectStatus::new);
        if(!Objects.equals(DirectStatus.STATUS_SUCCESS,directStatus.getStatus())) {
            throw new CommonPubBizException(PosConstant.APPLY_OPEN);
        }
        // 当前使用的收单机构
        ContractStatus contractStatus = Optional.ofNullable(contractStatusService.selectByMerchantSn(merchantSn))
                .orElseGet(ContractStatus::new);
        String acquirer = contractStatus.getAcquirer();
        List<String> indirectAcquirerList = mcAcquirerDAO.getIndirectAcquirerList();
        //间连非收银宝
        if(indirectAcquirerList.contains(acquirer) && !Objects.equals(acquirer,McConstant.ACQUIRER_TONGLIANV2)) {
            //当前不是收银宝，则抛出无法激活的异常
            throw new CommonPubBizException(PosConstant.CAN_NOT_ACTIVE);
        }
        final ProviderTerminalBindConfigDO bindConfigDO = providerTerminalBindConfigDAO
                .getByMerchantSnProviderPayWayTerminalSn(merchantSn, PayParamsModel.PROVIDER_TONGLIAN_V2, PaywayEnum.BANK_CARD.getValue(), terminalSn);
        if(Objects.nonNull(bindConfigDO) && Objects.equals(bindConfigDO.getStatus(), ProviderTerminalBindConfigStatus.BIND_FAIL.getStatus())) {
            throw new CommonPubBizException(StringUtils.defaultString(bindConfigDO.getResult(),"终端审核失败,请重新解绑绑定"));
        }
        if(Objects.isNull(bindConfigDO) || !Objects.equals(bindConfigDO.getStatus(), ProviderTerminalBindConfigStatus.BIND_SUCCESS.getStatus())) {
            throw new CommonPubBizException(PosConstant.SYB_NOT_ACTIVE);
        }
        final String providerTerminalId = bindConfigDO.getProviderTerminalId();
        // 调用收银宝服务进行终端添加
        com.wosai.upay.merchant.contract.model.ContractResponse response = tongLianV2Service.getTerminalInitKey(merchantSn, providerTerminalId);
        if(!response.isSuccess()){
            // 如果收银宝服务调用失败，则抛出异常
            throw new CommonPubBizException(response.getMessage());
        }
        // 处理收银宝服务返回的响应数据
        Map<String, Object> respData = Optional.ofNullable(response.getResponseParam()).orElseGet(HashMap::new);
        TerminalInitKeyResponse terminalInitKeyResponse = JSONObject.parseObject(JSONObject.toJSONString(respData), TerminalInitKeyResponse.class);
        String initkey = terminalInitKeyResponse.getInitkey();
        if(StringUtils.isEmpty(initkey)) {
            // 如果不存在有效的激活信息，则抛出异常
            throw new CommonPubBizException(PosConstant.ACTIVE_NO_FAIL);
        }
        PosActiveInfo posActiveInfo = new PosActiveInfo();
        posActiveInfo.setAcquireTermId(providerTerminalId);
        posActiveInfo.setAcquireMerNo(acquirerParams.getPay_merchant_id());
        // 更新终端配置，将终端号写入交易
        updateTlCardTerminalConfig(BeanUtil.getPropString(terminal, DaoConstants.ID), providerTerminalId);
        posActiveInfo.setActiveNo(initkey);
        //开通多业务的时候写入新的清算通道
        try {
            tradeConfigClient.updateClearProviderByAcquirer(merchantId, McConstant.ACQUIRER_TONGLIANV2);
        } catch (Exception e) {
            log.error("清算通道设置失败 商户号:{}",merchantSn,e);
            throw new CommonPubBizException("清算通道设置失败");
        }
        return posActiveInfo;
    }

    private void updateTlCardTerminalConfig(String terminalId, String acquireTermId) {
        final Map terminalConfigParams = CollectionUtil.hashMap(TERMINAL_ID,terminalId,
                TerminalConfig.PAYWAY, TradeConstants.PAYWAY_BANKCARD,
                TerminalConfig.C2B_STATUS,TerminalConfig.STATUS_OPENED,
                TerminalConfig.C2B_FEE_RATE,C2B_FEE_RATE,
                TerminalConfig.C2B_AGENT_NAME, Tl_AGENT_NAME,
                TerminalConfig.PROVIDER,PayParamsModel.PROVIDER_TONGLIAN_V2);
        Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName(SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY);
        String tradeParamKey = BeanUtil.getPropString(providerTradeParamsKey, String.valueOf(PayParamsModel.PROVIDER_TONGLIAN_V2));
        if(!StringUtils.isEmpty(acquireTermId)) {
            terminalConfigParams.put(MerchantConfig.PARAMS,CollectionUtil.hashMap(tradeParamKey, CollectionUtil.hashMap(TERM_ID, acquireTermId)));
        }else {
            terminalConfigParams.put(MerchantConfig.PARAMS, Maps.newHashMap());
        }
        try {
            tradeConfigService.updateBankCardTerminalConfig(terminalConfigParams);
        } catch (Exception e) {
            log.error("updateTlCardTerminalConfig acquireTermId:{},error:{}",acquireTermId,e);
        }
    }

    /**
     * 添加收银宝T9终端,这里只是添加,只有当回调成功才会真正写入交易,
     * 通联当前系统待审批记录只有1条
     * 所以要保证只有在前一个终端回调成功才允许进行后续终端绑定
     * @see
     * @param terminalSn
     */
    @Override
    public void t9TermAdd(String terminalSn) throws CommonPubBizException {
        Map terminal = terminalService.getTerminalBySn(terminalSn);
        // 从终端信息中提取商家ID，并根据商家ID获取商家信息
        String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        String merchantSn = merchant.getSn();
        //本地记录
        final List<ProviderTerminalBindConfigDO> terminalBindConfigDOS = providerTerminalBindConfigDAO
                .listByMerchantSnProviderPayWay(merchantSn, PayParamsModel.PROVIDER_TONGLIAN_V2, PaywayEnum.BANK_CARD.getValue());
        //这个终端是否已经绑定成功
        final boolean alreadyBind = terminalBindConfigDOS.stream()
                .anyMatch(config -> Objects.equals(config.getStatus(), ProviderTerminalBindConfigStatus.BIND_SUCCESS.getStatus())
                        && Objects.equals(config.getTerminalSn(),terminalSn));
        if(alreadyBind) {
            return;
        }
        //保证只有审核中的终端
        final boolean anyMatch = terminalBindConfigDOS.stream()
                .anyMatch(config -> Objects.equals(config.getStatus(), ProviderTerminalBindConfigStatus.PENDING.getStatus()));
        if(anyMatch) {
            log.warn("商户sn:{}终端还在审核中",merchantSn);
            return;
        }
        //由于收单机构限制每个商户最多只能添加10个终端且收单机构没有提供删除接口,所以需要本地自己记录收单机构终端号与收钱吧终端号的映射关系, 所有终端与收钱吧都建立联系,则需要新增一个
        // 调用收银宝服务进行终端查询
        com.wosai.upay.merchant.contract.model.ContractResponse queryResponse = tongLianV2Service.termOperate(merchantSn, TerminalReqTypeEnum.QUERY);
        if(!queryResponse.isSuccess()){
            // 如果收银宝服务调用失败，则抛出异常
            throw new CommonPubBizException(queryResponse.getMessage());
        }
        final TerminalResponse termQueryResponse = JSONObject.parseObject(JSONObject.toJSONString(queryResponse.getResponseParam()), TerminalResponse.class);
        //收单机构返回终端列表
        String termlist = Optional.ofNullable(termQueryResponse)
                .map(TerminalResponse::getTermlist)
                .orElse("[]");
        final List<TerminalResponse.TerminalInfo> termList =JSONArray.parseArray(termlist, TerminalResponse.TerminalInfo.class);
        //本地记录的绑定关系
        final List<ProviderTerminalBindConfigDO> bindConfigDOList = terminalBindConfigDOS;

        final List<String> localBindSuccessList = bindConfigDOList.stream()
                .filter(bind -> Objects.equals(bind.getStatus(), ProviderTerminalBindConfigStatus.BIND_SUCCESS.getStatus()))
                .map(ProviderTerminalBindConfigDO::getProviderTerminalId).collect(Collectors.toList());

        //找出状态正常且没有和本地终端建立联系的终端
        String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        final StoreInfo storeInfo = storeService.getStoreById(storeId,null);
        final Optional<TerminalResponse.TerminalInfo> unbindProviderInfo = termList.stream()
                //查询状态正常的终端
                .filter(info -> Objects.equals(info.getStatus(), TerminalStatusEnum.NORMAL.getCode()))
                .filter(info -> !localBindSuccessList.contains(info.getTermcode()))
                .findFirst();
        //还有没有建立联系的则需要建立绑定关系
        final ProviderTerminalBindConfigDO configDO = new ProviderTerminalBindConfigDO();
        configDO.setId(UUID.randomUUID().toString());
        configDO.setMerchantSn(merchantSn);
        configDO.setStoreSn(storeInfo.getSn());
        configDO.setTerminalSn(terminalSn);
        configDO.setProvider(PayParamsModel.PROVIDER_TONGLIAN_V2);
        configDO.setPayway(PaywayEnum.BANK_CARD.getValue());
        configDO.setSubMchId(termQueryResponse.getCusid());
        if(unbindProviderInfo.isPresent()) {
            configDO.setProviderTerminalId(unbindProviderInfo.get().getTermcode());
            configDO.setStatus(ProviderTerminalBindConfigStatus.BIND_SUCCESS.getStatus());
            configDO.setResult("绑定成功");
            providerTerminalBindConfigDAO.insertOne(configDO);
        }else {
            //没有可用终端则需要重新生成一个,并且建立一个绑定中的关系
            com.wosai.upay.merchant.contract.model.ContractResponse addResponse = tongLianV2Service.termOperate(merchantSn, TerminalReqTypeEnum.ADD);
            if(!addResponse.isSuccess()){
                // 如果收银宝服务调用失败，则抛出异常
                throw new CommonPubBizException(addResponse.getMessage());
            }
            final TerminalResponse termAddResponse = JSONObject.parseObject(JSONObject.toJSONString(addResponse.getResponseParam()), TerminalResponse.class);
            String addTerms = Optional.ofNullable(termAddResponse)
                    .map(TerminalResponse::getAddterms)
                    .orElse(""); // 空值兜底

            List<String> terms = Splitter.on("#")
                    .trimResults()
                    .omitEmptyStrings()
                    .splitToList(addTerms);

            if (terms.isEmpty()) {
                throw new CommonPubBizException("收银宝终端添加失败：未返回有效终端号");
            }
            configDO.setProviderTerminalId(terms.get(0));
            configDO.setStatus(ProviderTerminalBindConfigStatus.PENDING.getStatus());
            configDO.setResult("绑定中");
            providerTerminalBindConfigDAO.insertOne(configDO);
        }
    }


    @Override
    public void t9TermUnbind(String terminalSn) {
        final Map terminal = terminalService.getTerminalBySn(terminalSn);
        final String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        String merchantSn = merchant.getSn();
        //将交易侧"获取激活码时写入的终端号"删除
        updateTlCardTerminalConfig(BeanUtil.getPropString(terminal, "id"),null);
        //删除本地provider_terminal_bind_config表记录
        providerTerminalBindConfigDAO
                .deleteTerminalLevelProviderTerminalBindConfig(merchantSn,
                                                                PayParamsModel.PROVIDER_TONGLIAN_V2,
                                                                terminalSn,
                                                                PaywayEnum.BANK_CARD.getValue());
    }

    @Override
    public List<LimitResp> queryLimit(String merchantSn) {
        return Lists.newArrayList();
    }

    @Override
    public String getDevCode() {
        return this.tlPosDevCode;
    }

    @Override
    public String getForeignCardDevCode() {
        return this.tlForeignCardDevCode;
    }

    @Override
    public boolean isSupport(String devCode) {
        return Boolean.FALSE;
    }

    @Override
    public void closeForeignCard(String merchantSn) {
        //删除外卡记录
        final ForeignCard foreignCard = Optional.ofNullable(foreignCardMapper.selectSuccessByMerchantSnAndCode(merchantSn, tlForeignCardDevCode))
                .orElseGet(ForeignCard::new);
        if(Objects.nonNull(foreignCard.getId())) {
            foreignCardMapper.deleteByPrimaryKey(foreignCard.getId());
        }
        directStatusDAO.deleteByMerchantSnAndDevCode(merchantSn, tlForeignCardDevCode);
        final MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        final CallBackRequest callBackRequest = new CallBackRequest();
        callBackRequest.setBizType(TONGLIAN_FOREIGN_CARD_OPEN);
        callBackRequest.setMerchantId(merchant.getId());
        callBackRequest.setAuditResult(2);
        callBackRequest.setErrMessage("注销外卡");
        try {
            iotBusinessOpenService.resultCallBack(callBackRequest);
        } catch (Exception e) {
            log.warn("closeForeignCard resultCallBack merchantSn :{},error",merchantSn,e);
        }
    }


    /**
     * 调用收银宝服务先查询是否存在,如果存在然后判断费率是否一致,一致则直接保存记录,不一致则进行修改
     * @param merchantId
     * @param feeMap
     */
    public void applyBankCardProduct(String merchantId, Map<String, Object> feeMap) throws CommonPubBizException {
        final MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        final String merchantSn = merchant.getSn();

        // 调用收银宝服务先查询是否存在
        final MchProductDTO productDTO = new MchProductDTO();
        productDTO.setMerchantSn(merchantSn);
        productDTO.setFeeMap(feeMap);
        productDTO.setReqTypeEnum(ProductReqTypeEnum.QUERY);
        productDTO.setProductTypeEnum(ProductTypeEnum.BANK_CARD);
        com.wosai.upay.merchant.contract.model.ContractResponse response = tongLianV2Service.bankCardProduct(productDTO);
        if(!response.isSuccess()){
            // 如果收银宝服务调用失败，则抛出异常
            throw new CommonPubBizException(response.getMessage());
        }
        final Map<String, Object> responseParam = response.getResponseParam();
        final ProductResponse productResponse = JSONObject.parseObject(JSONObject.toJSONString(responseParam), ProductResponse.class);
        //产品信息
        final String prodListString = productResponse.getProdlist();
        if(StringUtils.isBlank(prodListString)) {
            productDTO.setReqTypeEnum(ProductReqTypeEnum.ADD);
            productDTO.setProductTypeEnum(ProductTypeEnum.BANK_CARD);
            com.wosai.upay.merchant.contract.model.ContractResponse addResponse = tongLianV2Service.bankCardProduct(productDTO);
            if(!addResponse.isSuccess()){
                // 如果收银宝服务调用失败，则抛出异常
                throw new CommonPubBizException(addResponse.getMessage());
            }
            //添加标识,用于回调定位
            redisTemplate.opsForValue().set(merchantSn+tlPosDevCode, "1", 7, TimeUnit.DAYS);
            return;
        }
        final Optional<ProductInfo> optionalProductInfo = JSONArray.parseArray(prodListString, ProductInfo.class)
                .stream()
                .filter(product -> Objects.equals(product.getPid(), "P0001")
                        && Objects.equals(product.getMtrxcode(), "VSP001"))
                .findFirst();
        //不存在添加
        if(!optionalProductInfo.isPresent()) {
            productDTO.setReqTypeEnum(ProductReqTypeEnum.ADD);
            com.wosai.upay.merchant.contract.model.ContractResponse addResponse = tongLianV2Service.bankCardProduct(productDTO);
            if(!addResponse.isSuccess()){
                // 如果收银宝服务调用失败，则抛出异常
                throw new CommonPubBizException(addResponse.getMessage());
            }
            //添加标识,用于回调定位
            redisTemplate.opsForValue().set(merchantSn+tlPosDevCode, "1", 7, TimeUnit.DAYS);
        }else {
            //存在则需要判断费率是否一致
            //将当前费率映射成收银宝数据
            final Map<String, String> feeInfo = getFeeInfo(feeMap, FeeType.BANK_CARD);
            // 获取费率信息
            final String feeRate = feeInfo.get("feeRate");
            final String creditRate = feeInfo.get("creditRate");
            final String topLimit = feeInfo.get("topLimit");
            // 构建产品信息
            ProductInfo localProductInfo = buildProductInfo(
                    feeRate,
                    creditRate,
                    topLimit
            );

            final ProductInfo productInfo = optionalProductInfo.get();
            if(SybPOSFeeRateUtils.isFeeRateEqual(localProductInfo, productInfo)) {
                //费率一致,认为成功通知IOT组
                notifyIOT(merchant, "02", null,TONGLIAN_INTERNL_CARD_OPEN);
            }else{
                //费率不一致,则进行修改
                productDTO.setReqTypeEnum(ProductReqTypeEnum.EDIT);
                com.wosai.upay.merchant.contract.model.ContractResponse editResponse = tongLianV2Service.bankCardProduct(productDTO);
                if(!editResponse.isSuccess()){
                    // 如果收银宝服务调用失败，则抛出异常
                    throw new CommonPubBizException(editResponse.getMessage());
                }
                //添加标识,用于回调定位
                redisTemplate.opsForValue().set(merchantSn+tlPosDevCode, "1", 7, TimeUnit.DAYS);
            }
        }
    }


    /**
     * 调用收银宝服务先查询是否存在,如果存在然后判断费率是否一致,一致则直接保存记录,不一致则进行修改
     * @param merchantId
     * @param feeMap
     */
    public void applyForeignCardProduct(String merchantId, Map<String, Object> feeMap) throws CommonPubBizException {
        final MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        final String merchantSn = merchant.getSn();
        // 调用收银宝服务先查询是否存在
        final MchProductDTO productDTO = new MchProductDTO();
        productDTO.setMerchantSn(merchantSn);
        productDTO.setFeeMap(feeMap);
        productDTO.setReqTypeEnum(ProductReqTypeEnum.QUERY);
        productDTO.setProductTypeEnum(ProductTypeEnum.FOREIGN_CARD);
        com.wosai.upay.merchant.contract.model.ContractResponse response = tongLianV2Service.foreignCardProduct(productDTO);
        if(!response.isSuccess()){
            // 如果收银宝服务调用失败，则抛出异常
            throw new CommonPubBizException(response.getMessage());
        }
        final Map<String, Object> responseParam = response.getResponseParam();
        final ProductResponse productResponse = JSONObject.parseObject(JSONObject.toJSONString(responseParam), ProductResponse.class);
        //产品信息
        final String prodListString = productResponse.getProdlist();
        if(StringUtils.isBlank(prodListString)) {
            productDTO.setReqTypeEnum(ProductReqTypeEnum.ADD);
            com.wosai.upay.merchant.contract.model.ContractResponse addResponse = tongLianV2Service.foreignCardProduct(productDTO);
            if(!addResponse.isSuccess()){
                // 如果收银宝服务调用失败，则抛出异常
                throw new CommonPubBizException(addResponse.getMessage());
            }
            //添加标识,用于回调定位
            redisTemplate.opsForValue().set(merchantSn+tlForeignCardDevCode, "1", 7, TimeUnit.DAYS);
            return;
        }
        final Optional<ProductInfo> optionalProductInfo = JSONArray.parseArray(prodListString, ProductInfo.class)
                .stream()
                .filter(product -> Objects.equals(product.getPid(), "P0004")
                        && Objects.equals(product.getMtrxcode(), "VSP001"))
                .findFirst();
        //不存在添加
        if(!optionalProductInfo.isPresent()) {
            productDTO.setReqTypeEnum(ProductReqTypeEnum.ADD);
            com.wosai.upay.merchant.contract.model.ContractResponse addResponse = tongLianV2Service.foreignCardProduct(productDTO);
            if(!addResponse.isSuccess()){
                // 如果收银宝服务调用失败，则抛出异常
                throw new CommonPubBizException(addResponse.getMessage());
            }
            //添加标识,用于回调定位
            redisTemplate.opsForValue().set(merchantSn+tlForeignCardDevCode, "1", 7, TimeUnit.DAYS);
        }else {
            //存在则需要判断费率是否一致
            //将当前费率映射成收银宝数据
            final Map<String, String> feeInfo = getFeeInfo(feeMap, FeeType.FOREIGN_CARD);
            final String feeRate = feeInfo.get("feeRate");
            final String creditRate = feeInfo.get("creditRate");
            final String topLimit = feeInfo.get("topLimit");
            // 构建产品信息
            ProductInfo localProductInfo = buildProductInfo(
                    feeRate,
                    creditRate,
                    topLimit
            );

            final ProductInfo productInfo = optionalProductInfo.get();
            if(SybPOSFeeRateUtils.isFeeRateEqual(localProductInfo, productInfo)) {
                //费率一致,认为成功通知IOT组
                notifyIOT(merchant, "02", null,TONGLIAN_FOREIGN_CARD_OPEN);
            }else{
                //费率不一致,则进行修改
                productDTO.setReqTypeEnum(ProductReqTypeEnum.EDIT);
                productDTO.setProductTypeEnum(ProductTypeEnum.FOREIGN_CARD);
                com.wosai.upay.merchant.contract.model.ContractResponse editResponse = tongLianV2Service.foreignCardProduct(productDTO);
                if(!editResponse.isSuccess()){
                    // 如果收银宝服务调用失败，则抛出异常
                    throw new CommonPubBizException(editResponse.getMessage());
                }
                //添加标识,用于回调定位
                redisTemplate.opsForValue().set(merchantSn+tlForeignCardDevCode, "1", 7, TimeUnit.DAYS);
            }
        }
    }



    /**
     * 预授权产品操作
     * 调用收银宝服务先查询是否存在,如果存在然后判断费率是否一致,一致则直接保存记录,不一致则进行修改
     * @param merchantId
     */
    public void applyPreAuthProduct(String merchantId) throws CommonPubBizException{
        final Map<String, Object> feeMap = getT9FeeMap(merchantId);
        final MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        final String merchantSn = merchant.getSn();
        // 调用收银宝服务先查询是否存在
        final MchProductDTO productDTO = new MchProductDTO();
        productDTO.setMerchantSn(merchantSn);
        productDTO.setFeeMap(feeMap);
        productDTO.setReqTypeEnum(ProductReqTypeEnum.QUERY);
        productDTO.setProductTypeEnum(ProductTypeEnum.PRE_AUTH);
        com.wosai.upay.merchant.contract.model.ContractResponse response = tongLianV2Service.preAuthProduct(productDTO);
        if(!response.isSuccess()){
            // 如果收银宝服务调用失败，则抛出异常
            throw new CommonPubBizException(response.getMessage());
        }
        final Map<String, Object> responseParam = response.getResponseParam();
        final ProductResponse productResponse = JSONObject.parseObject(JSONObject.toJSONString(responseParam), ProductResponse.class);

        //产品信息
        final String prodListString = productResponse.getProdlist();
        if(StringUtils.isBlank(prodListString)) {
            productDTO.setReqTypeEnum(ProductReqTypeEnum.ADD);
            productDTO.setProductTypeEnum(ProductTypeEnum.PRE_AUTH);
            com.wosai.upay.merchant.contract.model.ContractResponse addResponse = tongLianV2Service.preAuthProduct(productDTO);
            if(!addResponse.isSuccess()){
                // 如果收银宝服务调用失败，则抛出异常
                throw new CommonPubBizException(addResponse.getMessage());
            }
            //添加标识,用于回调定位
            redisTemplate.opsForValue().set(merchantSn+tlPreAuthDevCode, "1", 7, TimeUnit.DAYS);
            return;
        }
        final Optional<ProductInfo> optionalProductInfo = JSONArray.parseArray(prodListString, ProductInfo.class)
                .stream()
                .filter(product -> Objects.equals(product.getPid(), "P0001")
                        && Objects.equals(product.getMtrxcode(), "VSP004"))
                .findFirst();
        //不存在添加
        if(!optionalProductInfo.isPresent()) {
            productDTO.setReqTypeEnum(ProductReqTypeEnum.ADD);
            productDTO.setProductTypeEnum(ProductTypeEnum.PRE_AUTH);
            com.wosai.upay.merchant.contract.model.ContractResponse addResponse = tongLianV2Service.preAuthProduct(productDTO);
            if(!addResponse.isSuccess()){
                // 如果收银宝服务调用失败，则抛出异常
                throw new CommonPubBizException(addResponse.getMessage());
            }
            //添加标识,用于回调定位
            redisTemplate.opsForValue().set(merchantSn+tlPreAuthDevCode, "1", 7, TimeUnit.DAYS);
        }else {
            //存在则需要判断费率是否一致
            //将当前费率映射成收银宝数据
            final Map<String, String> feeInfo = getFeeInfo(feeMap, FeeType.PRE_AUTH);
            final String feeRate = feeInfo.get("feeRate");
            final String creditRate = feeInfo.get("creditRate");
            final String topLimit = feeInfo.get("topLimit");
            // 构建产品信息
            ProductInfo localProductInfo = buildProductInfo(
                    feeRate,
                    creditRate,
                    topLimit
            );

            final ProductInfo productInfo = optionalProductInfo.get();
            if(SybPOSFeeRateUtils.isFeeRateEqual(localProductInfo, productInfo)) {
                //费率一致,认为成功通知IOT组
                notifyIOT(merchant, "02", null,TONGLIAN_CARD_PRE_AUTH);
                return;
            }else{
                //费率不一致,则进行修改
                productDTO.setReqTypeEnum(ProductReqTypeEnum.EDIT);
                productDTO.setProductTypeEnum(ProductTypeEnum.PRE_AUTH);
                com.wosai.upay.merchant.contract.model.ContractResponse editResponse = tongLianV2Service.preAuthProduct(productDTO);
                if(!editResponse.isSuccess()){
                    // 如果收银宝服务调用失败，则抛出异常
                    throw new CommonPubBizException(editResponse.getMessage());
                }
                //添加标识,用于回调定位
                redisTemplate.opsForValue().set(merchantSn+tlPreAuthDevCode, "1", 7, TimeUnit.DAYS);
            }
        }
    }

    /**
     * 获取费率信息
     */
    private Map<String, String> getFeeInfo(Map<String, Object> feeMap, FeeType feeType) {
        Map<String, String> result = new HashMap<>();

        switch (feeType) {
            case BANK_CARD:
            case PRE_AUTH:
                Map<String, String> debit = (Map<String, String>) MapUtils.getMap(feeMap, "debit");
                Map<String, String> credit = (Map<String, String>) MapUtils.getMap(feeMap, "credit");
                result.put("feeRate",MapUtils.getString(debit, "fee"));
                result.put("creditRate", MapUtils.getString(credit, "fee"));
                result.put("topLimit", MapUtils.getString(debit, "max"));
                break;

            case FOREIGN_CARD:
                Map dccFeeMap = MapUtils.getMap(feeMap, "dcc");
                Map edcFeeMap = MapUtils.getMap(feeMap, "edc");
                result.put("feeRate", MapUtils.getString(edcFeeMap, "fee"));
                result.put("creditRate", MapUtils.getString(dccFeeMap, "fee"));
                result.put("topLimit", null);
                break;
        }

        return result;
    }

    /**
     * 收银宝添加产品回调
     * @param merchantSn 商户号
     * @param param 回调原始信息
     */
    public Boolean t9ProductCallback(String merchantSn, TongLianV2Controller.AuditNotifyRequest param) {
        log.info("tlv2 t9ProductCallback :{},{}", merchantSn,JSONObject.toJSONString(param));
        final MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        //02-审核通过,03-审核不通过
        final String auditstatus = param.getAuditstatus();
        final String errmsg = StringUtils.defaultIfBlank(param.getErrmsg(), "审核不通过");
        //优先查询银行卡刷卡是否开通
        String posKey = merchantSn+tlPosDevCode;
        if(redisTemplate.hasKey(posKey)){
            redisTemplate.delete(posKey);
            notifyIOT(merchant, auditstatus, errmsg,TONGLIAN_INTERNL_CARD_OPEN);
            return Boolean.TRUE;
        }
        String foreignCardKey = merchantSn+tlForeignCardDevCode;
        if(redisTemplate.hasKey(foreignCardKey)) {
            redisTemplate.delete(foreignCardKey);
            notifyIOT(merchant, auditstatus, errmsg, TONGLIAN_FOREIGN_CARD_OPEN);
            return Boolean.TRUE;
        }

        String preAuthKey = merchantSn+tlPreAuthDevCode;
        if(redisTemplate.hasKey(preAuthKey)) {
            redisTemplate.delete(preAuthKey);
            notifyIOT(merchant, auditstatus, errmsg, TONGLIAN_CARD_PRE_AUTH);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 通知IOT组开通结果
     * @param merchant  商户信息
     * @param auditstatus 02-审核通过,03-审核不通过
     * @param errmsg 03-审核不通过时传对应的错误信息
     * @param bizType 通知类型
     */
    private void notifyIOT(MerchantInfo merchant, String auditstatus, String errmsg, Integer bizType) {
        final CallBackRequest callBackRequest = new CallBackRequest();
        callBackRequest.setMerchantId(merchant.getId());
        callBackRequest.setBizType(bizType);
        if (Objects.equals(auditstatus, "02")) {
            callBackRequest.setAuditResult(1);
            iotBusinessOpenService.resultCallBack(callBackRequest);
        } else if (Objects.equals(auditstatus, "03")) {
            callBackRequest.setAuditResult(0);
            callBackRequest.setErrMessage(errmsg);
            iotBusinessOpenService.resultCallBack(callBackRequest);
        }
    }



    /**
     * 收银宝T9刷卡终端回调
     * @param merchantSn 商户号
     * @param param 回调原始信息
     */
    public Boolean t9TermCallback(String merchantSn, TongLianV2Controller.AuditNotifyRequest param) {
        log.info("tlv2 t9TermCallback :{},{}", merchantSn,JSONObject.toJSONString(param));
        Boolean termCallback = Boolean.FALSE;
        //02-审核通过,03-审核不通过
        final String auditstatus = param.getAuditstatus();
        final List<ProviderTerminalBindConfigDO> terminalBindConfigDOS = providerTerminalBindConfigDAO
                .listByMerchantSnProviderPayWay(merchantSn, PayParamsModel.PROVIDER_TONGLIAN_V2, PaywayEnum.BANK_CARD.getValue());
        final Optional<ProviderTerminalBindConfigDO> pending = terminalBindConfigDOS.stream()
                .filter(config -> Objects.equals(config.getStatus(), ProviderTerminalBindConfigStatus.PENDING.getStatus()))
                .findFirst();
        //不存在不需要处理
        if(!pending.isPresent()) {
            return termCallback;
        }
        final ProviderTerminalBindConfigDO bindConfigDO = pending.get();
        if(Objects.equals(auditstatus, "02")) {
            ProviderTerminalBindConfigDO newBindConfigDO = new ProviderTerminalBindConfigDO();
            //进行中的任务变为成功
            newBindConfigDO.setId(bindConfigDO.getId());
            newBindConfigDO.setStatus(ProviderTerminalBindConfigStatus.BIND_SUCCESS.getStatus());
            newBindConfigDO.setResult("绑定成功");
            providerTerminalBindConfigDAO.updateByPrimaryKeySelective(newBindConfigDO);
            termCallback = Boolean.TRUE;
        }else if (Objects.equals(auditstatus, "03")){
            //进行中的任务变为失败
            ProviderTerminalBindConfigDO newBindConfigDO = new ProviderTerminalBindConfigDO();
            newBindConfigDO.setId(bindConfigDO.getId());
            newBindConfigDO.setStatus(ProviderTerminalBindConfigStatus.BIND_FAIL.getStatus());
            newBindConfigDO.setResult("绑定失败:"+ BeanUtil.getPropString(param,"errmsg","审核失败"));
            providerTerminalBindConfigDAO.updateByPrimaryKeySelective(newBindConfigDO);
            termCallback = Boolean.TRUE;
        }
        return termCallback;
    }

    /**
     * 构建产品信息
     */
    private ProductInfo buildProductInfo(String feeRate, String creditRate, String topLimit) {
        return ProductInfo.builder()
                .feerate(shiftDecimalPoint(feeRate))
                .creditrate(shiftDecimalPoint(creditRate))
                .toplimit(topLimit)
                .build();
    }


    public static String shiftDecimalPoint(String numberStr) {
        BigDecimal number = new BigDecimal(numberStr);
        BigDecimal shifted = number.multiply(new BigDecimal("10"));
        shifted = shifted.stripTrailingZeros();
        // 转换为字符串，处理可能的科学计数法
        String result = shifted.toPlainString();
        // 如果包含小数点且小数部分为零，则去除小数部分
        if (result.contains(".") && result.endsWith(".0")) {
            result = result.substring(0, result.indexOf(".0"));
        }
        return result;
    }

    @Override
    public void closePreAuth(String merchantSn) {
        final PreAuthApply preAuthApply = preAuthApplyMapper.selectPreAuthApply(merchantSn, tlPreAuthDevCode);
        Optional.ofNullable(preAuthApply)
                .filter(apply -> Objects.equals(apply.getStatus(), PreAuthApplyConstant.Status.SUCCESS))
                .ifPresent(apply -> preAuthApplyMapper.deleteByPrimaryKey(apply.getId()));

        if (merchantSn != null && tlPreAuthDevCode != null) {
            directStatusDAO.deleteByMerchantSnAndDevCode(merchantSn, tlPreAuthDevCode);
        }

        final MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        final CallBackRequest callBackRequest = new CallBackRequest();
        callBackRequest.setBizType(TONGLIAN_CARD_PRE_AUTH);
        callBackRequest.setMerchantId(merchant.getId());
        callBackRequest.setAuditResult(2);
        callBackRequest.setErrMessage("注销预授权");
        try {
            iotBusinessOpenService.resultCallBack(callBackRequest);
        } catch (Exception e) {
            log.warn("closePreAuth resultCallBack merchantSn :{},error",merchantSn,e);
        }

    }
}
