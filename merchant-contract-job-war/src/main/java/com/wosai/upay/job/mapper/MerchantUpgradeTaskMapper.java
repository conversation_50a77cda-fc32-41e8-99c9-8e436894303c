package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.MerchantUpgradeTask;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface MerchantUpgradeTaskMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MerchantUpgradeTask record);

    int insertSelective(MerchantUpgradeTask record);

    List<MerchantUpgradeTask> selectWaitingTask();

    @Select("select * from merchant_upgrade_task where merchant_sn=#{merchantSn} and type = #{type}")
    List<MerchantUpgradeTask> selectByMerchantSnAndType(@Param("merchantSn") String merchantSn,@Param("type") Integer type);

    MerchantUpgradeTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantUpgradeTask record);

    int updateByPrimaryKey(MerchantUpgradeTask record);
}