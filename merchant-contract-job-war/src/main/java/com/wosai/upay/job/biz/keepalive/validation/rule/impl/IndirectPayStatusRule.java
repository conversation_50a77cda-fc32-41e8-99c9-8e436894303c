package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.trade.service.TradeStateService;
import com.wosai.trade.service.result.TradeStateResult;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 间连扫码收款权限规则
 *
 * <AUTHOR>
 * @date 2025/8/28
 */
@Component
public class IndirectPayStatusRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private TradeStateService tradeStateService;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            TradeStateResult tradeStateResult = tradeStateService.queryStateByBizIdAndMerchantId(context.getMerchantId(), 1);
            if (tradeStateResult == null || !tradeStateResult.getState()) {
                return createFailureResult(
                        "商户间连扫码收款权限不正常，不允许执行", "MERCHANT_INDIRECT_PAY_STATUS_BLOCKED");
            }
            return createSuccessResult("商户间连扫码收款权限正常，通过检查");

        } catch (Exception e) {
            logger.error("执行间连扫码收款权限规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.MERCHANT_INDIRECT_PAY_STATUS;
    }
}
