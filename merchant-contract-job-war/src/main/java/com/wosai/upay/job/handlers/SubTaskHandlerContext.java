package com.wosai.upay.job.handlers;

import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-04-08
 */
@Component
public class SubTaskHandlerContext {

    @Autowired
    private List<AbstractSubTaskHandler> handlers;


    public void handle(ContractTask task, ContractSubTask subTask) throws Exception {
        AbstractSubTaskHandler handler = getHandler(task, subTask);
        if (handler != null) {
            handler.handle(task, subTask);
        }
    }

    private AbstractSubTaskHandler getHandler(ContractTask task, ContractSubTask subTask) {
        for (AbstractSubTaskHandler handler : handlers) {
            if (handler.supports(task, subTask)) {
                return handler;
            }
        }
        return null;
    }


}
