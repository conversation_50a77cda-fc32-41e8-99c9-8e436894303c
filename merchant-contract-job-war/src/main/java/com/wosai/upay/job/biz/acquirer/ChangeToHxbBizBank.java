package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ConfigStatusEnum;
import com.shouqianba.cua.enums.status.UpdateStatusEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.request.CancelFeeRateRequest;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.job.biz.bankDirect.HxbImportBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.bo.HxParamsExtraBO;
import com.wosai.upay.job.refactor.model.constant.HxTradeParamsPropertyConstant;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.AuthStatusEnum;
import com.wosai.upay.job.refactor.model.enums.ConstractRuleOfAcquirerSelfEnum;
import com.wosai.upay.job.refactor.model.enums.Deleted;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.merchant.contract.model.huaxia.request.HXMerchantAdd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 收单机构切换到华夏
 *
 * <AUTHOR>
 * @date 2020-04-26
 */
@Component("hxb-AcquirerChangeBiz")
@Slf4j
public class ChangeToHxbBizBank extends AbstractBankDirectAcquirerChangeBiz {

    @Value("${hxb_dev_code}")
    public  String hxbDevCode;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private ContractSubTaskDAO contractSubTaskDAO;

    @Autowired
    HxbImportBiz hxbImportBiz;

    @Value("${hxb_multi_trade}")
    private Long hxbMultiTrade;

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_HXB.getValue();
    }


    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_HXB_RULE_GROUP;
    }

    @Override
    protected List<MerchantProviderParams> getDefaultChangeParams(McAcquirerChange change) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderIn(Arrays.asList(getProviderCode(change.getTarget_acquirer())))
                .andPaywayNotIn(Lists.newArrayList(PaywayEnum.ACQUIRER.getValue(), PaywayEnum.DCEP.getValue()))
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        return paramsMapper.selectByExampleWithBLOBs(example);
    }



    @Override
    public String getDevCode(String acquirer) {
        return hxbDevCode;
    }

    @Override
    protected long getDefaultComboId(String acquirer) {
        return 179L;
    }


    @Override
    protected void applyCombo(McAcquirerChange change) {
        Map ChangeExtra = CommonUtil.string2Map(change.getExtra());
        String feeRate = BeanUtil.getPropString(ChangeExtra, "hxb_fee_rate");
        if (WosaiStringUtils.isNotEmpty(feeRate)) {
            Map<String, String> applyFeeRateMap = CollectionUtil.hashMap(
                    String.valueOf(PaywayEnum.ALIPAY.getValue()), feeRate,
                    String.valueOf(PaywayEnum.WEIXIN.getValue()), feeRate,
                    String.valueOf(PaywayEnum.UNIONPAY.getValue()), feeRate
            );
            ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                    .setMerchantSn(change.getMerchant_sn())
                    .setTradeComboId(getDefaultComboId(change.getTarget_acquirer()))
                    .setAuditSn("银行业务开通成功设置费率")
                    .setApplyPartialPayway(Boolean.TRUE)
                    .setApplyFeeRateMap(applyFeeRateMap);
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
            return;
        }

        Map combo = new HashMap();
        // 检查历史切换成功记录
        McAcquirerChange latestSuccessApply = changeDao.getLatestSuccessApply(change.getMerchant_sn(), change.getTarget_acquirer());
        if (Objects.nonNull(latestSuccessApply)) {
            Map extra = CommonUtil.string2Map(latestSuccessApply.getExtra());
            combo = WosaiMapUtils.getMap(extra, "comboSnapshot");
        }

        if (WosaiMapUtils.isEmpty(combo)) {
            BankDirectApply apply = bankDirectApplyMapper.getApplyBySnAndDevCode(change.getMerchant_sn(), getDevCode(change.getTarget_acquirer()));
            if (Objects.nonNull(apply)) {
                combo = CommonUtil.string2Map(apply.getForm_body());
            }
        }

        long tradeComboId = BeanUtil.getPropLong(combo, "trade_combo_id");
        if (Objects.equals(tradeComboId, 0L)) {
            tradeComboId = getDefaultComboId(change.getTarget_acquirer());
        }
        List<Map<String, Object>> config = JSONObject.parseObject(BeanUtil.getPropString(combo, "merchant_config"), List.class);

        Map<String, String> applyFeeRateMap = config.stream().collect(Collectors.toMap(x -> BeanUtil.getPropString(x, "payway"), x -> BeanUtil.getPropString(x, "rate")));
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(change.getMerchant_sn())
                .setTradeComboId(tradeComboId)
                .setAuditSn("银行业务开通成功设置费率")
                .setApplyPartialPayway(Boolean.TRUE)
                .setApplyFeeRateMap(applyFeeRateMap);
        feeRateService.applyFeeRateOne(applyFeeRateRequest);

    }


    /**
     * 配置数字货币,上送数币交易参数
     *
     * @param merchantSn 商户号
     */
    @Override
    public void configDigitalCurrency(String merchantSn) {
        Optional<ContractSubTaskDO> subTaskDOOptional = contractSubTaskDAO.getByMerchantSnAndRule(merchantSn, ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue(),
                ConstractRuleOfAcquirerSelfEnum.HXB.getText(), PaywayEnum.ACQUIRER.getValue());
        if (!subTaskDOOptional.isPresent()) {
            log.error("商户 {} 切华夏银行收单机构 开通数币受理 subtask进件记录没查询到", merchantSn);
            return;
        }
        ContractSubTaskDO contractSubTaskDO = subTaskDOOptional.get();

        Optional<MerchantProviderParamsDO> merchantProviderParamsDOOptional = merchantProviderParamsDAO
                .getMerChantProviderParams(merchantSn, ChannelEnum.HX_BANK.getValue(), PaywayEnum.ACQUIRER.getValue());
        if (!merchantProviderParamsDOOptional.isPresent()) {
            log.error("商户未查询到交易参数, merchantSn= {}, channel= {}, payWay= {}", merchantSn, ChannelEnum.HX_BANK.getValue(), PaywayEnum.ACQUIRER.getValue());
            return;
        }
        MerchantProviderParamsDO merchantProviderParamsDO = merchantProviderParamsDOOptional.get();

        Map<?, ?> merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map<?, ?> hxExistedDigitalCurrencyConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.DCEP.getValue());
        Map<String, String> resultMap = JSON.parseObject(merchantProviderParamsDO.getExtra(), new TypeReference<Map<String, String>>() {});
        if (isParametersNotLogic(merchantSn, hxExistedDigitalCurrencyConfig, resultMap, merchantProviderParamsDO.getId())) {
            return;
        }
        String digitalCurrencyParam = resultMap.get(String.valueOf(PaywayEnum.DCEP.getValue()));
        HxParamsExtraBO hxParamsExtraBO = JSON.parseObject(digitalCurrencyParam, HxParamsExtraBO.class);
        HXMerchantAdd hxMerchantReqParam= JSON.parseObject(contractSubTaskDO.getRequestBody(), HXMerchantAdd.class);
        if (Objects.isNull(hxParamsExtraBO)
                || StringUtils.isBlank(hxParamsExtraBO.getPayMerchantId())
                || Objects.isNull(hxParamsExtraBO.getTrade())
                || Objects.isNull(hxParamsExtraBO.getTrade().getHxBankTradeParams())) {
            log.error("商户{}缺少华夏银行数币交易参数或者数币行业参数, 交易参数id={}", merchantSn, merchantProviderParamsDO.getId());
            return;
        }
        HashMap<String, Object> tradeParamsMap = getTradeParamsMap(hxParamsExtraBO, merchantProviderParamsDO.getPayMerchantId());
        HashMap<String, Object> digitalCurrencyConfigMap = getDigitalCurrencyConfigMap(merchantId);
        writeHxDigitalCurrencyParameters(merchantSn, hxExistedDigitalCurrencyConfig, digitalCurrencyConfigMap, tradeParamsMap);
        saveHxDigitalCurrencyParameters(merchantSn, hxParamsExtraBO);
    }

    private void saveHxDigitalCurrencyParameters(String merchantSn, HxParamsExtraBO hxParamsExtraBO) {
        // 因为目前还没有华夏-数币的规则组,暂时写死,待后续优化
        String ruleGroupId = "hxb";
        String contractRule = "hxb-1028-23";
        String channelNo = "hxb";
        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setId(UUID.randomUUID().toString());
        merchantProviderParamsDO.setMerchantSn(merchantSn);
        merchantProviderParamsDO.setOutMerchantSn(merchantSn);
        merchantProviderParamsDO.setProvider(ProviderEnum.PROVIDER_HXB.getValue());
        // 数币支付源商户号
        merchantProviderParamsDO.setPayMerchantId(hxParamsExtraBO.getPayMerchantId());
        merchantProviderParamsDO.setParentMerchantId(hxParamsExtraBO.getParentMerchantId());
        merchantProviderParamsDO.setProviderMerchantId(hxParamsExtraBO.getProviderMerchantId());
        merchantProviderParamsDO.setRuleGroupId(ruleGroupId);
        merchantProviderParamsDO.setContractRule(contractRule);
        merchantProviderParamsDO.setChannelNo(channelNo);
        merchantProviderParamsDO.setExtra(JSON.toJSONString(hxParamsExtraBO));
        merchantProviderParamsDO.setUpdateStatus(UpdateStatusEnum.SUCCESS.getValue());
        merchantProviderParamsDO.setPayway(PaywayEnum.DCEP.getValue());
        merchantProviderParamsDO.setParamsConfigStatus(ConfigStatusEnum.NOT_REQUIRE_CONFIG.getValue());
        merchantProviderParamsDO.setAuthStatus(AuthStatusEnum.YES.getValue());
        merchantProviderParamsDO.setStatus(UseStatusEnum.IN_USE.getValue());
        merchantProviderParamsDO.setCtime(System.currentTimeMillis());
        merchantProviderParamsDO.setMtime(System.currentTimeMillis());
        merchantProviderParamsDO.setDeleted(Deleted.NO_DELETED.getValue());
        Optional<MerchantProviderParamsDO> merchantProviderParamsOptional = merchantProviderParamsDAO
                .getMerChantProviderParams(merchantProviderParamsDO.getMerchantSn(), merchantProviderParamsDO.getRuleGroupId(),
                        merchantProviderParamsDO.getContractRule(), merchantProviderParamsDO.getChannelNo());
        if (merchantProviderParamsOptional.isPresent()) {
            merchantProviderParamsDO.setId(merchantProviderParamsOptional.get().getId());
            merchantProviderParamsDO.setCtime(null);
            merchantProviderParamsDAO.updateByPrimaryKey(merchantProviderParamsDO);
            return;
        }
        merchantProviderParamsDAO.saveMerchantParameters(merchantProviderParamsDO);
    }

    private boolean isParametersNotLogic(String merchantSn, Map<?, ?> hxDigitalCurrencyConfig, Map<String, String> resultMap, String merchantProviderParamsId) {
        if (MapUtils.isNotEmpty(hxDigitalCurrencyConfig) &&
            Objects.equals(BeanUtil.getPropString(hxDigitalCurrencyConfig, MerchantConfig.B2C_AGENT_NAME),HxTradeParamsPropertyConstant.AGENT_NAME_KEY )) {
            log.warn("商户{}切华夏银行收单机构开通数币受理上送交易参数时,交易参数已存在", merchantSn);
            return true;
        }
        if (Objects.isNull(resultMap)) {
            return true;
        }
        if (!resultMap.containsKey(String.valueOf(PaywayEnum.DCEP.getValue()))) {
            log.error("商户{}未查询到华夏银行数币交易参数, 交易参数id={}", merchantSn, merchantProviderParamsId);
            return true;
        }
        return false;
    }

    private void writeHxDigitalCurrencyParameters(String merchantSn, Map<?, ?> hxExistedDigitalCurrencyConfig,
                                                  HashMap<String, Object> digitalCurrencyConfigMap,
                                                  HashMap<String, Object> tradeParamsMap) {
        if (MapUtils.isEmpty(hxExistedDigitalCurrencyConfig)) {
            digitalCurrencyConfigMap.put(MerchantConfig.PARAMS, CollectionUtil.hashMap(HxTradeParamsPropertyConstant.TRADE_PARAMS_KEY, tradeParamsMap));
            tradeConfigService.createMerchantConfig(digitalCurrencyConfigMap);
        } else {
            Map<String, Object> paramsMap = org.apache.commons.collections.MapUtils.getMap(hxExistedDigitalCurrencyConfig, MerchantConfig.PARAMS);
            if (Objects.isNull(paramsMap)) {
                paramsMap = Maps.newHashMap();
            }
            paramsMap.put(HxTradeParamsPropertyConstant.TRADE_PARAMS_KEY, tradeParamsMap);
            digitalCurrencyConfigMap.put(MerchantConfig.PARAMS, paramsMap);
            digitalCurrencyConfigMap.put(DaoConstants.ID, BeanUtil.getPropString(hxExistedDigitalCurrencyConfig, DaoConstants.ID));
            tradeConfigService.updateMerchantConfig(digitalCurrencyConfigMap);
        }
        supportService.removeCachedParams(merchantSn);
    }

    @NotNull
    private HashMap<String, Object> getTradeParamsMap(HxParamsExtraBO hxParamsExtraBO, String acquirerPayMerchantId) {
        HashMap<String, Object> tradeParamsMap = Maps.newHashMap();
        tradeParamsMap.put(HxTradeParamsPropertyConstant.DEVELOPMENT_APP_ID_KEY, hxParamsExtraBO.getTrade().getHxBankTradeParams().getDevelopAppId());
        tradeParamsMap.put(HxTradeParamsPropertyConstant.PROVIDER_MERCHANT_ID_KEY, acquirerPayMerchantId);
        tradeParamsMap.put(HxTradeParamsPropertyConstant.PROVIDER_SERVICE_ID_KEY, hxParamsExtraBO.getTrade().getHxBankTradeParams().getProviderServiceId());
        return tradeParamsMap;
    }

    @NotNull
    private HashMap<String, Object> getDigitalCurrencyConfigMap(String merchantId) {
        HashMap<String, Object> digitalCurrencyConfigMap = Maps.newHashMap();
        digitalCurrencyConfigMap.put(MerchantConfig.PAYWAY, PaywayEnum.DCEP.getValue());
        digitalCurrencyConfigMap.put(MerchantConfig.MERCHANT_ID, merchantId);
        digitalCurrencyConfigMap.put(MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED);
        digitalCurrencyConfigMap.put(MerchantConfig.B2C_AGENT_NAME, HxTradeParamsPropertyConstant.AGENT_NAME_KEY);
        digitalCurrencyConfigMap.put(MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_HXB.getValue());
        digitalCurrencyConfigMap.put(MerchantConfig.B2C_FEE_RATE, "0.0");
        digitalCurrencyConfigMap.put(MerchantConfig.B2C_FORMAL, false);
        digitalCurrencyConfigMap.put(MerchantConfig.C2B_FORMAL, false);
        digitalCurrencyConfigMap.put(MerchantConfig.WAP_FORMAL, false);
        digitalCurrencyConfigMap.put(MerchantConfig.MINI_FORMAL, false);
        return digitalCurrencyConfigMap;
    }

    @Override
    protected void targetAcquirerPostBiz(McAcquirerChange change) {
        super.targetAcquirerPostBiz(change);
        //是否存在华夏银行线下导入的多业务
        hxbImportBiz.afterHxbChangeParams(change.getMerchant_sn());
    }

    @Override
    protected void sourceAcquirerPostBiz(McAcquirerChange change) {
        super.sourceAcquirerPostBiz(change);
        String merchantSn = change.getMerchant_sn();
        //取消华夏银行线下的套餐
        CancelFeeRateRequest rateRequest = new CancelFeeRateRequest();
        rateRequest.setMerchantSn(merchantSn);
        rateRequest.setTradeComboId(hxbMultiTrade);
        rateRequest.setAuditSn("业务管理:取消华夏银行线下导入设置的套餐");
        try {
            feeRateService.cancelFeeRate(rateRequest);
            supportService.removeCachedParams(merchantSn);
        } catch (Exception exception) {
            log.error("取消华夏银行线下导入设置的套餐异常,merchantSn:{},comboId:{},异常信息:{}",merchantSn,hxbMultiTrade,exception);
        }
    }
}
