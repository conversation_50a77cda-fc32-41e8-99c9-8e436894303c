package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shouqianba.workflow.bean.CallBackBean;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.job.Constants.ApproveConstant;
import com.wosai.upay.job.adapter.apollo.BatchTemplateApolloConfig;
import com.wosai.upay.job.handlers.BatchContext;
import com.wosai.upay.job.handlers.BatchProcessingHandler;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.dto.BatchProcessingDTO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * xxl_job_desc: 根据模板处理批量任务
 * <AUTHOR>
 * @date 2025/4/11
 */
@Slf4j
@Component("BatchProcessTemplateJobHandler")
public class BatchProcessTemplateJobHandler extends AbstractMcBatchTaskJobHandler {

    @Autowired
    private BatchProcessingHandler batchProcessingHandler;
    @Autowired
    private BatchTemplateApolloConfig apolloConfig;

    @Override
    public String getLockKey() {
        return "BatchProcessTemplateJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        List<Map> batchTemlateList = apolloConfig.getBatchTemplates();
        for (Map batchTemplate : batchTemlateList) {
            int type = BeanUtil.getPropInt(batchTemplate, "batchType");
            // model对象路径，默认为com.wosai.upay.job.model
            String templateModel = "com.wosai.upay.job.model." + BeanUtil.getPropString(batchTemplate, "templateModel");
            McBatchTaskExample mcBatchTaskExample = new McBatchTaskExample();
            mcBatchTaskExample.or().andStatusEqualTo(0).andEffect_timeLessThanOrEqualTo(new Date()).andTypeEqualTo(type);
            List<McBatchTask> list = mcBatchTaskMapper.selectByExampleWithBLOBs(mcBatchTaskExample);
            list.forEach(mcBatchTask -> {
                Map extra = Maps.newHashMap();
                try {
                    final String payload = mcBatchTask.getPayload();
                    extra = CommonUtil.string2Map(payload);
                    final BatchProcessingDTO batchProcessingDto = objectMapper.convertValue(MapUtils.getMap(extra, ApproveConstant.APPROVE_INFO), BatchProcessingDTO.class);
                    final String attachmentUrl = batchProcessingDto.getAttachmentUrls().get(0);
                    final List<Object> batchProcessingExcelList = excelUtil.getExcelInfoList(attachmentUrl, Class.forName(templateModel).newInstance());
                    if (WosaiCollectionUtils.isEmpty(batchProcessingExcelList)) {
                        throw new CommonPubBizException("批量处理条数不能为空");
                    }
                    // 文件最大处理数据量限制
                    int batchProcessMaxCount = BeanUtil.getPropInt(batchTemplate, "batchProcessMaxCount");
                    if (batchProcessMaxCount > 0) {
                        Optional.of(batchProcessingExcelList).filter(collection -> collection.size() >= batchProcessMaxCount).ifPresent(t -> {
                            throw new CommonPubBizException(String.format("批量查询一次不能超过%s条", batchProcessMaxCount));
                        });
                    }
                    BatchContext batchContext = new BatchContext();
                    batchContext.setBatchTemplate(batchTemplate);
                    batchContext.setBatchProcessingDTO(batchProcessingDto);
                    // 预处理
                    batchProcessingHandler.beforeHandlerEvent(batchContext);
                    // 根据模板处理任务
                    final List<CompletableFuture<Object>> futureList = batchProcessingExcelList.parallelStream().map(model ->
                            CompletableFuture.supplyAsync(() -> batchProcessingHandler.handlerEvent(model, batchContext), batchScheduleExecutorThreadPoolTaskExecutor)).collect(Collectors.toList());
                    // 后处理
                    batchProcessingHandler.afterHandlerEvent(batchContext);
                    //获取结果
                    final List<Object> batchResult = futureList.stream().map(CompletableFuture::join).collect(Collectors.toList());
                    //将postalCardResult集合变成文件上传到oss中
                    final String url = excelUtil.uploadToOss(batchResult, BASE_DIR);
                    //修改状态并将结果存储
                    extra.put(ApproveConstant.LAST_ATTACHMENT_URL, url);
                    mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(2).setId(mcBatchTask.getId()).setPayload(JSONObject.toJSONString(extra)).setResult("处理成功:" + url));
                    //回调审批中心
                    callBackBatchResult(extra, "处理结果请使用浏览器下载链接对应的Excel:" + url, AUDIT_EXECUTE_SUCCESS);
                    logService.updateTaskApplyLog(CollectionUtil.hashMap(DaoConstants.ID, mcBatchTask.getTask_apply_log_id(), TaskApplyLog.APPLY_STATUS, APPLY_STATUS_EXCUTE_SUCCESS, TaskApplyLog.APPLY_RESULT, JSON.toJSONString(CollectionUtil.hashMap("result", "处理结果请使用浏览器下载链接对应的Excel:" + url))));
                } catch (Exception e) {
                    log.error("BatchProcessTemplateJobHandler error mc_batch_task {}  exception", mcBatchTask.getId(), e);
                    mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(3).setId(mcBatchTask.getId()).setResult(ExceptionUtil.getThrowableMsg(e)));
                    //回调审批中心
                    callBackBatchResult(extra, ExceptionUtil.getThrowableMsg(e), AUDIT_EXECUTE_FAIL);
                    logService.updateTaskApplyLog(CollectionUtil.hashMap(DaoConstants.ID, mcBatchTask.getTask_apply_log_id(), TaskApplyLog.APPLY_STATUS, APPLY_STATUS_EXCUTE_FAILURE, TaskApplyLog.APPLY_RESULT, JSON.toJSONString(CollectionUtil.hashMap("result", ExceptionUtil.getThrowableMsg(e)))));
                }
            });
        }
    }

    private void callBackBatchResult(Map extra, String message, Integer resultType) {
        final BatchProcessingDTO dto = objectMapper.convertValue(MapUtils.getMap(extra, ApproveConstant.APPROVE_INFO), BatchProcessingDTO.class);
        final CallBackBean backBean = dto.getCallBackBean();
        // 由审批中心，迁移到spa，此处可能为null
        if (backBean == null) {
            return;
        }
        backBean.setResultType(resultType);
        backBean.setMessage(message);
        callBackService.addComment(backBean);
    }
}
