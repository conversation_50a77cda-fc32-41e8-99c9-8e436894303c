package com.wosai.upay.job.consumer;

import com.wosai.databus.LogEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.jackson.EventAwareJackson2PersistenceHelper;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.providers.ProviderFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.ByteBuffer;

/**
 * @Description:数据总线消费者基类
 * <AUTHOR>
 * Date 2019/12/24 4:05 下午
 **/
@Slf4j
public abstract class AbstractDataBusConsumer {

    @Autowired
    protected ProviderTerminalBiz providerTerminalBiz;

    @Autowired
    protected ProviderFactory providerFactory;

    protected EventAwareJackson2PersistenceHelper persistenceHelper = new EventAwareJackson2PersistenceHelper();


    public void handle(ConsumerRecord<String, GenericRecord> record) {
        GenericRecord datum = record.value();
        if (datum == null) {
            log.error("{} getValue null", record);
            return;
        }
        AbstractEvent event;
        try {
            ByteBuffer buffer = (ByteBuffer) datum.get(LogEntry.EVENT);
            event = (AbstractEvent) persistenceHelper.fromJsonBytes(buffer.array(), AbstractEvent.class);
            event.setSeq((Long) datum.get(LogEntry.SEQ));
            event.setTimestamp((Long) datum.get(LogEntry.TIMESTAMP));
        } catch (Throwable t) {
            log.error("{} dataBus consume error", record, t);
            return;
        }
        try {
            doHandleEvent(event);
        } catch (Throwable t) {
            log.error("{} dataBus consume error2", record, t);
            throw t;
        }
    }

    /**
     * 各类event处理
     */
    protected void doHandleEvent(AbstractEvent event) {
    }


}
