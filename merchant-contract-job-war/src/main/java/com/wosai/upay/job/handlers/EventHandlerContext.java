package com.wosai.upay.job.handlers;

import com.wosai.upay.job.model.ContractEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-08
 */
@Component
public class EventHandlerContext {

    @Autowired
    private List<AbstractEventHandler> handlers;


    public void handle(ContractEvent event) throws Exception {
        AbstractEventHandler handler = getHandler(event);
        if (handler != null) {
            handler.handle(event);
        }
    }

    private AbstractEventHandler getHandler(ContractEvent event) {
        for (AbstractEventHandler handler : handlers) {
            if (handler.supports(event)) {
                return handler;
            }
        }
        return null;
    }
}
