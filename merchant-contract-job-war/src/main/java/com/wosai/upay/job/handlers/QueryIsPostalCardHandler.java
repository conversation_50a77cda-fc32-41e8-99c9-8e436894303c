package com.wosai.upay.job.handlers;

import com.wosai.upay.job.model.QueryIsPostalCardExcel;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.model.psbc.bo.ShopAccountTypeCheckBO;
import com.wosai.upay.merchant.contract.service.PsbcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component("QueryIsPostalCardHandler")
public class QueryIsPostalCardHandler extends BatchProcessingHandler<QueryIsPostalCardExcel, QueryIsPostalCardExcel> {


    @Autowired
    private PsbcService psbcService;


    @Override
    public void doPreProcess(BatchContext batchContext) {
    }

    @Override
    public QueryIsPostalCardExcel handle(QueryIsPostalCardExcel queryIsPostalCardExcel, BatchContext batchContext) {
        ShopAccountTypeCheckBO shopAccountTypeCheckBO = new ShopAccountTypeCheckBO();
        shopAccountTypeCheckBO.setAccountId(queryIsPostalCardExcel.getAccountId());
        shopAccountTypeCheckBO.setAccountName(queryIsPostalCardExcel.getAccountName());
        shopAccountTypeCheckBO.setAccType(queryIsPostalCardExcel.getAccType());
        if (StringUtil.empty(queryIsPostalCardExcel.getAccountId()) || StringUtil.empty(queryIsPostalCardExcel.getAccountName())) {
            queryIsPostalCardExcel.setResult(null);
            return queryIsPostalCardExcel;
        }
        Boolean result = psbcService.shopAccountTypeCheck(shopAccountTypeCheckBO);

        queryIsPostalCardExcel.setResult(result);
        return queryIsPostalCardExcel;
    }

    @Override
    public void doAfterProcess(BatchContext batchContext) {
    }


    @Override
    public QueryIsPostalCardExcel handleError(QueryIsPostalCardExcel queryIsPostalCardExcel, BatchContext batchContext, Exception e) {
        queryIsPostalCardExcel.setResult(null);
        queryIsPostalCardExcel.setErrorMessage(e.getMessage());
        return queryIsPostalCardExcel;
    }

}
