package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonDataObjectNotExistsException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantAppConfig;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.model.CommonModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;


/**
 * Created by lihebin on 2018/9/7.
 */
@Component
@Slf4j
public class MerchantConfigParamsBiz {


    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private MerchantService merchantService;

    public Boolean updateMerchantConfigParamsV2(String merchantId, String merchantSn, Integer payway, String nestProperty, String param) {
        if (StringUtil.empty(merchantId) && StringUtil.empty(merchantSn)) {
            throw new CommonDataObjectNotExistsException("merchantId 与 merchantSn 不能同时为空");
        }
        String id;
        if (StringUtil.empty(merchantId)) {
            id = MapUtils.getString(merchantService.getMerchantBySn(merchantSn), DaoConstants.ID);
        } else {
            id = merchantId;
            merchantSn = MapUtils.getString(merchantService.getMerchantByMerchantId(id), Merchant.SN);
        }
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(id, payway);
        Map map = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS);
        try {
            BeanUtil.setNestedProperty(map, nestProperty, param);
            Map configParam = CollectionUtil.hashMap(CommonModel.ID, MapUtils.getString(merchantConfig, CommonModel.ID), MerchantConfig.PARAMS, map);
            tradeConfigService.updateMerchantConfig(configParam);
            supportService.removeCachedParams(merchantSn);
            return true;
        } catch (Throwable e) {
            log.error("MerchantConfigParamsBiz.updateMerchantConfigParams: ", e);
            return false;
        }
    }

    /**
     * @param merchantId 商户Id
     * @param payWay     支付方式
     * @param subAppId   小程序AppId
     * @param appId      业务方标识
     * @param feeRate    如果feeRate==null 表示使用merchant_config费率
     * @return
     * @Author: zhmh
     * @Description: 同步更新MerchantAppConfig
     * @time: 15:08 2021/1/11
     */
    public void handleMerchantAppConfig(String merchantId, Integer payWay, String subAppId, String appId, String feeRate) {
        log.info("同步更新MerchantAppConfig参数 merchantId:{},payWay:{},subAppId:{},appId:{}", merchantId, payWay, subAppId, appId);
        //构建merchantAppConfig参数
        Map<String, Object> merchantAppConfig = buildMerchantAppConfig(merchantId, payWay, appId, feeRate);
        //组装params
        final Map paramsMap = MapUtils.getMap(merchantAppConfig, MerchantAppConfig.PARAMS, Maps.newHashMap());
        //移除阶梯费率状态
        paramsMap.remove(MerchantAppConfig.LADDER_STATUS);
        //移除阶梯费率
        paramsMap.remove(MerchantAppConfig.LADDER_FEE_RATES);
        if (Objects.equals(payWay, PaywayEnum.WEIXIN.getValue())) {//修改微信相关配置
            String provider = BeanUtil.getPropString(merchantAppConfig, MerchantAppConfig.PROVIDER);
            if (!StringUtils.isEmpty(subAppId)) {
                String tradeParamsKey = CommonModel.PROVIDER_KEY.get(provider);
                final Map directTradeParams = MapUtils.getMap(paramsMap, tradeParamsKey, Maps.newHashMap());
                if (WosaiMapUtils.isEmpty(directTradeParams)) {
                    directTradeParams.put("weixin_mini_sub_appid", subAppId);
                    paramsMap.put(tradeParamsKey, directTradeParams);
                } else {
                    directTradeParams.put("weixin_mini_sub_appid", subAppId);
                }
            }
        }
        //数据库是否已经存在
        final Map appConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payWay, appId);
        if (MapUtils.isEmpty(appConfig)) { //不存在创建新的merchantAppConfig
            log.info("insert merchantAppConfig merchantId:{}, merchantAppConfig:{}", merchantId, JSON.toJSONString(merchantAppConfig));
            tradeConfigService.createMerchantAppConfig(merchantAppConfig);
        } else {
            //更新
            merchantAppConfig.put(DaoConstants.ID, BeanUtil.getPropString(appConfig, DaoConstants.ID));
            log.info("update merchantAppConfig merchantId:{},merchantAppConfig:{}", merchantId, JSON.toJSONString(merchantAppConfig));
            tradeConfigService.updateMerchantAppConfig(merchantAppConfig);
        }
    }

    /**
     * @Author: zhmh
     * @Description: 根据MerchantConfig数据组装MerchantAppConfig.可以让后来人看到到底取了哪些字段
     * @time: 17:23 2021/1/7
     */
    private Map<String, Object> buildMerchantAppConfig(String merchantId, Integer payWay, String appId, String feeRate) {
        //根据merchantId和支付方式获取交易参数
        final Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        if (org.apache.commons.collections4.MapUtils.isEmpty(merchantConfig)) {
            throw new CommonPubBizException(String.format("merchantId:%s,payWay:%s找不到交易参数", merchantId, payWay));
        }
        //根据merchant_config表信息构建merchant_app_config
        final Map<String, Object> merchantAppConfig = Maps.newHashMap();
        merchantAppConfig.put(MerchantAppConfig.MERCHANT_ID, merchantId);
        merchantAppConfig.put(MerchantAppConfig.APP_ID, appId);
        merchantAppConfig.put(MerchantAppConfig.PAYWAY, payWay);
        merchantAppConfig.put(MerchantAppConfig.B2C_FORMAL, merchantConfig.get(MerchantConfig.B2C_FORMAL));
        merchantAppConfig.put(MerchantAppConfig.B2C_STATUS, merchantConfig.get(MerchantConfig.B2C_STATUS));
        merchantAppConfig.put(MerchantAppConfig.B2C_FEE_RATE, StringUtil.empty(feeRate) ? merchantConfig.get(MerchantConfig.B2C_FEE_RATE) : feeRate);
        merchantAppConfig.put(MerchantAppConfig.B2C_AGENT_NAME, merchantConfig.get(MerchantConfig.B2C_AGENT_NAME));
        merchantAppConfig.put(MerchantAppConfig.C2B_FORMAL, merchantConfig.get(MerchantConfig.C2B_FORMAL));
        merchantAppConfig.put(MerchantAppConfig.C2B_FEE_RATE, StringUtil.empty(feeRate) ? merchantConfig.get(MerchantConfig.C2B_FEE_RATE) : feeRate);
        merchantAppConfig.put(MerchantAppConfig.C2B_STATUS, merchantConfig.get(MerchantConfig.C2B_STATUS));
        merchantAppConfig.put(MerchantAppConfig.C2B_AGENT_NAME, merchantConfig.get(MerchantConfig.C2B_AGENT_NAME));
        merchantAppConfig.put(MerchantAppConfig.WAP_FORMAL, merchantConfig.get(MerchantConfig.WAP_FORMAL));
        merchantAppConfig.put(MerchantAppConfig.WAP_STATUS, merchantConfig.get(MerchantConfig.WAP_STATUS));
        merchantAppConfig.put(MerchantAppConfig.WAP_FEE_RATE, StringUtil.empty(feeRate) ? merchantConfig.get(MerchantConfig.WAP_FEE_RATE) : feeRate);
        merchantAppConfig.put(MerchantAppConfig.WAP_AGENT_NAME, merchantConfig.get(MerchantConfig.WAP_AGENT_NAME));
        merchantAppConfig.put(MerchantAppConfig.MINI_FORMAL, merchantConfig.get(MerchantConfig.MINI_FORMAL));
        merchantAppConfig.put(MerchantAppConfig.MINI_STATUS, merchantConfig.get(MerchantConfig.MINI_STATUS));
        merchantAppConfig.put(MerchantAppConfig.MINI_FEE_RATE, StringUtil.empty(feeRate) ? merchantConfig.get(MerchantConfig.MINI_FEE_RATE) : feeRate);
        merchantAppConfig.put(MerchantAppConfig.MINI_AGENT_NAME, merchantConfig.get(MerchantConfig.MINI_AGENT_NAME));
        merchantAppConfig.put(MerchantAppConfig.APP_FORMAL, merchantConfig.get(MerchantConfig.APP_FORMAL));
        merchantAppConfig.put(MerchantAppConfig.APP_STATUS, merchantConfig.get(MerchantConfig.APP_STATUS));
        merchantAppConfig.put(MerchantAppConfig.APP_FEE_RATE, StringUtil.empty(feeRate) ? merchantConfig.get(MerchantConfig.APP_FEE_RATE) : feeRate);
        merchantAppConfig.put(MerchantAppConfig.APP_AGENT_NAME, merchantConfig.get(MerchantConfig.APP_AGENT_NAME));
        merchantAppConfig.put(MerchantAppConfig.H5_FORMAL, merchantConfig.get(MerchantConfig.H5_FORMAL));
        merchantAppConfig.put(MerchantAppConfig.H5_STATUS, merchantConfig.get(MerchantConfig.H5_STATUS));
        merchantAppConfig.put(MerchantAppConfig.H5_FEE_RATE, StringUtil.empty(feeRate) ? merchantConfig.get(MerchantConfig.H5_FEE_RATE) : feeRate);
        merchantAppConfig.put(MerchantAppConfig.H5_AGENT_NAME, merchantConfig.get(MerchantConfig.H5_AGENT_NAME));
        merchantAppConfig.put(MerchantAppConfig.EXTEND2_FORMAL, merchantConfig.get(MerchantConfig.EXTEND2_FORMAL));
        merchantAppConfig.put(MerchantAppConfig.EXTEND2_STATUS, merchantConfig.get(MerchantConfig.EXTEND2_STATUS));
        merchantAppConfig.put(MerchantAppConfig.EXTEND2_FEE_RATE, StringUtil.empty(feeRate) ? merchantConfig.get(MerchantConfig.EXTEND2_FEE_RATE) : feeRate);
        merchantAppConfig.put(MerchantAppConfig.EXTEND2_AGENT_NAME, merchantConfig.get(MerchantConfig.EXTEND2_AGENT_NAME));
        merchantAppConfig.put(MerchantAppConfig.PROVIDER, merchantConfig.get(MerchantConfig.PROVIDER));
        merchantAppConfig.put(MerchantAppConfig.PARAMS, merchantConfig.get(MerchantConfig.PARAMS));
        return merchantAppConfig;
    }



}
