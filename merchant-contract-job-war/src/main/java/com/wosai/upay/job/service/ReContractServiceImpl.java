package com.wosai.upay.job.service;

import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.RuleBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.externalservice.customer.CustomerRelationClient;
import com.wosai.upay.job.externalservice.paybusiness.PayBusinessOpenClient;
import com.wosai.upay.job.externalservice.paybusiness.model.PayCombosQueryReq;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import entity.common.OrganizationEs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: jerry
 * @date: 2019/8/8 15:28
 * @Description:重新报备
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class ReContractServiceImpl implements ReContractService {

    @Autowired
    RuleContext ruleContext;
    @Autowired
    ParamContextBiz paramContextBiz;
    @Autowired
    RuleBiz ruleBiz;

    @Autowired
    private WechatAuthBiz wechatAuthBiz;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private FeeRateService feeRateService;
    @Autowired
    private CustomerRelationClient customerRelationClient;
    @Autowired
    private PayBusinessOpenClient payBusinessOpenClient;

    @Override
    public Map reContract(ReContract reContract) {
        ContractRule contractRule = ruleContext.getContractRule(reContract.getRule());
        if (AcquirerTypeEnum.FU_YOU.getValue().equals(contractRule.getAcquirer())) {
            throw new CommonPubBizException("富友不允许重新报备子商户号");
        }
        if (AcquirerTypeEnum.GUOTONG.getValue().equals(contractRule.getAcquirer())) {
            throw new CommonPubBizException("国通不允许重新报备子商户号");
        }

        String merchantSn = reContract.getMerchant_sn();
        String remark = reContract.getRemark();
        //云闪付目前不支持重新报备
        if (PaywayEnum.UNIONPAY.getValue().equals(contractRule.getPayway())) {
            throw new CommonPubBizException("云闪付规则不支持重新报备");
        }
//        if (ProviderUtil.WEIXIN_PAY_WAY.equals(contractRule.getPayway())) {
//            throw new CommonPubBizException("微信不支持重新报备");
//        }
        Map paramContext;
        try {
            paramContext = paramContextBiz.getParamContextByMerchantSn(reContract.getMerchant_sn(), new ContractEvent().setEvent_type(0));
            paramContext.put(TYPE, "1");
            paramContext.put(REMARK, remark);
            paramContext.put(RECONTRACT, true);
        } catch (Exception e) {
            log.error("获取商户上下文错误", e);
            throw new CommonPubBizException("商户上下文信息有误无法重新报备或无法正确获取商户上下文信息请稍后再试");
        }
        String message = (String) ruleBiz.contractByRule(merchantSn, contractRule, paramContext, false).get(RuleBiz.CONTRACT_RES_KEY);
        if (StringUtils.isEmpty(message)) {
            return res(200, "重新报备成功");
        }
        throw new CommonPubBizException(message);
    }

    @Override
    public void reContractInNormalChannel(String merchantSn, String remark) {
        composeAcquirerBiz.reContractWx(merchantSn, remark, false);
    }

    @Override
    public void reContractInNormalChannelV2(String merchantSn, String remark, boolean forceMicro) {
        composeAcquirerBiz.reContractWx(merchantSn, remark, forceMicro);
    }

    @Override
    public AllowChangeIndustryResult allowChangeIndustry(AllowChangeIndustryRequest request) {
        Map settlementConfig = wechatAuthBiz.getSettlementConfig(request.getTargetIndustryId());
        if (WosaiMapUtils.isEmpty(settlementConfig)) {
            return AllowChangeIndustryResult.fail("目标行业配置的结算ID信息为空");
        }
        MerchantProviderParams providerParams = merchantProviderParamsMapper.getUseWeiXinParam(request.getMerchantSn());
        if (Objects.isNull(providerParams) || !settlementConfig.containsValue(providerParams.getWx_settlement_id())) {
            return AllowChangeIndustryResult.fail("当前在用微信子商户号结算ID和目标行业配置信息不匹配");
        }

        Map merchant = merchantService.getMerchantBySn(request.getMerchantSn());
        OrganizationEs mchIndirectOrg = customerRelationClient.getMchIndirectOrg(MapUtils.getString(merchant, DaoConstants.ID));
        List<Map> payCombos = payBusinessOpenClient.queryPayCombos(new PayCombosQueryReq().setIndustryId(request.getTargetIndustryId()).setOrganizationId(mchIndirectOrg.getId()), null);

        if (WosaiCollectionUtils.isEmpty(payCombos)) {
            return AllowChangeIndustryResult.fail("目标行业和商户所在行业配置的套餐为空");
        }

        List<ListMchFeeRateResult> listMchFeeRateResults = feeRateService.listMchEffectFeeRates(request.getMerchantSn());
        Optional<ListMchFeeRateResult> wxFeeRate = listMchFeeRateResults.stream().filter(r -> Integer.valueOf(PaywayEnum.WEIXIN.getValue()).equals(r.getPayWay())).findFirst();
        if (!wxFeeRate.isPresent()) {
            return AllowChangeIndustryResult.fail("商户在用的微信套餐信息不存在");
        }
        Long wxTradeComboId = wxFeeRate.get().getTradeComboId();
        boolean match = payCombos.stream().anyMatch(r -> wxTradeComboId == BeanUtil.getPropLong(r, DaoConstants.ID));
        if (!match) {
            return AllowChangeIndustryResult.fail("商户在用微信套餐与目标行业和组织配置的套餐不一致");
        }
        return AllowChangeIndustryResult.success();
    }


    private Map res(int i, String s) {
        Map result = Maps.newHashMap();
        result.put("code", i);
        result.put("message", s);
        return result;
    }
}
