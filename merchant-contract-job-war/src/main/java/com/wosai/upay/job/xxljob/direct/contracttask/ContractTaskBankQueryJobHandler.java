package com.wosai.upay.job.xxljob.direct.contracttask;

import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * xxl_job_desc: ContractTask-邮储华夏民生银行进件状态查询
 * 银行进件任务查询-华夏广发邮储
 *
 * <AUTHOR>
 * @date 2025/4/27
 */
@Slf4j
@Component("ContractTaskBankQueryJobHandler")
public class ContractTaskBankQueryJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private QueryContractStatusHandler queryContractStatusHandler;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public String getLockKey() {
        return "ContractTaskBankQueryJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            long currentTimeMillis = System.currentTimeMillis();
            List<ContractSubTask> contractSubTaskList = contractSubTaskMapper.selectPsbcContractQueryTask(
                    param.getBatchSize(),
                    StringUtil.formatDate(currentTimeMillis - param.getQueryTime()),
                    StringUtil.formatDate(currentTimeMillis)
            );
            for (ContractSubTask subTask : contractSubTaskList) {
                try {
                    if (ShutdownSignal.isShuttingDown()) {
                        return;
                    }
                    final ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id());
                    queryContractStatusHandler.doHandle(contractTask, subTask);
                } catch (Exception e) {
                    log.error("ContractTaskBankQueryJobHandler queryBankContractStatus error：", e);
                    chatBotUtil.sendMessageToContractWarnChatBot("queryBankContractStatus error" + ExceptionUtil.getThrowableMsg(e));
                }
            }
        } catch (Exception e) {
            log.error("ContractTaskBankQueryJobHandler queryBankContractStatus error：", e);
            chatBotUtil.sendMessageToContractWarnChatBot("queryBankContractStatus error" + ExceptionUtil.getThrowableMsg(e));
        }
    }

}
