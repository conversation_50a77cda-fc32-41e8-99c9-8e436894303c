package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TerminalConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.comboparams.SubAppIdSyns;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.PayParamsModel;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.service.ConfigSupportService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: jerry
 * @date: 2019/7/29 15:21
 * @Description:按规则处理的公共方法
 */
@Service
@Slf4j
public class RuleBiz {

    @Autowired
    private ConfigSupportService configSupportService;
    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private AgentAppidBiz agentAppidBiz;
    @Autowired
    SubAppIdSyns subAppIdSyns;

    public static final String CONTRACT_RES_KEY = "res";
    public static final String CONTRACT_PARAM_KEY = "param";

    @Autowired
    private ContractTaskBiz contractTaskBiz;
    @Autowired
    private MutexTaskCheckBiz mutexTaskCheckBiz;


    /**
     * 单规则报备
     *
     * @param: merchantSn
     * @param: contractRule
     * @param: paramContext 报备信息上下文
     * @param: checkParamsExist 是否检查商户子商户号已存在
     * @return: String  成功返回null 失败返回错误信息
     * @date: 11:45
     */
    @Transactional(rollbackFor = Exception.class)
    public Map contractByRule(String merchantSn, ContractRule contractRule, Map paramContext, boolean checkParamsExist) {
        Map result = new HashMap();
        String ruleGroupId = ProviderUtil.switchAcquirerToRuleGroupId(contractRule.getAcquirer());
        if (mutexTaskCheckBiz.checkMutexTaskProcessingForTask(merchantSn, ProviderUtil.CONTRACT_RECONTRACT, ruleGroupId)) {
            result.put(CONTRACT_RES_KEY, "存在待处理任务500");
            return result;
        }
        if (checkParamsExist && paramsExist(merchantSn, contractRule)) {
            result.put(CONTRACT_RES_KEY, "商户已报备400");
            return result;
        }
        String merchantName = BeanUtil.getPropString(paramContext.get("merchant"), Merchant.NAME);
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(merchantSn)
                .setMerchant_name(merchantName)
                .setType(checkParamsExist ? ProviderUtil.CONTRACT_TYPE_BATCH : ProviderUtil.CONTRACT_RECONTRACT)
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(1)
                .setAffect_status_success_task_count(0)
                .setRule_group_id(ruleGroupId);

        ContractSubTask contractSubTask = new ContractSubTask()
                .setP_task_id(contractTask.getId())
                .setChange_config(0)
                .setDefault_channel(0)
                .setPayway(contractRule.getPayway())
                .setContract_id(contractRule.getChannelNo())
                .setContract_rule(contractRule.getRule()).setStatus_influ_p_task(1)
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT)
                .setChannel(providerFactory.convertToBeanName(contractRule.getProvider()))
                .setMerchant_sn(merchantSn)
                .setRule_group_id(ruleGroupId);
        if(AcquirerTypeEnum.HAI_KE.getValue().equals(contractRule.getAcquirer())){
           Map param = doContract(contractTask, contractSubTask, contractRule);
           if(!StringUtil.empty(BeanUtil.getPropString(param, CONTRACT_PARAM_KEY))){
               ContractSubTask syncSubTask = new ContractSubTask()
                       .setP_task_id(contractTask.getId())
                       .setChange_config(0)
                       .setDefault_channel(0)
                       .setPayway(contractRule.getPayway())
                       .setContract_id(contractRule.getChannelNo())
                       .setContract_rule(contractRule.getRule() + "-sync")
                       .setStatus_influ_p_task(1)
                       .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT)
                       .setChannel(providerFactory.convertToBeanName(contractRule.getProvider()))
                       .setSchedule_dep_task_id(contractSubTask.getId())
                       .setMerchant_sn(merchantSn)
                       .setRule_group_id(ruleGroupId);
               ContractRule syncRule = ruleContext.getContractRule(contractRule.getRule() + "-sync");
               doContract(contractTask,syncSubTask,syncRule);
           }
            return param;
        } else {
            return doContract(contractTask, contractSubTask, contractRule);
        }
    }


    private Map doContract(ContractTask contractTask, ContractSubTask contractSubTask, ContractRule contractRule) {
        BasicProvider basicProvider = providerFactory.getProviderByContractRule(contractRule);
        Map contractRes = new HashMap();
        if (basicProvider == null) {
            contractRes.put(CONTRACT_RES_KEY, "该规则获取不到对应provider400");
            return contractRes;
        }
        ContractResponse contractResponse = basicProvider.processTaskByRule(contractTask, contractRule.getContractChannel(), contractSubTask);
        if (contractResponse == null) {
            contractRes.put(CONTRACT_RES_KEY, "该规则获取不到对应provider400");
            return contractRes;
        }
        boolean success = contractResponse.getCode() == 200;
        Map response = Maps.newHashMap();
        response.put("responseParam", contractResponse.getResponseParam());
        response.put("tradeParam", contractResponse.getTradeParam());
        if (success) {
            response.put("merchantProviderParamsId", contractResponse.getMerchantProviderParamsId());
            contractTask.setStatus(TaskStatus.SUCCESS.getVal()).setAffect_status_success_task_count(1).setResult("报备成功");
            contractSubTask.setStatus(TaskStatus.SUCCESS.getVal()).setRequest_body(JSON.toJSONString(contractResponse.getRequestParam())).setResponse_body(JSON.toJSONString(response)).setResult(contractResponse.getMessage());
        } else {
            Map result = CollectionUtil.hashMap("channel", contractSubTask.getChannel(),
                    "message", contractResponse.getMessage(), "code", contractResponse.getCode());
            if (contractSubTask.getPayway() != null) {
                result.put("payway", contractSubTask.getPayway());
            }
            contractSubTask.setRequest_body(JSON.toJSONString(contractResponse.getRequestParam()))
                    .setResponse_body(JSON.toJSONString(response))
                    .setResult(JSON.toJSONString(result)).setStatus(TaskStatus.FAIL.getVal());
            contractTask.setStatus(TaskStatus.FAIL.getVal()).setResult(JSON.toJSONString(result));
//            contractTaskMapper.insert(contractTask);
            // TODO 插入contract_task表并根据type和status判断是否发送消息到神策
            if(!contractSubTask.getContract_rule().contains("sync")) {
                contractTaskBiz.insert(contractTask);
            }
            contractSubTaskMapper.insert(contractSubTask.setP_task_id(contractTask.getId()));
            contractRes.put(CONTRACT_RES_KEY, "报备失败" + contractResponse.getMessage() + contractResponse.getCode());
            return contractRes;
        }
//        contractTaskMapper.insert(contractTask);
        // TODO 插入contract_task表并根据type和status判断是否发送消息到神策
        if(!contractSubTask.getContract_rule().contains("sync")) {
            contractTaskBiz.insert(contractTask);
        }
        contractSubTaskMapper.insert(contractSubTask.setP_task_id(contractTask.getId()));
        contractRes.put(CONTRACT_PARAM_KEY, contractResponse.getMerchantProviderParamsId());
        return contractRes;
    }

    public void changeDbbConfigParam(String paramId, String feeRate, List<String> terminals) {
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPrimaryKey(paramId);
        int payWay = merchantProviderParams.getPayway();
        String merchantSn = merchantProviderParams.getMerchant_sn();
        Map paramUpdate = getUpdateConfigParam(merchantProviderParams);
        String merchantCity = configSupportService.getMerchantCity(merchantSn);
        int provider = merchantProviderParams.getProvider();
        String channelNo = merchantProviderParams.getChannel_no();
        String agent = agentAppidBiz.getAgentName(payWay, provider, channelNo, merchantCity);
        if (StringUtils.isEmpty(agent)) {
            throw new CommonPubBizException("dbb agent未配置！ ");
        }
        //dbb只需要修改wap的
        Map update = CollectionUtil.hashMap(MerchantConfig.WAP_FEE_RATE, feeRate, MerchantConfig.WAP_AGENT_NAME, agent);
        changeTerminalConfigParam(paramUpdate, update, terminals, payWay);
    }

    public static final String TRADE_PARAM = "up_direct_trade_params";

    /**
     * 切换交易参数  修改terminal_config表
     **/
    protected void changeTerminalConfigParam(Map paramUpdate, Map update, List<String> terminalIds, int payWay) {
        //todo 是否需要判断入网的收单机构与当前交易参数的保持一致
        terminalIds.stream().forEach(terminalId -> {
            Map config = tradeConfigService.getTerminalConfigByTerminalIdAndPayway(terminalId, payWay);
            Map params = MapUtils.getMap(config, TerminalConfig.PARAMS, new HashMap());
            Map param = MapUtils.getMap(params, TRADE_PARAM, new HashMap());
            param.putAll(paramUpdate);
            params.put(TRADE_PARAM, param);
            if (config == null) {
                update.put(TerminalConfig.TERMINAL_ID, terminalId);
                update.put(TerminalConfig.PARAMS, params);
                update.put(TerminalConfig.PAYWAY, payWay);
                tradeConfigService.createTerminalConfig(update);
            } else {
                update.put(ConstantUtil.KEY_ID, MapUtils.getString(config, ConstantUtil.KEY_ID));
                update.put(TerminalConfig.PARAMS, params);
                tradeConfigService.updateTerminalConfig(update);
            }
        });
    }

    private Map getUpdateConfigParam(MerchantProviderParams params) {
        int payWay = params.getPayway();
        Map res = new HashMap();
        //todo 目前只有电宝宝会接入
        switch (payWay) {
            case PayParamsModel.PAYWAY_ALIPAY2:
                res.put(TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, params.getPay_merchant_id());
                res.put(TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, params.getProvider_merchant_id());
                break;
            case PayParamsModel.PAYWAY_WEIXIN:
                //todo 给九九折留坑
            default:
                throw new CommonPubBizException("支付方式不支持");
        }
        return res;
    }

    private boolean paramsExist(String merchantSn, ContractRule contractRule) {
        MerchantProviderParamsExample merchantProviderParamsExample = new MerchantProviderParamsExample();
        merchantProviderParamsExample.or()
                .andMerchant_snEqualTo(merchantSn)
                .andChannel_noEqualTo(contractRule.getChannelNo())
                .andPaywayEqualTo(contractRule.getPayway())
                .andProviderEqualTo(Integer.valueOf(contractRule.getProvider()));
        List<MerchantProviderParams> list = merchantProviderParamsMapper.selectByExample(merchantProviderParamsExample);
        return !CollectionUtils.isEmpty(list);
    }
}
