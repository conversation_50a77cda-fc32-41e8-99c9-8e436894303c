package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.req.UpdateMerchantReq;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.config.ApolloConfig;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.providers.FuyouProvider;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.FuyouParam;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 富友操作服务
 * <AUTHOR>
 * @Date 2024/05/27
 **/
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class FuyouServiceImpl implements FuyouOperateService {


    @Autowired
    MerchantService merchantService;

    @Autowired
    ParamContextBiz paramContextBiz;

    @Autowired
    FuyouService fuyouService;

    @Autowired
    ContractParamsBiz contractParamsBiz;

    @Autowired
    private FuyouProvider fuyouProvider;


    // 对公凭证
    private static String CERTFICATE = "fuyou_certificate";

    @Override
    public void syncFuyouPic(String merchantSn, String picUrl) {
        saveAcceptanceCertificate(merchantSn, picUrl);
        Map contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        fuyouService.uploadFilesZip(contextParam, fuyouParam);
        fuyouService.confirmUploadFiles(merchantSn, fuyouParam);
    }

    private void saveAcceptanceCertificate(String merchantSn, String url){
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        UpdateMerchantReq updateMerchantReq = new UpdateMerchantReq();
        Map extra = merchant.getExtra();
        if (CollectionUtils.isEmpty(extra)){
            extra = new HashMap();
        }
        extra.put(CERTFICATE, url);
        updateMerchantReq.setId(BeanUtil.getPropString(merchant, com.wosai.upay.common.dao.DaoConstants.ID));
        updateMerchantReq.setExtra(extra);
        merchantService.updateMerchant(updateMerchantReq, null);
    }


    @Override
    public ContractResponse syncFuyouSubInsCd(String merchantSn, String subInsCd) {
        final FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        return fuyouService.updateMerchantWithSubInsCd(paramContext, subInsCd,fuyouParam);
    }

    @Override
    public Map queryMerchant(String merchantSn) {
        final FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        ContractResponse response = fuyouService.queryMerchant(merchantSn, fuyouParam);
        return response.getResponseParam();
    }

    @Override
    public boolean fuYouSpecialIndustryApplySwitch() {
        return ApolloConfig.getFuYouSpecialIndustryApplySwitch();
    }

    @Override
    public ContractResponse contractAlipay(String merchantSn) {
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams("fuyou-1038-2", FuyouParam.class);
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        return fuyouProvider.contractAlipay(merchantSn, "fuyou-1038-2", fuyouParam, merchant.getIndustry());
    }

    @Override
    public ContractResponse contractWechat(String merchantSn) {
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams("fuyou-1038-3", FuyouParam.class);
        Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        return fuyouProvider.queryAndProcessFuYouInuseWechatMchId("178700778", null, merchantSn, "fuyou-1038-3", paramContext, fuyouParam);
    }
}
