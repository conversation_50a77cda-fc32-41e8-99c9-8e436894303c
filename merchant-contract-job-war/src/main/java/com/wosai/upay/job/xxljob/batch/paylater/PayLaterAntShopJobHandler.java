package com.wosai.upay.job.xxljob.batch.paylater;

import com.wosai.upay.job.Constants.PayLaterConstant;
import com.wosai.upay.job.biz.PayLaterBiz;
import com.wosai.upay.job.mapper.PayLaterApplyMapper;
import com.wosai.upay.job.model.payLater.PayLaterApply;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * xxl_job_desc: 先享后付创建蚂蚁门店
 * <AUTHOR>
 * @date 2025/4/2
 */
@Slf4j
@Component("PayLaterAntShopJobHandler")
public class PayLaterAntShopJobHandler extends AbstractBatchJobHandler<PayLaterApply> {

    @Autowired
    private PayLaterBiz payLaterBiz;
    @Autowired
    private PayLaterApplyMapper payLaterApplyMapper;

    @Override
    public List<PayLaterApply> queryTaskItems(BatchJobParam param) {
        return payLaterBiz.getPayLaterTasks(Collections.singletonList(PayLaterConstant.ProcessStatus.ANT_SHOP_APPLYING), param.getBatchSize(), param.getQueryTime());
    }

    @Override
    public String getLockKey(PayLaterApply payLaterApply) {
        return "PayLaterAntShopJobHandler:" + payLaterApply.getId();
    }

    @Override
    public void doHandleSingleData(PayLaterApply payLaterApply) {
        try {
            PayLaterApply apply = payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId());
            if (!PayLaterConstant.ProcessStatus.ANT_SHOP_APPLYING.equals(apply.getProcess_status())) {
                return;
            }
            payLaterBiz.handleAntShop(payLaterApply);
        } catch (Exception e) {
            log.error("商户号:{} 异常", payLaterApply.getMerchant_sn(), e);
            payLaterBiz.modifyPayLaterApply(payLaterApply, PayLaterConstant.Status.ANT_SHOP_FAIL,
                    PayLaterConstant.SubStatus.FAIL,
                    PayLaterConstant.ProcessStatus.FAIL,
                    PayLaterConstant.Result.ZHIMA_AUDIT_FAIL,
                    0);
        }
    }
}
