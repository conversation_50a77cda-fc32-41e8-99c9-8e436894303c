package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.dao.WeixinParamsUpdateApplyDao;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.merchant.contract.constant.UnionBusinessFileds;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.service.WeixinService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Created by lihebin on 2018/7/10.
 */
@Service
@AutoJsonRpcServiceImpl
public class ContractWeixinServiceImpl implements ContractWeixinService {
    private final static Logger log = LoggerFactory.getLogger(ContractWeixinServiceImpl.class);
    public static final String KEY_RESULT_CODE = "result_code";
    public static final String KEY_RESULT_MESSAGE = "message";
    public static final long RESULT_TIME = 1000L * 60 * 60 * 24 * 30 + 1000L * 60 * 60;


    @Autowired
    private MerchantService merchantService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private WeixinParamsUpdateApplyDao weixinParamsUpdateApplyDao;

    @Autowired
    @Lazy
    private MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    private ComposeAcquirerBiz acquirerBiz;

    @Autowired
    private WeixinService weixinService;

    @Autowired
    private RuleContext ruleContext;


    @Resource(name = "merchantContractJdbcTemplate")
    JdbcTemplate merchantContractJdbcTemplate;


    @Override
    public boolean updateCustomAppidApply(Map params) {
        String updateSql = "update custom_appid_apply set mtime = UNIX_TIMESTAMP()*1000, version = version + 1";
        int status = BeanUtil.getPropInt(params, CustomAppidApply.STATUS, -1);
        if (status != -1) {
            updateSql = updateSql + ", status = " + status;
        }
        String licenseName = BeanUtil.getPropString(params, CustomAppidApply.LICENSE_NAME);
        if (!StringUtil.empty(licenseName)) {
            updateSql = updateSql + ", license_name = '" + licenseName + "'";
        }

        String providerMerchantId = BeanUtil.getPropString(params, CustomAppidApply.PROVIDER_MERCHANT_ID);
        if (!StringUtil.empty(providerMerchantId)) {
            updateSql = updateSql + ", provider_merchant_id = '" + providerMerchantId + "'";
        }

        String weixinMerchantId = BeanUtil.getPropString(params, CustomAppidApply.WEIXIN_MERCHANT_ID);
        if (!StringUtil.empty(weixinMerchantId)) {
            updateSql = updateSql + ", weixin_merchant_id = '" + weixinMerchantId + "'";
        }
        String appid = BeanUtil.getPropString(params, CustomAppidApply.APPID);
        if (!StringUtil.empty(appid)) {
            updateSql = updateSql + ", appid = '" + appid + "'";
        }
        String miniAppid = BeanUtil.getPropString(params, CustomAppidApply.MINI_APPID);
        if (!StringUtil.empty(appid)) {
            updateSql = updateSql + ", mini_appid = '" + miniAppid + "'";
        }
        Object extra = BeanUtil.getProperty(params, CustomAppidApply.EXTRA);
        if (extra != null) {
            updateSql = updateSql + ", extra = '" + extra + "'";
        }
        long ctime = BeanUtil.getPropLong(params, DaoConstants.CTIME);
        if (ctime != 0) {
            updateSql = updateSql + ", ctime = UNIX_TIMESTAMP()*1000";
        }
        updateSql = updateSql + " where id = ?";
        String id = BeanUtil.getPropString(params, DaoConstants.ID);
        try {
            merchantContractJdbcTemplate.update(updateSql, id);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    @Override
    public Map getMerchantChannelMessage(Map params) {
        String merchantId = BeanUtil.getPropString(params, CommonModel.MERCHANT_ID);
        Map merchant = merchantService.getMerchant(merchantId);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        Map contactMessage = getMerchantOldConfig(merchantId);
        if (MapUtils.isEmpty(contactMessage)) {
            return CollectionUtil.hashMap(KEY_RESULT_CODE, 0, KEY_RESULT_MESSAGE, "直连或未配置交易参数商户无法获取关注公众号信息");
        }
        String weixinSubMchId = BeanUtil.getPropString(contactMessage, CommonModel.WEIXIN_SUB_MCH_ID);
        if (StringUtil.empty(weixinSubMchId)) {
            return CollectionUtil.hashMap(KEY_RESULT_CODE, 0, KEY_RESULT_MESSAGE, "直连或未配置交易参数商户无法获取关注公众号信息");
        }
        MerchantParamReq req = new MerchantParamReq()
                .setMerchant_sn(merchantSn)
                .setPay_merchant_id(weixinSubMchId);
        List<MerchantProviderParamsDto> paramsDtos = merchantProviderParamsService.getMerchantProviderParams(req);
        if (CollectionUtils.isNotEmpty(paramsDtos)) {
            MerchantProviderParamsDto paramsDto = paramsDtos.get(0);
            String channelNo = paramsDto.getChannel_no();
            String weixinAppid = paramsDto.getWeixin_sub_appid();
            String weixinSubScriptAppid = paramsDto.getWeixin_subscribe_appid();
            if (StringUtil.empty(weixinSubScriptAppid)) {
                Map customAppidApply = getCustomAppidApply(channelNo, merchantSn, 1, null, null, weixinAppid, null, null, 3);
                if (!MapUtils.isEmpty(customAppidApply)) {
                    return CollectionUtil.hashMap(KEY_RESULT_CODE, 3, KEY_RESULT_MESSAGE, "已取消推荐关注公众号");
                }
                weixinSubScriptAppid = weixinAppid;
            }
            Map channelAppidMap = getChannelAppIdMap(channelNo, weixinSubScriptAppid);
            if (MapUtils.isEmpty(channelAppidMap)) {
                Map customAppidApply = getCustomAppidApply(channelNo, merchantSn, -1, null, null, weixinAppid, weixinSubScriptAppid, null, -2);
                return CollectionUtil.hashMap(
                        KEY_RESULT_CODE, 1,
                        ChannelAppidMap.CHANNEL_NO, channelNo,
                        ChannelAppidMap.APPID, weixinSubScriptAppid,
                        ChannelAppidMap.CHANNEL_NAME, BeanUtil.getPropString(customAppidApply, CustomAppidApply.LICENSE_NAME)
                );
            }
            return CollectionUtil.hashMap(
                    KEY_RESULT_CODE, 2,
                    ChannelAppidMap.CHANNEL_NO, channelNo,
                    ChannelAppidMap.APPID, weixinSubScriptAppid,
                    ChannelAppidMap.CHANNEL_NAME, BeanUtil.getPropString(channelAppidMap, ChannelAppidMap.CHANNEL_NAME),
                    ChannelAppidMap.APPID_NAME, BeanUtil.getPropString(channelAppidMap, ChannelAppidMap.APPID_NAME)
            );
        }

        return Collections.emptyMap();
    }

    @Override
    public Map rollbackMerchantWeixinConfig(Map params) {
        return CollectionUtil.hashMap(CommonModel.RESULT_CODE, 0, CommonModel.RESULT_MESSAGE, "逻辑已下线");
    }

    @Override
    public Map getCustomAppidApplyByParams(Map params) {
        String merchantId = BeanUtil.getPropString(params, CommonModel.MERCHANT_ID);
        Map contactMessage = getMerchantOldConfig(merchantId);
        if (MapUtils.isEmpty(contactMessage)) {
            return new HashMap();
        }
        String channelNo = BeanUtil.getPropString(contactMessage, CustomAppidApply.CHANNEL_NO);
        String parentMerchantId = BeanUtil.getPropString(contactMessage, CustomAppidApply.PARENT_MERCHANT_ID);
        int type = BeanUtil.getPropInt(params, CustomAppidApply.TYPE);
        Map merchant = merchantService.getMerchant(merchantId);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        return getCustomAppidApplyByMerchantSnAndType(channelNo, parentMerchantId, merchantSn, type);
    }

    private Map getCustomAppidApplyByMerchantSnAndType(String channelNo, String parentMerchantId, String merchantSn, int type) {
        String querySql = "select id,provider_service_vendor,channel_no,parent_merchant_id,merchant_sn,provider_merchant_id,weixin_merchant_id,old_pay_appid,pay_appid,appid,mini_appid,license_name,merchant_name,type,status,extra,ctime,mtime from custom_appid_apply where deleted = 0 and merchant_sn = ? and type = ?";
        if (!StringUtil.empty(channelNo)) {
            querySql = querySql + " and channel_no = '" + channelNo + "'";
        }
        if (!StringUtil.empty(parentMerchantId)) {
            querySql = querySql + " and parent_merchant_id = '" + parentMerchantId + "'";
        }
        querySql = querySql + " order by ctime desc limit 1";
        try {
            return merchantContractJdbcTemplate.queryForMap(querySql, merchantSn, type);
        } catch (Exception e) {
            return new HashMap();
        }
    }


    private Map getMerchantOldConfig(String merchantId) {
        try {
            Map tradeParams = tradeConfigService.getTradeParams(3, 3, CollectionUtil.hashMap(MerchantConfig.MERCHANT_ID, merchantId));
            if (MapUtils.isEmpty(tradeParams)) {
                return new HashMap();
            }
            String channelNo;
            String weixinSubAppid;
            String weixinSubMchId;
            for (Object key : tradeParams.keySet()) {
                if (CommonModel.LAKALA_WANMA_TRADE_PARAMS.equals(key)) {
                    Map lakalaWanmaTradeParams = MapUtils.getMap(tradeParams, CommonModel.LAKALA_WANMA_TRADE_PARAMS);
                    channelNo = BeanUtil.getPropString(lakalaWanmaTradeParams, CommonModel.RECE_ORG_NO);
                    if (StringUtil.empty(channelNo)) {
                        return new HashMap();
                    }
                    weixinSubAppid = BeanUtil.getPropString(lakalaWanmaTradeParams, CommonModel.WEIXIN_SUB_APPID);
                    weixinSubMchId = BeanUtil.getPropString(lakalaWanmaTradeParams, CommonModel.WEIXIN_SUB_MCH_ID);
                    return CollectionUtil.hashMap(CustomAppidApply.CHANNEL_NO, channelNo, CustomAppidApply.PROVIDER_SERVICE_VENDOR, 3, CustomAppidApply.PAY_APPID, weixinSubAppid, CommonModel.WEIXIN_SUB_MCH_ID, weixinSubMchId);
                } else if (CommonModel.UP_TRADE_PARAMS.equals(key)) {
                    Map upTradeParams = MapUtils.getMap(tradeParams, CommonModel.UP_TRADE_PARAMS);
                    channelNo = BeanUtil.getPropString(upTradeParams, CommonModel.CHANNEL_ID);
                    if (StringUtil.empty(channelNo)) {
                        return new HashMap();
                    }
                    String parentMerchantId = BeanUtil.getPropString(upTradeParams, CommonModel.WEIXIN_MCH_ID);
                    weixinSubAppid = BeanUtil.getPropString(upTradeParams, CommonModel.WEIXIN_SUB_APPID);
                    weixinSubMchId = BeanUtil.getPropString(upTradeParams, CommonModel.WEIXIN_SUB_MCH_ID);
                    return CollectionUtil.hashMap(CustomAppidApply.CHANNEL_NO, channelNo, CustomAppidApply.PARENT_MERCHANT_ID, parentMerchantId, CustomAppidApply.PROVIDER_SERVICE_VENDOR, 5, CustomAppidApply.PAY_APPID, weixinSubAppid, CommonModel.WEIXIN_SUB_MCH_ID, weixinSubMchId);
                } else if (CommonModel.UP_DIRECT_TRADE_PARAMS.equals(key)) {
                    Map updirectTradeParams = MapUtils.getMap(tradeParams, CommonModel.UP_DIRECT_TRADE_PARAMS);
                    channelNo = BeanUtil.getPropString(updirectTradeParams, CommonModel.RECE_ORG_NO);
                    if (StringUtil.empty(channelNo)) {
                        return new HashMap();
                    }
                    weixinSubAppid = BeanUtil.getPropString(updirectTradeParams, CommonModel.WEIXIN_SUB_APPID);
                    weixinSubMchId = BeanUtil.getPropString(updirectTradeParams, CommonModel.WEIXIN_SUB_MCH_ID);
                    return CollectionUtil.hashMap(CustomAppidApply.CHANNEL_NO, channelNo, CustomAppidApply.PROVIDER_SERVICE_VENDOR, 6, CustomAppidApply.PAY_APPID, weixinSubAppid, CommonModel.WEIXIN_SUB_MCH_ID, weixinSubMchId);
                } else if (CommonModel.LKL_ORG_TRADE_PARAMS.equals(key)) {
                    Map updirectTradeParams = MapUtils.getMap(tradeParams, CommonModel.LKL_ORG_TRADE_PARAMS);
                    channelNo = BeanUtil.getPropString(updirectTradeParams, CommonModel.RECE_ORG_NO);
                    if (StringUtil.empty(channelNo)) {
                        return new HashMap();
                    }
                    weixinSubAppid = BeanUtil.getPropString(updirectTradeParams, CommonModel.WEIXIN_SUB_APPID);
                    weixinSubMchId = BeanUtil.getPropString(updirectTradeParams, CommonModel.WEIXIN_SUB_MCH_ID);
                    return CollectionUtil.hashMap(CustomAppidApply.CHANNEL_NO, channelNo, CustomAppidApply.PROVIDER_SERVICE_VENDOR, 6, CustomAppidApply.PAY_APPID, weixinSubAppid, CommonModel.WEIXIN_SUB_MCH_ID, weixinSubMchId);
                } else if (TransactionParam.UNION_PAY_TL_TRADE_PARAMS.equals(key)) {
                    Map updirectTradeParams = MapUtils.getMap(tradeParams, TransactionParam.UNION_PAY_TL_TRADE_PARAMS);
                    weixinSubAppid = BeanUtil.getPropString(updirectTradeParams, TransactionParam.UNION_PAY_TL_WEIXIN_SUB_APP_ID);
                    weixinSubMchId = BeanUtil.getPropString(updirectTradeParams, TransactionParam.UNION_PAY_TL_WEIXIN_SUB_MCH_ID);
                    return CollectionUtil.hashMap(CustomAppidApply.PAY_APPID, weixinSubAppid, CommonModel.WEIXIN_SUB_MCH_ID, weixinSubMchId);
                }
            }
        } catch (Exception ignored) {
        }
        return new HashMap();

    }


    @Override
    public Map getCustomAppidApply(String channelNo, String merchantSn, int type, String licenseName, String parentMerchantId, String payAppid, String appid, String miniAppid, int status) {
        String querySql = "select id,provider_service_vendor,channel_no,parent_merchant_id,merchant_sn,provider_merchant_id,weixin_merchant_id,pay_appid,old_pay_appid,appid,mini_appid,license_name,merchant_name,type,status,extra,ctime,mtime from custom_appid_apply where deleted = 0 and merchant_sn = ?";
        if (type != -1) {
            querySql = querySql + " and type = " + type;
        }

        if (!StringUtil.empty(channelNo)) {
            querySql = querySql + " and channel_no = '" + channelNo + "'";
        }
        if (!StringUtil.empty(parentMerchantId)) {
            querySql = querySql + " and parent_merchant_id = '" + parentMerchantId + "'";
        }
        if (type == 0 && StringUtil.empty(appid)) {
            log.error("getCustomAppidApply:{},{}", merchantSn, "type is 0 and appid is null");
            return new HashMap();
        }
        if (!StringUtil.empty(appid)) {
            querySql = querySql + " and appid = '" + appid + "'";
        }
        if (!StringUtil.empty(payAppid)) {
            querySql = querySql + " and pay_appid = '" + payAppid + "'";
        }
        if (!StringUtil.empty(miniAppid)) {
            querySql = querySql + " and mini_appid = '" + miniAppid + "'";
        }
        if (status != -1) {
            if (status == -2) {
                querySql = querySql + " and status IN (3,6)";
            } else {
                querySql = querySql + " and status = " + status;
            }
        }
        if (type == 0) {
            querySql = querySql + " and license_name = '" + licenseName + "'";
        }


        querySql = querySql + " order by ctime desc limit 1";
        Map customAppidApply;
        try {
            customAppidApply = merchantContractJdbcTemplate.queryForMap(querySql, merchantSn);
        } catch (Exception e) {
            customAppidApply = new HashMap();
        }
        return customAppidApply;
    }

    @Override
    public Map updateWeixinParams(Map params) {
        String merchantId = BeanUtil.getPropString(params, WeixinParamsUpdateApply.MERCHANT_ID);
        String merchantSn;
        Map merchant;
        if (!StringUtil.empty(merchantId)) {
            merchant = merchantService.getMerchant(merchantId);
            merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        } else {
            merchantSn = BeanUtil.getPropString(params, WeixinParamsUpdateApply.MERCHANT_SN);
            merchant = merchantService.getMerchantBySn(merchantSn);
        }
        String servicePhone = BeanUtil.getPropString(params, UnionBusinessFileds.SERVICE_PHONE);
        String merchantShortName = BeanUtil.getPropString(params, UnionBusinessFileds.MERCHANT_SHORTNAME);

        if (StringUtil.empty(servicePhone) && StringUtil.empty(merchantShortName)) {
            return CollectionUtil.hashMap(CommonModel.RESULT_CODE, CommonModel.RESULT_CODE_FAIL, CommonModel.RESULT_MESSAGE, "修改参数不可为空");
        }

        MerchantParamReq req = new MerchantParamReq()
                .setMerchant_sn(merchantSn)
                .setPayway(PaywayEnum.WEIXIN.getValue())
                .setStatus(1);
        List<MerchantProviderParamsDto> paramsDtos = merchantProviderParamsService.getMerchantProviderParams(req);
        if (WosaiCollectionUtils.isEmpty(paramsDtos)) {
            // CUA-10635
            if (!StringUtil.empty(servicePhone)) {
                merchantService.updateMerchant(CollectionUtil.hashMap(DaoConstants.ID, BeanUtil.getPropString(merchant, DaoConstants.ID), Merchant.CUSTOMER_PHONE, servicePhone));
            }
            return CollectionUtil.hashMap(CommonModel.RESULT_CODE, CommonModel.RESULT_CODE_SUCCESS, CommonModel.RESULT_MESSAGE, "修改成功");
        }
        MerchantProviderParamsDto paramsDto = paramsDtos.get(0);
        if (paramsDto.getProvider() == PayParamsModel.PROVIDER_FORMAL) {
            return CollectionUtil.hashMap(CommonModel.RESULT_CODE, CommonModel.RESULT_CODE_FAIL, CommonModel.RESULT_MESSAGE, "通道为直连通道,不允许修改");
        }
        weixinParamsUpdateApplyDao.saveWeixinParamsUpdateApply(paramsDto.getChannel_no(), merchantSn, paramsDto.getPay_merchant_id(), merchantShortName, servicePhone);
        if (!StringUtil.empty(servicePhone)) {
            merchantService.updateMerchant(CollectionUtil.hashMap(DaoConstants.ID, BeanUtil.getPropString(merchant, DaoConstants.ID), Merchant.CUSTOMER_PHONE, servicePhone));
        }

        acquirerBiz.updateWeixinParams(paramsDto, params);

        return CollectionUtil.hashMap(CommonModel.RESULT_CODE, CommonModel.RESULT_CODE_SUCCESS, CommonModel.RESULT_MESSAGE, "修改成功");
    }

    @Override
    public Map getWeixinUpdateTime(Map params) {
        String merchantId = BeanUtil.getPropString(params, WeixinParamsUpdateApply.MERCHANT_ID);
        String merchantSn;
        Map merchant;
        if (StringUtil.empty(merchantId)) {
            merchantSn = BeanUtil.getPropString(params, WeixinParamsUpdateApply.MERCHANT_SN);
            merchant = merchantService.getMerchantBySn(merchantSn);
        } else {
            merchant = merchantService.getMerchant(merchantId);
            merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        }
        if (MapUtils.isEmpty(merchant)) {
            return CollectionUtil.hashMap(CommonModel.RESULT_CODE, CommonModel.RESULT_CODE_FAIL, CommonModel.RESULT_MESSAGE, "商户不存在");
        }

        String weixinMerchantId = merchantProviderParamsService.getInUsePayMchId(merchantSn, 3);
        if (WosaiStringUtils.isEmpty(weixinMerchantId)) {
            // CUA-10635 当前没有在用的微信子商户号允许修改客服电话
            return CollectionUtil.hashMap(WeixinParamsUpdateApply.STATUS, WeixinParamsUpdateApply.STATUS_PASS);
        }
        Map weixinParamsUpdateApply = weixinParamsUpdateApplyDao.getWeixinParamsUpdateApply(weixinMerchantId);
        long mtime = BeanUtil.getPropLong(weixinParamsUpdateApply, DaoConstants.MTIME);
        long nowTime = System.currentTimeMillis();

        if (nowTime > mtime + RESULT_TIME) {
            weixinParamsUpdateApply.put(WeixinParamsUpdateApply.STATUS, WeixinParamsUpdateApply.STATUS_PASS);
        } else {
            weixinParamsUpdateApply.put(WeixinParamsUpdateApply.STATUS, WeixinParamsUpdateApply.STATUS_NOPASS);
        }
        weixinParamsUpdateApply.put(WeixinParamsUpdateApply.LOCK_TIME, mtime + RESULT_TIME - nowTime);
        return weixinParamsUpdateApply;
    }

    @Override
    public CommonResult changeGoldByMchId(String subMchId, boolean open) {
        try {
            com.wosai.upay.merchant.contract.model.ContractResponse response = null;
            if (open) {
                response = weixinService.openGolden(subMchId, getWxChannelNo(subMchId));
            } else {
                response = weixinService.closeGolden(subMchId, getWxChannelNo(subMchId));
            }
            return response.isSuccess() ? new CommonResult(CommonResult.SUCCESS, "SUCCESS") : new CommonResult(CommonResult.BIZ_FAIL, response.getMessage());
        } catch (Exception e) {
            log.error("changeGoldByMchId error {} {}", subMchId, open, e);
            return new CommonResult(CommonResult.BIZ_FAIL, e.getMessage());
        }
    }

    @Override
    public CommonResult changeTicketByMchId(String subMchId, boolean open) {
        try {
            com.wosai.upay.merchant.contract.model.ContractResponse response = null;
            if (open) {
                response = weixinService.openTicket(subMchId, getWxChannelNo(subMchId));
            } else {
                response = weixinService.closeTicket(subMchId, getWxChannelNo(subMchId));
            }
            return response.isSuccess() ? new CommonResult(CommonResult.SUCCESS, "SUCCESS") : new CommonResult(CommonResult.BIZ_FAIL, response.getMessage());
        } catch (Exception e) {
            log.error("changeGoldByMchId error {} {}", subMchId, open, e);
            return new CommonResult(CommonResult.BIZ_FAIL, e.getMessage());
        }
    }

    @Override
    public CommonResult changeAdvertiseByMchId(String subMchId, boolean open) {
        try {
            com.wosai.upay.merchant.contract.model.ContractResponse response = null;
            if (open) {
                response = weixinService.openAdvertise(subMchId, getWxChannelNo(subMchId));
            } else {
                response = weixinService.closeAdvertise(subMchId, getWxChannelNo(subMchId));
            }
            return response.isSuccess() ? new CommonResult(CommonResult.SUCCESS, "SUCCESS") : new CommonResult(CommonResult.BIZ_FAIL, response.getMessage());
        } catch (Exception e) {
            log.error("changeGoldByMchId error {} {}", subMchId, open, e);
            return new CommonResult(CommonResult.BIZ_FAIL, e.getMessage());
        }
    }

    /**
     * 获取子商户号所在微信渠道号
     *
     * @param subMchId
     * @return
     */
    private String getWxChannelNo(String subMchId) {
        List<MerchantProviderParamsDto> params = merchantProviderParamsService.getMerchantProviderParams(new MerchantParamReq().setPay_merchant_id(subMchId));
        if (WosaiCollectionUtils.isEmpty(params)) {
            throw new ContractBizException("子商户号不存在");
        }
        MerchantProviderParamsDto paramsDto = params.get(0);
        if (!PaywayEnum.WEIXIN.getValue().equals(paramsDto.getPayway())) {
            throw new ContractBizException(subMchId + "不是微信子商户号");
        }
        ContractChannel mcChannel = ruleContext.getContractChannel(paramsDto.getPayway(), paramsDto.getProvider() + "", paramsDto.getChannel_no());
        if (mcChannel == null || WosaiStringUtils.isEmpty(mcChannel.getPayway_channel_no())) {
            throw new ContractBizException(subMchId + "微信渠道不存在");
        }
        return mcChannel.getPayway_channel_no();
    }


    private Map getChannelAppIdMap(String channelNo, String appid) {
        try {
            String sql = "SELECT * FROM channel_appid_map WHERE channel_no = ? AND appid = ? AND type = 0 LIMIT 1";
            return merchantContractJdbcTemplate.queryForMap(sql, channelNo, appid);
        } catch (Exception e) {
            return new HashMap();
        }
    }
}
