package com.wosai.upay.job.xxljob.direct;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.DefaultValueUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * xxl_job_desc: 微信配置异常告警
 * <AUTHOR>
 * @date 2025/4/2
 */
@Slf4j
@Component("WeixinConfigStatusMonitorJobHandler")
public class WeixinConfigStatusMonitorJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ChatBotUtil chatBotUtil;
    @Resource(name = "merchantContractNPJdbcTemplate")
    private NamedParameterJdbcTemplate merchantContractNPJdbcTemplate;

    @Override
    public String getLockKey() {
        return "WeixinConfigStatusMonitorJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        List<MerchantProviderParams> appidParams = countParamsConfigStatus(param);
        if (CollectionUtils.isNotEmpty(appidParams)) {
            List<String> ids = appidParams.stream().map(MerchantProviderParams::getId).collect(Collectors.toList());
            sendMonitorDing(ids);
        }
    }

    private void sendMonitorDing(List<String> ids) {
        String text = String.format("微信配置异常，配置appid :  %s, id: %s", ids.size(), ids);
        chatBotUtil.sendMessageToContractWarnChatBot(text);
    }


    private List<MerchantProviderParams> countParamsConfigStatus(DirectJobParam param) {
        long now = System.currentTimeMillis();
        Map params = CollectionUtil.hashMap(CommonModel.PAYWAY, PaywayEnum.WEIXIN.getValue(),
                "beginTime", now - param.getStartTime(),
                "endTime", now - param.getEndTime(),
                CommonModel.PARAMS_CONFIG_STATUS, Arrays.asList(MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE, MerchantProviderParams.PARAMS_CONFIG_STATUS_FAIL)
        );
        String sql = "select * from merchant_provider_params where payway =:payway and mtime >:beginTime and mtime <:endTime and params_config_status in (:params_config_status) and provider !=:payway limit 100";
        return merchantContractNPJdbcTemplate.query(sql, params, new BeanPropertyRowMapper<>(MerchantProviderParams.class));
    }
}
