package com.wosai.upay.job.model.guotong;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/23
 */
@Data
public class GuotongReportInfoModel {
    /**
     * 00 全部 01银联微信、02银联支付宝、12收单刷卡、13银联扫码（云闪付）
     */
    private String payWay;
    /**
     * 报备结果列表
     */
    private List<ReportDetail> list;

    @Data
    public static class ReportDetail {

        /**
         * 报备状态
         * 00初始01报备成功02报备失败
         */
        private String status;
        /**
         * 支付宝机构 PID
         */
        private String zfbPid;
        /**
         * 第三方商户号
         */
        private String thirdMercid;
        /**
         * 支付通道
         */
        private String payWay;
        /**
         * 857银联商户号
         */
        private String reMercid;
        /**
         * 报备结果描述
         */
        private String result;
        /**
         * 微信渠道号
         */
        private String wxQdh;

        public boolean reportSuccess() {
            return "01".equals(status);
        }

        public boolean reportFail() {
            return "02".equals(status);
        }
    }
}
