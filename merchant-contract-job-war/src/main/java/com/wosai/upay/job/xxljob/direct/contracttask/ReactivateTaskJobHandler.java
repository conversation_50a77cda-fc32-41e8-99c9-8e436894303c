package com.wosai.upay.job.xxljob.direct.contracttask;

import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * xxl_job_desc: 进件任务-重新调度系统异常的入网任务
 * 扫描处于异常状态的入网任务
 *
 * <AUTHOR>
 * @date 2025/4/18
 */
@Slf4j
@Component("ReactivateTaskJobHandler")
public class ReactivateTaskJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public String getLockKey() {
        return "ReactivateTaskJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        SqlSession sqlSession = null;
        try {
            long current = System.currentTimeMillis();
            final long reactivateEndTime = current - param.getEndTime();
            final long reactivateStartTime = current - param.getStartTime();
            List<ContractTask> contractTasks = contractTaskMapper.selectReactivateContractTaskTodo(StringUtil.formatDate(reactivateStartTime), StringUtil.formatDate(reactivateEndTime), param.getBatchSize());
            if (CollectionUtils.isEmpty(contractTasks)) {
                return;
            }
            log.info("reactivateTask contractTasks:{}", contractTasks.stream().map(ContractTask::getId).collect(Collectors.toList()));
            //批量更新event创建时间让过期任务重新被扫描
            //使用批量执行器
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            ContractTaskMapper contractTaskMapper = sqlSession.getMapper(ContractTaskMapper.class);
            String formatDate = StringUtil.formatDate(current);
            contractTasks.forEach(task -> contractTaskMapper.updatePriority(formatDate, task.getId()));
            sqlSession.commit();
        } catch (Exception e) {
            if (Objects.nonNull(sqlSession)) {
                sqlSession.rollback();
            }
            log.error("reactivateTask error", e);
            chatBotUtil.sendMessageToContractWarnChatBot("重新激活未被扫描任务异常 " + ExceptionUtil.getThrowableMsg(e));
        } finally {
            if (Objects.nonNull(sqlSession)) {
                sqlSession.close();
            }
        }
    }
}
