package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.ChangeTradeParamsBiz;
import com.wosai.upay.job.biz.DataBusBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.PayParamsModel;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * xxl_job_desc: 银行直连-进件
 */
@Slf4j
@Component("BankDirectContractStatusJobHandler")
public class BankDirectContractStatusJobHandler extends AbstractBankDirectJobHandler {

    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private SubBizParamsBiz subBizParamsBiz;
    @Autowired
    private ChangeTradeParamsBiz tradeParamsBiz;
    @Autowired
    private DataBusBiz dataBusBiz;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Override
    public String getLockKey() {
        return "BankDirectContractStatusJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            final List<BankDirectApply> applyList = bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(
                    Lists.newArrayList(BankDirectApplyConstant.ProcessStatus.PENDING,
                            BankDirectApplyConstant.ProcessStatus.CONTRACT_APPLYING),
                    StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()),
                    param.getBatchSize()
            );

            applyList.forEach(apply -> {
                try {
                    if (ShutdownSignal.isShuttingDown()) {
                        return;
                    }
                    final Long taskId = apply.getTask_id();
                    final ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
                    handleContractTaskStatus(apply, contractTask);
                } catch (Exception exception) {
                    log.error("银行直连商户:{},进件状态异常", apply.getMerchant_sn(), exception);
                    chatBotUtil.sendMessageToContractWarnChatBot(
                            String.format("银行直连商户:%s,进件状态查询异常:%s",
                                    apply.getMerchant_sn(),
                                    exception.getMessage())
                    );
                }
            });
        } catch (Exception e) {
            log.error("merchantContractStatus exception", e);
            chatBotUtil.sendMessageToContractWarnChatBot(
                    String.format("银行直连商户进件状态查询异常:%s", e.getMessage())
            );
        }
    }

    private void handleContractTaskStatus(BankDirectApply apply, ContractTask contractTask) {
        final Integer status = contractTask.getStatus();
        if (Objects.equals(status, TaskStatus.PENDING.getVal())) {
            delayApply(apply, 5);
            return;
        }
        if (Objects.equals(status, TaskStatus.PROGRESSING.getVal()) && !applyIsFinish(apply)) {
            //修改direct_status(申请中)表状态和bank_direct_apply(status-10申请中,processStatus-10进件中)表状态
            modifyStatus(apply.getMerchant_sn(), apply, BankDirectApplyConstant.DirectStatus.PROCESSING, BankDirectApplyConstant.Status.APPLYING, BankDirectApplyConstant.APPLYING_MEMO, apply.getDev_code(), BankDirectApplyConstant.ProcessStatus.CONTRACT_APPLYING);
            delayApply(apply, 5);
        }
        if (Objects.equals(status, TaskStatus.SUCCESS.getVal()) && !applyIsFinish(apply)) {
            //修改direct_status(申请中)表状态和bank_direct_apply(status-10申请中,processStatus-20进件成功)表状态
            modifyStatus(apply.getMerchant_sn(), apply, BankDirectApplyConstant.DirectStatus.PROCESSING, BankDirectApplyConstant.Status.APPLYING, BankDirectApplyConstant.APPLYING_MEMO, apply.getDev_code(), BankDirectApplyConstant.ProcessStatus.CONTRACT_SUCCESS);
            final String acquire = BeanUtil.getPropString(apply.getExtraMap(), BankDirectApplyConstant.Extra.ACQUIRE);
            if (Objects.equals(AcquirerTypeEnum.UMB.getValue(), acquire)) {
                umbChangeAcquirer(apply);
            }
        }
        if (Objects.equals(status, TaskStatus.FAIL.getVal()) && !applyIsFinish(apply)) {
            //修改direct_status(失败)表状态和bank_direct_apply(status-30失败,processStatus-99失败)表状态
            modifyStatus(apply.getMerchant_sn(), apply, BankDirectApplyConstant.DirectStatus.FAIL, BankDirectApplyConstant.Status.FAIL, contractTask.getResult(), apply.getDev_code(), BankDirectApplyConstant.ProcessStatus.FAIL);
            //进件失败删除merchant_bank_account_pre表数据,这样商户重新绑定银行卡
            final String bankPreId = BeanUtil.getPropString(JSONObject.parseObject(apply.getForm_body(), Map.class), BankDirectApplyConstant.BANK_PRE_ID);
            if (!StringUtils.isEmpty(bankPreId)) {
                deletedMerchantBankAccountPre(bankPreId, "银行进件失败");
            }
        }
    }

    /**
     * @param apply
     * @return true-结束状态不允许变更 ,false-中间状态
     * <AUTHOR>
     * @Description: 银行直连申请业务是否处于结束状态
     * @ime 11:57
     */
    private Boolean applyIsFinish(BankDirectApply apply) {
        return Objects.equals(apply.getStatus(), BankDirectApplyConstant.Status.FAIL) || Objects.equals(apply.getStatus(), BankDirectApplyConstant.Status.SUCCESS);
    }

    private void umbChangeAcquirer(BankDirectApply apply) {
        Optional<MerchantProviderParamsDO> umbParamsOp = merchantProviderParamsDAO.getBySnAndProviderAndPayWay(apply.getMerchant_sn(), ProviderEnum.PROVIDER_UMB.getValue(), PaywayEnum.ACQUIRER.getValue());
        //如果有过其他收单机构的交易参数,那么等待凌晨的交易参数切换. 如果是新用户,单入的中投,则走下面的逻辑直接设置交易参数.
        List<MerchantProviderParamsDO> allProviderParams = merchantProviderParamsDAO.listByMerchantSnAndPayWay(apply.getMerchant_sn(), PaywayEnum.ACQUIRER.getValue());
        boolean onlyContractUMB = umbParamsOp.isPresent() && allProviderParams.size() == 1;
        if (!onlyContractUMB) {
            return;
        }

        // 中投参数设为默认
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(apply.getMerchant_sn())
                .andPaywayIn(Arrays.asList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.UNIONPAY.getValue()))
                .andProviderEqualTo(PayParamsModel.PROVIDER_UMB)
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExample(example);
        for (MerchantProviderParams providerParams : merchantProviderParams) {
            tradeParamsBiz.changeTradeParams(providerParams, null, Boolean.FALSE, subBizParamsBiz.getPayTradeAppId());
        }
        modifyStatus(apply.getMerchant_sn(), apply, BankDirectApplyConstant.DirectStatus.SUCCESS, BankDirectApplyConstant.Status.SUCCESS, "成功", apply.getDev_code(), BankDirectApplyConstant.ProcessStatus.SUCCESS);
        dataBusBiz.insert(ContractStatus.STATUS_SUCCESS, apply.getMerchant_sn(), "中投进件成功", AcquirerTypeEnum.UMB.getValue());
    }
}