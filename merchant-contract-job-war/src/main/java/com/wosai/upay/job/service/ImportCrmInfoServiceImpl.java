package com.wosai.upay.job.service;

import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.sales.merchant.business.bean.StoreInfoRequest;
import com.wosai.sales.merchant.business.model.AppInfoModel;
import com.wosai.sales.merchant.business.service.common.CommonMerchantInfoService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.job.biz.AopBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 用于对接crm,将没有走crm业务开通的数据导入到crm系统中
 * <AUTHOR>
 * @Date 2024/7/4 10:08
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class ImportCrmInfoServiceImpl implements ImportCrmInfoService {
    @Autowired
    private CommonMerchantInfoService commonMerchantInfoService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private AopBiz aopBiz;

    /**
     * 更新纪录
     * @param merchantSn 商户号
     * @param appStatus 状态
     * @param appId crm定义的应用appId,找crm同事要
     * @param maintainUserId 如果不指定,默认不传
     * @see AppInfoModel#STATUS_PENDING,AppInfoModel#STATUS_FAIL,AppInfoModel#STATUS_SUCCESS
     */
    @Override
    public void createOrUpdateBizOpenInfo(String merchantSn, Integer appStatus, String appId, String maintainUserId) {
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        if (StringUtils.isBlank(maintainUserId)) {
            try {
                maintainUserId = aopBiz.getMaintainUserId(merchantId);
            } catch (Exception exception) {
                log.info("createBizOpenInfo商户号:{},找不到商户维护人", merchantSn);
                return;
            }
        }
        PageInfo pageInfo = new PageInfo(1, 1);
        pageInfo.setOrderBy(Lists.newArrayList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC)));
        ListResult listResult = storeService.getSimpleStoreListByMerchantIdFromSlaveDb(merchantId, pageInfo);
        Map storeMap = listResult.getRecords().get(0);
        if (MapUtils.isEmpty(storeMap)) {
            log.info("createBizOpenInfo商户号:{},找不到门店", merchantSn);
            return;
        }
        final String storeId = BeanUtil.getPropString(storeMap, DaoConstants.ID);
        StoreInfoRequest storeInfoRequest = new StoreInfoRequest();
        storeInfoRequest.setStoreId(storeId);
        storeInfoRequest.setMerchantId(merchantId);
        storeInfoRequest.setPlatform("线下导入");
        storeInfoRequest.setPlatformVersion("线下导入");
        storeInfoRequest.setPlatformDevice("线下导入");
        storeInfoRequest.setUserId(maintainUserId);
        storeInfoRequest.setKeeperId(maintainUserId);
        storeInfoRequest.setAppId(appId);
        storeInfoRequest.setAppStatus(appStatus);
        storeInfoRequest.setNeedAuditMerchant(0);
        storeInfoRequest.setNeedAuditStore(0);
        storeInfoRequest.setIsNew(2);
        storeInfoRequest.setIsFirstStore(Boolean.TRUE);
        if (Objects.equals(appStatus, AppInfoModel.STATUS_PENDING)) {
            storeInfoRequest.setAppCtime(new Date());
            storeInfoRequest.setAppSubmitTime(new Date());
        } else if (Lists.newArrayList(AppInfoModel.STATUS_FAIL, AppInfoModel.STATUS_SUCCESS).contains(appStatus)) {
            storeInfoRequest.setAppConfirmTime(new Date());
        }
        commonMerchantInfoService.createBizOpenInfo(storeInfoRequest);
    }

}
