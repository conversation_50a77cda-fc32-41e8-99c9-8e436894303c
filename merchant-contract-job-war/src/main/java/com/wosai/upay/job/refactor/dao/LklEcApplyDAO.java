package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.upay.job.refactor.mapper.LklEcApplyMapper;
import com.wosai.upay.job.refactor.model.entity.LklEcApplyDO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.util.Optional;


/**
 * 签约申请记录表数据库访问层 {@link LklEcApplyDO}
 * 对LklEcApplyMapper层做出简单封装 {@link LklEcApplyMapper}
 *
 * <AUTHOR>
 */
@Repository
public class LklEcApplyDAO extends AbstractBaseDAO<LklEcApplyDO, LklEcApplyMapper> {

    public LklEcApplyDAO(SqlSessionFactory sqlSessionFactory, LklEcApplyMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }


    /**
     * 根据商户号查询
     *
     * @param merchantSn     商户号
     * @param devCode 应用方
     * @return Optional<LklEcApplyDO>
     */
    public Optional<LklEcApplyDO> getProcessApply(String merchantSn, String devCode) {
        LambdaQueryWrapper<LklEcApplyDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(LklEcApplyDO::getMerchantSn, merchantSn)
                .eq(LklEcApplyDO::getDevCode, devCode)
                .eq(LklEcApplyDO::getStatus,LklEcApplyDO.Status.PROCESSING);
        return selectOne(lambdaQueryWrapper);
    }



    /**
     * 根据商户号查询
     *
     * @param merchantSn     商户号
     * @param devCode 应用方
     * @return Optional<LklEcApplyDO>
     */
    public Optional<LklEcApplyDO> getApply(String merchantSn, String devCode) {
        LambdaQueryWrapper<LklEcApplyDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(LklEcApplyDO::getMerchantSn, merchantSn)
                .eq(LklEcApplyDO::getDevCode, devCode)
        .orderByDesc(LklEcApplyDO::getMtime);
        return selectOne(lambdaQueryWrapper);
    }



    /**
     * 根据电子申请单Id查询
     *
     * @param ecApplyId
     * @return Optional<LklEcApplyDO>
     */
    public Optional<LklEcApplyDO> getApplyByEcApplyId(String ecApplyId) {
        LambdaQueryWrapper<LklEcApplyDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(LklEcApplyDO::getEcApplyId, ecApplyId);
        return selectOne(lambdaQueryWrapper);
    }





}