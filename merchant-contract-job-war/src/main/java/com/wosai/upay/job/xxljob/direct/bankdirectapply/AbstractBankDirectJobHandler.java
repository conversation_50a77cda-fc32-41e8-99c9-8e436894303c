package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.task.bean.dto.req.TaskRpcStartReqDto;
import com.wosai.task.service.TaskInstanceService;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.biz.bankDirect.BankHandleService;
import com.wosai.upay.job.biz.bankDirect.BankHandleServiceFactory;
import com.wosai.upay.job.biz.bankDirect.HxbImportBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.enume.PsbcProcessStatusEnum;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.ProcessStatus;
import com.wosai.upay.job.model.ViewProcess;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.service.BankDirectService;
import com.wosai.upay.job.service.ContractTaskService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/22
 */
@Slf4j
public abstract class AbstractBankDirectJobHandler extends AbstractDirectJobHandler {

    @Autowired
    protected BankDirectApplyMapper bankDirectApplyMapper;
    @Autowired
    protected ContractTaskMapper contractTaskMapper;
    @Autowired
    protected DirectStatusBiz directStatusBiz;
    @Autowired
    protected MerchantService merchantService;
    @Autowired
    protected BankService bankService;
    @Autowired
    protected MerchantBankService merchantBankService;

    @Autowired
    protected ContractTaskService contractTaskService;

    @Autowired
    protected ProviderFactory providerFactory;

    @Autowired
    protected ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    protected BankDirectService bankDirectService;

    @Autowired
    protected TaskInstanceService taskInstanceService;

    @Autowired
    protected BankHandleServiceFactory factory;

    @Autowired
    protected HxbImportBiz hxbImportBiz;
    @Autowired
    protected ChatBotUtil chatBotUtil;

    /**
     * 调用接口派工
     *
     * @param acquireName
     * @param merchantSn
     */
    protected Boolean doStartTaskForRpc(String acquireName, String merchantSn, Long taskTemplateId) {
        TaskRpcStartReqDto dto = new TaskRpcStartReqDto();
        dto.setOperator("SYSTEM");
        dto.setOperatorName("SYSTEM");
        dto.setPlatform("SYSTEM");
        dto.setDiyParams(CollectionUtil.hashMap("title", acquireName, "merchant_sn", merchantSn));
        dto.setTaskTemplateId(taskTemplateId);
        dto.setTaskObjectSn(merchantSn);
        // 派工
        try {
            log.info("doStartTaskForRpc param :{}", JSONObject.toJSONString(dto));
            taskInstanceService.startTaskForRpc(dto);
        } catch (Exception exception) {
            log.error("doStartTaskForRpc error", exception);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * @param merchantSn      商户号
     *                        id      contractTask主键
     * @param bankDirectApply 申请单(优先根据id来查申请单)
     * @param directStatus    直连状态表 0-待处理 1-处理中 2-处理成功  3-处理失败
     * @param applyStatus     申请状态 0-已提交,10-申请中,20-申请成功,30-申请失败
     * @param devCode         业务标识
     * <AUTHOR>
     * @Description:修改direct_status表状态和bank_direct_apply表状态
     * @time 15:23
     */
    protected void modifyStatus(String merchantSn, BankDirectApply bankDirectApply, Integer directStatus, Integer applyStatus, String message, String devCode, Integer processStatus) {

        if (bankDirectApply == null) {
            log.error("直连申请单为空,商户号 : {}", merchantSn);
            return;
        }
        //状态变更才会发消息
        if (!Objects.equals(bankDirectApply.getStatus(), applyStatus)) {
            //修改direct_status状态为处理中
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, devCode, directStatus, message);
        }
        //如果子状态相同就结束避免频繁更新数据库
        if (Objects.equals(bankDirectApply.getProcess_status(), processStatus)) {
            return;
        }
        //申请单状态0-已提交,10-申请中,20-申请成功,30-申请失败
        bankDirectApply.setStatus(applyStatus);
        bankDirectApply.setResult(message);
        if (Objects.nonNull(processStatus)) {
            bankDirectApply.setProcess_status(processStatus);
            record(bankDirectApply, processStatus);
        }
        //同时更新时间
        bankDirectApply.setUpdate_at(new Date());
        bankDirectApply.setPriority(new Date());
        bankDirectApplyMapper.updateByPrimaryKeySelective(bankDirectApply);
        //华夏/工商线下导入,状态流转成功以后更新crm转态
        if (Objects.isNull(bankDirectApply.getTask_id()) && Lists.newArrayList(BankDirectApplyConstant.Status.FAIL, BankDirectApplyConstant.Status.SUCCESS).contains(applyStatus)) {
            hxbImportBiz.createOrUpdateBizOpenInfo(bankDirectApply.getMerchant_sn(), bankDirectApply.getDev_code(), applyStatus);
        }
    }

    /**
     * <AUTHOR>
     * @Description: 修改BankDirectApply
     * @time 09:15
     */
    protected void modifyBankDirectApply(BankDirectApply apply, Integer processStatus) {
        record(apply, processStatus);
        bankDirectApplyMapper.updateByPrimaryKeySelective(new BankDirectApply().setId(apply.getId()).setProcess_status(processStatus).setExtra(apply.getExtra()));
    }

    /**
     * <AUTHOR>
     * @Description: 使用extra中process字段记录process_status状态变更过程
     * @time 09:15
     */
    protected void record(BankDirectApply apply, Integer processStatus) {
        Map<String, Object> extraMap = apply.getExtraMap();
        if (MapUtils.isEmpty(extraMap)) {
            return;
        }
        //原有记录
        final String processStr = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.PROCESS);
        List<ProcessStatus> processList = Lists.newArrayList();
        if (!StringUtils.isEmpty(processStr)) {
            processList = JSONObject.parseArray(processStr, ProcessStatus.class);
        }
        if (processList.stream().anyMatch(x -> Objects.equals(x.getProcessStatus(), processStatus))) {
            return;
        }
        final ProcessStatus process = ProcessStatus.builder().processStatus(processStatus).updateAt(System.currentTimeMillis()).desc(PsbcProcessStatusEnum.getDesc(processStatus)).build();
        if (!CollectionUtils.isEmpty(processList)) {
            //添加新纪录
            processList.add(process);
            extraMap.put(BankDirectApplyConstant.Extra.PROCESS, processList);
        } else {
            final ArrayList<ProcessStatus> list = Lists.newArrayList(process);
            extraMap.put(BankDirectApplyConstant.Extra.PROCESS, list);
        }
        apply.setExtra(JSONObject.toJSONString(extraMap));
    }


    /**
     * <AUTHOR>
     * @Description: 使用extra中view_process字段记录状态变化供crm显示
     * @time 09:15
     */
    protected void recordViewProcess(BankDirectApply apply, Integer viewStatus, Date date) {
        if (Objects.isNull(apply)) {
            return;
        }
        Map<String, Object> extraMap = apply.getExtraMap();
        if (MapUtils.isEmpty(extraMap)) {
            return;
        }
        //原有记录
        final String processStr = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.VIEW_PROCESS);
        if (StringUtils.isEmpty(processStr)) {
            //初始化所有信息
            final BankHandleService handleService = factory.getBankHandleService(apply.getDev_code());
            if(Objects.isNull(handleService)) {
                return;
            }
            final List<ViewProcess> viewProcesses = handleService.initViewProcess(apply.getMerchant_sn());
            if (CollectionUtils.isEmpty(viewProcesses)) {
                return;
            }
            final Map<Integer, ViewProcess> processMap = viewProcesses.stream().collect(Collectors.toMap(x -> x.getViewStatus(), x -> x, (key1, key2) -> key1));
            //传过来的状态不存在
            if (!processMap.containsKey(viewStatus)) {
                return;
            }
            //初始化 这一步也是为了兼容之前没有的数据这样就不需要清洗数据了
            viewProcesses.stream().forEach(process -> {
                if (process.getViewStatus() <= viewStatus) {
                    process.setFinish(Boolean.TRUE);
                    final String timeTemplate = process.getTime();
                    final String time = timeTemplate.replace("#", StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", date));
                    process.setTime(time);
                }
            });
            extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, viewProcesses);
        } else {
            final List<ViewProcess> processes = JSONObject.parseArray(processStr, ViewProcess.class);
            //状态已经处于使用中就直接返回,避免长时间调用数据库
            final boolean match = processes.stream().anyMatch(x -> Objects.equals(x.getViewStatus(), viewStatus) && x.getFinish());
            if (match) {
                return;
            }
            processes.stream().forEach(process -> {
                if (process.getViewStatus() <= viewStatus && !process.getFinish()) {
                    process.setFinish(Boolean.TRUE);
                    final String timeTemplate = process.getTime();
                    final String time = timeTemplate.replace("#", StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", date));
                    process.setTime(time);
                }
            });
            extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, processes);
        }
        apply.setExtra(JSONObject.toJSONString(extraMap));
        bankDirectApplyMapper.updateByPrimaryKeySelective(apply);
    }

    /**
     * 推迟任务
     *
     * @param apply
     * @param minutes 分钟
     */
    protected void delayApply(BankDirectApply apply, int minutes) {
        final Long applyId = apply.getId();
        final BankDirectApply directApply = new BankDirectApply();
        directApply.setId(applyId);
        directApply.setPriority(DateUtils.addMinutes(new Date(), minutes));
        bankDirectApplyMapper.updateByPrimaryKeySelective(directApply);
    }


    /**
     * 当前件银行使用的卡为非默认则删除
     *
     * @param bankPreId pre表主键ID
     * @param remark
     */
    public void deletedMerchantBankAccountPre(String bankPreId, String remark) {
        if (StringUtils.isEmpty(bankPreId)) {
            return;
        }
        try {
            Map bankAccountPre = merchantBankService.getMerchantBankAccountPre(bankPreId);
            if (MapUtils.isEmpty(bankAccountPre)) {
                return;
            }
            //不是默认卡则要删除
            if (MapUtils.getInteger(bankAccountPre, MerchantBankAccountPre.DEFAULT_STATUS, MerchantBankAccountPre.DEFAULT_STATUS_FALSE) != MerchantBankAccountPre.DEFAULT_STATUS_TRUE) {
                bankService.deletedMerchantBankAccountPre(CollectionUtil.hashMap("id", bankPreId,
                        "operator", "job", "platform", "job", "remark", remark));
            }
        } catch (Exception e) {
            log.error("删除银行卡失败:bankPreId:{}", bankPreId, e);
        }
    }
}
