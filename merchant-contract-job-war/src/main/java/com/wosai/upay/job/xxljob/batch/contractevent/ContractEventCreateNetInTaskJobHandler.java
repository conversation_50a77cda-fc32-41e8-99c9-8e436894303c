package com.wosai.upay.job.xxljob.batch.contractevent;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.handlers.EventHandlerContext;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.wosai.upay.job.util.StringUtil.formatDate;

/**
 * xxl_job_desc: ContractEvent-创建入网任务
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("ContractEventCreateNetInTaskJobHandler")
public class ContractEventCreateNetInTaskJobHandler extends AbstractBatchJobHandler<ContractEvent> {

    @Autowired
    private ContractEventMapper contractEventMapper;
    @Autowired
    private EventHandlerContext eventHandlerContext;

    @Override
    public List<ContractEvent> queryTaskItems(BatchJobParam param) {
        return contractEventMapper.selectNetInEvent(formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
    }

    @Override
    public String getLockKey(ContractEvent contractEvent) {
        return "ContractEventCreateNetInTaskJobHandler:" + contractEvent.getId();
    }

    @Override
    public void doHandleSingleData(ContractEvent contractEvent) {
        try {
            contractEvent = contractEventMapper.selectByPrimaryKey(contractEvent.getId());
            if (Objects.equals(ContractEvent.STATUS_PENDING, contractEvent.getStatus())) {
                eventHandlerContext.handle(contractEvent);
            }
        } catch (Exception e) {
            log.warn("createTasks error {}", JSON.toJSONString(contractEvent), e);
        }
    }
}
