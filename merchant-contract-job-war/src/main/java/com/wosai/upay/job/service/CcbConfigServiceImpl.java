package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.CcbConfigBiz;
import com.wosai.upay.job.biz.IndustryMappingCommonBiz;
import com.wosai.upay.job.mapper.IndustryCodeV2Mapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.ccbConfig.*;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vo.ApiRequestParam;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/11/21
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class CcbConfigServiceImpl implements CcbConfigService {

    @Autowired
    MerchantService merchantService;

    @Autowired
    private DistrictsServiceV2 districtsServiceV2;

    @Autowired
    private CcbConfigBiz ccbConfigBiz;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;


    @Override
    public ContractResponse createCcbConfig(CreateCcbConfigReq req) {
        try {
            District district = districtsServiceV2.getDistrict(CollectionUtil.hashMap("code", req.getDistrictCode()));
            if (district == null) {
                return new ContractResponse().setSuccess(false).setMsg("地区code对应的省市信息不存在");
            }
            CcbConfig ccbConfig = ccbConfigBiz.getCcbConfigByDistrictCode(req.getDistrictCode());
            if (ccbConfig != null) {
                return new ContractResponse().setSuccess(false).setMsg("地区code对应的配置信息已存在");
            }
            ccbConfigBiz.addCcbConfig(req, district.getProvince_name(), district.getCity_name());
        } catch (Exception e) {
            log.error("创建建行配置异常: {}", JSON.toJSONString(req), e);
            return new ContractResponse().setSuccess(false).setMsg(e.getMessage());
        }
        return new ContractResponse().setSuccess(true);
    }

    @Override
    public ContractResponse updateCcbConfig(UpdateCcbConfigReq req) {
        try {
            CcbConfig ccbConfig = ccbConfigBiz.getCcbConfigById(req.getId());
            if (ccbConfig == null) {
                return new ContractResponse().setSuccess(false).setMsg("id对应的配置信息不存在");
            }
            ccbConfigBiz.updateCcbConfig(ccbConfig, req);
        } catch (Exception e) {
            log.error("更新建行配置异常: {}", JSON.toJSONString(req), e);
            return new ContractResponse().setSuccess(false).setMsg(e.getMessage());
        }
        return new ContractResponse().setSuccess(true);
    }

    @Override
    public ContractResponse deleteCcbConfig(DeleteCcbConfigReq req) {
        try {
            CcbConfig ccbConfig = ccbConfigBiz.getCcbConfigById(req.getId());
            if (ccbConfig == null) {
                return new ContractResponse().setSuccess(false).setMsg("id对应的配置信息不存在");
            }
            ccbConfigBiz.deleteCcbConfig(ccbConfig, req);
        } catch (Exception e) {
            log.error("删除建行配置异常: {}", JSON.toJSONString(req), e);
            return new ContractResponse().setSuccess(false).setMsg(e.getMessage());
        }
        return new ContractResponse().setSuccess(true);
    }

    @Override
    public ListResult<CcbConfig> getCcbConfig(QueryCcbConfigReq req) {
        List<CcbConfig> ccbConfig = ccbConfigBiz.getCcbConfig(req);
        return new ListResult<>(ccbConfig);
    }

    @Override
    public ListResult<CcbConfigChangeHistory> getChangeHistoryByCcbConfigId(QueryCcbChangeHistoryReq req) {
        List<CcbConfigChangeHistory> ccbConfigChangeHistories = ccbConfigBiz.getCcbConfigChangeHistoryByCcbConfigId(req);
        return new ListResult<>(ccbConfigChangeHistories);
    }

    @Override
    public CcbConfig getConfigByCode(ApiRequestParam<QueryCcbConfigCodeReq> req) {
        QueryCcbConfigCodeReq bodyParams = req.getBodyParams();
        CcbConfig config = ccbConfigBiz.getCcbConfigByDistrictCode(bodyParams.getCity());
        if (Objects.isNull(config)) {
            config = ccbConfigBiz.getCcbConfigByDistrictCode(bodyParams.getProvince());
        }
        return config;
    }

    @Override
    public CcbConfig getConfigByLocation(ApiRequestParam<String> districtFullName) {
        return getConfigByFullName(districtFullName.getBodyParams());
    }

    @Override
    public CcbConfig getConfigByMerchantId(String merchantId) {
        MerchantInfo merchantInfo = merchantService.getMerchantById(merchantId, null);
        return getConfigByFullName(String.format("%s %s", merchantInfo.getProvince(), merchantInfo.getCity()));
    }

    @Override
    public String getCcbMccByIndustryId(String industryId) {
        String wmAlyMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);
        if (StringUtils.isEmpty(wmAlyMcc)) {
            return "";
        }
        Map mccConverter = applicationApolloConfig.getMccConverter();
        return BeanUtil.getPropString(mccConverter, "ccb" + "." + wmAlyMcc, wmAlyMcc);
    }

    private CcbConfig getConfigByFullName(String fullName) {
        District district = districtsServiceV2.getCodeByName(fullName);
        if (Objects.isNull(district)) {
            return null;
        }
        CcbConfig config = ccbConfigBiz.getCcbConfigByDistrictCode(district.getCity_code());
        if (Objects.isNull(config)) {
            config = ccbConfigBiz.getCcbConfigByDistrictCode(district.getProvince_code());
        }
        return config;
    }

}
