package com.wosai.upay.job.refactor.biz.provider;

import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 结算通道业务处理
 * todo 待重构
 *
 * <AUTHOR>
 * @date 2024/1/17 15:45
 */
@Component
public class McProviderBiz {

    @Resource
    private McProviderDAO mcProviderDAO;


    /**
     * 给定结算通道列表,判断是否有属于给定的目标收单机构
     *
     * @param providers 结算通道标识列表
     * @param targetAcquirer 目标收单机构
     * @return true:有
     */
    public boolean existProvidersBelongToAcquirer(Set<String> providers, String targetAcquirer) {
        if (CollectionUtils.isEmpty(providers) || StringUtils.isBlank(targetAcquirer)) {
            return false;
        }
        return mcProviderDAO.listByProviders(providers).stream().map(McProviderDO::getAcquirer)
                .filter(StringUtils::isNotBlank).anyMatch(t -> StringUtils.equals(t.trim(), targetAcquirer.trim()));
    }

    /**
     * 根据结算通道标识获取收单机构
     *
     * @param provider 结算通道标识
     * @return 收单机构
     */
    public String getAcquirerByProvider(String provider) {
        if (StringUtils.isBlank(provider)) {
            return null;
        }
        return mcProviderDAO.getByProvider(provider).map(McProviderDO::getAcquirer).orElse(null);
    }

}
