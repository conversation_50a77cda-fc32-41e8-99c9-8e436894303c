package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/8
 */
@Component
@Slf4j
public class OtherCommonBizCheck {
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Value("${x-env-flag}")
    private String envFlag;

    public void check(String merchantSn, String merchantId, String sourceAcquirer, String targetAcquirer) {
        List<Map> list = applicationApolloConfig.getChangeAcquirerBizCheck();
        if (WosaiCollectionUtils.isEmpty(list)) {
            return;
        }
        ChangeAcquirerCheckRequest request = new ChangeAcquirerCheckRequest()
                .setMerchant_id(merchantId)
                .setMerchant_sn(merchantSn)
                .setSource_acquirer(sourceAcquirer)
                .setTarget_acquirer(targetAcquirer);

        for (Map map : list) {
            String url = WosaiMapUtils.getString(map, "url");
            String method = WosaiMapUtils.getString(map, "method");
            String biz = WosaiMapUtils.getString(map, "biz");
            ChangeAcquirerCheckResponse response = getCheckResponse(biz, url, method, request);
            if (!response.isAllowed()) {
                throw new CommonPubBizException(response.getMsg());
            }
        }

    }

    public ChangeAcquirerCheckResponse getCheckResponse(String biz, String url, String methodName, ChangeAcquirerCheckRequest request) {

        JsonRpcHttpClient client = getClient(url);
        Map<String, String> headers = new HashMap<>();
        headers.put("x-env-flag", envFlag);

        List params = new ArrayList<>();
        params.add(request);

        ChangeAcquirerCheckResponse response;
        try {
            response = client.invoke(methodName, params, ChangeAcquirerCheckResponse.class, headers);
        } catch (Throwable throwable) {
            log.error("切换收单机构检查 biz: {} url: {} method: {} request: {}", biz, url, methodName, JSON.toJSONString(request), throwable);
            throw new CommonPubBizException(String.format("调用%s检查接口异常：%s", biz, throwable.getMessage()));
        }
        log.info("切换收单机构检查 biz: {} url: {} method: {} request: {} response: {}", biz, url, methodName, JSON.toJSONString(request), JSON.toJSONString(response));
        return response;
    }

    private JsonRpcHttpClient getClient(String url) {
        URL serviceUrl = null;
        try {
            serviceUrl = new URL(url);
        } catch (MalformedURLException e) {
            log.error("", e);
        }
        JsonRpcHttpClient client = new JsonRpcHttpClient(new MyObjectMapper(), serviceUrl, new HashMap<>());
        client.setConnectionTimeoutMillis(10000);
        client.setReadTimeoutMillis(10000);
        return client;
    }

    @Data
    @Accessors(chain = true)
    private static class ChangeAcquirerCheckRequest {

        private String merchant_id;

        private String merchant_sn;

        private String source_acquirer;

        private String target_acquirer;

    }

    @Data
    @Accessors(chain = true)
    private static class ChangeAcquirerCheckResponse {

        private boolean allowed;

        private String msg;

    }
}
