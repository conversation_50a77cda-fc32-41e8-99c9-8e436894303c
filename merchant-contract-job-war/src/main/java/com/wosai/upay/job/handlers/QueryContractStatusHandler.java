package com.wosai.upay.job.handlers;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.common.utils.WosaiDateTimeUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.job.constant.CommonConstants;
import com.wosai.upay.job.enume.ChannelDelayQueryConfigEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.util.ChatBotUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.Objects;

import static javax.management.timer.Timer.*;

/**
 * @Description: 主动查询进件状态，并根据不同状态做业务处理
 * <AUTHOR>
 * @Date 2021/4/15 16:16
 */
@Component
@Slf4j
public class QueryContractStatusHandler extends AbstractSubTaskHandler {
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Override
    public boolean supports(ContractTask task, ContractSubTask subTask) {
        return false;
    }

    @Override
    protected void handleError(ContractTask task, ContractSubTask subTask, Exception e) {

    }

    @Override
    public void doHandle(ContractTask task, ContractSubTask subTask) {
        if (StringUtils.isEmpty(subTask.getContract_id()) || StringUtils.isEmpty(subTask.getMerchant_sn())) {
            log.info("queryContractStatus data contractId {},merchantSn {}", subTask.getContract_id(), subTask.getMerchant_sn());
            return;
        }
        HandleQueryStatusResp resp = queryStatusHandle(subTask);
        if (!resp.isSuccess() && !resp.isFail() && DateUtils.addDays(subTask.getCreate_at(), 60).before(new Date())) {
            resp.setFail(true)
                    .setRetry(false)
                    .setMessage("超过60天，自动失败");
            log.info("超过60天，自动失败 {} {} {}", task.getMerchant_sn(), task.getId(), subTask.getId());
        }
        handTaskAndSubTask(subTask, resp);
    }

    public void handTaskAndSubTask(ContractSubTask subTask, HandleQueryStatusResp resp) {
        //统一逻辑
        Long taskId = subTask.getP_task_id();
        Long subTaskId = subTask.getId();
        // 入网成功
        if (resp.isSuccess()) {
            //把这个子任务 下游任务标记为可调度
            contractSubTaskMapper.setEnableScheduleByDepId(subTaskId);
            //获取最新信息
            subTask = contractSubTaskMapper.selectByPrimaryKey(subTaskId);
            //当前子任务设置为成功，preStatus可能是 1 也有可能是 10
            contractSubTaskMapper.setSubTaskResult(1, 5, subTask.getResponse_body(), "已审核", subTaskId);
            contractSubTaskMapper.setSubTaskResult(10, 5, subTask.getResponse_body(), "已审核", subTaskId);
            if (subTask.getStatus_influ_p_task() == 1) {
                //更新父任务 affect_status_success_task_count 如果全成功了 更新status
                addAffectStatusSuccessTaskCountBiz.addAffectStatusSuccessTaskCount(taskId);
                //更新后的task
                ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
                if (!contractTask.getStatus().equals(TaskStatus.PROGRESSING.getVal()) && !contractTask.getStatus().equals(TaskStatus.FAIL.getVal())) {
                    taskResultService.changeStatusAndResultV2(taskId, subTaskId, contractTask.getStatus(), null, false);
                }
            }
        } else if (resp.isFail()) {
            String result = JSON.toJSONString(MapUtil.hashMap("message", resp.getMessage(), "result", resp.getMessage()));
            subTask.setStatus(6).setResult(result);
            contractSubTaskMapper.updateByPrimaryKey(subTask);
            //如果影响主任务则修改主任务状态
            if (subTask.getStatus_influ_p_task() == 1) {
                taskResultService.changeStatusAndResultV2(taskId, subTaskId, TaskStatus.FAIL.getVal(), result, false);
            }
        } else {
            ContractSubTask updateValue = new ContractSubTask()
                    .setId(subTask.getId())
                    .setPriority(ChannelDelayQueryConfigEnum.getNextPriority(subTask.getCreate_at().getTime(), subTask.getChannel()));
            contractSubTaskMapper.updateByPrimaryKey(updateValue);
        }
    }

    /**
     * @param subTask
     * @return
     */
    protected HandleQueryStatusResp queryStatusHandle(ContractSubTask subTask) {
        String channelName = subTask.getChannel();
        if (StringUtils.isEmpty(subTask.getContract_rule())) {
            log.info("subTask do not have contract rule : {}", subTask);
            chatBotUtil.sendMessageToContractWarnChatBot("subTask do not have contract rule, sub_task_id: " + subTask.getId());
            return new HandleQueryStatusResp()
                    .setFail(true)
                    .setMessage("子任务没有报备规则");
        } else {
            BasicProvider provider = providerFactory.getProviderByName(channelName);
            return doQueryStatus(provider, subTask);
        }
    }

    private HandleQueryStatusResp doQueryStatus(BasicProvider provider, ContractSubTask contractSubTask) {
        Assert.notNull(provider, "根据任务规则获取provider为空");
        return provider.queryAndHandleContractStatus(contractSubTask);
    }
}
