package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 收钱吧商户状态规则
 *
 * <AUTHOR>
 * @date 2025/8/28
 */
@Component
public class SqbMerchantStatusRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private MerchantService merchantService;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            int status = WosaiMapUtils.getIntValue(context.getMerchant(), Merchant.STATUS);
            if (Merchant.STATUS_ENABLED != status) {
                return createFailureResult(
                        String.format("商户状态不正常 %s，不允许执行", status), "SQB_MERCHANT_STATUS_BLOCKED");
            }
            return createSuccessResult("商户状态正常，通过检查");

        } catch (Exception e) {
            logger.error("执行收钱吧商户状态规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.SQB_MERCHANT_STATUS;
    }
}
