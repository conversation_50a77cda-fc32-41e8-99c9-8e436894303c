package com.wosai.upay.job.biz;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Agent;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.avro.MerchantContractSuccess;
import com.wosai.upay.job.avro.UpdateBankAccount;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.DO.PendingTasks;
import com.wosai.upay.job.model.acquirer.ByPassTradeConfig;
import com.wosai.upay.job.model.acquirer.MerchantTradeConfig;
import com.wosai.upay.job.providers.DefaultChangeTradeParamsBiz;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.acquirer.ChangeToLklV3Biz.LKLORG_CHANNEL_NO_LIST;
import static com.wosai.upay.job.biz.comboparams.ProviderParamsHandle.SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY;

/**
 * 对交易保活备用通道的处理
 *
 * <AUTHOR>
 * @date 2022/9/22
 */
@Slf4j
@Component
public class BackAcquirerBiz {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private AgentAppidBiz agentAppidBiz;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;

    @Autowired
    private PendingTasksBiz pendingTasksBiz;

    @Autowired
    private ContractTaskMapper taskMapper;

    @Autowired
    private ContractSubTaskMapper subTaskMapper;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private MerchantProviderParamsMapper providerParamsMapper;

    @Autowired
    DefaultChangeTradeParamsBiz defaultChangeTradeParamsBiz;
    @Autowired
    @Qualifier("backAcquirerSendMsgThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor backAcquirerSendMsgThreadPoolTaskExecutor;

    @Resource(name = "kafkaTemplate")
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String MERCHANT_CONTRACT_SUCCESS = "events.merchant-contract-job.contract-success";

    private static final String UPDATE_BANK_ACCOUNT = "events.merchant-contract-job.update-bank-account";

    public void handleContractTask(ContractTask task, int status) {
        // 如果是进行中的任务则不处理
        if (!TaskStatus.isFinish(status)) {
            return;
        }
        //小微升级触发的新增商户入网
        if(ProviderUtil.CONTRACT_TYPE_INSERT.equals(task.getType()) &&
                StrUtil.contains(task.getRule_group_id(), ContractRuleConstants.RULE_GROUP_MICROUPGRADE)) {
            return;

        }
        // 只异步处理新增商户入网和结算账户信息变更的任务
        if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(task.getType()) || ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equals(task.getType())) {
            backAcquirerSendMsgThreadPoolTaskExecutor.submit(() -> {
                try {
                    // 发送新增商户入网成功的消息
                    if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(task.getType()) && status == TaskStatus.SUCCESS.getVal()) {
                        sendMerchantContractSuccess(task);
                    }
                    // 发送更新银行卡的消息
                    if (ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equals(task.getType())) {
                        sendUpdateBankAccount(task, status);
                    }
                } catch (Exception e) {
                    log.error("通道保活发送消息失败, sn: {} id: {} type: {} ", task.getMerchant_sn(), task.getId(), task.getType(), e);
                }
            });
        }
    }

    private void sendUpdateBankAccount(ContractTask task, int status) {
        Map context = task.getEventContext();
        String merchantId = BeanUtil.getPropString(context, "merchant.id");
        List<ContractSubTask> subTasks = subTaskMapper.selectByPTaskId(task.getId());
        String acquirer = contractStatusMapper.selectByMerchantSn(task.getMerchant_sn()).getAcquirer();
        for (ContractSubTask subTask : subTasks) {
            // 如果该子任务对应的收单机构和当前商户的收单机构一致，则一定是匹配的
            if (acquirer.contains(getAcquirerFromGroupId(subTask.getRule_group_id()))) {
                UpdateBankAccount updateBankAccount = new UpdateBankAccount(task.getMerchant_sn(), merchantId, getAcquirerFromGroupId(subTask.getRule_group_id()), 1);
                sendMessage(UPDATE_BANK_ACCOUNT, updateBankAccount);
            } else {
                // 如果都是成功，则说明同步成功了
                if (TaskStatus.SUCCESS.getVal() == status && TaskStatus.SUCCESS.getVal().intValue() == subTask.getStatus()) {
                    UpdateBankAccount updateBankAccount = new UpdateBankAccount(task.getMerchant_sn(), merchantId, getAcquirerFromGroupId(subTask.getRule_group_id()), 1);
                    sendMessage(UPDATE_BANK_ACCOUNT, updateBankAccount);
                } else {
                    pendingTasksBiz.insertUpdateBankAccountMessage(subTask);
                }
            }
        }
    }

    public void handleUpdateBankAccount(PendingTasks pendingTasks) {
        Map eventMsg = JSON.parseObject(pendingTasks.getEvent_msg(), Map.class);
        long subTaskId = BeanUtil.getPropLong(eventMsg, "subTaskId");
        ContractSubTask subTask = subTaskMapper.selectByPrimaryKey(subTaskId);
        // 如果这个子任务还没有结束，就delay一下
        if (!TaskStatus.isFinish(subTask.getStatus())) {
            pendingTasksBiz.delayUpdateBankAccount(pendingTasks);
            return;
        }
        ContractTask task = taskMapper.selectByPrimaryKey(subTask.getP_task_id());
        Map context = task.getEventContext();
        String merchantId = BeanUtil.getPropString(context, "merchant.id");
        // 如果主任务是成功的，子任务也是成功的,则说明是一致的
        if (TaskStatus.SUCCESS.getVal().intValue() == task.getStatus() && TaskStatus.SUCCESS.getVal().intValue() == subTask.getStatus()) {
            UpdateBankAccount updateBankAccount = new UpdateBankAccount(task.getMerchant_sn(), merchantId, getAcquirerFromGroupId(subTask.getRule_group_id()), 1);
            sendMessage(UPDATE_BANK_ACCOUNT, updateBankAccount);
        } else {
            // 实时查询一下是否一致
            Map merchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(merchantId);
            String acquirer = ProviderUtil.switchRuleGroupIdToAcquirer(subTask.getRule_group_id());
            Boolean result = composeAcquirerBiz.getAcquirerBiz(acquirer).bankAccountConsistent(task.getMerchant_sn(), merchantBankAccount);
            UpdateBankAccount updateBankAccount = new UpdateBankAccount(task.getMerchant_sn(), merchantId, getAcquirerFromGroupId(subTask.getRule_group_id()), result ? 1 : 0);
            sendMessage(UPDATE_BANK_ACCOUNT, updateBankAccount);
        }
        // 结束pending tasks
        pendingTasksBiz.finishUpdateBankAccount(pendingTasks);
    }

    private void sendMerchantContractSuccess(ContractTask task) {
        String acquirer = getAcquirerFromGroupId(task.getRule_group_id());

        Map context = task.getEventContext();
        String merchantId = BeanUtil.getPropString(context, "merchant.id");
        MerchantContractSuccess merchantContractSuccess = new MerchantContractSuccess();
        merchantContractSuccess.setMerchantSn(task.getMerchant_sn());
        merchantContractSuccess.setMerchantId(merchantId);
        merchantContractSuccess.setAcquirer(acquirer);
        merchantContractSuccess.setBankChannelFlag(StringUtils.equalsAnyIgnoreCase(acquirer, "lkl", "lklV3", "tonglian", "ums") ? "0" : "1");
        sendMessage(MERCHANT_CONTRACT_SUCCESS, merchantContractSuccess);
    }

    /**
     * 获取收单机构，将lkl\lklV3 转换成lkl
     *
     * @param groupId 报备规则组
     * @return 规则组对应的收单机构
     */
    private String getAcquirerFromGroupId(String groupId) {
        String acquirer = ProviderUtil.switchRuleGroupIdToAcquirer(groupId);
        acquirer = ProviderUtil.externalAcquirer(acquirer);
        return acquirer;
    }

    private void sendMessage(String topic, Object message) {
        try {
            kafkaTemplate.send(topic, message);
            log.info("发送消息成功, topic:{} {}", topic, message);
        } catch (Exception e) {
            log.error("发送消息失败, topic:{} {}", topic, message, e);
        }
    }

    public List<ByPassTradeConfig> buildByPassTradeConfig(String merchantId, List<Integer> payways) {
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        // 先获取lklV3的merchant_provider_params
        MerchantProviderParams acquirerParams = providerParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        if (acquirerParams == null) {
            log.error("不存在lkl备用通道交易参数");
            return Lists.newArrayList();
        }
        List<ByPassTradeConfig> list = new ArrayList<>();
        for (Integer payway : payways) {
            MerchantProviderParams providerParams = null;
            if (payway.equals(PaywayEnum.ALIPAY.getValue())) {
                providerParams = getAliParams(merchantSn);
            }
            if (payway.equals(PaywayEnum.WEIXIN.getValue())) {
                providerParams = getWxParams(merchantSn);
            }
            if (payway.equals(PaywayEnum.UNIONPAY.getValue())) {
                providerParams = getUnionOpenParams(merchantSn);
            }
            if (providerParams == null) {
                log.error("备用通道交易参数 is null: {} {}", merchantSn, payway);
                continue;
            }
            ByPassTradeConfig config = doBuildByPassTradeConfig(merchant, providerParams, acquirerParams.getProvider_merchant_id());
            if (config != null) {
                list.add(config);
            }
        }
        return list;
    }

    private ByPassTradeConfig doBuildByPassTradeConfig(Map merchant, MerchantProviderParams providerParams, String providerMerchantId) {
        String agentName = agentAppidBiz.getAgentName(providerParams.getPayway(), providerParams.getProvider(), providerParams.getChannel_no(), BeanUtil.getPropString(merchant, Merchant.CITY));
        if (StringUtil.empty(agentName)) {
            log.error("备用通道 agent name is null:{}", JSON.toJSONString(providerParams));
            return null;
        }
        ByPassTradeConfig byPassTradeConfig = new ByPassTradeConfig()
                .setPayway(providerParams.getPayway())
                .setProvider(providerParams.getProvider())
                .setB2c_agent_name(agentName)
                .setC2b_agent_name(agentName)
                .setApp_agent_name(agentName)
                .setWap_agent_name(agentName)
                .setMini_agent_name(agentName)
                .setH5_agent_name(agentName);
        String tradeParamsKey = CommonModel.PROVIDER_KEY.get(String.valueOf(providerParams.getProvider()));
        Map params = null;
        if (providerParams.getPayway().equals(PaywayEnum.ALIPAY.getValue())) {
            params = CollectionUtil.hashMap(TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, providerParams.getPay_merchant_id(),
                    TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId);
        }
        if (providerParams.getPayway().equals(PaywayEnum.WEIXIN.getValue())) {
            params = CollectionUtil.hashMap(
                    TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID, providerParams.getPay_merchant_id(),
                    TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId);
        }
        if (providerParams.getPayway().equals(PaywayEnum.UNIONPAY.getValue())) {
            Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(BeanUtil.getPropString(merchant, DaoConstants.ID), null);
            Map merchantConfigParams = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
            String lklTermId = BeanUtil.getPropString(merchantConfigParams, "lakala_trade_params.lakala_term_id");
            if (ProviderEnum.PROVIDER_UION_OPEN.getValue().equals(providerParams.getProvider())) {
                params = CollectionUtil.hashMap(
                        TransactionParam.UNION_PAY_OPEN_MCH_ID, providerParams.getPay_merchant_id(),
                        TransactionParam.UNION_PAY_OPEN_TERM_ID, lklTermId);
            }
            if (ProviderEnum.PROVIDER_LKL_OPEN.getValue().equals(providerParams.getProvider())) {
                params = CollectionUtil.hashMap(
                        TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID, providerParams.getPay_merchant_id(),
                        TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_ID, lklTermId);
            }
        }
        byPassTradeConfig.setParams(CollectionUtil.hashMap(tradeParamsKey, params));
        return byPassTradeConfig;
    }

    private MerchantProviderParams getAliParams(String merchantSn) {
        // 先取新渠道的参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(ProviderEnum.PROVIDER_LKLORG.getValue())
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = providerParamsMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isEmpty(params)) {
            return null;
        }
        // 有多个支付宝交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams aliParam : params) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(aliParam.getAuth_status())) {
                return aliParam;
            }
        }
        return params.get(0);
    }

    private MerchantProviderParams getWxParams(String merchantSn) {
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        wxExample.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(ProviderEnum.PROVIDER_LKLORG.getValue())
                .andChannel_noIn(LKLORG_CHANNEL_NO_LIST)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        wxExample.setOrderByClause("ctime desc");
        List<MerchantProviderParams> wxParams = providerParamsMapper.selectByExampleWithBLOBs(wxExample);
        if (WosaiCollectionUtils.isEmpty(wxParams)) {
            return null;
        }
        // 有多个微信交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams wxParam : wxParams) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(wxParam.getAuth_status())) {
                return wxParam;
            }
        }
        return wxParams.get(0);
    }

    private MerchantProviderParams getUnionOpenParams(String merchantSn) {
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        wxExample.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(ProviderEnum.PROVIDER_LKL_OPEN.getValue())
                .andPaywayEqualTo(PaywayEnum.UNIONPAY.getValue())
                .andDeletedEqualTo(false);
        wxExample.setOrderByClause("ctime desc");
        List<MerchantProviderParams> unionParams = providerParamsMapper.selectByExampleWithBLOBs(wxExample);
        if (WosaiCollectionUtils.isEmpty(unionParams)) {
            MerchantProviderParamsExample wxExample2 = new MerchantProviderParamsExample();
            wxExample2.or().andMerchant_snEqualTo(merchantSn)
                    .andProviderEqualTo(ProviderEnum.PROVIDER_UION_OPEN.getValue())
                    .andPaywayEqualTo(PaywayEnum.UNIONPAY.getValue())
                    .andDeletedEqualTo(false);
            wxExample2.setOrderByClause("ctime desc");
            unionParams = providerParamsMapper.selectByExampleWithBLOBs(wxExample2);
        }
        if (WosaiCollectionUtils.isEmpty(unionParams)) {
            return null;
        }
        return unionParams.get(0);
    }

    /**
     * 根据merchant_provider_params获取merchant_config想关参数
     * @param paramsList
     * @return
     */
    public List<MerchantTradeConfig> buildTradeConfig(List<MerchantProviderParams> paramsList) {
        List<MerchantTradeConfig> result = paramsList.stream().map(merchantProviderParams -> {
            String merchantSn = merchantProviderParams.getMerchant_sn();
            Integer payway = merchantProviderParams.getPayway();
            Integer provider = merchantProviderParams.getProvider();
            String providerMerchantId = merchantProviderParams.getProvider_merchant_id();
            String payMerchantId = merchantProviderParams.getPay_merchant_id();
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            String merchantId = BeanUtil.getPropString(merchant, com.wosai.data.dao.DaoConstants.ID);

            String agentName = agentAppidBiz.getAgentName(payway, provider, merchantProviderParams.getChannel_no(), BeanUtil.getPropString(merchant, Merchant.CITY));
            if (StringUtil.empty(agentName)) {
                log.error("buildTradeConfig agent name is null:{}", JSON.toJSONString(merchantProviderParams));
                return null;
            }

            MerchantTradeConfig byPassTradeConfig = new MerchantTradeConfig();
            byPassTradeConfig.setB2c_formal(MerchantConfig.STATUS_CLOSED)
                    .setC2b_formal(MerchantConfig.STATUS_CLOSED)
                    .setWap_formal(MerchantConfig.STATUS_CLOSED)
                    .setMini_formal(MerchantConfig.STATUS_CLOSED)
                    .setApp_formal(MerchantConfig.STATUS_CLOSED)
                    .setExtend2_formal(MerchantConfig.STATUS_CLOSED)
                    .setPayway(payway)
                    .setProvider(provider)
                    .setB2c_agent_name(agentName)
                    .setC2b_agent_name(agentName)
                    .setApp_agent_name(agentName)
                    .setWap_agent_name(agentName)
                    .setMini_agent_name(agentName)
                    .setH5_agent_name(agentName);

            Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName(SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY);

            Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
            Map params = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS);
            String weixinMiniAppid = "";
            String weixinMiniAppSecret = "";
            // 如果以前配置过小程序appid, 切换到新通道也要配置
            if (payway.equals(PaywayEnum.WEIXIN.getValue())) {
                String oldProvider = BeanUtil.getPropString(merchantConfig, MerchantConfig.PROVIDER);
                String paramsKey = BeanUtil.getPropString(providerTradeParamsKey, oldProvider);
                weixinMiniAppid = BeanUtil.getPropString(params, paramsKey + ".weixin_mini_sub_appid");
                weixinMiniAppSecret = BeanUtil.getPropString(params, paramsKey + ".weixin_mini_sub_appsecret");
            }
            String tradeParamKey = BeanUtil.getPropString(providerTradeParamsKey, String.valueOf(provider));

            Map<String, Object> tradeParams = (Map) BeanUtil.getNestedProperty(tradeConfigService.getAgentByName(agentName), String.format("%s.%s", Agent.PARAMS, tradeParamKey));
            String lklTermId = BeanUtil.getPropString(params, TransactionParam.UNION_PAY_OPEN_TERM_ID);

            Map merchantTradeParams = defaultChangeTradeParamsBiz.getMerchantTradeParams(provider, payway, providerMerchantId, payMerchantId, null, null,
                    weixinMiniAppid, weixinMiniAppSecret, tradeParams, lklTermId, merchantProviderParams);
            if (merchantTradeParams == null) {
                return byPassTradeConfig;
            }

            Map map = CollectionUtil.hashMap(tradeParamKey, merchantTradeParams);
            byPassTradeConfig.setParams(map);
            return byPassTradeConfig;

        }).filter(Objects::nonNull).collect(Collectors.toList());
        return result;
    }
}
