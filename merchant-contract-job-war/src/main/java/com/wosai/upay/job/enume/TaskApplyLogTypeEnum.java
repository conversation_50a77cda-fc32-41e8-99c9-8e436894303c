package com.wosai.upay.job.enume;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
public enum TaskApplyLogTypeEnum implements ITextValueEnum<Integer> {

    BATCH_RE_CONTRACT(400, "批量报备"),
    BATCH_IMPORT_PARAMS(401, "批量通过名单导入配置交易参数"),
    BATCH_IMPORT_PARAMS_BY_RULE(402, "批量通过条件筛选配置交易参数"),
    HXB_SUB_MCH_STATUS_QUERY(462, "华夏子商户状态查询"),
    BATCH_IMPORT_ALI_CROSS_CITY_MERCHANTS(466, "批量导入支付宝跨城收款名单"),
    BATCH_IMPORT_UMS(467, "批量导入银商参数并切换"),
    ;

    private Integer value;
    private String text;

    TaskApplyLogTypeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
