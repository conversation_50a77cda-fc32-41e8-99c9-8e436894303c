package com.wosai.upay.job.refactor.biz.acquirer.bcs;

import org.springframework.stereotype.Service;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;

@Service
public class BcsAcquireFacade extends AbstractAcquirerHandler {
    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.BCS;
    }
}
