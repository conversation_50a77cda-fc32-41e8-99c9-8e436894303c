package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractTaskTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.job.Constants.ApproveConstant;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.acquirer.AbstractAcquirerChangeBiz;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.McBatchTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.changeAcquirerApprove.ChangeAcquirerApproveDTO;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.enums.NetInSceneEnum;
import com.wosai.upay.job.refactor.service.McRulesDecisionService;
import com.wosai.upay.job.service.ContractEventService;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.job.util.ExcelUtil;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Slf4j
abstract class AbstractMcBatchTaskJobHandler extends AbstractDirectJobHandler {

    @Autowired
    protected McBatchTaskMapper mcBatchTaskMapper;
    @Autowired
    protected ErrorCodeManageBiz errorCodeManageBiz;
    @Autowired
    protected CallBackService callBackService;
    @Autowired
    protected MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    protected ContractEventService contractEventService;
    @Autowired
    protected McRulesDecisionService mcRulesDecisionService;
    @Autowired
    protected ApplicationContext applicationContext;
    @Autowired
    protected ContractEventMapper contractEventMapper;
    @Autowired
    protected ContractTaskMapper contractTaskMapper;
    @Autowired
    protected ContractStatusService contractStatusService;
    @Autowired
    protected RuleContext ruleContext;
    @Autowired
    protected ExcelUtil excelUtil;
    @Autowired
    protected LogService logService;
    @Autowired
    @Qualifier("batchScheduleExecutorThreadPoolTaskExecutor")
    protected ThreadPoolTaskExecutor batchScheduleExecutorThreadPoolTaskExecutor;

    protected static final String BASE_DIR = "acquireChange/batch/";

    /**
     * 任务状态-执行成功
     */
    protected static final int APPLY_STATUS_EXCUTE_SUCCESS = 2;
    /**
     * 任务状态-执行失败
     */
    protected static final int APPLY_STATUS_EXCUTE_FAILURE = 3;

    protected static final int AUDIT_EXECUTE_SUCCESS = 1;
    protected static final int AUDIT_EXECUTE_FAIL = 0;


    protected ObjectMapper objectMapper = new ObjectMapper();

    protected void callBack(Map extra, String message, Integer resultType) {
        final ChangeAcquirerApproveDTO dto = objectMapper.convertValue(MapUtils.getMap(extra, ApproveConstant.APPROVE_INFO), ChangeAcquirerApproveDTO.class);
        final CallBackBean backBean = dto.getCallBackBean();

        backBean.setResultType(resultType);
        String type = "sp_msg";
        final ErrorInfo errorInfo = errorCodeManageBiz.getPromptMessageFromErrorCodeManager(type, message, ErrorCodeManageBiz.PLATFORM_ACQUIRER_APPROVE);
        backBean.setMessage(errorInfo.getMsg());
        callBackService.addComment(backBean);
    }

    protected Long doApplyOnlyContract(String merchantSn, String target) {
        final AbstractAcquirerChangeBiz changeBiz = getTargetChangeBiz(merchantSn, target);
        final McAcquirerChange change = new McAcquirerChange();
        change.setMerchant_sn(merchantSn);
        change.setTarget_acquirer(target);

        validateContract(change, changeBiz);
        validatePendingTasks(merchantSn, target);
        validateBankOrg(changeBiz, target);
        validateContractStatus(merchantSn);
        validateEligibilityToAcquirer(merchantSn, target);

        ContractEvent contractEvent = contractEventService.saveContractEvent(change.getMerchant_sn(), changeBiz.getContractGroup(merchantSn), "merchant-contract-job");
        return contractEvent.getId();
    }

    private void validateEligibilityToAcquirer(String merchantSn, String target) {
        ContractGroupRuleVerifyResultBO verifyResultBO = mcRulesDecisionService.checkMerchantEligibilityToAcquirer(merchantSn, target, NetInSceneEnum.CHANNEL_SWITCHING);
        if (!verifyResultBO.isCheckPass()) {
            throw new CommonPubBizException("商户不符合收单机构 " + target + " 准入标准");
        }
    }

    private void validateContract(McAcquirerChange change, AbstractAcquirerChangeBiz changeBiz) {
        if (changeBiz.hasContract(change)) {
            throw new CommonPubBizException(String.format("商户%s已经成功入网%s,请勿重新发起入网", change.getMerchant_sn(), change.getTarget_acquirer()));
        }
    }

    private void validatePendingTasks(String merchantSn, String target) {
        List<ContractEvent> contractEvents = contractEventMapper.selectEventTodoByMerchantSnAndType(merchantSn, ContractEvent.OPT_TYPE_NET_IN);
        if (WosaiCollectionUtils.isNotEmpty(contractEvents)) {
            throw new CommonPubBizException("正在处理入网任务,请稍等重试");
        }

        List<ContractTask> contractTasks = contractTaskMapper.getContractsBySnAndType(merchantSn, ContractTaskTypeEnum.NEW_MERCHANT_ONLINE.getValue());
        boolean match = contractTasks.parallelStream()
                .filter(task -> !Arrays.asList(TaskStatus.FAIL.getVal(), TaskStatus.SUCCESS.getVal()).contains(task.getStatus()))
                .anyMatch(task -> {
                    final RuleGroup ruleGroup = ruleContext.getRuleGroup(task.getRule_group_id());
                    if (ruleGroup == null) {
                        log.warn("RuleGroup is null for task with rule group id: {}", task.getRule_group_id());
                        return false;
                    }

                    if (!StringUtils.isEmpty(ruleGroup.getAcquirer())) {
                        return Objects.equals(ruleGroup.getAcquirer(), target);
                    } else {
                        // 处理通过切换收单机构新增入网的商户
                        final List<RuleItem> rules = ruleGroup.getRules();
                        if (rules == null || rules.isEmpty()) {
                            log.warn("No rules found in RuleGroup with id: {}", task.getRule_group_id());
                            return false;
                        }
                        return rules.parallelStream()
                                .anyMatch(rule -> Objects.isNull(rule.getDepend_on())
                                        && Objects.nonNull(rule.getContractRule())
                                        && Objects.equals(rule.getContractRule().getAcquirer(), target));
                    }
                });

        if (match) {
            throw new CommonPubBizException("已有入网任务,请勿申请");
        }
    }

    private void validateBankOrg(AbstractAcquirerChangeBiz changeBiz, String target) {
        boolean bankOrg = changeBiz.isBankOrg(target);
        if (bankOrg) {
            throw new CommonPubBizException(String.format("商户不允许通过切换入网银行收单机构%s", target));
        }
    }

    private void validateContractStatus(String merchantSn) {
        int contractStatus = contractStatusService.getContractStatus(merchantSn);
        if (Objects.equals(ContractStatus.STATUS_BIZ_FAIL, contractStatus)) {
            throw new CommonPubBizException("间连扫码开通失败不允许通过这种方式入网");
        }
    }

    /**
     * 获取要切换收单机构的相关能力
     *
     * @param merchantSn
     * @param target
     * @return
     */
    public AbstractAcquirerChangeBiz getTargetChangeBiz(String merchantSn, String target) {
        //兼容lkl升级v3问题
        if (target.contains(AcquirerTypeEnum.LKL.getValue())) {
            //1,当前商户报过的所有收单机构
            MerchantProviderParamsExample example = new MerchantProviderParamsExample();
            example.or().andMerchant_snEqualTo(merchantSn).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andDeletedEqualTo(false);
            List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);
            final boolean existLkl = records.stream().anyMatch(merchantProviderParam -> Objects.equals(merchantProviderParam.getContract_rule(), "lkl"));
            final boolean existLklV3 = records.stream().anyMatch(merchantProviderParam -> Objects.equals(merchantProviderParam.getContract_rule(), "lklV3"));
            if (existLkl && !existLklV3) {
                target = AcquirerTypeEnum.LKL.getValue();
            }
            if (existLklV3) {
                target = AcquirerTypeEnum.LKL_V3.getValue();
            }
        }

        AbstractAcquirerChangeBiz changeBiz = null;
        try {
            changeBiz = applicationContext.getBean(target + "-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
        } catch (Exception e) {
            log.error("商户:{},选择的收单机构找不到处理类", merchantSn, e);
            throw new CommonPubBizException(String.format("商户 %s 选择的收单机构找不到处理类", merchantSn));
        }
        return changeBiz;
    }
}
