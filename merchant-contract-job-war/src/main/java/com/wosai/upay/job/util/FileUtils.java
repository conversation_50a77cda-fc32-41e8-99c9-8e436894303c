package com.wosai.upay.job.util;

import java.io.File;

/**
 * @Auther: hrx
 * @Date: 2019-08-26
 * @Description: com.wosai.upay.job.util
 * @version: 1.0
 */
public class FileUtils {

    public static void delete(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return;
        }
        try {
            File file = new File(filePath);
            if (file.exists()) {
                if (file.isDirectory()) {
                    if (file.listFiles().length != 0) {
                        File[] files = file.listFiles();
                        for (File tmpFile : files) {
                            delete(tmpFile.getAbsolutePath());
                        }
                    }
                }
                file.delete();
            }
        } catch (Exception e) {
        }
    }

}