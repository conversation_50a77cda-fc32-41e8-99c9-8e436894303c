package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.McRuleGroup;
import com.wosai.upay.job.model.DO.McRuleGroupExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface McRuleGroupMapper {
    int countByExample(McRuleGroupExample example);

    int deleteByExample(McRuleGroupExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(McRuleGroup record);

    int insertSelective(McRuleGroup record);

    List<McRuleGroup> selectByExampleWithBLOBs(McRuleGroupExample example);

    List<McRuleGroup> selectByExample(McRuleGroupExample example);

    McRuleGroup selectByPrimaryKey(Integer id);

    McRuleGroup selectByGroupId(@Param("groupId") String groupId);

    int updateByExampleSelective(@Param("record") McRuleGroup record, @Param("example") McRuleGroupExample example);

    int updateByExampleWithBLOBs(@Param("record") McRuleGroup record, @Param("example") McRuleGroupExample example);

    int updateByExample(@Param("record") McRuleGroup record, @Param("example") McRuleGroupExample example);

    int updateByPrimaryKeySelective(McRuleGroup record);

    int updateByGroupIdSelective(McRuleGroup record);

    int updateByPrimaryKeyWithBLOBs(McRuleGroup record);

    int updateByPrimaryKey(McRuleGroup record);
}