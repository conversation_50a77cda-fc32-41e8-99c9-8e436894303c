package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * xxl_job_desc: 银行直连-待签约派工
 * 待签约派发工单
 * <AUTHOR>
 * @date 2025/4/23
 */
@Slf4j
@Component("BankDirectWaitForSignJobHandler")
public class BankDirectWaitForSignJobHandler extends AbstractBankDirectJobHandler {

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${sign_task_template_id}")
    private Long signTaskTemplateId;

    @Override
    public String getLockKey() {
        return "BankDirectWaitForSignJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            final List<BankDirectApply> applyList = bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(Lists.newArrayList(BankDirectApplyConstant.ProcessStatus.CONTRACT_APPLYING), StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
            if (CollectionUtils.isEmpty(applyList)) {
                return;
            }
            applyList.forEach(apply -> {
                final String merchantSn = apply.getMerchant_sn();
                try {
                    if (ShutdownSignal.isShuttingDown()) {
                        return;
                    }
                    //华夏,平安无需派工
                    final Integer bankRef = apply.getBank_ref();
                    if (Objects.equals(bankRef, BankDirectApplyRefEnum.HXB.getValue()) || Objects.equals(bankRef, BankDirectApplyRefEnum.PAB.getValue())) {
                        return;
                    }
                    //获取主任务
                    final Long taskId = apply.getTask_id();
                    //收单机构进件任务
                    final List<ContractSubTask> acquireSubTask = contractSubTaskMapper.getAcquireSubTask(taskId);
                    final ContractSubTask subTask = acquireSubTask.get(0);
                    final Date updateAt = subTask.getUpdate_at();
                    final String redisKey = taskId + ":" + "process_status:signStartTaskForRpc";
                    //超过7天或者已经派工的直接返回
                    if (DateUtils.addDays(updateAt, 7).before(new Date()) || redisTemplate.hasKey(redisKey)) {
                        return;
                    }
                    //记录一下派工了几次,超过两次也不需要派工
                    final String countKey = "waitSign" + ":" + apply.getId();
                    String count = redisTemplate.opsForValue().get(countKey);
                    if (!StringUtils.isEmpty(count) && Long.valueOf(count) >= 2) {
                        log.info("待签约派工超过两次applyId:{}", apply.getId());
                        return;
                    }
                    //判断状态
                    final Boolean toBeSigned = checkBankContractToBeSigned(subTask);
                    //不是待签约状态不需要派工
                    if (!toBeSigned) {
                        return;
                    }
                    final String acquire = BeanUtil.getPropString(apply.getExtraMap(), BankDirectApplyConstant.Extra.ACQUIRE);
                    final String acquireName = mcAcquirerDAO.getAcquirerName(acquire);
                    doStartTaskForRpc(acquireName, merchantSn, signTaskTemplateId);
                    //添加key
                    redisTemplate.opsForValue().set(redisKey, String.valueOf(taskId), 7L, TimeUnit.DAYS);
                    //记录派工次数
                    redisTemplate.expire(countKey, 90, TimeUnit.DAYS);
                    redisTemplate.opsForValue().increment(countKey);
                } catch (Exception exception) {
                    log.error("银行直连商户:{},签约派工", merchantSn, exception);
                }
            });
        } catch (Exception e) {
            log.error("signStartTaskForRpc exception", e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("银行直连商户签约派工异常:%s", e.getMessage()));
        }
    }

    /**
     * 判断当前申请是不是处于待签约
     *
     * @param subTask
     */
    private Boolean checkBankContractToBeSigned(ContractSubTask subTask) {
        try {
            final String channelName = subTask.getChannel();
            BasicProvider provider = providerFactory.getProviderByName(channelName);
            //是否处于待签约状态
            return provider.checkBankContractToBeSigned(subTask);
        } catch (Exception e) {
            log.info("checkBankContractToBeSigned exception", e);
        }
        return Boolean.FALSE;
    }
}
