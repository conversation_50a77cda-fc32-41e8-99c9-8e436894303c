package com.wosai.upay.job.biz.bankDirect;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class IcbcDirectBiz extends AbstractBankDirectApplyBiz{

    @Value("${icbc_dev_code}")
    public String icbcDevCode;
    @Override
    protected String chooseGroupByContextAndDevCode(Map<String, Object> context, String devCode) {
        return McConstant.RULE_GROUP_ICBC;
    }

    @Override
    protected Map<String, Object> completeContext(Map<String, Object> paramContext, BankDirectReq bankDirectReq) {
        return paramContext;
    }

    @Override
    protected Integer getBankRef(String dev_code) {
        return BankDirectApplyRefEnum.ICBC.getValue();
    }

    @Override
    protected String getAcquire() {
        return AcquirerTypeEnum.ICBC.getValue();
    }

    @Override
    protected Integer getProvider() {
        return ProviderEnum.PROVIDER_ICBC.getValue();
    }

    @Override
    public String getDevCode() {
        return icbcDevCode;
    }
}
