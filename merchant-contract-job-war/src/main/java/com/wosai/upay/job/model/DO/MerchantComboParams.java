package com.wosai.upay.job.model.DO;

import com.wosai.upay.job.model.combo.ComboSubStatus;
import com.wosai.upay.job.model.combo.MerchantComboParam;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

@Data
@Accessors(chain = true)
public class MerchantComboParams {
    private Long id;

    private String provider_params_id;

    private String merchant_sn;

    private Long trade_app_id;

    private Long combo_id;

    private Long combo_detail_id;

    private Integer provider;

    private Integer formal_status;

    private Integer payway;

    private String pay_merchant_id;

    private Integer b2c_in_use;

    private String b2c_fee_rate;

    private Integer c2b_in_use;

    private String c2b_fee_rate;

    private Integer wap_in_use;

    private String wap_fee_rate;

    private Integer mini_in_use;

    private String mini_fee_rate;

    private Integer app_in_use;

    private String app_fee_rate;

    private Integer h5_in_use;

    private String h5_fee_rate;

    private Long ctime;

    private Long mtime;

    private byte[] params;

    private Integer status;

    public MerchantComboParam toComboModule() {
        MerchantComboParam merchantComboParam = new MerchantComboParam();
        BeanUtils.copyProperties(this, merchantComboParam);
        return merchantComboParam;
    }

    public Boolean checkValid() {
        Boolean b2cInUse = (this.b2c_in_use != null && this.b2c_in_use == ComboSubStatus.USING.getVal());
        Boolean c2bInUse = (this.c2b_in_use != null && this.c2b_in_use == ComboSubStatus.USING.getVal());
        Boolean wapInUse = (this.wap_in_use != null && this.wap_in_use == ComboSubStatus.USING.getVal());
        Boolean miniInUse = (this.mini_in_use != null && this.mini_in_use == ComboSubStatus.USING.getVal());
        Boolean appInUse = (this.app_in_use != null && this.app_in_use == ComboSubStatus.USING.getVal());
        Boolean h5InUse = (this.h5_in_use != null && this.h5_in_use == ComboSubStatus.USING.getVal());
        return b2cInUse || c2bInUse || wapInUse || miniInUse || appInUse || h5InUse;
    }

}