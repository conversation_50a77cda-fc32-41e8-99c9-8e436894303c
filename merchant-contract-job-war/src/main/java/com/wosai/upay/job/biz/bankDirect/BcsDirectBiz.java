package com.wosai.upay.job.biz.bankDirect;

import static com.wosai.upay.job.providers.PsbcProvider.replaceHttp;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.StoreExtService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.enume.BankDirectApplyViewStatusEnum;
import com.wosai.upay.job.model.MerchantParamReq;
import com.wosai.upay.job.model.ViewProcess;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.service.MerchantProviderParamsService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class BcsDirectBiz extends AbstractBankDirectApplyBiz {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private StoreExtService mcStoreExtService;

    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;

    @Value("${bcs_dev_code}")
    public String bcsDevCode;

    @Override
    protected String chooseGroupByContextAndDevCode(Map<String, Object> context, String devCode) {
        return McConstant.RULE_GROUP_BCS;
    }

    @Override
    protected Map<String, Object> completeContext(Map<String, Object> paramContext, BankDirectReq bankDirectReq) {
        // 需要加入商户的门头照、内景照、门头合照以及商户的营业执照
        Map merchant = merchantService.getMerchantBySn(bankDirectReq.getMerchant_sn());
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        String bindedStoreId = BeanUtil.getPropString(merchant, "binded_store_id", "");
        MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService
            .getMerchantBusinessLicenseByMerchantId(merchantId, bankDirectReq.getDev_code());
        Map<String, String> merchantPhoto = new HashMap<>();
        if (Objects.nonNull(merchantBusinessLicense)) {
            // 查询到对应的营业执照信息
            String photo = merchantBusinessLicense.getPhoto();
            merchantPhoto.put("business_license_photo", photo);
        }
        StotreExtInfoAndPictures storeExtAndPictures =
            mcStoreExtService.findStoreExtAndPicturesByStoreId(bindedStoreId, bankDirectReq.getDev_code());
        if (Objects.nonNull(storeExtAndPictures)) {
            // 门头合照
            PhotoInfo brandPhoto = storeExtAndPictures.getBrandPhoto();
            if (Objects.nonNull(brandPhoto)) {
                merchantPhoto.put("brand_photo", brandPhoto.getUrl());
            }
            // 门头照
            PhotoInfo brandOnlyScenePhoto = storeExtAndPictures.getBrandOnlyScenePhoto();
            if (Objects.nonNull(brandOnlyScenePhoto)) {
                merchantPhoto.put("brand_only_scene_photo", brandOnlyScenePhoto.getUrl());
            } else {
                // 如果没有门头照，则使用门头合照
                merchantPhoto.put("brand_only_scene_photo", brandPhoto.getUrl());
            }
            // 内景照
            PhotoInfo indoorOnlyScenePhoto = storeExtAndPictures.getIndoorOnlyScenePhoto();
            if (Objects.nonNull(indoorOnlyScenePhoto)) {
                merchantPhoto.put("indoor_only_scene_photo", indoorOnlyScenePhoto.getUrl());
            } else {
                merchantPhoto.put("indoor_only_scene_photo", brandPhoto.getUrl());
            }
        }
        Map<String, Object> photoParam = new HashMap<>();
        photoParam.put("photo_param", merchantPhoto);
        // 设置费率
        String formBody = bankDirectReq.getForm_body();
        Map<String, Object> formBodyMap = JSONObject.parseObject(formBody, new TypeReference<Map<String, Object>>() {});
        List<Map<String, Object>> config = JSONObject.parseObject(
            BeanUtil.getPropString(formBodyMap, "merchant_config"), new TypeReference<List<Map<String, Object>>>() {});
        if (CollectionUtils.isNotEmpty(config)) {
            Map<String, Object> objectMap = config.get(0);
            String rate = (String)objectMap.get("rate");
            paramContext.put("rate", rate);
        }
        paramContext.putAll(photoParam);
        return paramContext;
    }

    @Override
    protected Integer getBankRef(String dev_code) {
        return BankDirectApplyRefEnum.BCS.getValue();
    }

    @Override
    protected String getAcquire() {
        return AcquirerTypeEnum.BCS.getValue();
    }

    @Override
    protected Integer getProvider() {
        return ProviderEnum.PROVIDER_BCS.getValue();
    }

    @Override
    public String getDevCode() {
        return bcsDevCode;
    }

    @Override
    public List<ViewProcess> initViewProcess(String merchantSn) {
        log.info("BcsDirectBiz.initViewProcess: 初始化BCS银行直连流程, acquire={}", getAcquire());
        List<ViewProcess> viewProcesses = preViewProcess(this.getAcquire());
        log.info("BcsDirectBiz.initViewProcess: 获取到的BCS银行直连流程, viewProcesses={}", viewProcesses);
        // 对于BCS渠道，需要修改展示流程
        for (ViewProcess viewProcess : viewProcesses) {
            if (!viewProcess.getExtra()) {
                // 该状态流程不需要修改
                continue;
            }
            if (Objects.equals(viewProcess.getViewStatus(), BankDirectApplyViewStatusEnum.SIGNED_AUDITING.getValue())) {
                // 已签约
            } else if (Objects.equals(viewProcess.getViewStatus(), BankDirectApplyViewStatusEnum.AUTHING.getValue())) {
                // 银行已审核
                String imageUrl = getWXImageUrl(getAcquire(), getProvider());
                viewProcess.setExtraMessage(imageUrl);
                viewProcess.setAliMessage(
                    replaceHttp("https://images.wosaimg.com/5a/f4d2939942458aa9ea4fc878c0b53fb7d614fa.PNG"));
                // 补充微信和支付宝子商户号，需要先获取对应的交易参数
                MerchantParamReq merchantParamReq = new MerchantParamReq();
                merchantParamReq.setMerchant_sn(merchantSn);
                merchantParamReq.setProvider(getProvider());
                List<MerchantProviderParamsDto> merchantProviderParams =
                    merchantProviderParamsService.getMerchantProviderParams(merchantParamReq);
                // 设置支付宝商家号
                merchantProviderParams.stream()
                    .filter(merchantProviderParamsDto -> Objects.equals(merchantProviderParamsDto.getPayway(),
                        PaywayEnum.ALIPAY.getValue()))
                    .findFirst().ifPresent(alipayParam -> viewProcess.setAliMch(alipayParam.getPay_merchant_id()));
                // 设置微信商家号
                merchantProviderParams.stream()
                    .filter(merchantProviderParamsDto -> Objects.equals(merchantProviderParamsDto.getPayway(),
                        PaywayEnum.WEIXIN.getValue()))
                    .findFirst().ifPresent(weixinParam -> viewProcess.setWxMch(weixinParam.getPay_merchant_id()));
            }
        }
        return viewProcesses;
    }
}
