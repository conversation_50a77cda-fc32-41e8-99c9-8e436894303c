package com.wosai.upay.job.xxljob.direct;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.trade.service.result.QueryMchApplyLogsResult;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.CcbConfigBiz;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.CcbMerchantFeeRateMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.CcbMerchantFeeRate;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.ccbConfig.CcbConfig;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExcelUtil;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.ccb.response.CcbFeeDisList;
import com.wosai.upay.merchant.contract.model.ccb.response.CcbMerchantInfoResp;
import com.wosai.upay.merchant.contract.model.ccb.response.CcbRateList;
import com.wosai.upay.merchant.contract.model.provider.CcbParam;
import com.wosai.upay.merchant.contract.service.CcbService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import sun.misc.BASE64Encoder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * xxl_job_desc: 建行费率信息比对
 * <AUTHOR>
 * @date 2023/4/11
 */
@Slf4j
@Component("CcbMerchantFeeRateJobHandler")
public class CcbMerchantFeeRateJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private CcbService ccbService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private FeeRateService feeRateService;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private CcbConfigBiz ccbConfigBiz;

    @Autowired
    private CcbMerchantFeeRateMapper feeRateMapper;

    @Autowired
    private AcquirerChangeDao acquirerChangeDao;

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Value("${ccb_dev_code}")
    private String ccbDevCode;

    @Value("${mail-gateway}")
    private String mailGateway;

    private static final String KEY = "QueryCcbMerchantFeeRate";
    private static final String WH = "WH";
    private static final String WD = "WD";
    private static final String ZH = "ZH";
    private static final String ZD = "ZD";
    private static final String TH = "TH";
    private static final String TD = "TD";
    private static final String DJ = "DJ";
    private static final String FD = "FD";
    private final int batchSize = 500;
    private final RestTemplate restTemplate = new RestTemplate();
    private static final String[] HEADERS = {"收钱吧商户号", "建行商户号", "省", "市",
            "建行微信借记卡手续费", "建行微信贷记卡手续费", "建行支付宝借记卡手续费", "建行支付宝贷记卡手续费",
            "收钱吧微信手续费", "收钱吧支付宝手续费",
            "收钱吧侧微信分润比例", "建行微信借记卡分润比例", "建行微信贷记卡分润比例",
            "收钱吧侧支付宝分润比例", "建行支付宝借记卡分润比例", "建行支付宝贷记卡分润比例"};

    @Override
    public String getLockKey() {
        return KEY;
    }


    @Override
    public void execute(DirectJobParam param) {
        List<CcbConfig> allCcbConfigs = ccbConfigBiz.getAllCcbConfigs();
        Map<String, CcbConfig> ccbConfigs = allCcbConfigs.stream().collect(Collectors.toMap(CcbConfig::getDistrict_code, r -> r));
        Set<String> accounts = allCcbConfigs.stream().map(CcbConfig::getAccount).collect(Collectors.toSet());
        String queryDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        CcbParam ccbParam = contractParamsBiz.buildContractParams("ccb", CcbParam.class);
        List<CcbMerchantFeeRateExcel> excels = new ArrayList<>();
        long minId = 0;
        // 每次查询 batchSize 条数据
        while (true) {
            List<CcbMerchantFeeRate> rows = feeRateMapper.selectByIdRange(minId, batchSize);
            if (WosaiCollectionUtils.isEmpty(rows)) {
                break;
            }
            for (CcbMerchantFeeRate row : rows) {
                try {
                    CcbMerchantFeeRateExcel excel = handleCcbMerchantFeeRate(ccbParam, ccbConfigs, accounts, row, queryDate);
                    if (excel != null) {
                        excels.add(excel);
                    }
                } catch (Exception e) {
                    log.error("处理失败 {} ", row.getMerchant_sn(), e);
                }
            }
            minId = rows.get(rows.size() - 1).getId();
        }
        try {
            doSendEmail(excels);
        } catch (Exception e) {
            log.error("发送费率信息不一致的建行商户失败 ", e);
        }
    }

    public void doSendEmail(List<CcbMerchantFeeRateExcel> excels) {
        List<String[]> list = new ArrayList<>();
        list.add(HEADERS);
        for (CcbMerchantFeeRateExcel excel : excels) {
            list.add(new String[]{excel.getMerchantSn(), excel.getProviderMerchantId(), excel.getProvince(), excel.getCity(),
                    excel.getWh(), excel.getWd(), excel.getZh(), excel.getZd(),
                    excel.getSqbWxFeeRate(), excel.getSqbAliFeeRate(),
                    excel.getSqbWxRatio(), excel.getWhRatio(), excel.getWdRatio(),
                    excel.getSqbAliRatio(), excel.getZhRatio(), excel.getZdRatio()
            });
        }
        byte[] content = ExcelUtil.writeExcel("fee_rate_info", list);
        Map request = CollectionUtil.hashMap(
                //测试使用
                "to", "<EMAIL>,<EMAIL>",
                "id", 276
        );
        if (Objects.isNull(content) || WosaiCollectionUtils.isEmpty(excels)) {
            request.put("content", "<p>今日没有手续费及分润比例不一致的商户</p>");
        } else {
            request.put("content", "<p>手续费及分润比例不一致的商户见附件</p>");
            request.put("attachments", Collections.singletonList(
                    CollectionUtil.hashMap(
                            "name", "费率信息.xlsx",
                            "data", new BASE64Encoder().encode(content))
            ));
        }
        Map result = restTemplate.postForObject(mailGateway, request, Map.class);
        if (MapUtils.getInteger(result, "errcode") == 0) {
            log.info("发送费率信息不一致的建行商户成功");
        } else {
            log.error("发送费率信息不一致的建行商户失败, 原因 {}", MapUtils.getString(result, "errmsg"));
        }
    }

    private CcbMerchantFeeRateExcel handleCcbMerchantFeeRate(CcbParam ccbParam, Map<String, CcbConfig> ccbConfigMap, Set<String> accounts, CcbMerchantFeeRate oldCcbMerchantFeeRate, String queryDate) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(oldCcbMerchantFeeRate.getMerchant_sn());
        if (!WosaiStringUtils.equals(AcquirerTypeEnum.CCB.getValue(), contractStatus.getAcquirer())) {
            return null;
        }
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(oldCcbMerchantFeeRate.getMerchant_sn(), null);
        String districtCode = merchantInfo.getDistrict_code();
        CcbConfig ccbConfig = ccbConfigMap.get(districtCode.substring(0, districtCode.length() - 2) + "00");
        if (ccbConfig == null) {
            ccbConfig = ccbConfigMap.get(districtCode.substring(0, districtCode.length() - 4) + "0000");
        }
        if (ccbConfig == null) {
            log.error("查询建行配置失败 {}", oldCcbMerchantFeeRate.getMerchant_sn());
            return null;
        }
        CcbMerchantInfoResp ccbMerchantInfoResp = queryMerchantInfo(oldCcbMerchantFeeRate.getMerchant_sn(), ccbParam);
        if (ccbMerchantInfoResp == null) {
            log.error("查询建行商户信息失败 {}", oldCcbMerchantFeeRate.getMerchant_sn());
            return null;
        }
        String minPrice = "02".equals(ccbMerchantInfoResp.getPosGrp().get(0).getSetlAccNoInd()) ? ccbConfig.getPrivate_min_price() : ccbConfig.getPublic_min_price();
        // 更新数据库的信息
        CcbMerchantFeeRate update = buildNewFeeRateInfo(ccbMerchantInfoResp, accounts);
        update.setId(oldCcbMerchantFeeRate.getId());
        update.setQuery_date(queryDate);
        update.setMtime(System.currentTimeMillis());
        feeRateMapper.updateByPrimaryKeySelective(update);
        return checkFeeRate(oldCcbMerchantFeeRate, update, minPrice);
    }

    private CcbMerchantFeeRateExcel checkFeeRate(CcbMerchantFeeRate oldFeeRateInfo, CcbMerchantFeeRate newFeeRateInfo, String minPrice) {
        Map<Integer, String> sqbFeeRateInfo = getSqbFeeRateInfo(oldFeeRateInfo.getMerchant_sn());
        if (WosaiMapUtils.isEmpty(sqbFeeRateInfo)) {
            return null;
        }
        Double weixinFee = Double.valueOf(sqbFeeRateInfo.get(PaywayEnum.WEIXIN.getValue()));
        Double aliFee = Double.valueOf(sqbFeeRateInfo.get(PaywayEnum.ALIPAY.getValue()));
        String wxRatio = getRatio(sqbFeeRateInfo.get(PaywayEnum.WEIXIN.getValue()), minPrice);
        String aliRatio = getRatio(sqbFeeRateInfo.get(PaywayEnum.ALIPAY.getValue()), minPrice);
        // 如果wh和wd不一致，或者是其中任何一个和收钱吧建行通道微信费率不一致，就推送邮件
        if (!Objects.equals(newFeeRateInfo.getWh(), newFeeRateInfo.getWd())
                || !Objects.equals(Double.valueOf(newFeeRateInfo.getWh()), weixinFee)
                || !Objects.equals(Double.valueOf(newFeeRateInfo.getWd()), weixinFee)) {
            return buildCcbMerchantFeeRateExcel(oldFeeRateInfo, sqbFeeRateInfo, newFeeRateInfo, wxRatio, aliRatio);
        }
        // 如果zh和zd不一致，或者是其中任何一个和收钱吧建行通道支付宝费率不一致，就推送邮件
        if (!Objects.equals(newFeeRateInfo.getZh(), newFeeRateInfo.getZd())
                || !Objects.equals(Double.valueOf(newFeeRateInfo.getZh()), aliFee)
                || !Objects.equals(Double.valueOf(newFeeRateInfo.getZd()), aliFee)) {
            return buildCcbMerchantFeeRateExcel(oldFeeRateInfo, sqbFeeRateInfo, newFeeRateInfo, wxRatio, aliRatio);
        }
        if (!Objects.equals(newFeeRateInfo.getWh_ratio(), newFeeRateInfo.getWd_ratio())
                || !Objects.equals(newFeeRateInfo.getWh_ratio(), wxRatio)
                || !Objects.equals(newFeeRateInfo.getWd_ratio(), wxRatio)) {
            return buildCcbMerchantFeeRateExcel(oldFeeRateInfo, sqbFeeRateInfo, newFeeRateInfo, wxRatio, aliRatio);
        }
        if (!Objects.equals(newFeeRateInfo.getZh_ratio(), newFeeRateInfo.getZd_ratio())
                || !Objects.equals(newFeeRateInfo.getZh_ratio(), aliRatio)
                || !Objects.equals(newFeeRateInfo.getZd_ratio(), aliRatio)) {
            return buildCcbMerchantFeeRateExcel(oldFeeRateInfo, sqbFeeRateInfo, newFeeRateInfo, wxRatio, aliRatio);
        }

        return null;
    }

    private CcbMerchantFeeRateExcel buildCcbMerchantFeeRateExcel(CcbMerchantFeeRate oldFeeRateInfo, Map<Integer, String> sqbFeeRateInfo, CcbMerchantFeeRate newFeeRateInfo, String wxRatio, String aliRatio) {
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(oldFeeRateInfo.getMerchant_sn(), null);
        CcbMerchantFeeRateExcel feeRateExcel = new CcbMerchantFeeRateExcel();
        feeRateExcel.setMerchantSn(oldFeeRateInfo.getMerchant_sn());
        feeRateExcel.setProviderMerchantId(oldFeeRateInfo.getProvider_merchant_id());
        feeRateExcel.setProvince(merchantInfo.getProvince());
        feeRateExcel.setCity(merchantInfo.getCity());
        feeRateExcel.setWh(newFeeRateInfo.getWh());
        feeRateExcel.setWd(newFeeRateInfo.getWd());
        feeRateExcel.setZh(newFeeRateInfo.getZh());
        feeRateExcel.setZd(newFeeRateInfo.getZd());
        feeRateExcel.setSqbWxFeeRate(WosaiMapUtils.getString(sqbFeeRateInfo, PaywayEnum.WEIXIN.getValue()));
        feeRateExcel.setSqbAliFeeRate(WosaiMapUtils.getString(sqbFeeRateInfo, PaywayEnum.ALIPAY.getValue()));
        feeRateExcel.setSqbWxRatio(wxRatio);
        feeRateExcel.setWhRatio(newFeeRateInfo.getWh_ratio());
        feeRateExcel.setWdRatio(newFeeRateInfo.getWd_ratio());
        feeRateExcel.setSqbAliRatio(aliRatio);
        feeRateExcel.setZhRatio(newFeeRateInfo.getZh_ratio());
        feeRateExcel.setZdRatio(newFeeRateInfo.getZd_ratio());
        return feeRateExcel;
    }

    private String getRatio(String rate, String bottomPrice) {
        // 特殊情况: 如果结算底价是100,则分润是0
        if ("100".equals(bottomPrice)) {
            return "0";
        }
        BigDecimal rateBigDecimal = new BigDecimal(rate);
        BigDecimal bottomPriceBigDecimal = new BigDecimal(bottomPrice);
        BigDecimal result = (rateBigDecimal.subtract(bottomPriceBigDecimal).divide(rateBigDecimal, 2, RoundingMode.HALF_UP));
        return String.valueOf(Double.valueOf(result.doubleValue() * 100).intValue());
    }

    private Map<Integer, String> getSqbFeeRateInfo(String merchantSn) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus.getAcquirer().equals(AcquirerTypeEnum.CCB.getValue())) {
            List<ListMchFeeRateResult> feeRates = feeRateService.listMchEffectFeeRates(merchantSn);
            return feeRates.stream().filter(r -> StringUtils.contains(r.getTradeComboName(), "建设银行")).collect(Collectors.toMap(ListMchFeeRateResult::getPayWay, ListMchFeeRateResult::getBscFeeRate));
        }
        McAcquirerChange mcAcquirerChange = acquirerChangeDao.getLatestSuccessApply(merchantSn, AcquirerTypeEnum.CCB.getValue());
        if (mcAcquirerChange != null && WosaiStringUtils.isNotEmpty(mcAcquirerChange.getExtra()) && mcAcquirerChange.getExtra().contains("merchant_config")) {
            Map extra = CommonUtil.string2Map(mcAcquirerChange.getExtra());
            List<Map<String, Object>> config = (List<Map<String, Object>>) WosaiMapUtils.getMap(extra, "comboSnapshot").get("merchant_config");
            return config.stream().collect(Collectors.toMap(x -> WosaiMapUtils.getInteger(x, "payway"), x -> BeanUtil.getPropString(x, "rate")));
        }
        List<QueryMchApplyLogsResult> applyLogsResults = feeRateService.queryMchApplyLogs(merchantSn);
        if (WosaiCollectionUtils.isNotEmpty(applyLogsResults)) {
            String name = applicationApolloConfig.getBankTradeAppId();
            Map<Integer, String> feeRates = applyLogsResults.stream().filter(r -> Objects.equals(r.getTradeAppName(), name) && r.getDescription().contains("建设银行")).collect(Collectors.toMap(QueryMchApplyLogsResult::getPayWay, QueryMchApplyLogsResult::getFeeRate));
            if (WosaiMapUtils.isNotEmpty(feeRates)) {
                return feeRates;
            }
        }
        BankDirectApply apply = bankDirectApplyMapper.getApplyBySnAndDevCode(merchantSn, ccbDevCode);
        if (apply != null && WosaiStringUtils.isNotEmpty(apply.getForm_body()) && apply.getForm_body().contains("merchant_config")) {
            Map formBody = CommonUtil.string2Map(apply.getForm_body());
            List<Map<String, Object>> config = (List<Map<String, Object>>) formBody.get("merchant_config");
            return config.stream().collect(Collectors.toMap(x -> WosaiMapUtils.getInteger(x, "payway"), x -> BeanUtil.getPropString(x, "rate")));
        }
        return null;
    }


    private CcbMerchantFeeRate buildNewFeeRateInfo(CcbMerchantInfoResp ccbMerchantInfoResp, Set<String> accounts) {
        CcbMerchantFeeRate update = new CcbMerchantFeeRate();
        Map<String, String> feeRates = new HashMap<>();
        Map<String, String> ratios = new HashMap<>();
        for (CcbRateList ccbRateList : ccbMerchantInfoResp.getRateList()) {
            feeRates.put(ccbRateList.getAcqCmsnChrgRateTpCd(), ccbRateList.getFstLvlHdCgRate());
            Optional<CcbFeeDisList> ratio = ccbRateList.getFeeDisList().stream().filter(r -> accounts.contains(r.getPrDsbPty1ApntAccNo())).findFirst();
            ratios.put(ccbRateList.getAcqCmsnChrgRateTpCd(), ratio.map(CcbFeeDisList::getDcnPrDsbPyAlctPctgVal).orElse(null));
        }
        update.setWh(feeRates.get(WH));
        update.setWd(feeRates.get(WD));
        update.setZh(feeRates.get(ZH));
        update.setZd(feeRates.get(ZD));
        update.setTh(feeRates.get(TH));
        update.setTd(feeRates.get(TD));
        update.setDj(feeRates.get(DJ));
        update.setFd(feeRates.get(FD));
        update.setWh_ratio(ratios.get(WH));
        update.setWd_ratio(ratios.get(WD));
        update.setZh_ratio(ratios.get(ZH));
        update.setZd_ratio(ratios.get(ZD));
        return update;
    }

    private CcbMerchantInfoResp queryMerchantInfo(String merchantSn, CcbParam ccbParam) {
        ContractResponse contractResponse = ccbService.queryMerchantInfo(merchantSn, ccbParam);
        if (contractResponse.isSuccess()) {
            Map<String, Object> responseParam = contractResponse.getResponseParam();
            return JSON.parseObject(JSON.toJSONString(responseParam.get("dataInfo")), CcbMerchantInfoResp.class);
        }
        if (contractResponse.isSystemFail()) {
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException ignored) {
            }
            contractResponse = ccbService.queryMerchantInfo(merchantSn, ccbParam);
            if (contractResponse.isSuccess()) {
                Map<String, Object> responseParam = contractResponse.getResponseParam();
                return JSON.parseObject(JSON.toJSONString(responseParam.get("dataInfo")), CcbMerchantInfoResp.class);
            }
        }
        return null;
    }

    @Data
    public static class CcbMerchantFeeRateExcel {

        /**
         * 收钱吧商户号
         */
        private String merchantSn;

        /**
         * 建行商户号
         */
        private String providerMerchantId;
        /**
         * 省
         */
        private String province;
        /**
         * 市
         */
        private String city;
        /**
         * 建行微信借记卡手续费
         */
        private String wh;
        /**
         * 建行微信贷记卡手续费
         */
        private String wd;
        /**
         * 建行支付宝借记卡手续费
         */
        private String zh;
        /**
         * 建行支付宝贷记卡手续费
         */
        private String zd;
        /**
         * 收钱吧微信手续费
         */
        private String sqbWxFeeRate;
        /**
         * 收钱吧支付宝手续费
         */
        private String sqbAliFeeRate;
        /**
         * 收钱吧微信分润比例
         */
        private String sqbWxRatio;
        /**
         * 建行微信借记卡分润比例
         */
        private String whRatio;
        /**
         * 建行微信贷记卡分润比例
         */
        private String wdRatio;
        /**
         * 收钱吧支付宝分润比例
         */
        private String sqbAliRatio;
        /**
         * 建行支付宝借记卡分润比例
         */
        private String zhRatio;
        /**
         * 建行支付宝贷记卡分润比例
         */
        private String zdRatio;
    }
}

