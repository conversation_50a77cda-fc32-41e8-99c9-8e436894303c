package com.wosai.upay.job.refactor.biz.acquirer.fuyou;

import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;

/**
 * 富友收单机构独有能力
 */
public interface FuYouUniqueAbility {

    /**
     * 开通富友D0业务
     *
     * @param contractTaskDO 开通D0业务主任务
     * @param force          是否强制处理任务(强制处理不会判断状态以及是否超时,都会处理)
     */
    void openDayZero(ContractTaskDO contractTaskDO, boolean force);

    /**
     * 查询开通富友D0业务结果
     *
     * @param contractTaskDO 开通D0业务主任务
     * @param force          强制查询(不会校验是否已经处理过该任务和任务是否已经超时)
     */
    String queryOpenDayZeroResult(ContractTaskDO contractTaskDO, boolean force);

    /**
     * 从富友的进件报文中，获取结算类型
     * 获取不到从进件的阿波罗配置中获取默认值
     *
     * @param merchantSn 商户号
     * @return 结算类型
     */
    String getContractSettleTypeByMerchantSn(String merchantSn);
}
