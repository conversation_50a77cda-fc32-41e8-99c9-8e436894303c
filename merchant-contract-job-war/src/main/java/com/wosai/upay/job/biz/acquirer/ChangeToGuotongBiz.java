package com.wosai.upay.job.biz.acquirer;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 收单机构切换到国通
 *
 * <AUTHOR>
 * @date 2025/1/24
 */
@Component("guotong-AcquirerChangeBiz")
@Slf4j
public class ChangeToGuotongBiz extends AbstractIndirectAcquirerChangeBiz {

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_GUOTONG.getValue();
    }

    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_GUOTONG_RULE_GROUP;
    }

    @Override
    protected List<MerchantProviderParams> getDefaultChangeParams(McAcquirerChange change) {
        //云闪付、翼支付交易参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(getProviderCode(change.getTarget_acquirer()))
                .andPaywayNotIn(Arrays.asList(PaywayEnum.ACQUIRER.getValue(), PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()))
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        //支付宝交易参数
        params.add(getAliParams(change));
        params.add(getWxParams(change));
        return params;
    }


    /**
     * 获取支付宝参数
     *
     * @param change
     * @return
     */
    private MerchantProviderParams getAliParams(McAcquirerChange change) {
        //获取最新的支付宝参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(getProviderCode(change.getTarget_acquirer()))
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        params = params.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(params)) {
            throw new ContractBizException("缺少可用支付宝子商户号");
        }
        // 有多个支付宝交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams aliParam : params) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(aliParam.getAuth_status())) {
                return aliParam;
            }
        }
        return params.get(0);
    }

    /**
     * 获取微信参数
     *
     * @param change
     * @return
     */
    private MerchantProviderParams getWxParams(McAcquirerChange change) {
        // 先获取新渠道的微信参数
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        wxExample.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(getProviderCode(change.getTarget_acquirer()))
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        wxExample.setOrderByClause("ctime desc");
        List<MerchantProviderParams> wxParams = paramsMapper.selectByExampleWithBLOBs(wxExample);
        wxParams = wxParams.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(wxParams)) {
            throw new CommonPubBizException(String.format("商户号:%s,没有找到微信参数", change.getMerchant_sn()));
        }
        // 有多个微信交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams wxParam : wxParams) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(wxParam.getAuth_status())) {
                return wxParam;
            }
        }
        return wxParams.get(0);
    }
}
