package com.wosai.upay.job.biz;

import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.side.service.GeneralRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

/**
 * @Description:
 * <AUTHOR>
 * Date 2020/3/22 3:47 下午
 **/
@Component
public class CompleteDateBiz {

    @Autowired
    private GeneralRuleService generalRuleService;

    public Date getLklDate(Date date) {
        int hour = date.getHours();
        if (hour <= 17) {
            return new Date();
        }
        String reviewComplete = generalRuleService.getFirstWeekDayAfterDate(StringUtil.formatDate(date));
        return StringUtil.parseDate(reviewComplete);
    }

    /**
     * 获取的日期格式 yyyy-MM-dd
     */
    public String getPayCompleteTime(Date date, int minutes) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minutes);
        return StringUtil.formatDate(calendar.getTime());
    }

    /**
     * 获取的日期格式 yyyy-MM-dd
     */
    public String getWeekDay(Date date, int days) {
        String reviewComplete = "";
        for (int i = 0; i < days; i++) {
            reviewComplete = generalRuleService.getFirstWeekDayAfterDate(StringUtil.formatDate(date));
            date = StringUtil.parseDate(reviewComplete);
        }
        return reviewComplete;
    }
}
