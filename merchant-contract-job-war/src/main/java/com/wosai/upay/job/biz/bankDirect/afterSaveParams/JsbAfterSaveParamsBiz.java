package com.wosai.upay.job.biz.bankDirect.afterSaveParams;

import com.wosai.upay.job.biz.AgentAppidBiz;
import com.wosai.upay.job.model.ConfigWxCommonReq;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.service.JsbService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class JsbAfterSaveParamsBiz extends AbstractAfterSaveParamsHandleBiz {
    private static final String JSB_PROVIDER = "1047";
    @Autowired
    private AgentAppidBiz agentAppidBiz;
    @Autowired
    private JsbService jsbService;

    @Override
    public boolean supportProvider(String provider) {
        return JSB_PROVIDER.equals(provider);
    }

    @Override
    public void configWx(ConfigWxCommonReq req) {
        //
        try {
            WeixinConfig configs = agentAppidBiz.getConfig(req.getMerchantSn(), Integer.parseInt(JSB_PROVIDER), "*********", req.getWeixinMchId());

            jsbService.weixinSubdevConfig(configs);
        } catch (Exception e) {
            //江苏银行还没上线,为避免可能的异常,先catch 一下,保证导参数流程正常
            log.error("配置jsb微信目录异常,商户号: {} ,", req.getMerchantSn(), e);
        }


    }
}
