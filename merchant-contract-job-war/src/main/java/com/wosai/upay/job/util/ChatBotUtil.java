package com.wosai.upay.job.util;

import cn.hutool.core.util.PhoneUtil;
import com.shouqianba.cua.chatbot.ChatBot;
import com.shouqianba.cua.chatbot.client.ChatBotClient;
import com.shouqianba.cua.chatbot.client.FeishuChatBotClient;
import com.shouqianba.cua.chatbot.message.TextMessage;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.shouqianba.cua.enums.feishu.FeishuMemberEnum.LUZEQIANG;

/**
 * <AUTHOR>
 * @date 2024/8/8
 */
@Component
@Slf4j
public class ChatBotUtil {

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private Environment environment;

    private boolean isProd;

    @PostConstruct
    public void init() {
        isProd = Arrays.asList(environment.getActiveProfiles()).contains("prod");
    }

    private ChatBotClient chatBotClient = new FeishuChatBotClient();
    /**
     * 进件告警机器人
     */
    private ChatBot contractWarnChatBot = new ChatBot("https://open.feishu.cn/open-apis/bot/v2/hook/8499ed10-c80b-4d53-8e1b-a97858ada1a2", "1XRrFCkBsfC3g17i6zp1Dc", chatBotClient);

    /**
     * 开通多业务失败告警机器人
     */
    private ChatBot multiBizChatBot = new ChatBot("https://open.feishu.cn/open-apis/bot/v2/hook/54f8fc82-3a59-49a3-b89f-d4d67c324f04", "GpoJyQoMSZGS8GvCehinc", chatBotClient);

    /**
     * 建行告警机器人
     */
    private ChatBot ccbChatBot = new ChatBot("https://open.feishu.cn/open-apis/bot/v2/hook/4302b80d-9bb1-48e5-ac9c-03cc418d6e26", "6XV4jcHBdT061CWGUyGxyb", chatBotClient);

    /**
     * 华夏银行告警机器人
     */
    private ChatBot hxbChatBot = new ChatBot("https://open.feishu.cn/open-apis/bot/v2/hook/d080abca-9bc3-4e2b-8be1-09fb3d32206a", "a86Fr7eD11iR6WkNlxcHtd", chatBotClient);

    /**
     * 向收单机构同步商户状态告警机器人
     */
    private ChatBot syncMchStatusChatBot = new ChatBot("https://open.feishu.cn/open-apis/bot/v2/hook/e5772745-0446-41dc-b113-7db035fac6db", "6Wl6yLzgWzlaoLPtmy4YTb", chatBotClient);

    /**
     * 小微升级告警机器人
     */
    private ChatBot microUpgradeChatBot = new ChatBot("https://open.feishu.cn/open-apis/bot/v2/hook/33a580df-897a-4929-bb7d-3a50084c23f1", "oWnlEnVICeiDjV2EgnkDZb", chatBotClient);

    /**
     * 切换支付模式异常机器人
     */
    private ChatBot paymentModeChangeChatBot = new ChatBot("https://open.feishu.cn/open-apis/bot/v2/hook/37c685de-ba9a-4398-a08c-6aff36c4c695", "rrSNgZFOnGmwGbfuoR6W5e", chatBotClient);

    private ChatBot fuYouSpecIndustryApplyFailChatBot = new ChatBot("https://open.feishu.cn/open-apis/bot/v2/hook/79a58f19-e76c-4f4c-b2c7-9e06d4ef55fd", "onYulfl0BgoRhdgnNiq59d", chatBotClient);
    
    private final ChatBot settlementCardAlarmChatBot =
        new ChatBot("https://open.feishu.cn/open-apis/bot/v2/hook/9602ca14-beb7-4d74-aaae-1317968fa407",
            "CEtIT7jRzW70RaCoWEg9lc", chatBotClient);

    public void sendMessageToMicroUpgradeChatBot(String message) {
        try {
            if (!isProd) {
                message = "【测试环境】" + message;
            }
            if (skip(message)) {
                return;
            }
            microUpgradeChatBot.sendMessageImmediately(new TextMessage(hideSensitiveInfo(message)));
        } catch (Exception e) {
            log.warn("发送消息失败 {}", message, e);
        }
    }

    public void sendMessageToContractWarnChatBot(String message) {
        sendMessage(contractWarnChatBot, message);
    }

    public void sendMessageToMultiBizChatBot(String message) {
        sendMessage(multiBizChatBot, message);
    }

    public void sendMessageToCcbChatBot(String message) {
        sendMessage(ccbChatBot, message);
    }

    public void sendMessageToHxbChatBot(String message) {
        sendMessage(hxbChatBot, message);
    }

    public void sendMessageToSyncMchStatusChatBot(String message) {
        sendMessage(syncMchStatusChatBot, message);
    }

    public void sendMessageToFuYouFeeRateExceptionChatBot(String message) {
        sendMessageImmediately(fuYouSpecIndustryApplyFailChatBot, message);
    }

    /**
     * 发送消息到结算卡清算行号不支持告警机器人
     * 
     * @param message 告警信息
     */
    public void sendMessageToSettlementCardAlarmChatBot(String message) {
        sendMessageImmediately(settlementCardAlarmChatBot, message);
    }

    /**
     * 指定 @ 的群组成员
     */
    public void sendMessageToPaymentModeChangeChatBot(String message) {
        try {
            if (!isProd) {
                return;
            }
            TextMessage text = new TextMessage(hideSensitiveInfo(message));
            text.setAtMembers(Arrays.asList(LUZEQIANG));
            paymentModeChangeChatBot.sendMessageImmediately(text);
        } catch (Exception e) {
            log.warn("发送消息失败 {}", message, e);
        }
    }

    private void sendMessage(ChatBot chatBot, String message) {
        try {
            if (!isProd || skip(message)) {
                return;
            }
            chatBot.delaySendMessage(new TextMessage(hideSensitiveInfo(message)));
        } catch (Exception e) {
            log.warn("发送消息失败 {}", message, e);
        }
    }

    private void sendMessageImmediately(ChatBot chatBot, String message) {
        try {
            if (!isProd || skip(message)) {
                return;
            }
            chatBot.sendMessageImmediately(new TextMessage(hideSensitiveInfo(message)));
        } catch (Exception e) {
            log.warn("发送消息失败 {}", message, e);
        }
    }

    /**
     * 跳过某些消息
     *
     * @param message
     * @return
     */
    public boolean skip(String message) {
        if (WosaiStringUtils.isEmpty(message)) {
            return true;
        }
        List<String> list = applicationApolloConfig.getSkipDingMsg();
        return list.stream().anyMatch(s -> message.contains(s));
    }

    /**
     * 脱敏
     *
     * @return
     */
    private String hideSensitiveInfo(String text) {
        return hidePhone(text);
    }

    private static Pattern PHONE_PATTERN = Pattern.compile("\\b(1[3-9]\\d{9})\\b");
    /**
     * 手机号脱敏
     *
     * @param text
     * @return
     */
    private String hidePhone(String text) {
        Matcher matcher = PHONE_PATTERN.matcher(text);
        if (matcher.find()) {
            String phone = matcher.group();
            String obscured = PhoneUtil.hideBetween(phone).toString();
            return text.replace(phone, obscured);
        }
        return text;
    }
}
