package com.wosai.upay.job.util;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.service.SelfHelpNetInEventService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: l<PERSON><PERSON>qiang
 * @Date: 2019/6/6
 * @Description:
 */

@Component
public class RecordEventUtil {
    private final static Logger log = LoggerFactory.getLogger(RecordEventUtil.class);
    @Autowired
    @Lazy
    private SelfHelpNetInEventService selfHelpNetInEventService;

    /**
     * 更改表merchant生成事件
     */
    public void createEventAfterChangeMerchant(Map beforeData, Map afterData, String merchantSn, Map msg, String[] message) {
        log.info("createEventAfterChangeMerchant->beforeData  {},afterData {},merchantSn {}", beforeData, afterData, merchantSn);
        try {
            String oldMessage = "";
            String newMessage = "";
            String newMessageForEmpty = "";
            List dataList = new ArrayList();
            for (int i = 0; i < message.length; i++) {
                oldMessage = BeanUtil.getPropString(beforeData, message[i], "");
                newMessage = BeanUtil.getPropString(afterData, message[i], "");
                newMessageForEmpty = BeanUtil.getPropString(afterData, message[i]);
                if (newMessage == null || "".equals(newMessage)) {
                    newMessage = oldMessage;
                }
                //修正新纪录为为空的字段处理
                if ("".equals(newMessageForEmpty)) {
                    newMessage = newMessageForEmpty;
                }
                if (!oldMessage.equals(newMessage)) {
                    dataList.add(message[i]);
                }
            }
            if (dataList.size() > 0) {
                selfHelpNetInEventService.saveSelfHelpNetInEvent(merchantSn, ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION, msg, dataList, null);
            }
            log.info("createEventAfterChangeMerchant->merchantSn :{} msg : {},dataList : {} ", merchantSn, msg, dataList);
        } catch (Exception e) {
            log.error("更改表merchant生成事件异常merchantSn :{}", merchantSn, e);
        }
    }

    public boolean checkIsUpdate(Map<String, Object> beforeData, Map<String, Object> afterData, String[] keys) {
        for (String key : keys) {
            String beforeValue = BeanUtil.getPropString(beforeData, key, "");
            String afterValue = BeanUtil.getPropString(afterData, key, "");

            if (!Objects.equals(beforeValue, afterValue)) {
                return true;
            }
        }
        return false;
    }


}
