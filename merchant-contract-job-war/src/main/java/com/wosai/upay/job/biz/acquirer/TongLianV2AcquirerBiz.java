package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.alipay.api.domain.TimePeriod;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.acquirer.SyncMchStatusResp;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.providers.TongLianV2Provider;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.TongLianUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import com.wosai.upay.merchant.contract.model.weixin.SubdevConfigResp;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * @Description: TongLianV2AcquirerBiz
 * <AUTHOR>
 * @Date 2023/6/16 15:51
 **/
@Component("tonglianV2-biz")
@Slf4j
public class TongLianV2AcquirerBiz implements IAcquirerBiz {

    @Autowired
    TongLianV2Service tongLianV2Service;

    @Autowired
    TongLianV2Provider tongLianV2Provider;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    ParamContextBiz paramContextBiz;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private TongLianUtil tongLianUtil;

    @Autowired
    private MerchantBankService merchantBankService;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private ContractSubTaskDAO contractSubTaskDAO;

    @Autowired
    private McProviderDAO mcProviderDAO;

    @Autowired
    private RuleContext ruleContext;


    @Autowired
    private TradeConfigService tradeConfigService;

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_TONGLIAN_V2;
    }

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        return BeanUtil.getPropString(merchantConfig, "params.tl_syb_trade_params.cus_id");
    }

    @Override
    public SyncMchStatusResp syncMchStatusToAcquirer(String merchantSn, int status) {
        MerchantProviderParams params = getAcquirerParams(merchantSn);
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByParams(params, TongLianV2Param.class);
        ContractResponse response = tongLianV2Service.updateMerchantStatus(merchantSn, status, tongLianV2Param);
        if (response.isSuccess()) {
            // 更新子商户号状态
            MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(status == ValidStatusEnum.VALID.getValue() ? ValidStatusEnum.VALID.getValue() : ValidStatusEnum.INVALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
            merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
            return new SyncMchStatusResp().setSuccess(true).setMessage("success");
        }
        return new SyncMchStatusResp().setSuccess(Boolean.FALSE).setMessage(response.getMessage());
    }

    @Override
    public String getNormalWxRule() {
        return "tonglianV2-1035-3";
    }

    private final static String STATUS_SUCCESS = "02";

    @Override
    public Boolean getAcquirerMchStatus(String merchantSn) {
        MerchantProviderParams params = getAcquirerParams(merchantSn);
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByParams(params, TongLianV2Param.class);
        ContractResponse response = tongLianV2Service.queryMerchantStatus(merchantSn, tongLianV2Param);
        if (!response.isSuccess()) {
            throw new CommonPubBizException(response.getMessage());
        }
        String auditstatus = MapUtils.getString(response.getResponseParam(), "auditstatus");
        if (STATUS_SUCCESS.equals(auditstatus)) {
            if (!params.getMerchant_state().equals(ValidStatusEnum.VALID.getValue())) {
                MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(ValidStatusEnum.VALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
                merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
            }
            return true;
        }
        if (params.getMerchant_state().equals(ValidStatusEnum.VALID.getValue())) {
            MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(ValidStatusEnum.INVALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
            merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
        }
        return false;
    }

    @Override
    public Boolean bankAccountConsistent(String merchantSn, Map bankAccount) {
        MerchantProviderParams params = getAcquirerParams(merchantSn);
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByParams(params, TongLianV2Param.class);
        ContractResponse contractResponse = tongLianV2Service.queryMerchantInfo(merchantSn, tongLianV2Param);
        if (!contractResponse.isSuccess()) {
            throw new CommonPubBizException(contractResponse.getMessage());
        }
        String remoteNumber = MapUtils.getString(contractResponse.getResponseParam(), "acctid");
        String localNumber = BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.NUMBER);
        return Objects.equals(remoteNumber, localNumber);
    }

    @Override
    public WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByParams(providerParams, TongLianV2Param.class);
        SubdevConfigResp subdevConfigResp = tongLianV2Service.queryWeChatAppIdParams(providerParams.getPay_merchant_id(), tongLianV2Param);
        MchInfo mchInfo = null;
        try {
//            mchInfo = tonglianService.queryWeChatMchInfoParams(providerParams.getPay_merchant_id());
        } catch (Exception e) {
        }
        return new WxMchInfo().setMchInfo(mchInfo).setSubdevConfig(subdevConfigResp);
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        Optional<MerchantProviderParamsDO> unionParam = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.UNIONPAY.getValue());
        if (unionParam.isPresent()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                    .retry(false)
                    .build();
        }
        ContractSubTaskDO contractSubTaskDO = getUnionNetInTask(merchantSn, AcquirerTypeEnum.TONG_LIAN_V2.getValue());
        if (Objects.nonNull(contractSubTaskDO)) {
            if (TaskStatus.PROGRESSING.getVal().equals(contractSubTaskDO.getStatus()) || TaskStatus.PENDING.getVal().equals(contractSubTaskDO.getStatus())) {
                if (ChronoUnit.DAYS.between(contractSubTaskDO.getCreateAt().toLocalDateTime().toLocalDate(), LocalDate.now()) < 7) {
                    return UnionPayOpenStatusQueryResp.builder()
                            .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                            .message("开通中，请稍后重试")
                            .retry(true)
                            .build();
                } else {
                    return UnionPayOpenStatusQueryResp.builder()
                            .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                            .message("暂时无法开通，联系销售支持")
                            .retry(false)
                            .build();
                }
            } else if (TaskStatus.SUCCESS.getVal().equals(contractSubTaskDO.getStatus())) {
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                        .message("开通成功")
                        .retry(false)
                        .build();
            } else {
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message(WosaiMapUtils.getString(JSON.parseObject(contractSubTaskDO.getResult()), "message"))
                        .retry(false)
                        .build();

            }
        }
        return UnionPayOpenStatusQueryResp.builder()
                .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                .message("暂时无法开通，联系销售支持")
                .retry(false)
                .build();
    }

    private ContractSubTaskDO getUnionNetInTask(String merchantSn, String acquirer) {
        List<ContractSubTaskDO> unionPaySubTasks = contractSubTaskDAO.listContractSubTaskDOsByPayway(merchantSn, ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT, PaywayEnum.UNIONPAY.getValue());
        for (ContractSubTaskDO unionPaySubTask : unionPaySubTasks) {
            String taskAcquirer = "";
            if (WosaiStringUtils.isNotBlank(unionPaySubTask.getContractRule())) {
                ContractRule contractRule = ruleContext.getContractRule(unionPaySubTask.getContractRule());
                taskAcquirer = contractRule.getAcquirer();
            } else {
                Optional<McProviderDO> mcProviderDOOptional = mcProviderDAO.getByBeanName(unionPaySubTask.getChannel());
                if (mcProviderDOOptional.isPresent()) {
                    taskAcquirer = mcProviderDOOptional.get().getAcquirer();
                }
            }

            if (acquirer.contains(McConstant.ACQUIRER_LKL) && taskAcquirer.contains(McConstant.ACQUIRER_LKL)) {
                return unionPaySubTask;
            }
            if (taskAcquirer.equals(acquirer)) {
                return unionPaySubTask;
            }
        }
        return null;
    }

    private MerchantProviderParams getAcquirerParams(String merchantSn){
        MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
        dto.createCriteria().andMerchant_snEqualTo(merchantSn).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue()).andDeletedEqualTo(false);
        return merchantProviderParamsMapper.selectByExample(dto).get(0);
    }

}