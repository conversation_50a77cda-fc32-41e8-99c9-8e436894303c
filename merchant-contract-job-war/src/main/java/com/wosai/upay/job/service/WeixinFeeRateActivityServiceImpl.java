package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.trade.service.ApplyActivityService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.activity.request.PaySourceHandelRequest;
import com.wosai.trade.service.request.CancelFeeRateRequest;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.LklPayMerchantBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WeixinAuthApplyBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.enume.WxUseType;
import com.wosai.upay.job.handlers.ContractSubTaskHandler;
import com.wosai.upay.job.mapper.ChannelActivityMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.util.Utils;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateParam;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateQueryResponse;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateResponse;
import com.wosai.upay.merchant.contract.model.weixin.ModifySpecialFeeRateParam;
import com.wosai.upay.merchant.contract.service.AuthApplyFlowService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class WeixinFeeRateActivityServiceImpl implements WeixinFeeRateActivityService {

    @Autowired
    ChannelActivityMapper mapper;

    @Autowired
    private CallBackService callBackService;

    @Autowired
    @Lazy
    private MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private AuthApplyFlowService authApplyFlowService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private BusinessLogBiz businessLogBiz;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private FeeRateService mchFeeRateService;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private WeixinAuthApplyBiz weixinAuthApplyBiz;

    @Autowired
    private SupportService supportService;
    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private LklPayMerchantBiz lklPayMerchantBiz;
    @Autowired
    private ApplyActivityService applyActivityService;

    public static String TRADE_COMB_ID_MAP = "trade_comb_ids";

    public static String OFFLINE_EDU_TRAIN = "offline_edu_train";

    public static String SCHOOL_CANTEEN = "school_canteen";

    @Value("${schoolcanteen_tag}")
    private String schoolcanteenTag;

    @Override
    public boolean isAuth(String mchId) {
        try {
            List<MerchantProviderParamsDto> query = merchantProviderParamsService.getMerchantProviderParams(new MerchantParamReq().setPay_merchant_id(mchId));
            if (query.size() != 1) {
                throw new RuntimeException("微信子商户号子商户错误");
            }
            MerchantProviderParamsDto param = query.get(0);
            ContractChannel contractChannel = ruleContext.getContractChannel(PaywayEnum.WEIXIN.getValue(), param.getProvider().toString(), param.getChannel_no());
            final boolean equals = ContractSubTaskHandler.AUTHORIZE_STATE_AUTHORIZED.
                    equals(authApplyFlowService.queryAuthStatus(mchId, contractChannel.buildAuthV3Param()).getAuthorize_state());
            if (equals) {//授权成功,修改本地报备参数授权状态和时间
                weixinAuthApplyBiz.updateAuthStatus(mchId, null, true);
            }
            return equals;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public CommonResult addApply(String auditId, Map<String, Object> info) {
        try {
            String merchantSn = MapUtils.getString(info, "merchant_sn");
            String merchantId = MapUtils.getString(info, "merchant_id");
            Integer type = MapUtils.getInteger(info, "type");
            Long applyId = MapUtils.getLong(info, "apply_id");
            //高校食堂 拉卡拉组织不允许参加
            if (type == 0) {
                boolean lklPayMerchant = lklPayMerchantBiz.isLklPayMerchant(merchantSn, merchantId);
                if (lklPayMerchant) {
                    return new CommonResult(CommonResult.BIZ_FAIL, "当前商户不支持参与该活动");
                }
            }

            String mch_id = MapUtils.getString(info, "mch_id");
            List<MerchantProviderParamsDto> query = merchantProviderParamsService.getMerchantProviderParams(new MerchantParamReq().setPay_merchant_id(mch_id));
            if (query.size() != 1) {
                throw new RuntimeException("微信子商户号子商户错误");
            }
            ChannelActivity activity = mapper.getLatestActivityByMchId(mch_id, 1L);
            if (activity != null && activity.getStatus() != 10) {
                String msg = activity.getStatus() == 3 ? String.format("子商户号 %s 已申请成功，请勿重复申请", mch_id) : String.format("子商户号 %s 已有申请在处理中，请勿重复申请", mch_id);
                return new CommonResult(CommonResult.BIZ_FAIL, msg);
            }

            mapper.insert(new ChannelActivity()
                    .setAudit_id(auditId)
                    .setApply_id(applyId)
                    .setMch_id(mch_id)
                    .setMerchant_sn(merchantSn)
                    .setMerchant_id(merchantId)
                    .setType(type)
                    .setStatus(-1)
                    .setForm_body(MapUtils.getString(info, "form_body")));
            return new CommonResult(CommonResult.SUCCESS, "提交成功");
        } catch (Exception e) {
            log.error("addApply-error", e);
            return new CommonResult(CommonResult.BIZ_FAIL, "提交微信费率活动异常: " + e.getMessage());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitOrUpdateToWechat(ChannelActivity r, MerchantProviderParamsDto params) {
        Long id = r.getId();
        String auditId = r.getAudit_id();
        int currentStatus = 1;
        int result = mapper.updateActivityStatusByIdAndPreStatus(id, currentStatus, 0);
        if (result != 1) {
            return;
        }

        ContractChannel contractChannel = ruleContext.getContractChannel(PaywayEnum.WEIXIN.getValue(), params.getProvider().toString(), params.getChannel_no());
        ChannelActivity latestActivityByMchId = mapper.getLatestActivityByMchId(r.getMch_id(), r.getId());
        ApplySpecialFeeRateResponse resp;
        if (latestActivityByMchId == null || WosaiStringUtils.isEmpty(latestActivityByMchId.getOut_uid())) {
            //新增流程
            String channelId = MapUtils.getString(contractChannel.getChannelParam(), "mch_id");
            ApplySpecialFeeRateParam param = new ApplySpecialFeeRateParam();
            param.setAcquiring_bank_id(channelId);
            param.setChannel_id(contractChannel.getPayway_channel_no());
            param.setSub_mchid(r.getMch_id());
            ApplySpecialFeeRateParam.Activity_detail detail = new ApplySpecialFeeRateParam.Activity_detail();
            if (Objects.equals(r.getType(), 0)) {//高校食堂
                detail.setActivity_id("schoolcanteen_001");
                detail.setActivity_rate("0");
            } else if (Objects.equals(r.getType(), 1)) {//线下教培
                detail.setActivity_id("education_001");
                detail.setActivity_rate("0.38");
            }
            String body = r.getForm_body();
            JSONArray pics = JSON.parseObject(body).getJSONArray("pics");
            List<ApplySpecialFeeRateParam.Material> information = pics
                    .stream()
                    .map(pic -> JSON.parseObject(JSON.toJSONString(pic), ApplySpecialFeeRateParam.Material.class))
                    .collect(Collectors.toList());
            detail.setActivity_apply_information(information);
            param.setActivity_detail(detail);
            resp = composeAcquirerBiz.applyFeeRate(params.getMerchant_sn(), param, contractChannel);
        } else {
            ModifySpecialFeeRateParam param = new ModifySpecialFeeRateParam();
            param.setApplication_id(latestActivityByMchId.getOut_uid());
            ModifySpecialFeeRateParam.Activity_detail_modification detail = new ModifySpecialFeeRateParam.Activity_detail_modification();
            String body = r.getForm_body();
            JSONArray pics = JSON.parseObject(body).getJSONArray("pics");
            List<ModifySpecialFeeRateParam.Material> information = pics
                    .stream()
                    .map(pic -> JSON.parseObject(JSON.toJSONString(pic), ModifySpecialFeeRateParam.Material.class))
                    .collect(Collectors.toList());
            detail.setActivity_apply_information(information);
            param.setActivity_detail_modification(detail);
            resp = composeAcquirerBiz.modifyFeeRate(params.getMerchant_sn(), param, contractChannel);
        }
        log.info(params.getPay_merchant_id() + "  response {}", JSON.toJSONString(resp));
        int code = Integer.parseInt(resp.getCode());
        String appId = resp.getApplication_id();
        if (code == 200 && !StringUtil.empty(appId)) {
            submitSuccess(id, appId, currentStatus, auditId, r.getApply_id());
        } else if (code >= 400 && code < 500) {
            statusToFail(id, resp.getMessage(), currentStatus, auditId, r.getType(), appId, r.getApply_id());
        } else {
            throw new RuntimeException(resp.getMessage());
        }

    }


    @Override
    public void statusToSubmit(ChannelActivity r) {
        boolean auth = isAuth(r.getMch_id());
        if (auth) {
            if(Utils.isNullOrZero(r.getApply_id())) {
                String auditId = r.getAudit_id();
                AuditInfo auditInfo = new AuditInfo(auditId);
                CallBackBean callBackBean = CallBackBean.builder()
                        .auditId(auditInfo.getAuditId())
                        .templateId(auditInfo.getTemplateId())
                        .resultType(AUDIT_EXECUTE_SUCCESS)
                        .message("微信实名认证成功，提交活动中")
                        .build();
                callBackService.addComment(callBackBean);
            }
            mapper.updateActivityStatusByIdAndPreStatus(r.getId(), 0, -1);
        }
    }

    @Override
    public void changeParams(Long queryTime, Integer queryLimit) {
        List<ChannelActivity> list = mapper.listByStatusAndCtimeLimit(4, StringUtil.formatDate(System.currentTimeMillis() - queryTime), queryLimit);
        list.forEach(r -> {
            try {
                MerchantProviderParamsDto params = merchantProviderParamsService.getMerchantProviderParams(
                        new MerchantParamReq()
                                .setPay_merchant_id(r.getMch_id())
                                .setPayway(PaywayEnum.WEIXIN.getValue())
                ).get(0);
                statusToFinalSuccess(r, params);
            } catch (Throwable e) {
                log.error("submit fee rate error {} ", r.getMerchant_sn(), e);
                //活动失败记录原因
                statusToFail(r.getId(),ExceptionUtil.getThrowableMsg(e),4, r.getAudit_id(), r.getType(), r.getOut_uid(), r.getApply_id());
                chatBotUtil.sendMessageToContractWarnChatBot(r.getMerchant_sn() + "查询微信优惠费率活动结果失败：" + ExceptionUtil.getThrowableMsg(e));
            }
        });
    }

    @Override
    public void changeOneParams(Long id) {
        ChannelActivity channelActivity = mapper.selectByPrimaryKey(id);
        MerchantProviderParamsDto params = merchantProviderParamsService.getMerchantProviderParams(
                new MerchantParamReq()
                        .setPay_merchant_id(channelActivity.getMch_id())
                        .setPayway(PaywayEnum.WEIXIN.getValue())
        ).get(0);
        statusToFinalSuccess(channelActivity, params);
    }


    @Override
    public void queryStatus(ChannelActivity r, MerchantProviderParamsDto params) throws Exception {
        Long id = r.getId();
        String auditId = r.getAudit_id();
        ContractChannel contractChannel = ruleContext.getContractChannel(PaywayEnum.WEIXIN.getValue(), params.getProvider().toString(), params.getChannel_no());
        Integer currentStatus = 2;
        ApplySpecialFeeRateQueryResponse response = composeAcquirerBiz.queryRateApplyStatus(params.getMerchant_sn(), r.getOut_uid(), contractChannel);
        log.info("{} query status response {}", r.getMerchant_sn(), JSON.toJSONString(response));
        String state = response.getApplication_state();
        if (response.getStatus_code() != 200) {
            throw new RuntimeException("查询返回状态码 " + response.getStatus_code());
        }
        if ("APPLICATION_STATE_AUDITING".equals(state)) {
            return;
        }
        if ("APPLICATION_STATE_PASSED".equals(state)) {
            mapper.updateActivityStatusByIdAndPreStatus(id, 4, currentStatus);
        }
        if ("APPLICATION_STATE_REJECTED".equals(state)) {
            statusToFail(id, response.getReject_reason(), currentStatus, auditId, r.getType(), r.getOut_uid(), r.getApply_id());
        }
    }

    public static final int AUDIT_EXECUTE_SUCCESS = 1;
    public static final int AUDIT_EXECUTE_FAIL = 0;


    private int submitSuccess(Long id, String bizId, Integer preStatus, String auditId, Long applyId) {
        if(Utils.isNullOrZero(applyId)) {
            AuditInfo auditInfo = new AuditInfo(auditId);
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(auditInfo.getAuditId())
                    .templateId(auditInfo.getTemplateId())
                    .resultType(AUDIT_EXECUTE_SUCCESS)
                    .message("微信行业活动报名成功，等待微信审核中")
                    .build();
            callBackService.addComment(callBackBean);
        }
        return mapper.updateActivityStatusAndOutUidByIdAndPreStatus(id, bizId, 2, preStatus);
    }

    private int statusToFail(Long id, String reason, Integer preStatus, String auditId, Integer type, String applicationId, Long applyId) {
        if (StringUtil.empty(reason)) {
            reason = "未知";
        }
        if(Objects.equals(type, 1)) {
            reason = reason + "  申请单号: " + applicationId;
        }
        if(Utils.isNullOrZero(applyId)) {
                AuditInfo auditInfo = new AuditInfo(auditId);
                CallBackBean callBackBean = CallBackBean.builder()
                        .auditId(auditInfo.getAuditId())
                        .templateId(auditInfo.getTemplateId())
                        .resultType(AUDIT_EXECUTE_FAIL)
                        .message(reason)
                        .build();
                callBackService.addComment(callBackBean);
            } else {
                PaySourceHandelRequest paySourceHandelRequest = new PaySourceHandelRequest();
                paySourceHandelRequest.setType(2);  //报名处理
                paySourceHandelRequest.setStatus(2);    //失败
                paySourceHandelRequest.setMsg(reason);
                applyActivityService.paySourceResultHandle(applyId, paySourceHandelRequest);
        }
        return mapper.updateActivityStatusAndResultByIdAndPreStatus(id, JSON.toJSONString(ImmutableMap.of("fail_reason", reason)), 10, preStatus);
    }

    private void statusToFinalSuccess(ChannelActivity activity, MerchantProviderParamsDto params) {
        AuditInfo auditInfo = new AuditInfo(activity.getAudit_id());
        Map merchant = merchantService.getMerchantBySn(params.getMerchant_sn());
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);

        Map before = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.WEIXIN.getValue());
        String remark = null;
        String logRemark = null;
        if (Objects.equals(activity.getType(), 0)) {//高校食堂切通道
            remark = "高校食堂报备成功，自动切通道";
            logRemark = "高校食堂报备成功后切换交易参数，审批编号：";
        } else if (Objects.equals(activity.getType(), 1)) {//线下教培
            remark = "线下教培报备成功，自动切通道";
            logRemark = "线下教培报备成功后切换交易参数，审批编号：";
        }
        try {
            merchantProviderParamsService.setDefaultMerchantProviderParams(params.getId(), null, remark);
        } catch (CommonPubBizException e) {
            if ("商户当前在用微信子商户号正在实名处理中 请等待授权完毕后切换交易参数".contains(e.getMessage())) {
                if(Utils.isNullOrZero(activity.getApply_id())) {
                    CallBackBean callBackBean = CallBackBean.builder()
                            .auditId(auditInfo.getAuditId())
                            .templateId(auditInfo.getTemplateId())
                            .resultType(AUDIT_EXECUTE_FAIL)
                            .message("活动费率生效失败，疯狂收钱吧存在待完成的微信升级认证，需完成后系统自动生效校园活动费率")
                            .build();
                    callBackService.addComment(callBackBean);
                }else {
                    PaySourceHandelRequest paySourceHandelRequest = new PaySourceHandelRequest();
                    paySourceHandelRequest.setType(2);
                    paySourceHandelRequest.setStatus(2);
                    paySourceHandelRequest.setMsg("活动费率生效失败，疯狂收钱吧存在待完成的微信升级认证，需完成后系统自动生效校园活动费率");
                    applyActivityService.paySourceResultHandle(activity.getApply_id(), paySourceHandelRequest);
                }
                return;
            }
        }
        //修改费率
        //判断是不是微信高校食堂或者是线下教培活动,如果是则使用特殊的接口修改费率
        if(Utils.isNullOrZero(activity.getApply_id())) {
            if (Objects.equals(activity.getType(), 0)) {
                mchFeeRateService.applyFeeRate(BeanUtil.getPropString(merchant, Merchant.SN), BeanUtil.getPropLong(applicationApolloConfig.getTradeCombIds(), SCHOOL_CANTEEN, -1L));
            } else if (Objects.equals(activity.getType(), 1)) {
                mchFeeRateService.applyFeeRate(BeanUtil.getPropString(merchant, Merchant.SN), BeanUtil.getPropLong(applicationApolloConfig.getTradeCombIds(), OFFLINE_EDU_TRAIN, -2L));
            }
        }
        //清除交易缓存
        supportService.removeCachedParams(params.getMerchant_sn());
        //修改线下教培微信子商户号用途(由入网时的1:一般用途->3:微信线下教培活动),由于高校食堂入网的时候不是这个这个逻辑所以高校食堂不用修改微信子商户用途
        if (Objects.equals(activity.getType(), 1) && !Objects.equals(params.getWx_use_type(), WxUseType.OFFLINE_EDU_TRAIN.getCode())) {
            merchantProviderParamsMapper.updateWxUseTypeById(params.getId(), WxUseType.OFFLINE_EDU_TRAIN.getCode());
        }
        Map after = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.WEIXIN.getValue());
        businessLogBiz.sendMerchantConfigBusinessLog(before, after, "报备服务", "报备服务", logRemark + auditInfo.getAuditSn());

        if(Utils.isNullOrZero(activity.getApply_id())) {
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(auditInfo.getAuditId())
                    .templateId(auditInfo.getTemplateId())
                    .resultType(AUDIT_EXECUTE_SUCCESS)
                    .message("申请成功，费率已调整")
                    .build();
            callBackService.addComment(callBackBean);
        } else { //费率管理平台
            PaySourceHandelRequest paySourceHandelRequest = new PaySourceHandelRequest();
            paySourceHandelRequest.setType(2);
            paySourceHandelRequest.setStatus(1);
            paySourceHandelRequest.setMsg("申请成功");
            applyActivityService.paySourceResultHandle(activity.getApply_id(), paySourceHandelRequest);
        }
        mapper.updateActivityStatusByIdAndPreStatus(activity.getId(), 3, 4);
    }

    @Data
    public static class AuditInfo {
        private Long auditId;
        private Long templateId;
        private String auditSn;

        public AuditInfo(String info) {
            String[] data = info.split(",");
            auditId = Long.valueOf(data[0]);
            templateId = Long.valueOf(data[1]);
            auditSn = data.length > 2 ? data[2] : "";
        }
    }

    @Override
    public void setFail(ChannelActivity r) {
        Long id = r.getId();
        String auditId = r.getAudit_id();
        statusToFail(id,"微信超时未授权",-1,auditId, r.getType(), r.getOut_uid(), r.getApply_id());
    }


    /**
     * <AUTHOR>
     * @Description: 微信特殊渠道规则
     * @time 09:29
     */
    final static List<String> CHANNELS = Lists.newArrayList("lkl-1016-3-36TB4213341","lkl-1016-3-342571947","tonglian-1020-3-344311921","tonglian-1020-3-254313567", "lkl-1033-3-217935905");
    @Override
    public CallBackBean cancelSchoolCanteen(String merchantSn, CallBackBean callBackBean, String auditSn) {
        callBackBean.setResultType(AUDIT_EXECUTE_SUCCESS);
        callBackBean.setMessage("取消成功");
        //当前系统设置的高校食堂套餐Id
        final Long tradeId = BeanUtil.getPropLong(applicationApolloConfig.getTradeCombIds(), SCHOOL_CANTEEN, -1L);
        //当前商户生效的套餐
        final List<ListMchFeeRateResult> listMchFeeRateResults = mchFeeRateService.listMchFeeRates(merchantSn);
        final boolean match = listMchFeeRateResults.parallelStream().anyMatch(x -> Objects.equals(x.getTradeComboId(), tradeId));
        // TODO: 2021/7/26  临时
        //当前商户没有使用高校食堂的套餐
        if(!match) {
            callBackBean.setResultType(AUDIT_EXECUTE_FAIL);
            callBackBean.setMessage("商户未参加该活动");
            return callBackBean;
        }
        //获取商户当前所有的merchant_provider_params参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andPaywayEqualTo(PaywayEnum.WEIXIN.getValue()).andProviderNotEqualTo(ProviderEnum.WEI_XIN.getValue()).andParams_config_statusEqualTo(MerchantProviderParams.PARAMS_CONFIG_STATUS_SUCCESS).andDeletedEqualTo(false);
        final List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(params)) {
            callBackBean.setResultType(AUDIT_EXECUTE_FAIL);
            callBackBean.setMessage("没有可用的微信交易参数");
        }
        log.info("cancelSchoolCanteen merchantSn:{},params:{}",merchantSn,JSONObject.toJSONString(params));
        //普通渠道先按照微信认证状态倒序然后在按照创建时间倒序
        final List<MerchantProviderParams> collect = params.parallelStream().filter(x -> !CHANNELS.contains(x.getContract_rule())).sorted(Comparator.comparing(MerchantProviderParams::getAuth_status, Comparator.nullsFirst(Integer::compareTo).reversed()).thenComparing(MerchantProviderParams::getCtime, Comparator.nullsFirst(Long::compareTo).reversed())).filter(Objects::nonNull).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)) {
            callBackBean.setResultType(AUDIT_EXECUTE_FAIL);
            callBackBean.setMessage("该商户不存在普通渠道");
        }
        //获取满足条件的交易参数
        log.info("普通渠道交易参数 merchantSn:{},params:{}",merchantSn,JSONObject.toJSONString(collect));
        final MerchantProviderParams toUse = collect.get(0);
        //当前交易配置信息
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map before = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.WEIXIN.getValue());
        //将普通渠道设为默认
        merchantProviderParamsService.setDefaultMerchantProviderParams(toUse.getId(), null, "取消微信高校食堂");
        //用于更新结算Id和微信用途
        final MerchantProviderParams updateParam = new MerchantProviderParams();
        final String settlementId = toUse.getWx_settlement_id();
        if(StringUtils.isEmpty(settlementId)) {
            //获取普通渠道结算规则Id
            final List<ContractSubTask> subTasks = contractSubTaskMapper.findTasksByMerchantAndPayway(new ContractSubTaskReq().setPayway(PaywayEnum.WEIXIN.getValue()).setMerchantSn(merchantSn));
            final Map<String, String> IdBusinessMap = subTasks.stream().filter(x -> MapUtils.isNotEmpty(JSONObject.parseObject(x.getResponse_body(), Map.class)) && MapUtils.isNotEmpty(JSONObject.parseObject(x.getRequest_body(), Map.class))).collect(Collectors.toMap(x -> BeanUtil.getPropString(JSONObject.parseObject(x.getResponse_body(), Map.class), "merchantProviderParamsId"), x -> BeanUtil.getPropString(JSONObject.parseObject(x.getRequest_body(), Map.class), "business"), (val1, val2) -> val1));
            //设置该渠道的结算规则Id
            final String wxSettlementId = IdBusinessMap.get(toUse.getId());
            updateParam.setWx_settlement_id(wxSettlementId);
        }
        //如果使用普通渠道报的高校食堂,将高校食堂用途修改为一般用途
        Integer wxUseType = toUse.getWx_use_type();
        if(!Objects.equals(wxUseType,WxUseType.NORMAL.getCode())) {
            updateParam.setWx_use_type(WxUseType.NORMAL.getCode());
        }
        merchantProviderParamsMapper.updateByPrimaryKeySelective(updateParam.setId(toUse.getId()).setMtime(System.currentTimeMillis()));
        //配置标准费率套餐
        mchFeeRateService.cancelFeeRate(new CancelFeeRateRequest().setMerchantSn(merchantSn).setTradeComboId(tradeId).setAuditSn("取消微信高校食堂活动"));
        Map after = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.WEIXIN.getValue());
        businessLogBiz.sendMerchantConfigBusinessLog(before, after, "报备服务", "报备服务", "取消高校食堂 " + auditSn);
        return callBackBean;
    }

    @Override
    public ChannelActivity getSuccessActivityByMchId(String mchId) {
        return mapper.getSuccessActivityByMchId(mchId,0);
    }
}
