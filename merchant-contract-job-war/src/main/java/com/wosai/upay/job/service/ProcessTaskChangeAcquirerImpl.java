package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/9/14 4:30 下午
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class ProcessTaskChangeAcquirerImpl implements ProcessTaskChangeAcquirer {


    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private ContractEventService contractEventService;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    /**
     * 商户入网 进行中 切换收单机构
     *
     * @param merchantSn 商户号
     * @param source     源收单机构
     * @param target     切换的收单机构
     * @return
     */
    @Override
    public boolean change(String merchantSn, String source, String target) {
        return changeByTaskStatus(merchantSn, source, target, 1);
    }

    @Override
    public boolean changeByTaskStatus(String merchantSn, String source, String target, Integer status) {
        //查询进行中的task
        ContractTask task = contractTaskMapper.getBySnAndTypeAndStatus(merchantSn, ProviderUtil.CONTRACT_TYPE_INSERT, status);
        if (task == null || !source.equals(task.getRule_group_id())) {
            return true;
        }
        //子任务状态置为失败
        List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectByPTaskIdAndStatus(task.getId(), status);
        if (CollectionUtils.isEmpty(contractSubTasks)) {
            return true;
        }
        for (ContractSubTask contractSubTask : contractSubTasks) {
            contractSubTaskMapper.updateStatusById(6, contractSubTask.getId());
        }
        //总任务状态置为失败
        contractTaskMapper.updateStatusById(6, task.getId());
        //删除
        contractStatusMapper.deleteBySn(merchantSn);

        //指定入网收单机构
        contractEventService.saveContractEvent(merchantSn, target, "crm");
        log.info("商户{}从收单机构{}切换到{}", merchantSn, source, target);
        return true;
    }


}
