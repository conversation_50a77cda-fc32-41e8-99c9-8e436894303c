package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonException;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.direct.WeixinDirectBiz;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.mapper.WeixinDirectApplyMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.DO.WeixinDirectApply;
import com.wosai.upay.job.model.direct.*;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/18
 */
@Service
@AutoJsonRpcServiceImpl
public class WeixinDirectApplyServiceImpl implements WeixinDirectApplyService {

    @Autowired
    private WeixinDirectBiz weixinDirectBiz;

    @Autowired
    private MerchantProviderParamsBiz paramsBiz;

    @Autowired
    private WeixinDirectApplyMapper applyMapper;

    @Autowired
    private RedisLock redisLock;

    private static final String LOCK_KEY = "weixinDirect:";

    private static final int LOCK_TIME = 10;

    @Override
    public ContractResponse applyWeixinDirectPay(WeixinDirectReq weixinDirectReq) {
        ContractResponse contractResponse = new ContractResponse();
        Map<String, Object> paramContext;
        try {
            weixinDirectBiz.preCheck(weixinDirectReq.getMerchant_sn(), weixinDirectReq.getDev_code());
            paramContext = weixinDirectBiz.buildWeixinParamContext(weixinDirectReq);
        } catch (CommonInvalidParameterException | ContextParamException exception) {
            weixinDirectBiz.createFailTask(weixinDirectReq.getMerchant_sn(), weixinDirectReq.getDev_code(), exception.getMessage());
            return contractResponse.setSuccess(false).setMsg("提交开通申请失败:" + exception.getMessage());
        }
        weixinDirectBiz.createTaskAndApplyAndStatus(weixinDirectReq, paramContext);
        return contractResponse.setSuccess(true);
    }

    @Override
    public ApplyStatusResp queryApplyStatus(String merchantSn, String devCode, String platform) {
        String key = LOCK_KEY + devCode + merchantSn;
        if (!redisLock.lock(key, key, LOCK_TIME)) {
            throw new CommonPubBizException("请求频繁，请稍后重试");
        }
        return weixinDirectBiz.getLatestStatusFromWeixin(merchantSn, devCode, platform);
    }

    @Override
    public ApplyStatusResp getApplyStatusByType(String merchantSn, String devCode, String platform) {
        return weixinDirectBiz.getWeixinDirectContractMemo(merchantSn, devCode, platform);
    }

    @Override
    public ContractResponse allowApplyToManual(String merchantSn, String devCode) {
        return weixinDirectBiz.allowApplyToManual(merchantSn, devCode);
    }

    @Override
    public ContractResponse applyWeixinDirectPayToManual(WeixinDirectReq  weixinDirectReq) {
        ContractResponse contractResponse = new ContractResponse();
        try {
            weixinDirectBiz.transferToManual(weixinDirectReq);
        } catch (CommonException e) {
            return contractResponse.setSuccess(false).setMsg("提交转人工申请失败:" + e.getMessage());
        }
        return contractResponse.setSuccess(true);
    }

    @Override
    public ContractResponse changeWeixinDirectApplyStatus(ChangeApplyStatusReq req) {
        ContractResponse contractResponse = new ContractResponse();
        try {
            weixinDirectBiz.changeWeixinDirectApplyStatus(req);
        } catch (CommonPubBizException e) {
            return contractResponse.setSuccess(false).setMsg("变更状态失败:" + e.getMessage());
        }
        return contractResponse.setSuccess(true);
    }

    @Override
    public ContractResponse bindMerchantWeixinDirectPay(BindMerchantReq bindMerchantReq) {
        ContractResponse contractResponse = new ContractResponse();
        MerchantProviderParamsDto providerParams;
        Map context;
        try {
            weixinDirectBiz.preCheck(bindMerchantReq.getMerchant_sn(), bindMerchantReq.getDev_code());
            context = weixinDirectBiz.checkIsSameSubject(bindMerchantReq);
            providerParams = getWeixinDirectParams(bindMerchantReq.getBind_merchant_sn());
        } catch (CommonInvalidParameterException exception) {
            weixinDirectBiz.createFailTask(bindMerchantReq.getMerchant_sn(), bindMerchantReq.getDev_code(), exception.getMessage());
            return contractResponse.setSuccess(false).setMsg("提交申请失败:" + exception.getMessage());
        }
        //直接成功 根据被绑定的商户配置交易参数等
        weixinDirectBiz.bindMerchantWeixinDirectPay(bindMerchantReq, providerParams, context);
        return contractResponse.setSuccess(true);
    }

    @Override
    public WeixinDirectIndustry getWeixinDirectIndustry(String industryId) {
        return weixinDirectBiz.getWeixinDirectIndustry(industryId);
    }

    @Override
    public WeixinDirectApplyDto getDirectApplyByTaskId(long taskId) {
        WeixinDirectApply weixinDirectApply = applyMapper.selectApplyByTaskId(taskId);
        if (weixinDirectApply == null) {
            return null;
        }
        WeixinDirectApplyDto directApplyDto = new WeixinDirectApplyDto();
        BeanUtils.copyProperties(weixinDirectApply, directApplyDto);
        return directApplyDto;
    }

    @Override
    public WeixinDirectApplyDto getDirectApplyByMerchantSnAndDevCode(String merchantSn, String devCode) {
        WeixinDirectApply weixinDirectApply = applyMapper.selectLatestApplyByMerchantSn(merchantSn, devCode);
        if (weixinDirectApply == null) {
            return null;
        }
        WeixinDirectApplyDto directApplyDto = new WeixinDirectApplyDto();
        BeanUtils.copyProperties(weixinDirectApply, directApplyDto);
        return directApplyDto;
    }

    private MerchantProviderParamsDto getWeixinDirectParams(String merchantSn) {
        MerchantProviderParamsDto dto = paramsBiz.getDirectParams(merchantSn, ProviderEnum.WEI_XIN.getValue(), PaywayEnum.WEIXIN.getValue());
        if (dto == null) {
            throw new CommonInvalidParameterException(merchantSn + "不是直连商户");
        }
        return dto;
    }
}
