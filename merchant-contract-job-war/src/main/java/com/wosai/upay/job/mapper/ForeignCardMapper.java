package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.ForeignCard;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ForeignCardMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ForeignCard record);

    int insertSelective(ForeignCard record);

    ForeignCard selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ForeignCard record);

    int updateByPrimaryKeyWithBLOBs(ForeignCard record);

    int updateByPrimaryKey(ForeignCard record);

    @Select("select * from foreign_card where merchant_sn=#{merchantSn} and status = #{status}  order by update_at desc limit 1")
    ForeignCard selectByMerchantSn(String merchantSn,Integer status);

    @Select("select * from foreign_card where merchant_sn=#{merchantSn} and status = #{status}")
    List<ForeignCard> selectListByMerchantSn(String merchantSn,Integer status);

    @Select("select * from foreign_card where merchant_sn=#{merchantSn} and dev_code = #{devCode}  order by update_at desc limit 1")
    ForeignCard selectByMerchantSnAndCode(String merchantSn, String devCode);

    @Select("select * from foreign_card where merchant_sn=#{merchantSn} and dev_code = #{devCode} and status=10  order by update_at desc limit 1")
    ForeignCard selectSuccessByMerchantSnAndCode(String merchantSn, String devCode);


    @Select("select * from foreign_card where ecApplyId=#{ecApplyId}  order by update_at desc limit 1")
    ForeignCard selectByEcApplyId(String ecApplyId);


    @Select("select * from foreign_card where status=10 and dev_code = 'OYLIAHFL3SEK'")
    List<ForeignCard> selectLklForeign();
}