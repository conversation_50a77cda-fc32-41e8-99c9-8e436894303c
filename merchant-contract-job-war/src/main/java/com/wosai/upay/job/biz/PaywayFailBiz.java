package com.wosai.upay.job.biz;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.cua.kafka.avro.PaywayContractFail;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.dto.PaywayFailHandleResult;
import com.wosai.upay.job.service.TaskResultService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractSysException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/1
 */
@Component
@Slf4j
public class PaywayFailBiz {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RuleContext ruleContext;

    @Resource(name = "aliKafkaTemplate")
    private KafkaTemplate<String, Object> aliKafkaTemplate;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private TaskResultService taskResultService;

    /**
     * 需要处理的payway
     */
    private static final List<Integer> PAY_WAY = Arrays.asList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue());

    /**
     * 需要处理的acquirer
     */
    private static final List<String> ACQUIRERS = Arrays.asList(AcquirerTypeEnum.LKL_V3.getValue(), AcquirerTypeEnum.HAI_KE.getValue(), AcquirerTypeEnum.FU_YOU.getValue());

    public PaywayFailHandleResult handle(ContractTask contractTask, ContractSubTask contractSubTask, ContractResponse contractResponse) {
        if (!shouldHandle(contractTask, contractSubTask, contractResponse)) {
            return new PaywayFailHandleResult(contractTask, contractSubTask, false);
        }
        RLock lock = redissonClient.getLock("PaywayFailBiz:" + contractTask.getMerchant_sn());
        try {
            if (lock.tryLock(2, TimeUnit.SECONDS)) {
                return doHandle(contractTask, contractSubTask, contractResponse);
            }
        } catch (InterruptedException e) {

        } finally {
            if (Objects.nonNull(lock) && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        log.info("并发重试 {} {} {}", contractTask.getMerchant_sn(), contractTask.getId(), contractSubTask.getId());
        throw new ContractSysException("并发重试");
    }

    private boolean shouldHandle(ContractTask contractTask, ContractSubTask contractSubTask, ContractResponse contractResponse) {
        try {
            if (Objects.equals(0, contractSubTask.getStatus_influ_p_task())) {
                return false;
            }
            if (!Objects.equals(ProviderUtil.CONTRACT_TYPE_INSERT, contractTask.getType())) {
                return false;
            }
            if (!PAY_WAY.contains(contractSubTask.getPayway())) {
                return false;
            }

            ContractRule contractRule = ruleContext.getContractRule(contractSubTask.getContract_rule());
            if (!ACQUIRERS.contains(contractRule.getAcquirer())) {
                return false;
            }

            boolean shouldHandle = applicationApolloConfig.getPaywayFailMsg()
                    .stream()
                    .anyMatch(failMsg -> contractResponse.getMessage().contains(failMsg));
            log.info("shouldHandle result {} {} {} {}", shouldHandle, contractTask.getMerchant_sn(), contractTask.getId(), contractSubTask.getId());
            return shouldHandle;
        } catch (Exception e) {
            log.error("shouldHandle error {} {} {}", contractTask.getMerchant_sn(), contractTask.getId(), contractSubTask.getId(), e);
            return false;
        }
    }

    private PaywayFailHandleResult doHandle(ContractTask contractTask, ContractSubTask contractSubTask, ContractResponse contractResponse) {
        Long taskId = contractTask.getId();
        contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
        if (TaskStatus.isFinish(contractTask.getStatus())) {
            log.info("taskFinish {} {}", contractTask.getMerchant_sn(), taskId);
            return new PaywayFailHandleResult(contractTask, contractSubTask, true);
        }

        // 检查其余payway是否失败
        List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectByPTaskId(taskId);
        List<Integer> allPayways = new ArrayList<>(PAY_WAY);
        allPayways.remove(contractSubTask.getPayway());
        List<ContractSubTask> otherPaywaySubTasks = contractSubTasks.stream()
                .filter(sub -> allPayways.contains(sub.getPayway()))
                .collect(Collectors.toList());
        boolean otherPaywayAllFail = WosaiCollectionUtils.isNotEmpty(otherPaywaySubTasks) &&
                otherPaywaySubTasks.stream()
                        .allMatch(sub -> Objects.equals(SubTaskStatus.FAIL.getVal(), sub.getStatus()));
        if (otherPaywayAllFail) {
            log.info("otherPaywayAllFail {} {}", contractTask.getMerchant_sn(), taskId);
            return new PaywayFailHandleResult(contractTask, contractSubTask, false);
        }

        setSubTaskNotAffectTask(contractSubTask, taskId);

        contractSubTaskMapper.selectByDependTaskId(contractSubTask.getId())
                .stream()
                .filter(subTask -> subTask.getStatus_influ_p_task() == 1)
                .forEach(subTask -> setSubTaskNotAffectTask(subTask, taskId));

        contractTask = contractTaskMapper.selectByPrimaryKey(contractTask.getId());
        if (TaskStatus.isFinish(contractTask.getStatus())) {
            log.info("taskFinish changeStatusAndResultV2 {} {}", contractTask.getMerchant_sn(), taskId);
            taskResultService.changeStatusAndResultV2(contractTask.getId(), contractSubTask.getId(), contractTask.getStatus(), null, false);
        }

        sendKafkaMsg(contractSubTask, contractResponse);
        return new PaywayFailHandleResult(contractTask, contractSubTask, false);
    }

    private void setSubTaskNotAffectTask(ContractSubTask contractSubTask, Long taskId) {
        ContractSubTask subTaskUpdateValue = new ContractSubTask()
                .setId(contractSubTask.getId())
                .setStatus_influ_p_task(0)
                .setStatus(TaskStatus.FAIL.getVal());
        contractSubTask.setStatus_influ_p_task(0)
                .setStatus(TaskStatus.FAIL.getVal());
        contractSubTaskMapper.updateByPrimaryKey(subTaskUpdateValue);
        contractTaskMapper.reduceAffectSubTaskCount(taskId);
    }

    private void sendKafkaMsg(ContractSubTask contractSubTask, ContractResponse contractResponse) {
        try {
            ContractRule contractRule = ruleContext.getContractRule(contractSubTask.getContract_rule());

            PaywayContractFail contractFail = new PaywayContractFail();
            contractFail.setMerchantSn(contractSubTask.getMerchant_sn());
            contractFail.setMerchantId(merchantService.getMerchantBySn(contractSubTask.getMerchant_sn(), null).getId());
            contractFail.setPayway(contractSubTask.getPayway());
            contractFail.setAcquirer(contractRule.getAcquirer());
            contractFail.setFailMsg(contractResponse.getMessage());

            aliKafkaTemplate.send("events_cua_contract_payway-fail", contractFail);
            log.info("send kafka msg success {}", contractFail);
        } catch (Exception e) {
            log.warn("sendKafkaMsg error {} {}", contractSubTask.getMerchant_sn(), contractSubTask.getId(), e);
        }
    }
}
