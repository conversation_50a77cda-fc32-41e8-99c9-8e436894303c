package com.wosai.upay.job.biz.comboparams;

import avro.shaded.com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.*;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.AgentAppidBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.adapter.apollo.MemoApolloConfig;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.WeixinSubAppidDto;
import com.wosai.upay.job.model.dto.WeixinSubDevResp;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.model.converter.MerchantProviderParamsConverter;
import com.wosai.upay.job.service.ConfigSupportService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.WeixinAppidConfig;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @Description: 切换交易参数时 同步merchant_config配置的小程序appid
 * 切换交易参数时 同步最新的subAppid配置
 * <AUTHOR>
 * @Date 2020/8/3 2:36 下午
 **/
@Slf4j
@Component
public class SubAppIdSyns {


    static Retryer retryer;

    static {
        retryer = RetryerBuilder.newBuilder()
                .retryIfException() // 抛出异常会进行重试
                .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS, 10, TimeUnit.SECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(5))
                .build();
    }

    @Lazy
    @Autowired
    MerchantProviderParamsService merchantProviderParamsService;
    @Autowired
    AgentAppidBiz agentAppidBiz;
    @Autowired
    TradeConfigService tradeConfigService;
    @Autowired
    MemoApolloConfig memoApolloConfig;
    @Autowired
    ComposeAcquirerBiz composeAcquirerBiz;
    @Autowired
    private ProviderFactory providerFactory;
    @Autowired
    @Qualifier("syncSubappidThreadPoolTaskScheduler")
    private ThreadPoolTaskScheduler syncSubappidThreadPoolTaskScheduler;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    /**
     * 配置appid
     *
     * @param oldParams
     * @param params
     * @param sync      是否同步配置
     */
    public void addSubAppid(MerchantProviderParams oldParams, MerchantProviderParams params, boolean sync) {
        if (memoApolloConfig.getForceAsync() || !sync) {
            asyncSubAppid(oldParams, params);
        } else {
            syncSubAppid(oldParams, params);
        }
    }

    /**
     * 同步配置
     *
     * @param oldParams
     * @param params
     */
    private void syncSubAppid(MerchantProviderParams oldParams, MerchantProviderParams params) {
        List<WeixinSubAppidDto> list = getAllSubAppid(oldParams, params);
        if (WosaiCollectionUtils.isNotEmpty(list)) {
            list.forEach(dto -> merchantProviderParamsService.justAddWeixinSubAppid(params.getId(), dto));
        }
    }

    /**
     * 异步配置
     *
     * @param oldParams 前一次默认交易参数
     * @param params    当前交易参数
     * <AUTHOR>
     * @Description:
     * @time 10:19 上午 2020/8/27
     **/
    private void asyncSubAppid(MerchantProviderParams oldParams, MerchantProviderParams params) {
        MerchantProviderParams copy = new MerchantProviderParams();
        BeanUtils.copyProperties(params, copy);
        syncSubappidThreadPoolTaskScheduler.getScheduledExecutor().schedule(() -> {
            try {
                List<WeixinSubAppidDto> list = getAllSubAppid(oldParams, copy);
                log.info("params主键:{},appid_config_list:{}", copy.getId(), JSON.toJSONString(list));
                if (WosaiCollectionUtils.isNotEmpty(list)) {
                    list.forEach(dto -> {
                        try {
                            retryer.call(new PaySubAppidSync(copy.getId(), dto));
                        } catch (ExecutionException e) {
                            log.error("retryer call ExecutionException :{}", e);
                        } catch (RetryException e) {
                            log.error("retryer call RetryException :{}", e);
                        }
                    });
                }
                try {
                    // fix CUA-11060 补偿支付目录
                    retryer.call(new PayAuthPathSync(oldParams, copy));
                } catch (ExecutionException e) {
                    log.error("retryer call pay_auth_path ExecutionException :{}", params.getId(), e);
                } catch (RetryException e) {
                    log.error("retryer call pay_auth_path RetryException :{}", params.getId(), e);
                }
            } catch (Exception e) {
                log.error("{} : {} 切换交易参数同步支付appid 重试后失败", copy.getMerchant_sn(), copy.getId(), e);
            }
        }, 30, TimeUnit.SECONDS);
    }

    /**
     * 获取所有需要配置的sub_appid
     *
     * @param oldParams
     * @param params
     * @return
     */
    private List<WeixinSubAppidDto> getAllSubAppid(MerchantProviderParams oldParams, MerchantProviderParams params) {
        List<WeixinSubAppidDto> list = new ArrayList<>();
        if (oldParams != null) {
            MerchantProviderParamsCustomDto customDto = merchantProviderParamsService.findMerchantProviderParamsDetailById(oldParams.getId());
            list.addAll(JSON.parseArray(JSON.toJSONString(customDto.getExtra().get("appid_config_list")), WeixinSubAppidDto.class));
        }

        String subAppId = agentAppidBiz.getSubAppid(params.getPayway(), params.getProvider(), params.getChannel_no(), params.getMerchant_sn());
        WeixinSubAppidDto weixinSubAppidDto = new WeixinSubAppidDto();
        weixinSubAppidDto.setType(1).setApp("切换交易参数同步最新appid配置")
                .setRemark("切换交易参数同步最新appid配置").setSub_appid(subAppId);
        list.add(weixinSubAppidDto);
        return list;
    }

    /**
     * 配置支付subAppid
     **/
    class PaySubAppidSync implements Callable {

        private String paramsId;
        private WeixinSubAppidDto subAppidDto;

        public PaySubAppidSync(String paramsId, WeixinSubAppidDto weixinSubAppidDto) {
            this.paramsId = paramsId;
            this.subAppidDto = weixinSubAppidDto;
        }


        @Override
        public Object call() {
            try {
                //addWeixinSubAppid添加后会记录在params的extra中
                merchantProviderParamsService.justAddWeixinSubAppid(paramsId, subAppidDto);
            } catch (Exception e) {
                log.error("paramsId:{},subAppidDto:{} 同步最新subAppid失败", paramsId, JSONObject.toJSONString(subAppidDto), e);
                List<String> asyncSubAppidRetryMsg = applicationApolloConfig.getAsyncSubAppidRetryMsg();
                if (asyncSubAppidRetryMsg.stream().anyMatch(r -> WosaiStringUtils.contains(e.getMessage(), r))) {
                    throw e;
                }
            }
            return null;
        }
    }

    /**
     * 配置支付目录
     **/
    class PayAuthPathSync implements Callable {

        private final MerchantProviderParams oldParams;
        private final MerchantProviderParams params;

        public PayAuthPathSync(MerchantProviderParams oldParams, MerchantProviderParams params) {
            this.oldParams = oldParams;
            this.params = params;
        }


        @Override
        public Object call() {
            try {
                List<String> needAddJsapiPaths = getNeedAddJsapiPaths();
                if (WosaiCollectionUtils.isNotEmpty(needAddJsapiPaths)) {
                    BasicProvider provider = providerFactory.getProvider(String.valueOf(params.getProvider()));
                    if (Objects.isNull(provider)) {
                        return null;
                    }
                    WeixinConfig weixinConfig = new WeixinConfig();
                    weixinConfig.setPayAuthPath(needAddJsapiPaths);
                    weixinConfig.setWeixinMchId(params.getPay_merchant_id());
                    WeixinSubDevResp resp = provider.weixinSubDevConfig(weixinConfig, params);
                    if (Objects.equals(0, resp.getCode())) {
                        throw new CommonPubBizException("微信开发配置失败：" + resp.getMessage());
                    }
                }
            } catch (Exception e) {
                log.error("paramsId:{} 配置支付目录失败", params.getId(), e);
                List<String> asyncSubAppidRetryMsg = applicationApolloConfig.getAsyncSubAppidRetryMsg();
                if (asyncSubAppidRetryMsg.stream().anyMatch(r -> WosaiStringUtils.contains(e.getMessage(), r))) {
                    throw e;
                }
            }
            return null;
        }

        private List<String> getNeedAddJsapiPaths() {
            WxMchInfo oldWxMchInfo = composeAcquirerBiz.getWxMchInfo(oldParams);
            if (Objects.isNull(oldWxMchInfo.getSubdevConfig()) || WosaiCollectionUtils.isEmpty(oldWxMchInfo.getSubdevConfig().getJsapi_path_list())) {
                return new ArrayList<>();
            }
            return oldWxMchInfo.getSubdevConfig().getJsapi_path_list();
        }
    }
}
