package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.MerchantProviderParamsExtMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExt;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.acquirer.SyncMchStatusResp;
import com.wosai.upay.job.model.acquirer.SyncSubMchIdStatusResp;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.AlipayBusinessFileds;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianParam;
import com.wosai.upay.merchant.contract.model.weixin.*;
import com.wosai.upay.merchant.contract.service.TongLianService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-04-16
 */
@Component("tonglian-biz")
@Slf4j
public class TongLianAcquirerBiz implements IAcquirerBiz {

    @Autowired
    private TongLianService tonglianService;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private ParamContextBiz paramContextBiz;

    @Autowired
    private WechatAuthBiz wechatAuthBiz;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    MerchantProviderParamsExtMapper merchantProviderParamsExtMapper;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private ContractSubTaskDAO contractSubTaskDAO;

    @Autowired
    private McProviderDAO mcProviderDAO;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_TONGLIAN;
    }

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        return BeanUtil.getPropString(merchantConfig, "params.tl_trade_params.tl_mch_id");
    }


    @Override
    public String getNormalWxRule() {
        return ContractRuleConstants.TONGLIAN_NORMAL_WEIXIN_RULE;
    }

    @Override
    public void updateWeixinParams(MerchantProviderParamsDto paramsDto, Map params) {
        try {
            Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(paramsDto.getMerchant_sn(), null);
            TongLianParam tongLianParam = contractParamsBiz.buildContractParamsByPayMchId(paramsDto.getPay_merchant_id(), TongLianParam.class);
            ContractResponse response = tonglianService.updateWeixinWithParams(paramContext, tongLianParam);
            log.info("tonglianMchContractServicePhone result:{},{}", params, response);
        } catch (Exception e) {
            log.error("tonglianMchContractServicePhone error:{},{}", params, e);
        }
    }

    @Override
    public ApplySpecialFeeRateResponse modifyFeeRate(String merchantSn, ModifySpecialFeeRateParam param, ContractChannel contractChannel) {
        return tonglianService.modifyRateApply(param, contractChannel.buildAuthV3Param(), contractChannel.getChannel());
    }

    @Override
    public ApplySpecialFeeRateResponse applyFeeRate(String merchantSn, ApplySpecialFeeRateParam param, ContractChannel contractChannel) {
        return tonglianService.applyFeeRate(param, contractChannel.buildAuthV3Param(), contractChannel.getChannel());
    }

    @Override
    public ApplySpecialFeeRateQueryResponse queryRateApplyStatus(String merchantSn, String applicationId, ContractChannel contractChannel) {
        return tonglianService.queryRateApplyStatus(applicationId, contractChannel.getChannel());
    }


    @Override
    public Map<String, Object> getUnionOpenParam(String merchantSn) {
        Map<String, Object> result = new HashMap<>();
        MerchantProviderParams unionOpenParam = merchantProviderParamsMapper.getUnionOpenParam(merchantSn);
        if (ObjectUtils.isEmpty(unionOpenParam)) {
            return null;
        }
        MerchantProviderParamsExt merchantProviderParamsExt = merchantProviderParamsExtMapper.getByParamId(unionOpenParam.getId(), MerchantProviderParamsExt.UNION_OPEN_TYPE);
        if (merchantProviderParamsExt.isUnionAudited()) {
            result.put("merNo", merchantProviderParamsExt.getExt_field_2());
            result.put("union", unionOpenParam.getPay_merchant_id());
        }
        return result;
    }

    /**
     * 微信高校食堂 获取商户 渠道号
     *
     * @return
     */
    @Override
    public String getSchoolCanteenChannelNo(String merchantSn) {
        return applicationApolloConfig.getUseSpecialChannel() ? "254313567" : "313848752";
    }

    @Override
    public WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        SubdevConfigResp subdevConfigResp = tonglianService.queryWeChatAppIdParams(providerParams.getPay_merchant_id());
        MchInfo mchInfo = null;
        try {
            mchInfo = tonglianService.queryWeChatMchInfoParams(providerParams.getPay_merchant_id());
        } catch (Exception e) {
        }
        return new WxMchInfo().setMchInfo(mchInfo).setSubdevConfig(subdevConfigResp);
    }

    @Override
    public AlipayMchInfo getAlipayMchInfo(MerchantProviderParams providerParams) {
        TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_ALI.getValue(), TongLianParam.class);
        Map alipayMap = tonglianService.queryAliSubMchWithParams(providerParams.getPay_merchant_id(), tongLianParam);
        if (!AlipayBusinessFileds.RETURN_ALIPAY_CODE_SUCCESS.equals(WosaiMapUtils.getString(alipayMap, AlipayBusinessFileds.CODE))) {
            throw new ContractBizException(WosaiMapUtils.getString(alipayMap, AlipayBusinessFileds.MSG));
        }
        return JSONObject.toJavaObject(JSONObject.parseObject(JSONObject.toJSONString(alipayMap)), AlipayMchInfo.class);
    }

    /**
     * 修改支付宝支付宝商户名信息
     *
     * @param params
     * @return
     */
    @Override
    public ContractResponse updateAlipayParams(MerchantProviderParams params, AlipayMchInfo alipayMchInfo) {
        Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(params.getMerchant_sn(), null);
        if(alipayMchInfo.getForce_micro()){
            contextParam.put("forceMicro",true);
        }
        TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_ALI.getValue(), TongLianParam.class);
        return tonglianService.updateAlipayMerchantNameWithParams(params.getPay_merchant_id(),contextParam, tongLianParam);
    }

    @Override
    public void syncMchInfo2PayWay(String merchantSn, int payWay) {
        if (payWay != PaywayEnum.ALIPAY.getValue() && payWay != PaywayEnum.WEIXIN.getValue()) {
            throw new ContractBizException("商户所在收单机构不支持向payway=" + payWay + "支付源同步信息");
        }

        Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);

        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_ALI.getValue(), TongLianParam.class);
            response = tonglianService.updateAlipayWithParams(contextParam, tongLianParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_WX.getValue(), TongLianParam.class);
            response = tonglianService.updateWeixinWithParams(contextParam, tongLianParam);
        }
        if (response == null) {
            throw new ContractBizException("同步失败：未获取到同步结果");
        }
        if (!response.isSuccess()) {
            throw new ContractBizException("同步失败：" + response.getMessage());
        }
    }

    @Override
    public Boolean updateWechatNameAndSettleMentId(MerchantProviderParams params, Map context) {
        WechatAuthBiz.WechatAuthNameAndSettId nameAndSettlementId = wechatAuthBiz.getMerchantNameAndSettlementId(context);
        String merchantName = nameAndSettlementId.getMerchantName();
        String settlementId = nameAndSettlementId.getSettlementId();
        String wx_settlement_id = params.getWx_settlement_id();
        if (Objects.isNull(wx_settlement_id) || !settlementId.equalsIgnoreCase(wx_settlement_id)) {
            return false;
        }
        TongLianParam tongLianParam = contractParamsBiz.buildContractParams(String.valueOf(params.getProvider()), params.getPayway(), params.getChannel_no(), TongLianParam.class);
        ContractResponse response = tonglianService.updateWechatName(params.getPay_merchant_id(), params.getOut_merchant_sn(), merchantName, tongLianParam);
        return response.isSuccess();
    }

    @Override
    public SyncMchStatusResp syncMchStatusToAcquirer(String merchantSn, int status) {
        return new SyncMchStatusResp()
                .setSuccess(Boolean.FALSE)
                .setMessage("商户当前在通联通道，请切换到其他通道后重试");
    }

    @Override
    public List<SyncSubMchIdStatusResp> syncSubMchIdStatus(String merchantSn, int status) {
        // 因为银联打开子商户号经常不生效，故不再关闭子商户号权限
        if (status != ValidStatusEnum.VALID.getValue()) {
            return com.beust.jcommander.internal.Lists.newArrayList();
        }

        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectTlWxAndAlipayParamsByMerchantSn(merchantSn);
        Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        List<SyncSubMchIdStatusResp> result = new ArrayList<>();
        for (MerchantProviderParams params : merchantProviderParams) {
            ContractResponse contractResponse = doSyncSubMchIdStatus(params, status == ValidStatusEnum.VALID.getValue() ? ValidStatusEnum.VALID.getValue(): ValidStatusEnum.INVALID.getValue(), contextParam);
            if (contractResponse.isSuccess()) {
                // 更新子商户号状态
                MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(status == ValidStatusEnum.VALID.getValue() ? ValidStatusEnum.VALID.getValue() : ValidStatusEnum.INVALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
                merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
                result.add(new SyncSubMchIdStatusResp().setSuccess(true).setMessage("success").setSubMchId(params.getPay_merchant_id()));
            } else {
                result.add(new SyncSubMchIdStatusResp().setSuccess(false).setMessage(contractResponse.getMessage()).setSubMchId(params.getPay_merchant_id()));
            }
        }
        log.info("修改子商户号状态,merchant_sn: {} 结果: {}", merchantSn, JSON.toJSONString(result));
        return result;
    }

    private ContractResponse doSyncSubMchIdStatus(MerchantProviderParams params, int status, Map<String, Object> contextParam) {
        try {
            if (params.getPayway().equals(PaywayEnum.ALIPAY.getValue())) {
                TongLianParam tongLianParam = contractParamsBiz.buildContractParamsByParams(params, TongLianParam.class);
                return tonglianService.syncAlipaySubMchIdStatus(params.getPay_merchant_id(), status, contextParam, tongLianParam);
            } else {
                TongLianParam tongLianParam = contractParamsBiz.buildContractParamsByParams(params, TongLianParam.class);
                return tonglianService.syncWechatSubMchIdStatus(params.getPay_merchant_id(), status, contextParam, tongLianParam);
            }
        } catch (Exception e) {
            log.error("通联同步子商户号状态异常, merchant_sn:{} id:{} ", params.getMerchant_sn(), params.getId(), e);
            return new ContractResponse().setCode(405).setMessage(e.getMessage());
        }
    }

    @Override
    public Boolean getAcquirerMchStatus(String merchantSn) {
        throw new CommonPubBizException("商户当前在通联通道，请切换到其他通道后重试");
    }

    @Override
    public Boolean bankAccountConsistent(String merchantSn, Map bankAccount) {
        Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN.getValue(), TongLianParam.class);
        final ContractResponse contractResponse = tonglianService.queryMerchant(contextParam, tongLianParam);
        if(!contractResponse.isSuccess()) {
            throw new CommonPubBizException(contractResponse.getMessage());
        }
        final Map<String, Object> responseParam = contractResponse.getResponseParam();
        final String acctid = BeanUtil.getPropString(responseParam, "acctid");
        //收钱吧银行卡
        final String number = BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.NUMBER);
        log.info("通联通道下商户号merchantSn:{},通联银行账户:{},收钱吧银行账户:{}",merchantSn,acctid,number);
        return Objects.equals(acctid,number);
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        Optional<MerchantProviderParamsDO> unionParam = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, ProviderEnum.PROVIDER_TONGLIAN.getValue(), PaywayEnum.UNIONPAY.getValue());
        if (unionParam.isPresent()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                    .retry(false)
                    .build();
        }
        ContractSubTaskDO contractSubTaskDO = getUnionNetInTask(merchantSn, AcquirerTypeEnum.TONG_LIAN.getValue());
        if (Objects.nonNull(contractSubTaskDO)) {
            if (TaskStatus.PROGRESSING.getVal().equals(contractSubTaskDO.getStatus()) || TaskStatus.PENDING.getVal().equals(contractSubTaskDO.getStatus())) {
                if (ChronoUnit.DAYS.between(contractSubTaskDO.getCreateAt().toLocalDateTime().toLocalDate(), LocalDate.now()) < 7) {
                    return UnionPayOpenStatusQueryResp.builder()
                            .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                            .message("开通中，请稍后重试")
                            .retry(true)
                            .build();
                } else {
                    return UnionPayOpenStatusQueryResp.builder()
                            .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                            .message("暂时无法开通，联系销售支持")
                            .retry(false)
                            .build();
                }
            } else if (TaskStatus.SUCCESS.getVal().equals(contractSubTaskDO.getStatus())) {
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                        .message("暂时无法开通，联系销售支持")
                        .retry(false)
                        .build();
            } else {
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message(WosaiMapUtils.getString(JSON.parseObject(contractSubTaskDO.getResult()), "message"))
                        .retry(false)
                        .build();

            }
        }
        return UnionPayOpenStatusQueryResp.builder()
                .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                .message("暂时无法开通，联系销售支持")
                .retry(false)
                .build();
    }

    private ContractSubTaskDO getUnionNetInTask(String merchantSn, String acquirer) {
        List<ContractSubTaskDO> unionPaySubTasks = contractSubTaskDAO.listContractSubTaskDOsByPayway(merchantSn, ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT, PaywayEnum.UNIONPAY.getValue());
        for (ContractSubTaskDO unionPaySubTask : unionPaySubTasks) {
            String taskAcquirer = "";
            if (WosaiStringUtils.isNotBlank(unionPaySubTask.getContractRule())) {
                ContractRule contractRule = ruleContext.getContractRule(unionPaySubTask.getContractRule());
                taskAcquirer = contractRule.getAcquirer();
            } else {
                Optional<McProviderDO> mcProviderDOOptional = mcProviderDAO.getByBeanName(unionPaySubTask.getChannel());
                if (mcProviderDOOptional.isPresent()) {
                    taskAcquirer = mcProviderDOOptional.get().getAcquirer();
                }
            }

            if (acquirer.contains(McConstant.ACQUIRER_LKL) && taskAcquirer.contains(McConstant.ACQUIRER_LKL)) {
                return unionPaySubTask;
            }
            if (taskAcquirer.equals(acquirer)) {
                return unionPaySubTask;
            }
        }
        return null;
    }
}
