package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.providers.PsbcProvider;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.psbc.bo.CallBackResponse;
import com.wosai.upay.merchant.contract.model.psbc.bo.MerchantStatusCallBack;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 邮储回调处理(merchant_contract项目中contractMerchantCallback调用)实现
 * <AUTHOR>
 * @Date 2021/4/19 15:03
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class PsbcCallBackServiceImpl implements PsbcCallBackService {

    @Autowired
    private PsbcProvider psbcProvider;

    @Autowired
    private ContractSubTaskMapper subTaskMapper;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    ContractTaskBiz contractTaskBiz;

    @Autowired
    private QueryContractStatusHandler queryContractStatusHandler;

    /**
     * 邮储注销状态
     */
    public static final List<String> PSBC_STATUS = Lists.newArrayList("02", "03");


    @Override
    public CallBackResponse psbcCallBackHandle(MerchantStatusCallBack merchantStatusCallBack) {
        //收钱吧商户号
        final String merchantSn = merchantStatusCallBack.getOutMerId();
        //邮储回调信息
        log.info("psbcCallBackHandle merchantSn:{},merchantStatusCallBack:{}", merchantSn, JSONObject.toJSONString(merchantStatusCallBack));
        final CallBackResponse callBackResponse = CallBackResponse.builder().result_code("200").result_message("处理成功").build();
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        if (CollectionUtils.isEmpty(merchant)) {
            //不存在该商户不做业务处理
            return callBackResponse;
        }
        //邮储返回的状态
        final String merSta = merchantStatusCallBack.getMerSta();
        //1:邮储主动发起关闭或者注销商户
        if (PSBC_STATUS.contains(merSta)) {
            return callBackResponse;
        }

        //2:邮储回调通知相应的进件或者审批当前商户是否有邮储处理中的任务
        final List<ContractSubTask> contractSubTasks = subTaskMapper.selectByMerchantSnAndPayway(merchantSn, 0);
        final List<ContractSubTask> tasks = contractSubTasks.stream().filter(task -> Objects.equals(task.getStatus(), 1) && Objects.equals(task.getChannel(), ProviderUtil.PSBC_CHANNEL))
                .sorted(Comparator.comparing(ContractSubTask::getCreate_at, Comparator.nullsLast(Date::compareTo)).reversed()).collect(Collectors.toList());
        //不存在进行中的且状态为1的邮储任务
        if (CollectionUtils.isEmpty(tasks)) {
            return callBackResponse;
        }
        final ContractSubTask subTask = tasks.get(0);
        //处理状态为1的邮储任务
        final HandleQueryStatusResp resp = psbcProvider.psbcResponseHandle(subTask, merchantStatusCallBack);
        queryContractStatusHandler.handTaskAndSubTask(subTask, resp);
        return callBackResponse;
    }

}
