package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.business.log.model.BusinessLog;
import com.wosai.business.log.model.BusinessLogType;
import com.wosai.business.log.service.BizObjectColumnService;
import com.wosai.business.log.service.BizOpLogService;
import com.wosai.business.log.service.BusinessLogService;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.sales.core.model.User;
import com.wosai.sales.core.service.UserService;
import com.wosai.sp.business.logstash.dto.PlatformEnum;
import com.wosai.sp.business.logstash.dto.ValidList;
import com.wosai.sp.business.logstash.dto.req.BsOpLogCreateReqDto;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.constant.CommonConstants;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.dto.SendBusinessLogDto;
import com.wosai.upay.job.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 提供商户日志相关操作，仅用于批量操作
 *
 * <AUTHOR>
 * @date 2019-07-29
 */
@Component
@Slf4j
public class BusinessLogBiz {

    /**
     * 业务对象code，操作merchant/merchant_config
     */
    private static final String BUSINESS_OBJECT_CODE_MERCHANT = "merchant";

    /**
     * 商户交易配置参数
     */
    private static final String BUSINESS_FUNCTION_CODE_TRADE_CONFIG = "1000066";

    /**
     * 强制结算
     */
    private static final String BUSINESS_FUNCTION_CODE_WITHDRAW = "1007202";

    public static final String CHANGE_ACQUIRER_LOG_TEMPLATE_CODE = "22AL331U8SVS";

    @Autowired
    private UserService userService;
    @Autowired
    MerchantService merchantService;
    @Autowired
    private BizOpLogService bizOpLogService;

    @Autowired
    private BizObjectColumnService bizObjectColumnService;

    @Autowired
    private BusinessLogService businessLogService;

    @Autowired
    private BusinessOpLogService businessOpLogService;
    @Autowired
    @Qualifier("businessLogThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor businessLogThreadPoolTaskExecutor;

    private final String merchantProviderParamsPrefix = "merchant_provider_params#";

    private final List<String> merchantProviderParamsManagesFields = Lists.newArrayList(
            "extra", "payway", "pay_merchant_id", "status", "weixin_sub_appid"
    );

    @Value("${bank.trade.protection.log.template.code}")
    private String bankTradeProtectionAndChangeLogTemplateCode;

    public void sendMerchantConfigBusinessLog(Map before, Map after, String userId, String userName, String remark) {
        SendBusinessLogDto dto = new SendBusinessLogDto()
                .setChange_before(before)
                .setChange_after(after)
                .setBusiness_object_code(BUSINESS_OBJECT_CODE_MERCHANT)
                .setBusiness_function_code(BUSINESS_FUNCTION_CODE_TRADE_CONFIG)
                .setTable_name("merchant_config")
                .setObject_id(BeanUtil.getPropString(after, MerchantConfig.MERCHANT_ID))
                .setNeedColumns(Arrays.asList("merchant_config#payway"))
                .setUser_id(userId)
                .setUser_name(userName)
                .setRemark(remark)
                .setOp_type(BizOpLog.OP_TYPE_BATCH_MODIFY);
        if (StringUtils.isEmpty(dto.getUser_id())) {
            dto.setUser_id("进件服务");
        }
        if (StringUtils.isEmpty(dto.getUser_name())) {
            dto.setUser_name("进件服务批量切交易参数");
        }
        if (StringUtils.isEmpty(dto.getRemark())) {
            dto.setRemark("进件服务批量切交易参数");
        }
        sendBusinessLog(dto);
    }

    public void sendWithdrawLog(String merchantId, long balance, String remark) {
        sendWithdrawLog(merchantId, balance, "进件服务", "进件服务", remark);
    }

    private void sendWithdrawLog(String merchantId, long balance, String userId, String userName, String remark) {
        BusinessLogType logType = BusinessLogType.builder().objectCode(BUSINESS_OBJECT_CODE_MERCHANT)
                .functionCode(BUSINESS_FUNCTION_CODE_WITHDRAW)
                .opType(BizOpLog.OP_TYPE_OTHER)
                .tableName("wallet")
                .remark(remark)
                .build();
        BusinessLog businessLog = BusinessLog.builder()
                .opId(UUID.randomUUID().toString())
                .objectId(merchantId)
                .platform("contract")
                .userId(userId)
                .userName(userName)
                .logType(logType)
                .before(CollectionUtil.hashMap("balance", balance))
                .after(CollectionUtil.hashMap("balance", 0))
                .build();

        businessLogService.sendBusinessLog(businessLog);
    }

    public void sendChangeAcquirerLog(String merchantId, String sourceAcqurier, String targetAcquirer,  String remark) {
        sendChangeAcquirerNewLog(merchantId, sourceAcqurier, targetAcquirer, "进件服务", "进件服务", remark);
    }

    public void sendChangeAcquirerLog(String merchantId, String sourceAcquirer, String targetAcquirer,  String remark, LogParamsDto logParamsDto) {
        ValidList<BsOpLogCreateReqDto.Diff> diffList = buildChangeAcquirerDiff(sourceAcquirer, targetAcquirer);
        String templateCode = org.apache.commons.lang3.StringUtils.isBlank(logParamsDto.getSceneTemplateCode())
                ? CHANGE_ACQUIRER_LOG_TEMPLATE_CODE : logParamsDto.getSceneTemplateCode();
        PlatformEnum platformEnum = Objects.isNull(PlatformEnum.getEnumByCode(logParamsDto.getLogPlatformEnum().getCode()))
                ? PlatformEnum.CRM_APP : PlatformEnum.getEnumByCode(logParamsDto.getLogPlatformEnum().getCode());
        String finalRemark ;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(logParamsDto.getRemark())) {
            finalRemark = logParamsDto.getRemark() + "，" + remark;
        } else {
            finalRemark = remark;
        }
        String userId = org.apache.commons.lang3.StringUtils.isBlank(logParamsDto.getUserId())
                ? "进件服务" : logParamsDto.getUserId();
        String userName = org.apache.commons.lang3.StringUtils.isBlank(logParamsDto.getUserName())
                ? "进件服务" : logParamsDto.getUserName();
        BsOpLogCreateReqDto dto = getContractJobBsOpLogCreateReqDto(merchantId, templateCode, diffList, userId, userName, finalRemark, platformEnum);
        sendBusinessLogStash(dto);
    }

    private ValidList<BsOpLogCreateReqDto.Diff> buildChangeAcquirerDiff(String sourceAcquirer, String targetAcquirer) {
        ValidList<BsOpLogCreateReqDto.Diff> diffList = new ValidList<>();
        BsOpLogCreateReqDto.Diff diff = new BsOpLogCreateReqDto.Diff();
        diff.setColumnCode("contract_status#acquirer");
        diff.setValueBefore(sourceAcquirer);
        diff.setValueAfter(targetAcquirer);
        diffList.add(diff);
        return diffList;
    }


    public void sendChangeAcquirerNewLog(String merchantId, String sourceAcqurier, String targetAcquirer, String userId, String userName, String remark) {
        ValidList<BsOpLogCreateReqDto.Diff> diffList = buildChangeAcquirerDiff(sourceAcqurier, targetAcquirer);
        BsOpLogCreateReqDto dto = getContractJobBsOpLogCreateReqDto(merchantId, CHANGE_ACQUIRER_LOG_TEMPLATE_CODE, diffList,
                userId, userName, remark, PlatformEnum.CRM_APP);
        sendBusinessLogStash(dto);
    }


    public void recordBankLog(BankDirectApply bankDirectApply) {
        //生成商户日志
        Map merchant = merchantService.getMerchantByMerchantSn(bankDirectApply.getMerchant_sn());
        Map body = JSONObject.parseObject(bankDirectApply.getForm_body(), Map.class);
        String userId = BeanUtil.getPropString(body, BankDirectApplyConstant.CRM_UESRID);
        String userName = "";
        if(StringUtils.isEmpty(userId)) {
            userName = BeanUtil.getPropString(body, "operate");
            userId = "spa线下导入";
            if (StringUtils.isEmpty(userName)) {
                userName = userId;
            }
        }else  {
            final Map user = userService.getUser(userId);
            userName = BeanUtil.getPropString(user, User.LINKMAN);
        }
        String feeRate = BeanUtil.getPropString(body, "merchant_config");
        final String from = BeanUtil.getPropString(body, "from");
        String remark = feeRate;
        if(!StringUtils.isEmpty(from)) {
            remark = "入网方式:线下导入,费率:"+remark;
        }else {
            remark = "入网方式:api进件,费率:"+remark;
        }
        sendContractBankLog(BeanUtil.getPropString(merchant, DaoConstants.ID),userId,userName,remark);
    }

    /**
     * 商户入网银行通道
     * @param merchantId
     * @param userId
     * @param userName
     * @param remark
     */
    public void sendContractBankLog(String merchantId, String userId, String userName, String remark) {
        ValidList<BsOpLogCreateReqDto.Diff> diffList = new ValidList<>();
        BsOpLogCreateReqDto.Diff diff = new BsOpLogCreateReqDto.Diff();
        diff.setColumnCode("bankDirectApply#extra_remark");
        diff.setValueBefore("");
        diff.setValueAfter(remark);
        diffList.add(diff);
        BsOpLogCreateReqDto dto = getContractJobBsOpLogCreateReqDto(merchantId, "AUS85PIP28RF", diffList,
                userId, userName, remark, PlatformEnum.CRM);
        sendBusinessLogStash(dto);
    }

    BsOpLogCreateReqDto getContractJobBsOpLogCreateReqDto(String merchantId, String templateCode, ValidList<BsOpLogCreateReqDto.Diff> diffList,
                                                          String userId, String userName, String remark, PlatformEnum platformEnum) {
        BsOpLogCreateReqDto dto = new BsOpLogCreateReqDto();
        dto.setLogTemplateCode(templateCode);
        dto.setOpObjectId(merchantId);
        dto.setDiffList(diffList);
        dto.setOpUserId(userId);
        dto.setOpUserName(userName);
        dto.setRemark(remark);
        if (Objects.nonNull(platformEnum)) {
            dto.setPlatformCode(platformEnum.getCode());
        }
        dto.setCtime(System.currentTimeMillis());
        return dto;
    }

    public void sendBusinessLog(SendBusinessLogDto dto) {
        businessLogThreadPoolTaskExecutor.submit(() -> {
            try {
                doSendBusinessLog(dto);
                log.info("sendBusinessLog : {}", dto);
            } catch (Exception e) {
                log.error("sendBusinessLog {}  error:", dto, e);
            }
        });
    }

    public void sendMerchantProviderParamsLog(MerchantProviderParamsDto before, MerchantProviderParamsDto after, LogParamsDto dto, List<String> specificFields, String merchantId) {
        try {
            if (Objects.isNull(before) && Objects.isNull(after)) {
                return;
            }
            if (CollectionUtils.isEmpty(specificFields)) {
                specificFields = merchantProviderParamsManagesFields;
            }
            if (StringUtils.isEmpty(merchantId)) {
                String merchantSn = Objects.isNull(before) ? after.getMerchant_sn() : before.getMerchant_sn();
                if (StringUtils.isEmpty(merchantSn)) {
                    return;
                }
                Map merchantMap = merchantService.getMerchantBySn(merchantSn);
                merchantId = WosaiMapUtils.getString(merchantMap, CommonModel.ID);
            }
            Map originMap = JSONObject.parseObject(JSONObject.toJSONString(before), Map.class);
            Map modifiedMap = JSONObject.parseObject(JSONObject.toJSONString(after), Map.class);
            BsOpLogCreateReqDto bsOpLogCreateReqDto = new BsOpLogCreateReqDto();
            bsOpLogCreateReqDto.setLogTemplateCode(dto.getSceneTemplateCode());
            bsOpLogCreateReqDto.setPlatformCode(dto.getLogPlatformEnum().getCode());
            bsOpLogCreateReqDto.setOpObjectId(merchantId);
            ValidList<BsOpLogCreateReqDto.Diff> validList = new ValidList<>();
            for (String field : specificFields) {
                String beforeValue = WosaiMapUtils.getString(originMap, field);
                String afterValue = null;
                if (Objects.equals("status", field) && Objects.nonNull(after)) {
                    afterValue = "设置默认参数 " + "payway: " + after.getPayway() + " 支付源商户号：" + after.getPay_merchant_id() + " id：" + after.getId();
                } else {
                    afterValue = WosaiMapUtils.getString(modifiedMap, field);
                }
                if (!Objects.equals(beforeValue, afterValue) || Objects.equals(field, CommonConstants.PAY_WAY)) {
                    BsOpLogCreateReqDto.Diff diff = new BsOpLogCreateReqDto.Diff();
                    diff.setColumnCode(merchantProviderParamsPrefix + field);
                    diff.setValueBefore(beforeValue);
                    diff.setValueAfter(afterValue);
                    validList.add(diff);
                }
            }
            if (validList.isEmpty()) {
                return;
            }
            bsOpLogCreateReqDto.setSameSaveColumnCode(merchantProviderParamsPrefix + CommonConstants.PAY_WAY);
            bsOpLogCreateReqDto.setDiffList(validList);
            bsOpLogCreateReqDto.setOpUserId(dto.getUserId());
            bsOpLogCreateReqDto.setOpUserName(dto.getUserName());
            bsOpLogCreateReqDto.setRemark(dto.getRemark());
            bsOpLogCreateReqDto.setCtime(System.currentTimeMillis());
            sendBusinessLogStash(bsOpLogCreateReqDto);
        } catch (Exception e) {
            log.error("sendMerchantProviderParamsLog,发送商户日志失败", e);
        }
    }

    /**
     * 记录银行保障交易自动切收单机构日志
     * 便于运营人员识别切收单机构是由银行保障交易配置自动触发
     * 银行->三方 or 三方->银行
     *
     * @param merchantId    商户id
     * @param bankAcquirer  银行收单机构
     * @param thirdAcquirer 三方收单机构
     * @param isBankToThird 是否银行切三方 true:银行切三方 false:三方切银行
     */
    public void recordBankProtectionAutoChangeLog(String merchantId, String bankAcquirer, String thirdAcquirer, boolean isBankToThird) {
        String remark = isBankToThird ? "银行交易报错,系统自动回切三方通道" : "银行交易报错,客户经理确认工单,系统自动回切银行通道";
        ValidList<BsOpLogCreateReqDto.Diff> validList = new ValidList<>();
        BsOpLogCreateReqDto.Diff diff = new BsOpLogCreateReqDto.Diff();
        diff.setColumnCode("mc_acquirer_change#target_acquirer");
        diff.setValueBefore(isBankToThird ? bankAcquirer : thirdAcquirer);
        diff.setValueAfter(isBankToThird ? thirdAcquirer : bankAcquirer);
        validList.add(diff);
        BsOpLogCreateReqDto reqDto = BsOpLogCreateReqDto.builder()
                .logTemplateCode(bankTradeProtectionAndChangeLogTemplateCode)
                .opObjectId(merchantId)
                .remark(remark)
                .diffList(validList)
                .opUserId("system")
                .opUserName("system")
                .platformCode(PlatformEnum.CRM_APP.getCode())
                .build();
        sendBusinessLogStash(reqDto);
    }

    /**
     * 记录商户银行保障交易配置日志
     * 关闭保障 or 恢复保障
     *
     * @param merchantId 商户id
     * @param isOpen     是否打开 true:恢复保障 false:关闭保障
     */
    public void recordMerchantBankTradeProtectionConfigLog(String merchantId, boolean isOpen) {
        String remark = isOpen ? "商户已恢复银行交易保障服务" : "商户已关闭银行交易保障服务";
        ValidList<BsOpLogCreateReqDto.Diff> validList = new ValidList<>();
        BsOpLogCreateReqDto.Diff diff = new BsOpLogCreateReqDto.Diff();
        diff.setColumnCode("bank_trade_protection_merchant_config#open_status");
        diff.setValueBefore(isOpen ? "关闭" : "打开");
        diff.setValueAfter(isOpen ? "打开" : "关闭");
        validList.add(diff);
        BsOpLogCreateReqDto reqDto = BsOpLogCreateReqDto.builder()
                .logTemplateCode(bankTradeProtectionAndChangeLogTemplateCode)
                .opObjectId(merchantId)
                .remark(remark)
                .diffList(validList)
                .opUserId("system")
                .opUserName("system")
                .platformCode(PlatformEnum.APP.getCode())
                .build();
        sendBusinessLogStash(reqDto);
    }

    public void recordKeepAliveConfigLog(String merchantId, boolean isOpen, LogParamsDto logParamsDto) {
        ValidList<BsOpLogCreateReqDto.Diff> validList = new ValidList<>();
        BsOpLogCreateReqDto.Diff diff = new BsOpLogCreateReqDto.Diff();
        diff.setColumnCode("keepAliveConfig#status");
        diff.setValueBefore(isOpen ? "关闭" : "打开");
        diff.setValueAfter(isOpen ? "打开" : "关闭");
        validList.add(diff);
        BsOpLogCreateReqDto reqDto = BsOpLogCreateReqDto.builder()
                .logTemplateCode(logParamsDto.getSceneTemplateCode())
                .opObjectId(merchantId)
                .remark(logParamsDto.getRemark())
                .diffList(validList)
                .opUserId(logParamsDto.getUserId())
                .opUserName(logParamsDto.getUserName())
                .platformCode(logParamsDto.getLogPlatformEnum().getCode())
                .build();
        sendBusinessLogStash(reqDto);
    }




    /**
     * 新商户日志
     * @param dto
     */
    private void sendBusinessLogStash(BsOpLogCreateReqDto dto) {
        businessLogThreadPoolTaskExecutor.submit(() -> {
            try {
                doSendBusinessLogStash(dto);
            } catch (Exception e) {
                log.error("sendBusinessLog {}  error:", dto, e);
            }
        });
    }

    protected void doSendBusinessLog(SendBusinessLogDto dto) {
        if (dto.getNeedColumns() == null) {
            dto.setNeedColumns(new ArrayList<>());
        }

        String op_id = UUID.randomUUID().toString();
        long op_time = System.currentTimeMillis();


        Set<String> columns = getBusinessObjectColumnCode(dto.getBusiness_object_code());
        if (columns.isEmpty()) {
            log.info("变化的业务字段列表为空 不做记录{} {}", dto.getChange_before(), dto.getChange_after());
            return;
        }
        columns.addAll(dto.getNeedColumns());
        columns.forEach(column -> {
            String realColumn = column.replace(dto.getTable_name() + "#", "");
            Object beforeValue = BeanUtil.getNestedProperty(dto.getChange_before(), realColumn);
            Object afterValue = BeanUtil.getNestedProperty(dto.getChange_after(), realColumn);
            boolean equal = Objects.equals(beforeValue, afterValue);

            if (dto.getNeedColumns().contains(column) || !equal) {
                bizOpLogService.createBizOpLog(CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, dto.getBusiness_object_code(),
                        BizOpLog.BUSINESS_SYSTEM_CODE, "contract",
                        BizOpLog.BUSINESS_SYSTEM_VERSION, "1.0",
                        BizOpLog.BUSINESS_FUNCTION_CODE, dto.getBusiness_function_code(),
                        BizOpLog.OP_TYPE, dto.getOp_type(),
                        BizOpLog.OP_ID, op_id,
                        BizOpLog.OP_USER_ID, dto.getUser_id(),
                        BizOpLog.OP_USER_NAME, dto.getUser_name(),
                        BizOpLog.OP_OBJECT_ID, dto.getObject_id(),
                        BizOpLog.OP_TIME, op_time,
                        BizOpLog.BUSINESS_OBJECT_COLUMN_CODE, column,
                        BizOpLog.OP_COLUMN_VALUE_BEFORE, beforeValue,
                        BizOpLog.OP_COLUMN_VALUE_AFTER, afterValue,
                        BizOpLog.REMARK, dto.getRemark()
                ));
            }
        });
    }

    /**
     * 新商户日志
     */
    private void doSendBusinessLogStash(BsOpLogCreateReqDto bsOpLogCreateReqDto) {
        log.info("doSendBusinessLogStash,[新商户日志]>>>>入参:{}", JSONObject.toJSONString(bsOpLogCreateReqDto));
        // 如果remark的长度超过256 需要截取
        if (org.apache.commons.lang3.StringUtils.isNotBlank(bsOpLogCreateReqDto.getRemark())
                && bsOpLogCreateReqDto.getRemark().length() > 256) {
            bsOpLogCreateReqDto.setRemark(bsOpLogCreateReqDto.getRemark().substring(0, 256));
        }
        businessOpLogService.createBusinessLogForAsync(bsOpLogCreateReqDto);
    }


    private Set<String> getBusinessObjectColumnCode(String businessObjectCode) {
        Map columns = bizObjectColumnService.findAllBizObjectColumns(businessObjectCode);
        if (WosaiMapUtils.isNotEmpty(columns)) {
            return new HashSet<>(columns.keySet());
        }
        return new HashSet<>();
    }


}
