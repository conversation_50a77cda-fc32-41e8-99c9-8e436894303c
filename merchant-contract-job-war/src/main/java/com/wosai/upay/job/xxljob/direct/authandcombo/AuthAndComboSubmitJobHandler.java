package com.wosai.upay.job.xxljob.direct.authandcombo;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.wosai.data.util.CollectionUtil;
import com.wosai.model.SystemResponse;
import com.wosai.service.SystemService;
import com.wosai.upay.job.Constants.MerchantChangeDataConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.AuthAndComboTaskBiz;
import com.wosai.upay.job.mapper.AuthAndComboTaskMapper;
import com.wosai.upay.job.model.AuthAndComboTask;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * xxl_job_desc: 授权和费率套餐任务-提交
 * <AUTHOR>
 * @date 2025/4/17
 */
@Slf4j
@Component("AuthAndComboSubmitJobHandler")
public class AuthAndComboSubmitJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private AuthAndComboTaskMapper authAndComboTaskMapper;
    @Autowired
    private AuthAndComboTaskBiz authAndComboTaskBiz;
    @Autowired
    private SystemService systemService;

    private static final Retryer<Object> RETRYER = RetryerBuilder.newBuilder()
            .retryIfResult(o -> Objects.equals(o, false))
            .withWaitStrategy(WaitStrategies.fixedWait(10, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterAttempt(3))
            .build();

    @Override
    public String getLockKey() {
        return "AuthAndComboSubmitJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        Long queryTime = param.getQueryTime();
        Integer queryLimit = param.getBatchSize();
        List<AuthAndComboTask> waitForSubmit = authAndComboTaskMapper.selectWaitForSubmit(new Date(System.currentTimeMillis() - queryTime), queryLimit);

        waitForSubmit.forEach(task -> {
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                RETRYER.call(() -> {
                    try {
                        // 这里行业信息还没写回原表,所以要跳过行业和结算ID是否匹配的校验
                        SystemResponse systemResponse = systemService.createAuthTask(CollectionUtil.hashMap("reason", "industry", "subMchId", task.getSub_mch_id(), "isCheckSettlementId", false));
                        return systemResponse.getResult();
                    } catch (Exception e) {
                        log.error("商户信息变更提交微信认证申请异常,id: {} , sn : {}", task.getId(), task.getMerchant_sn(), e);
                        return false;
                    }
                });
                // 不关心结果
                authAndComboTaskBiz.changeStatusAndSendMessage(task, MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_WAIT_AUTH, null);
            } catch (Exception e) {
                log.error("商户信息变更提交微信认证申请异常,id: {} , sn : {}", task.getId(), task.getMerchant_sn(), e);
                authAndComboTaskBiz.changeStatusAndSendMessage(task, MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_WAIT_AUTH, null);
            }
        });
    }
}
