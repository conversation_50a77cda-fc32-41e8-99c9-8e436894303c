package com.wosai.upay.job.model.dto;

import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/8/1
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
public class PaywayFailHandleResult {

    private ContractTask contractTask;

    private ContractSubTask contractSubTask;

    private boolean finish;
}
