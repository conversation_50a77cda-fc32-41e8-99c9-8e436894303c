package com.wosai.upay.job.util.luzhou;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.signers.SM2Signer;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.stereotype.Component;

import com.wosai.upay.job.util.luzhou.sm.SM2Util;
import com.wosai.upay.job.util.luzhou.sm.SM4Utils;

/**
 * 	通过国密签名
 * <AUTHOR>
 * TODO 这个签名可以是单例的吗?还是得每次请求重新生成一个签名对象?????
 */
@Component
public class SMSaltSigner {

    /** 密钥长度{@value}(按byte数组计算，转成base64后不一样) */
    private static final int KEY_LEN = 32;

    public String genKey() {
        return Base64.getEncoder().encodeToString(StringUtil.genRandomByte(KEY_LEN));
    }

    public String sign(ISignBlock signBlock, String key) {
        String input = signBlock.getSignBlock();
        String dataWithSalt = input + key;
        System.out.println(dataWithSalt);
        byte[] srcByte = dataWithSalt.getBytes(StandardCharsets.UTF_8);
        byte[] md = new byte[32];
        //not thread safe. TODO check this
        SM3Digest sm3 = new SM3Digest();
        sm3.update(srcByte, 0, srcByte.length);
        sm3.doFinal(md, 0);
        return Base64.getEncoder().encodeToString(md);
    }

    public static void main(String[] args) {
        String dataWithSalt = "/payGateway/payApi/merchant/getSm4Key&{\"isRefresh\":0}&M00014823&20230831163311FSuURpSNfUZhYz+T3woAwqkMqaHtWRotdtw4/Lj+yiI=";
        System.out.println(dataWithSalt);
        byte[] srcByte = dataWithSalt.getBytes(StandardCharsets.UTF_8);
        byte[] md = new byte[32];
        SM3Digest sm3 = new SM3Digest();
        sm3.update(srcByte, 0, srcByte.length);
        sm3.doFinal(md, 0);
        String ret = Base64.getEncoder().encodeToString(md);
        System.out.println(ret);
    }

    /**
     * sm4加密
     * @param secretKey 秘钥
     * @param plainText 待加密报文
     * @return 已加密报文
     */
    public String encryptData(String secretKey, String plainText) {
        SM4Utils sm4 = new SM4Utils();
        sm4.secretKey = secretKey;
        sm4.hexString = true;
        String cipherText = sm4.encryptData_ECB(plainText);
        return cipherText;
    }

    /**
     * sm4解密
     * @param secretKey  秘钥
     * @param cipherText  待解密报文
     * @return 已解密报文
     */
    public String decryptData(String secretKey, String cipherText) {
        SM4Utils sm4 = new SM4Utils();
        sm4.secretKey = secretKey;
        sm4.hexString = true;
        String plainText = sm4.decryptData_ECB(cipherText);
        return plainText;
    }

    /**
     * sm2解密
     * @param secretKey  私钥
     * @param cipherText  待解密报文
     * @return 已解密报文
     */
    public String decryptSm2(String secretKey, String cipherText) {
        SM2Util sm2 = new SM2Util();
        byte[] aa = hexStringToBytes(cipherText);
        String deData = sm2.decrypt(aa, secretKey);
        return deData;
    }

    public static byte[] hexStringToBytes(String hexString) {
        if (hexString == null || hexString.equals("")) {
            return null;
        }

        hexString = hexString.toUpperCase();
        int length = hexString.length() / 2;
        char[] hexChars = hexString.toCharArray();
        byte[] d = new byte[length];
        for (int i = 0; i < length; i++) {
            int pos = i * 2;
            d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
        }
        return d;
    }

    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }


    /**
     * 验证SM2签名
     * 
     * @param publicKeyHex sm2公钥的十六进制字符串
     * @param message 原始数据
     * @param signatureHex 签名的十六进制字符串
     * @return boolean
     */
    public boolean verifySignSm2(String publicKeyHex, String message, String signatureHex) {
        try {
            // 获取SM2曲线参数
            X9ECParameters ecParameters = GMNamedCurves.getByName("sm2p256v1");
            ECDomainParameters domainParameters = new ECDomainParameters(ecParameters.getCurve(), ecParameters.getG(),
                ecParameters.getN(), ecParameters.getH());

            // 解析公钥
            byte[] publicKeyBytes = Hex.decode(publicKeyHex);
            ECPoint publicPoint = ecParameters.getCurve().decodePoint(publicKeyBytes);
            ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(publicPoint, domainParameters);

            // 计算消息哈希
            byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
            SM3Digest digest = new SM3Digest();
            digest.update(messageBytes, 0, messageBytes.length);
            byte[] hash = new byte[digest.getDigestSize()];
            digest.doFinal(hash, 0);

            // 解析签名
            byte[] signatureBytes = Hex.decode(signatureHex);

            // 验证签名
            SM2Signer signer = new SM2Signer();
            signer.init(false, publicKeyParameters);
            signer.update(hash, 0, hash.length);
            return signer.verifySignature(signatureBytes);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
