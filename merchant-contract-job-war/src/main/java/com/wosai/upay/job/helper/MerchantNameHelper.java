package com.wosai.upay.job.helper;

import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.wosai.upay.job.biz.paramContext.ParamContextBiz.KEY_BANK_ACCOUNT;
import static com.wosai.upay.job.biz.paramContext.ParamContextBiz.KEY_BUSINESS_LICENCE;

@Component
@RequiredArgsConstructor
public class MerchantNameHelper {

    private final ParamContextBiz paramContextBiz;

    public String getMerchantName(String merchantSn) {
        Map<String, Object> context = paramContextBiz.getParamContextByMerchantSn(merchantSn);

        Map license = (Map) context.get(KEY_BUSINESS_LICENCE);
        Map bankAccount = (Map) context.get(KEY_BANK_ACCOUNT);

        if (BusinessLicenseTypeEnum.isMicro(WosaiMapUtils.getIntValue(license, MerchantBusinessLicence.TYPE, 0))) {
            return "商户_" + BeanUtil.getPropString(bankAccount, MerchantBankAccount.HOLDER);
        } else {
            return BeanUtil.getPropString(license, MerchantBusinessLicence.NAME);
        }
    }
}
