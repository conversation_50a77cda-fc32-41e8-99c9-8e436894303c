package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.mapper.MerchantLakalaContractMapper;
import com.wosai.upay.job.model.MerchantLakalaContract;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: lishuangqiang
 * @Date: 2019/4/14
 * @Description:
 */
@Service
@AutoJsonRpcServiceImpl
public class MerchantLakalaContractServiceImpl implements  MerchantLakalaContractService , InitializingBean {

    @Autowired
    MerchantLakalaContractMapper merchantLakalaContractMapper;
    @Override
    public List<MerchantLakalaContract> selectByParams(String merchant_sn, String contract_id, int opt_type) {
        return merchantLakalaContractMapper.selectByParams(merchant_sn,contract_id,opt_type);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
      // selectByParams("1680002787174","1548267854731",4);
    }
}
