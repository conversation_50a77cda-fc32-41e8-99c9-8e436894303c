package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.aop.gateway.model.ClientSideNoticeSendModel;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.app.dto.MerchantUserSimpleInfo;
import com.wosai.app.service.MerchantUserService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.job.constant.CommonConstants;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import entity.common.PageListResult;
import entity.common.UserEs;
import entity.request.CustomerRelationOriginReq;
import entity.response.CustomerRelationOriginResp;
import facade.ICustomerRelationFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;

/**
 * 包装AOP提供的通知、推送等能力
 *
 * <AUTHOR>
 * @date 2022/12/7
 */
@Component
@Slf4j
public class AopBiz {

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Autowired
    private MerchantUserService merchantUserService;

    @Value("${maintain.devCode}")
    private String maintainCode;

    @Autowired
    private ICustomerRelationFacade customerRelationFacade;

    /**
     * 给商户客户经理发通知到CRM APP
     *
     * @param merchantId
     * @param devCode      开发标识
     * @param templateCode 模板标识
     * @param data
     */
    public void sendNoticeToCrm(String merchantId, String devCode, String templateCode, Map data) {
        final ClientSideNoticeSendModel sendModel = new ClientSideNoticeSendModel();
        try {
            sendModel.setDevCode(devCode);
            sendModel.setTemplateCode(templateCode);
            sendModel.setAccountId(getMaintainUserId(merchantId));
            sendModel.setClientSides(Arrays.asList("TERMINALCRM"));
            sendModel.setTimestamp(System.currentTimeMillis());
            if (WosaiMapUtils.isNotEmpty(data)) {
                sendModel.setData(data);
            }
            clientSideNoticeService.send(sendModel);
            log.info("推送通知给Crm {} {}", merchantId, JSON.toJSONString(sendModel));
        } catch (Exception e) {
            log.error("发送通知异常 {} {}", merchantId, JSON.toJSONString(sendModel), e);
        }
    }



    public UserEs getMaintainUser(String merchantId) throws Exception {
        CustomerRelationOriginReq req = new CustomerRelationOriginReq();
        req.setCustomerIds(Arrays.asList(merchantId));
        req.setConfigCodes(Arrays.asList(maintainCode));
        PageListResult<CustomerRelationOriginResp> customerRelationOrigin = customerRelationFacade.findCustomerRelationOrigin(req);
        if (WosaiCollectionUtils.isEmpty(customerRelationOrigin.getRecords())) {
            throw new ContractBizException("查不到商户维护人");
        }
        return customerRelationOrigin.getRecords().get(0).getUser();
    }


    public String getMaintainUserId(String merchantId) throws Exception {
        return getMaintainUser(merchantId).getId();
    }


    public String getMaintainUserCellPhone(String merchantId) throws Exception {
        return getMaintainUser(merchantId).getCellphone();
    }

    /**
     * 给商户老板发通知到收钱吧APP
     *
     * @param merchantId
     * @param devCode      开发标识
     * @param templateCode 模板标识
     * @param data
     */
    public void sendNoticeToAdmin(String merchantId, String devCode, String templateCode, Map data) {
        final MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
        try {
            MerchantUserSimpleInfo userInfo = merchantUserService.getSuperAdminSimpleInfoByMerchantId(merchantId);
            if (userInfo != null) {
                sendModel.setDevCode(devCode);
                sendModel.setTemplateCode(templateCode);
                sendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                sendModel.setTimestamp(System.currentTimeMillis());
                if (WosaiMapUtils.isNotEmpty(data)) {
                    sendModel.setData(data);
                }
                clientSideNoticeService.sendToMerchantUser(sendModel);
                log.info("推送通知给老板 {} {}", merchantId, JSON.toJSONString(sendModel));
            }
        } catch (Exception e) {
            log.error("发送通知异常 {} {}", merchantId, JSON.toJSONString(sendModel), e);
        }
    }
}
