package com.wosai.upay.job.biz.keepalive.validation.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 校验结果
 */
@Data
public class KeepAliveValidationResult {

    /**
     * 整体是否成功
     */
    private boolean success;

    /**
     * 各规则执行结果
     */
    private List<KeepAliveCheckRuleResult> keepAliveCheckRuleResults;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 被哪个规则阻止
     */
    private String blockedBy;

    public KeepAliveValidationResult() {
        this.keepAliveCheckRuleResults = new ArrayList<>();
    }

    public KeepAliveValidationResult(boolean success) {
        this.success = success;
        this.keepAliveCheckRuleResults = new ArrayList<>();
    }

    public static KeepAliveValidationResult success() {
        return new KeepAliveValidationResult(true);
    }

    public static KeepAliveValidationResult failure(String failureReason, String blockedBy) {
        KeepAliveValidationResult result = new KeepAliveValidationResult(false);
        result.setFailureReason(failureReason);
        result.setBlockedBy(blockedBy);
        return result;
    }

    public void addRuleResult(KeepAliveCheckRuleResult keepAliveCheckRuleResult) {
        if (this.keepAliveCheckRuleResults == null) {
            this.keepAliveCheckRuleResults = new ArrayList<>();
        }
        this.keepAliveCheckRuleResults.add(keepAliveCheckRuleResult);

        // 如果有规则失败，则整体失败
        if (!keepAliveCheckRuleResult.isPassed()) {
            this.success = false;
            if (this.failureReason == null) {
                this.failureReason = keepAliveCheckRuleResult.getMessage();
            }
            if (this.blockedBy == null) {
                this.blockedBy = keepAliveCheckRuleResult.getRuleType();
            }
        }
    }

    @Override
    public String toString() {
        return "ValidationResult{" +
                "success=" + success +
                ", ruleResults=" + keepAliveCheckRuleResults +
                ", failureReason='" + failureReason + '\'' +
                ", blockedBy='" + blockedBy + '\'' +
                '}';
    }
}