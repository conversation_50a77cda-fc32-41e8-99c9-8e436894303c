package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.AntShopTaskConstant;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.enume.AntMerchantExpandOrderStatusEnum;
import com.wosai.upay.job.mapper.AntShopTaskMapper;
import com.wosai.upay.job.model.AntShopTaskExtra;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.DO.AntShopTask;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.util.TxnSeqNoWorker;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.Tuple2;
import com.wosai.upay.merchant.contract.model.bluesea.CustomizedInfo;
import com.wosai.upay.merchant.contract.model.newBlueSea.request.*;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import com.wosai.upay.merchant.contract.service.NewBlueSeaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static javax.management.timer.Timer.ONE_DAY;
import static javax.management.timer.Timer.ONE_MINUTE;

/**
 * @Description: 蚂蚁店铺相关操作
 * <AUTHOR>
 * @Date 2021/2/24 15:09
 */
@Component
@Slf4j
public class AntShopBiz {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private BlueSeaBiz blueSeaBiz;

    @Autowired
    private AntShopTaskMapper antShopTaskMapper;

    @Autowired
    private TxnSeqNoWorker txnSeqNoWorker;

    @Autowired
    private NewBlueSeaService newBlueSeaService;

    @Autowired
    private BlueSeaService blueSeaService;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    public CommonResult handlePaylaterAntShop(String merchantSn, String storeSn, String aliMchId, String account) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = MapUtils.getString(merchant, CommonModel.ID);
        final List<AntShopTask> shopTask = antShopTaskMapper.selectByStoreSn(merchantSn, storeSn, aliMchId);
        final List<AntShopTask> tasks = Optional.ofNullable(shopTask).orElseGet(ArrayList::new).stream()
                .sorted(
                        Comparator.comparing(AntShopTask::getStatus, Comparator.nullsLast(Integer::compareTo).reversed())
                                .thenComparing(AntShopTask::getPriority, Comparator.nullsLast(Date::compareTo).reversed())
                ).collect(Collectors.toList());
        //删除失败的多余数据减少数据量
        CompletableFuture.runAsync(() -> antShopTaskMapper.deleteByMerchantSn(merchantSn));
        if (CollectionUtils.isNotEmpty(tasks)) {
            if (AntShopTaskConstant.TaskStatus.SUCCESS.equals(tasks.get(0).getStatus())) {
                return new CommonResult(CommonResult.SUCCESS, "支付宝门店创建成功", tasks.get(0).getAnt_shop_id());
            } else if (AntShopTaskConstant.TaskStatus.FAIL.equals(tasks.get(0).getStatus())) {
                return new CommonResult(CommonResult.BIZ_FAIL, "支付宝门店创建失败:" + tasks.get(0).getDescription());
            } else {
                return new CommonResult(CommonResult.SUCCESS, "支付宝门店创建中");
            }
        }
        AntShopTask antShopTask = new AntShopTask()
                .setAli_mch_id(aliMchId)
                .setMerchant_sn(merchantSn)
                .setMerchant_id(merchantId)
                .setRetry(0)
                .setBusiness_type(2)
                .setStore_sn(storeSn);
        // 直连待运营审核并且将直连商户标识和支付宝账户放入extra中。 代运营审核需要这个支付宝账号信息
        AntShopTaskExtra extra = new AntShopTaskExtra().setIsDirect(true).setAccount(account);
        antShopTask.setExtra(JSON.toJSONString(extra));

        AntShopTask result = antShopTaskMapper.selectByMerchantSnAndALiMchId(merchantSn, aliMchId, AntShopTaskConstant.TaskStatus.SUCCESS);
        if (result != null) {
            antShopTask.setStatus(AntShopTaskConstant.TaskStatus.QUALIFY);
            //保存数据
            antShopTaskMapper.insertSelective(antShopTask);
            return new CommonResult(CommonResult.SUCCESS, "支付宝门店创建中");
        } else {
            antShopTask.setStatus(AntShopTaskConstant.TaskStatus.WAIT_OPERATION_APPLY);
            //保存数据
            antShopTaskMapper.insertSelective(antShopTask);
            return new CommonResult(210, "支付宝门店创建中");
        }
    }

    public CommonResult handleAntShop(String merchantSn, String storeSn, Integer businessType) {
        //商户基本信息
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = MapUtils.getString(merchant, CommonModel.ID);
        if (WosaiMapUtils.isEmpty(merchant)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "商户不存在");
        }

        if (WosaiStringUtils.isEmpty(storeSn)) {
            List<Map> storeList = blueSeaBiz.getStoreList(merchantId);
            if (WosaiCollectionUtils.isEmpty(storeList)) {
                return new CommonResult(CommonResult.BIZ_FAIL, "商户下没有门店");
            }
            storeSn = BeanUtil.getPropString(storeList.get(0), Store.SN);
        } else {
            Map store = blueSeaBiz.getStoreBySn(storeSn);
            if (!merchantId.equals(BeanUtil.getPropString(store, Store.MERCHANT_ID))) {
                return new CommonResult(CommonResult.BIZ_FAIL, "门店不存在");
            }
        }

        Tuple2<String, Integer> aliMchInfo = blueSeaBiz.getInUseMchIdV2(merchantSn);
        String aliMchId = aliMchInfo.get_1();
        if (WosaiStringUtils.isEmpty(aliMchId)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "商户没有支付宝子商户号");
        }
        if (Objects.equals(ProviderEnum.PROVIDER_HXB.getValue(), aliMchInfo.get_2())) {
            return new CommonResult(CommonResult.BIZ_FAIL, "华夏银行支付宝子商户号不支持");
        }

        final List<AntShopTask> shopTask = antShopTaskMapper.selectByStoreSn(merchantSn, storeSn, aliMchId);
        final List<AntShopTask> tasks = Optional.ofNullable(shopTask).orElseGet(ArrayList::new).stream()
                .filter(task -> !Objects.equals(task.getStatus(), AntShopTaskConstant.TaskStatus.FAIL)).distinct()
                .sorted(
                        Comparator.comparing(AntShopTask::getStatus, Comparator.nullsLast(Integer::compareTo).reversed())
                                .thenComparing(AntShopTask::getPriority, Comparator.nullsLast(Date::compareTo).reversed())
                ).collect(Collectors.toList());
        //删除失败的多余数据减少数据量
        CompletableFuture.runAsync(() -> antShopTaskMapper.deleteByMerchantSn(merchantSn));
        //是否存在
        if (CollectionUtils.isNotEmpty(tasks)) {
            if (AntShopTaskConstant.TaskStatus.SUCCESS.equals(tasks.get(0).getStatus())) {
                return new CommonResult(CommonResult.SUCCESS, "支付宝门店创建成功", tasks.get(0).getAnt_shop_id());
            } else {
                return new CommonResult(CommonResult.SUCCESS, "支付宝门店创建中");
            }
        }
        AntShopTask antShopTask = new AntShopTask()
                .setAli_mch_id(aliMchId)
                .setMerchant_sn(merchantSn)
                .setMerchant_id(merchantId)
                .setRetry(0)
                .setBusiness_type(businessType)
                .setStore_sn(storeSn);
        // 直连商户号
        // fine TODO
        if (Objects.equals(2, aliMchInfo.get_2())) {
            if (!aliMchId.startsWith("2088")) {
                return new CommonResult(CommonResult.BIZ_FAIL, "支付宝商户号不正确");
            }
            // 直连待运营审核并且将直连商户标识放入extra中
            AntShopTaskExtra extra = new AntShopTaskExtra().setIsDirect(true);
            antShopTask.setExtra(JSON.toJSONString(extra));

            AntShopTask result = antShopTaskMapper.selectByMerchantSnAndALiMchId(merchantSn, aliMchId, AntShopTaskConstant.TaskStatus.SUCCESS);
            if (result != null) {
                antShopTask.setStatus(AntShopTaskConstant.TaskStatus.QUALIFY);
                //保存数据
                antShopTaskMapper.insertSelective(antShopTask);
                return new CommonResult(CommonResult.SUCCESS, "支付宝门店创建中");
            } else {
                antShopTask.setStatus(AntShopTaskConstant.TaskStatus.WAIT_OPERATION_APPLY);
                //保存数据
                antShopTaskMapper.insertSelective(antShopTask);
                return new CommonResult(210, "支付宝门店创建中");
            }
        } else {
            antShopTask.setStatus(AntShopTaskConstant.TaskStatus.PENDING);
            //保存数据
            antShopTaskMapper.insertSelective(antShopTask);
            return new CommonResult(CommonResult.SUCCESS, "支付宝门店创建中");
        }
    }

    public void handleSubmitOperationApply(AntShopTask antShopTask) {
        //代运营操作类型
        final String operateType = BlueSeaConstant.AlipayOpenSpOperationApply.OPERATE_TYPE_OPERATION_AUTH;
        //构建请求参数
        final AlipayOpenSpOperationApplyReq req = AlipayOpenSpOperationApplyReq.builder().outBizNo(String.valueOf(txnSeqNoWorker.nextId()))
                .operateType(operateType)
                .accessProductCode(BlueSeaConstant.AlipayOpenSpOperationApply.ACCESS_PRODUCT_CODE_OPENAPI_AUTH_DEFAULT)
                .merchantNo(antShopTask.getAli_mch_id())
                .build();
        // 如果有支付宝账户信息，就传
        AntShopTaskExtra extra = JSON.parseObject(antShopTask.getExtra(), AntShopTaskExtra.class);
        if (Objects.nonNull(extra) && WosaiStringUtils.isNotEmpty(extra.getAccount())) {
            req.setAlipayAccount(extra.getAccount());
        }
        //发起申请
        AliCommResponse<AlipayOpenSpOperationApplyRequest, AlipayOpenSpOperationApplyResponse> response = newBlueSeaService.alipayOpenSpOperationApply(req);
        //主键Id
        final AntShopTask build = AntShopTask.builder().id(antShopTask.getId()).build();
        if (response.isSuccess()) {
            // 设置batchNo和提交时间
            extra.setBatchNo(response.getResp().getBatchNo()).setSubmitOperationTime(System.currentTimeMillis());
            build.setStatus(AntShopTaskConstant.TaskStatus.ALREADY_OPERATION_APPLY).setExtra(JSON.toJSONString(extra))
                    .setRequest_body(JSON.toJSONString(response.getReq())).setResponse_body(JSON.toJSONString(response.getResp()));
            updateAntShopTask(build);
        } else {
            if (response.isSystemFail()) {
                updateReTry(antShopTask, null, null);
            } else {//业务异常
                build.setStatus(AntShopTaskConstant.TaskStatus.FAIL)
                        .setRequest_body(JSON.toJSONString(response.getReq()))
                        .setResponse_body(JSON.toJSONString(response.getResp()))
                        .setDescription(response.getResp().getSubMsg());
                updateAntShopTask(build);
            }
        }
    }

    /**
     * 查询代运营审核状态
     *
     * @param antShopTask
     */
    public void queryOperationApplyResult(AntShopTask antShopTask) {
        final AntShopTask build = AntShopTask.builder().id(antShopTask.getId()).build();
        AntShopTaskExtra extra = JSON.parseObject(antShopTask.getExtra(), AntShopTaskExtra.class);
        //授权超过24小时认为任务失败
        if (System.currentTimeMillis() - extra.getSubmitOperationTime() > ONE_DAY) {
            //任务失败
            build.setStatus(AntShopTaskConstant.TaskStatus.FAIL).setDescription(AntShopTaskConstant.TIME_OUT);
            updateAntShopTask(build);
            return;
        }
        //构建请求参数
        final OpenSpOperationResultQueryReq req = OpenSpOperationResultQueryReq.builder().batchNo(extra.getBatchNo()).operateType(BlueSeaConstant.AlipayOpenSpOperationApply.OPERATE_TYPE_OPERATION_AUTH).build();
        AliCommResponse<AlipayOpenSpOperationResultQueryRequest, AlipayOpenSpOperationResultQueryResponse> response = newBlueSeaService.alipayOpenSpOperationResultQuery(req);
        if (response.isSuccess()) {
            final String handleStatus = response.getResp().getHandleStatus();
            //代运营操作授权成功
            if (BlueSeaConstant.HandleStatus.HANDLE_STATUS_SUCCESS.equalsIgnoreCase(handleStatus)) {
                build.setStatus(AntShopTaskConstant.TaskStatus.QUALIFY).setRequest_body(JSON.toJSONString(req)).setResponse_body(JSON.toJSONString(response.getResp()));
                updateAntShopTask(build);
            } else {
                //代运营操作授权处理中,或者无权限 过一段时间在次查询
                updateReTry(antShopTask, null, null);
            }
        } else if (response.isSystemFail()) {
            updateReTry(antShopTask, null, null);
        } else {
            build.setStatus(AntShopTaskConstant.TaskStatus.FAIL)
                    .setRequest_body(JSON.toJSONString(response.getReq()))
                    .setResponse_body(JSON.toJSONString(response.getResp()))
                    .setDescription(response.getResp().getSubMsg());
            updateAntShopTask(build);
        }
    }

    /**
     * @param
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 16:04 2021/2/24
     */
    public List<AntShopTask> getAntShopTasks(List<Integer> status, List<Integer> type, int limit, long queryTime) {
        long current = System.currentTimeMillis();
        String startTime = StringUtil.formatDate(current - queryTime);
        String endTime = StringUtil.formatDate(current);
        return antShopTaskMapper.selectByStatus(status, type, limit, startTime, endTime);
    }

    public void handleAntMerchantExpandShopCreate(AntShopTask antShopTask) {
        //判断商户是否经过其他活动创建了店铺
        final String merchantSn = antShopTask.getMerchant_sn();
        final String merchantId = antShopTask.getMerchant_id();
        final String aliMchId = antShopTask.getAli_mch_id();
        if (StringUtils.isEmpty(merchantSn) || StringUtils.isEmpty(merchantId) || StringUtils.isEmpty(aliMchId)) {
            return;
        }
        AliCommResponse<AntMerchantExpandShopQueryRequest, AntMerchantExpandShopQueryResponse> antShop = blueSeaBiz.existAntShop(merchantId, antShopTask.getStore_sn(), aliMchId);
        //已经存在蚂蚁店铺则将店铺Id存入并更新状态
        if (Objects.nonNull(antShop)) {
            //2修改antShopTask表状态
            final AntShopTask build = AntShopTask.builder()
                    .id(antShopTask.getId())
                    .ant_shop_id(antShop.getResp().getShopId())
                    .status(AntShopTaskConstant.TaskStatus.SUCCESS)
                    .description("已经存在并写入")
                    .request_body(JSONObject.toJSONString(antShop.getReq()))
                    .response_body(JSONObject.toJSONString(antShop.getResp())).build();
            updateAntShopTask(build);
            return;
        }
        //店铺不存在则创建店铺
        //创建门店申请
        AntMerchantExpandShopCreateReq req = AntMerchantExpandShopCreateReq.builder().build();
        //封装相同信息
        req = blueSeaBiz.buildShopCreateReq(req, merchantSn, antShopTask.getStore_sn());
        //设置特殊信息
        final Map merchant = merchantService.getMerchantBySn(merchantSn);
        //门店类目
        final Map<String, String> categoryMap = applicationApolloConfig.getAntShopIndustryMcc();
        req.setShopCategory(BeanUtil.getPropString(categoryMap, BeanUtil.getPropString(merchant, Merchant.INDUSTRY)));
        //商户角色id
        req.setIpRoleId(aliMchId);
        //调用接口
        doCreateAntShop(antShopTask, req, true);
    }

    private void doCreateAntShop(AntShopTask antShopTask, AntMerchantExpandShopCreateReq req, boolean retry) {
        final AliCommResponse<AntMerchantExpandShopCreateRequest, AntMerchantExpandShopCreateResponse> expandShopCreate = newBlueSeaService.antMerchantExpandShopCreate(req);
        final AntShopTask build = AntShopTask.builder().id(antShopTask.getId()).build();
        if (expandShopCreate.isSuccess()) {//业务处理成功后将status变成 2申请创建支付宝门店
            build.setStatus(AntShopTaskConstant.TaskStatus.SHOP_CREATED_APPLY)
                    .setAnt_shop_order_id(expandShopCreate.getResp().getOrderId())
                    .setRequest_body(JSONObject.toJSONString(expandShopCreate.getReq()))
                    .setResponse_body(JSONObject.toJSONString(expandShopCreate.getResp()));
            updateAntShopTask(build);
        } else if (expandShopCreate.isSystemFail()) {//系统异常需要重试
            updateReTry(antShopTask, JSONObject.toJSONString(expandShopCreate.getReq()), JSONObject.toJSONString(expandShopCreate.getResp()));
        } else {//业务异常直接失败并记下失败原因
            // 如果是  SHOP_ALREADY_EXIST  重试一次
            if (retry && "SHOP_ALREADY_EXIST".equals(expandShopCreate.getResp().getSubCode())) {
                req.setShopName(req.getShopName() + new Random().nextInt(10));
                doCreateAntShop(antShopTask, req, false);
            } else {
                build.setStatus(AntShopTaskConstant.TaskStatus.FAIL)
                        .setRequest_body(JSONObject.toJSONString(expandShopCreate.getReq()))
                        .setResponse_body(JSONObject.toJSONString(expandShopCreate.getResp()))
                        .setDescription(expandShopCreate.getResp().getSubMsg());
                updateAntShopTask(build);
            }
        }
    }

    /**
     * 主动查询店铺报名结果
     *
     * @param
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 15:58 2020/12/15
     */
    public void handleAntMerchantExpandShopQuery(AntShopTask antShopTask) {
        String storeSn = antShopTask.getStore_sn();
        if (WosaiStringUtils.isEmpty(storeSn)) {
            storeSn = BeanUtil.getPropString(blueSeaBiz.getStoreList(antShopTask.getMerchant_id()).get(0), Store.SN);
        }

        //如果不存在店铺Id申请单Id或者已经存在店铺Id就直接结束这个任务
        final String antShopOrderId = antShopTask.getAnt_shop_order_id();
        final String merchantSn = antShopTask.getMerchant_sn();
        if (Objects.isNull(antShopOrderId) || Objects.nonNull(antShopTask.getAnt_shop_id())) {
            return;
        }
        AntMerchantExpandOrderQueryReq queryReq = AntMerchantExpandOrderQueryReq.builder().orderId(antShopOrderId).build();
        final AliCommResponse<AntMerchantExpandOrderQueryRequest, AntMerchantExpandOrderQueryResponse> orderQuery = newBlueSeaService.antMerchantExpandOrderQuery(queryReq);
        final AntShopTask build = AntShopTask.builder().id(antShopTask.getId()).build();
        if (orderQuery.isSystemFail()) {//系统失败重试
            updateReTry(antShopTask, JSONObject.toJSONString(orderQuery.getReq()), JSONObject.toJSONString(orderQuery.getResp()));
        } else if (orderQuery.isBusinessFail()) {//业务失败
            build.setStatus(AntShopTaskConstant.TaskStatus.FAIL)
                    .setRequest_body(JSONObject.toJSONString(orderQuery.getReq()))
                    .setResponse_body(JSONObject.toJSONString(orderQuery.getResp()))
                    .setDescription(orderQuery.getResp().getSubMsg());
            updateAntShopTask(build);
        } else {//业务处理成功
            //申请单状态。99:已完结；-1:失败；031:已提交审核。
            final String status = orderQuery.getResp().getStatus();
            if (Objects.equals(AntMerchantExpandOrderStatusEnum.AUDITING.getValue(), status)) {
                return;
            }
            if (Objects.equals(AntMerchantExpandOrderStatusEnum.FAIL.getValue(), status)) {//失败
                //判断是不是重复提交的申请单
                final List<AntShopTask> shopTask = antShopTaskMapper.selectByStoreSn(merchantSn, storeSn, antShopTask.getAli_mch_id());
                final List<AntShopTask> tasks = Optional.ofNullable(shopTask).orElseGet(ArrayList::new).stream()
                        .filter(task -> Objects.equals(task.getStatus(), AntShopTaskConstant.TaskStatus.SUCCESS))
                        .distinct()
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tasks)) {
                    build.setStatus(AntShopTaskConstant.TaskStatus.SUCCESS)
                            .setAnt_shop_id(tasks.get(0).getAnt_shop_id());
                    updateAntShopTask(build);
                    return;
                }
                build.setStatus(AntShopTaskConstant.TaskStatus.FAIL)
                        .setRequest_body(JSONObject.toJSONString(orderQuery.getReq()))
                        .setResponse_body(JSONObject.toJSONString(orderQuery.getResp()))
                        .setDescription("创建门店申请单状态失败");
                updateAntShopTask(build);
                return;
            }
            if (Objects.equals(AntMerchantExpandOrderStatusEnum.FINISHED.getValue(), status)) {//已完结
                //审核完结
                final String ipRoleId = antShopTask.getAli_mch_id();
                AntMerchantExpandShopQueryReq shopQueryReq = AntMerchantExpandShopQueryReq.builder().storeId(storeSn).ipRoleId(ipRoleId).build();
                final AliCommResponse<AntMerchantExpandShopQueryRequest, AntMerchantExpandShopQueryResponse> shopQuery = newBlueSeaService.antMerchantExpandShopQuery(shopQueryReq);
                if (shopQuery.isSystemFail()) {//系统失败重试
                    updateReTry(antShopTask, JSONObject.toJSONString(shopQuery.getReq()), JSONObject.toJSONString(shopQuery.getResp()));
                } else if (shopQuery.isBusinessFail()) {
                    build.setStatus(AntShopTaskConstant.TaskStatus.FAIL)
                            .setRequest_body(JSONObject.toJSONString(shopQuery.getReq()))
                            .setResponse_body(JSONObject.toJSONString(shopQuery.getResp()))
                            .setDescription(shopQuery.getResp().getSubMsg());
                    updateAntShopTask(build);
                } else {
                    final String shopId = shopQuery.getResp().getShopId();
                    //设置店铺申请单对应的任务状态为8-支付宝门店创建成功
                    build.setStatus(AntShopTaskConstant.TaskStatus.SUCCESS)
                            .setRequest_body(JSONObject.toJSONString(shopQuery.getReq()))
                            .setResponse_body(JSONObject.toJSONString(shopQuery.getResp()))
                            .setDescription("成功")
                            .setAnt_shop_id(shopId);
                    try {
                        composeAcquirerBiz.syncMchInfo2PayWay(merchantSn, PaywayEnum.ALIPAY.getValue());
                    } catch (Exception e) {
                        log.warn(String.format("业务:%s,创建蚂蚁门店成功后同步MCC异常,商户号:%s,异常信息: %s", getBusiness(antShopTask.getBusiness_type()), merchantSn, e.getMessage()));
                    }
                    updateAntShopTask(build);
                }
            }
        }
    }


    public void updateAntShopTask(AntShopTask antShopTask) {
        antShopTaskMapper.updateByPrimaryKeySelective(antShopTask);
    }


    /**
     * @param antShopTask
     * @param request
     * @param response
     * @return
     * @Author: zhmh
     * @Description: 重试
     * @time: 11:14 2021/2/25
     */
    public void updateReTry(AntShopTask antShopTask, String request, String response) {
        final AntShopTask build = AntShopTask.builder()
                .id(antShopTask.getId())
                .retry(antShopTask.getRetry() + 1)
                .priority(new Date(System.currentTimeMillis() + antShopTask.getRetry() * ONE_MINUTE))
                .request_body(request)
                .response_body(response)
                .build();
        if (antShopTask.getRetry() > AntShopTaskConstant.MAX_RETRY) {
            build.setStatus(AntShopTaskConstant.TaskStatus.FAIL)
                    .setDescription(String.format("重试次数过多 状态：%s", antShopTask.getStatus()));

        }
        antShopTaskMapper.updateByPrimaryKeySelective(build);
    }

    public String getBusiness(Integer businessType) {
        if (Objects.equals(businessType, AntShopTaskConstant.BusinessType.SCAN_CODE)) {
            return "扫码点单";
        }
        return "其他业务方";
    }

    /**
     * @Author: zhmh
     * @Description: 升级M3
     * @time: 16:19 2021/3/3
     */
    public void updateMerchantToM3(AntShopTask antShopTask) {
        //商户号
        final String merchantSn = antShopTask.getMerchant_sn();
        final CustomizedInfo customizedInfo = blueSeaBiz.buildCustomizedInfo(merchantSn);
        //主键Id
        final AntShopTask build = AntShopTask.builder().id(antShopTask.getId()).build();
        try {
            log.info("antShop updateMerchantToM3 merchantSn:{},customizedInfo:{}", merchantSn, JSONObject.toJSONString(customizedInfo));
            final boolean result = blueSeaService.updateMerchantToM3(merchantSn, customizedInfo);
            if (!result) {
                updateAntShopTask(build.setStatus(AntShopTaskConstant.TaskStatus.FAIL).setDescription("升级M3返回失败"));
                return;
            }
            //记录商户是从M2升级到M3的
            updateAntShopTask(build.setStatus(AntShopTaskConstant.TaskStatus.QUALIFY));
        } catch (Exception e) {
            log.error("antShop updateMerchantToM3 error:{}", e);
            updateAntShopTask(build.setStatus(AntShopTaskConstant.TaskStatus.FAIL).setDescription(e.getMessage()));
        }
    }
}
