package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.data.bean.BeanUtil;
import com.wosai.shouqianba.withdrawservice.service.WithdrawConfigService;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 定时D0规则
 * 检查商户是否配置了定时D0规则
 */
@Component
public class TimingD0Rule extends AbstractKeepAliveCheckRule {

    @Autowired
    private WithdrawConfigService withdrawConfigService;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            // 检查商户是否配置了定时D0规则
            if (isMerchantSupportD0(context.getMerchantId())) {
                return createFailureResult(
                        "商户配置了定时D0规则，不允许执行",
                        "TIMING_D0_RULE_BLOCKED"
                );
            }
            return createSuccessResult("商户未配置定时D0规则，通过检查");

        } catch (Exception e) {
            logger.error("执行定时D0规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    private boolean isMerchantSupportD0(String merchantId) {
        Map withdrawConfig = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchantId);
        if (MapUtils.isEmpty(withdrawConfig)) {
            return false;
        }
        int status = BeanUtil.getPropInt(withdrawConfig, "status");
        return status != 0;
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.TIMING_D0_RULE;
    }
}