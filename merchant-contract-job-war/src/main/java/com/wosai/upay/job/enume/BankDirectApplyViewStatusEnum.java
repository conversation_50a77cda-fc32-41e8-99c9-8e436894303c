package com.wosai.upay.job.enume;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
public enum BankDirectApplyViewStatusEnum implements ITextValueEnum<Integer> {

    /**
     * 以提交，待银行分配
     */
    DISTRIBUTING(10, "已提交，待银行分配"),
    /**
     * 已分配，待商户签约
     */
    SIGNING(20, "已分配，待商户签约"),
    /**
     * 已分配，待银行审核
     */
    DISTRIBUTED_AUDITING(21, "已分配，待银行审核"),
    /**
     * 已签约，待银行审核
     */
    SIGNED_AUDITING(30, "已签约，待银行审核"),
    /**
     * 银行已审核，待商户微信/支付宝实名认证
     */
    AUTHING(40, "银行已审核，待商户微信/支付宝实名认证"),
    /**
     * 商户微信/支付宝实名认证
     */
    AUTHING_ENTERPRISE(41, "银行已审核，待商户微信/支付宝实名认证"),
    /**
     * 商户微信及支付宝实名认证均已成功,待切换通道
     */
    ACQUIRER_CHANGING(50, "商户微信及支付宝实名认证均已成功,待切换通道"),
    /**
     * 通道切换完成
     */
    SUCCESS(60, "通道切换完成")
    ;

    BankDirectApplyViewStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }
    private final int value;
    private final String text;

    @Override
    public Integer getValue() {
        return this.value;
    }

    @Override
    public String getText() {
        return this.text;
    }
}
