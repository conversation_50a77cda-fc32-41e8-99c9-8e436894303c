package com.wosai.upay.job.handlers;

import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.QueryICBCAuthStatusExcel;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.alipay.AlipayAuthStatusQueryRequest;
import com.wosai.upay.merchant.contract.model.alipay.AlipayAuthStatusQueryResponse;
import com.wosai.upay.merchant.contract.model.weixin.AuthV3Param;
import com.wosai.upay.merchant.contract.model.weixin.MchAuthResp;
import com.wosai.upay.merchant.contract.service.AliPayAuthApplyFlowService;
import com.wosai.upay.merchant.contract.service.AuthApplyFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.wosai.upay.job.biz.WechatAuthBiz.AUTHORIZE_STATE_AUTHORIZED;

/**
 * <AUTHOR>
 * @date 2022-07-04
 */
@Component("QueryICBCAuthStatusHandler")
public class QueryICBCAuthStatusHandler extends BatchProcessingHandler<QueryICBCAuthStatusExcel,QueryICBCAuthStatusExcel>{


    private AuthV3Param authV3ParamICBC;

    @Autowired
    private AuthApplyFlowService authApplyFlowService;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private AliPayAuthApplyFlowService aliPayAuthApplyFlowService;

    private final String ALIPAY_AUTHORIZE_STATE_AUTHORIZED = "AUTHORIZED";

    private final String ALIPAY_AUTHORIZE_STATE_UNAUTHORIZED = "UNAUTHORIZED";

    private final String ICBC_CHANNEL = "icbc-1030-3";



    @Override
    public void doPreProcess(BatchContext batchContext) {
        ContractChannel mcChannel = ruleContext.getContractChannel(ICBC_CHANNEL);
        authV3ParamICBC = mcChannel.buildAuthV3Param();
    }

    @Override
    public QueryICBCAuthStatusExcel handle(QueryICBCAuthStatusExcel queryAuthStatusExcel, BatchContext batchContext) {
        final String wxSubMchId = queryAuthStatusExcel.getWxSubMchId();
        try {
            MchAuthResp authResp = authApplyFlowService.queryAuthStatus(wxSubMchId, authV3ParamICBC);
            Boolean authed = AUTHORIZE_STATE_AUTHORIZED.equals(authResp.getAuthorize_state()) ? Boolean.TRUE : Boolean.FALSE;
            queryAuthStatusExcel.setWxResult(authed);
        }catch (Exception e){
            queryAuthStatusExcel.setWxErrorMsg(e.getMessage());
        }
        try {
            AlipayAuthStatusQueryRequest request = new AlipayAuthStatusQueryRequest();
            request.setSub_merchant_id(queryAuthStatusExcel.getAlipaySubMchId());
            AliCommResponse<AlipayAuthStatusQueryRequest, AlipayAuthStatusQueryResponse> response = aliPayAuthApplyFlowService.queryAuthStatus(request);
            if(response.getCode() == 200){
                if(ALIPAY_AUTHORIZE_STATE_AUTHORIZED.equals(response.getResp().getCheck_result())){
                    queryAuthStatusExcel.setAlipayResult(true);
                }else if(ALIPAY_AUTHORIZE_STATE_UNAUTHORIZED.equals(response.getResp().getCheck_result())){
                    queryAuthStatusExcel.setAlipayResult(false);
                }else {
                    queryAuthStatusExcel.setAlipayErrorMsg(response.getResp().getCheck_result());
                }
            }else{
                queryAuthStatusExcel.setAlipayErrorMsg(response.getMessage());
            }
        }catch (Exception e){
            queryAuthStatusExcel.setAlipayErrorMsg(e.getMessage());
        }
        return queryAuthStatusExcel;
    }

    @Override
    public void doAfterProcess(BatchContext batchContext) {

    }

    @Override
    public QueryICBCAuthStatusExcel handleError(QueryICBCAuthStatusExcel queryAuthStatusExcel, BatchContext batchContext, Exception e) {
        queryAuthStatusExcel.setWxErrorMsg(e.getMessage());
        queryAuthStatusExcel.setAlipayErrorMsg(e.getMessage());
        return queryAuthStatusExcel;
    }

}
