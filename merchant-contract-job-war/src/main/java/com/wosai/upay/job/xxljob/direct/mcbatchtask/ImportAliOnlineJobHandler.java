package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.job.Constants.ApproveConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.OnlinePaymentBiz;
import com.wosai.upay.job.model.BatchImportAliOnlineMerchantsExcel;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * xxl_job_desc: BatchTask-支付宝跨城收款导入
 * <AUTHOR>
 * @date 2025/4/17
 */
@Slf4j
@Component("ImportAliOnlineJobHandler")
public class ImportAliOnlineJobHandler extends AbstractMcBatchTaskJobHandler {

    @Autowired
    private OnlinePaymentBiz onlinePaymentBiz;

    @Override
    public String getLockKey() {
        return "ImportAliOnlineJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        McBatchTaskExample mcBatchTaskExample = new McBatchTaskExample();
        mcBatchTaskExample.or().andStatusEqualTo(0).andEffect_timeLessThanOrEqualTo(new Date()).andTypeEqualTo(11);
        List<McBatchTask> list = mcBatchTaskMapper.selectByExampleWithBLOBs(mcBatchTaskExample);
        list.forEach(mcBatchTask -> {
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                final String payload = mcBatchTask.getPayload();
                Map extra = CommonUtil.string2Map(payload);
                String fileUrl = WosaiMapUtils.getString(extra, "fileUrl");
                final List<BatchImportAliOnlineMerchantsExcel> batchImportAliOnlineMerchantsExcel = excelUtil.getExcelInfoList(fileUrl, new BatchImportAliOnlineMerchantsExcel());
                //异步线程多次调用

                final List<CompletableFuture<BatchImportAliOnlineMerchantsExcel>> futureList = batchImportAliOnlineMerchantsExcel.parallelStream().map(excel -> CompletableFuture.supplyAsync(() -> {
                    try {
                        onlinePaymentBiz.importAliAuditResult(excel);
                        excel.setMessage("成功");
                    } catch (Exception e) {
                        excel.setMessage(e.getMessage());
                    }
                    return excel;
                }, batchScheduleExecutorThreadPoolTaskExecutor)).collect(Collectors.toList());
                //获取结果
                final List<BatchImportAliOnlineMerchantsExcel> importResult = futureList.stream().map(CompletableFuture::join).collect(Collectors.toList());
                //将authStatusResult集合变成文件上传到oss中
                final String url = excelUtil.uploadToOss(importResult, BASE_DIR);
                //修改状态并将结果存储
                extra.put(ApproveConstant.LAST_ATTACHMENT_URL, url);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(2).setId(mcBatchTask.getId()).setPayload(JSONObject.toJSONString(extra)).setResult("处理成功"));
                logService.updateTaskApplyLog(CollectionUtil.hashMap(DaoConstants.ID, mcBatchTask.getTask_apply_log_id(), TaskApplyLog.APPLY_STATUS, APPLY_STATUS_EXCUTE_SUCCESS, TaskApplyLog.APPLY_RESULT, JSON.toJSONString(CollectionUtil.hashMap("result", "处理结果请使用浏览器下载链接对应的Excel:" + url))));
            } catch (Exception e) {
                log.error("batchImportAliOnlineMerchants error mc_batch_task {}  exception", mcBatchTask.getId(), e);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(3).setId(mcBatchTask.getId()).setResult(ExceptionUtil.getThrowableMsg(e)));
                logService.updateTaskApplyLog(CollectionUtil.hashMap(DaoConstants.ID, mcBatchTask.getTask_apply_log_id(), TaskApplyLog.APPLY_STATUS, APPLY_STATUS_EXCUTE_FAILURE, TaskApplyLog.APPLY_RESULT, JSON.toJSONString(CollectionUtil.hashMap("result", ExceptionUtil.getThrowableMsg(e)))));
            }
        });
    }
}
