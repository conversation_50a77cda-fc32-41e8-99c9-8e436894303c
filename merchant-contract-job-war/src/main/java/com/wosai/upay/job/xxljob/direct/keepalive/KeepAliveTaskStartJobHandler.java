package com.wosai.upay.job.xxljob.direct.keepalive;

import com.alibaba.fastjson.JSON;
import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.keepalive.KeepAliveMessageBiz;
import com.wosai.upay.job.biz.keepalive.KeepAliveParamsBuilder;
import com.wosai.upay.job.biz.keepalive.KeepAliveTaskBiz;
import com.wosai.upay.job.biz.keepalive.validation.engine.KeepAliveValidateEngine;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveValidationScenarioEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidationResult;
import com.wosai.upay.job.externalservice.aop.AopClient;
import com.wosai.upay.job.refactor.dao.ProviderParamsKeepaliveTaskDAO;
import com.wosai.upay.job.refactor.model.bo.KeepAliveTaskResultBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.ProviderParamsKeepaliveTaskDO;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/23
 */
@Slf4j
@Component("KeepAliveTaskStartJobHandler")
public class KeepAliveTaskStartJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private KeepAliveTaskBiz keepAliveTaskBiz;
    @Autowired
    private ProviderParamsKeepaliveTaskDAO taskDAO;
    @Autowired
    private KeepAliveMessageBiz keepAliveMessageBiz;
    @Autowired
    private KeepAliveParamsBuilder paramsBuilder;
    @Autowired
    private AopClient aopClient;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private KeepAliveValidateEngine ruleEngine;


    @Override
    public String getLockKey() {
        return "KeepAliveTaskStartJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        LocalDate startDay = LocalDate.now().plusDays(1);
        log.info("开始执行保活任务，批次大小: {}，日期：{}", param.getBatchSize(), startDay);
        // 查询待保活&& 状态是明天的任务
        List<ProviderParamsKeepaliveTaskDO> providerParamsKeepaliveTaskDOS = taskDAO.selectPendingTasksByStartDate(startDay, param.getBatchSize());
        log.info("查询到 {} 个待处理的保活任务", providerParamsKeepaliveTaskDOS.size());

        for (ProviderParamsKeepaliveTaskDO providerParamsKeepaliveTaskDO : providerParamsKeepaliveTaskDOS) {
            try {
                log.info("开始处理保活任务，任务ID: {}, 商户号: {}",
                        providerParamsKeepaliveTaskDO.getTaskId(), providerParamsKeepaliveTaskDO.getMerchantSn());

                KeepAliveTaskStartContext context = new KeepAliveTaskStartContext();
                context.setTaskDO(providerParamsKeepaliveTaskDO);
                doStart(context);

                log.info("保活任务处理完成，任务ID: {}", providerParamsKeepaliveTaskDO.getTaskId());
            } catch (Exception e) {
                log.error("处理保活任务异常，任务ID: {}", providerParamsKeepaliveTaskDO.getTaskId(), e);
            }
        }

        log.info("保活任务执行完成");
    }

    @Timed("商户保活-开启任务")
    private void doStart(KeepAliveTaskStartContext context) {
        try {
            // 校验
            Tuple2<Boolean, String> checkResult = check(context);
            if (!checkResult.get_1()) {
                // 更新状态为失败 & 更新任务
                keepAliveTaskBiz.reCreateKeepAliveTasksWhenCancel(context.getTaskDO(), KeepAliveTaskResultBO.createTaskCancelledResultFromSchedule("KeepAliveTaskStartJobHandler", "校验失败：" + checkResult.get_2()));
                return;
            }
            // 组装交易参数
            buildKeepAliveParams(context);
            // 更新任务状态
            updateTaskStatus(context);
            // 发送kafka消息
            sendKafkaMsg(context);
            // 发送aop通知
            sendAopMsg(context);
        } catch (Exception e) {
            log.error("执行保活任务失败，任务ID: {}", context.getTaskDO().getTaskId(), e);
            // 更新任务状态为失败
            KeepAliveTaskResultBO resultBO = KeepAliveTaskResultBO.createScheduleSystemErrorResult("KeepAliveTaskStartJobHandler", e.getMessage());
            keepAliveTaskBiz.reCreateKeepAliveTasksWhenFail(context.getTaskDO(), resultBO);
        }
    }

    private Tuple2<Boolean, String> check(KeepAliveTaskStartContext context) {
        String merchantSn = context.getTaskDO().getMerchantSn();
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        KeepAliveValidateContext keepAliveValidateContext = KeepAliveValidateContext.createForPreCircleOrExecute(merchant, KeepAliveValidationScenarioEnum.PRE_EXECUTE_CIRCLE, context.getTaskDO());
        KeepAliveValidationResult validate = ruleEngine.validate(keepAliveValidateContext, KeepAliveValidationScenarioEnum.PRE_EXECUTE_CIRCLE);
        if (!validate.isSuccess()) {
            return new Tuple2<>(false, validate.getFailureReason());
        }
        return new Tuple2<>(true, null);
    }

    private void sendKafkaMsg(KeepAliveTaskStartContext context) {
        keepAliveMessageBiz.sendKeepAliveMessage(context.getTaskDO());
    }

    private void sendAopMsg(KeepAliveTaskStartContext context) {
        aopClient.sendStartKeepAliveNotice(context.getTaskDO().getMerchantSn());
    }

    private void buildKeepAliveParams(KeepAliveTaskStartContext context) {
        MerchantProviderParamsDO providerParamsDO = context.getProviderParamsDO();
        KeepAliveParams keepAliveParams = paramsBuilder.buildKeepAliveParams(providerParamsDO);
        context.setKeepAliveParams(keepAliveParams);
    }

    private void updateTaskStatus(KeepAliveTaskStartContext context) {
        ProviderParamsKeepaliveTaskDO taskDO = context.getTaskDO();
        KeepAliveParams keepAliveParams = context.getKeepAliveParams();
        taskDAO.startKeepAliveTask(taskDO, JSON.toJSONString(keepAliveParams), KeepAliveTaskResultBO.createTaskExecutingResultFromSchedule("KeepAliveTaskStartJobHandler"));
    }


    @Data
    public static class KeepAliveParams {
        private String merchant_sn;
        private Integer provider;
        private Integer payway;
        private String b2c_agent_name;
        private String c2b_agent_name;
        private String wap_agent_name;
        private String mini_agent_name;
        private Map<String, Object> params;
        private String fee_rate;
        private Map<String, String> fee_rate_tag;
        private List<Map<String, Object>> ladder_fee_rates;
        private Map<String, Map<String, Object>> bankcard_fee;
        private List<Map<String, Object>> channel_fee_rates;
        private Map<String, List<Map<String, Object>>> channel_ladder_fee_rates;
    }

    @Data
    public static class KeepAliveTaskStartContext {
        private ProviderParamsKeepaliveTaskDO taskDO;
        private MerchantProviderParamsDO providerParamsDO;
        private KeepAliveParams keepAliveParams;
    }
}
