package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import com.wosai.upay.job.externalservice.customer.CustomerRelationClient;
import entity.common.OrganizationEs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 商户支付业务维护组织规则
 */
@Component
public class MerchantPaymentOrgRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private CustomerRelationClient customerRelationClient;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            OrganizationEs mchIndirectOrg = customerRelationClient.getMchIndirectOrg(context.getMerchantId());
            if (Objects.isNull(mchIndirectOrg) || WosaiStringUtils.isEmpty(mchIndirectOrg.getPath())) {
                return createFailureResult(
                        "商户组织信息不存在，不允许执行", "MERCHANT_PAYMENT_ORG_BLOCKED");
            }
            List<String> pathList = (List<String>) WosaiMapUtils.getObject(ruleConfig.getParams(), "pathList");
            boolean match = pathList.stream().anyMatch(r -> mchIndirectOrg.getPath().startsWith(r));
            if (!match) {
                return createFailureResult(
                        String.format("商户组织信息 %s 不在配置中，不允许执行", mchIndirectOrg.getPath()), "MERCHANT_PAYMENT_ORG_BLOCKED");
            }
            logger.debug("商户 {} 组织 {} 在配置中，通过检查", context.getMerchantSn(), mchIndirectOrg.getPath());
            return createSuccessResult(String.format("商户组织 %s 在配置中，通过检查", mchIndirectOrg.getPath()));

        } catch (Exception e) {
            logger.error("执行商户支付业务维护组织规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.MERCHANT_PAYMENT_ORG;
    }
}