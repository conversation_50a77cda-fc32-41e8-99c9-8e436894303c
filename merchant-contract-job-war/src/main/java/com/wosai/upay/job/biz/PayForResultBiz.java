package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.CallBackConstants;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.PayForTaskMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.PayForTask;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.service.TaskResultService;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description:代付成功失败的结果处理
 * <AUTHOR>
 * Date 2019/12/24 6:42 下午
 **/
@Component
@Slf4j
public class PayForResultBiz {

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private PayForTaskMapper payForTaskMapper;
    @Autowired
    private TaskResultService taskResultService;
    @Autowired
    private DataBusBiz dataBusBiz;

    @Autowired
    private CompleteDateBiz completeDateBiz;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    public void successHandle(ContractSubTask contractSubTask, Long payForId) {
        PayForTask payForTask = new PayForTask().setId(payForId).setStatus(PayForTask.SUCCESS_STATUS);
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id());
        Map context = JSON.parseObject(contractTask.getEvent_context(), Map.class);

        if (SubTaskStatus.REPAYFOR.getVal().equals(contractSubTask.getStatus())) {
            // 重新执行一次任务
            ContractSubTask update = new ContractSubTask()
                    .setId(contractSubTask.getId())
                    .setStatus(SubTaskStatus.PENDING.getVal())
                    .setPriority(new Date())
                    .setContract_id("")
                    ;
            contractSubTaskMapper.updateByPrimaryKey(update);

            contractTaskBiz.update(
                            new ContractTask()
                                    .setId(contractTask.getId())
                                    .setStatus(TaskStatus.PENDING.getVal())
                                    .setResult(CallBackConstants.RE_CONTRACT));
        } else {
            //对公审核通过时间 文案处理
            Map merchantBankAccount = (Map) context.get("bankAccount");
            int type = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.TYPE);
            String msg = "代付验证通过";
            if (type == BankAccountTypeEnum.PUBLIC.getValue()) {
                msg = "对公代付通过";
            }
            Map result = CollectionUtil.hashMap("channel", ProviderUtil.PAY_FOR_CHANNEL, "message", msg);
            // 这个子任务影响主任务再去修改，否则会造成主任务已经成功了，但是这里改成进行中了
            if (contractSubTask.getStatus_influ_p_task() == 1) {
                contractTask.setResult(JSON.toJSONString(result)).setStatus(TaskStatus.PROGRESSING.getVal()).setComplete_at(completeDateBiz.getLklDate(contractTask.getPriority()));
                contractTaskBiz.update(contractTask);
            }
            contractSubTask.setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue()).setResponse_body(JSON.toJSONString(result)).setResult(JSON.toJSONString(result));
            contractSubTaskMapper.updateByPrimaryKey(contractSubTask);
        }
        payForTaskMapper.updateByPrimaryKeySelective(payForTask);
        Map merchant = (Map) context.get(ParamContextBiz.KEY_MERCHANT);
        if (ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(contractSubTask.getTask_type())) {
            dataBusBiz.insertPayForEvent(BeanUtil.getPropString(merchant, Merchant.SN), BeanUtil.getPropString(merchant, DaoConstants.ID), ContractStatus.STATUS_PAY_FOR_SUCCESS);
        }
    }


    public void failHandler(ContractSubTask contractSubTask, Long payForId, Integer status) {
        PayForTask payForTask = new PayForTask().setId(payForId).setStatus(PayForTask.FAIL_STATUS);
        Map resp = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
        if (CollectionUtils.isEmpty(resp)) {
            resp = Maps.newHashMap();
        }
        resp.put(ProviderUtil.PAY_FOR_CHANNEL, "代付校验不通过");
        ContractSubTask update = new ContractSubTask().setId(contractSubTask.getId());
        String respString = JSON.toJSONString(resp);
        update.setResult(respString).setResponse_body(respString);
        if (SubTaskStatus.REPAYFOR.getVal().equals(contractSubTask.getStatus())) {
            update.setStatus(SubTaskStatus.FAIL.getVal());
        }
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id());
        //文案提示处理--》
        Map result = ScheduleUtil.getPayForTip(contractTask, status);
        contractTask.setStatus(TaskStatus.FAIL.getVal()).setResult(JSON.toJSONString(result));

        contractSubTaskMapper.updateByPrimaryKey(update);
//        contractTaskMapper.updateByPrimaryKey(contractTask);
        // TODO 更新contract_task表status,并根据type和status判断是否发送消息到神策
        contractTaskBiz.update(contractTask);
        payForTaskMapper.updateByPrimaryKeySelective(payForTask);
        taskResultService.changeStatusAndResultV2(contractTask.getId(), contractSubTask.getId(), TaskStatus.FAIL.getVal(), JSON.toJSONString(result), false);
    }

    /**
     * 需要金额验证时
     **/
    public void needVeriyHandle(ContractSubTask contractSubTask, Long payForId) {
        PayForTask payForTask = new PayForTask().setId(payForId).setStatus(PayForTask.VERIFY_STATUS);
        payForTaskMapper.updateByPrimaryKeySelective(payForTask);
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id());
        //文案提示处理--》
        Map result = JSON.parseObject(contractTask.getResult());
        if (result == null) {
            result = new HashMap();
        }
        result.put("message", ScheduleUtil.TIPS_PAY_FOR_VERIFY_PUBLIC);
        ContractTask update = new ContractTask().setId(contractTask.getId()).setResult(JSON.toJSONString(result));
//        contractTaskMapper.updateByPrimaryKey(update);
        // TODO 更新contract_task表status,并根据type和status判断是否发送消息到神策
        contractTaskBiz.update(update);
        Map context = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        Map merchant = (Map) context.get(ParamContextBiz.KEY_MERCHANT);
        if (ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(contractSubTask.getTask_type())) {
            dataBusBiz.insertPayForEvent(payForTask.getMerchant_sn(), BeanUtil.getPropString(merchant, DaoConstants.ID), ContractStatus.STATUS_WAIT_VERIFY);
        }
    }
}
