package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.ApproveConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.changeAcquirerApprove.AcquirerApprove;
import com.wosai.upay.job.model.changeAcquirerApprove.AcquirerApproveExcel;
import com.wosai.upay.job.service.ContractTaskResultServiceImpl;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * xxl_job_desc: BatchTask-仅入网任务结果
 * 单个仅入网
 *
 * <AUTHOR>
 * @date 2025/4/17
 */
@Slf4j
@Component("OnlyContractResultJobHandler")
public class OnlyContractResultJobHandler extends AbstractMcBatchTaskJobHandler {

    @Autowired
    private ContractTaskResultServiceImpl contractTaskResultService;

    /**
     * 设置缓存,写入3小时后自动删除
     */
    Cache<String, List<AcquirerApprove>> onlyContractCache = CacheBuilder.newBuilder()
            .maximumSize(16384)
            .expireAfterWrite(3, TimeUnit.HOURS)
            .build();

    @Override
    public String getLockKey() {
        return "OnlyContractResultJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        McBatchTaskExample mcBatchTaskExample = new McBatchTaskExample();
        mcBatchTaskExample.or().andStatusEqualTo(1).
                andEffect_timeLessThanOrEqualTo(new Date())
                .andTypeIn(Lists.newArrayList(12, 13));
        List<McBatchTask> list = mcBatchTaskMapper.selectByExampleWithBLOBs(mcBatchTaskExample);
        list.forEach(mcBatchTask -> {
            Map extra = Maps.newHashMap();
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                final String payload = mcBatchTask.getPayload();
                extra = CommonUtil.string2Map(payload);
                final Integer type = mcBatchTask.getType();
                //批量操作
                if (Objects.equals(type, 13)) {
                    //批量文件
                    final String lastAttachmentUrl = BeanUtil.getPropString(extra, ApproveConstant.LAST_ATTACHMENT_URL);
                    final List<AcquirerApprove> approveList = onlyContractCache.get(lastAttachmentUrl, () -> excelUtil.getExcelInfoList(lastAttachmentUrl, new AcquirerApprove()));
                    if (CollectionUtils.isEmpty(approveList)) {
                        return;
                    }
                    final List<AcquirerApproveExcel> acquirerApproveExcels = approveList.parallelStream()
                            .map(this::getContractEventResult)
                            .collect(Collectors.toList());
                    //判断是否全部处理完成
                    final boolean match = acquirerApproveExcels.stream().anyMatch(x -> StringUtils.isEmpty(x.getResult()));
                    //没有处理完成等到下次处理
                    if (match) {
                        return;
                    }
                    //处理完的上传并返回
                    final String url = excelUtil.uploadToOss(acquirerApproveExcels, BASE_DIR);
                    extra.put(ApproveConstant.LAST_ATTACHMENT_URL, url);
                    mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setId(mcBatchTask.getId())
                            .setPayload(JSONObject.toJSONString(extra))
                            .setStatus(2)
                            .setResult("处理结束"));
                    //删除临时批量文件
                    excelUtil.deleteTempExcel(lastAttachmentUrl, BASE_DIR);
                    callBack(extra, "处理结果请使用浏览器下载链接对应的Excel:" + url, AUDIT_EXECUTE_SUCCESS);
                } else {
                    //处理单个商户
                    final Long eventId = MapUtils.getLong(extra, ApproveConstant.EVENT_ID);
                    if (Objects.isNull(eventId)) {
                        callBack(extra, "商户仅入网失败", AUDIT_EXECUTE_FAIL);
                        mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(2).setId(mcBatchTask.getId()).setResult("处理结束"));
                    } else {
                        ContractEvent contractEvent = contractEventMapper.selectByPrimaryKey(eventId);
                        final Long taskId = contractEvent.getTask_id();
                        if (Objects.isNull(taskId)) {
                            callBack(extra, "有其他入网任务正在审核中,请耐心等待", AUDIT_EXECUTE_FAIL);
                            return;
                        }
                        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
                        //任务还在处理中,不需要处理
                        if (!Lists.newArrayList(TaskStatus.SUCCESS.getVal(), TaskStatus.FAIL.getVal()).contains(contractTask.getStatus())) {
                            return;
                        }
                        if (contractTask.getStatus().equals(TaskStatus.SUCCESS.getVal())) {
                            callBack(extra, "入网成功", AUDIT_EXECUTE_SUCCESS);
                        } else if (contractTask.getStatus().equals(TaskStatus.FAIL.getVal())) {
                            final String escapedContent = contractTaskResultService.getEscapedContent(contractTask);
                            callBack(extra, StringUtils.isEmpty(escapedContent) ? "入网失败" : escapedContent, AUDIT_EXECUTE_FAIL);
                        }
                        mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(2).setId(mcBatchTask.getId()).setResult("处理结束"));
                    }
                }
            } catch (Exception e) {
                log.error("onlyContractResult error mc_batch_task {}  exception", mcBatchTask.getId(), e);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(3).setId(mcBatchTask.getId()).setResult(ExceptionUtil.getThrowableMsg(e)));
                callBack(extra, ExceptionUtil.getThrowableMsg(e), AUDIT_EXECUTE_FAIL);
            }
        });
    }

    /**
     * @param acquirerApprove
     * @return acquirerApproveExcel
     * <AUTHOR>
     * @Description: 将AcquirCrApprove实例变成AcquirerApproveExcel实例
     * @time 11:41
     */
    AcquirerApproveExcel getContractEventResult(AcquirerApprove acquirerApprove) {
        final AcquirerApproveExcel acquirerApproveExcel = new AcquirerApproveExcel();
        BeanUtils.copyProperties(acquirerApprove, acquirerApproveExcel);
        final String eventId = acquirerApprove.getApplyId();
        //已经处理的不需要处理
        if (Objects.nonNull(eventId) && !StringUtils.isEmpty(acquirerApproveExcel.getResult())) {
            return acquirerApproveExcel;
        }
        //校验失败的不处理
        if (Objects.isNull(eventId) && WosaiStringUtils.isEmpty(acquirerApproveExcel.getResult())) {
            acquirerApproveExcel.setResult("商户仅入网失败");
            return acquirerApproveExcel;
        }

        //校验失败的不处理
        if (Objects.isNull(eventId) && !WosaiStringUtils.isEmpty(acquirerApproveExcel.getResult())) {
            return acquirerApproveExcel;
        }
        ContractEvent contractEvent = contractEventMapper.selectByPrimaryKey(Long.valueOf(eventId));

        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractEvent.getTask_id());
        if (contractTask.getStatus().equals(TaskStatus.SUCCESS.getVal())) {
            acquirerApproveExcel.setResult("入网成功");
            return acquirerApproveExcel;
        } else if (contractTask.getStatus().equals(TaskStatus.FAIL.getVal())) {
            final String escapedContent = contractTaskResultService.getEscapedContent(contractTask);
            acquirerApproveExcel.setResult(StringUtils.isEmpty(escapedContent) ? "入网失败" : escapedContent);
            return acquirerApproveExcel;
        }
        //进行中的直接返回,等待下次处理
        return acquirerApproveExcel;
    }
}
