package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.payLater.ZftMerchantApply;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ZftMerchantApplyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ZftMerchantApply record);

    int insertSelective(ZftMerchantApply record);

    ZftMerchantApply selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZftMerchantApply record);

    int updateByPrimaryKeyWithBLOBs(ZftMerchantApply record);

    int updateByPrimaryKey(ZftMerchantApply record);

    @Select("select * from zft_merchant_apply where merchant_sn=#{merchantSn} and account=#{account} order by update_at desc limit 1")
    ZftMerchantApply selectByCondition(@Param("merchantSn") String merchantSn, @Param("account") String account);

    List<ZftMerchantApply> selectByStatus(List<Integer> status, int limit, String startTime, String endTime);

    @Select("select * from zft_merchant_apply where smid=#{smid} order by update_at desc limit 1")
    ZftMerchantApply selectBySmid(@Param("smid") String smid);
}