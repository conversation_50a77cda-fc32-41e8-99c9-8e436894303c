package com.wosai.upay.job.xxljob.direct;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.service.ProcessTaskChangeAcquirer;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * xxl_job_desc: 将 ums正在进行中的任务切到拉卡拉
 * @Description:
 * <AUTHOR>
 * @Date: 2021/9/14 2:30 下午
 */
@Component(value = "UmsChangeToLklJobHandler")
@Slf4j
public class UmsChangeToLklJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private ProcessTaskChangeAcquirer processTaskChangeAcquirer;


    private static final String KEY = "contract_event_ums_ing_lkl";

    @Override
    public String getLockKey() {
        return KEY;
    }

    @Override
    public void execute(DirectJobParam param) {
        umsChangeToLkl();
    }


    /**
     * 切换 2小时前 入网审核中的
     */
    public void umsChangeToLkl() {
        //apollo开关
        String n = applicationApolloConfig.getCheckUmsLkl();
        if ("N".equals(n)) {
            return;
        }
        try {
            //时间
            Map map = applicationApolloConfig.getUmsToLkl();
            double hour = WosaiMapUtils.getDoubleValue(map, "hour", 2);
            int day = WosaiMapUtils.getIntValue(map, "day", 2);

            long endTime = System.currentTimeMillis() - ((long) (hour * 60 * 60 * 1000));
            String updateEndTime = StringUtil.formatDate(endTime);

            long startTime = endTime - ((long) day * 24 * 60 * 60 * 1000);
            String updateStartTime = StringUtil.formatDate(startTime);

            log.info("查询银商通道入网任务处理时间在{}到{}", updateStartTime, updateEndTime);
            //查询ums正在进行中的任务
            List<ContractStatus> list = contractStatusMapper.selectByUpdateAndStatusAndAcquirer(updateEndTime, updateStartTime, "ums", 1);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            for (ContractStatus contractStatus : list) {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                processTaskChangeAcquirer.change(contractStatus.getMerchant_sn(), "ums", "lklV3");
            }
        } catch (Exception e) {
            log.error("银商进行中切换异常", e);
        }
    }
}
