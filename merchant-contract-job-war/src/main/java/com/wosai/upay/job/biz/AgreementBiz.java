package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.wosai.agreement.model.req.IsSignAgreementTypeReq;
import com.wosai.agreement.model.req.SaveSignRecordReq;
import com.wosai.agreement.model.resp.IsSignAgreementTypeResp;
import com.wosai.agreement.model.resp.LatestAgreementResp;
import com.wosai.agreement.service.AgreementService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.AgreementEnum;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 间连扫码提交进件初始化签约协议至协议管理平台
 * https://jira.wosai-inc.com/browse/CUA-4754
 *
 * <AUTHOR>
 * @date 2022/10/19
 */
@Component
@Slf4j
public class AgreementBiz {

    @Autowired
    private AgreementService agreementService;

    @Autowired
    private ParamContextBiz paramContextBiz;

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    MerchantService merchantService;
    @Autowired
    @Qualifier("agreementThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor agreementThreadPoolTaskExecutor;

    /**
     * 开通间连扫码签署协议
     *
     * @param merchantSn
     */
    public void recordAgreementForContract(String merchantSn) {
        agreementThreadPoolTaskExecutor.submit(() -> {
            try {
                AgreementSnapshot snapshot = buildSnapshot(merchantSn);
                signAgreement(AgreementEnum.LKL_PAYMENT.getValue(), snapshot, null);
                signAgreement(AgreementEnum.TL_PAYMENT.getValue(), snapshot, null);
            } catch (Throwable t) {
                log.warn("recordAgreementForContract error {}", merchantSn, t);
            }
        });
    }

    /**
     * 开通阶段付协议
     *
     * @param merchantSn
     */
    public void recordAgreementForCredit(String merchantSn) {
        agreementThreadPoolTaskExecutor.submit(() -> {
            try {
                MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
                AgreementSnapshot snapshot = new AgreementSnapshot()
                        .setMerchant_sn(merchantSn)
                        .setMerchant_id(merchant.getId());
                signAgreement(AgreementEnum.TYPE_SQB_SESAMECREDIT.getValue(), snapshot, null);
            } catch (Throwable t) {
                log.warn("recordAgreementForCredit error {}", merchantSn, t);
            }
        });
    }

    /**
     * 进件成功后更新协议
     *
     * @param merchantSn
     */
    public void updateAgreement(String merchantSn) {
        agreementThreadPoolTaskExecutor.submit(() -> {
            try {
                recordAgreementSync(merchantSn);
            } catch (Throwable t) {
                log.warn("recordAgreementAfterContract error {}", merchantSn, t);
            }
        });

    }

    public void recordAgreementSync(String merchantSn) {
        Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        AgreementSnapshot snapshot = buildSnapshot(paramContext);
        List<ContractTask> contractTasks = contractTaskMapper.selectContractTasks(merchantSn);
        // 早期商户没有 contractTasks
        if (WosaiCollectionUtils.isEmpty(contractTasks)) {
            Map merchant = WosaiMapUtils.getMap(paramContext, ParamContextBiz.KEY_MERCHANT);
            long mchCtime = BeanUtil.getPropLong(merchant, DaoConstants.CTIME, System.currentTimeMillis());
            signAgreement(AgreementEnum.LKL_PAYMENT.getValue(), snapshot, new Date(mchCtime));
            return;
        }


        // 检查拉卡拉是否报备过
        for (ContractTask task : contractTasks) {
            if (
                    WosaiStringUtils.isEmpty(task.getRule_group_id()) ||
                            task.getRule_group_id().contains(McConstant.RULE_GROUP_LKL) ||
                            task.getRule_group_id().contains("lkl")

            ) {
                signAgreement(AgreementEnum.LKL_PAYMENT.getValue(), snapshot, task.getCreate_at());
            }
        }
        // 检查通联是否报备过
        for (ContractTask task : contractTasks) {
            if (task.getRule_group_id().contains(McConstant.RULE_GROUP_TONGLIAN)) {
                signAgreement(AgreementEnum.TL_PAYMENT.getValue(), snapshot, task.getCreate_at());
            }
        }
    }

    private AgreementSnapshot buildSnapshot(String merchantSn) {
        Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        return buildSnapshot(paramContext);
    }
    private AgreementSnapshot buildSnapshot(Map<String, Object> paramContext) {
        Map merchant = WosaiMapUtils.getMap(paramContext, ParamContextBiz.KEY_MERCHANT);
        Map bankAccount = WosaiMapUtils.getMap(paramContext, ParamContextBiz.KEY_BANK_ACCOUNT);
        Map businessLicence = WosaiMapUtils.getMap(paramContext, ParamContextBiz.KEY_BUSINESS_LICENCE);

        String address = String.format("%s %s %s %s",
                BeanUtil.getPropString(merchant, Merchant.PROVINCE),
                BeanUtil.getPropString(merchant, Merchant.CITY),
                BeanUtil.getPropString(merchant, Merchant.DISTRICT),
                BeanUtil.getPropString(merchant, Merchant.STREET_ADDRESS)
        );
        String contactCellphone = BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE, BeanUtil.getPropString(merchant, Merchant.CUSTOMER_PHONE));
        AgreementSnapshot snapshot = new AgreementSnapshot()
                .setMerchant_id(BeanUtil.getPropString(merchant, DaoConstants.ID))
                .setMerchant_sn(BeanUtil.getPropString(merchant, Merchant.SN))
                .setMerchant_name(BeanUtil.getPropString(merchant, Merchant.NAME))
                .setMerchant_business_name(BeanUtil.getPropString(merchant, Merchant.BUSINESS_NAME))
                .setMerchant_address(address)
                .setContact_cellphone(contactCellphone);
        int licenceType = BeanUtil.getPropInt(businessLicence, MerchantBusinessLicence.TYPE, BusinessLicenseTypeEnum.MICRO.getValue());
        if (licenceType == BusinessLicenseTypeEnum.MICRO.getValue()) {
            snapshot.setMerchant_name(BeanUtil.getPropString(bankAccount, MerchantBankAccount.HOLDER))
                    .setBusiness_licence_number(BeanUtil.getPropString(bankAccount, MerchantBankAccount.IDENTITY))
                    .setLegal_person_name(BeanUtil.getPropString(bankAccount, MerchantBankAccount.HOLDER));
        } else {
            snapshot.setMerchant_name(BeanUtil.getPropString(businessLicence, MerchantBusinessLicence.NAME))
                    .setBusiness_licence_number(BeanUtil.getPropString(businessLicence, MerchantBusinessLicence.NUMBER))
                    .setLegal_person_name(BeanUtil.getPropString(businessLicence, MerchantBusinessLicence.LEGAL_PERSON_NAME));
        }

        return snapshot;
    }


    private void signAgreement(String agreementType, AgreementSnapshot snapshot, Date signDate) {
        if (WosaiStringUtils.isEmpty(agreementType)) {
            return;
        }

        // 查询签署记录
        IsSignAgreementTypeResp resp = agreementService.isSignAgreementTypeSingle(
                new IsSignAgreementTypeReq()
                        .setAgreementType(agreementType)
                        .setSignObjType("merchant")
                        .setSignObjId(snapshot.getMerchant_id())
        );

        LatestAgreementResp waitUpdate = resp.getWaitUpdate();

        if (Objects.isNull(resp.getSigned())) {
            // 没签署过直接签署
            agreementService.saveSignRecordSingle(
                    new SaveSignRecordReq()
                            .setSignObjId(snapshot.getMerchant_id())
                            .setAgreementSn(waitUpdate.getSn())
                            .setSnapshot(JSON.parseObject(JSON.toJSONString(snapshot)))
                            .setCtime(signDate)
            );
        } else {
            AgreementSnapshot oldSnapshot = JSON.parseObject(resp.getSigned().getSnapshot(), AgreementSnapshot.class);

            // 判断关键信息是否变更
            boolean eq = !Objects.isNull(oldSnapshot) &&
                    Objects.equals(snapshot.getMerchant_name(), oldSnapshot.getMerchant_name()) &&
                    Objects.equals(snapshot.getBusiness_licence_number(), oldSnapshot.getBusiness_licence_number());
            if (!eq) {
                agreementService.saveSignRecordSingle(
                        new SaveSignRecordReq()
                                .setSignObjId(snapshot.getMerchant_id())
                                .setAgreementSn(waitUpdate != null ? waitUpdate.getSn() : resp.getSigned().getSn())
                                .setSnapshot(JSON.parseObject(JSON.toJSONString(snapshot)))
                                .setCtime(signDate)
                );
            }
        }

    }


    /**
     * 开通预授权签署协议
     *
     * @param merchantId
     */
    public void recordAgreementForT9(String merchantId,String url) {
        try {
            agreementService.saveSignRecordSingle(
                    new SaveSignRecordReq()
                            .setSignObjId(merchantId)
                            //TODO 待产品提供
                            .setAgreementSn("biz_Agreement2023110929")
                            .setSnapshot(CollectionUtil.hashMap("t9",url))
                            .setCtime(new Date())
            );
        } catch (Exception exception) {
            log.error("recordAgreementForT9 exception :{}",exception);
        }
    }




    @Data
    @Accessors(chain = true)
    private static class AgreementSnapshot {
        /**
         * 商户id
         */
        private String merchant_id;

        /**
         * 商户sn
         */
        private String merchant_sn;

        /**
         * 商户名
         * 小微：结算人姓名  个体组织：营业执照名称
         */
        private String merchant_name;

        /**
         * 商户经营名称
         */
        private String merchant_business_name;

        /**
         * 商户地址
         */
        private String merchant_address;

        /**
         * 商户负责人联系电话
         */
        private String contact_cellphone;

        /**
         * 统一社会信用代码
         * 小微：结算人证件号  个体组织：营业执照号
         */
        private String business_licence_number;

        /**
         * 法人姓名
         * 小微：结算人姓名  个体组织：营业执照法人姓名
         */
        private String legal_person_name;


    }


}
