package com.wosai.upay.job.xxljob.batch.contracttask;

import com.google.common.collect.Lists;
import com.wosai.upay.job.config.ApolloConfig;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * xxl_job_desc: 进件任务-富友入网和信息变更查询
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("ContractTaskFuyouQueryJobHandler")
public class ContractTaskFuyouQueryJobHandler extends AbstractBatchJobHandler<ContractSubTask> {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private SubTaskHandlerContext subTaskHandlerContext;
    @Autowired
    private ChatBotUtil chatBotUtil;


    private final static List<String> FU_YOU_ASYN_CONTRACT_TASK_TYPE_LIST = Lists.newArrayList("新增商户入网", "商户信息变更");


    @Override
    public List<ContractSubTask> queryTaskItems(BatchJobParam param) {
        if (!ApolloConfig.getFuYouSpecialIndustryApplySwitch()) {
            return new ArrayList<>();
        }
        long currentTimeMillis = System.currentTimeMillis();
        return contractSubTaskMapper.selectFuYouContractQueryTask(param.getBatchSize(),
                StringUtil.formatDate(currentTimeMillis - param.getQueryTime()),
                StringUtil.formatDate(currentTimeMillis));
    }

    @Override
    public String getLockKey(ContractSubTask subTask) {
        return "ContractTaskFuyouQueryJobHandler:" + subTask.getMerchant_sn();
    }

    @Override
    public void doHandleSingleData(ContractSubTask subTask) {
        try {
            subTask = contractSubTaskMapper.selectByPrimaryKey(subTask.getId());
            if (subTask.getStatus() != 1) {
                return;
            }
            ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id());
            if (!FU_YOU_ASYN_CONTRACT_TASK_TYPE_LIST.contains(contractTask.getType())) {
                return;
            }
            log.info(" merchant_change_data_task merchantSn {}  channel {}  contractId {} handleTask", subTask.getMerchant_sn(), subTask.getChannel(), subTask.getContract_id());
            subTaskHandlerContext.handle(contractTask, subTask);
        } catch (Exception e) {
            log.error("处理商户信息变更任务异常,商户号为 {}", subTask.getMerchant_sn(), e);
            chatBotUtil.sendMessageToContractWarnChatBot("processContractTasks error" + ExceptionUtil.getThrowableMsg(e) + "商户号为" + subTask.getMerchant_sn());
        }
    }
}
