package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonException;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.config.OssAK;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.mapper.McBatchTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.BatchContract;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.service.ReContractService;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.util.*;

/**
 * xxl_job_desc: 处理批量切换和报备任务
 * 基本上没有相关任务，将其改造为 direct 模式即可
 *
 * <AUTHOR>
 * @date 2025/4/11
 */
@Slf4j
@Component("BatchContractOrChangeJobHandler")
public class BatchContractOrChangeJobHandler extends AbstractMcBatchTaskJobHandler {

    @Autowired
    private ParamContextBiz paramContextBiz;
    @Autowired
    private RuleBiz ruleBiz;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private BusinessLogBiz businessLogBiz;
    @Autowired
    private ChangeTradeParamsBiz tradeParamsBiz;
    @Autowired
    private SubBizParamsBiz subBizParamsBiz;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    public static final String ENDPOINT_URL = "http://oss-cn-hangzhou.aliyuncs.com/";
    public static String STATICS_BUCKET_NAME = "wosai-images";

    private static final OSSClient client = OssAK.buildOSSClient(ENDPOINT_URL);

    @Override
    public String getLockKey() {
        return "BatchContractOrChangeJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        McBatchTaskExample mcBatchTaskExample = new McBatchTaskExample();
        mcBatchTaskExample.or().andStatusEqualTo(0).andEffect_timeLessThanOrEqualTo(new Date()).andTypeIn(Lists.newArrayList(1, 2, 3));
        List<McBatchTask> list = mcBatchTaskMapper.selectByExampleWithBLOBs(mcBatchTaskExample);
        list.forEach(r -> {
            if (ShutdownSignal.isShuttingDown()) {
                return;
            }
            boolean contract = r.getType() == 1;
            if (contract) {
                batchContract(r);
            } else {
                batchChange(r);
            }
        });
    }

    private void batchContract(McBatchTask mcBatchTask) {
        String url;
        try {
            ExcelImportResult importResult = produceSources(mcBatchTask);
            List<BatchContract> sources = importResult.getList();
            Map payLoad = JSON.parseObject(mcBatchTask.getPayload(), Map.class);
            ContractRule contractRule = ruleContext.getContractRule((String) payLoad.get("rule"));
            processContractSources(sources, contractRule);
            sources.addAll(importResult.getFailList());
            url = uploadToOss(mcBatchTask, sources);
            Integer resultStatus = StringUtils.isEmpty(url) ? 3 : 2;
            String applyResult = StringUtils.isEmpty(url) ? "上传文件失败" : url;
            //更新batch
            updateTaskResult(resultStatus, mcBatchTask, applyResult, null);
        } catch (Exception e) {
            log.error(" BatchContractProcessor error", e);
            //更新batch
            updateTaskResult(3, mcBatchTask, null, e);
        }
    }

    private void batchChange(McBatchTask mcBatchTask) {
        String url;
        try {
            Map payload = JSON.parseObject(mcBatchTask.getPayload(), Map.class);
            ContractChannel channel = ruleContext.getContractChannel((String) payload.get("rule"));

            List<BatchContract> sources;
            List<BatchContract> fails = new ArrayList<>();
            if (mcBatchTask.getType() == 2) {
                ExcelImportResult importResult = produceSources(mcBatchTask);
                sources = importResult.getList();
                fails = importResult.getFailList();
            } else {
                ContractChannel oldChannel = ruleContext.getContractChannel((String) payload.get("oldRule"));
                sources = produceSourcesByProvider(oldChannel, payload);
            }
            doChangeTradeParams(sources, channel, mcBatchTask.getOperator_id(), mcBatchTask.getOperator_name(), BeanUtil.getPropString(payload, "feeRate"));
            sources.addAll(fails);
            url = uploadToOss(mcBatchTask, sources);
            Integer resultStatus = StringUtils.isEmpty(url) ? 3 : 2;
            String applyResult = StringUtils.isEmpty(url) ? "上传文件失败" : url;
            //更新batch
            updateTaskResult(resultStatus, mcBatchTask, applyResult, null);
        } catch (Exception e) {
            log.error("BatchChangeProcessor error", e);
            updateTaskResult(3, mcBatchTask, null, e);
        }
    }

    private boolean changeConfig(String merchantSn, String channel, String fee) {
        if (StringUtils.isEmpty(merchantSn) || StringUtils.isEmpty(channel)) {
            throw new CommonInvalidParameterException("商户号或渠道不能为空");
        }
        ContractChannel contractChannel = ruleContext.getContractChannel(channel);
        MerchantProviderParamsExample merchantProviderParamsExample = new MerchantProviderParamsExample();
        //查询该 payway下 channelno下 所有的商户
        merchantProviderParamsExample.or()
                .andMerchant_snEqualTo(merchantSn)
                .andChannel_noEqualTo(contractChannel.getChannel_no())
                .andProviderEqualTo(Integer.valueOf(contractChannel.getProvider()))
                .andPaywayEqualTo(contractChannel.getPayway()).andDeletedEqualTo(false);
        merchantProviderParamsExample.setOrderByClause("ctime desc");
        List<MerchantProviderParams> merchantProviderParamsList = merchantProviderParamsMapper.selectByExample(merchantProviderParamsExample);
        if (CollectionUtils.isEmpty(merchantProviderParamsList)) {
            throw new CommonPubBizException(String.format("商户在 %s 下还未报备", channel));
        }
        MerchantProviderParams merchantProviderParams = merchantProviderParamsList.get(0);
        if (PaywayEnum.WEIXIN.getValue().equals(merchantProviderParams.getPayway()) && merchantProviderParams.getParams_config_status() == MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE) {
            throw new CommonPubBizException(String.format("商户在 %s 下微信子商户号还未完成配置", channel));
        }
        return tradeParamsBiz.changeTradeParams(merchantProviderParams, fee, false, subBizParamsBiz.getPayTradeAppId());

    }

    private List<BatchContract> produceSourcesByProvider(ContractChannel channel, Map payLoad) {
        MerchantProviderParamsExample merchantProviderParamsExample = new MerchantProviderParamsExample();
        //查询该 payway下 channelno下 所有的商户
        //全量切有风险 这里限制时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -7);
        merchantProviderParamsExample.or().andPaywayEqualTo(channel.getPayway()).andChannel_noEqualTo(channel.getChannel_no()).andCtimeGreaterThanOrEqualTo(calendar.getTimeInMillis()).andDeletedEqualTo(false);
        List<MerchantProviderParams> merchantProviderParamsList = merchantProviderParamsMapper.selectByExample(merchantProviderParamsExample);
        List<BatchContract> list = new ArrayList<>(merchantProviderParamsList.size());
        merchantProviderParamsList.forEach(record -> {
            BatchContract batchContract = new BatchContract();
            batchContract.setMerchantSn(record.getMerchant_sn());
            batchContract.setRemark(BeanUtil.getPropString(payLoad, "remark", "批量按条件配置交易参数"));
            list.add(batchContract);
        });
        return list;
    }

    private void doChangeTradeParams(List<BatchContract> sources, ContractChannel contractChannel, String userId, String userName, String fee) {
        Set<String> sns = new HashSet<>();
        sources.forEach(
                s -> {
                    try {
                        if (sns.add(s.getMerchantSn())) {
                            Map merchant = merchantService.getMerchantBySn(s.getMerchantSn());
                            if (WosaiMapUtils.isEmpty(merchant)) {
                                s.setErrorMsg("商户不存在");
                                return;
                            }
                            String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
                            Map before = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, contractChannel.getPayway());

                            changeConfig(s.getMerchantSn(), contractChannel.getChannel(), fee);
                            s.setErrorMsg("配置成功");
                            Map after = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, contractChannel.getPayway());
                            //异步写商户日志
                            businessLogBiz.sendMerchantConfigBusinessLog(before, after, userId, userName, s.getRemark());
                        } else {
                            s.setErrorMsg("商户号重复");
                        }
                    } catch (Exception e) {
                        if (e instanceof CommonException || e instanceof com.wosai.upay.common.exception.CommonException) {
                            s.setErrorMsg(ExceptionUtil.getThrowableMsg(e));
                        } else {
                            s.setErrorMsg("配置失败：" + ExceptionUtil.getThrowableMsg(e));
                        }
                    }

                }
        );
    }

    private void processContractSources(List<BatchContract> s, ContractRule contractRule) {
        Set<String> sns = Sets.newHashSet();
        boolean checkExist = true;
        //微信支持强制重新报备
        if (PaywayEnum.WEIXIN.getValue().equals(contractRule.getPayway())) {
            checkExist = false;
        }
        for (BatchContract record : s) {
            String merchantSn = record.getMerchantSn();
            if (sns.add(merchantSn)) {
                try {
                    Map paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, new ContractEvent().setEvent_type(0));
                    if (!checkExist) {
                        paramContext.put(ReContractService.TYPE, "1");
                    }
                    String message = (String) ruleBiz.contractByRule(merchantSn, contractRule, paramContext, checkExist).get(RuleBiz.CONTRACT_RES_KEY);
                    if (StringUtils.isEmpty(message)) {
                        record.setErrorMsg("报备成功");
                    } else {
                        record.setErrorMsg(message);
                    }
                } catch (ContextParamException ce) {
                    log.error(" merchantSn {} ce", merchantSn, ce);
                    record.setErrorMsg(ce.getMessage());
                } catch (Exception e) {
                    record.setErrorMsg(ExceptionUtil.getThrowableMsg(e));
                    log.error(" merchantSn {} e", merchantSn, e);
                }
            } else {
                record.setErrorMsg("商户号重复");
            }
        }
    }

    private ExcelImportResult<BatchContract> produceSources(McBatchTask mcBatchTask) throws Exception {
        ImportParams params = new ImportParams();
        params.setNeedVerfiy(true);
        params.setHeadRows(1);
        Map payLoad = JSON.parseObject(mcBatchTask.getPayload(), Map.class);
        OSSObject ossObject = client.getObject(STATICS_BUCKET_NAME, (String) payLoad.get("fileUrl"));
        return ExcelImportUtil.importExcelMore(ossObject.getObjectContent(), BatchContract.class, params);
    }

    private void updateTaskResult(Integer resultStatus, McBatchTask mcBatchTask, String res, Exception e) {
        if (e == null) {
            mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(resultStatus).setId(mcBatchTask.getId()).setResult(res));
            Map<String, Object> result = CollectionUtil.hashMap("downloadResultUrl", res);
            logService.updateTaskApplyLog(CollectionUtil.hashMap(DaoConstants.ID, mcBatchTask.getTask_apply_log_id(), TaskApplyLog.APPLY_STATUS, resultStatus, TaskApplyLog.APPLY_RESULT, JSON.toJSONString(result)));
        } else {
            mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(3).setId(mcBatchTask.getId()).setResult(ExceptionUtil.getThrowableMsg(e)));
            Map<String, Object> result = CollectionUtil.hashMap("downloadResultUrl", ExceptionUtil.getThrowableMsg(e));
            logService.updateTaskApplyLog(CollectionUtil.hashMap(DaoConstants.ID, mcBatchTask.getTask_apply_log_id(), TaskApplyLog.APPLY_STATUS, 3, TaskApplyLog.APPLY_RESULT, JSON.toJSONString(result)));
        }
    }

    private String uploadToOss(McBatchTask mcBatchTask, List<BatchContract> sources) {
        File file = null;
        try {
            //上传结果
            ExportParams exportParams = new ExportParams("批量" + (mcBatchTask.getType() == 1 ? "报备" : "配置交易参数"), "结果");
            exportParams.setType(ExcelType.XSSF);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, BatchContract.class, sources);
            file = File.createTempFile(System.currentTimeMillis() + "", ".xlsx");
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            workbook.write(fileOutputStream);
            fileOutputStream.close();
            ObjectMetadata objectMeta = new ObjectMetadata();
            String baseDir = "merchantContract/batch/";
            objectMeta.setContentLength(file.length());
            String key = baseDir + file.getName();
            client.putObject(STATICS_BUCKET_NAME, key, Files.newInputStream(file.toPath()), objectMeta);
            return key;
        } catch (Exception e) {
            log.error(" uploadToOss error", e);
        } finally {
            try {
                if (file != null) {
                    file.delete();
                }
            } catch (Exception e) {
                log.error("delete file error");
            }
        }
        return null;
    }
}
