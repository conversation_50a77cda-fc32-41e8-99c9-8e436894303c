package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.acquirer.SyncMchStatusResp;
import com.wosai.upay.job.model.acquirer.SyncSubMchIdStatusResp;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.dao.*;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.AlipayBusinessFileds;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.UnionAlipayParam;
import com.wosai.upay.merchant.contract.model.provider.UnionWeixinParam;
import com.wosai.upay.merchant.contract.model.weixin.*;
import com.wosai.upay.merchant.contract.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-04-16
 */
@Component("lkl-biz")
@Slf4j
public class LklAcquirerBiz implements IAcquirerBiz {

    @Autowired
    private NewLakalaService newLakalaService;

    @Autowired
    private AuthApplyFlowService authApplyFlowService;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Value("${lkl.channelno}")
    private String lklChannelNo;

    @Autowired
    ContractParamsBiz contractParamsBiz;

    @Autowired
    private WeixinService weixinService;

    @Autowired
    ParamContextBiz paramContextBiz;

    @Autowired
    private WechatAuthBiz wechatAuthBiz;

    @Autowired
    private NewUnionService newUnionService;

    @Autowired
    private LakalaService lakalaService;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private ContractSubTaskDAO contractSubTaskDAO;

    @Autowired
    private McProviderDAO mcProviderDAO;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private McChannelDAO mcChannelDAO;

    @Autowired
    private MerchantProviderParamsExtDAO merchantProviderParamsExtDAO;

    @Autowired
    private ProviderFactory providerFactory;


    @Autowired
    private TradeConfigService tradeConfigService;

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_LKL;
    }

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        return BeanUtil.getPropString(merchantConfig, "params.lakala_trade_params.lakala_merc_id");
    }

    @Override
    public SyncMchStatusResp syncMchStatusToAcquirer(String merchantSn, int status) {
        //可以重复开,但是重复关会提示 "同步商户状态失败: 终端状态已经是无效状态"
        ContractResponse contractResponse = newLakalaService.updateMerchantStatusToLklV2(merchantSn, status);
        SyncMchStatusResp result;
        final String message = contractResponse.getMessage();
        if (CommonResult.SUCCESS == contractResponse.getCode()) {
            result = new SyncMchStatusResp().setSuccess(true).setMessage("success");
        } else if (!Objects.equals(status, ValidStatusEnum.VALID.getValue()) && !StringUtils.isEmpty(message) && message.contains("终端状态已经是无效状态")) {
            result = new SyncMchStatusResp().setSuccess(true).setMessage("success");
        } else {
            result = new SyncMchStatusResp()
                    .setSuccess(false)
                    .setMessage(message);
        }
        if (result.isSuccess()) {
            MerchantProviderParams params = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA.getValue());
            // 更新子商户号状态
            MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(status == ValidStatusEnum.VALID.getValue() ? ValidStatusEnum.VALID.getValue() : ValidStatusEnum.INVALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
            merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
        }
        return result;
    }

    @Override
    public List<SyncSubMchIdStatusResp> syncSubMchIdStatus(String merchantSn, int status) {
        // 因为银联打开子商户号经常不生效，故不再关闭子商户号权限
        if (status != ValidStatusEnum.VALID.getValue()) {
            return Lists.newArrayList();
        }

        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectLklWxAndAliPayParamsByMerchantSn(merchantSn);
        Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null, false);
        List<SyncSubMchIdStatusResp> result = new ArrayList<>();
        for (MerchantProviderParams params : merchantProviderParams) {
            ContractResponse contractResponse = doSyncSubMchIdStatus(params, status == ValidStatusEnum.VALID.getValue() ? ValidStatusEnum.VALID.getValue() : ValidStatusEnum.INVALID.getValue(), contextParam);
            if (contractResponse.isSuccess()) {
                // 更新子商户号状态
                MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(status == ValidStatusEnum.VALID.getValue() ? ValidStatusEnum.VALID.getValue() : ValidStatusEnum.INVALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
                merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
                result.add(new SyncSubMchIdStatusResp().setSuccess(true).setMessage("success").setSubMchId(params.getPay_merchant_id()));
            } else {
                result.add(new SyncSubMchIdStatusResp().setSuccess(false).setMessage(contractResponse.getMessage()).setSubMchId(params.getPay_merchant_id()));
            }
        }
        log.info("修改子商户号状态,merchant_sn: {} 结果: {}", merchantSn, JSON.toJSONString(result));
        return result;
    }

    private ContractResponse doSyncSubMchIdStatus(MerchantProviderParams params, int status, Map<String, Object> contextParam) {
        try {
            if (PaywayEnum.ALIPAY.getValue().equals(params.getPayway())) {
                UnionAlipayParam unionAlipayParam = contractParamsBiz.buildContractParamsByParams(params, UnionAlipayParam.class);
                return newUnionService.syncAlipaySubMchIdStatus(params.getPay_merchant_id(), status, contextParam, unionAlipayParam);
            } else {
                UnionWeixinParam unionWeixinParam = contractParamsBiz.buildContractParamsByParams(params, UnionWeixinParam.class);
                return newUnionService.syncWechatSubMchIdStatus(params.getPay_merchant_id(), status, contextParam, unionWeixinParam);
            }
        } catch (Exception e) {
            log.error("拉卡拉同步子商户号状态异常, merchant_sn:{} id:{} ", params.getMerchant_sn(), params.getId(), e);
            return new ContractResponse().setCode(405).setMessage(e.getMessage());
        }
    }

    @Override
    public String getNormalWxRule() {
        return ContractRuleConstants.LKL_NORMAL_WEIXIN_RULE;
    }

    @Override
    public void updateWeixinParams(MerchantProviderParamsDto paramsDto, Map params) {
        try {
            Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(paramsDto.getMerchant_sn(), null);
            UnionWeixinParam unionWeixinParam = contractParamsBiz.buildContractParamsByPayMchId(paramsDto.getPay_merchant_id(), UnionWeixinParam.class);
            ContractResponse response = newUnionService.updateWechatWithParams(paramContext, unionWeixinParam);
            log.info("lklMchContractServicePhone result:{},{}", params, response);
        } catch (Exception e) {
            log.error("lklMchContractServicePhone error:{},{}", params, e);
        }
    }


    /**
     * 向支付宝更新商户名称
     *
     * @param params
     * @param alipayMchInfo
     */
    @Override
    public ContractResponse updateAlipayParams(MerchantProviderParams params, AlipayMchInfo alipayMchInfo) {
        Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(params.getMerchant_sn(), null);
        if (alipayMchInfo.getForce_micro()) {
            contextParam.put("forceMicro", true);
        }
        UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByParams(params, UnionAlipayParam.class);
        return newUnionService.updateAlipayMerchantNameWithParams(params.getPay_merchant_id(), contextParam, alipayParam);
    }


    @Override
    public ApplySpecialFeeRateResponse modifyFeeRate(String merchantSn, ModifySpecialFeeRateParam param, ContractChannel contractChannel) {
        String channelId = MapUtils.getString(contractChannel.getChannelParam(), "mch_id");
        return authApplyFlowService.modifyRateApply(param, channelId);
    }

    @Override
    public ApplySpecialFeeRateResponse applyFeeRate(String merchantSn, ApplySpecialFeeRateParam param, ContractChannel contractChannel) {
        String channelId = MapUtils.getString(contractChannel.getChannelParam(), "mch_id");
        return authApplyFlowService.applyFeeRate(param, channelId);
    }

    @Override
    public ApplySpecialFeeRateQueryResponse queryRateApplyStatus(String merchantSn, String applicationId, ContractChannel contractChannel) throws Exception {
        String channelId = MapUtils.getString(contractChannel.getChannelParam(), "mch_id");
        return authApplyFlowService.queryRateApplyStatus(applicationId, channelId);
    }


    /**
     * 获取商户银联参数
     *
     * @param merchantSn
     * @return
     */
    @Override
    public Map<String, Object> getUnionOpenParam(String merchantSn) {
        Map<String, Object> result = new HashMap<>();
        MerchantProviderParams merNo = merchantProviderParamsMapper.getPayMchIdByChannelAndPayWay(merchantSn, "S20180919151112EA3E6", String.valueOf(PaywayEnum.UNIONPAY.getValue()));
        if (ObjectUtils.isEmpty(merNo) || StringUtils.isEmpty(merNo.getPay_merchant_id())) {
            return null;
        }
        MerchantProviderParams union = merchantProviderParamsMapper.getPayMchIdByChannelAndPayWay(merchantSn, lklChannelNo, String.valueOf(PaywayEnum.ACQUIRER.getValue()));
        result.put("merNo", merNo.getPay_merchant_id());
        result.put("union", union.getPay_merchant_id());
        return result;
    }


    /**
     * 微信高校食堂 获取商户 渠道号
     *
     * @return
     */
    @Override
    public String getSchoolCanteenChannelNo(String merchantSn) {
        return applicationApolloConfig.getUseSpecialChannel() ? "217935905" : "36002013293";
    }

    @Override
    public WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        WeixinParam weixinParam = contractParamsBiz.buildContractParams(String.valueOf(providerParams.getProvider()), providerParams.getPayway(), providerParams.getChannel_no(), WeixinParam.class);
        weixinParam.setSub_mch_id(providerParams.getPay_merchant_id());
        SubdevConfigResp subdevConfigResp = weixinService.querySubdevConfig(weixinParam);
        MchInfo mchInfo = null;
        if (ProviderEnum.PROVIDER_LKLORG.getValue().equals(providerParams.getProvider())) {
            try {
                mchInfo = newUnionService.queryWeChatMchInfoParams(providerParams.getPay_merchant_id());
            } catch (Exception e) {
                log.error("查询微信子商户号信息失败: {}", providerParams.getPay_merchant_id(), e);
            }
        } else {
            mchInfo = weixinService.querySubMch(weixinParam);
        }
        return new WxMchInfo().setMchInfo(mchInfo).setSubdevConfig(subdevConfigResp);
    }

    @Override
    public AlipayMchInfo getAlipayMchInfo(MerchantProviderParams providerParams) {
        Map alipayMap = newUnionService.queryAlySubMch(providerParams.getPay_merchant_id());
        if (!AlipayBusinessFileds.RETURN_ALIPAY_CODE_SUCCESS.equals(WosaiMapUtils.getString(alipayMap, AlipayBusinessFileds.CODE))) {
            throw new ContractBizException(WosaiMapUtils.getString(alipayMap, AlipayBusinessFileds.MSG));
        }
        return JSONObject.toJavaObject(JSONObject.parseObject(JSONObject.toJSONString(alipayMap)), AlipayMchInfo.class);
    }

    @Override
    public void syncMchInfo2PayWay(String merchantSn, int payWay) {
        if (payWay != PaywayEnum.ALIPAY.getValue() && payWay != PaywayEnum.WEIXIN.getValue()) {
            throw new ContractBizException("商户所在收单机构不支持向payway=" + payWay + "支付源同步信息");
        }
        Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);

        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andPaywayEqualTo(payWay)
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isEmpty(params)) {
            throw new ContractBizException("商户没有正在使用的子商户号payway=" + payWay);
        }
        // 全部走银联接口

        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByParams(params.get(0), UnionAlipayParam.class);
            response = newUnionService.updateAlipayWithParams(contextParam, alipayParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByParams(params.get(0), UnionWeixinParam.class);
            response = newUnionService.updateWechatWithParams(contextParam, weixinParam);
        }
        if (response == null) {
            throw new ContractBizException("同步失败：未获取到同步结果");
        }
        if (!response.isSuccess()) {
            throw new ContractBizException("同步失败：" + response.getMessage());
        }
    }

    @Override
    public Boolean updateWechatNameAndSettleMentId(MerchantProviderParams params, Map context) {
        WechatAuthBiz.WechatAuthNameAndSettId nameAndSettlementId = wechatAuthBiz.getMerchantNameAndSettlementId(context);
        String merchantName = nameAndSettlementId.getMerchantName();
        String settlementId = nameAndSettlementId.getSettlementId();
        String wx_settlement_id = params.getWx_settlement_id();
        if (Objects.isNull(wx_settlement_id) || !settlementId.equalsIgnoreCase(wx_settlement_id)) {
            return false;
        }
        UnionWeixinParam unionWeixinParam = contractParamsBiz.buildContractParams(String.valueOf(params.getProvider()), params.getPayway(), params.getChannel_no(), UnionWeixinParam.class);
        ContractResponse response = newUnionService.updateWechatName(params.getPay_merchant_id(), merchantName, unionWeixinParam);
        return response.isSuccess();
    }

    /**
     * lkl没有提供商户状态查询接口,所以默认商户未知这样就可以走后续流程
     *
     * @param merchantSn 商户号
     * @return
     */
    @Override
    public Boolean getAcquirerMchStatus(String merchantSn) {
        MerchantProviderParams params = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA.getValue());
        final Map<String, Object> map = lakalaService.queryMerchant(merchantSn);
        final Map<String, Object> result = Optional.ofNullable(map).orElseGet(HashMap::new);
        //查询失败
        if (!Objects.equals(BeanUtil.getPropString(result, CommonModel.RESULT_CODE), CommonModel.RESULT_CODE_SUCCESS)) {
            throw new CommonPubBizException(BeanUtil.getPropString(result, CommonModel.RESULT_MESSAGE));
        }
        //商户收单机构状态 有效-VALID 无效-INVALID & 同步状态到数据库中
        final String merStatus = (String) BeanUtil.getNestedProperty(result, "message.status");
        if (Objects.equals(merStatus, "VALID")) {
            if (!params.getMerchant_state().equals(ValidStatusEnum.VALID.getValue())) {
                MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(ValidStatusEnum.VALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
                merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
            }
            return Boolean.TRUE;
        }
        if (params.getMerchant_state().equals(ValidStatusEnum.VALID.getValue())) {
            MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(ValidStatusEnum.INVALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
            merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean bankAccountConsistent(String merchantSn, Map bankAccount) {
        final Map<String, Object> result = Optional.ofNullable(lakalaService.queryMerchant(merchantSn)).orElseGet(HashMap::new);
        //查询失败
        if (!Objects.equals(BeanUtil.getPropString(result, CommonModel.RESULT_CODE), CommonModel.RESULT_CODE_SUCCESS)) {
            throw new CommonPubBizException(BeanUtil.getPropString(result, CommonModel.RESULT_MESSAGE));
        }
        //收单机构返回的银行卡
        final String accountNo = (String) BeanUtil.getNestedProperty(result, "message.accountNo");
        //收钱吧银行卡
        final String number = BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.NUMBER);
        log.info("lkl通道下商户号merchantSn:{},lkl银行账户:{},收钱吧银行账户:{}", merchantSn, accountNo, number);
        return Objects.equals(accountNo, number);
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        Optional<MerchantProviderParamsDO> unionParam = getUnionPayParams(merchantSn, AcquirerTypeEnum.LKL.getValue());
        if (unionParam.isPresent()) {
            // 先调用接口来查询，然后将结果写到ext中
            MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA.getValue());
            BasicProvider provider = providerFactory.getProvider(String.valueOf(acquirerParams.getProvider()));
            com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = provider.queryMerchantContractResult(acquirerParams.getProvider_merchant_id());
            MerchantProviderParamsExtDO extDO = new MerchantProviderParamsExtDO();
            extDO.setParamId(unionParam.get().getId());
            extDO.setExtField2(unionParam.get().getPayMerchantId());
            extDO.setType(MerchantProviderParamsExtDO.UNION_PAY);

            if (contractResponse.isSystemFail()) {
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                        .retry(false)
                        .build();
            } else if (contractResponse.isBusinessFail()) {
                extDO.setExtField1(MerchantProviderParamsExtDO.UNION_PAY_FAIL);
                extDO.setExtra(JSON.toJSONString(CollectionUtil.hashMap("message", contractResponse.getMessage())));
                merchantProviderParamsExtDAO.saveMerchantParametersExt(extDO);
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message(contractResponse.getMessage())
                        .retry(true)
                        .build();
            } else {
                String status = WosaiMapUtils.getString(contractResponse.getTradeParam(), "status");
                if (MerchantProviderParamsExtDO.UNION_PAY_FAIL.equals(status)) {
                    extDO.setExtField1(status);
                    extDO.setExtra(JSON.toJSONString(CollectionUtil.hashMap("message", contractResponse.getMessage())));
                    merchantProviderParamsExtDAO.saveMerchantParametersExt(extDO);
                    return UnionPayOpenStatusQueryResp.builder()
                            .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                            .message(contractResponse.getMessage())
                            .retry(true)
                            .build();
                } else if (MerchantProviderParamsExtDO.UNION_PAY_SUCCESS.equals(status)) {
                    extDO.setExtField1(status);
                    merchantProviderParamsExtDAO.saveMerchantParametersExt(extDO);
                    return UnionPayOpenStatusQueryResp.builder()
                            .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                            .retry(true)
                            .build();
                } else {
                    extDO.setExtField1(status);
                    extDO.setExtra(JSON.toJSONString(CollectionUtil.hashMap("message", "已注销")));
                    merchantProviderParamsExtDAO.saveMerchantParametersExt(extDO);
                    return UnionPayOpenStatusQueryResp.builder()
                            .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                            .message("已注销")
                            .retry(true)
                            .build();
                }
            }
        }
        ContractSubTaskDO contractSubTaskDO = getUnionNetInTask(merchantSn, AcquirerTypeEnum.LKL.getValue());
        if (Objects.nonNull(contractSubTaskDO)) {
            if (TaskStatus.PROGRESSING.getVal().equals(contractSubTaskDO.getStatus()) || TaskStatus.PENDING.getVal().equals(contractSubTaskDO.getStatus())) {
                if (ChronoUnit.DAYS.between(contractSubTaskDO.getCreateAt().toLocalDateTime().toLocalDate(), LocalDate.now()) < 7) {
                    return UnionPayOpenStatusQueryResp.builder()
                            .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                            .message("开通中，请稍后重试")
                            .retry(true)
                            .build();
                } else {
                    return UnionPayOpenStatusQueryResp.builder()
                            .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                            .message("暂时无法开通，联系销售支持")
                            .retry(false)
                            .build();
                }
            } else if (TaskStatus.SUCCESS.getVal().equals(contractSubTaskDO.getStatus())) {
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                        .message("暂时无法开通，联系销售支持")
                        .retry(false)
                        .build();
            } else {
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message(WosaiMapUtils.getString(JSON.parseObject(contractSubTaskDO.getResult()), "message"))
                        .retry(false)
                        .build();

            }
        }
        return UnionPayOpenStatusQueryResp.builder()
                .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                .message("暂时无法开通，联系销售支持")
                .retry(false)
                .build();
    }

    private ContractSubTaskDO getUnionNetInTask(String merchantSn, String acquirer) {
        List<ContractSubTaskDO> unionPaySubTasks = contractSubTaskDAO.listContractSubTaskDOsByPayway(merchantSn, ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT, PaywayEnum.UNIONPAY.getValue());
        for (ContractSubTaskDO unionPaySubTask : unionPaySubTasks) {
            String taskAcquirer = "";
            if (WosaiStringUtils.isNotBlank(unionPaySubTask.getContractRule())) {
                ContractRule contractRule = ruleContext.getContractRule(unionPaySubTask.getContractRule());
                taskAcquirer = contractRule.getAcquirer();
            } else {
                Optional<McProviderDO> mcProviderDOOptional = mcProviderDAO.getByBeanName(unionPaySubTask.getChannel());
                if (mcProviderDOOptional.isPresent()) {
                    taskAcquirer = mcProviderDOOptional.get().getAcquirer();
                }
            }

            if (acquirer.contains(McConstant.ACQUIRER_LKL) && taskAcquirer.contains(McConstant.ACQUIRER_LKL)) {
                return unionPaySubTask;
            }
            if (taskAcquirer.equals(acquirer)) {
                return unionPaySubTask;
            }
        }
        return null;
    }

    private Optional<MerchantProviderParamsDO> getUnionPayParams(String merchantSn, String acquirer) {
        List<McChannelDO> mcChannels = mcChannelDAO.getMcChannelByAcquirer(acquirer, PaywayEnum.UNIONPAY.getValue());
        for (McChannelDO mcChannel : mcChannels) {
            Optional<MerchantProviderParamsDO> merChantProviderParams = merchantProviderParamsDAO.getMerChantProviderParams(merchantSn, mcChannel.getChannelNo(), PaywayEnum.UNIONPAY.getValue());
            if (merChantProviderParams.isPresent()) {
                return merChantProviderParams;
            }
        }
        return Optional.empty();

    }

    @Override
    public Boolean bankAccountConsistentAndStatusOpen(String merchantSn, Map bankAccount) {
        final Map<String, Object> result = Optional.ofNullable(lakalaService.queryMerchant(merchantSn)).orElseGet(HashMap::new);
        //查询失败
        if (!Objects.equals(BeanUtil.getPropString(result, CommonModel.RESULT_CODE), CommonModel.RESULT_CODE_SUCCESS)) {
            throw new CommonPubBizException(BeanUtil.getPropString(result, CommonModel.RESULT_MESSAGE));
        }
        //收单机构返回的银行卡
        final String accountNo = (String) BeanUtil.getNestedProperty(result, "message.accountNo");
        //收钱吧银行卡
        final String number = BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.NUMBER);
        log.info("lkl通道下商户号merchantSn:{},lkl银行账户:{},收钱吧银行账户:{}", merchantSn, accountNo, number);
        final boolean bankAccountConsistent = Objects.equals(accountNo, number);
        if (!bankAccountConsistent) {
            return Boolean.FALSE;
        }
        //商户收单机构状态 有效-VALID 无效-INVALID & 同步状态到数据库中
        final String merStatus = (String) BeanUtil.getNestedProperty(result, "message.status");
        MerchantProviderParams params = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA.getValue());
        //在拉卡拉有效
        if (Objects.equals(merStatus, "VALID")) {
            if (!params.getMerchant_state().equals(ValidStatusEnum.VALID.getValue())) {
                MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(ValidStatusEnum.VALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
                merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
            }
            return Boolean.TRUE;
        }
        //在拉卡拉无效,但在收钱吧有效,更新收钱吧状态
        if (params.getMerchant_state().equals(ValidStatusEnum.VALID.getValue())) {
            MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(ValidStatusEnum.INVALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
            merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
        }
        return Boolean.FALSE;
    }
}