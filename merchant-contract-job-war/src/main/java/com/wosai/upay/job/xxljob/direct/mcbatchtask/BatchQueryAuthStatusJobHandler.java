package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.shouqianba.workflow.bean.CallBackBean;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.job.Constants.ApproveConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.QueryAuthStatusExcel;
import com.wosai.upay.job.model.dto.QueryAuthStatusDTO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.merchant.contract.model.weixin.MchAuthResp;
import com.wosai.upay.merchant.contract.service.AuthApplyFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.WechatAuthBiz.AUTHORIZE_STATE_AUTHORIZED;

/**
 * xxl_job_desc: 批量查询微信子商户号授权状态
 * <AUTHOR>
 * @date 2025/4/11
 */
@Slf4j
@Component("BatchQueryAuthStatusJobHandler")
public class BatchQueryAuthStatusJobHandler extends AbstractMcBatchTaskJobHandler {

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private AuthApplyFlowService authApplyFlowService;


    @Override
    public String getLockKey() {
        return "BatchQueryAuthStatusJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        McBatchTaskExample mcBatchTaskExample = new McBatchTaskExample();
        mcBatchTaskExample.or().andStatusEqualTo(0).andEffect_timeLessThanOrEqualTo(new Date()).andTypeEqualTo(6);
        List<McBatchTask> list = mcBatchTaskMapper.selectByExampleWithBLOBs(mcBatchTaskExample);
        list.forEach(mcBatchTask -> {
            Map extra = Maps.newHashMap();
            try {
                final String payload = mcBatchTask.getPayload();
                extra = CommonUtil.string2Map(payload);
                final QueryAuthStatusDTO queryAuthStatusDTO = new ObjectMapper().convertValue(MapUtils.getMap(extra, ApproveConstant.APPROVE_INFO), QueryAuthStatusDTO.class);
                final String attachmentUrl = queryAuthStatusDTO.getAttachmentUrls().get(0);
                final List<QueryAuthStatusExcel> queryAuthStatusExcelExcelList = excelUtil.getExcelInfoList(attachmentUrl, new QueryAuthStatusExcel());
                if (WosaiCollectionUtils.isEmpty(queryAuthStatusExcelExcelList)) {
                    throw new CommonPubBizException("批量查询一次条数不能为空");
                }
                final int hxWxAuthCount = applicationApolloConfig.getHxWxAuthCount();
                Optional.of(queryAuthStatusExcelExcelList).filter(collection -> collection.size() >= hxWxAuthCount).ifPresent(t -> {
                    throw new CommonPubBizException(String.format("批量查询一次不能超过%s条", hxWxAuthCount));
                });
                //异步线程多次调用
                final List<CompletableFuture<QueryAuthStatusExcel>> futureList = queryAuthStatusExcelExcelList.parallelStream().map(queryAuthStatusExcel -> CompletableFuture.supplyAsync(() -> {
                    final String wxSubMchId = queryAuthStatusExcel.getWxSubMchId();
                    MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(wxSubMchId);
                    ContractChannel contractChannel = ruleContext.getContractChannel(
                            merchantProviderParams.getPayway(), String.valueOf(merchantProviderParams.getProvider()), merchantProviderParams.getChannel_no()
                    );

                    MchAuthResp authResp = authApplyFlowService.queryAuthStatus(wxSubMchId, contractChannel.buildAuthV3Param());
                    Boolean authed = AUTHORIZE_STATE_AUTHORIZED.equals(authResp.getAuthorize_state()) ? Boolean.TRUE : Boolean.FALSE;
                    queryAuthStatusExcel.setResult(authed);
                    return queryAuthStatusExcel;
                }, batchScheduleExecutorThreadPoolTaskExecutor)).collect(Collectors.toList());
                //获取结果
                final List<QueryAuthStatusExcel> authStatusResult = futureList.stream().map(CompletableFuture::join).collect(Collectors.toList());
                //将authStatusResult集合变成文件上传到oss中
                final String url = excelUtil.uploadToOss(authStatusResult, BASE_DIR);
                //修改状态并将结果存储
                extra.put(ApproveConstant.LAST_ATTACHMENT_URL, url);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(2).setId(mcBatchTask.getId()).setPayload(JSONObject.toJSONString(extra)).setResult("处理成功"));
                //回调审批中心
                callBackAuthStatus(extra, "处理结果请使用浏览器下载链接对应的Excel:" + url, AUDIT_EXECUTE_SUCCESS);
                logService.updateTaskApplyLog(CollectionUtil.hashMap(DaoConstants.ID, mcBatchTask.getTask_apply_log_id(), TaskApplyLog.APPLY_STATUS, APPLY_STATUS_EXCUTE_SUCCESS, TaskApplyLog.APPLY_RESULT, JSON.toJSONString(CollectionUtil.hashMap("result", "处理结果请使用浏览器下载链接对应的Excel:" + url))));
            } catch (Exception e) {
                log.error("batchQueryAuthStatus error mc_batch_task {}  exception", mcBatchTask.getId(), e);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(3).setId(mcBatchTask.getId()).setResult(ExceptionUtil.getThrowableMsg(e)));
                //回调审批中心
                callBackAuthStatus(extra, ExceptionUtil.getThrowableMsg(e), AUDIT_EXECUTE_FAIL);
                logService.updateTaskApplyLog(CollectionUtil.hashMap(DaoConstants.ID, mcBatchTask.getTask_apply_log_id(), TaskApplyLog.APPLY_STATUS, APPLY_STATUS_EXCUTE_FAILURE, TaskApplyLog.APPLY_RESULT, JSON.toJSONString(CollectionUtil.hashMap("result", ExceptionUtil.getThrowableMsg(e)))));
            }
        });
    }

    private void callBackAuthStatus(Map extra, String message, Integer resultType) {
        final QueryAuthStatusDTO dto = objectMapper.convertValue(MapUtils.getMap(extra, ApproveConstant.APPROVE_INFO), QueryAuthStatusDTO.class);
        final CallBackBean backBean = dto.getCallBackBean();
        if (Objects.isNull(backBean)) {
            return;
        }
        backBean.setResultType(resultType);
        backBean.setMessage(message);
        callBackService.addComment(backBean);
    }
}
