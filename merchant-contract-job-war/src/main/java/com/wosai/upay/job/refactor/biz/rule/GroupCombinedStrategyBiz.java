package com.wosai.upay.job.refactor.biz.rule;

import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.upay.job.refactor.dao.GroupCombinedStrategyDAO;
import com.wosai.upay.job.refactor.dao.GroupCombinedStrategyDetailDAO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.enums.GroupCombinedTypeEnum;
import com.wosai.upay.job.refactor.model.enums.GroupTypeEnum;
import com.wosai.upay.job.refactor.service.factory.GroupCombinedProcessStrategyFactory;
import com.wosai.upay.job.refactor.service.localcache.McRulesLocalCacheService;
import com.wosai.upay.job.refactor.service.strategy.GroupCombinedProcessStrategy;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 进件报备规则组策略组合处理 {@link GroupCombinedStrategyDO}
 *
 * <AUTHOR>
 */
@Service
public class GroupCombinedStrategyBiz {

    @Resource
    private GroupCombinedProcessStrategyFactory groupCombinedProcessStrategyFactory;

    @Resource
    private McRulesLocalCacheService mcRulesLocalCacheService;

    @Resource
    private GroupCombinedStrategyDAO groupCombinedStrategyDAO;

    @Resource
    private GroupCombinedStrategyDetailDAO groupCombinedStrategyDetailDAO;

    /**
     * 根据件报备策略id集合获取策略
     *
     * @param strategyIds 策略id集合
     * @return List<GroupCombinedStrategyDO>
     */
    public List<GroupCombinedStrategyDO> listCombinedStrategyByStrategyIds(Set<Long> strategyIds) {
        if (CollectionUtils.isEmpty(strategyIds)) {
            return Collections.emptyList();
        }
        return groupCombinedStrategyDAO.listByIds(new ArrayList<>(strategyIds));
    }

    /**
     * 根据进件报备策略获取detail
     *
     * @param id 主键
     * @return List<GroupCombinedStrategyDetailDO>
     */
    public List<GroupCombinedStrategyDetailDO> listGroupDetail(Long id) {
        if (Objects.isNull(id)) {
            return Collections.emptyList();
        }
        Optional<GroupCombinedStrategyDO> combinedStrategyDOOptional = mcRulesLocalCacheService.getCombinedStrategyById(id);
        if (!combinedStrategyDOOptional.isPresent()) {
            return Collections.emptyList();
        }
        GroupCombinedStrategyDO combinedStrategyDO = combinedStrategyDOOptional.get();
        Integer strategyType = combinedStrategyDO.getStrategyType();
        List<GroupCombinedStrategyDetailDO> detailDOS = mcRulesLocalCacheService.listStrategyDetailByStrategyId(combinedStrategyDO.getId());
        if (CollectionUtils.isEmpty(detailDOS)) {
            return Collections.emptyList();
        }
        List<GroupCombinedStrategyDetailDO> resDetailDOS = Lists.newArrayListWithCapacity(detailDOS.size());
        groupCombinedProcessStrategyFactory.getStrategy(strategyType).ifPresent(strategy -> resDetailDOS.addAll(strategy.listSatisfactionDetails(detailDOS)));
        return resDetailDOS;
    }

    /**
     * 根据策略类型获取包含指定的groupId对应策略id集合
     *
     * @param groupCombinedTypeEnum 组合类型
     * @param groupIds              报备规则组唯一标识列表
     * @return 策略id集合
     */
    public Set<Long> listStrategyIdsByTypeAndGroupIds(GroupCombinedTypeEnum groupCombinedTypeEnum, List<String> groupIds) {
        if (Objects.isNull(groupCombinedTypeEnum) || CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptySet();
        }
        List<GroupCombinedStrategyDO> strategyDOList = groupCombinedStrategyDAO
                .listValidByGroupType(groupCombinedTypeEnum.getValue())
                .stream()
                .filter(t -> Objects.equals(t.getValidStatus(), ValidStatusEnum.VALID.getValue()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(strategyDOList)) {
            return Collections.emptySet();
        }
        return groupCombinedStrategyDetailDAO
                .listValidByStrategyIdsAndGroupIds(strategyDOList.stream().map(GroupCombinedStrategyDO::getId).collect(Collectors.toList()), groupIds)
                .stream()
                .filter(detail -> Objects.equals(detail.getValidStatus(), ValidStatusEnum.VALID.getValue()))
                .map(GroupCombinedStrategyDetailDO::getGroupStrategyId)
                .collect(Collectors.toSet());
    }

    /**
     * 根据收单机构获取策略id集合
     *
     * @param acquirer 收单机构
     * @return 策略id集合
     */
    public Set<Long> listStrategyIdsByAcquirer(String acquirer) {
        if (StringUtils.isBlank(acquirer)) {
            return Collections.emptySet();
        }
        Set<Long> strategyIds = groupCombinedStrategyDetailDAO
                .listByAcquirer(acquirer)
                .stream()
                .filter(detail -> Objects.equals(detail.getValidStatus(), ValidStatusEnum.VALID.getValue()))
                .map(GroupCombinedStrategyDetailDO::getGroupStrategyId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(strategyIds)) {
            return Collections.emptySet();
        }
        // 防止主表状态失效
        return groupCombinedStrategyDAO.listByIds(new ArrayList<>(strategyIds)).stream()
                .filter(detail -> Objects.equals(detail.getValidStatus(), ValidStatusEnum.VALID.getValue()))
                .map(GroupCombinedStrategyDO::getId)
                .collect(Collectors.toSet());
    }

    /**
     * 新增并且返回收单机构进件规则,不存在新增后返回,存在直接返回
     *
     * @param strategyType                  策略类型
     * @param groupCombinedStrategyDetailDOS 进件报备规则组策略组合detail
     * @return 进件报备规则组策略
     */
    @Transactional(rollbackFor = Exception.class)
    public GroupCombinedStrategyDO insertAndGetGroupCombineStrategy(Integer strategyType, List<GroupCombinedStrategyDetailDO> groupCombinedStrategyDetailDOS) {
        Optional<GroupCombinedProcessStrategy> strategy = groupCombinedProcessStrategyFactory.getStrategy(strategyType);
        if (!strategy.isPresent()) {
            throw new ContractBizException("报备规则组策略组合类型不存在");
        }
        Optional<GroupCombinedStrategyDO> groupCombineStrategyOpt = strategy.get().getGroupCombineStrategy(groupCombinedStrategyDetailDOS);
        if (groupCombineStrategyOpt.isPresent()) {
            return groupCombineStrategyOpt.get();
        }
        GroupCombinedStrategyDO groupCombinedStrategyDO = new GroupCombinedStrategyDO();
        groupCombinedStrategyDO.setValidStatus(ValidStatusEnum.VALID.getValue());
        groupCombinedStrategyDO.setStrategyType(strategyType);
        StringBuffer remark = new StringBuffer();
        groupCombinedStrategyDetailDOS.forEach(detailDO -> remark.append(EnumUtils.getEnum(GroupTypeEnum.class, detailDO.getGroupType()).getText())
                .append(EnumUtils.getEnum(AcquirerTypeEnum.class, detailDO.getAcquirer()).getText()));
        groupCombinedStrategyDO.setRemark(remark.toString());
        groupCombinedStrategyDAO.insertOne(groupCombinedStrategyDO);
        groupCombinedStrategyDetailDOS.forEach(detailDO -> {
            detailDO.setGroupStrategyId(groupCombinedStrategyDO.getId());
            detailDO.setValidStatus(ValidStatusEnum.VALID.getValue());
        });
        groupCombinedStrategyDetailDAO.batchInsert(groupCombinedStrategyDetailDOS);
        return groupCombinedStrategyDO;
    }

    /**
     * 根据策略id集合获取策略detail
     *
     * @param groupStrategyIds 策略id集合
     * @return List<GroupCombinedStrategyDetailDO>
     */
    public List<GroupCombinedStrategyDetailDO> listGroupDetailByStrategyIds(Set<Long> groupStrategyIds) {
        if (CollectionUtils.isEmpty(groupStrategyIds)) {
            return Collections.emptyList();
        }
        return groupCombinedStrategyDetailDAO.listByStrategyIds(new ArrayList<>(groupStrategyIds));
    }
}
