package com.wosai.upay.job.handlers;

import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: 增网增终task
 * <AUTHOR>
 * @Date 2021/4/25 2:51 下午
 **/
@Order(97)
@Component
public class AddMerAndShopSubTaskHandler extends AbstractSubTaskHandler {


    @Autowired
    ProviderFactory providerFactory;

    @Override
    protected void handleError(ContractTask task, ContractSubTask subTask, Exception e) throws Exception {
        handleResult(new ContractResponse().setCode(500).setMessage(e.toString()), subTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doHandle(ContractTask task, ContractSubTask subTask) {
        BasicProvider provider = providerFactory.getProviderByRule(subTask.getContract_rule());
        handleResult(provider.addTerminal(task,subTask), subTask);
    }

    @Override
    public boolean supports(ContractTask task, ContractSubTask subTask) {
        if (ProviderUtil.SUB_TASK_TASK_TYPE_ADD_SHOP.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_BRAND_ADD_SHOP.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_ADD_TERM.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_UNBIND_TERM.equals(subTask.getTask_type())
        ) {
            return true;
        }
        return false;
    }
}