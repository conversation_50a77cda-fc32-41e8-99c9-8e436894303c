package com.wosai.upay.job.refactor.biz.acquirer.lklv3;

import com.shouqianba.cua.utils.object.ConvertUtil;
import com.shouqianba.cua.utils.object.ObjectExtensionUtils;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.providers.LklOpenProvider;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.model.LklV3ContractResultDTO;
import com.wosai.upay.job.refactor.model.bo.LklOpenUnionPayTradeParamBO;
import com.wosai.upay.job.refactor.model.dto.NewMerchantContractResultRspDTO;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * lklV3商户入网处理
 *
 * <AUTHOR>
 * @date 2024/9/10 14:07
 */
@Component
@Slf4j
public class LklV3MerchantContractProcessor {

    @Resource
    private LklV3Service lklV3Service;

    @Resource
    private ParamContextBiz paramContextBiz;

    @Resource
    private ContractParamsBiz contractParamsBiz;

    // todo 待重构，移除该应用
    @Resource
    private LklOpenProvider lklOpenProvider;

    /**
     * 商户向收单机构报备
     *
     * @param merchantSn 商户号
     * @return 报备结果
     */
    public NewMerchantContractResultRspDTO contractToAcquirer(String merchantSn) {
        Map<String, Object> contextMap = paramContextBiz.getNetInParamContextByMerchantSn(merchantSn);
        return contractToAcquirer(merchantSn, contextMap);
    }

    /**
     * 商户向收单机构报备
     *
     * @param merchantSn 商户号
     * @return 报备结果
     */
    public NewMerchantContractResultRspDTO contractToAcquirer(String merchantSn, Map<String, Object> contextMap) {
        LklV3Param lklV3Param = getLklV3Param();
        paramContextBiz.fillFeeRatesByRuleGroupId(contextMap, McConstant.RULE_GROUP_LKLORG);
        NewMerchantContractResultRspDTO newMerchantContractResultRspDTO = new NewMerchantContractResultRspDTO();
        try {
            ContractResponse contractResponse = lklV3Service.contractMerchantWithParams(contextMap, lklV3Param);
            newMerchantContractResultRspDTO.setRequest(contractResponse.getRequestParam());
            newMerchantContractResultRspDTO.setResponse(contractResponse.getResponseParam());
            if (contractResponse.isSuccess()) {
                newMerchantContractResultRspDTO.setStatus(NewMerchantContractResultRspDTO.STATUS_PROCESSING);
                newMerchantContractResultRspDTO.setContractId(BeanUtil.getPropString(contractResponse.getResponseParam(),
                        "respData.contractId"));
            } else {
                log.error("contractToAcquirer error, merchantSn:{}, response:{}, message:{}", merchantSn, contractResponse, contractResponse.getMessage());
                newMerchantContractResultRspDTO.setStatus(NewMerchantContractResultRspDTO.STATUS_FAIL);
                newMerchantContractResultRspDTO.setMessage(contractResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("contractToAcquirer error, merchantSn:{}", merchantSn, e);
            newMerchantContractResultRspDTO.setStatus(NewMerchantContractResultRspDTO.STATUS_FAIL);
        }
        return newMerchantContractResultRspDTO;

    }

    private LklV3Param getLklV3Param() {
        return contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
    }

    /**
     * 查询进件的结果
     * todo 针对报文字段不一致场景，逻辑待重构，如何规避此问题
     *
     * @param contractId 进件id
     * @return 进件结果
     */
    public LklV3ContractResultDTO queryContractResult(String contractId) {
        ContractResponse contractResponse = lklV3Service.queryContractResult(contractId, getLklV3Param());
        LklV3ContractResultDTO lklV3ContractResultDTO = new LklV3ContractResultDTO();
        lklV3ContractResultDTO.setRequestMap(contractResponse.getRequestParam());
        lklV3ContractResultDTO.setResponseMap(contractResponse.getResponseParam());
        if (lklV3ContractResultDTO.isContractSuccess()) {
            Map<String, Object> responseParam = contractResponse.getResponseParam();
            LklOpenUnionPayTradeParamBO tradeParamBO = lklOpenProvider.getLklOpenUnionPayTradeParamBOByCallBackMsg(responseParam);
            lklV3ContractResultDTO.setAcquirerMerchantId(getAcquirerMerchantId(responseParam));
            lklV3ContractResultDTO.setUnionMerchantId(tradeParamBO.getProviderMerchantId());
            lklV3ContractResultDTO.setTermId(tradeParamBO.getTermId());
            lklV3ContractResultDTO.setShopId(getShopId(responseParam));
        }
        return lklV3ContractResultDTO;
    }


    private String getShopId(Map<String, Object> responseParam) {
        AtomicReference<String> shopId = new AtomicReference<>();
        ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(responseParam, "data.termDatas"),
                termData -> ConvertUtil.castToExpectedList(termData, Map.class)
                        .stream().findFirst().ifPresent(map ->
                                ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(map, "shopId"),
                                        termId -> ConvertUtil.castToExpectedType(termId, String.class).ifPresent(shopId::set))
                        ));
        if (StringUtils.isBlank(shopId.get())) {
            ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(responseParam, "respData.termDatas"),
                    termData -> ConvertUtil.castToExpectedList(termData, Map.class)
                            .stream().findFirst().ifPresent(map ->
                                    ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(map, "shopId"),
                                            termId -> ConvertUtil.castToExpectedType(termId, String.class).ifPresent(shopId::set))
                            ));
        }
        return shopId.get();
    }

    private String getAcquirerMerchantId(Map<String, Object> responseParam) {
        return StringUtils.isBlank(StringExtensionUtils.toSafeString(BeanUtil.getNestedProperty(responseParam, "data.merInnerNo"))) ?
                StringExtensionUtils.toSafeString(BeanUtil.getNestedProperty(responseParam, "respData.merInnerNo"))
                : StringExtensionUtils.toSafeString(BeanUtil.getNestedProperty(responseParam, "data.merInnerNo"));
    }

    public LklV3ContractResultDTO queryTaskResultByContractId(String contractId) {
        ContractResponse contractResponse = lklV3Service.queryContractResult(contractId, getLklV3Param());
        LklV3ContractResultDTO lklV3ContractResultDTO = new LklV3ContractResultDTO();
        lklV3ContractResultDTO.setRequestMap(contractResponse.getRequestParam());
        lklV3ContractResultDTO.setResponseMap(contractResponse.getResponseParam());
        return lklV3ContractResultDTO;
    }
}
