package com.wosai.upay.job.refactor.biz.acquirer;

import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 收单机构通用模板
 * 使用委托代替继承，避免了传统模板模式中基于继承的复杂性和耦合度，提供更好的代码复用性和可扩展性
 *
 * <AUTHOR>
 * @date 2024/7/18 09:17
 */
@Component
@Slf4j
public class AcquirerCommonTemplate {

    @Resource
    protected MerchantBasicInfoBiz merchantBasicInfoBiz;

    /**
     * 检查商户银行卡是否与收钱吧一致
     *
     * @param acquirer                      收单机构
     * @param merchantSn                    商户号
     * @param getAcquirerBankCardNoFunction 获取收单机构银行卡号的函数
     * @param predicateBiFunction           判断函数，可以为空，如果为空则默认比较两个银行卡号是否相等
     * @return 是否一致
     */
    public boolean checkBankCardConsistentWithSqb(String acquirer, String merchantSn,
                                                  Function<String, String> getAcquirerBankCardNoFunction,
                                                  @Nullable BiFunction<String, String, Boolean> predicateBiFunction) {
        if (StringUtils.isBlank(merchantSn)) {
            return false;
        }
        Optional<String> sqbBankCardOpt = merchantBasicInfoBiz.getMerchantBankCardNo(merchantSn);
        if (!sqbBankCardOpt.isPresent()) {
            log.warn("商户:{} 没有收钱吧银行卡号", merchantSn);
            return false;
        }
        String acquirerBankCard = getAcquirerBankCardNoFunction.apply(merchantSn);
        if (StringUtils.isBlank(acquirerBankCard)) {
            log.warn("商户:{}, 收单机构:{}, 没有收单机构银行卡号", merchantSn, acquirer);
            return false;
        }
        log.info("商户:{} 收单机构:{}, 收钱吧银行卡号:{} 收单机构银行卡号:{}", merchantSn, acquirer, sqbBankCardOpt.get(), acquirerBankCard);
        if (Objects.nonNull(predicateBiFunction)) {
            return predicateBiFunction.apply(sqbBankCardOpt.get(), acquirerBankCard);
        }
        return StringUtils.equals(sqbBankCardOpt.get(), acquirerBankCard);
    }


}
