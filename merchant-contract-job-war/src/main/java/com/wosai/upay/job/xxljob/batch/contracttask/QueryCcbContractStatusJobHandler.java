package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * xxl_job_desc: 进件任务-建行进件结果查询
 * <AUTHOR>
 * @date 2025/4/18
 */
@Slf4j
@Component("QueryCcbContractStatusJobHandler")
public class QueryCcbContractStatusJobHandler extends AbstractBatchJobHandler<ContractSubTask> {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private QueryContractStatusHandler queryContractStatusHandler;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public List<ContractSubTask> queryTaskItems(BatchJobParam param) {
        return contractSubTaskMapper.selectCcbContractQueryTask(
                param.getBatchSize(),
                StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()),
                StringUtil.formatDate(System.currentTimeMillis())
        );
    }

    @Override
    public String getLockKey(ContractSubTask contractSubTask) {
        return "QueryCcbContractStatusJobHandler:" + contractSubTask.getId();
    }

    @Override
    public void doHandleSingleData(ContractSubTask contractSubTask) {
        try {
            try {
                Thread.sleep(400L);
            } catch (Exception e) {
                log.error("sleep失败");
            }
            //1，再次查询
            ContractSubTask contractSubTaskLast = contractSubTaskMapper.selectByPrimaryKey(contractSubTask.getId());
            //2，判断状态
            if (TaskStatus.isFinish(contractSubTaskLast.getStatus())) {
                return;
            }
            final ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id());
            queryContractStatusHandler.doHandle(contractTask, contractSubTask);
        } catch (Exception e) {
            log.error("QueryCcbContractStatusJobHandler error：", e);
            chatBotUtil.sendMessageToContractWarnChatBot("QueryCcbContractStatusJobHandler error" + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
