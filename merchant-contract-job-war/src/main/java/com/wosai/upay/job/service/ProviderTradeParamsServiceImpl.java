package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.BackAcquirerBiz;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.PayParamsModel;
import com.wosai.upay.job.model.acquirer.ByPassTradeConfig;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.model.MerchantProviderParams;
import com.wosai.upay.merchant.contract.model.MerchantProviderTradeParams;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.wosai.upay.job.model.PayParamsModel.*;

/**
 * Created by lihebin on 2018/9/6.
 */
@Service
@AutoJsonRpcServiceImpl
public class ProviderTradeParamsServiceImpl implements ProviderTradeParamsService{

    private final static Logger log = LoggerFactory.getLogger(ProviderTradeParamsServiceImpl.class);

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private BackAcquirerBiz backAcquirerBiz;

    @Autowired
    private com.wosai.upay.merchant.contract.service.ProviderTradeParamsService providerParamsService;

    @Override
    public List<Map> listMerchantProviderParams(Map params) {
        String merchantSn = BeanUtil.getPropString(params, MerchantProviderParams.MERCHANT_SN);
        String providerMerchantId = BeanUtil.getPropString(params, MerchantProviderParams.PROVIDER_MERCHANT_ID);
        String payMerchantId = BeanUtil.getPropString(params, MerchantProviderParams.PAY_MERCHANT_ID);
        if (StringUtil.empty(merchantSn) && StringUtil.empty(providerMerchantId) && StringUtil.empty(payMerchantId)) {
            return new ArrayList<>();
        }
        int provider = BeanUtil.getPropInt(params, MerchantProviderParams.PROVIDER, -1);
        int payway = BeanUtil.getPropInt(params, MerchantProviderParams.PAYWAY, -1);
        if (provider == 0) {
            resultParamsHandle(getMerchantConfigParamsByFormal(merchantSn, payway));
        }
        PageInfo pageInfo = new PageInfo(1, 100, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        ListResult listResult = providerParamsService.listMerchantProviderParams(pageInfo, params);
        List<Map> merchantProviderParams = listResult.getRecords();
        return resultParamsHandle(getMerchantConfigParamsHandle(merchantProviderParams));
    }


    /**
     * 参数最后包装
     * @param merchantProviderParams
     * @return
     */
    private List<Map> resultParamsHandle(List<Map> merchantProviderParams) {
        for (Map merchantProviderParam : merchantProviderParams) {
            merchantProviderParam.remove(DaoConstants.ID);
            merchantProviderParam.remove(DaoConstants.DELETED);
            merchantProviderParam.remove(DaoConstants.VERSION);
            int payway = BeanUtil.getPropInt(merchantProviderParam, MerchantProviderParams.PAYWAY);
            if (payway != 0) {
                merchantProviderParam.put(PAYWAY_NAME, getPayName.get(payway));
            }
            int provider = BeanUtil.getPropInt(merchantProviderParam, MerchantProviderParams.PROVIDER, -1);
            if (provider != -1) {
                merchantProviderParam.put(PROVIDER_NAME, getPayName.get(provider));
            }
        }
        return merchantProviderParams;
    }


    /**
     * 直连交易参数获取
     * @param merchantSn
     * @param payway
     * @return
     */
    private List<Map> getMerchantConfigParamsByFormal(String merchantSn, int payway) {
        if (StringUtil.empty(merchantSn)) {
            return new ArrayList<>();
        }
        List<Map> result = new ArrayList<>();
        if (payway == -1) {
            result.add(getMerchantConfigParams(merchantSn, PAYWAY_ALIPAY));
            result.add(getMerchantConfigParams(merchantSn, PaywayEnum.ALIPAY.getValue()));
            result.add(getMerchantConfigParams(merchantSn, PaywayEnum.WEIXIN.getValue()));
        } else {
            result.add(getMerchantConfigParams(merchantSn, payway));
        }
        return result;
    }

    private List<Map> getMerchantConfigParamsHandle(List<Map> merchantProviderParams) {
        List<Map> merchantProviderParamsList = new ArrayList<>();
        for (Map merchantProviderParam : merchantProviderParams) {
            int payway = BeanUtil.getPropInt(merchantProviderParam, MerchantProviderParams.PAYWAY);
            if (payway == 0) {
                merchantProviderParam.put(MerchantProviderParams.STATUS, 1);
                merchantProviderParamsList.add(merchantProviderParam);
                continue;
            }
            String merchantSn = BeanUtil.getPropString(merchantProviderParam, MerchantProviderParams.MERCHANT_SN);

            Map merchantConfigParams = getMerchantConfigParams(merchantSn, payway);
            String channelNo = BeanUtil.getPropString(merchantProviderParam, MerchantProviderParams.CHANNEL_NO);
            String payMerchantId = BeanUtil.getPropString(merchantProviderParam, MerchantProviderParams.PAY_MERCHANT_ID);
            String weixinSubAppid = BeanUtil.getPropString(merchantProviderParam, MerchantProviderParams.WEIXIN_SUB_APPID);
            int provider = BeanUtil.getPropInt(merchantConfigParams, MerchantProviderParams.PROVIDER, -1);

            if (merchantConfigParams.containsValue(channelNo) && merchantConfigParams.containsValue(payMerchantId)) {
                if(payway != PaywayEnum.WEIXIN.getValue()){
                    merchantProviderParam.put(MerchantProviderParams.STATUS, 1);
                    merchantProviderParamsList.add(merchantProviderParam);
                    continue;
                }
                if (merchantConfigParams.containsValue(weixinSubAppid)) {
                    merchantProviderParam.put(MerchantProviderParams.STATUS, 1);
                }
                merchantProviderParamsList.add(merchantProviderParam);
            } else if (provider == PayParamsModel.PROVIDER_FORMAL){
                if (MapUtils.isEmpty(merchantConfigParams)) {
                    continue;
                }
                merchantProviderParamsList.remove(merchantConfigParams);
                merchantProviderParamsList.add(merchantConfigParams);
            } else {
                merchantProviderParamsList.add(merchantProviderParam);
            }
        }
        return merchantProviderParamsList;
    }


    /**
     * 获取商户支付方式参数
     * @param merchantSn
     * @param payway
     * @return
     */
    private Map getMerchantConfigParams(String merchantSn, int payway) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        try {
            Map params = tradeConfigService.getTradeParams(payway, 3, StringUtil.hashMap(MerchantConfig.MERCHANT_ID, merchantId));
            if (MapUtils.isEmpty(params)) {
                return new HashMap<>();
            }
            for (Object key : params.keySet()) {
                if (String.valueOf(key).contains(CommonModel.TRADE_PARAMS)) {
                    return getMerchantPayParams(merchantSn, MapUtils.getMap(params, key), payway, String.valueOf(key));
                }
            }
        } catch (Throwable e) {
            log.error("getMerchantConfigParams:{},{},{}", merchantSn, payway, e);
        }
        return new HashMap<>();
    }

    /**
     * 解析商户不同通道交易参数
     * @param params
     * @param payway
     * @param tradeParamsKey
     * @return
     */
    private Map getMerchantPayParams(String merchantSn, Map params, int payway, String tradeParamsKey) {
        Map result = new HashMap();
        boolean formal = BeanUtil.getPropBoolean(params, TransactionParam.LIQUIDATION_NEXT_DAY, true);
        result.put(TRADE_TYPE, formal);
        result.put(MerchantProviderParams.STATUS, 1);
        result.put(MerchantProviderParams.CHANNEL_NO, PROVIDER_NAME_FORMAL);
        result.put(MerchantProviderParams.PAYWAY, payway);
        result.put(MerchantProviderParams.MERCHANT_SN, merchantSn);
        switch (tradeParamsKey) {
            case TransactionParam.LAKALA_WANMA_TRADE_PARAMS:
                result.put(MerchantProviderParams.CHANNEL_NO, BeanUtil.getPropString(params, TransactionParam.LAKALA_WNAMA_RECE_ORG_NO));
                result.putAll(getParamsByPayway(payway, params, result));
                result.put(MerchantProviderParams.PROVIDER, ProviderEnum.PROVIDER_LKLWANMA.getValue());
                return result;
            case TransactionParam.UNION_PAY_DIRECT_TRADE_PARAMS:
                result.put(MerchantProviderParams.CHANNEL_NO, BeanUtil.getPropString(params, TransactionParam.UNION_PAY_CHANNEL_ID));
                result.putAll(getParamsByPayway(payway, params, result));
                result.put(MerchantProviderParams.PROVIDER, ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue());
                return result;
            case TransactionParam.UNION_PAY_TRADE_PARAMS:
                result.put(MerchantProviderParams.CHANNEL_NO, BeanUtil.getPropString(params, TransactionParam.UNION_PAY_CHANNEL_ID));
                result.putAll(getParamsByPayway(payway, params, result));
                result.put(MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_UNIONPAY.getValue());
                return result;
            case TransactionParam.NUCC_TRADE_PARAMS:
                result.put(MerchantProviderParams.CHANNEL_NO, BeanUtil.getPropString(params, TransactionParam.NUCC_CHANNEL_ID));
                result.putAll(getParamsByPayway(payway, params, result));
                result.put(MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_UNIONPAY.getValue());
                return result;
            case TransactionParam.WEIXIN_WAP_TRADE_PARAMS:
                result.put(MerchantProviderParams.PAYWAY, PaywayEnum.WEIXIN.getValue());
                result.put(MerchantProviderParams.PROVIDER, PROVIDER_FORMAL);
                result.put(MerchantProviderParams.PARENT_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_MCH_ID)) ;
                result.put(MerchantProviderParams.PROVIDER_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_MCH_ID));
                result.put(MerchantProviderParams.PAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_MCH_ID));
                result.put(MerchantProviderParams.WEIXIN_SUB_APPID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_APP_ID));
                result.put(MerchantProviderParams.WEIXIN_SUB_MINI_APPID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_MINI_SUB_APP_ID));
                return result;
            case TransactionParam.ALIPAY_V1_TRADE_PARAMS:
                result.put(MerchantProviderParams.PAYWAY, PAYWAY_ALIPAY);
                result.put(MerchantProviderParams.PROVIDER, PROVIDER_FORMAL);
                result.put(MerchantProviderParams.PAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.PARTNER));
                return result;
            case TransactionParam.ALIPAY_V2_TRADE_PARAMS:
                result.put(MerchantProviderParams.PAYWAY, PaywayEnum.ALIPAY.getValue());
                result.put(MerchantProviderParams.PROVIDER, PROVIDER_FORMAL);
                result.put(MerchantProviderParams.PAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.ALIPAY_PID));
                return result;
            case TransactionParam.JD_TRADE_PARAMS:
                result.put(MerchantProviderParams.PAYWAY, PaywayEnum.JD_WALLET.getValue());
                result.put(MerchantProviderParams.PROVIDER, PROVIDER_FORMAL);
                result.put(MerchantProviderParams.PAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.JD_MERCHANT_NO));
                return result;
            case TransactionParam.BESTPAY_TRADE_PARAMS:
                result.put(MerchantProviderParams.PAYWAY, PaywayEnum.BESTPAY.getValue());
                result.put(MerchantProviderParams.PROVIDER, PROVIDER_FORMAL);
                result.put(MerchantProviderParams.PAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.BAIFUBAO_SP_NO));
                return result;
            case TransactionParam.QQ_TRADE_PARAMS:
                result.put(MerchantProviderParams.PAYWAY, PaywayEnum.QQ_WALLET.getValue());
                result.put(MerchantProviderParams.PROVIDER, PROVIDER_FORMAL);
                result.put(MerchantProviderParams.PAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.QQ_MERCHANT_ID));
                return result;
            case TransactionParam.CMCC_TRADE_PARAMS:
                result.put(MerchantProviderParams.PAYWAY, PaywayEnum.CMCC.getValue());
                result.put(MerchantProviderParams.PROVIDER, PROVIDER_FORMAL);
                result.put(MerchantProviderParams.PAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.CMCC_MERCHANT_ID));
                return result;
            default:
                return new HashMap<>();
        }
    }


    /**
     * 根据支付方式获取对应key值
     * @param payway
     * @param params
     * @param result
     * @return
     */
    private Map getParamsByPayway(int payway, Map params, Map result) {
        if (payway == PaywayEnum.ALIPAY.getValue()) {
            result.put(MerchantProviderParams.PAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.ALIPAY_PID));
        } else {
            result.put(MerchantProviderParams.PAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_MCH_ID));
            result.put(MerchantProviderParams.WEIXIN_SUB_APPID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_APP_ID));
            result.put(MerchantProviderParams.WEIXIN_SUB_MINI_APPID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_MINI_SUB_APP_ID));
        }
        return result;
    }


    /**
     * fine todo:更换为枚举
     * 通道映射
     */
    private Map getPayName = StringUtil.hashMap(0,PROVIDER_NAME_FORMAL,
            PROVIDER_CIBBANK, PROVIDER_NAME_CIBBANK,
            PROVIDER_LAKALA, PROVIDER_NAME_LAKALA,
            PROVIDER_CITICBANK, PROVIDER_NAME_CITICBANK,
            PROVIDER_CIBGZBANK, PROVIDER_NAME_CIBGZBANK,
            PROVIDER_LKLWANMA, PROVIDER_NAME_LKLWANMA,
            PROVIDER_NUCC, PROVIDER_NAME_NUCC,
            PROVIDER_UNIONPAY, PROVIDER_NAME_UNIONPAY,
            PROVIDER_DIRECT_UNIONPAY, PROVIDER_NAME_DIRECT_UNIONPAY,
            PAYWAY_ALIPAY, PAYWAY_ALIPAY_NAME,
            PAYWAY_ALIPAY2, PAYWAY_ALIPAY2_NAME,
            PAYWAY_WEIXIN, PAYWAY_WEIXIN_NAME,
            PAYWAY_BAIFUBAO, PAYWAY_BAIFUBAO_NAME,
            PAYWAY_JDWALLET, PAYWAY_JDWALLET_NAME,
            PAYWAY_QQWALLET, PAYWAY_QQWALLET_NAME,
            PAYWAY_APPLEPAY, PAYWAY_APPLEPAY_NAME,
            PAYWAY_LAKALAWALLET, PAYWAY_LAKALAWALLET_NAME,
            PAYWAY_LKL_UNIONPAY, PAYWAY_LKL_UNIONPAY_NAME,
            PAYWAY_BESTPAY, PAYWAY_BESTPAY_NAME,
            PAYWAY_WEIXIN_HK, PAYWAY_WEIXIN_HK_NAME,
            PAYWAY_CMCC, PAYWAY_CMCC_NAME);

/**************************************************************************************************/

    @Override
    public Map getPayTradeParams(String merchantSn, int payway) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        supportService.removeCachedParams(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        try {
            Map params = tradeConfigService.getTradeParams(payway, 2, StringUtil.hashMap(MerchantConfig.MERCHANT_ID, merchantId));
            if (MapUtils.isEmpty(params)) {
                return new HashMap<>();
            }
            for (Object key : params.keySet()) {
                if (String.valueOf(key).contains(CommonModel.TRADE_PARAMS)) {
                    boolean formal = BeanUtil.getPropBoolean(params.get(key), TransactionParam.LIQUIDATION_NEXT_DAY, true);
                    if (!formal) {
                        return getPayTradeParamsFormal(MapUtils.getMap(params, key), String.valueOf(key), payway);
                    }
                    return getPayTradeParamsNoFormal(MapUtils.getMap(params, key), String.valueOf(key), payway);
                }
            }
        } catch (Exception e) {
           log.error("getPayTradeParams:{},{},{}", merchantSn, payway, e);
        }
       return new HashMap<>();
    }

    @Override
    public List<ByPassTradeConfig> getByPassTradeConfig(String merchantId, List<Integer> payways) {
        List<ByPassTradeConfig> result = new ArrayList<>();
        if (WosaiCollectionUtils.isEmpty(payways)) {
            return result;
        }
        return backAcquirerBiz.buildByPassTradeConfig(merchantId, payways);
    }


    private Map getPayTradeParamsNoFormal(Map params, String key, int payway) {
        Map<String, Object> result = new HashMap<>();
        result.put(MerchantConfig.PAYWAY, payway);

        result.put(PayParamsModel.PAYWAY_NAME,  getPayName.get(payway));
        switch (key) {
            case TransactionParam.LAKALA_WANMA_TRADE_PARAMS:
                result.put(TRADE_TYPE, TRADE_TYPE_STATUS);
                result.put(MerchantProviderTradeParams.CHANNEL_NO, BeanUtil.getPropString(params, TransactionParam.LAKALA_WNAMA_RECE_ORG_NO));
                result.put(MerchantProviderTradeParams.PARENT_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_MCH_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_MCH_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_APP_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_APP_ID));
                result.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.ALIPAY_PID));
                result.put(MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_LKLWANMA.getValue());
                result.put(PayParamsModel.PROVIDER_NAME,  ProviderEnum.PROVIDER_LKLWANMA.getText());
                return result;
            case TransactionParam.UNION_PAY_DIRECT_TRADE_PARAMS:
                result.put(TRADE_TYPE, TRADE_TYPE_STATUS);
                result.put(MerchantProviderTradeParams.PARENT_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_MCH_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_MCH_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_APP_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_APP_ID));
                result.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.ALIPAY_PID));
                result.put(MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue());
                result.put(PayParamsModel.PROVIDER_NAME,  ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getText());
                return result;
            case TransactionParam.UNION_PAY_TL_TRADE_PARAMS:
                result.put(TRADE_TYPE, TRADE_TYPE_STATUS);
                result.put(MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_TONGLIAN.getValue());
                result.put(PayParamsModel.PROVIDER_NAME,  ProviderEnum.PROVIDER_TONGLIAN.getText());
                result.put(MerchantProviderTradeParams.PARENT_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.UNION_PAY_TL_PROVIDER_MCH_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.UNION_PAY_TL_WEIXIN_SUB_MCH_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_APP_ID, BeanUtil.getPropString(params, TransactionParam.UNION_PAY_TL_WEIXIN_SUB_APP_ID));
                result.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.UNION_PAY_TL_ALIPAY_SUB_MCH_ID));
                return result;
            case TransactionParam.UNION_PAY_TRADE_PARAMS:
                result.put(TRADE_TYPE, TRADE_TYPE_STATUS);
                result.put(MerchantProviderTradeParams.PARENT_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_MCH_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_MCH_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_APP_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_APP_ID));
                result.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.ALIPAY_PID));
                result.put(MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_UNIONPAY.getValue());
                result.put(PayParamsModel.PROVIDER_NAME,  ProviderEnum.PROVIDER_UNIONPAY.getText());
                return result;
            case TransactionParam.NUCC_TRADE_PARAMS:
                result.put(TRADE_TYPE, TRADE_TYPE_STATUS);
                result.put(MerchantProviderTradeParams.PARENT_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_MCH_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_MCH_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_APP_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_APP_ID));
                result.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.ALIPAY_PID));
                result.put(MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_UNIONPAY.getValue());
                result.put(PayParamsModel.PROVIDER_NAME,  ProviderEnum.PROVIDER_UNIONPAY.getText());
                return result;

            case TransactionParam.CITICBANK_TRADE_PARAMS:
                result.put(TRADE_TYPE, TRADE_TYPE_NOPAY);
                result.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.CITICBANK_MCH_ID));
                result.put(MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_CITICBANK.getValue());
                result.put(PayParamsModel.PROVIDER_NAME,  ProviderEnum.PROVIDER_CITICBANK.getText());
                return result;
            case TransactionParam.CIBBANK_TRADE_PARAMS:
                result.put(TRADE_TYPE, TRADE_TYPE_NOPAY);
                result.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.CIBBANK_MCH_ID));
                result.put(MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_CIBBANK.getValue());
                result.put(PayParamsModel.PROVIDER_NAME,  ProviderEnum.PROVIDER_CIBBANK.getText());
                return result;

            case PayParamsModel.CIBGZBANK_TRADE_PARAMS:
                result.put(TRADE_TYPE, TRADE_TYPE_NOPAY);
                result.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.SWIFTPASS_MCH_ID));
                result.put(MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_CIBGZBANK.getValue());
                result.put(PayParamsModel.PROVIDER_NAME,  ProviderEnum.PROVIDER_CIBGZBANK.getText());
                return result;
            default:
                 return new HashMap<>();
        }
    }


    private Map<String, Object> getPayTradeParamsFormal(Map params, String key, int payway) {
        Map<String, Object> result = new HashMap<>();
        result.put(TRADE_TYPE, TRADE_TYPE_STATUS);
        result.put(MerchantConfig.PROVIDER, PROVIDER_FORMAL);
        result.put(PayParamsModel.PROVIDER_NAME, PROVIDER_NAME_FORMAL);
        result.put(MerchantConfig.PAYWAY, payway);
        result.put(PayParamsModel.PAYWAY_NAME,  getPayName.get(payway));
        switch (key) {
            case TransactionParam.WEIXIN_TRADE_PARAMS:
                result.put(MerchantProviderTradeParams.PARENT_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_MCH_ID)) ;
                result.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_MCH_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_APP_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_APP_ID));
                result.put(MerchantProviderTradeParams.WEIXIN_MINI_APP_ID, BeanUtil.getPropString(params, TransactionParam.WEIXIN_MINI_SUB_APP_ID));
                return result;
            case TransactionParam.ALIPAY_V1_TRADE_PARAMS:
                result.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.PARTNER));
                return result;
            case TransactionParam.ALIPAY_V2_TRADE_PARAMS:
                result.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.ALIPAY_PID));
                return result;
            case TransactionParam.JD_TRADE_PARAMS:
                result.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.JD_MERCHANT_NO));
                return result;
            case TransactionParam.BESTPAY_TRADE_PARAMS:
                result.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.BAIFUBAO_SP_NO));
                return result;
            case TransactionParam.QQ_TRADE_PARAMS:
                result.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.QQ_MERCHANT_ID));
                return result;
            case TransactionParam.CMCC_TRADE_PARAMS:
                result.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, BeanUtil.getPropString(params, TransactionParam.CMCC_MERCHANT_ID));
                return result;

            default:
                return new HashMap<>();
        }
    }

}
