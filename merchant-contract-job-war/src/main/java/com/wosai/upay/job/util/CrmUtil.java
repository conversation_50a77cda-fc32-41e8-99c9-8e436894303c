package com.wosai.upay.job.util;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: jerry
 * @date: 2019/5/22 10:20
 * @Description:类名如义 crm要能都更新 产品业务又有多种更新类型 单独处理这个矛盾
 */
public class CrmUtil {

    public static final List<String> MERCHANTS = Arrays.
            asList("name", "business_name", "industry", "province", "city", "country", "street_address", "customer_phone", "legal_person_name", "legal_person_id_number");


    public static final List<String> MERCHANT_BANK_ACCOUNT = Arrays.
            asList("id_validity", "type", "holder", "number", "branch_name", "opening_number", "clearing_number", "identity");

}
