package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.PersistenceException;
import com.wosai.databus.LogEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.pay.trade.state.TradeStateChangeEvent;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.model.acquirer.SyncMchStatusResp;
import com.wosai.upay.job.service.AcquirerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.collections.MapUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Map;


/**
 * @Description:  监听消息打开商户在收单机构状态
 * <AUTHOR>
 * @Date: 2021/10/19 2:33 下午
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class MerchantStatusSyncConsumer extends AbstractDataBusConsumer {
    public static final String TRADE_STATE_ALLIN = "databus.event.pay.trade.state.allin";
    @Autowired
    AcquirerService acquirerService;
    @Autowired
    MerchantService merchantService;



    @KafkaListener(topics = {TRADE_STATE_ALLIN}, containerFactory = "dataBusKafkaListenerContainerFactory")
    @Transactional(rollbackFor = Exception.class)
    public void consume(ConsumerRecord<String, GenericRecord> record) {
        GenericRecord datum = record.value();
        if (datum == null) {
            log.error("{} getValue null", record);
            return;
        }
        AbstractEvent event;
        try {
            ByteBuffer buffer = (ByteBuffer) datum.get(LogEntry.EVENT);
            event = fromJsonBytes(buffer.array(), TradeStateChangeEvent.class);
            event.setSeq((Long) datum.get(LogEntry.SEQ));
            event.setTimestamp((Long) datum.get(LogEntry.TIMESTAMP));
            doHandleEvent(event);
        } catch (Exception e) {
            log.error("{} dataBus consume error", record, e);
            throw e;
        }
    }

    @Override
    protected void doHandleEvent(AbstractEvent event) {
        log.info("start handling  event : {}", JSONObject.toJSONString(event));
        if (event instanceof TradeStateChangeEvent) {
            TradeStateChangeEvent tradeStateChangeEvent = (TradeStateChangeEvent) event;
            final String merchantId = tradeStateChangeEvent.getMerchantId();
            Map merchant = merchantService.getMerchantByMerchantId(merchantId);
            if (MapUtils.isEmpty(merchant)) {
                return;
            }
            String merchantSn = MapUtils.getString(merchant, Merchant.SN);
            final Boolean change = tradeStateChangeEvent.getStateAfterChange();
            if(!change) {
                return;
            }
            final SyncMchStatusResp statusResp = acquirerService.syncMchStatusToAcquirer(merchantSn, 1);
            log.info("监听消息处理状态结果商户号:{},{}",merchantSn,JSON.toJSONString(statusResp));
        }
    }


    private final static ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static <T> T fromJsonBytes(byte[] bytes, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(bytes, clazz);
        } catch (IOException e) {
            throw new PersistenceException(
                    String.format("unable to read %s from json bytes.", clazz.getName()), e
            );
        }
    }
}