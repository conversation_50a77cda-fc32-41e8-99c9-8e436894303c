package com.wosai.upay.job.xxljob.threadpool;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.wosai.upay.job.xxljob.JobThreadPoolExecutorRegistry;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/3/14
 */
@Slf4j
public class JobThreadPoolExecutorMonitor {

    private static final ScheduledThreadPoolExecutor MONITOR_SCHEDULE_THREAD_POOL_EXECUTOR = new ScheduledThreadPoolExecutor(1);

    public static void startMonitor() {
        log.info("启动定时任务线程池监控任务");
        MONITOR_SCHEDULE_THREAD_POOL_EXECUTOR.scheduleAtFixedRate(JobThreadPoolExecutorMonitor::monitor, 0, 60, TimeUnit.SECONDS);
    }

    public static void stopMonitor() {
        log.info("停止定时任务线程池监控任务");
        MONITOR_SCHEDULE_THREAD_POOL_EXECUTOR.shutdownNow();
    }

    public static void monitor() {
        try {
            List<ThreadPoolMetric> metrics = new ArrayList<>();
            JobThreadPoolExecutorRegistry.getAllExecutors().forEach((poolName, executor) -> {
                ThreadPoolMetric metric = new ThreadPoolMetric();
                metric.setPoolName(poolName);
                metric.setPoolSize(executor.getPoolSize());
                metric.setMaximumPoolSize(executor.getMaximumPoolSize());
                metric.setActiveCount(executor.getActiveCount());
                metric.setQueueSize(executor.getQueue().size());
                metrics.add(metric);
            });
            log.info(JSON.toJSONString(metrics));
        } catch (Exception e) {
            log.error("线程池监控任务发生异常", e);
        }
    }

    @Data
    private static class ThreadPoolMetric {
        @JSONField(ordinal = 1, name = "线程池名称")
        private String poolName;
        @JSONField(ordinal = 2, name = "当前线程数")
        private int poolSize;
        @JSONField(ordinal = 3, name = "最大线程数")
        private int maximumPoolSize;
        @JSONField(ordinal = 4, name = "活跃线程数")
        private int activeCount;
        @JSONField(ordinal = 5, name = "队列大小")
        private int queueSize;
    }
}
