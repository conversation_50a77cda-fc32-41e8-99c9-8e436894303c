package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.upay.job.biz.keepalive.KeepAliveConfigBiz;
import com.wosai.upay.job.biz.keepalive.KeepAliveTaskBiz;
import com.wosai.upay.job.biz.keepalive.validation.engine.KeepAliveValidateEngine;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidationResult;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.keepalive.KeepAliveConfigRequest;
import com.wosai.upay.job.model.keepalive.KeepAliveConfigResult;
import com.wosai.upay.job.model.keepalive.KeepAliveTaskInfo;
import com.wosai.upay.job.model.keepalive.KeepAliveTaskStatusUpdateRequest;
import com.wosai.upay.job.model.keepalive.KeepAliveTasksQueryRequest;
import com.wosai.upay.job.model.keepalive.KeepAliveValidateRequest;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Valid;

/**
 * 商户保活服务接口实现
 *
 * <AUTHOR>
 * @date 2025/8/14
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class MerchantKeepAliveServiceImpl implements MerchantKeepAliveService {

    @Autowired
    private KeepAliveConfigBiz keepAliveConfigBiz;

    @Autowired
    private KeepAliveTaskBiz keepAliveTaskBiz;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public CuaCommonResultDTO enableKeepAliveConfig(KeepAliveConfigRequest request, LogParamsDto logParamsDto) {
        RLock lock = redissonClient.getLock("keepAliveOperate:" + request.getMerchantSn());
        try {
            if (!lock.tryLock()) {
                throw new ContractBizException("正在处理中，请稍后再试");
            }
            keepAliveConfigBiz.enableKeepAliveConfig(request, logParamsDto);
            return CuaCommonResultDTO.success();
        } catch (ContractBizException e) {
            log.warn("加入保活计划业务异常：{}", JSON.toJSONString(request), e);
            return CuaCommonResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("加入保活计划系统异常", e);
            return CuaCommonResultDTO.fail("系统异常");
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public CuaCommonResultDTO disableKeepAliveConfig(KeepAliveConfigRequest request, LogParamsDto logParamsDto) {
        RLock lock = redissonClient.getLock("keepAliveOperate:" + request.getMerchantSn());
        try {
            if (!lock.tryLock()) {
                throw new ContractBizException("正在处理中，请稍后再试");
            }
            keepAliveConfigBiz.disableKeepAliveConfig(request, logParamsDto);
            return CuaCommonResultDTO.success();
        } catch (ContractBizException e) {
            log.warn("退出保活计划业务异常：{}", JSON.toJSONString(request), e);
            return CuaCommonResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("退出保活计划系统异常", e);
            return CuaCommonResultDTO.fail("系统异常");
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public KeepAliveConfigResult queryKeepAliveConfig(String merchantSn) {
        return keepAliveConfigBiz.queryKeepAliveConfig(merchantSn);
    }

    @Override
    public ListResult<KeepAliveTaskInfo> queryKeepAliveTasks(KeepAliveTasksQueryRequest keepAliveTasksQueryRequest) {
        return keepAliveTaskBiz.queryKeepAliveTasks(keepAliveTasksQueryRequest);
    }

    @Override
    public CuaCommonResultDTO updateKeepAliveTaskStatus(@Valid KeepAliveTaskStatusUpdateRequest request) {
        try {
            keepAliveTaskBiz.closeKeepAliveTaskFromAPI(request);
            return CuaCommonResultDTO.success();
        } catch (ContractBizException e) {
            log.warn("更新保活任务状态业务异常：{}", JSON.toJSONString(request), e);
            return CuaCommonResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("更新保活任务状态系统异常: {}", JSON.toJSONString(request), e);
            return CuaCommonResultDTO.fail("系统异常");
        }
    }

    @Override
    public CuaCommonResultDTO validateKeepAlive(KeepAliveValidateRequest request) {
        try {
            KeepAliveValidationResult keepAliveValidationResult = keepAliveConfigBiz.validateKeepAlive(request);
            if (!keepAliveValidationResult.isSuccess()) {
                return CuaCommonResultDTO.fail(keepAliveValidationResult.getFailureReason());
            }
            return CuaCommonResultDTO.success();
        } catch (ContractBizException e) {
            log.warn("更新保活任务状态业务异常：{}", JSON.toJSONString(request), e);
            return CuaCommonResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("更新保活任务状态系统异常: {}", JSON.toJSONString(request), e);
            return CuaCommonResultDTO.fail("系统异常");
        }
    }
}
