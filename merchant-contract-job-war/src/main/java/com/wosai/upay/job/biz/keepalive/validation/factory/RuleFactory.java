package com.wosai.upay.job.biz.keepalive.validation.factory;

import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.rule.KeepAliveCheckRule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 规则工厂类 - 优化版本
 */
@Component
public class RuleFactory {

    private static final Logger logger = LoggerFactory.getLogger(RuleFactory.class);

    @Autowired
    private List<KeepAliveCheckRule> keepAliveCheckRuleList;

    /**
     * 规则实例缓存 - 启动时初始化，避免运行时查找
     */
    private final Map<KeepAliveCheckRuleTypeEnum, KeepAliveCheckRule> ruleCache = new ConcurrentHashMap<>();

    /**
     * 初始化规则缓存
     */
    @PostConstruct
    public void initRuleCache() {
        logger.info("开始初始化规则缓存，规则数量: {}", keepAliveCheckRuleList.size());

        for (KeepAliveCheckRule rule : keepAliveCheckRuleList) {
            KeepAliveCheckRuleTypeEnum ruleType = rule.getRuleType();
            if (ruleType != null) {
                ruleCache.put(ruleType, rule);
                logger.debug("缓存规则: {}", ruleType);
            } else {
                logger.warn("发现规则类型为空的规则实例: {}", rule.getClass().getName());
            }
        }

        logger.info("规则缓存初始化完成，缓存规则数量: {}", ruleCache.size());
    }

    /**
     * 获取规则实例 - 优化版本
     */
    public KeepAliveCheckRule getRule(KeepAliveCheckRuleConfig keepAliveCheckRuleConfig) {
        if (keepAliveCheckRuleConfig == null || keepAliveCheckRuleConfig.getRuleType() == null) {
            logger.warn("规则配置或规则类型为空");
            return null;
        }

        KeepAliveCheckRuleTypeEnum ruleTypeEnum = KeepAliveCheckRuleTypeEnum.fromCode(keepAliveCheckRuleConfig.getRuleType());
        if (ruleTypeEnum == null) {
            logger.warn("未知的规则类型: {}", keepAliveCheckRuleConfig.getRuleType());
            return null;
        }

        KeepAliveCheckRule rule = ruleCache.get(ruleTypeEnum);
        if (rule == null) {
            logger.error("未找到规则实现: {}", ruleTypeEnum);
        }

        return rule;
    }
}