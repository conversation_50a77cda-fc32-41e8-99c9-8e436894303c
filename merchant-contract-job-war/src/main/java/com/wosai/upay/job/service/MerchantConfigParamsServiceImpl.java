package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * Created by l<PERSON><PERSON><PERSON> on 2018/9/7.
 */
@Service
@AutoJsonRpcServiceImpl
public class MerchantConfigParamsServiceImpl implements MerchantConfigParamsService {

    @Override
    public boolean updateMerchantConfigProviderCommon(Map params) {
        throw new RuntimeException("接口已废弃");
    }

    @Override
    public String authorizationCodeUrl(Map info, String codeName) throws Exception {
        throw new RuntimeException("接口已废弃");
    }

}
