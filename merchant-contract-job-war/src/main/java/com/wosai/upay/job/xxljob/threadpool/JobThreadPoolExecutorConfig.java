package com.wosai.upay.job.xxljob.threadpool;

import cn.hutool.core.thread.NamedThreadFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2025/3/14
 */
@Configuration
public class JobThreadPoolExecutorConfig {

    @Bean(name = "batchJobHandlerThreadPool")
    public JobThreadPoolExecutor batchJobHandlerThreadPool() {
        return new JobThreadPoolExecutor(
                "batchJobHandlerThreadPool",
                30,
                15,
                30,
                60,
                TimeUnit.SECONDS,
                new SynchronousQueue<>(),
                new NamedThreadFactory("batchJobHandlerThreadPool", false),
                new ThreadPoolExecutor.AbortPolicy());
    }

    @Bean(name = "directJobHandlerThreadPool")
    public JobThreadPoolExecutor directJobHandlerThreadPool() {
        return new JobThreadPoolExecutor(
                "directJobHandlerThreadPool",
                30,
                15,
                30,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                new NamedThreadFactory("directJobHandlerThreadPool", false),
                new ThreadPoolExecutor.AbortPolicy());
    }

}
