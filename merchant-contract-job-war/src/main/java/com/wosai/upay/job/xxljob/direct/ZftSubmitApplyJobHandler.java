package com.wosai.upay.job.xxljob.direct;

import com.wosai.upay.job.Constants.ZftMerchantConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.ZftBiz;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.model.payLater.ZftMerchantApply;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * xxl_job_desc: 创建直付通商户
 * @Description:支付宝直付通商户创建
 * <AUTHOR>
 * @Date 2023/8/1 下午2:14
 */
@Component("ZftSubmitApplyJobHandler")
@Slf4j
public class ZftSubmitApplyJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ZftBiz zftBiz;

    @Override
    public String getLockKey() {
        return "submitZftApply";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            int limit = Optional.of(param.getBatchSize()).orElse(ScheduleUtil.DEFAULT_QUERY_LIMIT);
            long queryTime = Optional.of(param.getQueryTime()).orElse(ScheduleUtil.DEFAULT_FIVE_DAYS_MILLIS_QUERY * 6);
            List<ZftMerchantApply> zftTasks = zftBiz.getZftTasks(Collections.singletonList(ZftMerchantConstant.Status.PENDING), limit, queryTime);
            if (CollectionUtils.isEmpty(zftTasks)) {
                return;
            }
            //查询0-已提交任务的等级
            zftTasks.forEach(zftMerchantApply -> {
                try {
                    if (ShutdownSignal.isShuttingDown()) {
                        return;
                    }
                    zftBiz.handleSubmitApply(zftMerchantApply);
                } catch (Exception e) {
                    log.error(" submitZftApply 商户号:{} 异常", zftMerchantApply.getMerchant_sn(), e);
                    ZftMerchantApply newApply = new ZftMerchantApply();
                    newApply.setStatus(ZftMerchantConstant.Status.FAIL).setResult("提交直付通商户异常").setId(zftMerchantApply.getId());
                    zftBiz.updateZftTask(newApply);
                }
            });
        } catch (Exception e) {
            log.error("submitZftApply", e);
        }
    }
}
