package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.workflow.bean.CallBackBean;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.Constants.ApproveConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.mapper.McAcquirerChangeMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.*;
import com.wosai.upay.job.model.changeAcquirerApprove.AcquirerApprove;
import com.wosai.upay.job.model.changeAcquirerApprove.AcquirerApproveExcel;
import com.wosai.upay.job.model.changeAcquirerApprove.ChangeAcquirerApproveDTO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * xxl_job_desc: BatchTask-切换收单机构结果
 * 处理 type=4、5 切换收单机构任务的执行结果
 *
 * <AUTHOR>
 * @date 2025/4/17
 */
@Slf4j
@Component("AcquirerChangeResultJobHandler")
public class AcquirerChangeResultJobHandler extends AbstractMcBatchTaskJobHandler {

    @Autowired
    private McAcquirerChangeMapper acquirerChangeMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    /**
     * 设置缓存,写入3小时后自动删除
     */
    Cache<String, List<AcquirerApprove>> changeCache = CacheBuilder.newBuilder()
            .maximumSize(16384)
            .expireAfterWrite(3, TimeUnit.HOURS)
            .build();


    @Override
    public String getLockKey() {
        return "AcquirerChangeResultJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        McBatchTaskExample mcBatchTaskExample = new McBatchTaskExample();
        mcBatchTaskExample.or().andStatusEqualTo(1).andEffect_timeLessThanOrEqualTo(new Date()).andTypeIn(Lists.newArrayList(4, 5));
        List<McBatchTask> list = mcBatchTaskMapper.selectByExampleWithBLOBs(mcBatchTaskExample);
        list.forEach(mcBatchTask -> {
            Map extra = Maps.newHashMap();
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                final String payload = mcBatchTask.getPayload();
                extra = CommonUtil.string2Map(payload);
                final Integer type = mcBatchTask.getType();
                //批量操作
                if (Objects.equals(type, 5)) {
                    //批量文件
                    final String lastAttachmentUrl = BeanUtil.getPropString(extra, ApproveConstant.LAST_ATTACHMENT_URL);
                    final List<AcquirerApprove> approveList = changeCache.get(lastAttachmentUrl, () -> excelUtil.getExcelInfoList(lastAttachmentUrl, new AcquirerApprove()));
                    if (CollectionUtils.isEmpty(approveList)) {
                        return;
                    }
                    final List<AcquirerApproveExcel> acquirerApproveExcels = approveList.parallelStream()
                            .map(acquirerApprove -> getAcquirerApproveExcel(acquirerApprove))
                            .collect(Collectors.toList());
                    //判断是否全部处理完成
                    final boolean match = acquirerApproveExcels.stream().anyMatch(x -> StringUtils.isEmpty(x.getResult()));
                    //没有处理完成等到下次处理
                    if (match) {
                        return;
                    }
                    //处理完的上传并返回
                    final String url = excelUtil.uploadToOss(acquirerApproveExcels, BASE_DIR);
                    extra.put(ApproveConstant.LAST_ATTACHMENT_URL, url);
                    mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setId(mcBatchTask.getId()).setPayload(JSONObject.toJSONString(extra)).setStatus(2).setResult("处理结束"));
                    //删除临时批量文件
                    excelUtil.deleteTempExcel(lastAttachmentUrl, BASE_DIR);
                    callBack(extra, "处理结果请使用浏览器下载链接对应的Excel:" + url, AUDIT_EXECUTE_SUCCESS);
                } else {
                    //处理单个商户
                    final Integer applyId = MapUtils.getInteger(extra, ApproveConstant.APPLYID);

                    if (Objects.isNull(applyId)) {
                        callBack(extra, "商户已提交新收单机构进件，请自行关注业务开通结果", AUDIT_EXECUTE_SUCCESS);
                        mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(2).setId(mcBatchTask.getId()).setResult("处理结束"));
                    } else {
                        final McAcquirerChange acquirerChange = acquirerChangeMapper.selectByPrimaryKey(applyId);
                        if (Objects.isNull(acquirerChange)) {
                            throw new CommonPubBizException("切换收单机构任务已被取消");
                        }

                        final Integer status = acquirerChange.getStatus();
                        //申请是否结束
                        if (!isFinish(status)) {
                            if (AcquirerTypeEnum.TONG_LIAN_V2.getValue().equals(acquirerChange.getTarget_acquirer())) {
                                addCommentSignUrl(acquirerChange, extra);
                            }
                            return;
                        }
                        String changeMemo = getResult(acquirerChange, status);
                        callBack(extra, changeMemo, Objects.equals(status, 19) ? AUDIT_EXECUTE_SUCCESS : AUDIT_EXECUTE_FAIL);
                        mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(2).setId(mcBatchTask.getId()).setResult("处理结束"));
                    }
                }
            } catch (Exception e) {
                log.error("acquirerApproveChange error mc_batch_task {}  exception", mcBatchTask.getId(), e);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(3).setId(mcBatchTask.getId()).setResult(ExceptionUtil.getThrowableMsg(e)));
                callBack(extra, ExceptionUtil.getThrowableMsg(e), AUDIT_EXECUTE_FAIL);
            }
        });

    }

    private AcquirerApproveExcel getAcquirerApproveExcel(AcquirerApprove acquirerApprove) {
        final AcquirerApproveExcel acquirerApproveExcel = new AcquirerApproveExcel();
        BeanUtils.copyProperties(acquirerApprove, acquirerApproveExcel);
        final String changeId = acquirerApprove.getApplyId();
        //校验失败的不处理
        if (Objects.isNull(changeId)) {
            if (WosaiStringUtils.isEmpty(acquirerApproveExcel.getResult())) {
                acquirerApproveExcel.setResult("商户已提交新收单机构进件，请自行关注业务开通结果");
            }
            return acquirerApproveExcel;
        }
        //已经处理的不需要处理
        if (!StringUtils.isEmpty(acquirerApproveExcel.getResult())) {
            return acquirerApproveExcel;
        }
        //处理未处理的
        final McAcquirerChange acquirerChange = acquirerChangeMapper.selectByPrimaryKey(Integer.valueOf(changeId));
        final Integer status = acquirerChange.getStatus();
        //申请是否结束
        if (!isFinish(status)) {
            return acquirerApproveExcel;
        }
        String changeMemo = getResult(acquirerChange, status);
        acquirerApproveExcel.setResult(changeMemo);
        return acquirerApproveExcel;
    }

    private void addCommentSignUrl(McAcquirerChange acquirerChange, Map extra) {
        MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
        dto.createCriteria()
                .andMerchant_snEqualTo(acquirerChange.getMerchant_sn())
                .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
        List<MerchantProviderParams> paramsList = merchantProviderParamsMapper.selectByExample(dto);

        if (!CollectionUtils.isEmpty(paramsList)) {
            MerchantProviderParams params = paramsList.get(0);
            Map paramsExtra = CommonUtil.bytes2Map(params.getExtra());
            String signUrl = BeanUtil.getPropString(paramsExtra, "signUrl");
            if (!StringUtil.empty(signUrl)) {
                final ObjectMapper mapper = new ObjectMapper();
                final ChangeAcquirerApproveDTO approvedto = mapper.convertValue(MapUtils.getMap(extra, ApproveConstant.APPROVE_INFO), ChangeAcquirerApproveDTO.class);
                final CallBackBean backBean = approvedto.getCallBackBean();
                backBean.setMessage("签约链接:" + signUrl);
                callBackService.addComment(backBean);
                paramsExtra.put("signUrlBak", signUrl);
                paramsExtra.remove("signUrl");
                merchantProviderParamsMapper.updateByPrimaryKeySelective(new MerchantProviderParams().setExtra(CommonUtil.map2Bytes(paramsExtra)).setId(params.getId()));
            }
        }
    }

    /**
     * 获取切换收单机构申请处理结果
     *
     * @param acquirerChange
     * @param status
     * @return
     */
    private String getResult(McAcquirerChange acquirerChange, Integer status) {
        String changeMemo = acquirerChange.getMemo();
        if (StringUtils.isEmpty(changeMemo) && Objects.equals(status, AcquirerChangeStatus.SUCCESS)) {
            changeMemo = "切换成功";
        }
        if (StringUtils.isEmpty(changeMemo) && Objects.equals(status, AcquirerChangeStatus.FAIL)) {
            changeMemo = "切换失败";
        }
        return changeMemo;
    }

    private Boolean isFinish(Integer status) {
        if (status == AcquirerChangeStatus.SUCCESS ||
                status == AcquirerChangeStatus.FAIL) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
