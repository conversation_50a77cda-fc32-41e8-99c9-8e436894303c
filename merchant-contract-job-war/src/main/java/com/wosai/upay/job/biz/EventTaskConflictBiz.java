package com.wosai.upay.job.biz;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.wosai.upay.job.util.ProviderUtil.CONTRACT_TYPE_INSERT;

/**
 * 检测 event 和 task 是否冲突
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Component
@Slf4j
public class EventTaskConflictBiz {

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    /**
     * 是否与执行中的任务冲突
     *
     * @param event
     * @return true：存在   false：不存在
     */
    public boolean conflictWithPendingTasks(ContractEvent event) {
        // 费率变更任务不与其他任务冲突
        if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type()) {
            return false;
        }
        //1.查询出商户目前状态为处理中的任务数量 若>0 则不进行处理
        String merchantSn = event.getMerchant_sn();
        List<ContractTask> pendingTasks = contractTaskMapper.selectTaskTodoByMerchantSn(merchantSn);
        if (WosaiCollectionUtils.isEmpty(pendingTasks)) {
            return false;
        }

        for (ContractTask contractTask : pendingTasks) {
            if (conflict(event, contractTask)) {
                log.info("merchant {} have contract-tasks skip, taskId: {}", merchantSn, contractTask.getId());
                return true;
            }
        }
        return false;
    }

    /**
     * 检查event和task是否冲突
     *
     * @param event
     * @param task
     * @return true 表示冲突
     */
    private boolean conflict(ContractEvent event, ContractTask task) {
        if (Objects.equals(task.getType(), CONTRACT_TYPE_INSERT)) {
            try {
                String ruleGroupId = task.getRule_group_id();
                RuleGroup ruleGroup = ruleContext.getRuleGroup(ruleGroupId.replace("change2", ""));
                McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(ruleGroup.getAcquirer());
                if (mcAcquirerDO.getType() == 1) {
                    return true;
                }
            } catch (Exception e) {
                log.warn("三方新增商户入网冲突检测异常 {} {} {}", event.getId(), task.getId(), task.getMerchant_sn(), e);
            }
        }

        // 不同收单机构的任务
        if (!Objects.equals(event.getRule_group_id(), task.getRule_group_id())) {
            return false;
        }
        return true;
    }
}
