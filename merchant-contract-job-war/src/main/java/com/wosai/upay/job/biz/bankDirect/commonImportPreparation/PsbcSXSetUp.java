package com.wosai.upay.job.biz.bankDirect.commonImportPreparation;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Joiner;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.ProviderTerminalIdBiz;
import com.wosai.upay.job.model.ProviderTerminal;
import com.wosai.upay.job.refactor.dao.ProviderTerminalDAO;
import com.wosai.upay.job.refactor.model.entity.ProviderTerminalDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.service.PsbcsxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Description: 邮储山西银行终端号报备
 * <AUTHOR>
 * @Date 2025/5/27 19:56
 */
@Slf4j
@Component
public class PsbcSXSetUp  implements ImportPreSetup {

    @Autowired
    private PsbcsxService psbcsxService;
    
    
    @Autowired
    private ProviderTerminalIdBiz providerTerminalIdBiz;

    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;


    @Autowired
    private ProviderTerminalDAO providerTerminalDAO;


    @Override
    public boolean needSetup(String provider) {
        return Objects.equals(provider, String.valueOf(ProviderEnum.PROVIDER_PSBCSX.getValue()));
    }

    @Override
    public Map setup(List<String> param) {
        //商户号
        final String merchantSn = param.get(0).trim();
        //收单机构商户号
        final String providerSn = param.get(2).trim();
        //8位终端号
        final String devicedId = providerTerminalIdBiz.getProviderTerminalIdByMerchantSn();
        try {
            psbcsxService.registerTerminal(merchantSn,devicedId,providerSn);
        } catch (Exception e) {
           throw new ContractBizException(StringUtils.defaultIfEmpty(e.getMessage(),"终端号报备失败"));
        }
        //删除原有终端数据
        providerTerminalBiz.deleteExistTerminal(merchantSn,ProviderEnum.PROVIDER_PSBCSX.getValue());
        providerTerminalBiz.merchantConnectionProviderTerminal(merchantSn,
                devicedId,
                providerSn,
                ProviderEnum.PROVIDER_PSBCSX.getValue()
                );
        //将支付宝和微信子商户号写入到bound_sub_mch_ids中
        final String wxNo = param.get(3).trim();
        final String aliNo = param.get(4).trim();
        final LambdaQueryWrapper<ProviderTerminalDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProviderTerminalDO::getMerchantSn, merchantSn)
        .eq(ProviderTerminalDO::getAcquirerMerchantId, providerSn)
        .eq(ProviderTerminalDO::getMerchantSn, merchantSn)
        .eq(ProviderTerminalDO::getProvider, ProviderEnum.PROVIDER_PSBCSX.getValue())
                .eq(ProviderTerminalDO::getProviderTerminalId,devicedId);
        final Optional<ProviderTerminalDO> providerTerminalDO = providerTerminalDAO.selectOne(wrapper);
        if(providerTerminalDO.isPresent()) {
            final ProviderTerminalDO updateProviderTerminalDO = new ProviderTerminalDO();
            final ProviderTerminalDO terminalDO = providerTerminalDO.get();
            updateProviderTerminalDO.setId(terminalDO.getId());
            updateProviderTerminalDO.setBoundSubMchIds(Joiner.on(",").join(wxNo,aliNo));
            providerTerminalDAO.updateByPrimaryKeySelective(updateProviderTerminalDO);

            final ProviderTerminal providerTerminal = new ProviderTerminal();
            providerTerminal.setProvider(ProviderEnum.PROVIDER_PSBCSX.getValue());
            providerTerminal.setMerchant_sn(merchantSn);
            providerTerminal.setProvider_terminal_id(devicedId);
            providerTerminal.setAcquirer_merchant_id(providerSn);
            providerTerminalBiz.insertSuccessTerminalBindResult(providerTerminal,wxNo ,
                    PaywayEnum.WEIXIN.getValue(), null);
            providerTerminalBiz.insertSuccessTerminalBindResult(providerTerminal,aliNo ,
                    PaywayEnum.ALIPAY.getValue(), null);
            return Collections.singletonMap("terminalId",devicedId);
        }
        return Collections.singletonMap("terminalId",devicedId);
    }
}
