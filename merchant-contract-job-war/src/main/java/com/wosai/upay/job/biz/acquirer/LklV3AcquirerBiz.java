package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.DeleteStatusEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.cua.utils.object.ConvertUtil;
import com.shouqianba.cua.utils.object.ObjectExtensionUtils;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.LklV3ShopTermBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.LklV3Term;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.acquirer.SyncMchStatusResp;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.dao.*;
import com.wosai.upay.job.refactor.model.bo.LklOpenUnionPayTradeParamBO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.model.enums.ConfigStatusEnum;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.lklV3.MerAccountResp;
import com.wosai.upay.merchant.contract.model.lklV3.MerchantDetailResp;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.zookeeper.Op;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/5/21 4:26 下午
 **/
@Component("lklV3-biz")
@Slf4j
public class LklV3AcquirerBiz extends LklAcquirerBiz {

    @Autowired
    private LklV3Service lklV3Service;

    @Autowired
    private LklV3ShopTermBiz lklV3ShopTermBiz;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Value("${lkl.v3.channelno}")
    private String lklChannelNo;
    @Value("${lkl_open.contract_rule}")
    private String lklOpenContractRule;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private McChannelDAO mcChannelDAO;

    @Autowired
    private MerchantProviderParamsExtDAO merchantProviderParamsExtDAO;

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private MerchantService merchantService;

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_LKLORG;
    }

    @Override
    public String getNormalWxRule() {
        return ContractRuleConstants.LKL_ORG_NORMAL_WEIXIN_RULE;
    }

    /**
     * 微信高校食堂 获取商户 渠道号
     *
     * @return
     */
    @Override
    public String getSchoolCanteenChannelNo(String merchantSn) {
        // 如果有在拉卡拉渠道下入的高校食堂，就返回这个渠道号
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andChannel_noEqualTo("217935905")
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExample(example);
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            return "217935905";
        }
        return super.getSchoolCanteenChannelNo(merchantSn);
    }

    @Override
    public SyncMchStatusResp syncMchStatusToAcquirer(String merchantSn, int status) {
        final LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
        //v3允许重复关闭
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        ContractResponse response = lklV3Service.updateMerchantStatusToLkl(acquirerParams.getPay_merchant_id(), status, lklV3Param);
        if (response.isSuccess() && ValidStatusEnum.VALID.getValue() == status) {
            List<LklV3Term> unbindLklV3Terms = lklV3ShopTermBiz.getUnbindLklV3Term(merchantSn);
            if (!StringUtil.listEmpty(unbindLklV3Terms)) {
                unbindLklV3Terms.forEach(term -> {
                    final String termNo = term.getTermNo();
                    if(StringUtils.isNotBlank(termNo)) {
                        lklV3Service.updateTermStatusToLkl(
                                merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue()).getPay_merchant_id()
                                , termNo, ValidStatusEnum.INVALID.getValue(), lklV3Param);
                    }

                });
            }
        }
        if (response.isSuccess()) {
            // 更新子商户号状态
            MerchantProviderParams update = new MerchantProviderParams().setId(acquirerParams.getId()).setMerchant_state(status == ValidStatusEnum.VALID.getValue() ? ValidStatusEnum.VALID.getValue() : ValidStatusEnum.INVALID.getValue()).setVersion(acquirerParams.getVersion() + 1).setMtime(System.currentTimeMillis());
            merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
            return new SyncMchStatusResp().setSuccess(true).setMessage("success");
        } else {
            return new SyncMchStatusResp().setSuccess(false).setMessage(response.getMessage());
        }
    }

    @Override
    public Map<String, Object> getUnionOpenParam(String merchantSn) {
        Map<String, Object> result = new HashMap<>();
        MerchantProviderParams merNo = merchantProviderParamsMapper.getPayMchIdByChannelAndPayWay(merchantSn, "S20180919151112EA3E6", String.valueOf(PaywayEnum.UNIONPAY.getValue()));
        if (ObjectUtils.isEmpty(merNo) || StringUtils.isEmpty(merNo.getPay_merchant_id())) {
            return null;
        }
        MerchantProviderParams union = merchantProviderParamsMapper.getPayMchIdByChannelAndPayWay(merchantSn, lklChannelNo, String.valueOf(PaywayEnum.ACQUIRER.getValue()));
        result.put("merNo", merNo.getPay_merchant_id());
        result.put("union", union.getPay_merchant_id());
        return result;
    }

    @Override
    public Boolean getAcquirerMchStatus(String merchantSn) {
        MerchantProviderParams params = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());

        final String providerMerchantId = Optional.ofNullable(params)
                .map(param -> param.getProvider_merchant_id())
                .orElseThrow(() -> new CommonPubBizException("商户在lklV3没有进件成功"));
        final LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);

        MerchantDetailResp merchantDetailResp = lklV3Service.queryMerchantDetail(providerMerchantId, lklV3Param);
        if (Objects.isNull(merchantDetailResp)) {
            throw new CommonPubBizException("该商户被拉卡拉关闭的, 不可以变更为有效状态");
        }
        //商户收单机构状态 有效-VALID 无效-INVALID & 同步状态到数据库
        final String merStatus = merchantDetailResp.getStatus();
        if (Objects.equals(merStatus, "VALID")) {
            if (!params.getMerchant_state().equals(ValidStatusEnum.VALID.getValue())) {
                MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(ValidStatusEnum.VALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
                merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
            }
            return Boolean.TRUE;
        }
        if (params.getMerchant_state().equals(ValidStatusEnum.VALID.getValue())) {
            MerchantProviderParams update = new MerchantProviderParams().setId(params.getId()).setMerchant_state(ValidStatusEnum.INVALID.getValue()).setVersion(params.getVersion() + 1).setMtime(System.currentTimeMillis());
            merchantProviderParamsMapper.updateByPrimaryKeySelective(update);
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean bankAccountConsistent(String merchantSn, Map bankAccount) {
        //lklv3商户号
        final String providerMerchantId = Optional.ofNullable(merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue()))
                .map(MerchantProviderParams::getProvider_merchant_id)
                .orElseThrow(() -> new CommonPubBizException("商户在lklV3没有进件成功"));


        final LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
        MerAccountResp merAccountResp = lklV3Service.queryMerAccount(providerMerchantId, lklV3Param);
        if (Objects.isNull(merAccountResp)) {
            throw new CommonPubBizException(String.format("%s已被拉卡拉关闭,无法同步信息到拉卡拉",merchantSn));
        }
        //lklV3系统银行卡号返回格式为622908********9913
        final String acctNo = merAccountResp.getAcctNo();
        //收钱吧银行卡
        final String number = BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.NUMBER);
        log.info("lklV3通道下商户号merchantSn:{},lklV3银行账户:{},收钱吧银行账户:{}", merchantSn, acctNo, number);
        if(!Objects.equals(acctNo.length(),number.length())) {
            return Boolean.FALSE;
        }
        if (!acctNo.contains("*")) {
            return Objects.equals(acctNo, number);
        }
        final int start = acctNo.indexOf("*");
        final int end = acctNo.lastIndexOf("*");
        final String acctNoPrefix = acctNo.substring(0, start);
        final String numberPrefix = number.substring(0, start);
        final String acctNoSuffix = acctNo.substring(end + 1);
        final String numberSuffix = number.substring(end + 1);
        return Objects.equals(acctNoPrefix, numberPrefix) && Objects.equals(acctNoSuffix, numberSuffix);
    }



    @Override
    public Boolean bankAccountConsistentAndStatusOpen(String merchantSn, Map bankAccount) {
        final Boolean consistent = bankAccountConsistent(merchantSn, bankAccount);
        if(!consistent) {
            return Boolean.FALSE;
        }
        final Boolean acquirerMchStatus = getAcquirerMchStatus(merchantSn);
        if(!acquirerMchStatus) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        if (Objects.isNull(acquirerParams)) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .retry(false)
                    .build();
        }
        BasicProvider provider = providerFactory.getProvider(String.valueOf(acquirerParams.getProvider()));
        com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = provider.queryMerchantContractResult(acquirerParams.getProvider_merchant_id());
        Optional<MerchantProviderParamsDO> unionParam = getUnionPayParams(merchantSn, AcquirerTypeEnum.LKL_V3.getValue());
        if (contractResponse.isSystemFail()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .retry(false)
                    .build();
        } else if (contractResponse.isBusinessFail()) {
            saveOrUpdateParamsExt(unionParam, MerchantProviderParamsExtDO.UNION_PAY_FAIL, contractResponse.getMessage());
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                    .message(contractResponse.getMessage())
                    .retry(true)
                    .build();
        } else {
            String status = WosaiMapUtils.getString(contractResponse.getTradeParam(), "status");
            if (MerchantProviderParamsExtDO.UNION_PAY_FAIL.equals(status)) {
                saveOrUpdateParamsExt(unionParam, status, contractResponse.getMessage());
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message(contractResponse.getMessage())
                        .retry(true)
                        .build();
            } else if (MerchantProviderParamsExtDO.UNION_PAY_SUCCESS.equals(status)) {
                // 如果参数不存在要存一下
                if (!unionParam.isPresent()) {
                    saveLklUnionPayParams(merchantSn);
                }
                unionParam = getUnionPayParams(merchantSn, AcquirerTypeEnum.LKL_V3.getValue());
                saveOrUpdateParamsExt(unionParam, status, contractResponse.getMessage());
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                        .retry(true)
                        .build();
            } else {
                saveOrUpdateParamsExt(unionParam, status, "已注销");
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message("已注销")
                        .retry(true)
                        .build();
            }
        }
    }

    private void saveOrUpdateParamsExt(Optional<MerchantProviderParamsDO> unionParam, String status, String message) {
        if (!unionParam.isPresent()) {
            return;
        }
        Optional<MerchantProviderParamsExtDO> unionPayStatus = merchantProviderParamsExtDAO.getUnionPayStatus(unionParam.get().getId());
        // 如果状态和文案没有发生变化就不需要去做更新
        if (unionPayStatus.isPresent() && Objects.equals(unionPayStatus.get().getExtField1(), status) && Objects.equals(unionPayStatus.get().getFailMessage(), message)){
            return;
        }
        MerchantProviderParamsExtDO extDO = new MerchantProviderParamsExtDO();
        extDO.setParamId(unionParam.get().getId());
        extDO.setExtField2(unionParam.get().getPayMerchantId());
        extDO.setType(MerchantProviderParamsExtDO.UNION_PAY);
        extDO.setExtField1(status);
        extDO.setExtra(JSON.toJSONString(CollectionUtil.hashMap("message", message)));
        if (unionPayStatus.isPresent()) {
            extDO.setId(unionPayStatus.get().getId());
            merchantProviderParamsExtDAO.updateMerchantParams(extDO);
        } else {
            merchantProviderParamsExtDAO.saveMerchantParametersExt(extDO);
        }
    }

    private void saveLklUnionPayParams(String merchantSn) {
        try {
            ContractRule contractRule = ruleContext.getContractRule(lklOpenContractRule);
            ContractChannel contractChannel = contractRule.getContractChannel();
            LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO = getLklOpenUnionPayTradeParamByMerchantConfig(merchantSn);
            MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
            merchantProviderParamsDO.setId(UUID.randomUUID().toString());
            merchantProviderParamsDO.setCtime(System.currentTimeMillis());
            merchantProviderParamsDO.setMtime(System.currentTimeMillis());
            merchantProviderParamsDO.setDeleted(DeleteStatusEnum.NO_DELETED.getValue());
            merchantProviderParamsDO.setMerchantSn(merchantSn);
            merchantProviderParamsDO.setOutMerchantSn(merchantSn);
            merchantProviderParamsDO.setPayway(PaywayEnum.UNIONPAY.getValue());
            merchantProviderParamsDO.setChannelNo(contractChannel.getChannel_no());
            merchantProviderParamsDO.setContractRule(lklOpenContractRule);
            merchantProviderParamsDO.setRuleGroupId(McConstant.RULE_GROUP_LKLORG);
            merchantProviderParamsDO.setProvider(ProviderEnum.PROVIDER_LKL_OPEN.getValue());
            merchantProviderParamsDO.setParentMerchantId(lklOpenUnionPayTradeParamBO.getProviderMerchantId());
            merchantProviderParamsDO.setProviderMerchantId(lklOpenUnionPayTradeParamBO.getProviderMerchantId());
            merchantProviderParamsDO.setPayMerchantId(lklOpenUnionPayTradeParamBO.getProviderMerchantId());
            merchantProviderParamsDO.setParamsConfigStatus(ConfigStatusEnum.NOT_REQUIRE_CONFIG.getValue());
            merchantProviderParamsDO.setExtra(JSON.toJSONString(lklOpenUnionPayTradeParamBO));
            Optional<MerchantProviderParamsDO> unionParam = getUnionPayParams(merchantSn, AcquirerTypeEnum.LKL_V3.getValue());
            if (unionParam.isPresent()) {
                return;
            }
            merchantProviderParamsDAO.saveMerchantParameters(merchantProviderParamsDO);
        } catch (ContractBizException e) {
            log.error("保存云闪付参数失败 {}", merchantSn, e);
        }
    }

    private LklOpenUnionPayTradeParamBO getLklOpenUnionPayTradeParamByMerchantConfig(String merchantSn) {
        Map<?, ?> merchant = merchantService.getMerchantBySn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            throw new ContractBizException("商户不存在, merchantSn:" + merchantSn);
        }
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);
        LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO = new LklOpenUnionPayTradeParamBO();
        Map<String, Object> merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (Objects.isNull(merchantConfig)) {
            throw new ContractBizException("商户配置不存在, merchantId:" + merchantId);
        }
        ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(merchantConfig, String.format("%s.%s.%s",
                        MerchantConfig.PARAMS, TransactionParam.LAKALA_TRADE_PARAMS, TransactionParam.LAKALA_TERM_ID)),
                t -> lklOpenUnionPayTradeParamBO.setTermId(ConvertUtil.castToExpectedType(t, String.class).orElse(null)));
        ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(merchantConfig, String.format("%s.%s.%s",
                        MerchantConfig.PARAMS, TransactionParam.LAKALA_TRADE_PARAMS, TransactionParam.LAKALA_MERC_ID)),
                t -> lklOpenUnionPayTradeParamBO.setProviderMerchantId(ConvertUtil.castToExpectedType(t, String.class).orElse(null)));
        if (StringUtils.isAnyBlank(lklOpenUnionPayTradeParamBO.getProviderMerchantId(), lklOpenUnionPayTradeParamBO.getTermId())) {
            throw new ContractBizException("银联商户号或者终端号为空, merchantSn = " + merchantSn);
        }
        return lklOpenUnionPayTradeParamBO;
    }

    private Optional<MerchantProviderParamsDO> getUnionPayParams(String merchantSn, String acquirer) {
        List<McChannelDO> mcChannels = mcChannelDAO.getMcChannelByAcquirer(acquirer, PaywayEnum.UNIONPAY.getValue());
        for (McChannelDO mcChannel : mcChannels) {
            Optional<MerchantProviderParamsDO> merChantProviderParams = merchantProviderParamsDAO.getMerChantProviderParams(merchantSn, mcChannel.getChannelNo(), PaywayEnum.UNIONPAY.getValue());
            if (merChantProviderParams.isPresent()) {
                return merChantProviderParams;
            }
        }
        return Optional.empty();

    }

}