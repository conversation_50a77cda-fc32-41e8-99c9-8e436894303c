package com.wosai.upay.job.refactor.biz.acquirer.fuyou;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.*;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.utils.thread.ThreadPoolWorker;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.dto.OpenD0ResultDTO;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.ContractTaskDAO;
import com.wosai.upay.job.refactor.dao.MerchantAcquirerInfoDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantAcquirerInfoDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.service.rpc.ClearanceService;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 富友D0开通处理
 *
 * <AUTHOR>
 * @date 2024/7/23 14:25
 */
@Component
@Slf4j
public class FuYouOpenDayZeroProcessor {

    @Resource(name = "fuyouService")
    private FuyouService fuyouService;

    @Resource
    private MerchantAcquirerInfoDAO merchantAcquirerInfoDAO;

    @Resource
    private ClearanceService clearanceService;

    protected static final Integer DEFAULT_RETRY_NUM = 10;

    @Resource
    private ContractSubTaskDAO contractSubTaskDAO;

    @Resource
    private ContractTaskDAO contractTaskDAO;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    protected MerchantService merchantService;

    private String getContractTaskResult(String message) {
        HashMap<String, String> map = Maps.newHashMap();
        map.put(CommonModel.RESULT_MESSAGE, message);
        return JSON.toJSONString(map);
    }

    /**
     * 开通富友D0业务
     *
     * @param contractTaskDO 开通D0业务主任务
     * @param force          是否强制处理任务(强制处理不会判断状态以及是否超时,都会处理)
     */
    public void openDayZero(ContractTaskDO contractTaskDO, boolean force) {
        if (Objects.isNull(contractTaskDO)) {
            return;
        }
        if (!force &&
                !Objects.equals(contractTaskDO.getStatus(), ContractTaskProcessStatusEnum.WAIT_PROCESS.getValue())) {
            log.warn("开通D0业务已在处理中或者处理结束, id = {}", contractTaskDO.getId());
            return;
        }
        if (!force && Objects.nonNull(contractTaskDO.getCompleteAt()) && contractTaskDO.getCompleteAt().before(new Timestamp(System.currentTimeMillis()))) {
            updateFailTask(contractTaskDO, "开通D0业务失败,规定时间内未开通成功");
            return;
        }
        String merchantSn = contractTaskDO.getMerchantSn();
        Optional<MerchantProviderParamsDO> paramsDOOptional = merchantProviderParamsDAO.getBySnAndProviderAndPayWay(merchantSn, ProviderEnum.PROVIDER_FUYOU.getValue(), PaywayEnum.ACQUIRER.getValue());
        if (!paramsDOOptional.isPresent()) {
            updateFailTask(contractTaskDO, "开通D0业务失败,不存在富友的交易配置");
            return;
        }
        Map<Integer, ContractSubTaskDO> subTaskMap = contractSubTaskDAO.listByPTaskId(contractTaskDO.getId()).stream().collect(Collectors.toMap(ContractSubTaskDO::getTaskType, Function.identity(), (k1, k2) -> k1));
        ContractSubTaskDO openD0SubTask = subTaskMap.get(ContractSubTaskTypeEnum.OPEN_DAY_ZERO.getValue());
        ContractSubTaskDO queryD0ResultSubTask = subTaskMap.get(ContractSubTaskTypeEnum.QUERY_DAY_ZERO_OPEN_RESULT.getValue());
        if (Objects.isNull(openD0SubTask) || Objects.isNull(queryD0ResultSubTask)) {
            updateFailTask(contractTaskDO, "不存在开通D0业务或者D0结果查询的子任务");
            return;
        }
        handleOpenD0Rsp(contractTaskDO, paramsDOOptional.get().getProviderMerchantId(), openD0SubTask, queryD0ResultSubTask);
        recordOpenDayZeroStatusAndNotify(contractTaskDO, openD0SubTask, paramsDOOptional.get().getProviderMerchantId());
        contractTaskDO.setPriority(new Timestamp(System.currentTimeMillis()));
        contractTaskDAO.batchUpdateTasks(contractTaskDO, Lists.newArrayList(openD0SubTask, queryD0ResultSubTask));
    }

    private void updateFailTask(ContractTaskDO contractTaskDO, String result) {
        contractTaskDO.setStatus(ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue());
        contractTaskDO.setResult(getContractTaskResult(result));
        contractTaskDAO.updateByIdIgnoreUpdateTime(contractTaskDO);
    }

    private void handleOpenD0Rsp(ContractTaskDO contractTaskDO, String providerMerchantId, ContractSubTaskDO openD0SubTask, ContractSubTaskDO queryD0ResultSubTask) {
        ContractResponse contractResponse = fuyouService.openDayZero(contractTaskDO.getMerchantSn(), providerMerchantId);
        openD0SubTask.setResponseBody(JSON.toJSONString(contractResponse.getResponseParam()));
        openD0SubTask.setRequestBody(JSON.toJSONString(contractResponse.getRequestParam()));
        openD0SubTask.setResult(contractResponse.getMessage());
        if (contractResponse.isSuccess()) {
            Object modifyNo = BeanUtil.getNestedProperty(contractResponse.getResponseParam(), "modify_no");
            if (Objects.nonNull(modifyNo) && modifyNo instanceof String && StringUtils.isNotBlank((String) modifyNo)) {
                handleOpenD0ApplyRspSuccess(contractTaskDO, openD0SubTask, queryD0ResultSubTask, (String) modifyNo);
            } else {
                log.warn("商户{}开通D0业务成功,但是没有返回modify_no", contractTaskDO.getMerchantSn());
                handleOpenD0ApplyWithoutContractId(contractTaskDO, openD0SubTask);
            }
        } else if (contractResponse.isBusinessFail()) {
            handleOpenD0ApplyBusinessFail(contractTaskDO, openD0SubTask, contractResponse);
        } else if (contractResponse.isSystemFail()) {
            handleOpenD0ApplySystemFail(contractTaskDO, openD0SubTask, contractResponse);
        }
    }

    private void handleOpenD0ApplyBusinessFail(ContractTaskDO contractTaskDO, ContractSubTaskDO openD0SubTask, ContractResponse contractResponse) {
        openD0SubTask.setStatus(ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue());
        contractTaskDO.setStatus(ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue());
        contractTaskDO.setResult(getContractTaskResult(contractResponse.getMessage()));
        contractTaskDO.setCompleteAt(new Timestamp(System.currentTimeMillis()));
    }

    private void handleOpenD0ApplySystemFail(ContractTaskDO contractTaskDO, ContractSubTaskDO openD0SubTask, ContractResponse contractResponse) {
        openD0SubTask.setStatus(ContractSubTaskProcessStatusEnum.SYSTEM_EXCEPTION_FAILURE.getValue());
        Integer alreadyRetryTimes = openD0SubTask.getRetry();
        openD0SubTask.setRetry(Objects.isNull(alreadyRetryTimes) ? 1 : alreadyRetryTimes + 1);
        if (openD0SubTask.getRetry() < DEFAULT_RETRY_NUM) {
            openD0SubTask.setStatus(ContractSubTaskProcessStatusEnum.SYSTEM_EXCEPTION_FAILURE.getValue());
            contractTaskDO.setStatus(ContractTaskProcessStatusEnum.WAIT_PROCESS.getValue());
        } else {
            openD0SubTask.setStatus(ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue());
            contractTaskDO.setStatus(ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue());
            contractTaskDO.setResult(getContractTaskResult(contractResponse.getMessage()));
            contractTaskDO.setCompleteAt(new Timestamp(System.currentTimeMillis()));
        }
    }

    private void handleOpenD0ApplyWithoutContractId(ContractTaskDO contractTaskDO, ContractSubTaskDO openD0SubTask) {
        openD0SubTask.setResult("回调id为空");
        openD0SubTask.setStatus(ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue());
        contractTaskDO.setStatus(ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue());
        contractTaskDO.setResult(getContractTaskResult("回调id为空"));
        contractTaskDO.setCompleteAt(new Timestamp(System.currentTimeMillis()));
    }

    private void handleOpenD0ApplyRspSuccess(ContractTaskDO contractTaskDO, ContractSubTaskDO openD0SubTask, ContractSubTaskDO queryD0ResultSubTask, String modifyNo) {
        queryD0ResultSubTask.setContractId(modifyNo);
        queryD0ResultSubTask.setScheduleStatus(ScheduleStatusEnum.CAN.getValue());
        openD0SubTask.setStatus(ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue());
        contractTaskDO.setAffectStatusSuccessTaskCount(1);
        contractTaskDO.setResult(getContractTaskResult("审核中"));
        contractTaskDO.setStatus(ContractTaskProcessStatusEnum.AUDITING.getValue());
    }


    /**
     * 查询开通富友D0业务结果
     *
     * @param contractTaskDO 开通D0业务主任务
     * @param force          强制查询(不会校验是否已经处理过该任务和任务是否已经超时)
     */
    public String queryOpenDayZeroResult(ContractTaskDO contractTaskDO, boolean force) {
        if (Objects.isNull(contractTaskDO)) {
            return "任务为空";
        }
        String merchantSn = contractTaskDO.getMerchantSn();
        Map<Integer, ContractSubTaskDO> subTaskMap = contractSubTaskDAO.listByPTaskId(contractTaskDO.getId()).stream().collect(Collectors.toMap(ContractSubTaskDO::getTaskType, Function.identity(), (k1, k2) -> k1));
        ContractSubTaskDO queryD0ResultSubTask = subTaskMap.get(ContractSubTaskTypeEnum.QUERY_DAY_ZERO_OPEN_RESULT.getValue());
        Optional<String> validateResult = validateQueryFuYouD0SubTask(queryD0ResultSubTask);
        if (validateResult.isPresent()) {
            return validateResult.get();
        }
        if (!force && Objects.equals(contractTaskDO.getStatus(), ContractTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())) {
            return queryD0ResultSubTask.getResult();
        }
        if (!force && Objects.nonNull(contractTaskDO.getCompleteAt()) && contractTaskDO.getCompleteAt().before(new Timestamp(System.currentTimeMillis()))) {
            contractTaskDO.setStatus(ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue());
            contractTaskDO.setResult(getContractTaskResult("开通D0业务失败,规定时间内未开通成功"));
            contractTaskDAO.updateByIdIgnoreUpdateTime(contractTaskDO);
            return "开通D0业务失败,规定时间内未开通成功";
        }
        Optional<MerchantProviderParamsDO> paramsDOOptional = merchantProviderParamsDAO.getBySnAndProviderAndPayWay(merchantSn, ProviderEnum.PROVIDER_FUYOU.getValue(), PaywayEnum.ACQUIRER.getValue());
        if (!paramsDOOptional.isPresent()) {
            return "商户富友交易配置不存在";
        }
        String providerMerchantId = paramsDOOptional.get().getProviderMerchantId();
        handleQueryOpenD0ResultRsp(contractTaskDO, queryD0ResultSubTask, providerMerchantId);
        recordOpenDayZeroStatusAndNotify(contractTaskDO, queryD0ResultSubTask, providerMerchantId);
        contractTaskDO.setPriority(new Timestamp(System.currentTimeMillis()));
        contractTaskDAO.batchUpdateTasks(contractTaskDO, Lists.newArrayList(queryD0ResultSubTask));
        return queryD0ResultSubTask.getResult();
    }

    private Optional<String> validateQueryFuYouD0SubTask(ContractSubTaskDO queryD0ResultSubTask) {
        if (Objects.isNull(queryD0ResultSubTask)) {
            return Optional.of("查询开通富友DO任务为空");
        }
        if (Objects.equals(ScheduleStatusEnum.NOT_CAN.getValue(), queryD0ResultSubTask.getScheduleStatus())) {
            return Optional.of("查询开通富友DO任务当前不可调度");
        }
        if (StringUtils.isBlank(queryD0ResultSubTask.getContractId())) {
            return Optional.of("查询开通富友DO任务回调id为空");
        }
        return Optional.empty();
    }

    private  void recordOpenDayZeroStatusAndNotify(ContractTaskDO contractTaskDO, ContractSubTaskDO subTaskDO, String providerMerchantId) {
        if (Objects.equals(contractTaskDO.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue())
                || Objects.equals(contractTaskDO.getStatus(), ContractTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())) {
            MerchantAcquirerInfoDO merchantAcquirerInfoDO = saveAndGetMerchantAcquirerInfo(contractTaskDO, providerMerchantId);
            asyncNotifyOpenD0Result(contractTaskDO, subTaskDO, merchantAcquirerInfoDO);
        }
    }

    private void asyncNotifyOpenD0Result(ContractTaskDO contractTaskDO, ContractSubTaskDO subTaskDO, MerchantAcquirerInfoDO merchantAcquirerInfoDO) {
        ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> {
            try {
                Integer openStatus = merchantAcquirerInfoDO.getDayZeroOpenStatus();
                OpenD0ResultDTO openD0ResultDTO = new OpenD0ResultDTO();
                openD0ResultDTO.setMerchantId(merchantService.getMerchantBySn(contractTaskDO.getMerchantSn(), null).getId());
                openD0ResultDTO.setOpenSuccess(Objects.equals(openStatus, DayZeroOpenStatusEnum.OPENED_SUCCESSFULLY.getValue()));
                if (!openD0ResultDTO.isOpenSuccess()) {
                    openD0ResultDTO.setFailedMessage(subTaskDO.getResult());
                }
                clearanceService.openFuyouD0Callback(openD0ResultDTO);
            } catch (Exception e) {
                log.error("通知商户开通D0业务结果失败, 任务id:{}", contractTaskDO.getId(), e);
            }
        });
    }

    private @NotNull MerchantAcquirerInfoDO saveAndGetMerchantAcquirerInfo(ContractTaskDO contractTaskDO, String providerMerchantId) {
        MerchantAcquirerInfoDO merchantAcquirerInfoDO = new MerchantAcquirerInfoDO();
        merchantAcquirerInfoDO.setMerchantSn(contractTaskDO.getMerchantSn());
        merchantAcquirerInfoDO.setAcquirer(AcquirerTypeEnum.FU_YOU.getValue());
        merchantAcquirerInfoDO.setAcquirerMerchantId(providerMerchantId);
        merchantAcquirerInfoDO.setDayZeroOpenStatus(Objects.equals(contractTaskDO.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue())
                ? DayZeroOpenStatusEnum.OPENING_FAILED.getValue() : DayZeroOpenStatusEnum.OPENED_SUCCESSFULLY.getValue());
        merchantAcquirerInfoDAO.insertOrUpdateOne(merchantAcquirerInfoDO);
        return merchantAcquirerInfoDO;
    }

    private void handleQueryOpenD0ResultRsp(ContractTaskDO contractTaskDO, ContractSubTaskDO queryD0ResultSubTask, String providerMerchantId) {
        ContractResponse contractResponse = fuyouService.queryBusinessAuditStatus(queryD0ResultSubTask.getMerchantSn(),
                providerMerchantId, queryD0ResultSubTask.getContractId(), FuYouBusinessAuditTypeEnum.MERGE_SETTLEMENT.getValue());
        queryD0ResultSubTask.setRequestBody(JSON.toJSONString(contractResponse.getRequestParam()));
        queryD0ResultSubTask.setResponseBody(JSON.toJSONString(contractResponse.getResponseParam()));
        Map<String, Object> responseParam = contractResponse.getResponseParam();
        String modifyStatus = MapUtils.getString(responseParam, "modify_st");
        String modifyStatusDesc = MapUtils.getString(responseParam, "modify_st_desc");
        String modifyDealMsg = MapUtils.getString(responseParam, "modify_deal_msg");
        String result = "变更状态: " + modifyStatusDesc;
        if (StringUtils.isNotBlank(modifyDealMsg)) {
            result += ",处理意见: " + modifyDealMsg;
        }
        if (contractResponse.isSuccess()) {
            if (FuYouBusinessAuditModifyStatusEnum.PROCESSED.getValue().equals(modifyStatus)) {
                handleQueryD0AuditSuccess(contractTaskDO, queryD0ResultSubTask, result);
            } else if (FuYouBusinessAuditModifyStatusEnum.REFUSED.getValue().equals(modifyStatus)) {
                handlerQueryD0AuditFailed(contractTaskDO, queryD0ResultSubTask, result, modifyDealMsg);
            } else {
                queryD0ResultSubTask.setResult(result);
                queryD0ResultSubTask.setStatus(ContractSubTaskProcessStatusEnum.WAIT_PROCESS.getValue());
            }
        } else {
            handleQueryD0RspFailed(contractTaskDO, queryD0ResultSubTask, contractResponse, modifyStatusDesc, result);
        }
    }

    private void handleQueryD0RspFailed(ContractTaskDO contractTaskDO, ContractSubTaskDO queryD0ResultSubTask, ContractResponse contractResponse, String modifyStatusDesc, String result) {
        contractTaskDO.setCompleteAt(new Timestamp(System.currentTimeMillis()));
        contractTaskDO.setResult(getContractTaskResult(contractResponse.getMessage()));
        contractTaskDO.setStatus(ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue());
        queryD0ResultSubTask.setStatus(ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue());
        queryD0ResultSubTask.setResult(StringUtils.isBlank(modifyStatusDesc) ? contractResponse.getMessage() : result);
    }

    private void handlerQueryD0AuditFailed(ContractTaskDO contractTaskDO, ContractSubTaskDO queryD0ResultSubTask, String result, String modifyMsg) {
        contractTaskDO.setResult(getContractTaskResult(modifyMsg));
        contractTaskDO.setStatus(ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue());
        queryD0ResultSubTask.setStatus(ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue());
        queryD0ResultSubTask.setResult(result);
        contractTaskDO.setCompleteAt(new Timestamp(System.currentTimeMillis()));
    }

    private void handleQueryD0AuditSuccess(ContractTaskDO contractTaskDO, ContractSubTaskDO queryD0ResultSubTask, String result) {
        contractTaskDO.setStatus(ContractTaskProcessStatusEnum.PROCESS_SUCCESS.getValue());
        contractTaskDO.setResult(getContractTaskResult("审核通过"));
        contractTaskDO.setAffectStatusSuccessTaskCount(2);
        queryD0ResultSubTask.setResult(result);
        contractTaskDO.setCompleteAt(new Timestamp(System.currentTimeMillis()));
        queryD0ResultSubTask.setStatus(ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue());
    }
}
