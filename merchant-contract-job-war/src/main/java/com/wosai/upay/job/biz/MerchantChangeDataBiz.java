package com.wosai.upay.job.biz;

import com.wosai.upay.job.avro.MerchantChangeDataStatusChange;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/4/10
 */
@Slf4j
@Component
public class MerchantChangeDataBiz {

    @Resource(name = "kafkaTemplate")
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String MERCHANT_CHANGE_DATA_TOPIC = "events.cua.merchant-contract-job.change-data";

    /**
     * 商户信息变更任务状态变更后发送消息
     *
     * @param merchantSn  商户号
     * @param merchantId  商户id
     * @param preStatus   之前的状态
     * @param afterStatus 新的状态
     */
    public void sendMerchantChangeDataStatusChange(String merchantSn, String merchantId, Integer preStatus, int afterStatus, String memo, String applySource, Boolean reContract) {
        MerchantChangeDataStatusChange statusChange = new MerchantChangeDataStatusChange();
        statusChange.setMerchantSn(merchantSn);
        statusChange.setMerchantId(merchantId);
        statusChange.setPreStatus(preStatus);
        statusChange.setStatus(afterStatus);
        statusChange.setMessage(memo);
        statusChange.setSource(applySource);
        statusChange.setReContract(reContract);
        log.info("sendMerchantChangeDataStatusChange : {}", statusChange);
        // 直接发送,不延迟。
        // 商户认证驳回重新提交常见
        // 延迟发送到pay-business-open到提交商户认证审核有一个时间差，在这个时间差内如果crm调用getOpenPayStatus会直接报商户认证审核失败
        // 同步发送可以缩小这个时间差，降低这种情况的发生
        kafkaTemplate.send(MERCHANT_CHANGE_DATA_TOPIC, statusChange);
    }
}
