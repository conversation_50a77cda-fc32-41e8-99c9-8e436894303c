package com.wosai.upay.job.biz.acquirer;


import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McRuleGroupDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * 用于银行收单机构的通用biz，比如获取默认规则组，查询开通业务状态等等
 */
@Component("common-biz")
@Slf4j
public class CommonAcquirerBiz implements IAcquirerBiz {

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    private McRuleGroupDAO mcRuleGroupDAO;

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return mcRuleGroupDAO.getDefaultRuleGroup(acquirer);
    }

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        throw new ContractBizException("商户收单机构，不支持此操作");
    }

    @Override
    public String getNormalWxRule() {
        return "";
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus != null) {

            McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(contractStatus.getAcquirer());

            if (mcAcquirerDO != null && WosaiStringUtils.isNotEmpty(mcAcquirerDO.getProvider())) {
                Optional<MerchantProviderParamsDO> unionParam = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, Integer.parseInt(mcAcquirerDO.getProvider()), PaywayEnum.UNIONPAY.getValue());

                if (unionParam.isPresent()) {
                    return UnionPayOpenStatusQueryResp.builder()
                            .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                            .retry(false)
                            .build();
                }
            }
        }


        return UnionPayOpenStatusQueryResp.builder()
                .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                .message("暂时无法开通，联系销售支持")
                .retry(false)
                .build();
    }
}
