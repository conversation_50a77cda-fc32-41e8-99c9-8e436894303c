package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.risk.bean.BlacklistBean;
import com.wosai.risk.bean.SimpleRiskOrderBean;
import com.wosai.risk.bean.req.BlistSceneReq;
import com.wosai.risk.bean.vo.SceneValidateVO;
import com.wosai.risk.constants.BlistConstant;
import com.wosai.risk.service.IRiskBlistSceneService;
import com.wosai.risk.service.RiskOrderGenerateService;
import com.wosai.upay.bank.model.MerchantBankAccount;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.refactor.model.enums.ContractSubTaskTypeEnum;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import entity.common.UserEs;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.JsonAggregateOnNullType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 黑名单相关处理
 * https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********
 *
 * <AUTHOR>
 * @date 2020-02-25
 */
@Component
@Slf4j
public class BlackListBiz {

    @Autowired
    private RiskOrderGenerateService riskOrderGenerateService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private IRiskBlistSceneService riskBlistSceneService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private BankBusinessLicenseService bankBusinessLicenseService;

    @Autowired
    private AopBiz aopBiz;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    private static final String ID_CARD_BLACK_MESSAGE_FEATURE = "身份证号";


    public void syncBlacklist(ContractSubTask subTask, String merchantSn, Integer payway, String message) {
        try {
            List<String> words = applicationApolloConfig.getBlacklistWords();
            if (WosaiStringUtils.isNotEmpty(message) && words.stream().anyMatch(word -> message.contains(word))) {
                String source = payway == null ? "lakala" : payway.equals(PaywayEnum.ALIPAY.getValue()) ? "alipay" : "";
                if (WosaiStringUtils.isNotEmpty(source)) {
                    syncBlacklist(subTask, merchantSn, source, getData(message));
                    merchantService.hitBlacklist(merchantService.getMerchantBySn(merchantSn, null).getId());
                }
            }
        } catch (Exception e) {
            log.error("syncBlacklist error merchant_sn: {} payway: {} message {}", merchantSn, payway, message, e);
        }
    }

    /**
     * 支付宝黑名单检验不通过，暂时不能创建
     * 根据我公司风险监测系统的监测结果，你的账户可能存在风险，暂时不能创建。原因：黑名单校验不通过
     * 商户号:1680003288858 报错来源:拉卡拉 错误提示:其他（自行填写）,[江苏炒货重庆观音桥店]身份证号:211222196407213048在黑名单中！
     * 当前进件的商户存在风险，进件失败
     *
     * @param message
     * @return
     */
    private Map getData(String message) {
        if (message.contains(ID_CARD_BLACK_MESSAGE_FEATURE)) {
            String identity = message.substring(message.lastIndexOf(":") + 1, message.lastIndexOf("在黑名单中"));
            return CollectionUtil.hashMap("identity", identity);
        } else {
            return null;
        }
    }

    /**
     * 只有新增商户入网和银行卡变更，第三方返回黑名单同步风控
     *
     * @param subTask
     * @param merchantSn
     * @param source
     * @param data
     */
    private void syncBlacklist(ContractSubTask subTask, String merchantSn, String source, Map data) {
        // task_type  0 基本信息变更  1 商户状态  2 结算账户变更 3 费率变更 4 银行卡更新 5 进件 6 更新
        // module  1  入网  2 商户变更
        if (Objects.nonNull(subTask) && (subTask.getTask_type() == ContractSubTaskTypeEnum.BANK_CARD_UPDATE.getValue() || subTask.getTask_type() == ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())) {
            int module = subTask.getTask_type() == ContractSubTaskTypeEnum.BANK_CARD_UPDATE.getValue() ? 2 : 1;
            BlacklistBean blacklistBean = new BlacklistBean();
            blacklistBean.setMerchantSn(merchantSn);
            blacklistBean.setSource(source);
            blacklistBean.setModule(module);
            if (WosaiMapUtils.isNotEmpty(data)) {
                blacklistBean.setData(data);
            }
            riskOrderGenerateService.syncBlacklist(blacklistBean);
        }
    }

    public void checkBlackList(String merchantSn) {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, devCode);
        MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), devCode);
        MerchantBankAccount merchantBankAccount = bankBusinessLicenseService.getMerchantBankAccountByMerchantId(merchant.getId());


        BlistSceneReq blistSceneReq = new BlistSceneReq();
        blistSceneReq.setId(merchant.getId());
        blistSceneReq.setCode("enrolment");

        Map<String, String> businessParam = new HashMap<>(5);
        businessParam.put(BlistConstant.FIELD_TYPE_OWNER_CELLPHONE, merchant.getOwner_cellphone());
        businessParam.put(BlistConstant.FIELD_TYPE_ACCOUNT_IDENTITY, merchantBankAccount.getIdentity());
        businessParam.put(BlistConstant.FIELD_TYPE_ACCOUNT_ENTRY_NUMBER, merchantBankAccount.getNumber());
        businessParam.put(BlistConstant.FIELD_TYPE_LEGAL_IDENTITY, merchantBusinessLicense.getLegal_person_id_number());
        businessParam.put(BlistConstant.FIELD_TYPE_BUSINESS_LICENSE_NUMBER, merchantBusinessLicense.getNumber());
        blistSceneReq.setBusinessParam(businessParam);
        SceneValidateVO sceneValidateVO = riskBlistSceneService.validateByBizParam(blistSceneReq);
        if (!sceneValidateVO.isValidateResult()) {
            throw new ContractBizException("黑名单校验失败：" + sceneValidateVO.getTerminology());
        }
    }


    /**
     * 创建风控黑名单
     * @param merchantSn
     * @param errMsg
     */
    public void createRiskOrder(String merchantSn, String taskType, String acquirer,String errMsg) {
        CompletableFuture.runAsync(() -> {
            try {
                String ruleCode = fetchBlackRuleCode(taskType, acquirer, errMsg);
                if (StringUtil.isBlank(ruleCode)){
                    log.info("创建风控工单,未获取风控规则,merchantSn:{},acquirer:{}", merchantSn, acquirer);
                    return;
                }
                MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, devCode);
                SimpleRiskOrderBean simpleRiskOrderBean = new SimpleRiskOrderBean();
                simpleRiskOrderBean.setMerchantSn(merchantSn);
                simpleRiskOrderBean.setRuleCode(ruleCode);
                simpleRiskOrderBean.setRemark(acquirer + taskType + ":" + errMsg);
                try {
                    UserEs userEs = aopBiz.getMaintainUser(merchant.getId());
                    simpleRiskOrderBean.setUserId(userEs.getId());
                    simpleRiskOrderBean.setUserName(userEs.getLinkman());
                }catch (ContractBizException e){
                    Thread.sleep(15000);
                    UserEs userEs = aopBiz.getMaintainUser(merchant.getId());
                    simpleRiskOrderBean.setUserId(userEs.getId());
                    simpleRiskOrderBean.setUserName(userEs.getLinkman());
                }
                simpleRiskOrderBean.setPlatform("SP");
                log.info("创建风控工单,{}", JSONObject.toJSONString(simpleRiskOrderBean));
                riskOrderGenerateService.createRiskOrder(simpleRiskOrderBean);
            }catch (Exception e){
                log.error("创建风控黑名单工单异常:{}", merchantSn,e);
            }
        });
    }



    private String fetchBlackRuleCode(String taskType, String acquirer,String result) {
        List<Map<String, Object>> blackRiskList = applicationApolloConfig.getBlackRiskKey();
        if (blackRiskList == null) {
            return null;
        }
        for (Map<String, Object> taskMap : blackRiskList) {
            List<Map<String, Object>> acquirerkList = getAcquirerList(taskMap, taskType);
            if (acquirerkList != null) {
                for (Map<String, Object> acquirerkMap : acquirerkList) {
                    List<Map<String, Object>> keyMapList = getKeyMapList(acquirerkMap, acquirer);
                    if (keyMapList != null) {
                        String ruleCode = findRuleCode(keyMapList, result);
                        if (ruleCode != null) {
                            return ruleCode;
                        }
                    }
                }
            }
        }
        return null;
    }

    private List<Map<String, Object>> getAcquirerList(Map<String, Object> taskMap, String acquirer) {
        Object subTaskObj = taskMap.get(acquirer);
        if (subTaskObj instanceof List) {
            return (List<Map<String, Object>>) subTaskObj;
        }
        return null;
    }

    private List<Map<String, Object>> getKeyMapList(Map<String, Object> subTaskMap, String subTaskType) {
        Object keyMapObj = subTaskMap.get(subTaskType);
        if (keyMapObj instanceof List) {
            return (List<Map<String, Object>>) keyMapObj;
        }
        return null;
    }

    private String findRuleCode(List<Map<String, Object>> keyMapList, String result) {
        for (Map<String, Object> keyMap : keyMapList) {
            if (keyMap != null) {
                for (Map.Entry<String, Object> entry : keyMap.entrySet()) {
                    if (result.contains(entry.getKey())) {
                        return entry.getValue().toString();
                    }
                }
            }
        }
        return null;
    }


}
