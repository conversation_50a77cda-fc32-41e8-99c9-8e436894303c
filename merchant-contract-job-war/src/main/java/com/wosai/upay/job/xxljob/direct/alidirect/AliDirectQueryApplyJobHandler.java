package com.wosai.upay.job.xxljob.direct.alidirect;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.alipay.api.request.AlipayOpenAgentOrderQueryRequest;
import com.alipay.api.response.AlipayOpenAgentOrderQueryResponse;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.direct.AliDirectBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.enume.AliDirectApplyStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.AliDirectApplyMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.AliDirectApply;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.monitor.MonitorObject;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.directPay.AlipayOpenAgentOrderQueryReq;
import com.wosai.upay.merchant.contract.service.AliPayDirectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.List;

/**
 * xxl_job_desc: 支付宝直连-查询申请单
 * 支付宝直连提交申请单任务
 */
@Slf4j
@Component("AliDirectQueryApplyJobHandler")
public class AliDirectQueryApplyJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private AliPayDirectService aliPayDirectService;

    @Autowired
    private AliDirectApplyMapper applyMapper;

    @Autowired
    private DirectStatusBiz directStatusBiz;

    @Autowired
    private AliDirectBiz aliDirectBiz;

    @Autowired
    private ContractTaskBiz taskBiz;

    @Autowired
    private MonitorLog monitorLog;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Value("${ali.direct}")
    private String aliDirectDevCode;

    private static final long FOUR_HOURS = 4 * 60 * 60 * 1000;

    @Override
    public String getLockKey() {
        return "AliDirectQueryApplyJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        Long queryTime = param.getQueryTime();
        Integer queryLimit = param.getBatchSize();
        long currentTime = System.currentTimeMillis();
        List<AliDirectApply> applyList = applyMapper.getAppliesByPrioirtyAndStatus(
                DateFormatUtils.format(currentTime - queryTime, "yyyy-MM-dd HH:mm:ss"),
                DateFormatUtils.format(currentTime, "yyyy-MM-dd HH:mm:ss"),
                Lists.newArrayList(AliDirectApplyStatus.IN_ALI_AUDITING.getVal(), AliDirectApplyStatus.WAIT_FOR_SIGN.getVal()),
                queryLimit);
        applyList.forEach(apply -> {
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                doQueryApply(apply);
            } catch (Throwable e) {
                log.error("confirm apply error {} ", apply.getMerchant_sn(), e);
                monitorLog.recordMonitor("支付宝直连", apply.getMerchant_sn() + "支付宝直连确认申请单" + apply.getId() + "异常" + ExceptionUtil.getThrowableMsg(e));
            }
        });
    }

    private void doQueryApply(AliDirectApply apply) {
        String merchantSn = apply.getMerchant_sn();
        long start = System.currentTimeMillis();
        int code = 200;
        String message = "";
        try {
            AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse> response = aliPayDirectService.alipayOpenAgentOrderQuery(new AlipayOpenAgentOrderQueryReq().setBatchNo(apply.getBatch_no()));
            code = response.getCode();
            message = response.getMessage();
            if (response.isSuccess()) {
                String aliStatus = response.getResp().getOrderStatus();
                AliDirectApplyStatus applyStatus;
                if (WosaiStringUtils.isEmpty(aliStatus) || (applyStatus = AliDirectBiz.ALI_STATUS_MAPPING.get(aliStatus)) == null) {
                    log.error("同步支付宝直连申请单返回状态不存在:{} {}", merchantSn, aliStatus);
                    monitorLog.recordMonitor("支付宝直连", apply.getMerchant_sn() + "查询支付宝直连申请" + apply.getId() + "返回未知状态" + aliStatus);
                    return;
                }
                //状态未发生变化 四个小时之后再查,这种情况不用发消息，所以用单独更新priority的方法
                if (applyStatus.getVal().equals(apply.getStatus())) {
                    applyMapper.updateByPrimaryKeySelective(new AliDirectApply().setId(apply.getId()).setPriority(new Date(System.currentTimeMillis() + FOUR_HOURS)));
                    return;
                }
                aliDirectBiz.doAfterStatusChanged(response.getResp(), applyStatus, apply, true);
            } else {
                if (response.isSystemFail()) {
                    monitorLog.recordMonitor("支付宝直连", apply.getMerchant_sn() + "查询支付宝直连申请" + apply.getBatch_no() + "返回500" + message);
                    // 往后推五分钟再查询
                    applyMapper.updateByPrimaryKeySelective(new AliDirectApply().setId(apply.getId()).setPriority(new Date(System.currentTimeMillis() + ScheduleUtil.DEFAULT_FIVE_MINUTES_MILLIS_QUERY)));
                } else {
                    String finalMessage = message;
                    transactionTemplate.executeWithoutResult(status -> {
                        AliDirectApply update = new AliDirectApply().setId(apply.getId()).setRequest_body(JSON.toJSONString(response.getReq()))
                                .setResponse_body(JSON.toJSONString(response.getResp())).setStatus(AliDirectApplyStatus.APPLY_REJECTED.getVal()).setResult(finalMessage);
                        applyMapper.updateByPrimaryKeySelective(update);
                        taskBiz.update(new ContractTask().setId(apply.getTask_id()).setStatus(TaskStatus.FAIL.getVal())
                                .setResult(JSON.toJSONString(CollectionUtil.hashMap("channel", ProviderUtil.ALI_DIRECT, "message", finalMessage))));
                        directStatusBiz.createOrUpdateDirectStatus(merchantSn, aliDirectDevCode, DirectStatus.STATUS_BIZ_FAIL, finalMessage);
                    });
                }
            }
        } finally {
            monitorLog.recordObject(new MonitorObject()
                    .setSn(merchantSn)
                    .setEvent(MonitorObject.ALI_DIRECT_APPLY)
                    .setCost(System.currentTimeMillis() - start)
                    .setStatus(code)
                    .setMessage(message));
        }
    }
}