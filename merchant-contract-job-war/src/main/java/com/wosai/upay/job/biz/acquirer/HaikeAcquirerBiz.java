package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shouqianba.campus.center.application.CampusZoneService;
import com.shouqianba.campus.center.request.zone.CheckSpecifiedRangeCampusZoneExistReq;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.dao.*;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.AlipayBusinessFileds;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.model.provider.TongLianParam;
import com.wosai.upay.merchant.contract.model.provider.UnionWeixinParam;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import com.wosai.upay.merchant.contract.model.weixin.SubdevConfigResp;
import com.wosai.upay.merchant.contract.model.weixin.WeixinParam;
import com.wosai.upay.merchant.contract.service.HaikeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-07-19
 */
@Component("haike-biz")
@Slf4j
public class HaikeAcquirerBiz implements IAcquirerBiz {

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private HaikeService haikeService;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private CampusZoneService campusZoneService;

    @Autowired
    private ParamContextBiz paramContextBiz;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private McChannelDAO mcChannelDAO;

    @Autowired
    private MerchantProviderParamsExtDAO merchantProviderParamsExtDAO;

    @Autowired
    private ProviderFactory providerFactory;


    @Autowired
    private TradeConfigService tradeConfigService;

    @Override
    public String getMerchantDefaultRuleGroup(String merchantSn, String acquirer) {
        String channelNo = getInUseWxChannelNo(merchantSn);
        return Objects.equals("*********", channelNo) ? McConstant.RULE_GROUP_HAIKE : McConstant.RULE_GROUP_HAIKE_ORG;
    }

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_HAIKE;
    }


    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        return BeanUtil.getPropString(merchantConfig, "params.hk_trade_params.hk_mch_id");
    }


    /**
     * 获取商户 渠道号  海科不用区分特殊渠道（高校等），微信都是统一的渠道号
     *
     * @return
     */
    @Override
    public String getSchoolCanteenChannelNo(String merchantSn) {
        return getInUseWxChannelNo(merchantSn);
    }

    /**
     * 获取微信普通渠道规则
     *
     * @return
     */
    @Override
    public String getMerchantNormalWxRule(String merchantSn) {
        String channelNo = getInUseWxChannelNo(merchantSn);
        return "haike-1037-3-" + channelNo;
    }

    public String getNormalWxRule() {
        return ContractRuleConstants.HAIKE_NORMAL_WEIXIN_RULE;
    }

    @Override
    public void updateWeixinParams(MerchantProviderParamsDto paramsDto, Map params) {
        try {
            Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(paramsDto.getMerchant_sn(), null);
            UnionWeixinParam unionWeixinParam = contractParamsBiz.buildContractParamsByPayMchId(paramsDto.getPay_merchant_id(), UnionWeixinParam.class);
            ContractResponse response = haikeService.updateWeixinWithParams(paramContext, unionWeixinParam);
            log.info("haike MchContractServicePhone result:{},{}", params, response);
        } catch (Exception e) {
            log.error("haike MchContractServicePhone error:{},{}", params, e);
        }
    }

    @Override
    public WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        WeixinParam weixinParam = contractParamsBiz.buildContractParams(String.valueOf(providerParams.getProvider()), providerParams.getPayway(), providerParams.getChannel_no(), WeixinParam.class);
        weixinParam.setSub_mch_id(providerParams.getPay_merchant_id());
        HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
        SubdevConfigResp subdevConfigResp = haikeService.queryWeixinSubdevConfig(providerParams.getPay_merchant_id(), haikeParam);
        MchInfo mchInfo = null;
        try {
            mchInfo = haikeService.queryWeChatMchInfoParams(providerParams.getPay_merchant_id());
        } catch (Exception e) {
            log.error("查询微信子商户号信息失败: {}", providerParams.getPay_merchant_id(), e);
        }
        return new WxMchInfo().setMchInfo(mchInfo).setSubdevConfig(subdevConfigResp);
    }

    @Override
    public AlipayMchInfo getAlipayMchInfo(MerchantProviderParams providerParams) {
        Map alipayMap = haikeService.queryAlySubMch(providerParams.getPay_merchant_id());
        if (!AlipayBusinessFileds.RETURN_ALIPAY_CODE_SUCCESS.equals(WosaiMapUtils.getString(alipayMap, AlipayBusinessFileds.CODE))) {
            throw new ContractBizException(WosaiMapUtils.getString(alipayMap, AlipayBusinessFileds.MSG));
        }
        return JSONObject.toJavaObject(JSONObject.parseObject(JSONObject.toJSONString(alipayMap)), AlipayMchInfo.class);
    }

    private String getInUseWxChannelNo(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andProviderEqualTo(ProviderEnum.PROVIDER_HAIKE.getValue())
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExample(example);
        if (WosaiCollectionUtils.isEmpty(merchantProviderParams)) {
            return "*********";
        }
        return merchantProviderParams.get(0).getChannel_no();
    }

    @Override
    public Boolean bankAccountConsistent(String merchantSn, Map bankAccount) {
        Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
        final ContractResponse contractResponse = haikeService.queryMerchant(contextParam, haikeParam);
        if(!contractResponse.isSuccess()) {
            throw new CommonPubBizException(contractResponse.getMessage());
        }
        final Map<String, Object> responseParam = contractResponse.getResponseParam();
        Map account = (Map)responseParam.get("bankcard_data");
        String acntNo = BeanUtil.getPropString(account, "acc_no");
        //收钱吧银行卡
        final String number = BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.NUMBER);
        log.info("海科通道下商户号merchantSn:{},海科银行账户:{},收钱吧银行账户:{}",merchantSn, acntNo,number);
        return Objects.equals(acntNo,number);
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_HAIKE.getValue());
        if (Objects.isNull(acquirerParams)) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .retry(false)
                    .build();
        }
        BasicProvider provider = providerFactory.getProvider(String.valueOf(acquirerParams.getProvider()));
        com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = provider.queryMerchantContractResult(acquirerParams.getProvider_merchant_id());
        Optional<MerchantProviderParamsDO> unionParam = getUnionPayParams(merchantSn, AcquirerTypeEnum.HAI_KE.getValue());
        if (contractResponse.isSystemFail()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .retry(false)
                    .build();
        } else if (contractResponse.isBusinessFail()) {
            saveOrUpdateParamsExt(unionParam, MerchantProviderParamsExtDO.UNION_PAY_FAIL, contractResponse.getMessage());
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                    .message(contractResponse.getMessage())
                    .retry(true)
                    .build();
        } else {
            String status = WosaiMapUtils.getString(contractResponse.getTradeParam(), "status");
            if (MerchantProviderParamsExtDO.UNION_PAY_FAIL.equals(status)) {
                saveOrUpdateParamsExt(unionParam, status, contractResponse.getMessage());
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message(contractResponse.getMessage())
                        .retry(true)
                        .build();
            } else if (MerchantProviderParamsExtDO.UNION_PAY_SUCCESS.equals(status)) {
                // 如果参数不存在要存一下
                if (!unionParam.isPresent()) {
                    saveHaikeUnionPayParams(acquirerParams, contractResponse);
                }
                unionParam = getUnionPayParams(merchantSn, AcquirerTypeEnum.HAI_KE.getValue());
                saveOrUpdateParamsExt(unionParam, status, contractResponse.getMessage());
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                        .retry(true)
                        .build();
            } else {
                saveOrUpdateParamsExt(unionParam, status, "已注销");
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message("已注销")
                        .retry(true)
                        .build();
            }
        }
    }

    private void saveHaikeUnionPayParams(MerchantProviderParams acquirerParams, ContractResponse contractResponse) {
        String payMerchantId = BeanUtil.getPropString(contractResponse.getResponseParam(), "bank_biz_info.bank_merch_no");
        String merchantName = WosaiMapUtils.getString(contractResponse.getResponseParam(), "merch_short_name");
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams();
        merchantProviderParams.setId(UUID.randomUUID().toString());
        merchantProviderParams.setMerchant_sn(acquirerParams.getMerchant_sn());
        merchantProviderParams.setOut_merchant_sn(acquirerParams.getMerchant_sn());
        merchantProviderParams.setMerchant_name(merchantName);
        merchantProviderParams.setCtime(System.currentTimeMillis());
        merchantProviderParams.setMtime(System.currentTimeMillis());
        merchantProviderParams.setDeleted(false);
        merchantProviderParams.setPayway(PaywayEnum.UNIONPAY.getValue());
        merchantProviderParams.setStatus(UseStatusEnum.NO_USE.getValue());
        merchantProviderParams.setChannel_no("C1000001");
        merchantProviderParams.setParent_merchant_id(acquirerParams.getParent_merchant_id());
        merchantProviderParams.setProvider_merchant_id(acquirerParams.getProvider_merchant_id());
        merchantProviderParams.setProvider(ProviderEnum.PROVIDER_HAIKE.getValue());
        merchantProviderParams.setParams_config_status(2);
        merchantProviderParams.setPay_merchant_id(payMerchantId);
        merchantProviderParams.setStatus(UseStatusEnum.NO_USE.getValue());
        merchantProviderParams.setContract_rule(McConstant.RULE_GROUP_HAIKE);
        merchantProviderParams.setRule_group_id(McConstant.RULE_GROUP_HAIKE);
        HashMap<String, String> extraMap = Maps.newHashMap();
        extraMap.put("bankMerchNo", merchantProviderParams.getPay_merchant_id());
        merchantProviderParams.setExtra(CommonUtil.map2Bytes(extraMap));
        Optional<MerchantProviderParamsDO> unionParam = getUnionPayParams(acquirerParams.getMerchant_sn(), AcquirerTypeEnum.HAI_KE.getValue());
        if (unionParam.isPresent()) {
            return;
        }
        merchantProviderParamsMapper.insertSelective(merchantProviderParams);
    }

    private void saveOrUpdateParamsExt(Optional<MerchantProviderParamsDO> unionParam, String status, String message) {
        if (!unionParam.isPresent()) {
            return;
        }
        Optional<MerchantProviderParamsExtDO> unionPayStatus = merchantProviderParamsExtDAO.getUnionPayStatus(unionParam.get().getId());
        // 如果状态和文案没有发生变化就不需要去做更新
        if (unionPayStatus.isPresent() && Objects.equals(unionPayStatus.get().getExtField1(), status) && Objects.equals(unionPayStatus.get().getFailMessage(), message)){
            return;
        }
        MerchantProviderParamsExtDO extDO = new MerchantProviderParamsExtDO();
        extDO.setParamId(unionParam.get().getId());
        extDO.setExtField2(unionParam.get().getPayMerchantId());
        extDO.setType(MerchantProviderParamsExtDO.UNION_PAY);
        extDO.setExtField1(status);
        extDO.setExtra(JSON.toJSONString(CollectionUtil.hashMap("message", message)));
        if (unionPayStatus.isPresent()) {
            extDO.setId(unionPayStatus.get().getId());
            merchantProviderParamsExtDAO.updateMerchantParams(extDO);
        } else {
            merchantProviderParamsExtDAO.saveMerchantParametersExt(extDO);
        }
    }

    private Optional<MerchantProviderParamsDO> getUnionPayParams(String merchantSn, String acquirer) {
        List<McChannelDO> mcChannels = mcChannelDAO.getMcChannelByAcquirer(acquirer, PaywayEnum.UNIONPAY.getValue());
        for (McChannelDO mcChannel : mcChannels) {
            Optional<MerchantProviderParamsDO> merChantProviderParams = merchantProviderParamsDAO.getMerChantProviderParams(merchantSn, mcChannel.getChannelNo(), PaywayEnum.UNIONPAY.getValue());
            if (merChantProviderParams.isPresent()) {
                return merChantProviderParams;
            }
        }
        return Optional.empty();

    }

}
