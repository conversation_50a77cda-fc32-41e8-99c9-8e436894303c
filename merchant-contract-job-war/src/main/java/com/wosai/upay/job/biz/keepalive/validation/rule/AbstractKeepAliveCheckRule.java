package com.wosai.upay.job.biz.keepalive.validation.rule;

import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 商户规则抽象基类
 */
public abstract class AbstractKeepAliveCheckRule implements KeepAliveCheckRule {
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    @Override
    public final KeepAliveCheckRuleResult execute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        long startTime = System.currentTimeMillis();

        try {
            logger.debug("开始执行规则，类型: {}, 商户SN: {}", getRuleType(), context.getMerchantSn());

            // 执行具体的规则逻辑
            KeepAliveCheckRuleResult result = doExecute(context, ruleConfig);

            long duration = System.currentTimeMillis() - startTime;
            logger.debug("规则执行完成，类型: {}, 商户SN: {}, 结果: {}, 耗时: {}ms",
                    getRuleType(), context.getMerchantSn(), result.isPassed(), duration);

            return result;

        } catch (Exception e) {
            logger.error("规则执行异常，类型: {}, 商户SN: {}", getRuleType(), context.getMerchantSn(), e);
            return KeepAliveCheckRuleResult.failure(getRuleType().getDescription(), "规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }
    
    /**
     * 具体的规则执行逻辑，由子类实现
     */
    protected abstract KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig);
    
    /**
     * 创建成功结果
     */
    protected KeepAliveCheckRuleResult createSuccessResult(String message) {
        return KeepAliveCheckRuleResult.success(getRuleType().getDescription(), message);
    }
    
    /**
     * 创建失败结果
     */
    protected KeepAliveCheckRuleResult createFailureResult(String message, String errorCode) {
        return KeepAliveCheckRuleResult.failure(getRuleType().getDescription(), message, errorCode);
    }
}