package com.wosai.upay.job.refactor.biz.acquirer;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.utils.object.ConvertUtil;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;
import com.wosai.upay.job.refactor.service.factory.AbstractStrategyFactory;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * 收单机构门面（本身已经具备工厂能力）
 * 为什么写这个类？
 * 1. 为了保持调用方和实现类的解耦
 * 2. 为了方便扩展，管理
 *
 * <AUTHOR>
 * @date 2023/11/28 10:09
 */
@Component
public class AcquirerFacade extends AbstractStrategyFactory<AcquirerSharedAbility, String> {

    @Resource
    private ContractStatusDAO contractStatusDAO;


    /**
     * 根据收单机构类型获取对应的共有能力处理类
     *
     * @param acquirer 收单机构类型
     * @return 收单机构处理类
     */
    public Optional<AcquirerSharedAbility> getSharedAbilityByAcquirer(String acquirer) {
        if (StringUtils.isBlank(acquirer)) {
            return Optional.empty();
        }
        return super.getStrategy(acquirer);
    }

    /**
     * 根据收单机构类型枚举获取对应的共有能力处理类
     *
     * @param acquirerTypeEnum 收单机构类型枚举
     * @return 收单机构处理类
     */
    public AcquirerSharedAbility getSharedAbilityByAcquirerEnum(AcquirerTypeEnum acquirerTypeEnum) {
        if (Objects.isNull(acquirerTypeEnum)) {
            throw new ContractBizException("acquirer type is null");
        }
        Optional<AcquirerSharedAbility> strategy = super.getStrategy(acquirerTypeEnum.getValue());
        if (!strategy.isPresent()) {
            throw new ContractBizException("acquirer type is not supported, acquirer type: " + acquirerTypeEnum.getValue());
        }
        return strategy.get();
    }

    /**
     * 根据商户号，获取商户所在收单机构对应的共有能力处理类
     *
     * @param merchantSn 商户号
     * @return 收单机构处理类
     */
    public Optional<AcquirerSharedAbility> getSharedAbilityBySn(String merchantSn) {
        Optional<ContractStatusDO> contractStatus = contractStatusDAO.getByMerchantSn(merchantSn);
        if (!contractStatus.isPresent()) {
            return Optional.empty();
        }
        String acquirer = contractStatus.get().getAcquirer();
        if (StringUtils.isBlank(acquirer)) {
            return Optional.empty();
        }
        return super.getStrategy(acquirer);
    }


    /**
     * 根据收单机构类型和class获取对应的独有能力处理类
     *
     * @param acquirerTypeEnum 收单机构类型
     * @param clazz            类型
     * @param <T>              泛型
     * @return 收单机构处理类
     */
    public <T> T getUniqueAbilityAcquirer(AcquirerTypeEnum acquirerTypeEnum, Class<T> clazz) {
        return ConvertUtil.castToExpectedType(getSharedAbilityByAcquirerEnum(acquirerTypeEnum), clazz)
                .orElseThrow(() -> new ContractBizException("acquirer type is not supported, acquirer type: " + acquirerTypeEnum.getValue()));
    }


}
