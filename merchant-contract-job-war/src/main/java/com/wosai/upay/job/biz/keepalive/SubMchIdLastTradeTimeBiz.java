package com.wosai.upay.job.biz.keepalive;

import com.wosai.upay.job.refactor.dao.SubMchIdLastTradeTimeDAO;
import com.wosai.upay.job.refactor.model.entity.ProviderParamsKeepaliveTaskDO;
import com.wosai.upay.job.refactor.model.entity.SubMchIdLastTradeTimeDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/8/26
 */
@Component
public class SubMchIdLastTradeTimeBiz {

    @Autowired
    private SubMchIdLastTradeTimeDAO subMchIdLastTradeTimeDAO;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void insertOrUpdateSubMchIdLastTradeTime(ProviderParamsKeepaliveTaskDO taskDO,
                                                    long payTime) {
        Optional<SubMchIdLastTradeTimeDO> subMchIdLastTradeTimeDO =
                subMchIdLastTradeTimeDAO.selectByMerchantSnAndSubMchId(taskDO.getMerchantSn(), taskDO.getSubMchId());

        if (subMchIdLastTradeTimeDO.isPresent() && subMchIdLastTradeTimeDO.get().getTradeTime() < payTime) {
            SubMchIdLastTradeTimeDO lastTradeTimeDO = subMchIdLastTradeTimeDO.get();
            lastTradeTimeDO.setTradeTime(payTime);
            subMchIdLastTradeTimeDAO.update(lastTradeTimeDO);
        } else {
            SubMchIdLastTradeTimeDO lastTradeTimeDO = new SubMchIdLastTradeTimeDO();
            lastTradeTimeDO.setMerchantSn(taskDO.getMerchantSn());
            lastTradeTimeDO.setSubMchId(taskDO.getSubMchId());
            lastTradeTimeDO.setProvider(taskDO.getProvider());
            lastTradeTimeDO.setPayway(taskDO.getPayway());
            lastTradeTimeDO.setTradeTime(payTime);
            subMchIdLastTradeTimeDAO.insertOne(lastTradeTimeDO);
        }

    }
}
