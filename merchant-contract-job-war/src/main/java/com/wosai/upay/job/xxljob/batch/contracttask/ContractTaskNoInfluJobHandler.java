package com.wosai.upay.job.xxljob.batch.contracttask;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * xxl_job_desc: 进件任务-处理不影响主任务的子任务
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("ContractTaskNoInfluJobHandler")
public class ContractTaskNoInfluJobHandler extends AbstractBatchJobHandler<ContractSubTask> {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private SubTaskHandlerContext subTaskHandlerContext;
    @Autowired
    private ChatBotUtil chatBotUtil;

    private final static List<String> CHANNELS = Lists.newArrayList(ProviderUtil.LKL_PROVIDER_CHANNEL, ProviderUtil.PSBC_CHANNEL, ProviderUtil.UMS_PROVIDER_CHANNEL, ProviderUtil.CGB_CHANNEL, ProviderUtil.CCB_CHANNEL, ProviderUtil.FUYOU_CHANNEL, ProviderUtil.LKL_V3_PROVIDER_CHANNEL, ProviderUtil.LZB_CHANNEL);

    @Override
    public List<ContractSubTask> queryTaskItems(BatchJobParam param) {
        return contractSubTaskMapper.getNoInfluenceSubTask(param.getBatchSize(), StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()));
    }

    @Override
    public String getLockKey(ContractSubTask subTask) {
        // 锁的粒度到商户的业务类型
        return "ContractTaskNoInfluJobHandler:" + subTask.getMerchant_sn() + subTask.getTask_type();
    }

    @Override
    public void doHandleSingleData(ContractSubTask subTask) {
        try {
            //1，再次查询
            ContractSubTask subTaskLast = contractSubTaskMapper.selectByPrimaryKey(subTask.getId());
            //2，判断状态
            if (subTaskLast.getStatus().equals(TaskStatus.SUCCESS.getVal()) || subTaskLast.getStatus().equals(TaskStatus.FAIL.getVal())) {
                return;
            }
            //判断请求等待回调
            if (CHANNELS.contains(subTaskLast.getChannel()) && StrUtil.isNotEmpty(subTaskLast.getContract_id())) {
                //富友云闪付进件任务
                if (ProviderUtil.FUYOU_CHANNEL.equals(subTaskLast.getChannel())
                        && ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue().equals(subTaskLast.getTask_type())
                        && Objects.equals(subTaskLast.getPayway(), PaywayEnum.UNIONPAY.getValue())) {
                    //修改云闪付sub_task的状态
                    contractSubTaskMapper.updateByPrimaryKey(new ContractSubTask().setId(subTaskLast.getId()).setStatus(ContractSubTaskProcessStatusEnum.WAIT_CALL_BACK.getValue()));
                }
                return;
            }

            if (subTaskLast.getRetry() > 10) {
                contractSubTaskMapper.updateByPrimaryKey(new ContractSubTask().setId(subTaskLast.getId()).setStatus(TaskStatus.FAIL.getVal()));
                return;
            }

            ContractTask task = contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id());
            subTaskHandlerContext.handle(task, subTaskLast);

        } catch (Exception e) {
            log.error("处理不影响主任务的子任务失败，商户号{},业务类型{}", subTask.getMerchant_sn(), subTask.getTask_type(), e);
            chatBotUtil.sendMessageToContractWarnChatBot("处理不影响主任务的子任务失败,商户号:" + subTask.getMerchant_sn() +
                    ",业务类型:" + subTask.getTask_type() + "错误信息:" + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
