package com.wosai.upay.job.util.bcs;

import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.CipherParameters;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithID;
import org.bouncycastle.crypto.signers.SM2Signer;

/**
 * SM2签名工具类
 * <p>
 * 提供SM2验签功能，支持默认用户ID和自定义用户ID。
 * </p>
 * <p>
 * 注意：此工具类依赖Bouncy Castle库。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public class Sm2Util {

    private static final byte[] defaultUserID = "1234567812345678".getBytes();

    private static final X9ECParameters sm2p256v1 = GMNamedCurves.getByName("sm2p256v1");

    /**
     * sm2验签
     * <p>
     * userId使用默认：1234567812345678
     *
     * @param publicKey 公钥，二进制数据
     * @param sourceData 待验签数据
     * @param signData 签名值
     * @return 返回是否成功
     */
    public static boolean verifySign(byte[] publicKey, byte[] sourceData, byte[] signData) {
        return verifySign(defaultUserID, publicKey, sourceData, signData);
    }

    /**
     * sm2验签
     *
     * @param userId ID值，若无约定，使用默认：1234567812345678
     * @param publicKey 公钥，二进制数据
     * @param sourceData 待验签数据
     * @param signData 签名值
     * @return 返回是否成功
     */
    public static boolean verifySign(byte[] userId, byte[] publicKey, byte[] sourceData, byte[] signData) {

        if (publicKey.length == 64) {
            byte tmp[] = new byte[65];
            System.arraycopy(publicKey, 0, tmp, 1, publicKey.length);
            tmp[0] = 0x04;
            publicKey = tmp;
        }

        ECDomainParameters parameters =
            new ECDomainParameters(sm2p256v1.getCurve(), sm2p256v1.getG(), sm2p256v1.getN());
        ECPublicKeyParameters pubKeyParameters =
            new ECPublicKeyParameters(sm2p256v1.getCurve().decodePoint(publicKey), parameters);
        SM2Signer signer = new SM2Signer();
        CipherParameters param;
        if (userId != null) {
            param = new ParametersWithID(pubKeyParameters, userId);
        } else {
            param = pubKeyParameters;
        }
        signer.init(false, param);
        signer.update(sourceData, 0, sourceData.length);
        return signer.verifySignature(signData);
    }
}
