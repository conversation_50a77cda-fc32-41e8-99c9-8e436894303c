package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.merchant.contract.model.provider.ChannelParam;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 组装进件参数
 *
 * <AUTHOR>
 * @date 2019-11-11
 */
@Component
public class ContractParamsBiz {

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private RuleContext ruleContext;

    private static final Cache<String, Object> CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    public <T> T buildContractParams(String channel, Class<T> tClass) {
        ContractChannel contractChannel = ruleContext.getContractChannel(channel);
        return buildParam(contractChannel, null, tClass);
    }

    public <T> T buildContractParamsByPayMchId(String payMerchantId, Class<T> tClass) {
        Optional<MerchantProviderParamsDO> paramsOptional = merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId(payMerchantId);
        if (!paramsOptional.isPresent()) {
            throw new CommonPubBizException("构建 " + tClass.getName() + " 失败");
        }
        MerchantProviderParamsDO merchantProviderParamsDO = paramsOptional.get();
        return buildContractParams(String.valueOf(merchantProviderParamsDO.getProvider()), merchantProviderParamsDO.getPayway(), merchantProviderParamsDO.getChannelNo(), tClass);
    }

    public <T> T buildContractParamsByParams(MerchantProviderParams providerParams, Class<T> tClass) {
        String provider = BeanUtil.getPropString(providerParams, "provider", "-1");
        int payway = BeanUtil.getPropInt(providerParams, "payway", -1);
        String channelNo = BeanUtil.getPropString(providerParams, "channel_no", "");
        return buildContractParams(provider, payway, channelNo, tClass);
    }

    public <T> T buildContractParams(String provider, int payway, String channelNo, Class<T> tClass) {
        ContractChannel contractChannel = ruleContext.getContractChannel(payway, provider, channelNo);
        return buildParam(contractChannel, null, tClass);
    }


    public <T> T buildContractParamsByContractSubTask(ContractSubTask contractSubTask, Class<T> tClass) {
        ContractChannel contractChannel = ruleContext.getContractRule(contractSubTask.getContract_rule()).getContractChannel();
        return buildParam(contractChannel, contractSubTask, tClass);
    }

    public <T> T buildParam(ContractChannel contractChannel, ContractSubTask sub, Class<T> tClass) {
        String metadata = JSON.toJSONString(contractChannel.getChannelParam());
        T target = JSON.parseObject(metadata, tClass);
        if (target == null) {
            throw new CommonPubBizException("构建 " + tClass.getName() + " 失败");
        }
        BeanUtils.copyProperties(contractChannel, target);
        if (target instanceof ChannelParam) {
            ((ChannelParam) target).setPayway(contractChannel.getPayway());
            ((ChannelParam) target).setProvider(contractChannel.getProvider());
            if (sub != null) {
                ((ChannelParam) target).setContract_rule(sub.getContract_rule());
                ((ChannelParam) target).setRule_group_id(sub.getRule_group_id());
            }
        }
        return target;
    }
}
