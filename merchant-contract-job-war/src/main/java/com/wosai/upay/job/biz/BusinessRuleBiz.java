package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.model.NetInRuleGroups;
import com.wosai.upay.job.refactor.model.bo.ContractRuleDecisionEvaluateResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.enums.NetInSceneEnum;
import com.wosai.upay.job.refactor.service.McRulesDecisionService;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description:某些特殊产品需要的规则 譬如 某个地区升级M3 通联渠道报备规则等
 * <AUTHOR>
 * Date 2020/3/24 9:26 上午
 **/
@Component
@Slf4j
public class BusinessRuleBiz {

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;


    /**
     * 是否需要开启
     */
    public boolean shouldOpenGold() {
        Map route = applicationApolloConfig.getWechatGoldRoute();
        if (WosaiMapUtils.isEmpty(route)) {
            return false;
        }
        return WosaiMapUtils.getBooleanValue(route, "all", false);
    }

    public NetInRuleGroups getRuleGroupId(String merchantSn) {
        return getRuleGroupId(merchantSn, null);
    }

    @Resource
    private McRulesDecisionService mcRulesDecisionService;

    /**
     * 获取报备规则组
     * 双通道报备
     *
     * @param merchantSn     商户号
     * @param organizationId 组织id
     * @return 进件通道组
     */
    public NetInRuleGroups getRuleGroupId(String merchantSn, String organizationId) {
        Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>> evaluateResult = null;
        try {
            evaluateResult = mcRulesDecisionService.chooseGroupBySnAndOrg(merchantSn, organizationId, NetInSceneEnum.BUSINESS_OPENING);
            Set<GroupCombinedStrategyDetailDO> groupsBOS = evaluateResult.get_1().stream()
                    .sorted(GroupCombinedStrategyDetailDO::compareTo).collect(Collectors.toCollection(LinkedHashSet::new));
            return buildNetInRuleGroups(groupsBOS);
        } catch (Exception e) {
            log.error("新逻辑获取进件规则通道组失败,商户号:{},组织id:{},结果:{}", merchantSn, organizationId, JSON.toJSONString(evaluateResult), e);
            throw new CommonPubBizException("进件失败，匹配收单机构异常，请在线咨询客服转技术核实");
        }
    }

    public NetInRuleGroups getRuleGroupId(MerchantFeatureBO merchantFeatureBO) {
        Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>> evaluateResult = null;
        try {
            evaluateResult = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO);
            Set<GroupCombinedStrategyDetailDO> groupsBOS = evaluateResult.get_1().stream()
                    .sorted(GroupCombinedStrategyDetailDO::compareTo).collect(Collectors.toCollection(LinkedHashSet::new));
            return buildNetInRuleGroups(groupsBOS);
        } catch (Exception e) {
            log.error("新逻辑获取进件规则通道组失败,商户号:{},结果:{}", merchantFeatureBO.getMerchantSn(), JSON.toJSONString(evaluateResult), e);
            // 如果有第二个返回值（命中的禁用规则），则将其作为异常原因抛出
            if (Objects.nonNull(evaluateResult) && CollectionUtils.isNotEmpty(evaluateResult.get_2())) {
                throw new CommonPubBizException("进件失败，匹配收单机构异常，请在线咨询客服转技术核实，原因：" + JSON.toJSONString(evaluateResult.get_2()));
            }
            throw new CommonPubBizException("进件失败，匹配收单机构异常，请在线咨询客服转技术核实");
        }
    }

    private NetInRuleGroups buildNetInRuleGroups(Set<GroupCombinedStrategyDetailDO> groupsBOS) {
        if (CollectionUtils.isEmpty(groupsBOS)) {
            throw new CommonPubBizException("进件失败，根据商户特征未匹配到收单机构，请在线咨询客服");
        }
        NetInRuleGroups netInRuleGroups = new NetInRuleGroups();
        Iterator<GroupCombinedStrategyDetailDO> iterator = groupsBOS.iterator();
        if (iterator.hasNext()) {
            netInRuleGroups.setPrimaryRuleGroupId(iterator.next().getGroupId());
        }
        if (iterator.hasNext()) {
            netInRuleGroups.setSecondaryRuleGroupId(iterator.next().getGroupId());
        }
        return netInRuleGroups;
    }


}
