package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.enume.ChannelDelayQueryConfigEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.context.ContractTaskContext;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import com.wosai.upay.side.service.GeneralRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * xxl_job_desc: 进件任务-间连扫码入网任务
 */
@Slf4j
@Component("ContractTaskIndirectNetInJobHandler")
public class ContractTaskIndirectNetInJobHandler extends AbstractBatchJobHandler<ContractTaskContext> {

    @Autowired
    private GeneralRuleService generalRuleService;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private SubTaskHandlerContext subTaskHandlerContext;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Override
    public List<ContractTaskContext> queryTaskItems(BatchJobParam param) {
        String reviewComplete = generalRuleService.getFirstWeekDayAfterDate(
                LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        );
        List<ContractTask> tasks = contractTaskMapper.selectIndirectContractTaskTodo(
                StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()),
                param.getBatchSize(),
                applicationApolloConfig.getBankruleGroupIds()
        );

        return tasks.stream()
                .map(task -> new ContractTaskContext(task, reviewComplete))
                .collect(Collectors.toList());
    }

    @Override
    public String getLockKey(ContractTaskContext context) {
        return "ContractTaskIndirectNetInJobHandler:" + context.getTask().getMerchant_sn();
    }

    @Override
    public void doHandleSingleData(ContractTaskContext context) {
        ContractTask contractTask = context.getTask();
        String reviewComplete = context.getReviewComplete();
        String merchantSn = contractTask.getMerchant_sn();
        try {
            contractTask = contractTaskMapper.selectByPrimaryKey(contractTask.getId());
            if (contractTask.getStatus().equals(TaskStatus.SUCCESS.getVal()) || contractTask.getStatus().equals(TaskStatus.FAIL.getVal())) {
                return;
            }
            if (contractTask.getStatus().equals(TaskStatus.PENDING.getVal())) {
                //处理预计审核完成时间
                contractTask.setComplete_at(StringUtil.parseDate(reviewComplete));
            }
            List<ContractSubTask> contractSubTaskList = contractSubTaskMapper
                    .selectByMerchantSnAndPTaskIdAndScheduleStatus(merchantSn, contractTask.getId(), 1);
            if (CollectionUtils.isEmpty(contractSubTaskList)) {
                log.info("进件拉卡拉 contractTask:id {} merchant_sn {} 暂无可处理子任务", contractTask.getId(), merchantSn);
            }
            for (ContractSubTask contractSubTask : contractSubTaskList) {
                if (ProviderUtil.CHANNELS.contains(contractSubTask.getChannel()) && !StringUtils.isEmpty(contractSubTask.getContract_id()) && !"resubmit".equals(contractSubTask.getContract_id())) {
                    // 等待回调，不再调度，等回调后再调度
                    Date nextPriority = ChannelDelayQueryConfigEnum.getNextPriority(contractSubTask.getCreate_at().getTime(), contractSubTask.getChannel());
                    contractTaskMapper.updatePriority(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(nextPriority), contractTask.getId());
                    continue;
                }
                log.info(" merchantSn {}  channel {}  contractId {} handleTask", contractSubTask.getMerchant_sn(), contractSubTask.getChannel(), contractSubTask.getContract_id());
                subTaskHandlerContext.handle(contractTask, contractSubTask);
            }
        } catch (Exception e) {
            log.error(" processContractTasks error,商户号为 {}", merchantSn, e);
            chatBotUtil.sendMessageToContractWarnChatBot("processContractTasks error" + ExceptionUtil.getThrowableMsg(e) + "商户号为" + merchantSn);
        }
    }
}