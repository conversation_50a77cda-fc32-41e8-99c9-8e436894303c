package com.wosai.upay.job.xxljob.direct.contracttask;

import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * xxl_job_desc: 进件任务-拉卡拉任务状态查询
 * 拉卡拉任务查询
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("ContractTaskQueryLklContractStatusJobHandler")
public class ContractTaskQueryLklContractStatusJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private LklV3Service lklV3Service;
    @Autowired
    private ContractParamsBiz contractParamsBiz;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public String getLockKey() {
        return "ContractTaskQueryLklContractStatusJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        long currentTimeMillis = System.currentTimeMillis();
        List<ContractSubTask> subTasks = contractSubTaskMapper.selectLKLV3ContractQueryTask(param.getBatchSize(),
                StringUtil.formatDate(currentTimeMillis - param.getQueryTime()),
                StringUtil.formatDate(currentTimeMillis));
        try {
            if (StringUtil.listEmpty(subTasks)) {
                return;
            }
            subTasks.forEach(subTask -> {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                LklV3Param v3Param = contractParamsBiz.buildContractParamsByContractSubTask(subTask, LklV3Param.class);
                lklV3Service.queryTaskByContractId(subTask.getContract_id(), v3Param);
                contractSubTaskMapper.updateByPrimaryKey(
                        new ContractSubTask()
                                .setId(subTask.getId())
                                .setPriority(new Date())
                );
            });
        } catch (Exception e) {
            log.error("queryLklV3contractStatus error", e);
            chatBotUtil.sendMessageToContractWarnChatBot("queryLklV3contractStatus error" + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
