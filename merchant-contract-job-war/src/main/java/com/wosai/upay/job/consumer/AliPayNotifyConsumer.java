package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.pub.alipay.authinto.event.AlipayAppGatewayNotifyEvent;
import com.wosai.upay.job.model.direct.AliAuthorizeCallBack;
import com.wosai.upay.job.service.AliDirectApplyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 支付宝开放平台消息消费
 * <p>
 * https://d1a66bevqh7.feishu.cn/wiki/QU3hwCQ5Ni1D6XkX1UPcIQU0n3b
 * https://opendocs.alipay.com/common/02km9i?pathHash=9b9d5fed
 *
 * <AUTHOR>
 * @date 2024/9/9
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class AliPayNotifyConsumer {

    @Autowired
    private AliDirectApplyService aliDirectApplyService;

    @KafkaListener(topics = "events_PAY_alipay-authinto_notify", containerFactory = "merchantActiveKafkaListenerContainerFactory")
    public void listener(List<AlipayAppGatewayNotifyEvent> eventList) {
        if (WosaiCollectionUtils.isEmpty(eventList)) {
            return;
        }

        for (AlipayAppGatewayNotifyEvent event : eventList) {
            String msg = StandardCharsets.UTF_8.decode(event.getNotifyParams()).toString();
            log.info("AliPayNotify {}", msg);
            Map<String, String> params = JSON.parseObject(msg, Map.class);
            if ("open_app_auth_notify".equals(params.get("notify_type"))) {
                try {
                    authAliPay(params);
                } catch (Exception e) {
                    log.error("AliPayNotify consumer error {}", msg);
                }
            }
        }
    }

    private void authAliPay(Map<String, String> params) {
        String bizContentString = params.get("biz_content");
        if (WosaiStringUtils.isEmpty(bizContentString)) {
            return;
        }
        Map bizContent = JSON.parseObject(bizContentString, Map.class);
        Map detail = MapUtils.getMap(bizContent, "detail");
        Map notifyContext = MapUtils.getMap(bizContent, "notify_context");
        Map triggerContext = MapUtils.getMap(notifyContext, "trigger_context");
        String batchNo = MapUtils.getString(triggerContext, "out_biz_no");
        AliAuthorizeCallBack aliAuthorizeCallBack = JSON.parseObject(JSON.toJSONString(detail), AliAuthorizeCallBack.class);
        aliAuthorizeCallBack.setBatchNo(batchNo);
        aliDirectApplyService.authorizeMerchant(aliAuthorizeCallBack);
    }
}
