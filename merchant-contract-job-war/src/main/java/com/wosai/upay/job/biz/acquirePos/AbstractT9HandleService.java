package com.wosai.upay.job.biz.acquirePos;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.PosConstant;
import com.wosai.upay.job.biz.AgreementBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.enume.PlatformEnum;
import com.wosai.upay.job.mapper.ForeignCardMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.PreAuthApplyMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryReq;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.dto.AcquirerMerchantDto;
import com.wosai.upay.job.model.dto.acquirePos.CrmOpenCheckDTO;
import com.wosai.upay.job.model.dto.acquirePos.PosActiveInfo;
import com.wosai.upay.job.model.dto.acquirePos.PosActiveInfoDTO;
import com.wosai.upay.job.model.dto.acquirePos.UnbindDTO;
import com.wosai.upay.job.model.lklV3Pos.ApplyPosRequest;
import com.wosai.upay.job.refactor.dao.DirectStatusDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.audit.api.model.Terminal;
import com.wosai.upay.merchant.contract.model.fuyou.response.ModifyCancelResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.util.*;

import static com.wosai.upay.job.Constants.PosConstant.NET_IN_FAIL;
import static com.wosai.upay.job.service.LKlV3PosServiceImpl.convertCentsToYuanFormatted;


/**
 * @Description: 抽象出不同收单机构使用的通用逻辑
 * <AUTHOR>
 * @Date 2024/2/26 17:26
 */
@Slf4j
public abstract class AbstractT9HandleService implements T9HandleService {

    public static final String BANKCARD_FEE = "bankcard_fee";
    public static final String TRADECOMBO_ID = "tradeComboId";
    public static final String FEEMAP = "feeMap";
    @Autowired
    protected MerchantProviderParamsService merchantProviderParamsService;
    @Autowired
    protected MerchantService merchantService;
    @Autowired
    protected ContractStatusService contractStatusService;
    @Autowired
    protected TerminalService terminalService;
    @Autowired
    @Lazy
    protected T9HandleFactory factory;
    @Autowired
    protected McAcquirerDAO mcAcquirerDAO;
    @Autowired
    protected DirectStatusBiz directStatusBiz;
    @Autowired
    protected MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    protected TradeConfigService tradeConfigService;
    @Autowired
    protected SupportService supportService;
    @Autowired
    protected SubBizParamsBiz subBizParamsBiz;
    @Autowired
    protected FeeRateService feeRateService;

    @Autowired
    protected ComposeAcquirerBiz composeAcquirerBiz;

    @Autowired
    protected ForeignCardMapper foreignCardMapper;

    @Resource
    protected DirectStatusDAO directStatusDAO;

    @Autowired
    protected AgreementBiz agreementBiz;


    @Autowired
    protected PreAuthApplyMapper preAuthApplyMapper;

    protected String ERROR_MEMO = "未实现";

    /**
     * 检查是否存在其他通道的T9绑定
     *
     * @param merchantId 商户ID，用于查询终端绑定信息
     * @param vendorAppAppidList 供应商应用ID列表，用于比对是否绑定在其他通道
     * @throws CommonPubBizException 如果存在其他通道的T9绑定，则抛出异常
     */
    public void existOtherT9(String merchantId, List<String> vendorAppAppidList) {
        // 检查列表中任意一个供应商应用ID是否绑定了激活状态的终端
        boolean anyMatch = vendorAppAppidList.stream().anyMatch(vendor -> {
            // 构建查询过滤条件
            Map filter = CollectionUtil.hashMap(Terminal.VENDOR_APP_APPID, vendor, Terminal.STATUS, Terminal.STATUS_ACTIVATED);
            // 根据过滤条件查询终端信息
            ListResult terminals = terminalService.getTerminals(merchantId, null, null, filter);
            // 处理查询结果为空的情况
            ListResult listResult = Optional.ofNullable(terminals).orElseGet(ListResult::new);
            // 判断是否存在绑定关系
            return listResult.getTotal() > 0;
        });
        // 如果存在绑定关系，则抛出异常
        if (anyMatch) {
            throw new CommonPubBizException(PosConstant.EXIST_OTHER_POS);
        }
    }

    /**
     * 对CRM开放平台进行检验的接口方法。
     * 
     * @param dto 包含商户信息和收单机构信息的数据传输对象。
     *             其中需要商户编号（merchantSn）和期望接入的收单机构（openAcquire）。
     * @throws CommonPubBizException 如果商户未成功接入所指定的收单机构，或者当前正在使用的收单机构与期望接入的不一致时抛出。
     */
    @Override
    public void crmOpenCheck(CrmOpenCheckDTO dto) {
        // 根据商户号获取商户信息
        String merchantSn = dto.getMerchantSn();
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        String merchantId = merchant.getId();
        
        // 获取商户所有收单机构信息
        List<AcquirerMerchantDto> acquirerMerchantInfo = merchantProviderParamsService.getAcquirerMerchantInfo(merchantId);
        
        // 检查是否已成功接入指定的收单机构
        String openAcquire = dto.getAcquire();

        // 获取指定收单机构的中文名称
        final String acquireName = mcAcquirerDAO.getAcquirerName(openAcquire);
        boolean match = acquirerMerchantInfo.parallelStream().anyMatch(info -> info.getAcquirer().contains(openAcquire));
        // 如果未成功接入指定收单机构，则抛出异常
        if (!match) {
            throw new CommonPubBizException(String.format(NET_IN_FAIL, acquireName,acquireName));
        }
        checkBankCardConsistence(dto);
        // 获取当前商户正在使用的收单机构
        ContractStatus contractStatus = Optional.ofNullable(contractStatusService.selectByMerchantSn(merchantSn))
                .orElseGet(ContractStatus::new);
        String usingAcquire = contractStatus.getAcquirer();
        
        if (Objects.equals(usingAcquire, openAcquire)) {
            // 如果当前使用的收单机构即为所期望接入的机构，则进行进一步的检验
            List<String> otherAcquireVenderList = factory.otherAcquireVenderList(openAcquire);
            existOtherT9(merchantId, otherAcquireVenderList);
            unionPayOpenStatusCheck(merchantId, openAcquire, acquireName);
            return;
        }
        
        // 如果当前使用的收单机构为间接收单机构，则不允许
        List<String> indirectAcquirerList = mcAcquirerDAO.getIndirectAcquirerList();
        if (indirectAcquirerList.contains(usingAcquire)) {
            throw new CommonPubBizException(String.format(NET_IN_FAIL, acquireName, acquireName));
        }
        
        // 在银行通道中
        List<String> otherAcquireVenderList = factory.otherAcquireVenderList(openAcquire);
        existOtherT9(merchantId, otherAcquireVenderList);
        unionPayOpenStatusCheck(merchantId, openAcquire, acquireName);
    }

    private void unionPayOpenStatusCheck(String merchantId, String acquirer, String acquireName) {
        UnionPayOpenStatusQueryReq req = new UnionPayOpenStatusQueryReq();
        req.setMerchantId(merchantId);
        req.setAcquirer(acquirer);
        req.setPlatform(PlatformEnum.CRM);
        UnionPayOpenStatusQueryResp unionPayOpenStatusQueryResp = merchantProviderParamsService.queryUnionPayOpenStatus(req);
        if (!unionPayOpenStatusQueryResp.getStatus().equals(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)) {
            boolean fallBack = Boolean.TRUE.equals(unionPayOpenStatusQueryResp.getFallBack());
            throw new CommonPubBizException(
                    String.format(
                            PosConstant.UNION_PAY_OPEN_FAIL,acquireName,
                            fallBack || StringUtil.empty(unionPayOpenStatusQueryResp.getMessage())
                                    ? "请联系销售支持处理"
                                    : unionPayOpenStatusQueryResp.getMessage()
                    )
            );
        }
    }

    /**
     * 校验银行卡一致性
     *
     * @param dto 请求dto
     */
    public void checkBankCardConsistence(CrmOpenCheckDTO dto) {
        boolean consistence = subBizParamsBiz.checkBankCardConsistence(dto.getMerchantSn(), dto.getAcquire());
        if (!consistence) {
            throw new CommonPubBizException(SubBizParamsBiz.BANK_CARD_NOT_CONSISTENCE_ERROR_MSG);
        }
    }

    @Override
    public ContractResponse openPos(ApplyPosRequest request) {
        return null;
    }

    @Override
    public PosActiveInfo getPosActiveInfo(PosActiveInfoDTO dto) {
        throw new CommonPubBizException(ERROR_MEMO);
    }


    @Override
    public void fyTermCancel(UnbindDTO dto) {

    }



    @Override
    public ModifyCancelResponse fyModifyCancel(String merchantSn, String modifyNo) {
        return new ModifyCancelResponse();
    }


    @Override
    public void t9TermAdd(String terminalSn) throws CommonPubBizException {

    }

    @Override
    public void t9TermUnbind(String terminalSn) {

    }

    /**
     * 从交易测获取当前商户T9费率信息
     * @param merchantId 商户ID
     * @return 费率信息映射
     */
    public Map<String, Object> getT9FeeMap(String merchantId) {
        Map<String, Object> feeMap = new HashMap<>();

        Optional<Map> bankcardFee = Optional.ofNullable(tradeConfigService.getAnalyzedMerchantConfigsByPayWayList(merchantId, new int[]{PaywayEnum.BANK_CARD.getValue()}))
                .filter(list -> !list.isEmpty())
                .map(list -> (Map<String, String>) list.get(0))
                .map(bankMap -> MapUtils.getMap(bankMap, "bankcard_fee"));

        if (bankcardFee.isPresent()) {
            Map<String, Object> map = bankcardFee.get();
            feeMap.put("credit", MapUtils.getMap(map, "credit"));

            Map<String, String> debitMap = MapUtils.getMap(map, "debit");
            feeMap.put("debit", debitMap);
            if(debitMap.containsKey("max") && Objects.nonNull(debitMap.get("max"))) {
                final String max = MapUtils.getString(debitMap, "max");
                final String yuan = convertCentsToYuanFormatted(max);
                debitMap.put("max",yuan);
            }
            feeMap.put("debit",debitMap);
        }
        return feeMap;
    }

}
