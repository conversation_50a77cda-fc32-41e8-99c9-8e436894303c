package com.wosai.upay.job.xxljob.direct;

import com.alibaba.fastjson.JSON;
import com.wosai.common.exception.CommonException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.ChangeTradeParamsBiz;
import com.wosai.upay.job.biz.JdBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.exception.MerchantContractException;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.PayWayConfigChangeMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.PayWayConfigChange;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.DefaultValueUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * xxl_job_desc: 通道切换任务
 * @Author: jerry
 * @date: 2019/4/13 11:08
 * @Description:切换通道任务
 */
@Slf4j
@Component("ChangeChannelJobHandler")
public class ChangeChannelJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private PayWayConfigChangeMapper payWayConfigChangeMapper;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private ChangeTradeParamsBiz tradeParamsBiz;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    SubBizParamsBiz subBizParamsBiz;

    @Autowired
    private JdBiz jdBiz;

    @Override
    public String getLockKey() {
        return "ChangeChannelJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            long currentTimeMillis = System.currentTimeMillis();
            List<PayWayConfigChange> payWayConfigChangeList = payWayConfigChangeMapper.selectByUpdateAt(StringUtil.formatDate(currentTimeMillis - param.getQueryTime()), StringUtil.formatDate(currentTimeMillis), param.getBatchSize());
            if (CollectionUtils.isEmpty(payWayConfigChangeList)) {
                log.info("query change channel time {} limit{} no record", param.getQueryTime(), param.getBatchSize());
                return;
            }
            payWayConfigChangeList.forEach(payWayConfigChange -> {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                doChangeChannel(payWayConfigChange);
            });

        } catch (Exception e) {
            log.error("ChangeChannelJobHandler changeChannel error", e);
        }
    }


    private void doChangeChannel(PayWayConfigChange payWayConfigChange) {
        String payWay = payWayConfigChange.getPayway().toString();
        String merchantSn = payWayConfigChange.getMerchant_sn();
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            log.info("{} 商户不存在", merchantSn);
            payWayConfigChange.setStatus(PayWayConfigChange.STATUS_BIZ_FAIL);
            payWayConfigChangeMapper.updateByPrimaryKeySelective(payWayConfigChange);
            return;
        }

        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map resp = JSON.parseObject(payWayConfigChange.getBody(), Map.class);

        // 新流程切交易参数
        String merchantProviderParamsId = WosaiMapUtils.getString(resp, "merchantProviderParamsId");
        if (WosaiStringUtils.isNotEmpty(merchantProviderParamsId)) {
            try {
                boolean status = tradeParamsBiz.changeTradeParams(merchantProviderParamsId, null, false, subBizParamsBiz.getPayTradeAppId());
                if (status) {
                    updateChangeConfigSuccess(payWayConfigChange);
                    try {
                        //设置京东白条套餐
                        jdBiz.setJdComb(merchantProviderParamsId);
                    } catch (Exception e) {
                        log.error("merchantSn {} payway {} 设置白条套餐报错, merchantProviderParamsId : {}", merchantSn, payWay, merchantProviderParamsId, e);
                    }
                }
            } catch (Exception e) {
                if (e instanceof CommonException || e instanceof com.wosai.upay.common.exception.CommonException || e instanceof MerchantContractException) {
                    updateChangeConfigFail(payWayConfigChange, e);
                } else {
                    chatBotUtil.sendMessageToContractWarnChatBot(String.format("%s %s 交易参数切换失败, merchantProviderParamsId : %s exception msg: %s", merchantSn, payWay, merchantProviderParamsId, ExceptionUtil.getThrowableMsg(e)));
                    log.error("merchantSn {} payway {} 交易参数切换失败, merchantProviderParamsId : {}", merchantSn, payWay, merchantProviderParamsId, e);
                }
            }

        } else {
            Map trade = WosaiMapUtils.getMap(resp, "tradeParam");
            String subMch = WosaiMapUtils.getString(trade, "weixin_merchant_id");
            if (!StringUtils.isEmpty(subMch)) {
                MerchantProviderParams param = merchantProviderParamsMapper.getByPayMerchantId(subMch);
                if (param != null) {
                    payWayConfigChange.setStatus(PayWayConfigChange.STATUS_SUCCESS);
                    payWayConfigChangeMapper.updateByPrimaryKeySelective(payWayConfigChange);
                    return;
                }
            }
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("%s %s 交易参数切换失败, merchantProviderParamsId 为空", merchantSn, merchantId));
        }

    }


    private void updateChangeConfigSuccess(PayWayConfigChange payWayConfigChange) {
        PayWayConfigChange update = new PayWayConfigChange().setId(payWayConfigChange.getId()).setStatus(PayWayConfigChange.STATUS_SUCCESS);
        payWayConfigChangeMapper.updateByPrimaryKeySelective(update);
    }

    private void updateChangeConfigFail(PayWayConfigChange payWayConfigChange, Exception e) {
        payWayConfigChange.setStatus(PayWayConfigChange.STATUS_BIZ_FAIL);
        payWayConfigChangeMapper.updateByPrimaryKeySelective(payWayConfigChange);
        log.info(" merchantSn {} payway {} 交易参数切换失败 {}", payWayConfigChange.getMerchant_sn(), payWayConfigChange.getPayway(), payWayConfigChange.getId(), e);
    }
}
