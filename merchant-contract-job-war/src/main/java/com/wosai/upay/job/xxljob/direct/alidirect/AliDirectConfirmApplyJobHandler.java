package com.wosai.upay.job.xxljob.direct.alidirect;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.alipay.api.request.AlipayOpenAgentConfirmRequest;
import com.alipay.api.response.AlipayOpenAgentConfirmResponse;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.enume.AliDirectApplyStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.AliDirectApplyMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.AliDirectApply;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.monitor.MonitorObject;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.directPay.AlipayOpenAgentConfirmReq;
import com.wosai.upay.merchant.contract.service.AliPayDirectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.List;

/**
 * xxl_job_desc: 支付宝直连-确认申请单
 * 支付宝直连提交申请单任务
 */
@Slf4j
@Component("AliDirectConfirmApplyJobHandler")
public class AliDirectConfirmApplyJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private AliPayDirectService aliPayDirectService;

    @Autowired
    private AliDirectApplyMapper applyMapper;

    @Autowired
    private DirectStatusBiz directStatusBiz;

    @Autowired
    private ContractTaskBiz taskBiz;

    @Autowired
    private MonitorLog monitorLog;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Value("${ali.direct}")
    private String aliDirectDevCode;

    private static final String BATCH_STATUS_IS_FINAL = "BATCH_STATUS_IS_FINAL";

    @Override
    public String getLockKey() {
        return "AliDirectConfirmApplyJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        Long queryTime = param.getQueryTime();
        Integer queryLimit = param.getBatchSize();
        long currentTime = System.currentTimeMillis();
        List<AliDirectApply> applyList = applyMapper.getAppliesByPrioirtyAndStatus(
                DateFormatUtils.format(currentTime - queryTime, "yyyy-MM-dd HH:mm:ss"),
                DateFormatUtils.format(currentTime, "yyyy-MM-dd HH:mm:ss"),
                Lists.newArrayList(AliDirectApplyStatus.ALREADY_SUBMITTED.getVal()),
                queryLimit);
        applyList.forEach(apply -> {
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                doConfirmApply(apply);
            } catch (Throwable e) {
                log.error("confirm apply error {} ", apply.getMerchant_sn(), e);
                monitorLog.recordMonitor("支付宝直连", apply.getMerchant_sn() + "支付宝直连确认申请单" + apply.getId() + "异常" + ExceptionUtil.getThrowableMsg(e));
            }
        });
    }

    public void doConfirmApply(AliDirectApply apply) {
        String merchantSn = apply.getMerchant_sn();
        long start = System.currentTimeMillis();
        int code = 200;
        String message = "";
        try {
            AliCommResponse<AlipayOpenAgentConfirmRequest, AlipayOpenAgentConfirmResponse> response = aliPayDirectService.alipayOpenAgentConfirm(new AlipayOpenAgentConfirmReq().setBatchNo(apply.getBatch_no()));
            code = response.getCode();
            message = response.getMessage();
            //3. 判断是否成功 如果上一次确认接口超时，但是支付宝内部已经处理完成，此时再去调用该接口会返回事务已经达到终态，这种情况下直接将申请单改为审核中即可
            if (response.isSuccess() || (WosaiStringUtils.isNotEmpty(response.getResp().getSubCode()) && BATCH_STATUS_IS_FINAL.equals(response.getResp().getSubCode()))) {
                //3.1  成功 更新申请单为审核中，5分钟之后再查
                applyMapper.updateByPrimaryKeySelective(new AliDirectApply().setId(apply.getId()).setStatus(AliDirectApplyStatus.IN_ALI_AUDITING.getVal())
                        .setUser_id(response.getResp().getUserId()).setResponse_body(JSON.toJSONString(response.getResp()))
                        .setPriority(new Date(System.currentTimeMillis() + ScheduleUtil.DEFAULT_FIVE_MINUTES_MILLIS_QUERY)));
            } else {
                if (response.isSystemFail()) {
                    //3.2.2 500
                    monitorLog.recordMonitor("支付宝直连", apply.getMerchant_sn() + " 确认支付宝直连申请返回500 " + message);
                    applyMapper.updateByPrimaryKeySelective(new AliDirectApply().setId(apply.getId()).setPriority(new Date(System.currentTimeMillis() + ScheduleUtil.DEFAULT_FIVE_MINUTES_MILLIS_QUERY)));
                } else {
                    //3.2.1 非500，更新申请单为失败 报备任务失败 间连状态为失败
                    String finalMessage = message;
                    transactionTemplate.executeWithoutResult(status -> {
                        AliDirectApply update = new AliDirectApply().setId(apply.getId()).setRequest_body(JSON.toJSONString(response.getReq()))
                                .setResponse_body(JSON.toJSONString(response.getResp())).setStatus(AliDirectApplyStatus.APPLY_REJECTED.getVal()).setResult(finalMessage);
                        applyMapper.updateByPrimaryKeySelective(update);
                        taskBiz.update(new ContractTask().setId(apply.getTask_id()).setStatus(TaskStatus.FAIL.getVal())
                                .setResult(JSON.toJSONString(CollectionUtil.hashMap("channel", ProviderUtil.ALI_DIRECT, "message", finalMessage))));
                        directStatusBiz.createOrUpdateDirectStatus(merchantSn, aliDirectDevCode, DirectStatus.STATUS_BIZ_FAIL, finalMessage);
                    });
                }
            }
        } finally {
            monitorLog.recordObject(new MonitorObject()
                    .setSn(merchantSn)
                    .setEvent(MonitorObject.ALI_DIRECT_APPLY)
                    .setCost(System.currentTimeMillis() - start)
                    .setStatus(code)
                    .setMessage(message));
        }
    }
}