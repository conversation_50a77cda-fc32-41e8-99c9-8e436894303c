package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.biz.merchantlevel.service.MerchantActiveLevelService;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 商户交易等级规则
 * A B C D E F 依次升高
 * <AUTHOR>
 * @date 2025/8/28
 */
@Component
public class MerchantPayLevelRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private MerchantActiveLevelService merchantActiveLevelService;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            Map level = merchantActiveLevelService.getMerchantActiveLevelByMerchantId(context.getMerchantId());
            String minLevel = WosaiMapUtils.getString(ruleConfig.getParams(), "minLevel");
            // 检查商户是否开通了分账功能
            if (WosaiMapUtils.isEmpty(level)) {
                return createFailureResult(
                        "商户交易等级为空，不允许执行",
                        "MERCHANT_PAY_LEVEL_BLOCKED"
                );
            }
            String activeLevel = WosaiMapUtils.getString(level, "level");
            if (activeLevel != null && activeLevel.compareTo(minLevel) < 0) {
                return createFailureResult(
                        "商户交易等级为" + activeLevel + "，小于最小等级" + minLevel + "，不允许执行",
                        "MERCHANT_PAY_LEVEL_BLOCKED"
                );
            }

            logger.debug("商户 {} 交易等级规则为 {}，最小等级为 {}，通过检查", context.getMerchantSn(), activeLevel, minLevel);
            return createSuccessResult(String.format("商户交易等级规则为%s，通过检查", activeLevel));

        } catch (Exception e) {
            logger.error("执行商户交易等级规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.MERCHANT_PAY_LEVEL;
    }
}
