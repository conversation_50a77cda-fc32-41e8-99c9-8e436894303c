package com.wosai.upay.job.refactor.biz.acquirer.hxb;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import org.springframework.stereotype.Service;

/**
 * 华夏银行收单处理门面
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
public class HxbAcquirerFacade extends AbstractAcquirerHandler {

    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.HXB;
    }

}
