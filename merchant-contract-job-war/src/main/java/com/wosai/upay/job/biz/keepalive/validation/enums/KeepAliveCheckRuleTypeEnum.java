package com.wosai.upay.job.biz.keepalive.validation.enums;

import lombok.Getter;

/**
 * 规则类型枚举
 */
@Getter
public enum KeepAliveCheckRuleTypeEnum {
    
    /**
     * 费率活动规则
     */
    FEE_ACTIVITY("FEE_ACTIVITY", "费率活动规则"),
    
    /**
     * 额度包补贴活动
     */
    QUOTA_ACTIVITY("QUOTA_ACTIVITY", "额度包补贴活动"),
    
    /**
     * 定时D0规则
     */
    TIMING_D0_RULE("TIMING_D0_RULE", "定时D0规则"),
    
    /**
     * 自定义结算周期
     */
    CUSTOM_WITHDRAW("CUSTOM_WITHDRAW", "自定义结算周期"),
    
    /**
     * 商户分账规则
     */
    MERCHANT_SHARING("MERCHANT_SHARING", "商户分账规则"),
    
    /**
     * 商户支付业务维护组织规则
     */
    MERCHANT_PAYMENT_ORG("MERCHANT_PAYMENT_ORG", "商户支付业务维护组织规则"),
    
    /**
     * AT状态规则
     */
    AT_STATUS("AT_STATUS", "AT状态规则"),
    
    /**
     * 收单机构商户号状态规则
     */
    ACQUIRER_MERCHANT_STATUS("ACQUIRER_MERCHANT_STATUS", "收单机构商户号状态规则"),
    
    /**
     * 收钱吧商户状态规则
     */
    SQB_MERCHANT_STATUS("SQB_MERCHANT_STATUS", "收钱吧商户状态规则"),
    
    /**
     * 商户间扫收款权限规则
     */
    MERCHANT_INDIRECT_PAY_STATUS("MERCHANT_INDIRECT_PAY_STATUS", "商户间扫收款权限规则"),
    
    /**
     * 交易活跃规则
     */
     PAY_ACTIVE("PAY_ACTIVE", "交易活跃规则"),
    
    /**
     * 商户等级规则
     */
    MERCHANT_PAY_LEVEL("MERCHANT_PAY_LEVEL", "商户等级规则"),
    /**
     * 刷卡规则
     */
    BANK_POS("BANK_POS", "刷卡规则"),
    /**
     * 支付业务通道范围规则
     */
    PAY_PROVIDER_SCOPE("PAY_PROVIDER_SCOPE", "支付业务通道范围规则");
    
    private final String code;
    private final String description;
    
    KeepAliveCheckRuleTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static KeepAliveCheckRuleTypeEnum fromCode(String code) {
        for (KeepAliveCheckRuleTypeEnum keepAliveCheckRuleTypeEnum : values()) {
            if (keepAliveCheckRuleTypeEnum.getCode().equals(code)) {
                return keepAliveCheckRuleTypeEnum;
            }
        }
        return null;
    }
}