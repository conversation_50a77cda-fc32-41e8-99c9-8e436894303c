package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.data.util.CollectionUtil;
import com.wosai.service.IMerchantGrayService;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 自定义结算周期规则
 * 检查商户是否开通了自定义结算周期
 */
@Component
public class CustomWithdrawRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private IMerchantGrayService iMerchantGrayService;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            Map grayResult = iMerchantGrayService.query(CollectionUtil.hashMap("merchant_id", context.getMerchantId(), "type", 5));
            // value=0 非白名单商户
            if (!"0".equals(MapUtils.getString(grayResult, "value"))) {
                return createFailureResult(
                        "商户开通了自定义结算周期，不允许执行",
                        "CUSTOM_WITHDRAW_BLOCKED");
            }
            return createSuccessResult("商户未开通自定义结算周期，通过检查");

        } catch (Exception e) {
            logger.error("执行自定义结算周期检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.CUSTOM_WITHDRAW;
    }
}