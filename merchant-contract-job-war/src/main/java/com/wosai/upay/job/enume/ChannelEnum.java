package com.wosai.upay.job.enume;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 渠道枚举
 * <AUTHOR>
 * @date 2024/3/8
 */
public enum ChannelEnum implements ITextValueEnum<String> {

    /**
     * 拉卡拉
     */
    LKL("lkl", "拉卡拉"),

    /**
     * 拉卡拉V3
     */
    LKLV3("lklV3", "拉卡拉V3"),
    /**
     * 通联
     */
    TONGLIAN("tonglian", "通联"),

    /**
     * 通联收银宝
     */
    TONGLIAN_V2("tonglianV2", "通联收银宝"),
    /**
     * 通联-支付宝
     */
    TONGLIAN_ALI("tonglian-1020-2", "通联-支付宝"),
    /**
     * 通联-微信
     */
    TONGLIAN_WX("tonglian-1020-3", "通联-微信"),
    /**
     * 通联-银联云闪付
     */
    TONGLIAN_UP("tonglian-1020-17", "通联-银联云闪付"),
    /**
     * 海科
     */
    HAIKE("haike", "海科"),

    /**
     * 富友
     */
    FUYOU("fuyou", "富友"),
    /**
     * 国通星驿
     */
    GUOTONG("guotong", "国通星驿"),
    /**
     * 邮储银行
     */
    PSBC("psbc", "邮储银行"),
    /**
     * 建设银行
     */
    CCB("ccb", "建设银行"),
    /**
     * 华夏银行
     */
    HX_BANK("hxb", "华夏银行"),
    /**
     * 华夏银行-支付宝
     */
    HX_ALI("hxb-1028-2", "华夏银行-支付宝"),

    /**
     * 华夏银行-微信
     */
    HX_WX("hxb-1028-3", "华夏银行-微信"),

    /**
     * 华夏银行-银联云闪付
     */
    HX_UP("hxb-1028-17", "华夏银行-银联云闪付"),

    /**
     * 泸州银行
     */
    LZB_BANK("lzb", "泸州银行"),

    /**
     * 泸州银行-支付宝
     */
    LZB_ALI("lzb-1049-2", "泸州银行-支付宝"),

    /**
     * 泸州银行-微信
     */
    LZB_WX("lzb-1049-3", "泸州银行-微信"),

    /**
     * 泸州银行-银联云闪付
     */
    LZ_UP("lzb-1049-17", "泸州银行-银联云闪付"),

    UMB_BANK("umb", "中投科信-商户来自收钱吧"),

    HAIKE_FOOD_CARD_NO("haike-food_card", "海科-饭卡"),

    HAIKE_FOOD_CARD("haike-1037-31", "海科-饭卡");

    ChannelEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    /**
     * 渠道标识
     */
    private final String value;
    /**
     * 渠道名称
     */
    private final String text;


    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }

    /**
     * 根据value获取text
     * 
     * @param value String
     * @return text
     */
    public static String getTextByValue(String value) {
        for (ChannelEnum channel : ChannelEnum.values()) {
            if (channel.getValue().equals(value)) {
                return channel.getText();
            }
        }
        return "";
    }
}
