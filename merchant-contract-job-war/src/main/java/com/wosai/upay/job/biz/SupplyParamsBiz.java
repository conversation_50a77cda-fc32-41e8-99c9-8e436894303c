package com.wosai.upay.job.biz;

import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.SubBizParamsMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.application.RuleContractRequest;
import com.wosai.upay.job.model.subBizParams.SubBizParams;
import com.wosai.upay.job.model.subBizParams.SubBizParamsExample;
import com.wosai.upay.job.service.ContractApplicationService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.Tuple2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.acquirer.ChangeToLklV3Biz.LKLORG_CHANNEL_NO_LIST;

/**
 * 补充缺失的交易参数
 *
 * <AUTHOR>
 * @date 2024/1/17
 */
@Component
public class SupplyParamsBiz {

    @Autowired
    private SubBizParamsMapper subBizParamsMapper;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private SubBizParamsBiz subBizParamsBiz;
    @Autowired
    private ChangeTradeParamsBiz changeTradeParamsBiz;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;


    /**
     * 补充拉卡拉的微信和支付宝交易参数
     *
     * @param merchantSn 商户号
     * @return true：需要派工  false：不需要派工  只有发生了重新报备和切参数成功才会去派工
     */
    public boolean supplyLklParams(String merchantSn) {
        SubBizParamsExample example = new SubBizParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(ProviderEnum.PROVIDER_LAKALA_V3.getValue())
                .andTrade_app_idNotEqualTo(subBizParamsBiz.getPayTradeAppId())
                .andDeletedEqualTo(false);
        // 获取非支付业务的业务通道配置
        List<SubBizParams> subBizParams = subBizParamsMapper.selectByExampleWithBLOBs(example);
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        // 没有业务通道在拉卡拉 并且当前收单机构也不在拉卡拉
        if (WosaiCollectionUtils.isEmpty(subBizParams) && (Objects.isNull(contractStatus) || !contractStatus.getAcquirer().equals(AcquirerTypeEnum.LKL_V3.getValue()))) {
            return false;
        }
        Tuple2<MerchantProviderParams, Boolean> wxParam = getWxParams(merchantSn);
        Tuple2<MerchantProviderParams, Boolean> aliParam = getAliParams(merchantSn);
        boolean reContract = wxParam.get_2() || aliParam.get_2();
        boolean changeParams = false;
        for (SubBizParams subBizParam : subBizParams) {
            List<String> paramIds = Optional.ofNullable((List<String>) subBizParam.getExtraMap().get(String.valueOf(subBizParam.getProvider()))).orElseGet(ArrayList::new);
            for (String paramId : paramIds) {
                MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPrimaryKey(paramId);
                if (Objects.nonNull(merchantProviderParams) && merchantProviderParams.getDeleted()) {
                    if (PaywayEnum.WEIXIN.getValue().equals(merchantProviderParams.getPayway())) {
                        changeTradeParamsBiz.changeTradeParams(wxParam.get_1().getId(), null, false, subBizParam.getTrade_app_id());
                        changeParams = true;
                    }
                    if (PaywayEnum.ALIPAY.getValue().equals(merchantProviderParams.getPayway())) {
                        changeTradeParamsBiz.changeTradeParams(aliParam.get_1().getId(), null, false, subBizParam.getTrade_app_id());
                        changeParams = true;
                    }
                }
            }
        }
        if (contractStatus.getAcquirer().equals(AcquirerTypeEnum.LKL_V3.getValue())) {
            MerchantProviderParams useWeiXinParam = merchantProviderParamsMapper.getUseWeiXinParam(merchantSn);
            if (Objects.isNull(useWeiXinParam)) {
                changeTradeParamsBiz.changeTradeParams(wxParam.get_1().getId(), null, false, subBizParamsBiz.getPayTradeAppId());
                changeParams = true;
            }
            MerchantProviderParams useAlipayParam = merchantProviderParamsMapper.getUseAlipayParam(merchantSn);
            if (Objects.isNull(useAlipayParam)) {
                changeTradeParamsBiz.changeTradeParams(aliParam.get_1().getId(), null, false, subBizParamsBiz.getPayTradeAppId());
                changeParams = true;
            }
        }
        return reContract && changeParams;
    }

    /**
     * 主动切换收单机构时获取微信交易参数
     *
     * @param merchantSn 商户号
     * @return
     */
    private Tuple2<MerchantProviderParams, Boolean> getWxParams(String merchantSn) {
        List<MerchantProviderParams> wxParams = Lists.newArrayList();
        // 先获取新渠道的微信参数
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        wxExample.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(ProviderEnum.PROVIDER_LKLORG.getValue())
                .andChannel_noIn(LKLORG_CHANNEL_NO_LIST)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        wxExample.setOrderByClause("ctime desc");
        wxParams.addAll(merchantProviderParamsMapper.selectByExampleWithBLOBs(wxExample));
        wxParams = wxParams.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(wxParams)) {
            CommonResult result = applicationContext.getBean(ContractApplicationService.class).contractByRule(
                    new RuleContractRequest()
                            .setRule(ContractRuleConstants.LKL_ORG_NORMAL_WEIXIN_RULE)
                            .setMerchantSn(merchantSn)
                            .setPlat("supplyParam")
                            .setReContract(true)
                            .setConfigParam(false)
            );
            if (!result.isSuccess()) {
                throw new ContractBizException("缺少可用微信子商户号");
            }
            wxParams = merchantProviderParamsMapper.selectByExampleWithBLOBs(wxExample);
            if (WosaiCollectionUtils.isEmpty(wxParams)) {
                throw new ContractBizException("缺少可用微信子商户号");
            }
            return new Tuple2<>(wxParams.get(0), true);
        }

        // 有多个微信交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams wxParam : wxParams) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(wxParam.getAuth_status())) {
                return new Tuple2<>(wxParam, false);
            }
        }
        return new Tuple2<>(wxParams.get(0), false);
    }


    /**
     * 主动切换收单机构时获取支付宝交易参数
     *
     * @param merchantSn 商户号
     * @return
     */
    private Tuple2<MerchantProviderParams, Boolean> getAliParams(String merchantSn) {
        List<MerchantProviderParams> aliParams;
        // 先取新渠道的参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(ProviderEnum.PROVIDER_LKLORG.getValue())
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        aliParams = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);
        aliParams = aliParams.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(aliParams)) {
            CommonResult result = applicationContext.getBean(ContractApplicationService.class).contractByRule(
                    new RuleContractRequest()
                            .setRule(ContractRuleConstants.LKL_ORG_NORMAL_ALI_RULE)
                            .setMerchantSn(merchantSn)
                            .setPlat("supplyParam")
                            .setReContract(true)
                            .setConfigParam(false)
            );
            if (!result.isSuccess()) {
                throw new ContractBizException("缺少可用支付宝子商户号");
            }
            aliParams = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);
            if (WosaiCollectionUtils.isEmpty(aliParams)) {
                throw new ContractBizException("缺少可用支付宝子商户号");
            }
            return new Tuple2<>(aliParams.get(0), true);
        }

        // 有多个支付宝交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        MerchantProviderParams providerParams = aliParams.parallelStream()
                .filter(param -> PayMchAuthStatusEnum.YES.getValue().equals(param.getAuth_status()))
                .findFirst().orElse(null);
        return Objects.isNull(providerParams) ? new Tuple2<>(aliParams.get(0), false) : new Tuple2<>(providerParams, false);
    }

    /**
     * 同步AT到拉卡拉，防止部分老商户不能提现
     *
     * @param merchantSn
     */
    public void syncAT2Lkl(String merchantSn) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        if (Objects.nonNull(acquirerParams)) {
            merchantProviderParamsService.syncSubMchToLkl(acquirerParams.getProvider_merchant_id());
        }
    }

}
