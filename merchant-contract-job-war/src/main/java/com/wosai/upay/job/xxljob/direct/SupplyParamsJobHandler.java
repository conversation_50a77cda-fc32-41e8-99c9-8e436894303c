package com.wosai.upay.job.xxljob.direct;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.request.GetLogsRequest;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.task.bean.dto.req.TaskRpcStartReqDto;
import com.wosai.task.service.TaskInstanceService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.SupplyParamsBiz;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * xxl_job_desc: 查询报错日志补参数
 * 获取指定报错日志的终端号，对其进行补参数的操作
 * 只在生产环境执行
 *
 * <AUTHOR>
 * @date 2024/3/15
 */
@Slf4j
@Component("SupplyParamsJobHandler")
public class SupplyParamsJobHandler extends AbstractDirectJobHandler {

    @Autowired
    @Qualifier("hnSLSClient")
    private Client hnSlsClient;

    @Autowired
    @Qualifier("hdSLSClient")
    private Client hdSlsClient;

    @Autowired
    private SupplyParamsBiz supplyParamsBiz;

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private TaskInstanceService taskInstanceService;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    private static final String SUPPLY_PARAMS = "SUPPLY_PARAMS";
    private static final String HD_PROJECT = "upay-gateway";
    private static final String HN_PROJECT = "sz-qr-wap-pay";

    private static final String LOG_STORE_HN_CORE_BUSINESS = "core-b";
    private static final String LOG_STORE_CORE_BUSINESS = "core-business";
    private static final String LOG_STORE_OTHER_CORE_BUSINESS = "other-core-business";
    private static final Long TASK_TEMPLATE_ID = 462L;

    @Override
    public String getLockKey() {
        return SUPPLY_PARAMS;
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            Set<String> terminalSns = queryTerminalSns();
            log.info("待处理终端列表: {}", JSON.toJSONString(terminalSns));
            for (String terminalSn : terminalSns) {
                if (WosaiStringUtils.isEmpty(terminalSn)) {
                    continue;
                }
                Map terminal = terminalService.getTerminalBySn(terminalSn);
                String merchantSn = WosaiMapUtils.getString(terminal, "merchant_sn");
                if (WosaiStringUtils.isEmpty(merchantSn)) {
                    continue;
                }
                String redisKey = "supply_params:" + merchantSn;
                // 在一天内已经补充过参数，不用在调用了
                if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
                    continue;
                }
                // 补充参数，并且派工
                doSupplyParamsAndDispatchWorkers(merchantSn);
                //添加key
                redisTemplate.opsForValue().set(redisKey, merchantSn, 1L, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            log.error("supplyParams schedule exception", e);
        }
    }

    /**
     * 补充交易参数并且派工
     *
     * @param merchantSn 商户号
     */
    private void doSupplyParamsAndDispatchWorkers(String merchantSn) {
        try {
            boolean needDispatchWorker = supplyParamsBiz.supplyLklParams(merchantSn);
            if (needDispatchWorker) {
                TaskRpcStartReqDto dto = new TaskRpcStartReqDto();
                dto.setOperator("SYSTEM");
                dto.setOperatorName("SYSTEM");
                dto.setPlatform("SYSTEM");
                dto.setTaskTemplateId(TASK_TEMPLATE_ID);
                dto.setTaskObjectSn(merchantSn);
                taskInstanceService.startTaskForRpc(dto);
            }
        } catch (Exception e) {
            log.error("补充交易参数失败: {}", merchantSn, e);
        }
    }

    private Set<String> queryTerminalSns() throws LogException {
        // 查询最近3分钟的
        int duration = 180;
        Set<String> terminalSns = new HashSet<>();
        int fromTime = (int) (System.currentTimeMillis() / 1000 - duration);
        int toTime = fromTime + duration;
        queryLogs(terminalSns, fromTime, toTime, LOG_STORE_CORE_BUSINESS, HD_PROJECT);
        queryLogs(terminalSns, fromTime, toTime, LOG_STORE_OTHER_CORE_BUSINESS, HD_PROJECT);
        queryLogs(terminalSns, fromTime, toTime, LOG_STORE_HN_CORE_BUSINESS, HN_PROJECT);

        return terminalSns;
    }

    private void queryLogs(Set<String> terminalSns, int fromTime, int toTime, String logStore, String project) throws LogException {
        if (StringUtils.isEmpty(project)) {
            return;
        }
        String query = applicationApolloConfig.getErrorLogQueryRequest().get(project);
        if (StringUtils.isEmpty(query)) {
            return;
        }
        GetLogsRequest coreRequest = new GetLogsRequest(project, logStore, fromTime, toTime, "", query);
        GetLogsResponse coreResponse;
        if (project.equals(HD_PROJECT)) {
            coreResponse = hdSlsClient.GetLogs(coreRequest);
        } else {
            coreResponse = hnSlsClient.GetLogs(coreRequest);
        }
        if (coreResponse == null || (!coreResponse.IsCompleted())) {
            throw new RuntimeException("query error not complete");
        }
        if (coreResponse.IsCompleted()) {
            for (QueriedLog log : coreResponse.getLogs()) {
                for (LogContent mContent : log.mLogItem.mContents) {
                    terminalSns.add(mContent.getValue());
                }
            }
        }
    }
}