package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.biz.BlackListBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/26
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class BlackRiskServiceImpl implements BlackRiskService{

    @Autowired
    private BlackListBiz blackListBiz;


    @Override
    public void createRiskOrder(String merchantSn, String taskType, String acquirer, String errMsg) {
        blackListBiz.createRiskOrder(merchantSn, taskType, acquirer, errMsg);
    }
}
