package com.wosai.upay.job.refactor.biz.merchant;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.DeleteStatusEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 商户基本信息处理
 *
 * <AUTHOR>
 * @date 2024/7/24 14:47
 */
@Component
@Slf4j
public class MerchantBasicInfoBiz {

    @Autowired
    private MerchantService merchantService;

    @Resource(type = MerchantBankService.class)
    private MerchantBankService merchantBankService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private StoreService storeService;

    @Resource
    private ContractStatusDAO contractStatusDAO;


    /**
     * 判断商户是否进件成功
     *
     * @param merchantSn 商户号
     * @return true-进件成功
     */
    public boolean isContractSuccess(String merchantSn) {
        Optional<ContractStatusDO> contractStatus = contractStatusDAO.getByMerchantSn(merchantSn);
        if (!contractStatus.isPresent()
                || !Objects.equals(contractStatus.get().getStatus(), ContractStatus.STATUS_SUCCESS)
                || StringUtils.isBlank(contractStatus.get().getAcquirer())) {
            return false;
        }
        return true;
    }


    /**
     * 根据商户号获取商户sn
     *
     * @param merchantId 商户号
     * @return 商户sn
     */
    public Optional<String> getMerchantSnByMerchantId(String merchantId) {
        return Optional.ofNullable(merchantService.getMerchantById(merchantId, null)).map(MerchantInfo::getSn);
    }



    /**
     * 根据商户sn获取商户id
     *
     * @param merchantSn 商户sn
     * @return 商户sn
     */
    public Optional<String> getMerchantIdByMerchantSn(String merchantSn) {
        return Optional.ofNullable(merchantService.getMerchantBySn(merchantSn, null)).map(MerchantInfo::getId);
    }

    /**
     * 判断商户是否小微商户
     *
     * @param merchantSn 商户号
     * @return 是否小微商户
     */
    public boolean isMerchantLicenseMicro(String merchantSn) {
        Optional<BusinessLicenseTypeEnum> licenceType = getMerchantBusinessLicenceType(merchantSn);
        if (!licenceType.isPresent() || licenceType.get().isMicro()) {
            return true;
        }
        return false;
    }


    /**
     * 获取商户营业执照类型
     *
     * @param merchantSn 商户号
     * @return 营业执照类型
     */
    public Optional<BusinessLicenseTypeEnum> getMerchantBusinessLicenceType(String merchantSn) {
        Optional<String> merchantIdOpt = getMerchantIdByMerchantSn(merchantSn);
        if (!merchantIdOpt.isPresent()) {
            log.error("商户{}不存在", merchantSn);
            throw new ContractBizException("商户不存在");
        }
        MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantIdOpt.get(), null);
        if (Objects.isNull(license)) {
            return Optional.of(BusinessLicenseTypeEnum.MICRO);
        }
        return EnumUtils.ofNullable(BusinessLicenseTypeEnum.class, license.getType());
    }

    /**
     * 获取商户营业执照信息
     *
     * @param merchantSn 商户号
     * @return 营业执照信息
     */
    public MerchantBusinessLicenseInfo getMerchantBusinessLicenseInfo(String merchantSn) {
        Optional<String> merchantIdOpt = getMerchantIdByMerchantSn(merchantSn);
        if (!merchantIdOpt.isPresent()) {
            log.error("商户{}不存在", merchantSn);
            throw new ContractBizException("商户不存在");
        }
        return merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantIdOpt.get(), null);
    }

    /**
     * 获取商户在收钱吧的银行卡号
     *
     * @param merchantSn 商户号
     * @return 商户银行卡号
     */
    public Optional<String> getMerchantBankCardNo(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Optional.empty();
        }
        Map bankAccount = null;
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);
        if (Objects.isNull(merchantInfo) || StringUtils.isBlank(merchantInfo.getId())) {
            return Optional.empty();
        }
        String merchantId = merchantInfo.getId();
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap(CommonModel.MERCHANT_ID, merchantId, "default_status", ValidStatusEnum.VALID.getValue()));
        if (listResult != null && listResult.getTotal() > 0) {
            bankAccount = listResult.getRecords().get(0);
        }
        return Optional.ofNullable(MapUtils.getString(bankAccount, MerchantBankAccount.NUMBER));
    }

    /**
     * 获取商户所有的收钱吧终端
     * 注：最多查10000个
     *
     * @param merchantSn 商户号
     */
    public List<Map> listAllSqbTerminals(String merchantSn) {
        Optional<String> merchantId = getMerchantIdByMerchantSn(merchantSn);
        if (!merchantId.isPresent()) {
            return Collections.emptyList();
        }
        List<Map> terminalMaps = Lists.newArrayList();
        for (int i = 1; i <= 20; i++) {
            PageInfo pageInfo = new PageInfo(i, 500);
            //pageInfo.setOrderBy(Lists.newArrayList(new OrderBy(DaoConstants.ID, OrderBy.OrderType.ASC)));
            Map queryMap = Maps.newHashMap();
            queryMap.put("merchant_id", merchantId.get());
            queryMap.put("deleted", DeleteStatusEnum.NO_DELETED.getValue());
            ListResult terminals = terminalService.findTerminals(pageInfo, queryMap);
            if (Objects.isNull(terminals) || terminals.getTotal() == 0) {
                break;
            }
            terminalMaps.addAll(terminals.getRecords());
        }
        return terminalMaps;
    }

    /**
     * 获取商户信息
     *
     * @param merchantSn 商户号
     * @return 商户信息
     */
    public MerchantInfo getMerchantInfoBySn(String merchantSn) {
        return merchantService.getMerchantBySn(merchantSn, null);
    }

    /**
     * 获取商户信息
     *
     * @param merchantId 商户号
     * @return 商户信息
     */
    public MerchantInfo getMerchantInfoById(String merchantId) {
        return merchantService.getMerchantById(merchantId, null);
    }

    /**
     * 获取商户所有的收钱吧门店
     * 注：最多查10000个
     *
     * @param merchantSn 商户号
     */
    public List<Map> listAllSqbStores(String merchantSn) {
        Optional<String> merchantId = getMerchantIdByMerchantSn(merchantSn);
        if (!merchantId.isPresent()) {
            return Collections.emptyList();
        }
        List<Map> storeMaps = Lists.newArrayList();
        for (int i = 1; i <= 20; i++) {
            PageInfo pageInfo = new PageInfo(i, 500);
            Map queryMap = Maps.newHashMap();
            queryMap.put("merchant_id", merchantId.get());
            queryMap.put("deleted", DeleteStatusEnum.NO_DELETED.getValue());
            ListResult stores = storeService.findStores(pageInfo, queryMap);
            if (Objects.isNull(stores) || stores.getTotal() == 0) {
                break;
            }
            storeMaps.addAll(stores.getRecords());
        }
        return storeMaps;
    }

    /**
     * 获取门店下所有终端
     */
    public List<Map> listAllSqbTerminalsByStoreSn(String storeSn) {
        if (StringUtils.isBlank(storeSn)) {
            return Collections.emptyList();
        }
        Map store = storeService.getStoreByStoreSn(storeSn);
        if (MapUtils.isEmpty(store)) {
            return Collections.emptyList();
        }
        List<Map> terminalMaps = Lists.newArrayList();
        for (int i = 1; i <= 20; i++) {
            PageInfo pageInfo = new PageInfo(i, 500);
            Map queryMap = Maps.newHashMap();
            queryMap.put("store_id", MapUtils.getString(store, "id"));
            queryMap.put("deleted", DeleteStatusEnum.NO_DELETED.getValue());
            ListResult terminals = terminalService.findTerminals(pageInfo, queryMap);
            if (Objects.isNull(terminals) || terminals.getTotal() == 0) {
                break;
            }
            terminalMaps.addAll(terminals.getRecords());
        }
        return terminalMaps;
    }


}

