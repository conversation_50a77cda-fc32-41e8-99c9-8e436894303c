package com.wosai.upay.job.xxljob.batch.paylater;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.PayLaterConstant;
import com.wosai.upay.job.biz.PayLaterBiz;
import com.wosai.upay.job.mapper.PayLaterApplyMapper;
import com.wosai.upay.job.model.payLater.PayLaterApply;
import com.wosai.upay.job.model.payLater.StatusLog;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * xxl_job_desc: 先享后付超时自动失败
 * <AUTHOR>
 * @date 2025/4/2
 */
@Slf4j
@Component("PayLaterZhimaMerchantTimeoutJobHandler")
public class PayLaterZhimaMerchantTimeoutJobHandler extends AbstractBatchJobHandler<PayLaterApply> {

    @Autowired
    private PayLaterBiz payLaterBiz;
    @Autowired
    private PayLaterApplyMapper payLaterApplyMapper;

    @Override
    public List<PayLaterApply> queryTaskItems(BatchJobParam param) {
        return payLaterBiz.getPayLaterTasks(Collections.singletonList(PayLaterConstant.ProcessStatus.ZHIMA_APPLYING), param.getBatchSize(), param.getQueryTime());
    }

    @Override
    public String getLockKey(PayLaterApply payLaterApply) {
        return "PayLaterZhimaMerchantTimeoutJobHandler:" + payLaterApply.getId();
    }

    @Override
    public void doHandleSingleData(PayLaterApply payLaterApply) {
        try {
            PayLaterApply apply = payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId());
            if (!PayLaterConstant.ProcessStatus.ZHIMA_APPLYING.equals(apply.getProcess_status())) {
                return;
            }
            Map<String, Object> extraMap = apply.getExtraMap();
            List<StatusLog> processStatusList = JSONObject.parseArray(BeanUtil.getPropString(extraMap, PayLaterConstant.Extra.PROCESS_STATUS), StatusLog.class);
            if (!CollectionUtils.isEmpty(processStatusList)) {
                StatusLog timeOut = processStatusList.stream().filter(statusLog -> {
                    boolean equals = Objects.equals(statusLog.getStatus(), PayLaterConstant.ProcessStatus.ZHIMA_APPLYING);
                    Long updateAt = statusLog.getUpdateAt();
                    boolean before = DateUtils.addDays(DateUtil.date(updateAt), 30).before(new Date());
                    return equals && before;
                }).findFirst().orElse(null);
                if (Objects.nonNull(timeOut)) {
                    String failMessage = "芝麻商户审核失败";
                    payLaterBiz.modifyPayLaterApply(payLaterApply, PayLaterConstant.Status.ZHIMA_FAIL,
                            PayLaterConstant.SubStatus.FAIL,
                            PayLaterConstant.ProcessStatus.FAIL,
                            failMessage, 0);
                }

            }
        } catch (Exception e) {
            log.error("商户号:{} 异常", payLaterApply.getMerchant_sn(), e);
            payLaterBiz.modifyPayLaterApply(payLaterApply, PayLaterConstant.Status.ZHIMA_FAIL,
                    PayLaterConstant.SubStatus.FAIL,
                    PayLaterConstant.ProcessStatus.FAIL,
                    PayLaterConstant.Result.ZHIMA_AUDIT_FAIL,
                    0);
        }
    }
}
