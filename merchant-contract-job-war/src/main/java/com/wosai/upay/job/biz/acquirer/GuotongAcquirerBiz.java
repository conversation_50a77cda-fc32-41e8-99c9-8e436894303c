package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.guotong.BaseGuotongResponseModel;
import com.wosai.upay.job.model.guotong.GuotongMerchantInfoModel;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.dao.McChannelDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsExtDAO;
import com.wosai.upay.job.refactor.model.entity.McChannelDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsExtDO;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.guotong.request.GuotongMerchantInfoQueryRequest;
import com.wosai.upay.merchant.contract.model.provider.GuotongParam;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import com.wosai.upay.merchant.contract.model.weixin.SubdevConfigResp;
import com.wosai.upay.merchant.contract.service.GuotongService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/1/24
 */
@Component("guotong-biz")
@Slf4j
public class GuotongAcquirerBiz implements IAcquirerBiz {

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private GuotongService guotongService;

    @Autowired
    MerchantBusinessLicenseService mcMerchantBusinessLicenseService;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private MerchantProviderParamsExtDAO merchantProviderParamsExtDAO;

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private McChannelDAO mcChannelDAO;

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        return BeanUtil.getPropString(merchantConfig, "params.guotong_trade_params.guotong_mch_id");
    }


    /**
     * 获取微信普通渠道规则
     *
     * @return
     */
    @Override
    public String getNormalWxRule() {
        return ContractRuleConstants.GUOTONG_NORMAL_WEIXIN_RULE;
    }


    @Override
    public WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        GuotongParam guotongParam = contractParamsBiz.buildContractParamsByParams(providerParams, GuotongParam.class);
        WxMchInfo wxMchInfo = new WxMchInfo();
        SubdevConfigResp resp;
        wxMchInfo.setMchInfo(new MchInfo().setMerchant_name(providerParams.getMerchant_name()));
        resp = guotongService.queryWechatSubDevConfig(providerParams.getPay_merchant_id(), guotongParam);
        wxMchInfo.setSubdevConfig(resp);
        return wxMchInfo;
    }


    @Override
    public AlipayMchInfo getAlipayMchInfo(MerchantProviderParams providerParams) {
        AlipayMchInfo alipayMchInfo = new AlipayMchInfo();
        alipayMchInfo.setName(providerParams.getMerchant_name()).setSub_merchant_id(providerParams.getPay_merchant_id());
        return alipayMchInfo;
    }

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_GUOTONG;
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_GUOTONG.getValue());
        if (Objects.isNull(acquirerParams)) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .retry(false)
                    .build();
        }
        BasicProvider provider = providerFactory.getProvider(String.valueOf(acquirerParams.getProvider()));
        ContractResponse contractResponse = provider.queryMerchantContractResult(acquirerParams.getProvider_merchant_id());
        Optional<MerchantProviderParamsDO> unionParam = getUnionPayParams(merchantSn, AcquirerTypeEnum.GUOTONG.getValue());

        if (contractResponse.isSystemFail()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .retry(false)
                    .build();
        } else if (contractResponse.isBusinessFail()) {
            saveOrUpdateParamsExt(unionParam, MerchantProviderParamsExtDO.UNION_PAY_FAIL, contractResponse.getMessage());
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                    .message(contractResponse.getMessage())
                    .retry(true)
                    .build();
        } else {
            String status = WosaiMapUtils.getString(contractResponse.getTradeParam(), "status");
            if (MerchantProviderParamsExtDO.UNION_PAY_FAIL.equals(status)) {
                saveOrUpdateParamsExt(unionParam, status, contractResponse.getMessage());
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message(contractResponse.getMessage())
                        .retry(true)
                        .build();
            } else if (MerchantProviderParamsExtDO.UNION_PAY_SUCCESS.equals(status)) {
                // 如果参数不存在要存一下
                if (!unionParam.isPresent()) {
                    saveGuotongUnionPayParams(acquirerParams, contractResponse);
                }
                unionParam = getUnionPayParams(merchantSn, AcquirerTypeEnum.GUOTONG.getValue());

                saveOrUpdateParamsExt(unionParam, status, contractResponse.getMessage());
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                        .retry(true)
                        .build();
            } else {
                saveOrUpdateParamsExt(unionParam, status, "已注销");
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message("已注销")
                        .retry(true)
                        .build();
            }
        }
    }

    private void saveGuotongUnionPayParams(MerchantProviderParams acquirerParams, ContractResponse contractResponse) {
        ContractRule contractRule = ruleContext.getContractRule(ContractRuleConstants.GUOTONG_NORMAL_UNIONPAY_RULE);
        long timeMillis = System.currentTimeMillis();
        String payMerchantId = WosaiMapUtils.getString(contractResponse.getTradeParam(), "sub_mch_id");
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams();
        merchantProviderParams.setId(UUID.randomUUID().toString());
        merchantProviderParams.setMerchant_sn(acquirerParams.getMerchant_sn());
        merchantProviderParams.setOut_merchant_sn(acquirerParams.getOut_merchant_sn());
        merchantProviderParams.setMerchant_name(acquirerParams.getMerchant_name());
        merchantProviderParams.setCtime(timeMillis);
        merchantProviderParams.setMtime(timeMillis);
        merchantProviderParams.setDeleted(false);
        merchantProviderParams.setPayway(PaywayEnum.UNIONPAY.getValue());
        merchantProviderParams.setStatus(UseStatusEnum.NO_USE.getValue());
        merchantProviderParams.setChannel_no(contractRule.getChannelNo());
        merchantProviderParams.setParent_merchant_id(acquirerParams.getParent_merchant_id());
        merchantProviderParams.setProvider_merchant_id(acquirerParams.getProvider_merchant_id());
        merchantProviderParams.setProvider(ProviderEnum.PROVIDER_GUOTONG.getValue());
        merchantProviderParams.setParams_config_status(2);
        merchantProviderParams.setPay_merchant_id(payMerchantId);
        merchantProviderParams.setStatus(UseStatusEnum.NO_USE.getValue());
        merchantProviderParams.setContract_rule(ContractRuleConstants.GUOTONG_NORMAL_UNIONPAY_RULE);
        merchantProviderParams.setRule_group_id(McConstant.RULE_GROUP_GUOTONG);
        Optional<MerchantProviderParamsDO> unionParam = getUnionPayParams(acquirerParams.getMerchant_sn(), AcquirerTypeEnum.GUOTONG.getValue());

        if (unionParam.isPresent()) {
            return;
        }
        merchantProviderParamsMapper.insertSelective(merchantProviderParams);
    }

    private void saveOrUpdateParamsExt(Optional<MerchantProviderParamsDO> unionParam, String status, String message) {
        if (!unionParam.isPresent()) {
            return;
        }
        Optional<MerchantProviderParamsExtDO> unionPayStatus = merchantProviderParamsExtDAO.getUnionPayStatus(unionParam.get().getId());
        // 如果状态和文案没有发生变化就不需要去做更新
        if (unionPayStatus.isPresent() && Objects.equals(unionPayStatus.get().getExtField1(), status) && Objects.equals(unionPayStatus.get().getFailMessage(), message)) {
            return;
        }
        MerchantProviderParamsExtDO extDO = new MerchantProviderParamsExtDO();
        extDO.setParamId(unionParam.get().getId());
        extDO.setExtField2(unionParam.get().getPayMerchantId());
        extDO.setType(MerchantProviderParamsExtDO.UNION_PAY);
        extDO.setExtField1(status);
        extDO.setExtra(JSON.toJSONString(CollectionUtil.hashMap("message", message)));
        if (unionPayStatus.isPresent()) {
            extDO.setId(unionPayStatus.get().getId());
            merchantProviderParamsExtDAO.updateMerchantParams(extDO);
        } else {
            merchantProviderParamsExtDAO.saveMerchantParametersExt(extDO);
        }
    }

    private Optional<MerchantProviderParamsDO> getUnionPayParams(String merchantSn, String acquirer) {
        List<McChannelDO> mcChannels = mcChannelDAO.getMcChannelByAcquirer(acquirer, PaywayEnum.UNIONPAY.getValue());
        for (McChannelDO mcChannel : mcChannels) {
            Optional<MerchantProviderParamsDO> merChantProviderParams = merchantProviderParamsDAO.getMerChantProviderParams(merchantSn, mcChannel.getChannelNo(), PaywayEnum.UNIONPAY.getValue());
            if (merChantProviderParams.isPresent()) {
                return merChantProviderParams;
            }
        }
        return Optional.empty();

    }


    @Override
    public Boolean bankAccountConsistent(String merchantSn, Map bankAccount) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_GUOTONG.getValue());
        if (Objects.isNull(acquirerParams)) {
            throw new CommonPubBizException("商户未报备成功");
        }
        GuotongParam guotongParam = contractParamsBiz.buildContractParamsByParams(acquirerParams, GuotongParam.class);
        GuotongMerchantInfoQueryRequest request = new GuotongMerchantInfoQueryRequest();
        request.setAcquirerMerchantId(acquirerParams.getPay_merchant_id());
        ContractResponse contractResponse = guotongService.queryMerchant(request, guotongParam);
        if (!contractResponse.isSuccess()) {
            throw new CommonPubBizException(contractResponse.getMessage());
        }
        BaseGuotongResponseModel<GuotongMerchantInfoModel> guotongMerchantInfoQueryResponse = JSON.parseObject(JSON.toJSONString(contractResponse.getResponseParam()), new TypeReference<BaseGuotongResponseModel<GuotongMerchantInfoModel>>() {
        });
        if (!guotongMerchantInfoQueryResponse.isSuccess()) {
            throw new CommonPubBizException(guotongMerchantInfoQueryResponse.getMsg());
        }
        //收钱吧银行卡
        final String number = BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.NUMBER);
        return Objects.equals(number, guotongMerchantInfoQueryResponse.getData().getAccountNo());
    }
}
