package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.model.AppInfoResponse;
import com.wosai.service.SystemService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.OnlinePaymentConstant;
import com.wosai.upay.job.biz.OnlinePaymentBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.enume.PlatformEnum;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.BatchImportAliOnlineMerchantsExcel;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.model.OnlinePaymentFailMessageQueryReq;
import com.wosai.upay.job.model.onlinePayment.*;
import com.wosai.upay.job.refactor.dao.OpenOnlinePaymentApplyDAO;
import com.wosai.upay.job.refactor.model.bo.OnlinePaymentApplyProcessBO;
import com.wosai.upay.job.refactor.model.entity.OpenOnlinePaymentApplyDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.Tuple2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Service
@AutoJsonRpcServiceImpl
public class OnlinePaymentServiceImpl implements OnlinePaymentService {

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Autowired
    private SystemService systemService;
    @Autowired
    private OnlinePaymentBiz onlinePaymentBiz;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private OpenOnlinePaymentApplyDAO openOnlinePaymentApplyDAO;
    @Autowired
    private ErrorCodeManageBiz errorCodeManageBiz;

    @Resource
    private SubBizParamsBiz subBizParamsBiz;

    private static final List<String> SUPPORT_ONLINE_PAYMENT_ACQUIRERS = Arrays.asList(AcquirerTypeEnum.LKL.getValue(), AcquirerTypeEnum.LKL_V3.getValue(), AcquirerTypeEnum.HAI_KE.getValue());

    @Override
    public OnlinePaymentOpenResp openOnlinePayment(OnlinePaymentOpenReq req) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(req.getMerchantSn());
        Tuple2<Boolean, String> checkResp = doCheckMerchantAllowOpenOnlinePayment(req.getMerchantSn(), req.getPayway(), contractStatus);
        if (!checkResp.get_1()) {
            return new OnlinePaymentOpenResp().setStatus(OnlinePaymentConstant.ApplyStatus.FAIL).setFailMsg(checkResp.get_2());
        }
        // 查询最近的申请单
        Optional<OpenOnlinePaymentApplyDO> openOnlinePaymentApplyDO = openOnlinePaymentApplyDAO.queryLatestOpenOnlinePaymentApplyByAcquirer(req.getMerchantSn(), req.getPayway(), contractStatus.getAcquirer());
        if (openOnlinePaymentApplyDO.isPresent()) {
            Integer latestApplyStatus = openOnlinePaymentApplyDO.get().getStatus();
            if (latestApplyStatus.equals(OnlinePaymentConstant.ApplyStatus.PENDING) || latestApplyStatus.equals(OnlinePaymentConstant.ApplyStatus.APPLYING)) {
                return new OnlinePaymentOpenResp().setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
            }
            if (latestApplyStatus.equals(OnlinePaymentConstant.ApplyStatus.SUCCESS)) {
                return onlinePaymentBiz.resetOnlinePaymentParams(openOnlinePaymentApplyDO.get());
            }
        }
        // 保存申请单
        onlinePaymentBiz.insertApplyWithKafka(req.getMerchantSn(), req.getPayway(), contractStatus.getAcquirer());
        return new OnlinePaymentOpenResp().setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
    }

    @Override
    public OnlinePaymentOpenCheckResp checkMerchantAllowOpenOnlinePayment(OnlinePaymentOpenCheckReq req) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(req.getMerchantSn());
        Tuple2<Boolean, String> checkResp = doCheckMerchantAllowOpenOnlinePayment(req.getMerchantSn(), req.getPayway(), contractStatus);
        if (!checkResp.get_1()) {
            return OnlinePaymentOpenCheckResp.fail(checkResp.get_2());
        }
        return OnlinePaymentOpenCheckResp.success();
    }

    @Override
    public Integer queryMerchantOnlinePaymentOpenStatus(MerchantOnlinePaymentOpenStatusReq req) {
        Map merchant = merchantService.getMerchantByMerchantId(req.getMerchantId());
        String merchantSn = WosaiMapUtils.getString(merchant, Merchant.SN);
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        Optional<OpenOnlinePaymentApplyDO> openOnlinePaymentApplyDO = openOnlinePaymentApplyDAO.queryLatestOpenOnlinePaymentApplyByAcquirer(merchantSn, req.getPayway(), contractStatus.getAcquirer());
        if (openOnlinePaymentApplyDO.isPresent()) {
            Integer latestApplyStatus = openOnlinePaymentApplyDO.get().getStatus();
            if (latestApplyStatus.equals(OnlinePaymentConstant.ApplyStatus.PENDING) || latestApplyStatus.equals(OnlinePaymentConstant.ApplyStatus.APPLYING)) {
                return OnlinePaymentConstant.OpenStatus.OPENING;
            } else if (latestApplyStatus.equals(OnlinePaymentConstant.ApplyStatus.SUCCESS)) {
                return OnlinePaymentConstant.OpenStatus.OPEN_SUCCESS;
            } else {
                return OnlinePaymentConstant.OpenStatus.OPEN_FAIL;
            }
        }
        return OnlinePaymentConstant.OpenStatus.NOT_OPEN;
    }

    @Override
    public Boolean queryOnlinePaymentSubMchIdAuthStatus(OnlinePaymentSubMchIdAuthStatusQueryReq req) {
        Map merchant = merchantService.getMerchantByMerchantId(req.getMerchantId());
        String merchantSn = WosaiMapUtils.getString(merchant, Merchant.SN);
        Optional<OpenOnlinePaymentApplyDO> latestApply = openOnlinePaymentApplyDAO.queryLatestOpenOnlinePaymentApply(merchantSn, req.getPayway());
        if (!latestApply.isPresent()) {
            throw new ContractBizException("线上收款申请单不存在");
        }
        return onlinePaymentBiz.queryAuthStatusAndSetDefault(latestApply.get());
    }

    @Override
    public OnlinePaymentApplyInfoQueryResp queryOnlinePaymentApplyInfo(OnlinePaymentApplyInfoQueryReq req) {
        Map merchant = merchantService.getMerchantByMerchantId(req.getMerchantId());
        Optional<OpenOnlinePaymentApplyDO> latestApply = openOnlinePaymentApplyDAO.queryLatestOpenOnlinePaymentApply(WosaiMapUtils.getString(merchant, Merchant.SN), req.getPayway());
        if (!latestApply.isPresent()) {
            throw new ContractBizException("线上收款申请单不存在");
        }
        OpenOnlinePaymentApplyDO apply = latestApply.get();
        OnlinePaymentApplyInfoQueryResp onlinePaymentApplyInfoQueryResp = new OnlinePaymentApplyInfoQueryResp();
        onlinePaymentApplyInfoQueryResp.setProcessStatus(apply.getProcessStatus());
        if (apply.getStatus().equals(OnlinePaymentConstant.ApplyStatus.FAIL)) {
            Tuple2<String, String> titleAndMsg = getFailTitleAndMessage(apply, PlatformEnum.APP);
            return onlinePaymentApplyInfoQueryResp.setStatus(OnlinePaymentConstant.ApplyStatus.FAIL)
                    .setFailTitle(titleAndMsg.get_1())
                    .setFailMessage(titleAndMsg.get_2());
        } else if (apply.getStatus().equals(OnlinePaymentConstant.ApplyStatus.SUCCESS)) {
            return onlinePaymentApplyInfoQueryResp.setStatus(OnlinePaymentConstant.ApplyStatus.SUCCESS);
        } else {
            if (apply.getProcessStatus().equals(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH)) {
                AppInfoResponse appInfoResponse = systemService.getAppInfoByInfo(CollectionUtil.hashMap("subMchId", apply.getPayMerchantId(), "reason", "online"));
                onlinePaymentApplyInfoQueryResp.setApplyId(String.valueOf(appInfoResponse.getAppAuthInfo().getApply_id()));
            }

            List<OnlinePaymentApplyProcessBO> processList = apply.processList();
            List<OnlinePaymentApplyProcess> processes = processList.stream().map(onlinePaymentApplyProcessBO -> new OnlinePaymentApplyProcess()
                    .setStage(onlinePaymentApplyProcessBO.getStage())
                    .setStageName(onlinePaymentApplyProcessBO.getStageName())
                    .setFinish(onlinePaymentApplyProcessBO.getFinish())
                    .setMessage(onlinePaymentApplyProcessBO.getMessage())).collect(Collectors.toList());
            return onlinePaymentApplyInfoQueryResp
                    .setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING)
                    .setProcess(processes);
        }
    }

    @Override
    public void importAliOnlineMerchants(String merchantSn, String status, String rejectReason) {
        BatchImportAliOnlineMerchantsExcel excel = new BatchImportAliOnlineMerchantsExcel();
        excel.setMerchantSn(merchantSn);
        excel.setStatus(status);
        excel.setRejectReason(rejectReason);
        onlinePaymentBiz.importAliAuditResult(excel);
    }

    @Override
    public Map<PlatformEnum, String> queryOnlinePaymentFailMessage(OnlinePaymentFailMessageQueryReq req) {
        Optional<OpenOnlinePaymentApplyDO> latestApply = openOnlinePaymentApplyDAO.queryLatestOpenOnlinePaymentApply(req.getMerchantSn(), req.getPayway());
        if (!latestApply.isPresent()) {
            throw new ContractBizException("线上收款申请单不存在");
        }
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = latestApply.get();
        if (!openOnlinePaymentApplyDO.getStatus().equals(OnlinePaymentConstant.ApplyStatus.FAIL)) {
            throw new ContractBizException("线上收款申请单未失败");
        }
        Map<PlatformEnum, String> result = new HashMap<>();
        for (PlatformEnum platformEnum : req.getPlatform()) {
            result.put(platformEnum, getFailTitleAndMessage(openOnlinePaymentApplyDO, platformEnum).get_2());
        }
        return result;
    }

    private Tuple2<String, String> getFailTitleAndMessage(OpenOnlinePaymentApplyDO apply, PlatformEnum platformEnum) {
        if (apply.getProcessStatus().equals(OnlinePaymentConstant.ApplyProcessStatus.CONTRACT_FAIL)) {
            return new Tuple2<>("报备失败",getMsg(apply.getResult(), platformEnum));
        } else if (apply.getProcessStatus().equals(OnlinePaymentConstant.ApplyProcessStatus.SUBMIT_AUTH_FAIL) || apply.getProcessStatus().equals(OnlinePaymentConstant.ApplyProcessStatus.QUERY_AUTH_FAIL)) {
            return new Tuple2<>("认证失败", getMsg(apply.getResult(), platformEnum));
        } else if (apply.getProcessStatus().equals(OnlinePaymentConstant.ApplyProcessStatus.CHANGE_PARAMS_FAIL)) {
            return new Tuple2<>("开通失败", getMsg(apply.getResult(), platformEnum));
        } else {
            // 支付宝审批驳回 不转译文案
            return new Tuple2<>("支付宝审批驳回", apply.getResult());
        }
    }

    /**
     * 获取线上收款报错提示
     * @param result
     * @param platform
     * @return
     */
    private String getMsg(String result, PlatformEnum platform) {
        String type = platform.getValue().toLowerCase(Locale.ROOT) + "_msg";
        final ErrorInfo errorInfo = errorCodeManageBiz.getPromptMessageFromErrorCodeManager(type, result, ErrorCodeManageBiz.PLATFORM_ONLINE_PAYMENT);
        return errorInfo.getMsg();
    }


    private Tuple2<Boolean, String> doCheckMerchantAllowOpenOnlinePayment(String merchantSn, Integer payway, ContractStatus contractStatus) {
        // 校验能否开通线上收款 间连扫码是否开通成功 && 当前所在通道是否允许开通线上收款
        if (contractStatus == null || contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS) {
            return new Tuple2<>(false, "【cc001】您暂未开通支付功能，请联系客户经理处理");
        }
        if (!SUPPORT_ONLINE_PAYMENT_ACQUIRERS.contains(contractStatus.getAcquirer())) {
            return new Tuple2<>(false, "【cc002】您当前所在的支付机构暂不支持开通，请联系客户经理处理");
        }
        // 海科小微不允许开通微信线上收款
        if (AcquirerTypeEnum.HAI_KE.getValue().equals(contractStatus.getAcquirer()) && PaywayEnum.WEIXIN.getValue().equals(payway)) {
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            Map<String, Object> license = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(BeanUtil.getPropString(merchant, DaoConstants.ID));
            if (BeanUtil.getPropInt(license, MerchantBusinessLicence.TYPE) == 0) {
                return new Tuple2<>(false, "【cc003】您当前所在的支付机构暂不支持开通，请联系客户经理处理");
            }
        }
        return new Tuple2<>(true, null);
    }
}
