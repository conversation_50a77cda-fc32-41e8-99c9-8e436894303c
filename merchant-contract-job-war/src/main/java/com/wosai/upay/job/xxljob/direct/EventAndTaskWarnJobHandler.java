package com.wosai.upay.job.xxljob.direct;

import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.DefaultValueUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * xxl_job_desc: task和 event未处理数量告警
 * <AUTHOR>
 * @date 2025/4/2
 */
@Slf4j
@Component("EventAndTaskWarnJobHandler")
public class EventAndTaskWarnJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractEventMapper contractEventMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ChatBotUtil chatBotUtil;
    @Override
    public String getLockKey() {
        return "EventAndTaskWarnJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            Integer warnCount = param.getBatchSize();
            String end = LocalDateTime.now().minusMinutes(5).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String start = LocalDateTime.now().minusMinutes(10).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            int taskCount = contractTaskMapper.selectWarnCount(start, end);
            int eventCount = contractEventMapper.selectEventCount(start, end);
            if (taskCount > warnCount) {
                chatBotUtil.sendMessageToContractWarnChatBot("超过5分钟 新增商户入网 审核未完成 数量" + taskCount);
            }
            if (eventCount > warnCount) {
                chatBotUtil.sendMessageToContractWarnChatBot("超过5分钟 contract_event 未处理 数量" + eventCount);
            }
        } catch (Exception e) {
            log.error("EventAndTaskWarnJobHandler", e);
        }
    }
}
