package com.wosai.upay.job.model.callback.res;

import java.io.Serializable;

import lombok.Data;

@Data
public class BcsCallBackResponse implements Serializable {

    private int code;

    private String msg;

    private String status;

    public static BcsCallBackResponse success() {
        BcsCallBackResponse res = new BcsCallBackResponse();
        res.setStatus("SUCCESS");
        res.setCode(200);
        return res;
    }

    public static BcsCallBackResponse fail(int code, String msg) {
        BcsCallBackResponse res = new BcsCallBackResponse();
        res.setCode(code);
        res.setMsg(msg);
        res.setStatus("FAIL");
        return res;
    }

}
