package com.wosai.upay.job.xxljob.batch.contracttask;

import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * xxl_job_desc: 进件任务-国通进件结果查询
 * <AUTHOR>
 * @date 2025/4/18
 */
@Slf4j
@Component("QueryGuotongContractStatusJobHandler")
public class QueryGuotongContractStatusJobHandler extends AbstractBatchJobHandler<ContractSubTask> {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private QueryContractStatusHandler queryContractStatusHandler;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public List<ContractSubTask> queryTaskItems(BatchJobParam param) {
        long current = System.currentTimeMillis();
        return contractSubTaskMapper.selectGuotongContractQueryTask(param.getBatchSize(),
                StringUtil.formatDate(current - param.getQueryTime()),
                StringUtil.formatDate(current));
    }

    @Override
    public String getLockKey(ContractSubTask contractSubTask) {
        return "QueryGuotongContractStatusJobHandler:" + contractSubTask.getId();
    }

    @Override
    public void doHandleSingleData(ContractSubTask contractSubTask) {
        try {
            ContractSubTask subTask = contractSubTaskMapper.selectByPrimaryKey(contractSubTask.getId());
            if (!ContractSubTaskProcessStatusEnum.WAIT_CALL_BACK_TEN.getValue().equals(subTask.getStatus())) {
                return;
            }
            final ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id());
            queryContractStatusHandler.doHandle(contractTask, subTask);
        } catch (Exception e) {
            log.error("QueryGuotongContractStatusJobHandler error：", e);
            chatBotUtil.sendMessageToContractWarnChatBot("QueryGuotongContractStatusJobHandler error" + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
