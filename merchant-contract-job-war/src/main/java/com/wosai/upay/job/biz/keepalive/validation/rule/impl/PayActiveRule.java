package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import com.wosai.upay.job.refactor.dao.SubMchIdLastTradeTimeDAO;
import com.wosai.upay.job.refactor.model.entity.SubMchIdLastTradeTimeDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 商户交易活跃
 *
 * <AUTHOR>
 * @date 2025/8/28
 */
@Component
public class PayActiveRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private SubMchIdLastTradeTimeDAO subMchIdLastTradeTimeDAO;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            List<SubMchIdLastTradeTimeDO> subMchIdLastTradeTimeDOS = subMchIdLastTradeTimeDAO.selectByMerchantSn(context.getMerchantSn());
            if (WosaiCollectionUtils.isEmpty(subMchIdLastTradeTimeDOS)) {
                return createFailureResult(
                        "商户交易非活跃，不允许执行", "PAY_ACTIVE_BLOCKED");

            }
            return createSuccessResult("商户交易活跃正常，通过检查");

        } catch (Exception e) {
            logger.error("执行商户交易活跃规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.PAY_ACTIVE;
    }
}
