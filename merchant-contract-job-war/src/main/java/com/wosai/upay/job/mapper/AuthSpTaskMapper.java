package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.AuthSpTask;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Description: AuthSpTaskMapper ep101 派工中心表
 * <AUTHOR>
 * @Date 2022/2/22 11:22 上午
 **/
public interface AuthSpTaskMapper {

    @Select("select * from auth_sp_task where merchant_sn = #{merchantSn} limit 1")
    AuthSpTask selectBySn(@Param("merchantSn") String merchantSn);

    @Insert("insert into auth_sp_task (merchant_sn, level, template_id) values (#{merchant_sn}, #{level}, #{template_id})")
    void insertTask(AuthSpTask authSpTask);

}
