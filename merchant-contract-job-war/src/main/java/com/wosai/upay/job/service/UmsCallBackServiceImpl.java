package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.UmsCallBackBiz;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractSubTaskAndParam;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.ChinaUmsParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/3/16 11:26 上午
 */
@AutoJsonRpcServiceImpl
@Component
public class UmsCallBackServiceImpl implements UmsCallBackService {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private UmsCallBackBiz umsCallBackBiz;
    @Autowired
    private ContractParamsBiz baseBiz;


    /**
     * 根据contractId 查询 任务信息
     *
     * @param contractId
     * @return
     */
    @Override
    public ContractSubTask queryContractSubTaskByContractId(String contractId) {
        return contractSubTaskMapper.selectByContractId(contractId);
    }

    /**
     * 根据contractId 查询任务信息 以及收单机构参数信息
     *
     * @param contractId
     * @return
     */
    @Override
    public ContractSubTaskAndParam getContractSubTaskAndParam(String contractId) {
        ContractSubTask contractSubTask = contractSubTaskMapper.selectByContractId(contractId);
        ChinaUmsParam chinaUmsParam = baseBiz.buildContractParamsByContractSubTask(contractSubTask, ChinaUmsParam.class);
        return new ContractSubTaskAndParam().setContractSubTask(contractSubTask).setChannelParam(chinaUmsParam);
    }

    /**
     * 银联商务回调 接口处理
     *
     * @param contractId
     * @param contractResponse
     * @return
     */
    @Override
    public Boolean umsCallBackHandleTask(String contractId, ContractResponse contractResponse) {
        ContractSubTask contractSubTask = contractSubTaskMapper.selectByContractId(contractId);
        umsCallBackBiz.handUmsMerchantContractResult(contractResponse,contractSubTask);

        return null;
    }


}
