package com.wosai.upay.job.config;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.ScheduledThreadPoolExecutor;

/**
 * Created by lihebin on 23/08/2018.
 */
@Configuration
public class ScheduleConfig implements SchedulingConfigurer {

    @Bean(name = "scheduleThreadPool")
    public ScheduledThreadPoolExecutor threadPoolExecutor() {
        ScheduledThreadPoolExecutor scheduledThreadPoolExecutor = new ScheduledThreadPoolExecutor(10,
                new BasicThreadFactory.Builder().namingPattern("job-schedule-pool-%d").build());
        scheduledThreadPoolExecutor.setExecuteExistingDelayedTasksAfterShutdownPolicy(false);
        return scheduledThreadPoolExecutor;

    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setScheduler(threadPoolExecutor());
    }
}
