package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.*;
import com.shouqianba.cua.enums.status.ConfigStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.model.StoreExtInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.PhotoInfoService;
import com.wosai.mc.service.StoreExtService;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.handlers.ContractSubTaskHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.dto.AcquirerMerchantDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.ContractTaskDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.job.refactor.model.enums.DefaultStatusEnum;
import com.wosai.upay.job.refactor.model.enums.PaywayEnum;
import com.wosai.upay.job.service.task.TrackContractTaskResultTaskService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.CommonConstant;
import com.wosai.upay.merchant.contract.enume.LklPicTypeV3;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.weixin.AuthV3Param;
import com.wosai.upay.merchant.contract.model.weixin.MchAuthResp;
import com.wosai.upay.merchant.contract.service.AuthApplyFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.paramContext.ParamContextBiz.KEY_BANK_ACCOUNT;

/**
 * <AUTHOR>
 * Date 2019/10/13 11:56 上午
 **/
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class ContractTaskServiceImpl implements ContractTaskService {
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private ProviderFactory providerFactory;
    @Autowired
    private AuthApplyFlowService authApplyFlowService;
    @Autowired
    RedisLock redisLock;
    @Autowired
    WeixinAuthApplyBiz weixinAuthApplyBiz;
    @Autowired
    CommonEventHandler commonEventHandler;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    PendingTasksBiz pendingTasksBiz;

    @Autowired
    ComposeAcquirerBiz composeAcquirerBiz;
    @Autowired
    private ParamContextBiz paramContextBiz;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private McProviderDAO mcProviderDAO;

    @Autowired
    private StoreService storeService;

    @Autowired
    private StoreExtService mcStoreExtService;

    @Autowired
    private PhotoInfoService photoInfoService;



    @Override
    public boolean checkMchAuthBySubMchId(String mchId) {
        MerchantProviderParamsDto merchantProviderParamsDto = merchantProviderParamsMapper.getByPayMerchantId(mchId).toProviderParamsModule();
        if (Objects.isNull(merchantProviderParamsDto)) {
            throw new ContractBizException("不存在此商户号");
        }
        ContractChannel contractChannel = ruleContext.getContractChannel(merchantProviderParamsDto.getPayway(), String.valueOf(merchantProviderParamsDto.getProvider()), merchantProviderParamsDto.getChannel_no());
        if (contractChannel == null) {
            throw new ContractBizException("根据子商户号查不到报备通道");
        }
        AuthV3Param authV3Param = contractChannel.buildAuthV3Param();
        if (authV3Param == null) {
            throw new ContractBizException(String.format("子商户号所属渠道%s没有微信V3参数", contractChannel.getChannel_no()));
        }
        MchAuthResp mchAuthResp = authApplyFlowService.queryAuthStatus(mchId, authV3Param);
        if (!"200".equalsIgnoreCase(mchAuthResp.getCode())) {
            throw new ContractBizException("微信接口不稳定，请稍后重试");
        }
        return ContractSubTaskHandler.AUTHORIZE_STATE_AUTHORIZED.equalsIgnoreCase(mchAuthResp.getAuthorize_state());
    }

    @Override
    public boolean checkMchAuthByMchSn(String merchantSn) {
        MerchantProviderParams params = merchantProviderParamsMapper.getUseWeiXinParam(merchantSn);
        if (params == null) {
            throw new ContractBizException("商户没有在用微信子商户号");
        }
        return checkMchAuthBySubMchId(params.getPay_merchant_id());
    }


    @Override
    public ContractTask createSuccessTask(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andPaywayEqualTo(0).andDeletedEqualTo(false);
        List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);
        records = commonEventHandler.setRule(records);
        if (CollectionUtils.isEmpty(records)) {
            return null;
        }
        records = commonEventHandler.distinctByRule(records);
        //去除当前正在使用的收单机构
        final String merchantAcquirer = composeAcquirerBiz.getMerchantAcquirer(merchantSn);
        final String ruleGroup = composeAcquirerBiz.getAcquirerDefaultRuleGroup(merchantAcquirer);
        final List<MerchantProviderParams> params = records.parallelStream().filter(record -> !Objects.equals(record.getRule_group_id(), ruleGroup)).collect(Collectors.toList());
        //子任务上下文参数
        Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        //模拟换卡任务
        paramContext.put("cardRequestParam", paramContext.get(KEY_BANK_ACCOUNT));
        List<ContractSubTask> contractSubTaskList = new ArrayList<>(params.size());
        for (MerchantProviderParams record : params) {
            String rule = record.getContract_rule();
            String groupId = record.getRule_group_id();
            ContractRule contractRule;
            //获取默认规则
            if (WosaiStringUtils.isEmpty(rule)) {
                contractRule = ruleContext.getDefaultRule(String.valueOf(record.getProvider()), record.getPayway(), record.getChannel_no());
                if (contractRule == null) {
                    log.error("存量商户无法加载默认规则 {}", record);
                    continue;
                }
            } else {
                try {
                    //获取具体规则信息
                    contractRule = ruleContext.getContractRule(rule);
                } catch (com.wosai.upay.common.exception.CommonPubBizException e) {
                    log.info("更新处理事件规则被禁用或规则不存在无需更新", record);
                    continue;
                }
            }
            //生成任务
            BasicProvider provider = providerFactory.getProviderByContractRule(contractRule);
            //创建一个换卡的event实例
            final ContractEvent event = new ContractEvent().setMerchant_sn(merchantSn).setEvent_type(ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS).setRule_group_id(groupId);
            ContractSubTask contractSubTask = provider.produceTaskByRule(merchantSn, event, paramContext, contractRule);
            if (contractSubTask != null) {
                contractSubTask.setRule_group_id(groupId);
                contractSubTaskList.add(contractSubTask);
            }
        }
        if (contractSubTaskList.isEmpty()) {
            //无任何子任务生成
            log.info(" 开通银行直连商户{}银行卡设为默认更新事件无任何子任务生成", merchantSn);
            return null;
        }
        String merchantName = BeanUtil.getPropString(paramContext.get("merchant"), Merchant.NAME);
        int affectCount = (int) contractSubTaskList.stream().filter(sub -> Objects.equals(sub.getStatus_influ_p_task(), 1)).count();
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(merchantSn)
                .setMerchant_name(merchantName)
                .setType(ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT)
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(affectCount)
                .setAffect_status_success_task_count(0)
                .setStatus(affectCount > 0 ? TaskStatus.PENDING.getVal() : TaskStatus.SUCCESS.getVal());
        // TODO 插入contract_task表并根据type和status判断是否发送消息到神策
        contractTaskBiz.insert(contractTask);
        Long pTaskId = contractTask.getId();
        for (ContractSubTask sub : contractSubTaskList) {
            sub.setP_task_id(pTaskId);
            contractSubTaskMapper.insert(sub);
        }
        return contractTask;
    }

    @Override
    public ContractTask createTaskByAssignAcquire(String merchantSn, String acquirer) {
        final String ruleGroup = composeAcquirerBiz.getAcquirerDefaultRuleGroup(acquirer);
        final RuleGroup group = ruleContext.getRuleGroup(ruleGroup);
        final List<ContractRule> rules = group.getRules()
                .parallelStream()
                .filter(ruleItem -> Objects.isNull(ruleItem.getDepend_on()))
                .map(x -> x.getContractRule())
                .filter(y -> !Objects.isNull(y))
                .collect(Collectors.toList());
        log.info("商户号:{},指定的收单机构:{},对应的规则对象:{}", merchantSn, acquirer, JSONObject.toJSONString(rules));
        if (CollectionUtils.isEmpty(rules)) {
            throw new CommonPubBizException(String.format("商户号:%s,指定的收单机构:%s,对应的规则不存在", merchantSn, acquirer));
        }
        final ContractRule contractRule = rules.get(0);
        //子任务上下文参数
        Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        //模拟换卡任务
        paramContext.put("cardRequestParam", paramContext.get(KEY_BANK_ACCOUNT));
        //生成任务
        BasicProvider provider = providerFactory.getProviderByContractRule(contractRule);
        //创建一个换卡的event实例
        String groupId = group.getGroup_id();
        final ContractEvent event = new ContractEvent()
                .setMerchant_sn(merchantSn)
                .setEvent_type(ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS)
                .setRule_group_id(groupId);
        ContractSubTask contractSubTask = provider.produceTaskByRule(merchantSn, event, paramContext, contractRule);
        if (contractSubTask != null) {
            contractSubTask.setRule_group_id(groupId);
            //由于当前收单机构不是指定的收单机构因此需要设置子任务影响主任务
            contractSubTask.setStatus_influ_p_task(1);
        }
        String merchantName = BeanUtil.getPropString(paramContext.get("merchant"), Merchant.NAME);
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(merchantSn)
                .setMerchant_name(merchantName)
                .setType(ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT)
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(1)
                .setAffect_status_success_task_count(0)
                .setStatus(TaskStatus.PENDING.getVal())
                .setRule_group_id(groupId);
        // TODO 插入contract_task表并根据type和status判断是否发送消息到神策
        contractTaskBiz.insert(contractTask);
        Long pTaskId = contractTask.getId();
        contractSubTask.setP_task_id(pTaskId);
        contractSubTaskMapper.insert(contractSubTask);
        return contractTask;
    }


    @Override
    public ContractTask syncBankAccount2Acquire(String merchantSn, String acquire, String bankPreId) {
        final String ruleGroup = composeAcquirerBiz.getAcquirerDefaultRuleGroup(acquire);
        final RuleGroup group = ruleContext.getRuleGroup(ruleGroup);
        final List<ContractRule> rules = group.getRules()
                .parallelStream()
                .filter(ruleItem -> Objects.isNull(ruleItem.getDepend_on()))
                .map(x -> x.getContractRule())
                .filter(y -> !Objects.isNull(y))
                .collect(Collectors.toList());
        log.info("商户号:{},指定的收单机构:{},对应的规则对象:{}", merchantSn, acquire, JSONObject.toJSONString(rules));
        if (CollectionUtils.isEmpty(rules)) {
            throw new CommonPubBizException(String.format("商户号:%s,指定的收单机构:%s,对应的规则不存在", merchantSn, acquire));
        }
        final ContractRule contractRule = rules.get(0);
        //子任务上下文参数
        Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, null, bankPreId);
        //模拟换卡任务
        paramContext.put("cardRequestParam", paramContext.get(KEY_BANK_ACCOUNT));
        //生成任务
        BasicProvider provider = providerFactory.getProviderByContractRule(contractRule);
        //创建一个换卡的event实例
        String groupId = group.getGroup_id();
        final ContractEvent event = new ContractEvent()
                .setMerchant_sn(merchantSn)
                .setEvent_type(ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS)
                .setRule_group_id(groupId);
        ContractSubTask contractSubTask = provider.produceTaskByRule(merchantSn, event, paramContext, contractRule);
        if (contractSubTask != null) {
            contractSubTask.setRule_group_id(groupId);
            //由于当前收单机构不是指定的收单机构因此需要设置子任务影响主任务
            contractSubTask.setStatus_influ_p_task(1);
        }
        String merchantName = BeanUtil.getPropString(paramContext.get("merchant"), Merchant.NAME);
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(merchantSn)
                .setMerchant_name(merchantName)
                .setType(ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT)
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(1)
                .setAffect_status_success_task_count(0)
                .setStatus(TaskStatus.PENDING.getVal())
                .setRule_group_id(groupId);
        // TODO 插入contract_task表并根据type和status判断是否发送消息到神策
        contractTaskBiz.insert(contractTask);
        Long pTaskId = contractTask.getId();
        contractSubTask.setP_task_id(pTaskId);
        contractSubTaskMapper.insert(contractSubTask);
        return contractTask;
    }

    @Override
    public ContractTask syncFeeRate2Acquirer(String merchantSn, String acquirer) {
        final String ruleGroup = composeAcquirerBiz.getAcquirerDefaultRuleGroup(acquirer);
        final RuleGroup group = ruleContext.getRuleGroup(ruleGroup);
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);
        List<AcquirerMerchantDto> acquirerMerchantInfo = merchantProviderParamsService.getAcquirerMerchantInfo(merchantInfo.getId());
        Optional<AcquirerMerchantDto> acquirerMerchantDto = acquirerMerchantInfo.parallelStream().filter(info -> info.getAcquirer().contains(acquirer)).findFirst();
        if (!acquirerMerchantDto.isPresent()) {
            // 如果未获取到指定收单机构，则抛出异常
            throw new CommonPubBizException(String.format("商户未入网指定收单机构:%s", acquirer));
        }
        String groupId = group.getGroup_id();
        final ContractEvent event = new ContractEvent()
                .setMerchant_sn(merchantSn)
                .setEvent_type(ContractEvent.OPT_TYPE_MERCHANT_FEERATE)
                .setRule_group_id(groupId)
                .setEvent_msg("{\"table_name\":\"merchant_config\",\"opt_type\":2,\"msg\":[\"b2c_fee_rate\"]}");

        return buildContractTask(event, merchantSn, groupId, acquirerMerchantDto.get().getProvider());
    }

    @Override
    public ContractTask syncFeeRate2Acquirer(@NotEmpty(message = "商户号不能为空") String merchantSn, @NotEmpty(message = "收单机构不能为空") String acquirer, @NotEmpty(message = "多业务id不能为空") String appid) {
        final String ruleGroup = composeAcquirerBiz.getAcquirerDefaultRuleGroup(acquirer);
        final RuleGroup group = ruleContext.getRuleGroup(ruleGroup);
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);
        Map evenMsg = new HashMap();
        evenMsg.put("table_name", "merchant_config");
        evenMsg.put("opt_type", "2");
        Map feeMap = new HashMap();
        List<AcquirerMerchantDto> acquirerMerchantInfo = merchantProviderParamsService.getAcquirerMerchantInfo(merchantInfo.getId());
        Optional<AcquirerMerchantDto> acquirerMerchantDto = acquirerMerchantInfo.parallelStream().filter(info -> info.getAcquirer().contains(acquirer)).findFirst();
        if (!acquirerMerchantDto.isPresent()) {
            // 如果未获取到指定收单机构，则抛出异常
            throw new CommonPubBizException(String.format("商户未入网指定收单机构:%s", acquirer));
        }
        feeMap.put("provider", acquirerMerchantDto.get().getProvider());
        feeMap.put("app_id", appid);
        List<Map> feeRateList = new ArrayList<>();
        feeRateList.add(feeMap);
        evenMsg.put("msg", feeRateList);
        String groupId = group.getGroup_id();
        final ContractEvent event = new ContractEvent()
                .setMerchant_sn(merchantSn)
                .setEvent_type(ContractEvent.OPT_TYPE_MERCHANT_FEERATE)
                .setRule_group_id(groupId)
                .setEvent_msg(JSONObject.toJSONString(evenMsg));

        return buildContractTask(event, merchantSn, groupId, acquirerMerchantDto.get().getProvider());
    }


    private ContractTask buildContractTask(ContractEvent event, String merchantSn, String groupId, String paramProvider){
        //子任务上下文参数
        Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, event);

        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andProviderEqualTo(Integer.valueOf(paramProvider)).andDeletedEqualTo(false);
        List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);


        records = commonEventHandler.distinctByRule(records);

        records = commonEventHandler.setRule(records);

        List<ContractSubTask> subTasks = new ArrayList<>();
        int affectSubTaskCount = 0;
        for (MerchantProviderParams record : records) {
            String rule = record.getContract_rule();
            ContractRule contractRule;
            //获取默认规则
            if (WosaiStringUtils.isEmpty(rule)) {
                contractRule = ruleContext.getDefaultRule(String.valueOf(record.getProvider()), record.getPayway(), record.getChannel_no());
                if (contractRule == null) {
                    log.error("存量商户无法加载默认规则 {}", record);
                    continue;
                }
            } else {
                try {
                    //获取具体规则信息
                    contractRule = ruleContext.getContractRule(rule);
                } catch (com.wosai.upay.common.exception.CommonPubBizException e) {
                    log.info("更新处理事件规则被禁用或规则不存在无需更新", record);
                    continue;
                }
            }
            //生成任务
            BasicProvider provider = providerFactory.getProviderByContractRule(contractRule);
            ContractSubTask contractSubTask = provider.produceTaskByRule(merchantSn, event, paramContext, contractRule);
            if (contractSubTask != null) {
                contractSubTask.setRule_group_id(groupId);
                //由于当前收单机构不是指定的收单机构因此需要设置子任务影响主任务
                contractSubTask.setStatus_influ_p_task(contractRule.getUpdateInfluPtask());
                if (contractRule.getUpdateInfluPtask() > 0) {
                    affectSubTaskCount++;
                }
                subTasks.add(contractSubTask);
            }
        }
        if (WosaiCollectionUtils.isEmpty(subTasks)) {
            return null;
        }

        String merchantName = BeanUtil.getPropString(paramContext.get("merchant"), Merchant.NAME);
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(merchantSn)
                .setMerchant_name(merchantName)
                .setType(ProviderUtil.CONTRACT_TYPE_UPDATE_FEERATE)
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(affectSubTaskCount)
                .setAffect_status_success_task_count(0)
                .setStatus(TaskStatus.PENDING.getVal())
                .setRule_group_id(groupId);
        contractTaskBiz.insert(contractTask);
        Long pTaskId = contractTask.getId();
        for (ContractSubTask contractSubTask : subTasks) {
            contractSubTask.setP_task_id(pTaskId);
            contractSubTaskMapper.insert(contractSubTask);
        }
        return contractTask;
    }

    @Resource
    private ContractTaskDAO contractTaskDAO;

    @Resource
    private TrackContractTaskResultTaskService trackContractTaskResultTaskService;

    /**
     * 新增富友更新商户基本信息任务
     * select * from contract_sub_task where p_task_id = 44291057270;
     *
     * @param merchantSn 商户号
     * @return 主任务主键id
     */
    @Override
    public Long insertFuYouUpdateMerchantInfoTask(String merchantSn) {
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);
        if (Objects.isNull(merchantInfo)) {
            throw new CommonPubBizException("商户不存在");
        }
        ContractTaskDO contractTaskDO = buildContractTask(merchantSn, merchantInfo, AcquirerTypeEnum.FU_YOU, ContractTaskTypeEnum.UPDATE_MERCHANT_BASIC_INFO);
        ContractSubTaskDO contractSubTaskDO = buildContractSubTask(merchantSn, AcquirerTypeEnum.FU_YOU, ContractSubTaskTypeEnum.BASIC_INFO_CHANGE);
        contractTaskDAO.batchInsertTasks(contractTaskDO, Lists.newArrayList(contractSubTaskDO));
        return contractTaskDO.getId();
    }

    private ContractSubTaskDO buildContractSubTask(String merchantSn, AcquirerTypeEnum acquirerTypeEnum, ContractSubTaskTypeEnum subTaskType) {
        ContractSubTaskDO contractSubTaskDO = new ContractSubTaskDO();
        contractSubTaskDO.setMerchantSn(merchantSn);
        contractSubTaskDO.setChannel(acquirerTypeEnum.getValue());
        contractSubTaskDO.setDefaultChannel(DefaultStatusEnum.NOT_DEFAULT.getValue());
        contractSubTaskDO.setChangeConfig(ConfigStatusEnum.NOT_CONFIG.getValue());
        contractSubTaskDO.setTaskType(subTaskType.getValue());
        contractSubTaskDO.setPayway(PaywayEnum.ACQUIRER.getValue());
        contractSubTaskDO.setScheduleStatus(ScheduleStatusEnum.CAN.getValue());
        contractSubTaskDO.setStatusInfluPTask(AffectPrimaryTaskStatusEnum.YES.getValue());
        contractSubTaskDO.setContractRule(acquirerTypeEnum.getValue());
        contractSubTaskDO.setRuleGroupId(acquirerTypeEnum.getValue());
        return contractSubTaskDO;
    }

    private ContractSubTaskDO buildContractSubTask(String merchantSn, AcquirerTypeEnum acquirerTypeEnum, MerchantProviderParams params, ContractSubTaskTypeEnum subTaskType) {
        ContractSubTaskDO contractSubTaskDO = new ContractSubTaskDO();
        contractSubTaskDO.setMerchantSn(merchantSn);
        contractSubTaskDO.setChannel(acquirerTypeEnum.getValue());
        contractSubTaskDO.setDefaultChannel(DefaultStatusEnum.NOT_DEFAULT.getValue());
        contractSubTaskDO.setChangeConfig(ConfigStatusEnum.NOT_CONFIG.getValue());
        contractSubTaskDO.setTaskType(subTaskType.getValue());
        contractSubTaskDO.setPayway(PaywayEnum.ACQUIRER.getValue());
        contractSubTaskDO.setScheduleStatus(ScheduleStatusEnum.CAN.getValue());
        contractSubTaskDO.setStatusInfluPTask(AffectPrimaryTaskStatusEnum.YES.getValue());
        contractSubTaskDO.setContractRule(params.getContract_rule());
        contractSubTaskDO.setRuleGroupId(params.getRule_group_id());
        return contractSubTaskDO;
    }

    private ContractTaskDO buildContractTask(String merchantSn, MerchantInfo merchantInfo,
                                             AcquirerTypeEnum acquirerTypeEnum, ContractTaskTypeEnum taskType) {
        ContractTaskDO contractTaskDO = new ContractTaskDO();
        contractTaskDO.setMerchantSn(merchantSn);
        contractTaskDO.setMerchantName(merchantInfo.getName());
        contractTaskDO.setType(taskType.getValue());
        Map<String, Object> context = paramContextBiz.getNetInParamContextByMerchantSn(merchantSn);
        context.put(CommonConstant.SQB_FEE_RATES, paramContextBiz.getSqbFeeRates(merchantInfo.getId()));
        contractTaskDO.setEventContext(JSON.toJSONString(context));
        contractTaskDO.setAffectSubTaskCount(1);
        contractTaskDO.setAffectStatusSuccessTaskCount(0);
        contractTaskDO.setRuleGroupId(acquirerTypeEnum.getValue());
        return contractTaskDO;
    }

    private ContractTaskDO buildContractTask(String merchantSn, MerchantInfo merchantInfo,
                                             MerchantProviderParams params, ContractTaskTypeEnum taskType) {
        ContractTaskDO contractTaskDO = new ContractTaskDO();
        contractTaskDO.setMerchantSn(merchantSn);
        contractTaskDO.setMerchantName(merchantInfo.getName());
        contractTaskDO.setType(taskType.getValue());
        Map<String, Object> context = paramContextBiz.getNetInParamContextByMerchantSn(merchantSn);
        context.put(CommonConstant.SQB_FEE_RATES, paramContextBiz.getSqbFeeRates(merchantInfo.getId()));
        contractTaskDO.setEventContext(JSON.toJSONString(context));
        contractTaskDO.setAffectSubTaskCount(1);
        contractTaskDO.setAffectStatusSuccessTaskCount(0);
        contractTaskDO.setRuleGroupId(params.getRule_group_id());
        return contractTaskDO;
    }


    /**
     * 更新首家门店照片同步收单机构
     * 目前支持的收单机构：拉卡拉，富友，海科，收银宝,中投
     *
     * @param merchantSn 商户号
     */
    @Override
    public void updateFirstStorePhotoSyncToAcquirer(String merchantSn, String storeId) {
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);
        if (Objects.isNull(merchantInfo)) {
            throw new CommonPubBizException("商户不存在");
        }
        StoreInfo store = storeService.getStoreById(storeId, null);
        if (Objects.isNull(store)) {
            throw new CommonPubBizException("门店不存在");
        }
        Set<String> acquirers = merchantTradeParamsBiz.listParamsByMerchantSn(merchantSn).stream()
                .filter(t -> Objects.equals(t.getPayway(), com.shouqianba.cua.enums.core.PaywayEnum.ACQUIRER.getValue()))
                .map(t -> mcProviderDAO.getByProvider(t.getProvider().toString()))
                .filter(t -> t.isPresent() && StringUtils.isNotBlank(t.get().getAcquirer()))
                .map(t -> t.get().getAcquirer()).collect(Collectors.toSet());
        // haike和收银宝走更新商户信息接口
        if (acquirers.contains(AcquirerTypeEnum.HAI_KE.getValue())) {
            ContractTaskDO contractTaskDO = buildContractTask(merchantSn, merchantInfo, AcquirerTypeEnum.HAI_KE, ContractTaskTypeEnum.UPDATE_MERCHANT_BASIC_INFO);
            ContractSubTaskDO contractSubTaskDO = buildContractSubTask(merchantSn, AcquirerTypeEnum.HAI_KE, ContractSubTaskTypeEnum.BASIC_INFO_CHANGE);
            contractTaskDAO.batchInsertTasks(contractTaskDO, Lists.newArrayList(contractSubTaskDO));

        }
        if (acquirers.contains(AcquirerTypeEnum.TONG_LIAN_V2.getValue())) {
            ContractTaskDO contractTaskDO = buildContractTask(merchantSn, merchantInfo, AcquirerTypeEnum.TONG_LIAN_V2, ContractTaskTypeEnum.UPDATE_MERCHANT_BASIC_INFO);
            ContractSubTaskDO contractSubTaskDO = buildContractSubTask(merchantSn, AcquirerTypeEnum.TONG_LIAN_V2, ContractSubTaskTypeEnum.BASIC_INFO_CHANGE);
            contractTaskDAO.batchInsertTasks(contractTaskDO, Lists.newArrayList(contractSubTaskDO));
        }
        if (acquirers.contains(AcquirerTypeEnum.UMB.getValue())) {
            MerchantProviderParams params = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, PayParamsModel.PROVIDER_UMB);
            ContractTaskDO contractTaskDO = buildContractTask(merchantSn, merchantInfo, params, ContractTaskTypeEnum.UPDATE_MERCHANT_BASIC_INFO);
            ContractSubTaskDO contractSubTaskDO = buildContractSubTask(merchantSn, AcquirerTypeEnum.UMB, params, ContractSubTaskTypeEnum.STORE_PHOTO_UPDATE);
            contractTaskDAO.batchInsertTasks(contractTaskDO, Lists.newArrayList(contractSubTaskDO));
        }
        // 拉卡拉 富友走附件上传进件任务
        if (acquirers.contains(AcquirerTypeEnum.LKL_V3.getValue())) {
            try {
                insertMultiLklUploadPicTasks(merchantSn, store, merchantInfo);
            } catch (Exception e) {
                log.warn("构建lkl门店照片附件上传任务失败, storeId:{}", storeId);
            }
        }
        if (acquirers.contains(AcquirerTypeEnum.FU_YOU.getValue())) {
            ContractTaskDO contractTaskDO = buildContractTask(merchantSn, merchantInfo, AcquirerTypeEnum.FU_YOU, ContractTaskTypeEnum.ATTACHMENT_UPLOAD);
            ContractSubTaskDO contractSubTaskDO = buildContractSubTask(merchantSn, AcquirerTypeEnum.FU_YOU, ContractSubTaskTypeEnum.ATTACHMENT_UPLOAD);
            contractTaskDAO.batchInsertTasks(contractTaskDO, Lists.newArrayList(contractSubTaskDO));
            trackContractTaskResultTaskService.insertTask(TrackContractTaskResultTaskService.BUSINESS_SCENE_TYPE_UPDATE_FIRST_STORE,
                    merchantSn, null, contractTaskDO.getId(), null);
        }
    }


    private void insertMultiLklUploadPicTasks(String merchantSn, StoreInfo store, MerchantInfo merchantInfo) {
        Optional<String> acquirerMerchantId = merchantTradeParamsBiz.getAcquirerMerchantId(merchantSn, AcquirerTypeEnum.LKL_V3.getValue());
        if (!acquirerMerchantId.isPresent()) {
            log.warn("lkl同步收件门店异常，acquirerMerchantId为空, merchantSn:{}", merchantSn);
            return;
        }
        StotreExtInfoAndPictures storeExtInfo = mcStoreExtService.findStoreExtAndPicturesByStoreId(store.getId(), null);
        if (Objects.isNull(storeExtInfo)) {
            log.warn("lkl同步收件门店异常，storeExt为空, storeId:{}", store.getId());
        } else {
            // 门头照
            if (Objects.nonNull(storeExtInfo.getBrandPhoto()) && StringUtils.isNotEmpty(storeExtInfo.getBrandPhoto().getUrl())) {
                insertLklUploadPicTask(merchantSn, acquirerMerchantId.get(), merchantInfo, storeExtInfo.getBrandPhoto().getUrl(), String.valueOf(LklPicTypeV3.MERCHANT_PHOTO));
            }
            // 外景
            if (Objects.nonNull(storeExtInfo.getOutdoorMaterialPhoto()) && StringUtils.isNotEmpty(storeExtInfo.getOutdoorMaterialPhoto().getUrl())) {
                insertLklUploadPicTask(merchantSn, acquirerMerchantId.get(), merchantInfo, storeExtInfo.getOutdoorMaterialPhoto().getUrl(), String.valueOf(LklPicTypeV3.OTHERS));
            }
            // 内景物料照
            if (Objects.nonNull(storeExtInfo.getIndoorMaterialPhoto()) && StringUtils.isNotEmpty(storeExtInfo.getIndoorMaterialPhoto().getUrl())) {
                insertLklUploadPicTask(merchantSn, acquirerMerchantId.get(), merchantInfo, storeExtInfo.getIndoorMaterialPhoto().getUrl(), String.valueOf(LklPicTypeV3.SHOPINNER));
            }
        }
    }

    private void insertLklUploadPicTask(String merchantSn, String merInnerNo, MerchantInfo merchantInfo, String photoUrl, String type) {
        ContractTaskDO contractTaskDO = buildContractTask(merchantSn, merchantInfo, AcquirerTypeEnum.LKL_V3, ContractTaskTypeEnum.ATTACHMENT_UPLOAD);
        JSONObject contextObject = JSON.parseObject(contractTaskDO.getEventContext());
        contextObject.put(CommonModel.DATA, photoUrl);
        contextObject.put(CommonModel.TYPE, type);
        contextObject.put("merInnerNo", merInnerNo);
        contractTaskDO.setEventContext(contextObject.toJSONString());
        ContractSubTaskDO contractSubTaskDO = buildContractSubTask(merchantSn, AcquirerTypeEnum.LKL_V3, ContractSubTaskTypeEnum.ATTACHMENT_UPLOAD);
        contractTaskDAO.batchInsertTasks(contractTaskDO, Lists.newArrayList(contractSubTaskDO));
    }
}
