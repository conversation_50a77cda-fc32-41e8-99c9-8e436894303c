package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.MerchantAppModuleWhite;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * Created by lihebin on 2018/7/16.
 */
@Service
@AutoJsonRpcServiceImpl
public class ConfigSupportServiceImpl implements ConfigSupportService {

    private final static Logger log = LoggerFactory.getLogger(ConfigSupportServiceImpl.class);
    @Autowired
    private MerchantService merchantService;


    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;


    @Resource(name = "merchantContractJdbcTemplate")
    JdbcTemplate merchantContractJdbcTemplate;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Override
    public Map getMerchantModuleWhite(Map params) {
        String merchantId = BeanUtil.getPropString(params, MerchantAppModuleWhite.MERCHANT_ID);
        int moduleWhiteType = applicationApolloConfig.getAppModuleWhiteType();
        if (moduleWhiteType == 0) {
            String sql = "select merchantId from merchant_app_module_white where merchantId = ?";
            try {
                Map merchantAppModuleWhite = merchantContractJdbcTemplate.queryForMap(sql, merchantId);
                if (!MapUtils.isEmpty(merchantAppModuleWhite)) {
                    return CollectionUtil.hashMap(CommonModel.DATA, CollectionUtil.hashMap(CommonModel.VISIBLE, true));
                }
            } catch (Exception ignored) {
            }
        } else {
            MerchantInfo merchant = merchantService.getMerchantById(merchantId, devCode);
            if (merchant == null) {
                return CollectionUtil.hashMap(CommonModel.DATA, CollectionUtil.hashMap(CommonModel.VISIBLE, false));
            }
            Map tradeParams = tradeConfigService.getTradeParams(3, 3, CollectionUtil.hashMap(MerchantConfig.MERCHANT_ID, merchantId));
            if (MapUtils.isEmpty(tradeParams)) {
                return CollectionUtil.hashMap(CommonModel.DATA, CollectionUtil.hashMap(CommonModel.VISIBLE, true));
            }
            try {
                for (Object key : tradeParams.keySet()) {
                    if (key.toString().contains(CommonModel.TRADE_PARAMS)) {
                        Map merchantConfig = (Map) BeanUtil.getProperty(tradeParams, key.toString());
                        boolean formal = BeanUtil.getPropBoolean(merchantConfig, CommonModel.LIQUIDATION_NEXT_DAY, true);
                        if (formal) {
                            return CollectionUtil.hashMap(CommonModel.DATA, CollectionUtil.hashMap(CommonModel.VISIBLE, true));
                        }
                    }
                }
            } catch (Throwable ignored) {
            }
        }
        return CollectionUtil.hashMap(CommonModel.DATA, CollectionUtil.hashMap(CommonModel.VISIBLE, false));
    }


    @Override
    public String getMerchantCity(String merchantSn) {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, devCode);
        if (Objects.isNull(merchant)) {
            return null;
        }
        String merchantProvince = merchant.getProvince();
        String merchantCity = merchant.getCity();
        if (WosaiStringUtils.isEmptyAny(merchantProvince, merchantCity)) {
            throw new ContractBizException("商户省市信息为空");
        }
        //直辖市的特殊处理
        for (String municipality : CommonModel.MUNICIPAL_CITY_LIST) {
            if (municipality.contains(merchantProvince)) {
                merchantCity = merchantProvince;
                break;
            }
        }
        return merchantCity;
    }
}
