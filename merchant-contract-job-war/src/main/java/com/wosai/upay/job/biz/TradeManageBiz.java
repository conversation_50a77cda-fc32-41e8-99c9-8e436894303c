package com.wosai.upay.job.biz;

import com.github.rholder.retry.*;
import com.wosai.trade.service.TradeStateService;
import com.wosai.upay.job.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2020-04-23
 */
@Component
@Slf4j
public class TradeManageBiz {
    static Retryer retryer;

    static {
        retryer = RetryerBuilder.newBuilder()
                .retryIfException() // 抛出异常会进行重试
                // 重试策略, 此处设置的是重试间隔时间
                .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS, 10, TimeUnit.SECONDS))
                // 重试次数
                .withStopStrategy(StopStrategies.stopAfterAttempt(6))
                .build();
    }

    @Autowired
    private TradeStateService tradeStateService;
    @Autowired
    @Qualifier("merchantPayChangeThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor merchantPayChangeThreadPoolTaskExecutor;

    /**
     * 打开交易权限
     *
     * @param merchantSn
     * @param merchantId
     */
    public void openMerchantPay(String merchantSn, String merchantId) {
        changeMerchantPay(merchantSn, merchantId, true);
    }

    /**
     * 关闭交易权限
     *
     * @param merchantSn
     * @param merchantId
     */
    public void closeMerchantPay(String merchantSn, String merchantId) {
        changeMerchantPay(merchantSn, merchantId, false);
    }

    /**
     * 开启或关闭商户交易权限
     *
     * @param merchantSn
     * @param merchantId
     */
    public void changeMerchantPay(String merchantSn, String merchantId, boolean open) {
        try {
            doChangeMerchantPay(merchantSn, merchantId, open);
        } catch (Exception e) {
            merchantPayChangeThreadPoolTaskExecutor.execute(() -> {
                try {
                    retryer.call(new Callable<Boolean>() {
                        @Override
                        public Boolean call() throws Exception {
                            doChangeMerchantPay(merchantSn, merchantId, open);
                            return null;
                        }
                    });
                } catch (Exception e1) {
                }
            });
        }
    }

    private void doChangeMerchantPay(String merchantSn, String merchantId, boolean open) {
        if (open) {
            if (tradeStateService.updateStateByBizAndTypeAndMerchantId(merchantId, 1, 1, true, null)) {
                log.info("管理交易权限 商户id {}，商户号 {} 开启交易权限", merchantId, merchantSn);
            } else {
                throw new ApplicationException("开启交易权限失败", 410);
            }
        } else {
            if (tradeStateService.updateStateByBizAndTypeAndMerchantId(merchantId, 1, 1, false, null)) {
                log.info("管理交易权限 商户id {}，商户号 {} 关闭交易权限", merchantId, merchantSn);
            } else {
                throw new ApplicationException("关闭交易权限失败", 410);
            }
        }
    }
}