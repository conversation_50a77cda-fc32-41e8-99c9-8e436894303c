package com.wosai.upay.job.biz.directparams;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.Constants.TradeConstants;
import com.wosai.upay.job.model.directparams.BaseParams;
import com.wosai.upay.job.model.directparams.BestpayDirectParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 翼支付直连目前支持 WAP B2C
 *
 * <AUTHOR>
 * @date 2019-08-29
 */
@Component
public class BestpayDirectParamsBiz extends DirectParamsBiz {

    private static final int PAYWAY = PaywayEnum.BESTPAY.getValue();

    @Override
    public void addDirectParams(BaseParams baseParams) {
        checkMerchant(baseParams);

        BestpayDirectParams params = (BestpayDirectParams) baseParams;

        Map<String, Object> allDirectParams = new HashMap<>(5);

        MerchantProviderParamsDto dto = paramsBiz.getDirectParams(params.getMerchant_sn(), PAYWAY, PAYWAY);
        if (dto != null) {
            allDirectParams = dto.getExtra();
        }

        if (isNotEmpty(params.getBestpay_trade_params())) {
            Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(baseParams.getMerchant_id(), PAYWAY);

            Map<String, Object> directParams = bean2Map(params.getBestpay_trade_params());
            appendOldParams(directParams, WosaiMapUtils.getMap(allDirectParams, TransactionParam.BESTPAY_TRADE_PARAMS));
            directParams.putIfAbsent(TradeConstants.OLD_FEE_RATE, WosaiMapUtils.getString(merchantConfig, MerchantConfig.WAP_FEE_RATE));
            directParams.putIfAbsent(TradeConstants.OLD_AGENT_NAME, WosaiMapUtils.getString(merchantConfig, MerchantConfig.WAP_AGENT_NAME));

            paramsBiz.saveDirectMerchantProviderParams(
                    new MerchantProviderParamsDto()
                            .setMerchant_sn(params.getMerchant_sn())
                            .setExtra(CollectionUtil.hashMap(TransactionParam.BESTPAY_TRADE_PARAMS, directParams))
                            .setProvider(PAYWAY)
                            .setPayway(PAYWAY)
                            .setPay_merchant_id(params.getBestpay_trade_params().getMerchant_id())

            );
            tradeConfigService.updateBestPayTradeParams(params.getMerchant_id(), directParams);

            tradeConfigService.updateMerchantConfig(CollectionUtil.hashMap(
                    DaoConstants.ID, WosaiMapUtils.getString(merchantConfig, DaoConstants.ID),
                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.B2C_FEE_RATE, params.getBestpay_trade_params().getFee_rate(),
                    MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.WAP_FEE_RATE, params.getBestpay_trade_params().getFee_rate()
            ));
            supportService.removeCachedParams(params.getMerchant_sn());
        }
    }

    @Override
    public void deleteDirectParams(MerchantProviderParamsDto params, String subPayway, String feeRate) {
        Map directParams = (Map) params.getExtra().remove(TransactionParam.BESTPAY_TRADE_PARAMS);
        feeRate = getFeeRate(feeRate, WosaiMapUtils.getString(directParams, TradeConstants.OLD_FEE_RATE));

        paramsBiz.deleteParamsById(params.getId());

        Map merchant = merchantService.getMerchantBySn(params.getMerchant_sn());
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);

        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY);
        Map merchantConfigParams = WosaiMapUtils.getMap(merchantConfig, MerchantConfig.PARAMS, new HashMap());
        merchantConfigParams.remove(TransactionParam.BESTPAY_TRADE_PARAMS);
        
        String agentName = getAgentName(directParams, merchantConfig, PAYWAY, params.getMerchant_sn());
        Map updateInfo = CollectionUtil.hashMap(
                DaoConstants.ID, WosaiMapUtils.getString(merchantConfig, DaoConstants.ID),
                MerchantConfig.PARAMS, merchantConfigParams,
                MerchantConfig.B2C_FORMAL, MerchantConfig.STATUS_CLOSED,
                MerchantConfig.B2C_AGENT_NAME, agentName,
                MerchantConfig.B2C_FEE_RATE, feeRate,
                MerchantConfig.WAP_FORMAL, MerchantConfig.STATUS_CLOSED,
                MerchantConfig.WAP_AGENT_NAME, agentName,
                MerchantConfig.WAP_FEE_RATE, feeRate
        );

        tradeConfigService.updateMerchantConfig(updateInfo);

        supportService.removeCachedParams(params.getMerchant_sn());
    }

    @Override
    public BaseParams getDirectParams(String merchantSn) {
        return null;
    }

    @Override
    public List<MerchantProviderParamsCustomDto> handleDirectParams(MerchantProviderParamsCustomDto source) {
        return Arrays.asList(source);
    }
}
