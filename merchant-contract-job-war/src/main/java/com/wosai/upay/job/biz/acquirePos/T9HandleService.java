package com.wosai.upay.job.biz.acquirePos;


import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.dto.acquirePos.*;
import com.wosai.upay.job.model.lklV3Pos.ApplyPosRequest;
import com.wosai.upay.merchant.contract.model.LimitResp;
import com.wosai.upay.merchant.contract.model.fuyou.response.ModifyCancelResponse;

import java.util.List;

/**
 * @Description: 根据业务标识获取对应的处理类
 * <AUTHOR>
 * @Date 2024/2/26 17:20
 */
public interface T9HandleService {

     String choseAcquire();

     void innerBindCheck(InnerBindCheckDTO dto) ;

     void crmOpenCheck(CrmOpenCheckDTO dto);


     /**
      * 上传相应资料开通pos刷卡服务,注意拉卡拉费率最大值单位为"元"而富友费率最大值单位是"分"
      *
      * @param request
      * @return
      */
     ContractResponse openPos(ApplyPosRequest request);

    /**
     * 获取激活码
     * @param dto
     * @return
     */
     PosActiveInfo getPosActiveInfo(PosActiveInfoDTO dto);


    /**
     * 富友终端解绑
     * @param dto
     */
     void fyTermCancel(UnbindDTO dto);


    /**
     * 向收单机构添加银行卡刷卡终端
     * @param terminalSn
     */
     void t9TermAdd(String terminalSn) throws CommonPubBizException;


    /**
     *  向收单机构解绑银行卡刷卡终端
     * @param terminalSn
     */
    void t9TermUnbind(String terminalSn);

    /**
     * 取消富友扣率变更
     * @param merchantSn
     * @param modifyNo
     * @return
     */
    ModifyCancelResponse fyModifyCancel(String merchantSn, String modifyNo);

    /**
     * 查询限额
     * @param merchantSn
     * @return
     */
    List<LimitResp> queryLimit(String merchantSn);

    String getDevCode();

    String getForeignCardDevCode();

    boolean isSupport(String devCode);

    void closeForeignCard(String merchantSn);

    void closePreAuth(String merchantSn);
}
