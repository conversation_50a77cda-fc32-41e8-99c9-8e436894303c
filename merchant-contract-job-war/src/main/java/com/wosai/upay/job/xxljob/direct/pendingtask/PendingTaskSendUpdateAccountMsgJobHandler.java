package com.wosai.upay.job.xxljob.direct.pendingtask;

import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.BackAcquirerBiz;
import com.wosai.upay.job.mapper.PendingTasksMapper;
import com.wosai.upay.job.model.DO.PendingTasks;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.wosai.upay.job.util.StringUtil.formatDate;

/**
 * xxl_job_desc: PendingTask-发送更新银行卡消息
 * <AUTHOR>
 * @date 2025/4/25
 */
@Slf4j
@Component("PendingTaskSendUpdateAccountMsgJobHandler")
public class PendingTaskSendUpdateAccountMsgJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private PendingTasksMapper pendingTasksMapper;
    @Autowired
    private BackAcquirerBiz backAcquirerBiz;
    @Autowired
    private ChatBotUtil chatBotUtil;


    @Override
    public String getLockKey() {
        return "PendingTaskSendUpdateAccountMsgJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            List<PendingTasks> pendingTasks = pendingTasksMapper.selectPendingTasksByCreateAtAndType(formatDate(System.currentTimeMillis() - param.getQueryTime()), PendingTasks.TYPE_UPDATE_BANK_ACCOUNT_MESSAGE, param.getBatchSize());
            for (PendingTasks pendingTask : pendingTasks) {
                try {
                    if (ShutdownSignal.isShuttingDown()) {
                        return;
                    }
                    backAcquirerBiz.handleUpdateBankAccount(pendingTask);
                } catch (Exception e) {
                    log.error("handleUpdateBankAccount error, id:{} ", pendingTask.getId(), e);
                }
            }
        } catch (Exception e) {
            log.error(" execute sendUpdateBankAccountMessage error ", e);
            chatBotUtil.sendMessageToContractWarnChatBot("结算账户变更PendingTask处理异常 " + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
