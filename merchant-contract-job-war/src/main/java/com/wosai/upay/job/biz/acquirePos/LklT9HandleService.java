package com.wosai.upay.job.biz.acquirePos;

import com.shouqianba.cua.enums.contract.ContractTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.Constants.PosConstant;
import com.wosai.upay.job.Constants.PreAuthApplyConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.LklV3ShopTermBiz;
import com.wosai.upay.job.biz.acquirer.IAcquirerBiz;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.externalservice.coreb.TradeConfigClient;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.model.LklV3BankTerm;
import com.wosai.upay.job.model.dto.AcquirerMerchantDto;
import com.wosai.upay.job.model.dto.acquirePos.InnerBindCheckDTO;
import com.wosai.upay.job.model.dto.acquirePos.PosActiveInfo;
import com.wosai.upay.job.model.dto.acquirePos.PosActiveInfoDTO;
import com.wosai.upay.job.model.lklV3Pos.ApplyPosRequest;
import com.wosai.upay.job.service.LKlV3PosServiceImpl;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.merchant.contract.model.LimitResp;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2024/2/26 18:01
 */
@Component
@Slf4j
public class LklT9HandleService extends AbstractT9HandleService {

    @Autowired
    private LKlV3PosServiceImpl lKlV3PosServiceImpl;

    @Value("${lkl_pso_dev_code}")
    public String lklPsoDevCode;

    @Value("${lklForeignCard.devCode}")
    private String lklForeignCardDevCode;

    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    private TradeConfigClient tradeConfigClient;

    @Autowired
    private LklV3ShopTermBiz lklV3ShopTermBiz;

    @Autowired
    private StoreService storeService;

    @Autowired
    private ContractParamsBiz contractParamsBiz;
    @Autowired
    private LklV3Service lklV3Service;


    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ErrorCodeManageBiz errorCodeManageBiz;

    private static final String SPA_SYSTEM = "spa";
    private static final String LKL_ACTIVE = "lkl_active";

    @Override
    public String choseAcquire() {
        return McConstant.ACQUIRER_LKLV3;
    }

    @Override
    public void innerBindCheck(InnerBindCheckDTO dto) {
        MerchantInfo merchant = merchantService.getMerchantBySn(dto.getMerchantSn(), null);
        String merchantSn = merchant.getSn();
        final String merchantId = merchant.getId();
        List<AcquirerMerchantDto> acquirerMerchantInfo = merchantProviderParamsService.getAcquirerMerchantInfo(merchantId);
        //是否成功进件
        boolean match = acquirerMerchantInfo.parallelStream().anyMatch(info -> info.getAcquirer().contains(McConstant.ACQUIRER_LKLV3));
        if (!match) {
            throw new CommonPubBizException(PosConstant.FAIL_OTHER_ACQUIRE);
        }
        //当前正在使用的收单机构
        ContractStatus contractStatus = Optional.ofNullable(contractStatusService.selectByMerchantSn(merchantSn))
                .orElseGet(ContractStatus::new);
        String acquirer = contractStatus.getAcquirer();
        if (Objects.equals(acquirer, McConstant.ACQUIRER_LKLV3)) {
            List<String> otherAcquireVenderList = factory.otherAcquireVenderList(McConstant.ACQUIRER_LKLV3);
            existOtherT9(merchantId, otherAcquireVenderList);
            return;
        }
        List<String> indirectAcquirerList = mcAcquirerDAO.getIndirectAcquirerList();
        if (indirectAcquirerList.contains(acquirer)) {
            throw new CommonPubBizException(PosConstant.FAIL_OTHER_ACQUIRE);
        }
        //当前在银行通道
        List<String> otherAcquireVenderList = factory.otherAcquireVenderList(McConstant.ACQUIRER_LKLV3);
        existOtherT9(merchantId, otherAcquireVenderList);
        return;
    }

    @Override
    public ContractResponse openPos(ApplyPosRequest request) {
        return lKlV3PosServiceImpl.applyPos(request);
    }

    /**
     * 获取POS机激活信息
     *
     * @param dto 包含POS终端序列号的DTO对象
     * @return 返回POS机的激活信息
     * @throws CommonPubBizException 如果激活检查不通过，则抛出异常
     */
    @Override
    public PosActiveInfo getPosActiveInfo(PosActiveInfoDTO dto) {
        // 根据终端序列号获取终端信息
        String terminalSn = dto.getTerminalSn();
        Map terminal = terminalService.getTerminalBySn(terminalSn);

        // 获取商户ID
        String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
        // 根据商户ID获取商户信息
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        // 获取商户序列号
        String merchantSn = merchant.getSn();

        // 获取刷卡业务开通状态
        DirectStatus directStatus = Optional.ofNullable(directStatusBiz.getDirectStatusByMerchantSnAndDevCode(merchantSn, lklPsoDevCode))
                .orElseGet(DirectStatus::new);
        // 如果状态不成功，则抛出异常
        if(!Objects.equals(DirectStatus.STATUS_SUCCESS,directStatus.getStatus())) {
            throw new CommonPubBizException(PosConstant.APPLY_OPEN);
        }

        // 当前使用的收单机构
        ContractStatus contractStatus = Optional.ofNullable(contractStatusService.selectByMerchantSn(merchantSn))
                .orElseGet(ContractStatus::new);
        String acquirer = contractStatus.getAcquirer();
        List<String> indirectAcquirerList = mcAcquirerDAO.getIndirectAcquirerList();
        //间连非拉卡拉
        if(indirectAcquirerList.contains(acquirer) && !Objects.equals(acquirer,McConstant.ACQUIRER_LKLV3)) {
            //当前不是拉卡拉，则抛出无法激活的异常
            throw new CommonPubBizException(PosConstant.CAN_NOT_ACTIVE);
        }

        // 获取终端ID和店铺ID
        String terminalId = BeanUtil.getPropString(terminal, DaoConstants.ID);
        String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        // 根据店铺ID获取店铺信息
        StoreInfo store = storeService.getStoreById(storeId, null);
        // 获取终端激活信息
        LklV3BankTerm info = lklV3ShopTermBiz.getTermActiveInfo(terminal, terminalId, store, merchant);

        // 检查激活信息是否存在且激活号不为空
        boolean exist = Optional.ofNullable(info).filter(term -> StringUtils.isNotBlank(term.getActiveNo())).isPresent();
        //激活码不存在
        if(!exist) {
            processLKLTerminalTask(store,terminal);
        }
        //开通多业务的时候写入新的清算通道
        try {
            tradeConfigClient.updateClearProviderByAcquirer(merchantId, McConstant.ACQUIRER_LKLV3);
        } catch (Exception e) {
            log.error("清算通道设置失败 商户号:{}",merchantSn,e);
            throw new CommonPubBizException("清算通道设置失败");
        }
        // 创建并返回POS激活信息
        PosActiveInfo posActiveInfo = new PosActiveInfo().setActiveNo(info.getActiveNo());
        return posActiveInfo;
    }

    @Override
    public List<LimitResp> queryLimit(String merchantSn) {
        final String providerMerchantId = Optional.ofNullable(merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue()))
                .map(MerchantProviderParams::getProvider_merchant_id)
                .orElse(null);
        //还没有进件成功
        if (org.apache.commons.lang3.StringUtils.isEmpty(providerMerchantId)) {
            return new ArrayList<>();
        }
        final LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
        return lklV3Service.queryLimit(providerMerchantId, lklV3Param);
    }

    @Override
    public String getDevCode() {
        return this.lklPsoDevCode;
    }

    @Override
    public String getForeignCardDevCode() {
        return this.lklForeignCardDevCode;
    }

    @Override
    public boolean isSupport(String devCode) {
        return Objects.equals(this.lklPsoDevCode, devCode);
    }



    public void processLKLTerminalTask(StoreInfo store, Map<String, Object> terminal) {
        final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        String storeSn = store.getSn();
        LklV3ShopTerm shopTerm = getShopTerm(storeSn);
        try {
            if (StringUtils.isBlank(shopTerm.getShopId())) {
                //增网问题
                processStoreTask(shopTerm, storeSn);
            } else {
                //增终问题
                processTerminalTask(shopTerm, terminal);
            }
        } catch (CommonPubBizException e) {
            log.error("processLKLTerminalTask CommonPubBizException,terminalSn:{},storeSn:{}",terminalSn,storeSn,e);
            throw new CommonPubBizException(e.getMessage());
        }catch (Exception e) {
            log.error("processLKLTerminalTask Exception,terminalSn:{},storeSn:{}",terminalSn,storeSn,e);
            throw new CommonPubBizException(PosConstant.ACTIVE_NO_FAIL);
        }
    }


    /**
     * 门店没有对应的记录
     * @param storeSn
     * @return
     */
    private LklV3ShopTerm getShopTerm(String storeSn) {
        LklV3ShopTerm shopTerm = lklV3ShopTermBiz.selectLklV3ShopTermByStoreSn(storeSn);
        if (Objects.isNull(shopTerm)) {
            throw new CommonPubBizException(PosConstant.ACTIVE_NO_FAIL);
        }
        return shopTerm;
    }

    /**
     * 门店级别问题
     * @param shopTerm
     * @param storeSn
     */
    private void processStoreTask(LklV3ShopTerm shopTerm, String storeSn) {
        Long subTaskId = shopTerm.getLklV3TermInfo().stream()
                .filter(x -> storeSn.equalsIgnoreCase(x.getDevSerialNo()))
                .findAny()
                .map(ter -> ter.getSubTaskId())
                .orElse(0L);

        handleTaskStatus(getContractTask(subTaskId));
    }

    /**
     * 终端级别问题
     * @param shopTerm
     * @param terminal
     */
    private void processTerminalTask(LklV3ShopTerm shopTerm, Map<String, Object> terminal) {
        Long subTaskId = Optional.ofNullable(shopTerm)
                .map(LklV3ShopTerm::getLklV3TermInfo)
                .orElseGet(ArrayList::new)
                .stream()
                .filter(x -> MapUtils.getString(terminal, DaoConstants.ID).equalsIgnoreCase(x.getDevSerialNo()))
                .findAny()
                .map(ter -> ter.getSubTaskId())
                .orElse(0L);

        handleTaskStatus(getContractTask(subTaskId));
    }

    private ContractTask getContractTask(Long subTaskId) {
        return Optional.ofNullable(contractSubTaskMapper.selectByPrimaryKey(subTaskId))
                .map(ContractSubTask::getP_task_id)
                .map(contractTaskMapper::selectByPrimaryKey)
                .orElse(new ContractTask());
    }

    private void handleTaskStatus(ContractTask contractTask) {
        Integer status = contractTask.getStatus();
        if (Objects.equals(status, ContractTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())) {
            throw new CommonPubBizException(PosConstant.ACTIVE_NO_FAIL);
        }
        if (Objects.equals(status, ContractTaskProcessStatusEnum.WAIT_PROCESS.getValue())) {
            throw new CommonPubBizException(getErrorMessage("限流"));
        }
        if (Objects.equals(status, ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue())) {
            throw new CommonPubBizException(getErrorMessage("增网增终失败"));
        }
        throw new CommonPubBizException(getErrorMessage("增网增终审核"));
    }

    /**
     * 错误码管理平台
     * @param errorType
     * @return
     */
    private String getErrorMessage(String errorType) {
        return errorCodeManageBiz.getPromptMessageFromErrorCodeManager(SPA_SYSTEM, errorType, LKL_ACTIVE).getMsg();
    }


    @Override
    public void closeForeignCard(String merchantSn) {
        //删除外卡记录
        final ForeignCard foreignCard = Optional.ofNullable(foreignCardMapper.selectSuccessByMerchantSnAndCode(merchantSn,lklForeignCardDevCode))
                .orElseGet(ForeignCard::new);
        if(Objects.nonNull(foreignCard.getId())) {
            foreignCardMapper.deleteByPrimaryKey(foreignCard.getId());
        }
        directStatusDAO.deleteByMerchantSnAndDevCode(merchantSn,lklForeignCardDevCode);

    }


    @Override
    public void closePreAuth(String merchantSn) {
        final PreAuthApply preAuthApply = preAuthApplyMapper.selectLKlByMerchantSn(merchantSn);
        Optional.ofNullable(preAuthApply)
                .filter(apply -> Objects.equals(apply.getStatus(), PreAuthApplyConstant.Status.SUCCESS))
                .ifPresent(apply -> preAuthApplyMapper.deleteByPrimaryKey(apply.getId()));

    }

}
