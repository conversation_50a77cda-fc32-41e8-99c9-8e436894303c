package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.ContractTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractTaskTypeEnum;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.DataBusBiz;
import com.wosai.upay.job.biz.WeixinAuthApplyBiz;
import com.wosai.upay.job.biz.direct.AliDirectBiz;
import com.wosai.upay.job.biz.direct.WeixinDirectBiz;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MchAuthApplyMapper;
import com.wosai.upay.job.mapper.TaskMchMapper;
import com.wosai.upay.job.model.ContractStatusCode;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.MchAuthInfo;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;


/**
 * @Author: lishuangqiang
 * @Date: 2019/4/1
 * @Description:
 */
@Service
@AutoJsonRpcServiceImpl
public class ContractTaskResultServiceImpl implements ContractTaskResultService {


    public static final Logger logger = LoggerFactory.getLogger(ContractTaskResultServiceImpl.class);


    @Autowired
    ContractTaskMapper contractTaskMapper;
    @Autowired
    ContractStatusServieImpl contractStatusService;
    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    DataBusBiz dataBusBiz;
    @Autowired
    BankCardServiceImpl bankCardService;
    @Autowired
    MerchantService merchantService;

    @Autowired
    WeixinAuthApplyBiz weixinAuthApplyBiz;

    @Autowired
    private AliDirectBiz aliDirectBiz;

    @Autowired
    private WeixinDirectBiz weixinDirectBiz;

    @Autowired
    MchAuthApplyMapper mchAuthApplyMapper;

    @Autowired
    TaskMchMapper taskMchMapper;

    @Override
    public Map<String, Object> getContractasks(Map queryFilter) {
        Map<String, Object> result = new HashMap<>();
        Integer pageNum = queryFilter.get("pageNum") != null ? (Integer) queryFilter.get("pageNum") : 1;
        Integer pageSize = queryFilter.get("pageSize") != null ? (Integer) queryFilter.get("pageSize") : 10;
        PageHelper.startPage(pageNum, pageSize, false);
        long current = System.currentTimeMillis();
        long zero = current / (1000 * 3600 * 24) * (1000 * 3600 * 24) - TimeZone.getDefault().getRawOffset();
        long end = zero + 24 * 60 * 60 * 1000 - 1;
        String createAtStart = queryFilter.get("create_at_start") != null ? (String) queryFilter.get("create_at_start") : StringUtil.formatDate(zero);
        String createAtEnd = queryFilter.get("create_at_end") != null ? (String) queryFilter.get("create_at_end") : StringUtil.formatDate(end);
        String merchantSn = (String) queryFilter.get("merchant_sn");
        Integer status = (Integer) queryFilter.get("status");
        String type = (String) queryFilter.get("type");
        if (!StringUtils.isEmpty(type)) {
            type = type.replaceAll("至拉卡拉", "");
        }
        if (ProviderUtil.CONTRACT_TYPE_AUTH.equals(type) && status != null && status == 1) {
            status = 2;
        }
        if (WosaiStringUtils.isNotBlank(merchantSn)) {
            createAtStart = null;
            createAtEnd = null;
        }
        List<ContractTask> resultData = contractTaskMapper.selectByParams(merchantSn, status, type, createAtStart, createAtEnd);
        for (ContractTask contractTask : resultData) {
            String contract_memo = "";
            try {
                contract_memo = this.getContractMemo(contractTask);
            } catch (Exception e) {
                logger.warn("get contract memo error", e);
            }
            if (ProviderUtil.CONTRACT_TYPE_AUTH.equalsIgnoreCase(contractTask.getType())) {
                Map sp = contractStatusService.doGetWeixinUpgrade(contractTask, "sp");
                String contract_code = MapUtils.getString(sp, "contract_code");
                if (contract_code.equalsIgnoreCase(ContractStatusCode.UPGRADE_WAIT_FOR_AUTH.getCode())) {
                    contractTask.setNeed_auth(true);
                }
                Map eventContext = contractTask.getEventContext();
                MchAuthInfo mchAuthInfo = weixinAuthApplyBiz.getMchAuthApplyByTaskId(contractTask.getId());
                if (Objects.nonNull(mchAuthInfo)) {
                    contractTask.setAuth_type(mchAuthInfo.getType());
                    eventContext.put("forceAuth", mchAuthInfo.getForceAuth());
                } else {
                    eventContext.put("forceAuth", true);
                }
                contractTask.setEvent_context(JSON.toJSONString(eventContext));
            } else if (ProviderUtil.CONTRACT_TYPE_ALIPAY_AUTH.equalsIgnoreCase(contractTask.getType())) {
                Map eventContext = contractTask.getEventContext();
                contractTask.setEvent_context(JSON.toJSONString(eventContext));
                contractTask.setResult("请从支付业务-支付源申请单管理中具体进度及状态描述");
            }
            if (!StringUtil.empty(contract_memo)) {
                contractTask.setResult(contract_memo);
            }
        }
        result.put("data", resultData);
        result.put("total", 10000);
        return result;
    }

    /**
     * 转义信息，异常不成功则直接返回原来的信息
     * @param contractTask
     * @return
     */
    public String getEscapedContent(ContractTask contractTask) {
        try {
            return getContractMemo(contractTask);
        } catch (Exception e) {
            return contractTask.getResult();
        }
    }

    public String getContractMemo(ContractTask contractTask) {
        if (contractTask.getType().contains("直连")) {
            if (ProviderUtil.ALI_DIRECT_APPLY.equals(contractTask.getType())) {
                return aliDirectBiz.getAliDirectContractMemo(contractTask, "sp").getContract_memo();
            } else {
                return weixinDirectBiz.getWeixinDirectContractMemo(contractTask, "sp").getContract_memo();
            }
        }
        if (Objects.equals(contractTask.getType(), ContractTaskTypeEnum.MICRO_UPGRADE.getValue())) {
            Optional<ContractTaskProcessStatusEnum> statusEnum = EnumUtils.ofNullable(ContractTaskProcessStatusEnum.class, contractTask.getStatus());
            if (statusEnum.isPresent()) {
                return statusEnum.get().getText();
            }
            return ContractStatusCode.UNKNOWN_STATUS_CODE.getMsg();
        }
        return contractStatusService.getMessageByContractTask(contractTask);
    }


    /**
     * <AUTHOR>
     * @Description: 银行直连, contract_task表中status=1 处理中文案处理
     * @time 09:29
     */
    public static String getBankApplyContractMemo(ContractTask contractTask) {
        final String result = contractTask.getResult();
        if (StringUtils.isEmpty(result) || result.contains("500")) {
            return "审核中,请稍后";
        }
        return result;
    }


    @Override
    public ContractTask selectByPrimaryKey(Long id) {
        return contractTaskMapper.selectByPrimaryKey(id);
    }

    @Override
    public ContractTask getWechatAuthSucceedTask(String merchantSn, String mchId) {
        List<ContractTask> contractsBySnAndType = contractTaskMapper.getContractsBySnAndType(merchantSn, ProviderUtil.CONTRACT_TYPE_AUTH);
        if (StringUtil.listEmpty(contractsBySnAndType)) {
            return null;
        }
        for (ContractTask contractTask : contractsBySnAndType) {
            ContractSubTask weixinSub = contractSubTaskMapper.getWeixinSub(contractTask.getId());
            if (Objects.isNull(weixinSub)) {
                continue;
            }
            Map map = JSON.parseObject(weixinSub.getResponse_body(), Map.class);
            String payMerchtId = (String) BeanUtil.getNestedProperty(map, "tradeParam.weixin_merchant_id");
            if (!StringUtil.empty(payMerchtId) && mchId.equalsIgnoreCase(payMerchtId)) {
                return contractTask;
            }
        }
        return null;
    }
}
