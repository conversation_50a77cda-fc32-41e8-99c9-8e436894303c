package com.wosai.upay.job.xxljob.batch.paylater;

import com.wosai.upay.job.Constants.PayLaterConstant;
import com.wosai.upay.job.biz.PayLaterBiz;
import com.wosai.upay.job.mapper.PayLaterApplyMapper;
import com.wosai.upay.job.model.payLater.PayLaterApply;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * xxl_job_desc: 先享后付创建芝麻商户
 * <AUTHOR>
 * @date 2025/4/2
 */
@Slf4j
@Component("PayLaterCreateZhimaMerchantJobHandler")
public class PayLaterCreateZhimaMerchantJobHandler extends AbstractBatchJobHandler<PayLaterApply> {

    @Autowired
    private PayLaterBiz payLaterBiz;
    @Autowired
    private PayLaterApplyMapper payLaterApplyMapper;

    @Override
    public List<PayLaterApply> queryTaskItems(BatchJobParam param) {
        return payLaterBiz.getPayLaterTasks(Collections.singletonList(PayLaterConstant.ProcessStatus.ZFT_SUCCESS), param.getBatchSize(), param.getQueryTime());
    }

    @Override
    public String getLockKey(PayLaterApply payLaterApply) {
        return "PayLaterCreateZhimaMerchantJobHandler:" + payLaterApply.getId();
    }

    @Override
    public void doHandleSingleData(PayLaterApply payLaterApply) {
        try {
            PayLaterApply apply = payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId());
            if (!PayLaterConstant.ProcessStatus.ZFT_SUCCESS.equals(apply.getProcess_status())) {
                return;
            }
            payLaterBiz.createZhiMaMerchant(payLaterApply);
        } catch (Exception e) {
            log.error("商户号:{} 异常", payLaterApply.getMerchant_sn(), e);
            payLaterBiz.modifyPayLaterApply(payLaterApply, PayLaterConstant.Status.ZHIMA_FAIL,
                    PayLaterConstant.SubStatus.FAIL,
                    PayLaterConstant.ProcessStatus.FAIL,
                    PayLaterConstant.Result.ZHIMA_AUDIT_FAIL,
                    0);
        }
    }
}
