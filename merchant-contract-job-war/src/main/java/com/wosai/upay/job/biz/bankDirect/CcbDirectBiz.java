package com.wosai.upay.job.biz.bankDirect;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.paramContext.FeeRateUtil;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.enume.BankDirectApplyViewStatusEnum;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ViewProcess;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.model.psbc.SelfAuditRejectRequest;
import com.wosai.upay.job.providers.CcbProvider;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.wosai.upay.job.providers.PsbcProvider.replaceHttp;

/**
 * <AUTHOR>
 * @date 2021/8/6
 */
@Component
public class CcbDirectBiz extends AbstractBankDirectApplyBiz {

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private CcbProvider ccbProvider;

    @Value("${ccb_dev_code}")
    private String ccbDevCode;

    @Override
    public String getDevCode() {
        return ccbDevCode;
    }

    /**
     * 根据上下文和设备代码选择规则组
     * 该方法主要用于确定给定的上下文和设备代码应该使用哪个规则组进行处理
     * 它首先尝试根据上下文中的商户地区代码来选择规则组如果找不到精确匹配，
     * 则会尝试使用更广泛的地区代码（如市或省）如果仍然找不到匹配项，
     * 则返回默认规则组
     *
     * @param context 包含请求上下文信息的映射，包括商户信息等
     * @param devCode 设备代码，用于标识特定的设备或渠道
     * @return 返回选定的规则组字符串如果无法根据地区代码找到规则组，
     * 则返回默认规则组
     */
    @Override
    protected String chooseGroupByContextAndDevCode(Map<String, Object> context, String devCode) {
        Map<String, String> ccbRuleGroup = applicationApolloConfig.getCcbRuleGroup();
        Map merchant = (Map) context.get(ParamContextBiz.KEY_MERCHANT);
        String districtCode = WosaiMapUtils.getString(merchant, Merchant.DISTRICT_CODE);
        if (ccbRuleGroup.containsKey(districtCode)) {
            return ccbRuleGroup.get(districtCode);
        }
        districtCode = districtCode.substring(0, 4) + "00";
        if (ccbRuleGroup.containsKey(districtCode)) {
            return ccbRuleGroup.get(districtCode);
        }
        districtCode = districtCode.substring(0, 2) + "0000";
        if (ccbRuleGroup.containsKey(districtCode)) {
            return ccbRuleGroup.get(districtCode);
        }
        return WosaiMapUtils.getString(ccbRuleGroup, "default", McConstant.RULE_GROUP_CCB);
    }

    @Override
    protected Map<String, Object> completeContext(Map<String, Object> paramContext, BankDirectReq bankDirectReq) {
        Map formBody = JSON.parseObject(bankDirectReq.getForm_body(), Map.class);
        //放入费率
        List config = (List) formBody.get("merchant_config");
        paramContext.put("ccb_feeRate", config);
        //放入套餐
        paramContext.put("trade_combo_id", BeanUtil.getPropString(formBody, "trade_combo_id"));
        //放入业务标识
        paramContext.put("dev_code", ccbDevCode);
        // 放入建行预留手机号
        paramContext.put("phone_number", BeanUtil.getPropString(formBody, "phone_number"));
        // 放入数字钱包
        paramContext.put("digital_wallet_number", BeanUtil.getPropString(formBody, "digital_wallet_number"));
        // 放入辅助证明材料
        paramContext.put("support_materials", WosaiMapUtils.getMap(formBody, "support_materials"));
        // 合照
        paramContext.put("out_door_photo", BeanUtil.getPropString(formBody, "out_door_photo"));
        // 法人手持身份证照片
        paramContext.put("handheld_id_card_photo", BeanUtil.getPropString(formBody, "handheld_id_card_photo"));
        // 备注
        paramContext.put("remark", BeanUtil.getPropString(formBody, "remark"));
        //放入数据来源标记
        paramContext.put("from",BeanUtil.getPropString(formBody, "from"));
        //放入机构行号
        paramContext.put("ins_no",BeanUtil.getPropString(formBody, "ins_no"));

        //放入spa可以识别的费率信息
        final List<Map> list = ((List<Map>) config).stream().map(x -> {
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.WEIXIN.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.WECHAT_PAY_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.ALIPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.ALIPAY_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.UNIONPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_DEBIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            return null;
        }).filter(x -> Objects.nonNull(x)).collect(Collectors.toList());
        paramContext.putIfAbsent(ParamContextBiz.MERCHANT_FEE_RATES, list);
        return paramContext;
    }

    @Override
    protected Integer getBankRef(String dev_code) {
        return BankDirectApplyRefEnum.CCB.getValue();
    }

    @Override
    protected String getAcquire() {
        return AcquirerTypeEnum.CCB.getValue();
    }

    @Override
    protected Integer getProvider() {
        return ProviderEnum.PROVIDER_CCB.getValue();
    }

    @Override
    public List<ViewProcess> initViewProcess(String merchantSn) {
        final List<ViewProcess> viewProcesses = preViewProcess(getAcquire());
        if(CollectionUtils.isEmpty(viewProcesses)) {
            return Lists.newArrayList();
        }
        List<ViewProcess> list;
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, getDevCode());
        //营业执照类型
        final Integer licenseType = license.getType();
        //对私商户
        if(licenseType.equals(BusinessLicenseTypeEnum.MICRO.getValue())) {
            list = viewProcesses.stream().filter(x -> !x.getViewStatus().equals(BankDirectApplyViewStatusEnum.AUTHING_ENTERPRISE.getValue())).collect(Collectors.toList());
        }else {
            //对公商户
            list = viewProcesses.stream().filter(x -> !x.getViewStatus().equals(BankDirectApplyViewStatusEnum.AUTHING.getValue())).collect(Collectors.toList());
        }
        //设置微信图片地址链接
        list.stream().forEach(x-> {
            if(Objects.equals(x.getExtra(),Boolean.TRUE)) {
                final String imageUrl = getWXImageUrl(getAcquire(), getProvider());
                x.setExtraMessage(imageUrl);
                x.setAliMessage(replaceHttp("https://images.wosaimg.com/43/94c324ceebb13328dd8d980818e6d3f4f57756.png"));
            }
        });
        return list;
    }

    @Override
    public com.wosai.upay.merchant.contract.model.ContractResponse doReject(String merchantSn, SelfAuditRejectRequest request, ContractSubTask contractSubTask) {
        final ContractResponse response = new ContractResponse();
        response.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION);
        try {
            final Boolean toBeSigned = ccbProvider.checkBankContractToBeSigned(contractSubTask);
            if(toBeSigned) {
                response.setCode(Constant.RESULT_CODE_SUCCESSS);
            } else {
                response.setMessage("当前商户审核状态不支持自助驳回");
            }
        } catch (Exception exception) {
            response.setMessage(ExceptionUtil.getThrowableMsg(exception));
        }
        return response;
    }
}
