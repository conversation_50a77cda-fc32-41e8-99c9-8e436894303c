package com.wosai.upay.job.biz;

/**
 * 开通九九折处理逻辑
 *
 * <AUTHOR>
 * @date 2020-10-23
 */

import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonDataObjectNotExistsException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.enume.JjzNormalRateEnum;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.dto.WeixinSubAppidDto;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OpenJjzBiz {

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    ContractStatusMapper contractStatusMapper;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private SyncBiz syncBiz;

    @Autowired
    @Lazy
    private MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    private MerchantConfigParamsBiz merchantConfigParamsBiz;

    @Value("${tradeAppId}")
    private String tradeAppId;

    public static final String SUCCESS_MSG = "业务处理成功";

    public CommonResult handlerJjzBiz(String merchantSn, String subAppId, Integer rateType) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andProviderGreaterThan(1000)
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);

        List<MerchantProviderParams> wxParams = Optional.ofNullable(params).orElseGet(ArrayList::new).stream()
                .filter(param -> Objects.equals(PaywayEnum.WEIXIN.getValue(), param.getPayway())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wxParams)) {
            throw new com.wosai.common.exception.CommonPubBizException("商户间连微信支付未报备");
        }
        //根据商户号获取商户
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        //当前商户报备信息
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(MapUtils.getString(merchant, Merchant.SN));
        //为当前正在使用的微信子商户号配置久久折小程序appId
        wxParams.forEach(param -> {
            //前置校验
            preCheck(merchant, contractStatus);
            // 配置小程序 appid
            addJjzWxAppid(subAppId, param);
        });
        //根据配置获取久久折业务方标识
        String appId = tradeAppId;
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        //久久折0费率
        params.stream().filter(param -> Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()).contains(param.getPayway())).forEach(param -> merchantConfigParamsBiz.handleMerchantAppConfig(merchantId, param.getPayway(), subAppId, appId, "0.0"));
        //删除交易网关缓存
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, Merchant.SN));
        //获取哪些支付方式开通了0费率
        final List<Integer> payWayList = params.stream().filter(param -> Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()).contains(param.getPayway())).map(MerchantProviderParams::getPayway).collect(Collectors.toList());
        return new CommonResult(CommonResult.SUCCESS, SUCCESS_MSG,payWayList);
    }

    @Transactional(rollbackFor = Exception.class)
    public MerchantProviderParamsDto addJjzWxAppid(String subAppId, MerchantProviderParams merchantProviderParams) {
        //构建小程序信息
        WeixinSubAppidDto weixinSubAppidDto = new WeixinSubAppidDto();
        weixinSubAppidDto.setApp("久久折").setRemark("久久折配置").setSub_appid(subAppId).setType(2);
        //更新MerchantProviderParams表中extra并且将sub_appid配置到微信
        return merchantProviderParamsService.justAddWeixinSubAppid(merchantProviderParams.getId(), weixinSubAppidDto);
    }


    /**
     * @param merchant
     * @param contractStatus
     */
    private void preCheck(Map merchant, ContractStatus contractStatus) {
        if (MapUtils.isEmpty(merchant)) {
            throw new CommonDataObjectNotExistsException("没有找到该商户号对应的商户");
        }
        //1、商户是否报备完成
        if (Objects.isNull(contractStatus) || !Objects.equals(contractStatus.getStatus(), ContractStatus.STATUS_SUCCESS)) {
            throw new CommonPubBizException("商户报备未完成");
        }
    }

    public CommonResult handlerJjzNormalRate(String merchantSn, String subAppId) {
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        if (MapUtils.isEmpty(merchant)) {
            throw new CommonDataObjectNotExistsException("没有找到该商户号对应的商户");
        }
        //当前商户报备信息
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(MapUtils.getString(merchant, Merchant.SN));
        //1、商户是否报备完成
        if (Objects.isNull(contractStatus) || !Objects.equals(contractStatus.getStatus(), ContractStatus.STATUS_SUCCESS)) {
            throw new CommonPubBizException("报备未完成");
        }
        //微信报备参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);

        if (CollectionUtils.isEmpty(params)) {
            throw new CommonPubBizException("微信交易参数未使用");
        }
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        //交易参数
        final Map map = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.WEIXIN.getValue());
        final boolean formal = syncBiz.isFormal(map);
        if (formal) {
            final JjzNormalRateEnum directWeixinPay = JjzNormalRateEnum.DIRECT_WEIXIN_PAY;
            return new CommonResult(directWeixinPay.getCode(), directWeixinPay.getMessage());
        }
        //为当前正在使用的所有微信子商户号添加久久折小程序appId
        params.forEach(merchantProviderParams -> addJjzWxAppid(subAppId, merchantProviderParams));
        return new CommonResult(CommonResult.SUCCESS, "添加成功");
    }
}
