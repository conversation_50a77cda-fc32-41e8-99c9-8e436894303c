package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.providers.CcbProvider;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/6
 */
@Component("ccb-AcquirerChangeBiz")
@Slf4j
public class ChangeToCcbBizBank extends AbstractBankDirectAcquirerChangeBiz {

    @Value("${ccb_dev_code}")
    public String ccbDevCode;
    @Autowired
    private ContractSubTaskMapper mapper;
    @Autowired
    private CcbProvider ccbProvider;

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_CCB.getValue();
    }

    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_CCB_RULE_GROUP;
    }

    @Override
    public String getDevCode(String acquirer) {
        return ccbDevCode;
    }

    @Override
    protected long getDefaultComboId(String acquirer) {
        return 128L;
    }

    @Override
    public void configDigitalCurrency(String merchantSn) {
        // 去contractSubTask表中 去查询一条记录  然后去配置
        ContractSubTask subTask = mapper.getCcbInsertSubTask(merchantSn);
        if (subTask == null) {
            log.error("商户 {} 切建行收单机构 开通数币受理 subtask进件记录没查询到", merchantSn);
            return;
        }
        if (StringUtils.isEmpty(subTask.getResponse_body())) {
            log.error("商户 {} 切建行收单机构 开通数币受理 进件信息为空或进件失败", merchantSn);
            return;
        }
        ContractResponse contractResponse = new ContractResponse();
        contractResponse.setTradeParam(JSONObject.parseObject(subTask.getResponse_body(), Map.class));
        // 配置数币受理
        ccbProvider.configDigitalCurrency(subTask, contractResponse);
    }

}
