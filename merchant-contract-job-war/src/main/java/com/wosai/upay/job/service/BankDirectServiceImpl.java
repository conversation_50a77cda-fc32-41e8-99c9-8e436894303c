package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.job.Constants.CcbConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.SelfOpenCcbDecpBiz;
import com.wosai.upay.job.biz.bankDirect.BankHandleService;
import com.wosai.upay.job.biz.bankDirect.BankHandleServiceFactory;
import com.wosai.upay.job.biz.store.StoreBiz;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.externalservice.coreb.TradeConfigClient;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.ProviderTerminalMapper;
import com.wosai.upay.job.mapper.PsbcTerminalMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.direct.ApplyStatusResp;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.model.psbc.ModifySupportingMaterialsRequest;
import com.wosai.upay.job.model.psbc.SelfAuditRejectRequest;
import com.wosai.upay.job.providers.PabProvider;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.Utils;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ccb.response.CcbContractResp;
import com.wosai.upay.merchant.contract.model.pingan.response.GetAgreementStatusResponse;
import com.wosai.upay.merchant.contract.model.pingan.response.SubmitContractInfoResponse;
import com.wosai.upay.merchant.contract.model.provider.HXParam;
import com.wosai.upay.merchant.contract.model.psbc.response.QrCodeBatchApplyResponse;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.UpdateTermInfoDTO;
import com.wosai.upay.merchant.contract.service.HXService;
import com.wosai.upay.merchant.contract.service.PingAnService;
import com.wosai.upay.merchant.contract.service.PsbcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description: 开通行业务实现
 * <AUTHOR>
 * @Date 2021/4/7 09:58
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class BankDirectServiceImpl implements BankDirectService {
    @Autowired
    private BankHandleServiceFactory factory;

    @Autowired
    private SelfOpenCcbDecpBiz selfOpenCcbDecpBiz;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    PsbcService psbcService;

    @Autowired
    StoreService storeService;
    @Autowired
    com.wosai.mc.service.MerchantService mcMerchantService;
    @Autowired
    PsbcTerminalMapper psbcTerminalMapper;
    @Autowired
    TerminalService terminalService;

    @Autowired
    ContractStatusService contractStatusService;

    @Autowired
    protected PingAnService pingAnService;

    @Autowired
    private PabProvider pabProvider;

    @Autowired
    private HXService hxService;

    @Autowired
    private ProviderTerminalMapper providerTerminalMapper;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private ContractApplicationService contractApplicationService;

    @Autowired
    private StoreBiz storeBiz;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private TradeConfigClient tradeConfigClient;


    @Override
    public ContractResponse applyBankDirect(BankDirectReq bankDirectReq) {
        final BankHandleService handleService = factory.getBankHandleService(bankDirectReq.getDev_code());
        return handleService.applyBankDirect(bankDirectReq);
    }

    @Override
    public ApplyStatusResp getApplyInfo(String merchantSn, String devCode, String platform) {
        final BankHandleService handleService = factory.getBankHandleService(devCode);
        return handleService.getApplyInfo(merchantSn, devCode, platform);
    }

    @Override
    public Integer getApplyStatus(String merchantSn, String devCode) {
        final BankHandleService handleService = factory.getBankHandleService(devCode);
        return handleService.getApplyStatus(merchantSn, devCode);
    }

    @Override
    public Integer getContractTaskStatus(String merchantSn, String devCode) {
        final BankHandleService handleService = factory.getBankHandleService(devCode);
        return handleService.getContractTaskStatus(merchantSn, devCode);
    }

    @Override
    public Map<String, String> getSignParamForCcb(Map request) {
        String merchantId = BeanUtil.getPropString(request, MerchantBankAccount.MERCHANT_ID);
        if (WosaiStringUtils.isEmpty(merchantId)) {
            throw new ContractBizException("商户id不能为空");
        }
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        if (MapUtils.isEmpty(merchant)) {
            throw new ContractBizException("商户不存在");
        }
        ContractSubTask contractSubTask = contractSubTaskMapper.getCcbInsertSubTask(BeanUtil.getPropString(merchant, Merchant.SN));
        if (contractSubTask == null) {
            throw new ContractBizException("建行进件子任务不存在");
        }
        if (contractSubTask.getStatus().equals(TaskStatus.FAIL.getVal())) {
            throw new ContractBizException("建行申请已失败，请重新提交");
        }
        if (contractSubTask.getStatus().equals(TaskStatus.SUCCESS.getVal())) {
            throw new ContractBizException("建行申请已成功");
        }
        // 进行中的子任务有两种情况，一种待签约 一种签约成功在银行审核中  所以这里要根据result判断一下
        // 不是待签约的状态直接报错
        if (!CcbConstant.WAIT_FOR_SIGN.equals(contractSubTask.getResult())) {
            throw new ContractBizException("建行申请不处于待签约状态");
        }
        Map responseBody = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
        Map responseParam = MapUtils.getMap(responseBody, "responseParam");
        Map dataInfo = MapUtils.getMap(responseParam, "dataInfo");
        if (WosaiMapUtils.isEmpty(dataInfo)) {
            throw new ContractBizException("建行返回数据为空");
        }
        CcbContractResp ccbContractResp = JSON.parseObject(JSON.toJSONString(dataInfo), CcbContractResp.class);
        return CollectionUtil.hashMap("url", ccbContractResp.getUrl(), "ccbParam", ccbContractResp.getParam().getCcbParam(), "sysId", ccbContractResp.getParam().getSysId());
    }

    @Override
    public ContractResponse openCcbDecp(Map request) {
        String merchantId = BeanUtil.getPropString(request, MerchantBankAccount.MERCHANT_ID);
        String phoneNumber = BeanUtil.getPropString(request, "phone_number");
        String walletNumber = BeanUtil.getPropString(request, "wallet_number");
        if (WosaiStringUtils.isEmpty(merchantId) || WosaiStringUtils.isEmpty(walletNumber)) {
            throw new ContractBizException("商户id或者钱包号不能为空");
        }
        String key = "self_open_ccb_decp:" + merchantId;
        if (!redisLock.lock(key, key, 30L)) {
            throw new CommonPubBizException("请求频繁，请稍后重试");
        }
        try {
            return selfOpenCcbDecpBiz.openCcbDecp(merchantId, phoneNumber, walletNumber);
        } catch (Exception e) {
            log.error("开通数字人民币收款异常:{}", merchantId, e);
            return new ContractResponse().setSuccess(false).setMsg(e.getMessage());
        } finally {
            // 释放掉锁
            redisLock.unlock(key, key);
        }
    }

    @Override
    public DecpParamResp getOpenDecpParam(Map request) {
        String merchantId = BeanUtil.getPropString(request, MerchantBankAccount.MERCHANT_ID);
        String ucUserId = BeanUtil.getPropString(request, "uc_user_id");
        if (WosaiStringUtils.isEmptyAny(merchantId, ucUserId)) {
            throw new ContractBizException("商户id或用户id不能为空");
        }
        return selfOpenCcbDecpBiz.getOpenDecpParam(merchantId, ucUserId);
    }

    @Override
    public DecpParamResp getSuccessDecpParam(Map request) {
        String merchantId = BeanUtil.getPropString(request, MerchantBankAccount.MERCHANT_ID);
        if (WosaiStringUtils.isEmpty(merchantId)) {
            throw new ContractBizException("商户id不能为空");
        }
        return selfOpenCcbDecpBiz.getSuccessDecpParam(merchantId);
    }

    @Override
    public ContractResponse cancelCcbDecp(Map request) {
        String merchantId = BeanUtil.getPropString(request, MerchantBankAccount.MERCHANT_ID);
        String identity = BeanUtil.getPropString(request, MerchantBankAccount.IDENTITY);
        if (WosaiStringUtils.isEmpty(merchantId) || WosaiStringUtils.isEmpty(identity)) {
            throw new ContractBizException("商户id或者证件号不能为空");
        }
        return selfOpenCcbDecpBiz.cancelCcbDecp(merchantId);
    }

    @Override
    public ContractResponse selfAuditReject(@Valid SelfAuditRejectRequest request) {
        final BankHandleService handleService = factory.getBankHandleService(request.getDevCode());
        return handleService.selfAuditReject(request);
    }

    @Override
    public List<ViewProcess> getViewProcess(@NotBlank(message = "商户号不能为空") String merchantSn, @NotBlank(message = "应用标识不能为空") String devCode) {
        final BankHandleService handleService = factory.getBankHandleService(devCode);
        return handleService.getViewProcess(merchantSn, devCode);
    }

    @Override
    public ContractResponse modifySupportingMaterials(@Valid ModifySupportingMaterialsRequest req) {
        final BankHandleService handleService = factory.getBankHandleService(req.getDevCode());
        return handleService.modifySupportingMaterials(req);
    }

    /**
     * 更新银行审批状态
     */
    @Override
    public void refreshAndHandleContractStatus(@NotBlank(message = "商户号不能为空") String merchantSn,
                                               @NotBlank(message = "应用标识不能为空") String devCode) {
        final BankHandleService handleService = factory.getBankHandleService(devCode);
        handleService.refreshAndHandleContractStatus(merchantSn, devCode);
    }


    @Override
    public void qrCodeBatchApply(Integer qrCount) {
        com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = psbcService.qrCodeBatchApply(qrCount);
        if (!contractResponse.isSuccess()) {
            log.info("qrCodeBatchApply 调用失败");
            return;
        }
        //获取邮储返回门店码信息
        Map<String, Object> responseParam = contractResponse.getResponseParam();
        if (CollectionUtils.isEmpty(responseParam)) {
            log.info("responseParam 为空");
            return;
        }
        QrCodeBatchApplyResponse response = new MyObjectMapper().convertValue(responseParam, QrCodeBatchApplyResponse.class);
        List<QrCodeBatchApplyResponse.QrCodeInfo> infos = response.getQrCodeInfoList();
        //插入数据库
        infos.parallelStream().forEach(info -> {
            PsbcTerminal psbcTerminal = new PsbcTerminal();
            psbcTerminal.setQr_code(info.getQrCode());
            psbcTerminal.setQr_code_id(info.getQrCodeId());
            psbcTerminalMapper.insertSelective(psbcTerminal);
        });
    }


    @Override
    public void psbcQrCodeOperate(String storeId, Integer operType, String qrCode) {
        StoreInfo storeInfo = Optional.ofNullable(storeService.getStoreById(storeId, null))
                .orElseThrow(() -> new CommonPubBizException("门店不存在"));
        String storeSn = storeInfo.getSn();
        Map merchant = Optional.ofNullable(merchantService.getMerchantByMerchantId(storeInfo.getMerchant_id()))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        //商户当前在不在邮储通道
        ContractStatus contractStatus = contractStatusService.selectByMerchantSn(merchantSn);
        boolean psbc = Optional.ofNullable(contractStatus)
                .filter(contract -> Objects.equals(contract.getAcquirer(), AcquirerTypeEnum.PSBC.getValue()))
                .isPresent();
        if (!psbc) {
            log.info("psbcQrCodeOperate 商户号:{},门店号:{},当前通道不在邮储", merchantSn, storeSn);
            throw new CommonPubBizException("当前通道不在邮储");
        }
        try {
            PsbcTerminal psbcTerminal = psbcTerminalMapper.selectByQrCode(qrCode);
            if (Objects.isNull(psbcTerminal) || Objects.equals(psbcTerminal.getStatus(), "1")) {
                log.info("psbcQrCodeOperate 商户号:{},门店号:{},qrCode:{}找不到记录或已经绑定", merchantSn, storeSn, qrCode);
                throw new CommonPubBizException("找不到对应终端或已经绑定");
            }
            String qrCodeId = psbcTerminal.getQr_code_id();
            com.wosai.upay.merchant.contract.model.ContractResponse response = psbcService.qrCodeOperate(merchantSn, operType, qrCodeId);
            if (response.isSuccess()) {
                //修改本地状态
                psbcTerminalMapper.updateByqrCodeId(String.valueOf(operType), qrCodeId, storeSn, merchantSn);
                //调用交易接口插入信息
                Map<String, Object> activationCodeV2 = terminalService.createActivationCodeV2(null, null, storeSn, "邮储预制码", 1);
                String code = BeanUtil.getPropString(activationCodeV2, "code");
                //TODO 待定
                // vendorAppAppid-产品申请
                //code-createActivationCodeV2这个返回
                //deviceFingerprint-邮储码的Id
                //name-邮储预制码(最好咨询一下产品)
                terminalService.activateV2("2023101100006007", code, null, qrCodeId, "邮储预制码", null, null, null, null, null);
            } else {
                throw new CommonPubBizException(response.getMessage());
            }
        } catch (Exception exception) {
            log.error("psbcQrCodeOperate merchantSn:{},storeSn:{},error :{}", merchantSn, storeSn, exception);
            throw new CommonPubBizException(exception.getMessage());
        }
    }

    @Override
    public String getPabSignUrl(Map map) {
        final String merchantId = MapUtils.getString(map, "merchant_id");
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        if (MapUtils.isEmpty(merchant)) {
            throw new ContractBizException("商户不存在");
        }
        ContractSubTask contractSubTask = contractSubTaskMapper.getPabSignSubTask(BeanUtil.getPropString(merchant, Merchant.SN));
        if (contractSubTask == null) {
            throw new ContractBizException("平安签约子任务不存在");
        }
        if (contractSubTask.getStatus().equals(TaskStatus.FAIL.getVal())) {
            throw new ContractBizException("签约信息提交失败,无需签约");
        }

        if (contractSubTask.getStatus().equals(TaskStatus.SUCCESS.getVal())) {
            throw new ContractBizException("签约已成功,请勿重复签约");
        }
        final Map responseBody = contractSubTask.getResponseBody();
        final SubmitContractInfoResponse submitContractInfoResponse = JSONObject.parseObject(BeanUtil.getPropString(responseBody, "responseParam"), SubmitContractInfoResponse.class);
        final String applyToken = submitContractInfoResponse.getData().getApplyToken();
        final String h5Url = pingAnService.getH5Url(applyToken);
        return h5Url;
    }


    @Override
    public void pabSignCallBack(Map map) {
        log.info("pab sign  callback : {}", JSON.toJSONString(map));
        final String agreementStatus = BeanUtil.getPropString(map, "agreementStatus");
        final String applyNo = BeanUtil.getPropString(map, "applyNo");
        try {
            final ContractSubTask contractSubTask = contractSubTaskMapper.selectByContractId(applyNo);
            if (Objects.isNull(contractSubTask) || SubTaskStatus.isFinish(contractSubTask.getStatus())) {
                return;
            }
            final GetAgreementStatusResponse getAgreementStatusResponse = new GetAgreementStatusResponse();
            final GetAgreementStatusResponse.Data data = new GetAgreementStatusResponse.Data();
            data.setAgreementStatus(agreementStatus);
            data.setApplyNo(applyNo);
            getAgreementStatusResponse.setData(data);

            com.wosai.upay.merchant.contract.model.ContractResponse response = new com.wosai.upay.merchant.contract.model.ContractResponse();
            response.setMessage("签约成功");
            response.setCode(Constant.RESULT_CODE_SUCCESSS);
            response.setResponseParam(cn.hutool.core.bean.BeanUtil.beanToMap(getAgreementStatusResponse, null));
            pabProvider.doHandleContractStatus(contractSubTask, response);
        } catch (Exception e) {
            log.error("pabSignCallBack error applyNo:{}", applyNo, e);
        }
    }

    @Override
    public ContractResponse restartHXTerminal(String payMerchantId, String providerTerminalId) {
        ContractResponse response = new ContractResponse();

        // 查找子商户号管理的商户，可能关联多个
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andPay_merchant_idEqualTo(payMerchantId)
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> merchantProviderParamsList = merchantProviderParamsMapper.selectByExample(example);
        if (WosaiCollectionUtils.isEmpty(merchantProviderParamsList)) {
            return response.setSuccess(Boolean.FALSE).setMsg("子商户号信息不存在");
        }

        // 查到终端信息
        ProviderTerminal queryParam = new ProviderTerminal();
        queryParam.setProvider(ProviderEnum.PROVIDER_HXB.getValue());
        queryParam.setProvider_terminal_id(providerTerminalId);
        List<ProviderTerminal> providerTerminalList = Optional.ofNullable(providerTerminalMapper.selectByCondition(queryParam))
                .orElse(new ArrayList<>())
                .stream()
                .filter(providerTerminal -> Objects.equals(providerTerminalId, providerTerminal.getProvider_terminal_id()))
                .collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(providerTerminalList)) {
            // 说明管理侧清理过了，要清理交易侧
            for (MerchantProviderParams params : merchantProviderParamsList) {
                tradeConfigClient.clearProviderTerminal(params.getMerchant_sn(), ProviderEnum.PROVIDER_HXB.getValue());
            }
            return new ContractResponse().setSuccess(true).setMsg("已清理小终端");
        }

        ProviderTerminal terminal = providerTerminalList.get(0);
        Optional<MerchantProviderParams> paramsOptional = merchantProviderParamsList.stream()
                .filter(p -> Objects.equals(p.getMerchant_sn(), terminal.getMerchant_sn()))
                .findFirst();
        if (!paramsOptional.isPresent()) {
            return new ContractResponse().setSuccess(false).setMsg("数据不匹配，需人工处理");
        }
        MerchantProviderParams merchantProviderParams = paramsOptional.get();

        AddTermInfoDTO dto = new AddTermInfoDTO();
        dto.setDeviceId(providerTerminalId);
        dto.setMerchantSn(terminal.getMerchant_sn());
        dto.setSubMchId(payMerchantId);
        //没有门店SN 默认取第一家门店
        String storeSn = terminal.getStore_sn();
        if (WosaiStringUtils.isBlank(storeSn)) {
            storeSn = storeBiz.getFirstStoreSnByMerchantSn(terminal.getMerchant_sn());
        }
        dto.setStoreSn(storeSn);
        HXParam hxParam = contractParamsBiz.buildContractParams(String.valueOf(merchantProviderParams.getProvider()), merchantProviderParams.getPayway(), merchantProviderParams.getChannel_no(), HXParam.class);
        com.wosai.upay.merchant.contract.model.ContractResponse terminalResp = hxService.addTermInfo(dto, hxParam, merchantProviderParams.getPayway());
        if (terminalResp.isSuccess()) {
            return response.setSuccess(true).setMsg("开通成功");
        } else {
            if (WosaiStringUtils.isNotEmpty(terminal.getTerminal_sn())) {
                tradeConfigClient.clearProviderTerminal(terminal.getMerchant_sn(), ProviderEnum.PROVIDER_HXB.getValue());
            }
            contractApplicationService.reCreateProviderTerminal(merchantProviderParams.getMerchant_sn(), null, null, ProviderEnum.PROVIDER_HXB.getValue());
            return response.setSuccess(true).setMsg("已尝试重新报备终端");
        }
    }


    @Override
    public HxStoreCheckResp checkCreateStoreProvinceDiffMerchant(CheckStoreDiffProvinceReq req) {
        HxStoreCheckResp resp = new HxStoreCheckResp();
        resp.setCheckResult(false);

        Map merchant = merchantService.getMerchant(req.getMerchantId());
        if (Objects.isNull(merchant)) {
            throw new CommonPubBizException("商户不存在,merchantId: " + req.getMerchantId());
        }
        ContractStatus contractStatus = contractStatusService.selectByMerchantSn(MapUtil.getString(merchant, "sn"));
        if (Objects.nonNull(contractStatus) && ProviderUtil.HXB_CHANNEL.equals(contractStatus.getAcquirer()) && ContractStatus.STATUS_SUCCESS == contractStatus.getStatus() &&
                !Utils.compareProvince(MapUtil.getString(merchant, "district_code"), req.getDistrictCode())) {
            resp.setCheckResult(true)
                    .setCheckMsg("华夏银行不支持跨省异地门店收款，不能创建跨省门店");
        }
        return resp;
    }


    @Override
    public HxStoreCheckResp haveDiffProvinceStoreByMerchantId(String merchantId) {
        return storeBiz.haveDiffProvinceStore(merchantId);
    }


    @Override
    public Set<String> getCmbcProfitSharingMode() {
        return applicationApolloConfig.getCmbcProfitSharingMode();
    }

    @Override
    public Set<String> getCmbcContractBranch() {
        return applicationApolloConfig.getCmbcContractBranch();
    }
}
