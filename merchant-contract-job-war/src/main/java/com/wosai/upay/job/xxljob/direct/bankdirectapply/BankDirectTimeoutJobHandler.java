package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.psbc.SelfAuditRejectRequest;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * xxl_job_desc: 银行直连-签约超时
 * 进件待签约过期后（7天）自动驳回
 */
@Slf4j
@Component("BankDirectTimeoutJobHandler")
public class BankDirectTimeoutJobHandler extends AbstractBankDirectJobHandler {

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public String getLockKey() {
        return "BankDirectTimeoutJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            final List<BankDirectApply> applyList = bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(Lists.newArrayList(BankDirectApplyConstant.ProcessStatus.CONTRACT_APPLYING), StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
            if (CollectionUtils.isEmpty(applyList)) {
                return;
            }
            applyList.forEach(apply -> {
                try {
                    if (ShutdownSignal.isShuttingDown()) {
                        return;
                    }
                    //判断状态
                    checkStatusAndDoTimeOut(apply);
                } catch (Exception exception) {
                    log.error("银行直连商户:{},签约超时自助驳回", apply.getMerchant_sn(), exception);
                }
            });
        } catch (Exception e) {
            log.error("timeOutReject exception", e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("银行直连商户签约超时自助驳回异常:%s", e.getMessage()));
        }
    }

    /**
     * 判断当前申请是不是处于待签约,并自动驳回
     *
     * @param apply
     */
    private void checkStatusAndDoTimeOut(BankDirectApply apply) {
        //获取主任务
        final Long taskId = apply.getTask_id();
        final ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
        //收单机构进件任务
        final List<ContractSubTask> acquireSubTask = contractSubTaskMapper.getAcquireSubTask(taskId);
        final ContractSubTask subTask = acquireSubTask.get(0);
        final Date updateAt = subTask.getUpdate_at();
        final String channelName = subTask.getChannel();
        //华夏,平安不支持自助驳回
        final Integer bankRef = apply.getBank_ref();
        if (Objects.equals(bankRef, BankDirectApplyRefEnum.HXB.getValue()) || Objects.equals(bankRef, BankDirectApplyRefEnum.PAB.getValue())) {
            return;
        }
        //进件不到7天不需要判断
        if (Objects.equals(contractTask.getStatus(), TaskStatus.PROGRESSING.getVal())
                && DateUtils.addDays(updateAt, 7).after(new Date())) {
            return;
        }
        BasicProvider provider = providerFactory.getProviderByName(channelName);
        //是否处于待签约状态
        final Boolean toBeSigned = provider.checkBankContractToBeSigned(subTask);
        if (!toBeSigned) {
            return;
        }
        //进件待签约过期后（7天）自动驳回
        final Map merchant = merchantService.getMerchantByMerchantSn(apply.getMerchant_sn());
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        SelfAuditRejectRequest rejectRequest = new SelfAuditRejectRequest();
        rejectRequest.setDescription("进件待签约过期后（7天）自动驳回");
        rejectRequest.setDevCode(apply.getDev_code());
        rejectRequest.setMerchantId(merchantId);
        bankDirectService.selfAuditReject(rejectRequest);
    }
}