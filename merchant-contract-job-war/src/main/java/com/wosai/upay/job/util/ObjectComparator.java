package com.wosai.upay.job.util;

import com.google.common.collect.Sets;
import com.wosai.upay.merchant.contract.model.pingan.request.SubmitContractInfoRequest;
import org.apache.commons.lang3.builder.EqualsBuilder;

import java.util.Set;

public class ObjectComparator {

    /**
     * 比较两个对象是否相等，支持指定字段排除和嵌套对象比较
     *
     * @param obj1         第一个对象
     * @param obj2         第二个对象
     * @param excludeFields 要排除的字段名称数组
     * @return 若对象在排除字段之外的字段相等，则返回 true，否则返回 false
     */
    public static boolean compare(Object obj1, Object obj2, Set<String> excludeFields) {
        return isEqual(obj1, obj2, excludeFields);
    }

    /**
     * 递归比较两个对象，支持排除指定字段
     *
     * @param obj1         第一个对象
     * @param obj2         第二个对象
     * @param excludeProps 排除字段集合
     * @return 比较结果
     */
    private static boolean isEqual(Object obj1, Object obj2, Set<String> excludeProps) {
        if (obj1 == obj2) {
            return true;
        }
        if (obj1 == null || obj2 == null) {
            return false;
        }
        return EqualsBuilder.reflectionEquals(obj1, obj2, excludeProps);
    }

    public static void main(String[] args) {
        // 创建测试对象
        SubmitContractInfoRequest contract1 = new SubmitContractInfoRequest();
        contract1.setApplyNo("001");
        contract1.setRequestTime("2023-10-10 12:00:00");
        contract1.setContactPhone("1234");
        SubmitContractInfoRequest contract2 = new SubmitContractInfoRequest();
        contract2.setApplyNo("001");
        contract2.setRequestTime("2023-10-10 12:00:00");
        contract2.setContactPhone("1234");
        // 使用 compare 方法并排除字段
        boolean isEqual = compare(contract1, contract2, Sets.newHashSet("applyNo", "requestTime"));
        System.out.println("比较结果: " + isEqual);
    }
}
