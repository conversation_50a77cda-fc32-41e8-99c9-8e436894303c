package com.wosai.upay.job.model.callback.req;

import java.io.Serializable;

import lombok.Data;

@Data
public class BcsCallbackReq implements Serializable {

    private String address;

    private String bizType;

    private String brief;

    private String busiLiceNo;

    private String contactName;

    private String contactPhone;

    private String legalCerNo;

    private String legalCerType;

    private String legalName;

    private String legalPhone;

    /**
     * 返回的长沙银行主商户编号
     */
    private String mchtNo;

    private String name;

    private String reserved1;

    private String reserved2;

    private String reserved3;

    private String reserved4;

    private String reserved5;

    /**
     * 返回的状态码
     */
    private String returnCode;

    /**
     * 返回的状态描述
     */
    private String returnMsg;

    private String settleCycle;

    private int settleInterval;

    private String status;

    private String tradeNo;

}
