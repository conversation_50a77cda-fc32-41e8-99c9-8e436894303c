package com.wosai.upay.job.config;

import com.aliyun.openservices.log.Client;
import com.wosai.middleware.aliyun.sls.DynamicCredentialsProvider;
import com.wosai.middleware.vault.Vault;
import com.wosai.middleware.vault.VaultConfig;
import com.wosai.middleware.vault.VaultException;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.wosai.upay.job.config.CustomerApplicationProvider.MERCHANT_CONTRACT_JOB;

@Configuration
public class SlsConfig {

    private static final String SLS_ENDPOINT_HD = "http://cn-hangzhou.log.aliyuncs.com";
    private static final String SLS_ENDPOINT_HN = "http://cn-shenzhen.log.aliyuncs.com";


    @Bean (name = "hdSLSClient")
    public Client getHDSLSClient() {
        try {
            Vault vault = Vault.load("cua", MERCHANT_CONTRACT_JOB);
            return new Client(SLS_ENDPOINT_HD, new DynamicCredentialsProvider(vault));
        } catch (VaultException vaultException) {
            throw new ContractBizException("获取slsClient失败", vaultException);

        }
    }


    @Bean (name = "hnSLSClient")
    public Client getHNSLSClient() {
        try {
            Vault vault = Vault.load("cua",MERCHANT_CONTRACT_JOB);
            return new Client(SLS_ENDPOINT_HN, new DynamicCredentialsProvider(vault));
        } catch (VaultException vaultException) {
            throw new ContractBizException("获取slsClient失败", vaultException);

        }
    }
}

