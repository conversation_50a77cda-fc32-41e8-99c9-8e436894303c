package com.wosai.upay.job.refactor.biz.acquirer.icbc;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import org.springframework.stereotype.Service;

/**
 * 工商银行收单处理门面
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
public class IcbcAcquirerFacade extends AbstractAcquirerHandler {

    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.ICBC;
    }

}
