package com.wosai.upay.job.biz.messageStrategy;

import avro.shaded.com.google.common.collect.Maps;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TerminalConfig;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.biz.BlueSeaBiz;
import com.wosai.upay.job.mapper.BlueSeaTaskMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.model.MchSnapshot;
import com.wosai.upay.job.model.TerminalInfo;
import com.wosai.upay.job.model.newBlueSea.BlueSeaActivityChanged;
import com.wosai.upay.job.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Predicate;
import java.util.stream.Collectors;


/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/12/2 18:18
 */
@Component
@Slf4j
public class BlueSeaActivityChangedBiz implements AliMessageHandleService {

    @Autowired
    private BlueSeaTaskMapper blueSeaTaskMapper;

    @Autowired
    private BlueSeaBiz blueSeaBiz;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private TerminalService terminalService;

    public static final String FEE_RATE = "0.2";

    @Override
    public String getMsgMethod() {
        return "alipay.open.sp.blueseaactivity.changed";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleMessageBiz(String bizContent) {
        log.info("新蓝海活动申请单结果通知业务处理 : {}", bizContent);
        final BlueSeaActivityChanged activityChanged = JSONObject.parseObject(bizContent, BlueSeaActivityChanged.class);
        log.info("bizContent 转换成 BlueSeaActivityChanged : {}", JSONObject.toJSONString(activityChanged));
        //活动申请单Id
        final String activityOrderId = activityChanged.getOrderId();
        //是否存在这个记录
        BlueSeaTask blueSeaTask = blueSeaTaskMapper.findTaskByActivityOrderId(activityOrderId);
        log.info("查询到状态为10的结果集且activity_order_id:{},blueSeaTask:{}", activityOrderId, JSONObject.toJSONString(blueSeaTask));
        if (Objects.isNull(blueSeaTask)) {
            //没有查询到说明数据不存在或状态已经被改(cas)
            return;
        }
        //新蓝海活动申请是否通过
        //活动申请状态
        final String status = activityChanged.getStatus();
        if (BlueSeaConstant.AlipayActivityStatus.STATUS_AUDITING.equalsIgnoreCase(status)) {//活动审核中就结束
            return;
        }
        handleActivityStatusAndTerminalConfig(activityChanged.getMemo(), activityOrderId, blueSeaTask, status);

    }

    /**
     * 修改蓝海任务状态和终端费率
     *
     * @param memo            审核驳回原因，在订单状态为失败时有效
     * @param activityOrderId 活动申请单Id
     * @param blueSeaTask     蓝海任务
     * @param status          支付宝返回的活动状态
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 15:29 2020/12/14
     */
    public void handleActivityStatusAndTerminalConfig(String memo, String activityOrderId, BlueSeaTask blueSeaTask, String status) {
        final boolean pass = BlueSeaConstant.AlipayActivityStatus.STATUS_PASS.equalsIgnoreCase(status);
        final BlueSeaTask newTask = blueSeaBiz.buildProcessAndMchSnapshot(blueSeaTask.getId(), pass ? BlueSeaConstant.ACTIVITY_CREATE_SUCCESS : BlueSeaConstant.FAIL, MchSnapshot.builder().activityStatus(status).build());
        //TODO 新蓝海申请活动Id
        newTask.setActivity_order_id(activityOrderId);
        //如果失败就会有失败描述
        newTask.setDescription(pass ? null : memo);
        newTask.setStatus(pass ? BlueSeaConstant.ACTIVITY_CREATE_SUCCESS : BlueSeaConstant.FAIL);
        log.info("根据activityOrderId:{},更新bluesea_task:{}", activityOrderId, JSONObject.toJSONString(newTask));
        //根据活动报名Id更新状态
        blueSeaTaskMapper.updateTaskByActivityOrderId(newTask);
        //如果活动审核失败发送消息到审批中心
        if (!pass) {
            blueSeaBiz.callBack(newTask.getStatus(), newTask.getDescription(), blueSeaTask.getAudit_id(), blueSeaTask.getApply_id(), MapUtils.getLong(JSONObject.parseObject(blueSeaTask.getForm_body(), Map.class), BlueSeaConstant.TEMPLATE_ID), blueSeaTask.getMerchant_id(), blueSeaTask.getType(), 2);
            return;
        }
        //快消活动不修改费率,整个流程完毕
        if (Objects.equals(blueSeaTask.getType(), BlueSeaConstant.kx)) {
            //发送消息到审批中心
            blueSeaBiz.callBack(BlueSeaConstant.SUCCESS, newTask.getDescription(), blueSeaTask.getAudit_id(), blueSeaTask.getApply_id(), MapUtils.getLong(JSONObject.parseObject(blueSeaTask.getForm_body(), Map.class), BlueSeaConstant.TEMPLATE_ID), blueSeaTask.getMerchant_id(), blueSeaTask.getType(), 2);
            return;
        }
        //修改终端费率
        //TODO 记录终端terminalId
        final List<TerminalInfo> terminalInfo = blueSeaTask.getTerminalInfo();
        //没有蜻蜓设备
        if (CollectionUtils.isEmpty(terminalInfo)) {
            blueSeaBiz.callBack(BlueSeaConstant.FAIL, "没有蜻蜓设备", blueSeaTask.getAudit_id(), blueSeaTask.getApply_id(), MapUtils.getLong(JSONObject.parseObject(blueSeaTask.getForm_body(), Map.class), BlueSeaConstant.TEMPLATE_ID), blueSeaTask.getMerchant_id(), blueSeaTask.getType(), 2);
            return;
        }
        //queryStatus==200 代表绑定成功
        //设备指纹集合
        final Set<String> deviceFingerprints = terminalInfo.stream().filter(x -> Objects.equals(x.getQueryStatus(), 200) && Objects.equals(x.getBindStatus(), 200)).map(TerminalInfo::getDeviceFingerprint).collect(Collectors.toSet());
        //根据指纹找到对应的设备主键Ids
        final List<String> ids = Optional.ofNullable(deviceFingerprints).orElseGet(HashSet::new).stream()
                .map(fingerprint -> terminalService.getTerminalByDeviceFingerprint(fingerprint))
                .filter(terminal -> MapUtils.isNotEmpty(terminal)).map(terminal -> MapUtils.getString(terminal, "id")).collect(Collectors.toList());
        //是否全部修改成功
        final boolean result = ids.stream().map(terminalId -> CompletableFuture.supplyAsync(() -> updateTerminalConfig(blueSeaTask.getMerchant_sn(), activityOrderId, terminalId)))
                .map(booleanCompletableFuture -> booleanCompletableFuture.join()).allMatch(Predicate.isEqual(Boolean.TRUE));
        final BlueSeaTask task = blueSeaBiz.buildProcessAndMchSnapshot(blueSeaTask.getId(), result ? BlueSeaConstant.SUCCESS : BlueSeaConstant.FAIL, null);
        task.setStatus(result ? BlueSeaConstant.SUCCESS : BlueSeaConstant.FAIL);
        //记录日志
        task.setDescription(result ? null : "修改费率异常");
        task.setActivity_order_id(activityOrderId);
        log.info("修改费率task:{}", JSONObject.toJSONString(task));
        List<Map> pcTerminals = blueSeaBiz.findAllDragonFlyOrPc(blueSeaTask.getMerchant_id(), BlueSeaConstant.BLUESEA_PCTERMINAL_VENDER_APP_APPID);
        if (!StringUtil.listEmpty(pcTerminals)) {
            List<String> collect = pcTerminals.stream().map(pcTerminal -> {
                updatePcTerminalConfig(blueSeaTask.getMerchant_sn(), MapUtils.getString(pcTerminal, CommonModel.ID));
                return MapUtils.getString(pcTerminal, BlueSeaConstant.FINGER_PRINT);
            }).collect(Collectors.toList());
            blueSeaTask.getMchSnapshot().setPcTerminals(collect);
        }
        blueSeaTaskMapper.updateTaskByCondition(task);
        //发送消息到审批中心
        blueSeaBiz.callBack(task.getStatus(), task.getDescription(), blueSeaTask.getAudit_id(), blueSeaTask.getApply_id(), MapUtils.getLong(JSONObject.parseObject(blueSeaTask.getForm_body(), Map.class), BlueSeaConstant.TEMPLATE_ID), blueSeaTask.getMerchant_id(), blueSeaTask.getType(), 2);
    }

    /**
     * 修改终端费率
     *
     * @param merchantSn      商户号
     * @param activityOrderId 活动申请单Id
     * @param terminalId      终端Id
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 20:04 2020/12/2
     */
    public Boolean updateTerminalConfig(String merchantSn, String activityOrderId, String terminalId) {
        try {
            //获取终端参数
            Map config = tradeConfigService.getTerminalConfigByTerminalIdAndPayway(terminalId, PaywayEnum.ALIPAY.getValue());
            //设置费率和子支付方式(与智谦沟通后确定支付宝只用设置B2C,C2B,WAP)
            Map<String, Object> update = Maps.newHashMap();
            update.put(MerchantConfig.B2C_FEE_RATE, FEE_RATE);
            update.put(MerchantConfig.C2B_FEE_RATE, FEE_RATE);
            update.put(MerchantConfig.WAP_FEE_RATE, FEE_RATE);
            update.put(MerchantConfig.B2C_STATUS, Boolean.TRUE);
            update.put(MerchantConfig.C2B_STATUS, Boolean.TRUE);
            update.put(MerchantConfig.WAP_STATUS, Boolean.TRUE);
            if (Objects.isNull(config)) {
                update.put(TerminalConfig.TERMINAL_ID, terminalId);
                update.put(TerminalConfig.PAYWAY, PaywayEnum.ALIPAY.getValue());
                log.info("终端Id:{},创建终端费率参数:{}", terminalId, JSONObject.toJSONString(update));
                tradeConfigService.createTerminalConfig(update);
            } else {
                update.put(ConstantUtil.KEY_ID, MapUtils.getString(config, ConstantUtil.KEY_ID));
                log.info("终端Id:{},更新终端费率参数:{}", terminalId, JSONObject.toJSONString(update));
                tradeConfigService.updateTerminalConfig(update);
            }
            //清除商户交易缓存
            supportService.removeCachedParams(merchantSn);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("新蓝海报名商户号:{},活动申请Id:{},终端terminalId:{},Exception:{}", merchantSn, activityOrderId, terminalId, e);
        }
        return Boolean.FALSE;
    }

    /**
     * 修改pc插件的费率
     *
     * @param merchantSn
     * @param terminalId
     */
    public void updatePcTerminalConfig(String merchantSn, String terminalId) {
        Map config = tradeConfigService.getTerminalConfigByTerminalIdAndPayway(terminalId, PaywayEnum.ALIPAY.getValue());
        Map<String, Object> update = CollectionUtil.hashMap(MerchantConfig.B2C_FEE_RATE, FEE_RATE, MerchantConfig.B2C_STATUS, Boolean.TRUE);
        if (Objects.isNull(config)) {
            update.put(TerminalConfig.TERMINAL_ID, terminalId);
            update.put(TerminalConfig.PAYWAY, PaywayEnum.ALIPAY.getValue());
            tradeConfigService.createTerminalConfig(update);
        } else {
            update.put(CommonModel.ID, MapUtils.getString(config, CommonModel.ID));
            tradeConfigService.updateTerminalConfig(update);
        }
        supportService.removeCachedParams(merchantSn);
    }

}
