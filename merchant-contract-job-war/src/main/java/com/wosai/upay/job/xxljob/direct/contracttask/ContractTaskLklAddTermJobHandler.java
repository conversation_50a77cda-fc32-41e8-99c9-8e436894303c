package com.wosai.upay.job.xxljob.direct.contracttask;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * xxl_job_desc: 进件任务-拉卡拉增网增终
 * 处理lklV3 增加门店 + 增加终端 + 解绑终端 的功能
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("ContractTaskLklAddTermJobHandler")
public class ContractTaskLklAddTermJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private SubTaskHandlerContext subTaskHandlerContext;
    @Autowired
    private ChatBotUtil chatBotUtil;

    private static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    @Autowired
    private ScheduleUtil scheduleUtil;

    @Override
    public String getLockKey() {
        return "ContractTaskLklAddTermJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        List<ContractTask> tasks = contractTaskMapper.selectShopTermTodo(StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
        Map<String, Integer> merchantCount = new HashMap<>();

        tasks.forEach(task -> {
            List<ContractSubTask> subTasks = contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus(task.getMerchant_sn(), task.getId(), 1);
            if (WosaiCollectionUtils.isEmpty(subTasks)) {
                return;
            }
            ContractSubTask contractSubTask = subTasks.get(0);
            Integer count = merchantCount.getOrDefault(task.getMerchant_sn(), 0);
            count++;
            if (!Objects.equals(contractSubTask.getTask_type(), ProviderUtil.SUB_TASK_TASK_TYPE_ADD_TERM) && count > 20) {
                // 同一个商户非增终任务，一批最多执行20个
                contractTaskMapper.updatePriorityAndResult(task.getId(),
                        LocalDateTime.now().plusMinutes(scheduleUtil.getQueryTime().getLklV3Delay()).format(formatter),
                        task.getResult()
                );
                return;
            }
            merchantCount.put(task.getMerchant_sn(), count);

            try {
                subTaskHandlerContext.handle(task, contractSubTask);
            } catch (Exception e) {
                log.error("lklV3 add shop/term error, merchantSn:{}", task.getMerchant_sn(), e);
                chatBotUtil.sendMessageToContractWarnChatBot(String.format("lklV3 add shop/term error, merchantSn:%s%s", task.getMerchant_sn(), ExceptionUtil.getThrowableMsg(e)));
            }
        });
    }
}
