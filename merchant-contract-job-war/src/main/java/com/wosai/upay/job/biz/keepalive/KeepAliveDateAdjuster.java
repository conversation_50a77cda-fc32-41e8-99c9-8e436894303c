package com.wosai.upay.job.biz.keepalive;

import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;

import java.time.LocalDate;
import java.util.*;

/**
 * 保活日期调整器
 * 负责调整保活任务的执行日期，确保不同 provider 的任务不在同一天执行
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
public class KeepAliveDateAdjuster {

    /**
     * 调整重复的日期，确保不同的 provider 对应的日期不一致
     *
     * 业务规则：
     * 1. 相同 provider 的任务可以在同一天执行
     * 2. 不同 provider 的任务不能在同一天执行
     * 3. 如果日期冲突，按照输入顺序，先出现的 provider 优先保持原日期
     * 4. 保持 LinkedHashMap 的顺序
     *
     * @param dateMap 参数与日期的映射（LinkedHashMap，保持顺序）
     * @return 调整后的映射
     */
    public static Map<MerchantProviderParamsDO, LocalDate> adjustDuplicateDates(
            Map<MerchantProviderParamsDO, LocalDate> dateMap) {
        
        if (dateMap.isEmpty()) {
            return new LinkedHashMap<>();
        }

        Map<MerchantProviderParamsDO, LocalDate> result = new LinkedHashMap<>();
        Map<LocalDate, Set<Integer>> dateProviderMap = new HashMap<>();

        for (Map.Entry<MerchantProviderParamsDO, LocalDate> entry : dateMap.entrySet()) {
            MerchantProviderParamsDO param = entry.getKey();
            LocalDate originalDate = entry.getValue();
            Integer provider = param.getProvider();

            LocalDate finalDate = findAvailableDate(originalDate, provider, dateProviderMap);
            
            dateProviderMap.computeIfAbsent(finalDate, k -> new HashSet<>()).add(provider);
            result.put(param, finalDate);
        }

        return result;
    }

    private static LocalDate findAvailableDate(LocalDate originalDate, 
                                             Integer provider, 
                                             Map<LocalDate, Set<Integer>> dateProviderMap) {
        LocalDate candidateDate = originalDate;
        
        while (isDateConflict(candidateDate, provider, dateProviderMap)) {
            candidateDate = candidateDate.plusDays(1);
        }
        
        return candidateDate;
    }

    private static boolean isDateConflict(LocalDate date, 
                                        Integer provider, 
                                        Map<LocalDate, Set<Integer>> dateProviderMap) {
        Set<Integer> providersOnDate = dateProviderMap.get(date);
        if (providersOnDate == null || providersOnDate.isEmpty()) {
            return false;
        }
        
        return !providersOnDate.contains(provider);
    }
}
