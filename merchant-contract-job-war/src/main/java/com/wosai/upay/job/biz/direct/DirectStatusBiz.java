package com.wosai.upay.job.biz.direct;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.wosai.upay.job.mapper.DirectStatusMapper;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.merchant.contract.avro.DirectApplyStatusChange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/12/19
 */
@Component
public class DirectStatusBiz {

    @Autowired
    private DirectStatusMapper directStatusMapper;
    @Autowired
    @Qualifier("directStatusThreadPoolTaskScheduler")
    private ThreadPoolTaskScheduler directStatusThreadPoolTaskScheduler;

    @Resource(name = "kafkaTemplate")
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Value("${direct.apply.topic}")
    private String topic;

    /**
     * 总状态的处理(没有就新增，有就更新)
     * TODO 发送消息
     * @param merchantSn 商户号
     * @param devCode 应用标识
     */
    public void createOrUpdateDirectStatus(String merchantSn, String devCode, int status, String message) {
        int preStatus = getPreStatus(merchantSn, devCode, status);
        DirectApplyStatusChange statusChange = new DirectApplyStatusChange();
        statusChange.setMerchantSn(merchantSn);
        statusChange.setDevCode(devCode);
        statusChange.setPreStatus(preStatus);
        statusChange.setStatus(status);
        statusChange.setMessage(message);
        scheduleSendMessage(statusChange);
    }

    /**
    * 获取并更新指定设备的前置状态
    * 该方法用于根据商户序列号和设备代码获取当前状态，并更新为新的状态
    * 如果当前状态不存在，则插入新的状态记录
    *
    * @param merchantSn 商户序列号，用于标识特定的商户
    * @param devCode 设备代码，用于标识特定的设备
    * @param status 新的状态值
    * @return 返回更新前的状态值
    */
    public int getPreStatus(String merchantSn, String devCode, int status) {
    // 根据商户序列号和设备代码查询DirectStatus对象
    DirectStatus directStatus = directStatusMapper.selectDirectStatusByMerchantSn(merchantSn, devCode);

    int preStatus;
    // 如果查询结果为空，表示没有对应的DirectStatus记录
    if (directStatus == null) {
        // 设置前置状态为待处理状态
        preStatus = DirectStatus.STATUS_PENDING;
        // 插入新的DirectStatus记录
        directStatusMapper.insert(new DirectStatus().setMerchant_sn(merchantSn).setDev_code(devCode).setStatus(status));
    } else {
        // 如果查询结果不为空，获取当前状态作为前置状态
        preStatus = directStatus.getStatus();
        // 更新当前状态为新的状态值
        directStatusMapper.updateByPrimaryKeySelective(new DirectStatus().setId(directStatus.getId()).setStatus(status));
    }
    // 返回前置状态
    return preStatus;
}


    /**
     * 账户验证中、签约中、开通权限中等发送的消息
     * @param merchantSn 商户号
     * @param devCode 应用标识
     * @param status 状态
     * @param message 消息
     */
    public void sendStatusChangeMessage(String merchantSn, String devCode, int status, String message) {
        DirectApplyStatusChange statusChange = new DirectApplyStatusChange();
        statusChange.setMerchantSn(merchantSn);
        statusChange.setDevCode(devCode);
        statusChange.setPreStatus(DirectStatus.STATUS_PROCESS);
        statusChange.setStatus(status);
        statusChange.setMessage(message);
        scheduleSendMessage(statusChange);
    }

    /**
     * 获取当前商户号该应用的总状态
     * @param merchantSn 商户号
     * @param devCode 应用标识
     * @return
     */
    public DirectStatus getDirectStatusByMerchantSnAndDevCode(String merchantSn, String devCode) {
        return directStatusMapper.selectDirectStatusByMerchantSn(merchantSn, devCode);
    }

    private void scheduleSendMessage(DirectApplyStatusChange statusChange) {
        directStatusThreadPoolTaskScheduler.getScheduledExecutor().schedule(new Runnable() {
            @Override
            public void run() {
                kafkaTemplate.send(topic, statusChange);
            }
        }, 2, TimeUnit.SECONDS);
    }
}
