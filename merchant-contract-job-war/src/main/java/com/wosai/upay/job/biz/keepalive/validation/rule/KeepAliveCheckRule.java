package com.wosai.upay.job.biz.keepalive.validation.rule;

import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;

/**
 * 商户规则接口
 */
public interface KeepAliveCheckRule {
    
    /**
     * 规则执行
     *
     * @param context    商户上下文信息
     * @param ruleConfig
     * @return 规则执行结果
     */
    KeepAliveCheckRuleResult execute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig);
    
    /**
     * 获取规则类型
     * @return 规则类型
     */
    KeepAliveCheckRuleTypeEnum getRuleType();
}