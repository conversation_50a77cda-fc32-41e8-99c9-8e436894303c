package com.wosai.upay.job.xxljob.direct;

import com.wosai.upay.job.mapper.PayForTaskMapper;
import com.wosai.upay.job.model.PayForTask;
import com.wosai.upay.job.providers.PayForProvider;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * xxl_job_desc: 处理代付任务
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("PayForTaskJobHandler")
public class PayForTaskJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private PayForTaskMapper payForTaskMapper;
    @Autowired
    private PayForProvider payForProvider;

    @Override
    public String getLockKey() {
        return "PayForTaskJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            //当前时间 5s内 的payForTask不会被扫到,避免 30天内已有代付成功订单时,task更新状态顺序错误
            List<PayForTask> payForTasks = payForTaskMapper.selectByCreate(StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()), StringUtil.formatDate(System.currentTimeMillis() - 5000L), param.getBatchSize());
            if (CollectionUtils.isEmpty(payForTasks)) {
                return;
            }
            for (PayForTask payForTask : payForTasks) {
                payForProvider.processTask(payForTask);
            }
        } catch (Exception e) {
            log.error("processPayForTask error ", e);
        }
    }
}
