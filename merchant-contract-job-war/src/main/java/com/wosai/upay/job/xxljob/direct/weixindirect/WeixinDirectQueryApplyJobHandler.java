package com.wosai.upay.job.xxljob.direct.weixindirect;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.biz.direct.WeixinDirectBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.enume.WeixinDirectApplyStatus;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.WeixinDirectApplyMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.WeixinDirectApply;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.monitor.MonitorObject;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.weixin.DirectApplymentStatusResp;
import com.wosai.upay.merchant.contract.service.WeiXinDirectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.HttpStatus;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * xxl_job_desc: 微信直连-查询申请单
 * <AUTHOR>
 * @date 2025/4/11
 */
@Slf4j
@Component("WeixinDirectQueryApplyJobHandler")
public class WeixinDirectQueryApplyJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private WeiXinDirectService weiXinDirectService;

    @Autowired
    private WeixinDirectApplyMapper applyMapper;

    @Autowired
    private DirectStatusBiz directStatusBiz;

    @Autowired
    private ContractTaskBiz taskBiz;

    @Autowired
    private WeixinDirectBiz directBiz;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private MonitorLog monitorLog;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    public String getLockKey() {
        return "WeixinDirectQueryApplyJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        long current = System.currentTimeMillis();
        List<WeixinDirectApply> applyList = applyMapper.getAppliesByPriorityAndStatus(
                DateFormatUtils.format(current - param.getQueryTime(), "yyyy-MM-dd HH:mm:ss"),
                DateFormatUtils.format(current, "yyyy-MM-dd HH:mm:ss"),
                Lists.newArrayList(WeixinDirectApplyStatus.IN_WEIXIN_AUDITING.getVal(), WeixinDirectApplyStatus.WAIT_FOR_VERIFY.getVal()
                        , WeixinDirectApplyStatus.WAIT_FOR_SIGN.getVal(), WeixinDirectApplyStatus.IN_OPENING_PERMISSION.getVal()),
                param.getBatchSize());
        applyList.forEach(apply -> {
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                doQueryApply(apply);
            } catch (Exception e) {
                log.error("query apply error {} ", apply.getMerchant_sn(), e);
                chatBotUtil.sendMessageToContractWarnChatBot(apply.getMerchant_sn() + "查询微信直连申请单状态" + apply.getId() + "失败" + ExceptionUtil.getThrowableMsg(e));
            }
        });
    }

    private void doQueryApply(WeixinDirectApply apply) {
        String merchantSn = apply.getMerchant_sn();
        long start = System.currentTimeMillis();
        int code = 200;
        String message = "";
        try {
            //2. 提交到微信
            DirectApplymentStatusResp statusResp = weiXinDirectService.queryApplyStatus(directBiz.getBusinessCode(apply));
            code = Integer.parseInt(statusResp.getCode());
            message = statusResp.getMessage();
            if (code == HttpStatus.SC_OK) {
                String weixinStatus = statusResp.getApplyment_state();
                WeixinDirectApplyStatus weixinDirectApplyStatus;
                if (WosaiStringUtils.isEmpty(weixinStatus) || (weixinDirectApplyStatus = WeixinDirectBiz.WEIXIN_STATUS_MAPPING.get(weixinStatus)) == null) {
                    log.error("同步微信直连申请单返回状态不存在:{} {}", merchantSn, weixinStatus);
                    chatBotUtil.sendMessageToContractWarnChatBot(apply.getMerchant_sn() + "同步微信直连申请单返回状态不存在" + weixinStatus);
                    return;
                }
                //状态未发生变化
                if (weixinDirectApplyStatus.getVal().equals(apply.getStatus())) {
                    return;
                }
                directBiz.doAfterStatusChanged(statusResp, weixinDirectApplyStatus, apply);
            } else {
                if (code == HttpStatus.SC_INTERNAL_SERVER_ERROR || code == 429) {
                    log.error("同步微信直连申请单返回500:{} {}", merchantSn, apply.getId());
                    chatBotUtil.sendMessageToContractWarnChatBot(apply.getMerchant_sn() + "同步微信直连申请单" + apply.getId() + "返回500" + statusResp.getMessage());
                } else {
                    //查询还有业务异常
                    String finalMessage = message;
                    transactionTemplate.executeWithoutResult(tx -> {
                        applyMapper.updateByPrimaryKeySelective(new WeixinDirectApply().setId(apply.getId()).setResponse_body(JSON.toJSONString(statusResp)).setResult(finalMessage).setStatus(WeixinDirectApplyStatus.APPLY_REJECTED.getVal()));
                        taskBiz.update(new ContractTask().setId(apply.getTask_id()).setStatus(TaskStatus.FAIL.getVal()).setResult(JSON.toJSONString(CollectionUtil.hashMap("channel", ProviderUtil.WEIXIN_DIRECT, "message", finalMessage))));
                        directStatusBiz.createOrUpdateDirectStatus(merchantSn, apply.getDev_code(), DirectStatus.STATUS_BIZ_FAIL, finalMessage);
                    });
                    log.error("{} 同步微信直连申请单{}返回业务异常:{}", merchantSn, apply.getId(), message);
                    chatBotUtil.sendMessageToContractWarnChatBot(apply.getMerchant_sn() + "同步微信直连申请单" + apply.getId() + "返回业务异常" + message);
                }
            }
        } finally {
            monitorLog.recordObject(new MonitorObject()
                    .setSn(merchantSn)
                    .setEvent(MonitorObject.WEIXIN_DIRECT_APPLY)
                    .setCost(System.currentTimeMillis() - start)
                    .setStatus(code)
                    .setMessage(message));
        }

    }
}
