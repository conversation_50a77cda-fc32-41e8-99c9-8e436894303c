package com.wosai.upay.job.adapter.apollo.model;

import lombok.Data;

import java.util.Map;

/**
 * 规则配置类
 */
@Data
public class KeepAliveCheckRuleConfig {

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 是否需要校验
     */
    private boolean needValidation;

    /**
     * 优先级
     */
    private int priority;

    /**
     * 规则参数
     */
    private Map<String, Object> params;

    public KeepAliveCheckRuleConfig() {
    }

    public KeepAliveCheckRuleConfig(String ruleType, boolean needValidation, int priority, Map<String, Object> params) {
        this.ruleType = ruleType;
        this.needValidation = needValidation;
        this.priority = priority;
        this.params = params;
    }

    @Override
    public String toString() {
        return "RuleConfig{" +
                "ruleType='" + ruleType + '\'' +
                ", needValidation=" + needValidation +
                ", priority=" + priority +
                ", params=" + params +
                '}';
    }
}