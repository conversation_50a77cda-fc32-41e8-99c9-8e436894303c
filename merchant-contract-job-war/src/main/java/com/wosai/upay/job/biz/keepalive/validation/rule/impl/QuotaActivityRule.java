package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.shouqianba.cua.enums.core.ClearTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.trade.service.activity.response.QuotaApplyConditionQueryResponse;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import com.wosai.upay.job.externalservice.trademanage.TradeManageClient;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 额度包活动规则
 * 检查商户是否参与了额度包活动且在活动中
 */
@Component
public class QuotaActivityRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private TradeManageClient tradeManageClient;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        String merchantSn = context.getMerchantSn();
        try {
            List<MerchantProviderParamsDO> acquirerParams = merchantProviderParamsDAO.getParamsBySnAndPayWay(merchantSn, PaywayEnum.ACQUIRER.getValue());
            boolean hasIndirectParams = acquirerParams.stream().anyMatch(r -> mcAcquirerDAO.getByProvider(String.valueOf(r.getProvider())).getClearType().equals(ClearTypeEnum.INDIRECT.getValue()));
            if (!hasIndirectParams) {
                return createSuccessResult("商户没有间连参数，通过检查");
            }
            List<QuotaApplyConditionQueryResponse> response = tradeManageClient.getEffectQuotaByMerchantSn(merchantSn);
            if (WosaiCollectionUtils.isNotEmpty(response)) {
                return createFailureResult(
                        String.format("商户参与了额度包补贴活动 %s 且在活动中，不允许执行", response.get(0).getActivityName()),
                        "QUOTA_ACTIVITY_BLOCKED");

            }
            return createSuccessResult("商户未参与额度包补贴活动，通过检查");

        } catch (Exception e) {
            logger.error("执行额度包补贴活动检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.QUOTA_ACTIVITY;
    }
}