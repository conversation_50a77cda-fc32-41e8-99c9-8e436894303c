package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerMerchantStatusEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.FeeEffectiveTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.shouqianba.cua.enums.core.ClearTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.shouqianba.model.dto.response.DisableReasonRspDTO;
import com.shouqianba.model.enums.TradeParamsDisableReasonAccessSideEnum;
import com.wosai.bsm.financebackend.service.ProviderChangeService;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.SharingConfig;
import com.wosai.profit.sharing.model.response.SharingConfigInfo;
import com.wosai.profit.sharing.service.SharingConfigService;
import com.wosai.shouqianba.withdrawservice.service.WithdrawService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.bank.BankFeeRateService;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.BatchTemplateApolloConfig;
import com.wosai.upay.job.avro.AcquirerChange;
import com.wosai.upay.job.avro.TradeAppAcquirerChange;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.store.StoreBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.exception.ChangeParamsIgnoreException;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.externalservice.brand.BrandBusinessClient;
import com.wosai.upay.job.externalservice.brand.model.BrandMerchantInfoQueryResp;
import com.wosai.upay.job.externalservice.coreb.TradeConfigClient;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.acquirer.AcquirerChangeSaveDTO;
import com.wosai.upay.job.model.acquirer.SyncMchStatusResp;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.dto.request.ChangeAcquirerReqDTO;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.subBizParams.SubBizParams;
import com.wosai.upay.job.model.subBizParams.SubBizParamsExample;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.biz.provider.McProviderBiz;
import com.wosai.upay.job.refactor.dao.McAcquirerChangeDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McContractRuleDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.event.AcquirerChangeEvent;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerChangeDO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.model.enums.NetInSceneEnum;
import com.wosai.upay.job.refactor.service.McRulesDecisionService;
import com.wosai.upay.job.refactor.service.impl.InterScheduleTaskServiceImpl;
import com.wosai.upay.job.refactor.service.impl.task.BusinessLicenceTaskServiceImpl;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationTask;
import com.wosai.upay.job.service.ContractEventService;
import com.wosai.upay.job.service.ContractTaskService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ThreadLocalUtil;
import com.wosai.upay.job.volcengine.dataCenter.DataCenterProducer;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.wallet.service.WalletService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-04-23
 */
@Slf4j
public abstract class AbstractAcquirerChangeBiz {

    @Resource
    private McProviderBiz mcProviderBiz;

    @Autowired
    protected AcquirerChangeDao changeDao;

    @Autowired
    MerchantService merchantService;

    @Autowired
    private ContractEventService contractEventService;

    @Autowired
    private ChangeTradeParamsBiz tradeParamsBiz;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    protected MerchantProviderParamsMapper paramsMapper;

    @Autowired
    private WalletService walletServcie;

    @Autowired
    private WithdrawService withdrawService;

    @Autowired
    private BankService bankService;

    @Autowired
    @Lazy
    private MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    protected TradeConfigService tradeConfigService;

    @Autowired
    private MerchantBankService merchantBankService;

    @Autowired
    private ProviderChangeService providerChangeService;

    @Autowired
    protected BusinessLogBiz businessLogBiz;

    @Autowired
    private SharingConfigService sharingConfigService;

    @Autowired
    private AcquirerChangeCheckBiz checkBiz;

    @Resource(name = "kafkaTemplate")
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private ChatBotUtil chatBotUtil;

    private static final String ACQUIRER_CHANGE_TOPIC = "events.merchant-contract-job.acquirer-change";
    private static final String TRADE_APP_ACQUIRER_CHANGE_TOPIC = "events_CUA_tradeApp-acquirer-change";
    protected static final String BANK_CHANNEL = "1";
    protected static final String NOT_BANK_CHANNEL = "0";

    @Autowired
    protected McAcquirerDAO mcAcquirerDAO;

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    FeeRateService feeRateService;

    @Autowired
    SupportService supportService;

    @Autowired
    BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    ComposeAcquirerBiz composeAcquirerBiz;

    @Autowired
    private AcquirerFacade acquirerFacade;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Lazy
    @Autowired
    CommonEventHandler commonEventHandler;
    @Autowired
    private BrandBusinessClient brandBusinessClient;

    @Autowired
    private ContractTaskService contractTaskService;

    @Autowired
    private OtherCommonBizCheck otherCommonBizCheck;

    @Autowired
    private TmpBiz tmpBiz;

    @Autowired
    protected AopBiz aopBiz;

    @Autowired
    SubBizParamsBiz subBizParamsBiz;
    @Autowired
    SubBizParamsMapper subBizParamsMapper;

    @Autowired
    RuleContext ruleContext;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    BankFeeRateService bankFeeRateService;
    @Autowired
    private McContractRuleDAO mcContractRuleDAO;

    @Autowired
    private McRulesDecisionService mcRulesDecisionService;

    @Autowired
    WxSettlementIdChangeBiz wxSettlementIdChangeBiz;

    @Autowired
    private DataCenterProducer dataCenterProducer;

    @Resource
    private InterScheduleTaskServiceImpl interScheduleTaskService;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Autowired
    private TradeConfigClient tradeConfigClient;

    private static final int DEFAULT_TRUE = 1;
    //银行通道使用套餐标识
    public static final String BANK_COMBO_SNAPSHOT = "bankComboSnapshot";
    //间连通道使用套餐标识
    public static final String INDIRECT_COMBO_SNAPSHOT = "indirectComboSnapshot";

    /**
     * 切换收单机构的时候不需要关闭lkl\lklV3的子商户号等
     * <a href="https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********">文档</a>
     */
    private static final List<String> NOT_CLOSE_ACQUIRER = Arrays.asList(AcquirerTypeEnum.LKL.getValue(), AcquirerTypeEnum.LKL_V3.getValue());

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    @Resource
    private ComposeAcquirerBiz acquirerBiz;

    @Resource
    private McAcquirerChangeDAO mcAcquirerChangeDAO;

    @Autowired
    private BatchTemplateApolloConfig batchTemplateApolloConfig;

    @Autowired
    private StoreBiz storeBiz;


    @Value("${payment}")
    private String payment;


    @Value("${bank_cooperation}")
    private String bankCooperation;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;


    /**
     * 支付业务和银行合作数据需要写在merchant-config表
     */

    public List<String> modifyMerchantConfigAppId;

    @PostConstruct
    public void init() {
        modifyMerchantConfigAppId = Lists.newArrayList(payment, bankCooperation);
    }

    /**
     * 提交切收单机构申请, 返回任务id
     *
     * @param changeAcquirerReqDTO 切收单机构请求参数
     * @param logParamsDto         日志参数
     * @return 任务id
     */
    public Integer applyChangeAcquirerAndReturnTaskId(ChangeAcquirerReqDTO changeAcquirerReqDTO, LogParamsDto logParamsDto) {
        String merchantSn = changeAcquirerReqDTO.getMerchantSn();
        String targetAcquirer = reGetTargetAcquirer(changeAcquirerReqDTO, merchantSn);
        Optional<String> merchantIdOpt = merchantBasicInfoBiz.getMerchantIdByMerchantSn(merchantSn);
        if (!merchantIdOpt.isPresent()) {
            throw new CommonPubBizException(String.format("商户 %s 不存在", merchantSn));
        }
        String merchantId = merchantIdOpt.get();
        String sourceAcquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        checkApplyChange(changeAcquirerReqDTO, merchantSn, merchantId, sourceAcquirer, targetAcquirer);
        McAcquirerChangeDO mcAcquirerChangeDO = buildMcAcquirerChange(changeAcquirerReqDTO, logParamsDto, merchantId, sourceAcquirer);
        return mcAcquirerChangeDAO.insertOne(mcAcquirerChangeDO);
    }

    private void checkApplyChange(ChangeAcquirerReqDTO changeAcquirerReqDTO, String merchantSn, String merchantId, String sourceAcquirer, String targetAcquirer) {
        acquirerSpecialCheck(merchantSn, merchantId, sourceAcquirer, targetAcquirer, changeAcquirerReqDTO.getImmediately());
        checkBankOrg(targetAcquirer, merchantSn, sourceAcquirer);
        preCheck(merchantSn, merchantId, sourceAcquirer, targetAcquirer, changeAcquirerReqDTO.getTradeAppId(), changeAcquirerReqDTO.getForceChange());
    }

    private McAcquirerChangeDO buildMcAcquirerChange(ChangeAcquirerReqDTO changeAcquirerReqDTO, LogParamsDto logParamsDto, String merchantId, String sourceAcquirer) {
        McAcquirerChangeDO mcAcquirerChangeDO = new McAcquirerChangeDO();
        mcAcquirerChangeDO.setApplyId(UUID.randomUUID().toString());
        mcAcquirerChangeDO.setMerchantSn(changeAcquirerReqDTO.getMerchantSn());
        mcAcquirerChangeDO.setMerchantId(merchantId);
        mcAcquirerChangeDO.setSourceAcquirer(sourceAcquirer);
        mcAcquirerChangeDO.setTargetAcquirer(changeAcquirerReqDTO.getTargetAcquirerTypeEnum().getValue());
        mcAcquirerChangeDO.setMemo("申请成功");
        mcAcquirerChangeDO.setImmediately(changeAcquirerReqDTO.getImmediately() ? 1 : 0);
        HashMap<String, Object> extraMap = Maps.newHashMap();
        extraMap.put(McAcquirerChangeDO.TRADE_APP_ID_KEY, changeAcquirerReqDTO.getTradeAppId());
        extraMap.put(McAcquirerChangeDO.FORCE_CHANGE_KEY, changeAcquirerReqDTO.getForceChange());
        extraMap.put(McAcquirerChangeDO.LOG_PARAMS_KEY, logParamsDto);
        mcAcquirerChangeDO.setExtra(JSON.toJSONString(extraMap));
        return mcAcquirerChangeDO;
    }

    private String reGetTargetAcquirer(ChangeAcquirerReqDTO changeAcquirerReqDTO, String merchantSn) {
        String targetAcquirer = changeAcquirerReqDTO.getTargetAcquirerTypeEnum().getValue();
        if (targetAcquirer.contains(AcquirerTypeEnum.LKL.getValue())) {
            MerchantProviderParamsExample example = new MerchantProviderParamsExample();
            example.or().andMerchant_snEqualTo(merchantSn).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andDeletedEqualTo(false);
            List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);
            final boolean existLkl = records.stream().anyMatch(merchantProviderParam -> Objects.equals(merchantProviderParam.getContract_rule(), "lkl"));
            final boolean existLklV3 = records.stream().anyMatch(merchantProviderParam -> Objects.equals(merchantProviderParam.getContract_rule(), "lklV3"));
            if (existLkl && !existLklV3) {
                targetAcquirer = AcquirerTypeEnum.LKL.getValue();
            }
            if (existLklV3) {
                targetAcquirer = AcquirerTypeEnum.LKL_V3.getValue();
            }
        }
        return targetAcquirer;
    }

    private void checkBankOrg(String targetAcquirer, String merchantSn, String sourceAcquirer) {
        if (isBankOrg(targetAcquirer)) {
            McAcquirerChange tmp = new McAcquirerChange();
            tmp.setMerchant_sn(merchantSn);
            tmp.setTarget_acquirer(targetAcquirer);
            tmp.setSource_acquirer(sourceAcquirer);
            boolean hasContract = hasContract(tmp) && checkOtherPayWay(tmp);
            if (!hasContract) {
                throw new CommonPubBizException(String.format("直连收单机构%s,还未完成报备", targetAcquirer));
            }
        }
    }


    /**
     * 保存申请
     * @param dto
     * @return
     */
    public boolean applyChangeAcquirer(AcquirerChangeSaveDTO dto) {
        String targetAcquirer = dto.getTargetAcquirer();
        final String merchantSn = dto.getMerchantSn();
        final String sourceAcquirer = dto.getSourceAcquirer();
        //兼容lkl升级v3问题
        if (targetAcquirer.contains(AcquirerTypeEnum.LKL.getValue())) {
            //1,当前商户报过的所有收单机构
            MerchantProviderParamsExample example = new MerchantProviderParamsExample();
            example.or().andMerchant_snEqualTo(merchantSn).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andDeletedEqualTo(false);
            List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);
            final boolean existLkl = records.stream().anyMatch(merchantProviderParam -> Objects.equals(merchantProviderParam.getContract_rule(), "lkl"));
            final boolean existLklV3 = records.stream().anyMatch(merchantProviderParam -> Objects.equals(merchantProviderParam.getContract_rule(), "lklV3"));
            if (existLkl && !existLklV3) {
                targetAcquirer = AcquirerTypeEnum.LKL.getValue();
            }
            if (existLklV3) {
                targetAcquirer = AcquirerTypeEnum.LKL_V3.getValue();
            }
        }
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            throw new CommonPubBizException(String.format("商户 %s 不存在", merchantSn));
        }
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);

        acquirerSpecialCheck(merchantSn, merchantId, sourceAcquirer, targetAcquirer, dto.getImmediately());

        //银行直连流程较长所以必须先判断是不是已经报备成功了
        checkBankOrg(targetAcquirer, merchantSn, sourceAcquirer);
        checkSourceAcquirer(merchantSn, sourceAcquirer);
        preCheck(merchantSn, merchantId, sourceAcquirer, targetAcquirer, dto.getTradeAppId(), dto.getForceChange());
        dto.setMerchantId(merchantId);
        changeDao.save(dto);
        return true;
    }

    private void checkSourceAcquirer(String merchantSn, String sourceAcquirer) {
        try {
            Optional<AcquirerSharedAbility> sharedAbility = acquirerFacade.getSharedAbilityByAcquirer(sourceAcquirer);
            if (!sharedAbility.isPresent()) {
                return;
            }
            //如果是通过通用导入的间连收单机构参数则不允许切走
            final Map configMap = batchTemplateApolloConfig.getSupportIndirectConfig();
            if (Objects.nonNull(configMap)) {
                final List<MerchantProviderParamsDO> usingParams = merchantProviderParamsDAO.getUsingBySnAndPayWayList(merchantSn, Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.UNIONPAY.getValue()));
                usingParams.stream().forEach(params -> {
                    if (configMap.containsKey(String.valueOf(params.getProvider()))) {
                        final McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByProvider(String.valueOf(params.getProvider()));
                        throw new CommonPubBizException(String.format("商户%s已有%s机构参数，禁止切换", merchantSn, Optional.ofNullable(mcAcquirerDO).map(McAcquirerDO::getName).orElse("未知收单机构")));
                    }
                });
            }
            CuaCommonResultDTO cuaCommonResultDTO = sharedAbility.get().canChangeToOtherAcquirer(merchantSn);
            if (!cuaCommonResultDTO.isSuccess()) {
                throw new CommonPubBizException(cuaCommonResultDTO.getMessage());
            }
        } catch (CommonPubBizException e) {
            throw e;
        } catch (Exception e) {
            log.warn("checkSourceAcquirer error, merchantSn:{}, sourceAcquirer:{}", merchantSn, sourceAcquirer, e);
        }

    }

    /**
     * 创建申请时前置检查
     *
     * @param merchantSn
     * @param merchantId
     * @param sourceAcquirer
     * @param targetAcquirer
     * @param tradeAppId     业务方Id 此时必须指定
     */
    public void preCheck(String merchantSn, String merchantId, String sourceAcquirer, String targetAcquirer, String tradeAppId, boolean forceChange) {
        if (StringUtils.isBlank(tradeAppId)) {
            throw new CommonPubBizException("切换收单机构必须指定业务方");
        }
        if (Objects.equals(sourceAcquirer, AcquirerTypeEnum.ZJTLCB.getValue())) {
            throw new CommonPubBizException("泰隆银行商户不允许切换收单机构");
        }
        // 品牌主商户不允许切换收单机构
        BrandMerchantInfoQueryResp brandMerchantInfo = brandBusinessClient.getBrandMerchantInfoByMerchantId(merchantId);
        if (brandMerchantInfo.isMainBrandMerchant()) {
            throw new CommonPubBizException("品牌主商户不允许切换收单机构");
        }
        // 品牌子商户支付宝/微信品牌模式，不允许切换收单机构
        if (brandMerchantInfo.isSubBrandMerchant() && brandMerchantInfo.isBrandPayMode()) {
            throw new CommonPubBizException("品牌子商户支付宝/微信品牌模式，不允许切换收单机构");
        }

        final SubBizParamsExample example = new SubBizParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andTrade_app_idEqualTo(tradeAppId)
                .andDeletedEqualTo(Boolean.FALSE);
        Set<String> providers = subBizParamsMapper.selectByExample(example).stream().map(t -> t.getProvider().toString()).collect(Collectors.toSet());
        boolean alreadyInTargetAcquirer = mcProviderBiz.existProvidersBelongToAcquirer(providers, targetAcquirer);
        if (alreadyInTargetAcquirer) {
            throw new CommonPubBizException(String.format("当前商户%s,%s业务已在收单机构%s", merchantSn, subBizParamsBiz.getTradeAppNameById(tradeAppId), targetAcquirer));
        }
        //间连的是时候根据这个在校验一次,防止有lkl或者lklv3这种场景
        if (tradeAppId.equals(subBizParamsBiz.getPayTradeAppId()) && (targetAcquirer.equals(sourceAcquirer) || (sourceAcquirer.contains(AcquirerTypeEnum.LKL.getValue()) && targetAcquirer.contains(AcquirerTypeEnum.LKL.getValue())))) {
            throw new CommonPubBizException("当前商户已在收单机构 " + targetAcquirer);
        }
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        String contractAcquirer = Optional.ofNullable(contractStatus)
                .filter(contract -> contract.getStatus() == ContractStatus.STATUS_SUCCESS)
                .map(contract -> contract.getAcquirer()).orElseGet(String::new);

        // 多业务切到与移动支付业务不一致的通道
        if (!modifyMerchantConfigAppId.contains(tradeAppId)
                && !Objects.equals(contractAcquirer, targetAcquirer)) {
            if (isThirdPartyOrg(contractAcquirer) || isBankOrg(targetAcquirer)) {
                throw new CommonPubBizException(String.format("%s切换到%s与移动支付业务通道%s是不一致", subBizParamsBiz.getTradeAppNameById(tradeAppId), targetAcquirer, contractAcquirer));
            }
        }

        if (changeDao.getLatestUnFinishedApply(merchantSn) != null) {
            throw new CommonPubBizException("当前商户有未完成的收单机构变更");
        }

        ContractGroupRuleVerifyResultBO verifyResultBO = mcRulesDecisionService.checkMerchantEligibilityToAcquirer(merchantSn, targetAcquirer, NetInSceneEnum.CHANNEL_SWITCHING);
        if (!verifyResultBO.isCheckPass()) {
            throw new CommonInvalidParameterException(verifyResultBO.getMessage());
        }

        if (isThirdPartyOrg(targetAcquirer)) {
            String ruleGroupId = getAcquirerSharedAbility(targetAcquirer).getDefaultContractRuleGroupId()
                    .orElseThrow(() -> new CommonPubBizException(targetAcquirer + "没有默认进件规则组"));
            List<ContractTask> contractTasks = contractTaskMapper.selectProcessContractTasks(merchantSn, ruleGroupId);
            if (WosaiCollectionUtils.isNotEmpty(contractTasks)) {
                throw new CommonPubBizException("目标收单机构还有未完成的进件任务");
            }
        }
        //如果目标收单机构不支持特殊行业,则检查当前行业是否是特殊行业
        if (!isSupportSpecialIndustry(targetAcquirer)) {
            // 如果是特殊行业，不允许切换
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            String industry = BeanUtil.getPropString(merchant, Merchant.INDUSTRY, "");
            List<String> specialIndustry = applicationApolloConfig.getSpecialIndustry();
            if (specialIndustry.contains(industry)) {
                throw new CommonInvalidParameterException("当前商户行业不支持切换到银行通道");
            }
        }
        if (!ThreadLocalUtil.isDoingMicroUpgrade() &&
                interScheduleTaskService.isExistedProcessingTask(merchantSn, InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION)) {
            throw new CommonPubBizException("商户存在正在进行中的营业执照认证任务");
        }
        if (!ThreadLocalUtil.isDoingMicroUpgrade()) {
            checkExistedProcessingLicenseUpdateTask(merchantSn);
        }
        if (!forceChange) {
            preBizCheck(merchantSn, merchantId, sourceAcquirer, targetAcquirer);
        }
        checkAcquirerMerchantDisableWhenChange(merchantSn, targetAcquirer);
        subBizParamsBiz.multiCheckWhenChange(merchantSn, targetAcquirer, tradeAppId);
    }

    @Resource
    private BusinessLicenceTaskServiceImpl businessLicenceTaskService;

    private void checkExistedProcessingLicenseUpdateTask(String merchantSn) {
        Boolean exist = businessLicenceTaskService.isExistedCrmLicenseApplyProcessing(merchantSn);
        if (Objects.equals(exist, Boolean.TRUE)) {
            throw new ContractBizException("存在进行中的营业执照审批单任务");
        }
    }

    /**
     * 如果收单机构商户号被禁用且非小微升级，不可切换
     */
    private void checkAcquirerMerchantDisableWhenChange(String merchantSn, String acquirer) {
        Optional<MerchantProviderParamsDO> acquirerParams = merchantTradeParamsBiz.getAcquirerParams(merchantSn, acquirer);
        if (!acquirerParams.isPresent()) {
            return;
        }
        if (!acquirerParams.get().paramsIsDisabled()) {
            return;
        }

        List<DisableReasonRspDTO> disableReasonRspDTOS = merchantTradeParamsBiz.listDisableReasonsByAcquirerMerchantId(merchantSn, acquirerParams.get().getPayMerchantId());
        if (CollectionUtils.isEmpty(disableReasonRspDTOS)) {
            return;
        }
        boolean existedDisableReasonNotMicroUpgrade = disableReasonRspDTOS.stream().anyMatch(disableReasonRspDTO ->
                !(Objects.equals(TradeParamsDisableReasonAccessSideEnum.CUA, disableReasonRspDTO.getAccessSide()) &&
                        StringUtils.equals(disableReasonRspDTO.getDisableReason(),
                                BusinessLicenceCertificationTask.MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON)));
        if (existedDisableReasonNotMicroUpgrade) {
            throw new CommonPubBizException("目标收单机构" + acquirer + "商户号已禁用且存在非小微升级的禁用原因，不可切换收单机构");
        }
    }

    /**
     * 前置业务检查，切换过程中检查
     *
     * @param merchantSn
     * @param merchantId
     * @param sourceAcquirer
     * @param targetAcquirer
     */
    public void preBizCheck(String merchantSn, String merchantId, String sourceAcquirer, String targetAcquirer) {
        // 是否开启业务检查
        if (!checkBiz.isCheck(AcquirerChangeCheckBiz.ALL)) {
            return;
        }

        // 特殊检查，临时处理
        if (checkBiz.isCheck(AcquirerChangeCheckBiz.TMP_CHECK)) {
            if (tmpBiz.isInTmpMch(merchantSn)) {
                throw new CommonPubBizException("特殊检查不通过");
            }
        }

        //直接清算变成间接清算或者直接清算变成直接清算不需检查
        final McAcquirerDO souMcAcquirer = mcAcquirerDAO.getByAcquirer(sourceAcquirer);
        if (Objects.equals(souMcAcquirer.getClearType(), ClearTypeEnum.DIRECT.getValue())) {
            return;
        }

        Map<String, Object> balances = walletServcie.getBalances(merchantId);
        long balance = BeanUtil.getPropLong(balances, "withdrawable_balance");
        long frozenBalance = BeanUtil.getPropLong(balances, "frozen_balance");
        long waitingFrozenBalance = BeanUtil.getPropLong(balances, "waiting_frozen_balance");

        if (frozenBalance + waitingFrozenBalance > 0) {
            throw new CommonPubBizException("商户有冻结余额，不允许变更收单机构");
        }

        Map merchantBank = merchantService.getMerchantBankAccountByMerchantId(merchantId);

        // 检查银行卡是否正常
        if (checkBiz.isCheck(AcquirerChangeCheckBiz.BANK_ACCOUNT_STATUS)) {
            if (bankService.bankChangeProgressing(CollectionUtil.hashMap(MerchantBankAccountPre.MERCHANT_ID, merchantId))) {
                throw new CommonPubBizException("商户银行卡正在变更中");
            }
            if (balance > 0) {
                if (MerchantBankAccount.VERIFY_STATUS_FAIL == BeanUtil.getPropInt(merchantBank, MerchantBankAccount.VERIFY_STATUS)) {
                    throw new CommonPubBizException("商户银行卡验证失败，不允许变更收单机构");
                }
            }
        }

        // 检查提现记录
        if (checkBiz.isCheck(AcquirerChangeCheckBiz.WITHDRAW)) {
            if (balance > 0) {
                PageInfo pageInfo = new PageInfo(1, 20, System.currentTimeMillis() - 14 * 24 * 60 * 60 * 1000L, System.currentTimeMillis());
                ListResult listResult = withdrawService.findWithdraws(pageInfo, CollectionUtil.hashMap(
                        "merchant_id", merchantId,
                        "card_no", BeanUtil.getPropString(merchantBank, MerchantBankAccount.NUMBER)
                ));
                if (WosaiCollectionUtils.isEmpty(listResult.getRecords())) {
                    throw new CommonPubBizException("商户最近两周没有成功提现记录，不允许变更收单机构");
                }
                boolean withdrawSuccess = listResult.getRecords().stream().anyMatch(withdraw -> 2 == BeanUtil.getPropInt(withdraw, "status"));
                if (!withdrawSuccess) {
                    throw new CommonPubBizException("商户最近两周没有成功提现记录，不允许变更收单机构");
                }
            }
        }

        // 检查是否开通分账
        if (checkBiz.isCheck(AcquirerChangeCheckBiz.SHARING)) {
            SharingConfigInfo sharingConfigInfo = sharingConfigService.getMerchantSharingConfigInfo(merchantId);
            boolean useShare = sharingConfigInfo != null &&
                    (Objects.equals(SharingConfig.STATUS_ENABLED, sharingConfigInfo.getIndirectStatus()) || sharingConfigInfo.isReceiver());
            if (useShare) {
                throw new CommonPubBizException("商户已开通分账功能，不允许变更收单机构");
            }
        }

        // 检查预授权
        if (checkBiz.isCheck(AcquirerChangeCheckBiz.DEPOSIT)) {
            Map tradeConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
            int alipay = BeanUtil.getPropInt(tradeConfig, "params.deposit.alipay", TransactionParam.DEPOSIT_CLOSE);
            if (TransactionParam.DEPOSIT_OPEN == alipay) {
                throw new CommonPubBizException("商户已开通预授权功能，不允许变更收单机构");
            }
        }

        // 检查store_config/terminal_config
//        checkStoreConfigAndTerminalConfig(merchantId);

        otherCommonBizCheck.check(merchantSn, merchantId, sourceAcquirer, targetAcquirer);
    }


    /**
     * 根据收单机构获取是否银行通道的标识
     *
     * @param acquirer
     * @return 0:非银行通道 1:银行通道
     */
    public String getBankChannelFlag(String acquirer) {
        return isBankOrg(acquirer) ? BANK_CHANNEL : NOT_BANK_CHANNEL;
    }

    public boolean isBankOrg(String acquirer) {
        boolean bankOrg;
        try {
            bankOrg = getAcquirerSharedAbility(acquirer).getAcquirerInfo().isBankOrg();
        } catch (Exception e) {
            Map configMap = batchTemplateApolloConfig.getSupportBankConfig();
            McAcquirerDO acquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
            Map<String, Object> detailMap = WosaiMapUtils.getMap(configMap, acquirerDO.getProvider());
            String configAcquire = WosaiMapUtils.getString(detailMap, "acquire");
            if (StringUtils.isEmpty(configAcquire)) {
                // 找到0对应的groupId,这个就是对应的acquire
                configAcquire = (String) BeanUtil.getNestedProperty(detailMap, "0.rule_group_id");
            }
            if (e instanceof CommonPubBizException && e.getMessage().contains("SharedAbility未实现") && Objects.equals(acquirer, configAcquire)) {
                log.warn("checkBankOrg 通过线下导入的银行商户来说,需要通用配置不开发代码所以这里需要单独处理,如果已经对接了api,则请实现AcquirerSharedAbility接口");
                bankOrg = Objects.equals(acquirerDO.getType(), AcquirerOrgTypeEnum.BANK.getValue());
            } else {
                throw e;
            }
        }
        return bankOrg;
    }

    public boolean isThirdPartyOrg(String acquirer) {
        boolean thirdPartyOrg;
        try {
            thirdPartyOrg = getAcquirerSharedAbility(acquirer).getAcquirerInfo().isThirdPartyOrg();
        } catch (Exception e) {
            Map configMap = batchTemplateApolloConfig.getSupportBankConfig();
            McAcquirerDO acquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
            Map<String, Object> detailMap = WosaiMapUtils.getMap(configMap, acquirerDO.getProvider());
            String configAcquire = WosaiMapUtils.getString(detailMap, "acquire");
            if (StringUtils.isEmpty(configAcquire)) {
                // 找到0对应的groupId,这个就是对应的acquire
                configAcquire = (String) BeanUtil.getNestedProperty(detailMap, "0.rule_group_id");
            }
            if (e instanceof CommonPubBizException && e.getMessage().contains("SharedAbility未实现") && Objects.equals(acquirer, configAcquire)) {
                log.warn("isThirdPartyOrg 通过线下导入的银行商户来说,需要通用配置不开发代码所以这里需要单独处理,如果已经对接了api,则请实现AcquirerSharedAbility接口");
                thirdPartyOrg = Objects.equals(acquirerDO.getType(), AcquirerOrgTypeEnum.THIRD_PARTY.getValue());
            } else {
                throw e;
            }
        }
        return thirdPartyOrg;
    }

    /**
     * 是否支持特殊行业
     * @param acquirer
     * @return
     */
    public boolean isSupportSpecialIndustry(String acquirer) {
        boolean isSupportSpecialIndustry = false;
        try {
            isSupportSpecialIndustry = getAcquirerSharedAbility(acquirer).getAcquirerInfo().isSupportSpecialIndustry();
        } catch (Exception e) {
            return isSupportSpecialIndustry;
        }
        return isSupportSpecialIndustry;
    }

    public AcquirerSharedAbility getAcquirerSharedAbility(String acquirer) {
        return acquirerFacade.getSharedAbilityByAcquirer(acquirer)
                .orElseThrow(() -> new CommonPubBizException(acquirer + " SharedAbility未实现"));
    }

    /**
     * 子类实现特殊处理
     * 默认无特性校验 如果有 子类重写
     *
     * @param merchantSn
     * @param merchantId
     * @param sourceAcquirer
     * @param targetAcquirer
     */
    protected void acquirerSpecialCheck(String merchantSn, String merchantId, String sourceAcquirer, String targetAcquirer, boolean immediately) {

    }

    /**
     * 向目标收单机构报备
     */
    public void contractToTargetAcquirer(McAcquirerChange change) {
        boolean isContracted = hasContract(change);
        if (!isContracted) {
            createContractEeventToTargetAcquirer(change);
            return;
        }
        if (isAcquirerMerchantDisabledByMicroUpdate(change.getMerchant_sn(), change.getTarget_acquirer())) {
            log.info("收单机构商户号禁用原因是小微升级,删除原交易参数, merchantSn:{}, acquirer:{}", change.getMerchant_sn(), change.getTarget_acquirer());
            merchantTradeParamsBiz.deleteAllAcquirerRelatedParams(change.getMerchant_sn(), change.getTarget_acquirer());
            createContractEeventToTargetAcquirer(change);
        } else {
            //判断支付宝和微信是否都成功
            checkOtherPayWay(change);
            // 更新银行卡
            syncBankAccount(change);
        }
    }

    private boolean isAcquirerMerchantDisabledByMicroUpdate(String merchantSn, String acquirer) {
        try {
            List<DisableReasonRspDTO> disableReasonRspDTOS = merchantTradeParamsBiz.listMerchantAcquirerParamDisableReasons(merchantSn, acquirer);
            if (CollectionUtils.isEmpty(disableReasonRspDTOS)) {
                return false;
            }
            return disableReasonRspDTOS.stream().anyMatch(disableReasonRspDTO ->
                    Objects.equals(TradeParamsDisableReasonAccessSideEnum.CUA, disableReasonRspDTO.getAccessSide()) &&
                            StringUtils.equals(disableReasonRspDTO.getDisableReason(),
                                    BusinessLicenceCertificationTask.MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON));
        } catch (Exception e) {
            log.error("查询商户交易参数禁用原因失败, merchantSn:{}, acquirer:{}", merchantSn, acquirer, e);
            return false;
        }
    }

    private void createContractEeventToTargetAcquirer(McAcquirerChange change) {
        //银行直连没有报备的直接抛出异常
        if (isBankOrg(change.getTarget_acquirer())) {
            throw new CommonPubBizException(String.format("银行收单机构%s，还未进件成功，不允许切换", change.getTarget_acquirer()));
        }
        ContractEvent contractEvent = contractEventService.saveContractEvent(change.getMerchant_sn(), getContractGroup(change.getMerchant_sn()), "merchant-contract-job");
        Map appendExtra = CollectionUtil.hashMap(
                "event_id", contractEvent.getId()
        );
        changeDao.updateStatusWitExtra(change, AcquirerChangeStatus.CONTRACTING, "报备中", appendExtra);
    }

    @NotNull
    private AtomicBoolean isAcquirerMerchantStatusClose(McAcquirerChange change) {
        AtomicBoolean idAcquirerMerchantStatusClose = new AtomicBoolean(false);
        acquirerFacade.getSharedAbilityByAcquirer(change.getTarget_acquirer()).ifPresent(acquirerSharedAbility -> {
            AcquirerMerchantStatusEnum status = acquirerSharedAbility.getAcquirerMerchantStatus(change.getMerchant_sn());
            idAcquirerMerchantStatusClose.set(Objects.equals(status, AcquirerMerchantStatusEnum.CLOSE));
        });
        return idAcquirerMerchantStatusClose;
    }

    /**
     * 支付宝和微信必须同时有,否则抛出异常
     *
     * @param change
     */
    public boolean checkOtherPayWay(McAcquirerChange change) {
        final List<MerchantProviderParams> defaultChangeParams = getDefaultChangeParams(change);
        //是否存在微信参数
        final boolean matchWx = defaultChangeParams.stream().anyMatch(param -> Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue()));
        //是否存在支付宝参数
        final boolean matchZfb = defaultChangeParams.stream().anyMatch(param -> Objects.equals(param.getPayway(), PaywayEnum.ALIPAY.getValue()));
        if (matchWx && matchZfb) {
            return true;
        }
        throw new CommonPubBizException("支付宝/微信交易参数不存在,不允许切换");
    }

    private boolean isSkipUpdateBankAccount(McAcquirerChange change) {
        try {
            if (Objects.isNull(change) || StringUtils.isEmpty(change.getExtra())) {
                return false;
            }
            Map<String, Object> extraMap = JSON.parseObject(change.getExtra(), new TypeReference<Map<String, Object>>() {
            });
            return MapUtils.getBoolean(extraMap, AcquirerChangeSaveDTO.SKIP_SYNC_BANK_ACCOUNT_KEY, false);
        } catch (Exception e) {
            log.error("isSkipUpdateBankAccount error, merchantSn:{}, extra:{}", change.getMerchant_sn(), change.getExtra(), e);
            return false;
        }
    }

    /**
     * <AUTHOR>
     * @Description: 同步银行卡信息
     * @time 14:24
     */
    private void syncBankAccount(McAcquirerChange change) {
        if (isSkipUpdateBankAccount(change)) {
            log.info("skip update bank account when change acquirer, merchantSn:{}, targetAcquirer:{}", change.getMerchant_sn(), change.getTarget_acquirer());
            changeDao.updateStatus(change, AcquirerChangeStatus.CONTRACTED, "跳过同步银行卡");
            return;
        }
        final String targetAcquirer = change.getTarget_acquirer();
        final String merchantSn = change.getMerchant_sn();
        final IAcquirerBiz targetAcquirerBiz = composeAcquirerBiz.getAcquirerBiz(targetAcquirer);

        if (isBankOrg(change.getTarget_acquirer())) {
            changeDao.updateStatus(change, AcquirerChangeStatus.CONTRACTED, "银行直连跳过同步银行卡");
            return;
        }
        final String merchantId = change.getMerchant_id();
        //银行卡是否一致
        Map bankAccount = null;
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", merchantId, "default_status", DEFAULT_TRUE));
        if (listResult != null && listResult.getTotal() > 0) {
            bankAccount = listResult.getRecords().get(0);
        }
        if (CollectionUtils.isEmpty(bankAccount)) {
            throw new ContextParamException("获取商户卡信息为空merchantSn:" + merchantSn + " merchantId:" + merchantId);
        }
        final Boolean consistent = targetAcquirerBiz.bankAccountConsistent(merchantSn, bankAccount);
        if (consistent) {
            changeDao.updateStatus(change, AcquirerChangeStatus.CONTRACTED, "银行卡与收单机构一致不需要同步");
            return;
        }
        //创建一个目标收单机构的换卡的任务
        final ContractTask task = contractTaskService.createTaskByAssignAcquire(merchantSn, change.getTarget_acquirer());
        if (Objects.nonNull(task)) {
            Map appendExtra = CollectionUtil.hashMap(
                    "task_id", task.getId()
            );
            changeDao.updateStatusWitExtra(change, AcquirerChangeStatus.CONTRACTING, "同步银行卡中", appendExtra);
        } else {
            throw new ContractBizException("向收单机构同步银行卡任务创建失败");
        }

    }

    /**
     * 检查是否已经报备过
     *
     * @param change
     * @return
     */
    public boolean hasContract(McAcquirerChange change) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(getProviderCode(change.getTarget_acquirer()))
                .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andDeletedEqualTo(false);
        return WosaiCollectionUtils.isNotEmpty(paramsMapper.selectByExampleWithBLOBs(example));
    }

    public void syncFeeToTargetAcquirer(McAcquirerChange change) {
        AcquirerSharedAbility sharedAbility = null;
        final String target_acquirer = change.getTarget_acquirer();
        try {
            sharedAbility = getAcquirerSharedAbility(target_acquirer);
        } catch (Exception e) {
            Map configMap = batchTemplateApolloConfig.getSupportBankConfig();
            McAcquirerDO acquirerDO = mcAcquirerDAO.getByAcquirer(target_acquirer);
            Map<String, Object> detailMap = WosaiMapUtils.getMap(configMap, acquirerDO.getProvider());
            String configAcquire = WosaiMapUtils.getString(detailMap, "acquire");
            if (StringUtils.isEmpty(configAcquire)) {
                // 找到0对应的groupId,这个就是对应的acquire
                configAcquire = (String) BeanUtil.getNestedProperty(detailMap, "0.rule_group_id");
            }
            if (e instanceof CommonPubBizException && e.getMessage().contains("SharedAbility未实现") && Objects.equals(target_acquirer, configAcquire)) {
                log.warn("syncFeeToTargetAcquirer 通过线下导入的银行商户来说,来不及开发代码所以这里需要单独处理,如果已经对接了api,则请实现AcquirerSharedAbility接口");
                if (Objects.equals(FeeEffectiveTypeEnum.SQB.getValue(), acquirerDO.getFeeEffectiveType())) {
                    changeDao.updateStatus(change, AcquirerChangeStatus.SYNC_FEE_SUCCESS, "费率以收钱吧为准，不需要同步");
                    sendNotice(change.getMerchant_id());
                    return;
                }
            } else {
                throw e;
            }
        }

        if (sharedAbility.getAcquirerInfo().isFeeBasedOnSQB()) {
            changeDao.updateStatus(change, AcquirerChangeStatus.SYNC_FEE_SUCCESS, "费率以收钱吧为准，不需要同步");
            sendNotice(change.getMerchant_id());
            return;
        }

        if (isBankOrg(change.getSource_acquirer())) {
            changeDao.updateStatus(change, AcquirerChangeStatus.SYNC_FEE_SUCCESS, "从银行切走，不需要同步费率");
            sendNotice(change.getMerchant_id());
            return;
        }

        // 检查费率是否一致
        boolean feeRateConsistentWithSqb = sharedAbility.isFeeRateConsistentWithSqb(change.getMerchant_sn());
        if (feeRateConsistentWithSqb) {
            changeDao.updateStatus(change, AcquirerChangeStatus.SYNC_FEE_SUCCESS, "费率一致，不需要同步");
            sendNotice(change.getMerchant_id());
            return;
        }

        ContractTask contractTask = contractTaskService.syncFeeRate2Acquirer(change.getMerchant_sn(), target_acquirer);
        if (change.getImmediately() && Objects.equals(AcquirerTypeEnum.FU_YOU.getValue(), target_acquirer)) {
            throw new ContractBizException("富友费率不一致，不支持实时生效，已自动同步费率，请次日重试");
        }

        if (Objects.isNull(contractTask)) {
            throw new CommonPubBizException("生成同步费率任务失败");
        }

        Map appendExtra = CollectionUtil.hashMap(
                "fee_task_id", contractTask.getId()
        );
        changeDao.updateStatusWitExtra(change, AcquirerChangeStatus.SYNC_FEE, "同步费率中", appendExtra);
    }

    /**
     * 报备失败后的一些处理
     *
     * @param change
     * @return
     */
    public void postContractFail(McAcquirerChange change) {
        try {
            ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(change.getMerchant_sn());
            Long version = contractStatus.getVersion();
            contractStatus.setStatus(ContractStatus.STATUS_SUCCESS).setVersion(version + 1);
            contractStatusMapper.updateByPrimaryKeySelective(contractStatus);

            Map merchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(change.getMerchant_id());
            merchantBankService.updateMerchantBankAccount(CollectionUtil.hashMap(
                    MerchantBankAccount.MERCHANT_ID, change.getMerchant_id(),
                    MerchantBankAccount.VERIFY_STATUS, MerchantBankAccount.VERIFY_STATUS_SUCC
            ));

            String number = BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.NUMBER);
            Map pre = merchantBankService.getMerchantBankAccountPreByMerchantIdAndNumber(change.getMerchant_id(), number);
            merchantBankService.updateMerchantBankAccountPre(CollectionUtil.hashMap(
                    DaoConstants.ID, BeanUtil.getPropString(pre, DaoConstants.ID),
                    MerchantBankAccountPre.VERIFY_STATUS, MerchantBankAccountPre.VERIFY_STATUS_SUCC
            ));
        } catch (Exception e) {
            log.error("postContractFail error " + change.getMerchant_sn(), e);
        }
    }

    /**
     * 获取报备规则组
     *
     * @return
     */
    public abstract String getContractGroup(String merchantSn);

    @Transactional(rollbackFor = Exception.class)
    public void doChangeAcquirer(McAcquirerChange change) {
        Boolean payChange = isPayChange(change);
        //直接清算
        final McAcquirerDO souMcAcquirer = mcAcquirerDAO.getByAcquirer(change.getSource_acquirer());
        if (Objects.equals(souMcAcquirer.getClearType(), ClearTypeEnum.INDIRECT.getValue()) && payChange) {
            // 提现
            withdraw(change);
        }
        changeTradeParams(change, payChange);
        if (!payChange) {
            // 处理成功
            changeSuccess(change);
            return;
        }
        //其他后置处理(如开启原有支付的支付方式和关闭原有套餐)
        try {
            otherBizPost(change);
        } catch (Exception e) {
            log.error("切换收单机构后置处理失败, sn: {} ", change.getMerchant_sn(), e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("切换收单机构后置处理失败 %s %s %s", change.getId(), change.getMerchant_sn(), e.getMessage()));
        }
        // 处理成功
        changeSuccess(change);
    }

    public void changeTradeParams(McAcquirerChange change, Boolean payChange) {
        String merchantSn = change.getMerchant_sn();
        final String targetAcquirer = change.getTarget_acquirer();
        if (payChange) {
            // 更新收单机构
            ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
            ContractStatus updateValue = new ContractStatus()
                    .setId(contractStatus.getId())
                    .setAcquirer(targetAcquirer);
            contractStatusMapper.updateByPrimaryKeySelective(updateValue);
        }

        // 保存当前在用交易参数
        saveCurrentUseParams(change);

        // 切换交易参数
        final List<MerchantProviderParams> changeParams = getDefaultChangeParamsWhenChangeAcquire(change);
        boolean existedATDisableParams = changeParams.stream().filter(t -> Objects.equals(t.getPayway(), PaywayEnum.WEIXIN.getValue())
                        || Objects.equals(t.getPayway(), PaywayEnum.ALIPAY.getValue()))
                .anyMatch(MerchantProviderParams::paramsIsDisabled);
        if (existedATDisableParams) {
            throw new ContractBizException("存在AT商户号被禁用，不能切收单机构");
        }
        //将参数排序,微信放在首位,优先设置微信参数,如果失败就可以避免其他参数出现问题
        final ArrayList<MerchantProviderParams> paramsList = Lists.newArrayList();
        AtomicReference<Boolean> reContractWx = new AtomicReference<>(Boolean.FALSE);
        changeParams.forEach(param -> {
            if (Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue())) {
                chooseWxParam(merchantSn, paramsList, reContractWx, param);
            } else {
                paramsList.add(param);
            }
        });
        Map extra = CommonUtil.string2Map(change.getExtra());
        String tradeAppId = BeanUtil.getPropString(extra, "tradeAppId");
        if (StringUtils.isBlank(tradeAppId)) {
            tradeAppId = subBizParamsBiz.getPayTradeAppId();
        }
        //1、通道切换审批-切换储值业务至银行通道，取消储值业务交易参数；
        cancelStoredConfig(merchantSn, targetAcquirer, tradeAppId);
        //业务标识
        String finalTradeAppId = tradeAppId;
        List<Integer> changedPayWays = new ArrayList<>();
        paramsList.stream().forEach(param -> {
            try {
                //银行->间连可能会出现间连组织+行业配置的套餐已经不存在了,但还是恢复到之前的套餐,这种情况需要报错中断流程,后续出现工单,产品去除了限制
//                checkIndirectComb(change, merchantSn, targetAcquirer);
                boolean success = tradeParamsBiz.changeTradeParams(param, null, false, finalTradeAppId);
                if (success) {
                    changedPayWays.add(param.getPayway());
                }
                if (Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue()) && reContractWx.get()) {
                    //重新报备的微信子商户号按组织业务开通规则取配微信费率套餐，在最终通道切换生效微信费率套餐
                    wxSettlementIdChangeBiz.setCombAfterReContractWx(change);
                }
            } catch (ChangeParamsIgnoreException e) {
                // 忽略异常
            }
        });
        deletedTradeParams(change, changedPayWays, payChange, finalTradeAppId);
        if (payChange) {
            updateClearanceProvider(change);
        }
        //当移动支付业务切回间连时取消其他业务配置的套餐
        if (payChange && isThirdPartyOrg(targetAcquirer)) {
            cancelOtherCombo(merchantSn);
        }
        changeDao.updateStatus(change, AcquirerChangeStatus.CHANGE_PARAMS, "切换交易参数成功");

    }

    /**
     * 根据结算ID判断是不是需要重新报备
     *
     * @param merchantSn
     * @param paramsList
     * @param reContractWx
     * @param param
     */
    public void chooseWxParam(String merchantSn, ArrayList<MerchantProviderParams> paramsList, AtomicReference<Boolean> reContractWx, MerchantProviderParams param) {
        // 检查微信子商户号结算id
        boolean pass = wxSettlementIdChangeBiz.checkSettlementId(param);
        if (!Objects.equals(param.getProvider(), ProviderEnum.PROVIDER_FUYOU.getValue()) && !pass) {
            //判断目标通道是否允许重新报备微信子商户号（目前支持拉卡拉、海科）
            String rule = param.getContract_rule();
            //支持重新报备微信
            List<String> reContractWxList = applicationApolloConfig.getReContractWx();
            boolean anyMatch = reContractWxList.stream().anyMatch(reWx -> StringUtils.containsAny(rule, reWx));
            if (!anyMatch) {
                throw new CommonPubBizException("目标通道微信子商户号与商户当前行业不匹配，请修改商户行业或保留在当前通道。");
            }
            //重新报备
            //mc_rule_group表rule与acquire对应关系
            List<McContractRuleDO> ruleList = mcContractRuleDAO.listAllRule();
            //规则和acquire对应关系
            final Map<String, String> ruleAcquireMap = ruleList.parallelStream()
                    .collect(Collectors.toMap(x -> x.getRule(), x -> x.getAcquirer(), (val1, val2) -> val1));
            String acquire = BeanUtil.getPropString(ruleAcquireMap, rule);
            if (rule.contains(ContractRuleConstants.LKL_ORG_CONTRACT_RULE_FEATURE)) {
                acquire = AcquirerTypeEnum.LKL_V3.getValue();
            }
            CommonResult commonResult = composeAcquirerBiz.reContractWx(merchantSn, acquire);
            if (!commonResult.isSuccess()) {
                throw new ContractBizException(String.format("商户号:{%s},目标通道微信子商户号与商户当前行业不匹配,重新报备微信子商户号失败,请修改商户行业或保留在当前通道。", merchantSn));
            }
            String subMchId = BeanUtil.getPropString(commonResult.getBiz_response(), "subMchId");
            if (StringUtils.isBlank(subMchId)) {
                throw new ContractBizException(String.format("商户号:{%s},获取不到最新子商户号。", merchantSn));
            }
            MerchantProviderParams wxParam = merchantProviderParamsMapper.getByPayMerchantId(subMchId);
            paramsList.add(0, wxParam);
            reContractWx.set(Boolean.TRUE);
        } else {
            paramsList.add(0, param);
        }
    }

    /**
     * 判断当前切换是不是移动支付业务
     *
     * @param change
     * @return
     */
    public Boolean isPayChange(McAcquirerChange change) {
        Map extra = CommonUtil.string2Map(change.getExtra());
        String tradeAppId = BeanUtil.getPropString(extra, "tradeAppId");
        return Objects.equals(tradeAppId, subBizParamsBiz.getPayTradeAppId());
    }

    /**
     * 切换清算通道标志
     *
     * @param change
     */
    protected void updateClearanceProvider(McAcquirerChange change) {
        tradeConfigClient.updateClearProviderByAcquirer(change.getMerchant_id(), change.getTarget_acquirer());
    }

    private void saveCurrentUseParams(McAcquirerChange change) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andPaywayNotEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            List<String> lastUseParams = params.stream()
                    .filter(param -> !param.getProvider().equals(param.getPayway()))
                    .map(param -> param.getId())
                    .collect(Collectors.toList());
            Map appendExtra = CollectionUtil.hashMap(
                    "last_use_params", lastUseParams
            );
            changeDao.appendExtra(change, appendExtra);
        }
    }

    /**
     * 获取默认切换的参数
     *
     * @param change 收单机构切换信息
     * @return 默认待切换的参数列表
     */
    protected abstract List<MerchantProviderParams> getDefaultChangeParams(McAcquirerChange change);


    /**
     * 切换收单机构时获取交易参数,和com.wosai.upay.job.biz.acquirer.AbstractAcquirerChangeBiz#getDefaultChangeParams(com.wosai.upay.job.model.DO.McAcquirerChange) 基本一致
     * 不一样在于要切换到拉卡拉V3的时候当前这个方法只会选择1033通道的支付宝和微信交易参数,如果不存在则重新报备
     *
     * @param change
     * @return
     */
    List<MerchantProviderParams> getDefaultChangeParamsWhenChangeAcquire(McAcquirerChange change) {
        return getDefaultChangeParams(change);
    }

    /**
     * 根据商户号获取对应交易参数
     *
     * @param merchantSn
     * @return
     */
    public List<MerchantProviderParams> getDefaultChangeParamsForSubBiz(String merchantSn) {
        try {
            McAcquirerChange change = new McAcquirerChange();
            change.setMerchant_sn(merchantSn);
            return this.getDefaultChangeParams(change);
        } catch (Exception e) {
            log.error("getDefaultChangeParamsForSubBiz error :{}", e);
            return null;
        }
    }

    private void withdraw(McAcquirerChange change) {
        // 20221025 现在余额服务采用小账户模式，提现不成功也不会错乱
        try {
            long balance = walletServcie.getBalance(change.getMerchant_id());
            if (balance > 0) {
                // 提现
                List<Map<String, Object>> withdraws = withdrawService.changeAcquirerWithdraw(change.getMerchant_id());
                List<String> withdrawIds = withdraws.stream()
                        .map(withdraw -> BeanUtil.getPropString(withdraw, DaoConstants.ID))
                        .collect(Collectors.toList());
                changeDao.updateStatusWitExtra(change, AcquirerChangeStatus.WITHDRAW, "发起提现", CollectionUtil.hashMap(
                        "withdraws", withdrawIds
                ));
                businessLogBiz.sendWithdrawLog(change.getMerchant_id(), balance, "切换收单机构，强制结算");
                log.info("{} 发起强制提现 {}", change.getMerchant_sn(), JSON.toJSONString(withdrawIds));
            }
        } catch (Exception e) {
            log.error("{} {} 发起提现错误", change.getMerchant_sn(), change.getId(), e);
        }
    }

    public void changeSuccess(McAcquirerChange change) {
        changeDao.updateStatus(change, AcquirerChangeStatus.SUCCESS, "切换成功");
        sendChangeSuccessLog(change, "切换收单机构成功");
        applicationEventPublisher.publishEvent(new AcquirerChangeEvent(change, change.getMerchant_sn()));
        sendKafkaMsg(change);
        sendTradeAppKafkaMsg(change);
    }

    public void sendChangeSuccessLog(McAcquirerChange change, String remark) {
        try {
            String extra = change.getExtra();
            JSONObject extraMap = JSON.parseObject(extra);
            if (MapUtils.isEmpty(extraMap) || !extraMap.containsKey(McAcquirerChangeDO.LOG_PARAMS_KEY)) {
                businessLogBiz.sendChangeAcquirerLog(change.getMerchant_id(), change.getSource_acquirer(), change.getTarget_acquirer(), remark);
                return;
            }
            LogParamsDto logParamsDto = JSON.parseObject(JSON.toJSONString(extraMap.get("logParamsDto")), LogParamsDto.class);
            businessLogBiz.sendChangeAcquirerLog(change.getMerchant_id(), change.getSource_acquirer(), change.getTarget_acquirer(), remark, logParamsDto);
        } catch (Exception ex) {
            log.error("切换收单机构发送日志失败, change:{}", JSON.toJSONString(change), ex);
        }
    }

    public void sendKafkaMsg(McAcquirerChange change) {
        try {
            AcquirerChange acquirerChange = new AcquirerChange(
                    change.getMerchant_sn(),
                    change.getMerchant_id(),
                    change.getSource_acquirer(),
                    change.getTarget_acquirer(),
                    getBankChannelFlag(change.getTarget_acquirer())
            );
            kafkaTemplate.send(ACQUIRER_CHANGE_TOPIC, acquirerChange);
            log.info("切换收单机构发送消息成功: {} {}", change.getId(), change.getMerchant_sn());
        } catch (Exception e) {
            log.error("切换收单机构发送消息失败: {} {}", change.getId(), change.getMerchant_sn(), e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("切换收单机构发送消息失败 %s %s %s", change.getId(), change.getMerchant_sn(), e.getMessage()));
        }
    }

    public void sendTradeAppKafkaMsg(McAcquirerChange change) {
        //加入开关判断避免出错影响其他业务
        boolean tradeAppSwitch = applicationApolloConfig.getTradeAppSwitch();
        //关闭则不执行后续方法直接返回
        if (!tradeAppSwitch) {
            return;
        }
        String tradeAppId = null;
        try {
            String extra = change.getExtra();
            tradeAppId = BeanUtil.getPropString(JSONObject.parseObject(extra, Map.class), "tradeAppId");
            if (StringUtils.isEmpty(tradeAppId)) {
                return;
            }

            TradeAppAcquirerChange tradeAppAcquirerChange = new TradeAppAcquirerChange(
                    change.getMerchant_sn(),
                    change.getMerchant_id(),
                    change.getSource_acquirer(),
                    change.getTarget_acquirer(),
                    getBankChannelFlag(change.getTarget_acquirer()),
                    tradeAppId
            );
            kafkaTemplate.send(TRADE_APP_ACQUIRER_CHANGE_TOPIC, tradeAppAcquirerChange);
            log.info("sendTradeAppKafkaMsg切换收单机构发送消息成功: 切换Id:{},商户号:{}", change.getId(), change.getMerchant_sn());
            if (Objects.equals(tradeAppId, subBizParamsBiz.getPayTradeAppId())) {
                dataCenterProducer.publishProfile(change.getMerchant_id(), CollectionUtil.hashMap(
                        "pay_acquire", change.getTarget_acquirer()
                ));

            } else {
                dataCenterProducer.publishProfile(change.getMerchant_id(), CollectionUtil.hashMap(
                        "business_acquire", change.getTarget_acquirer()
                ));
            }
        } catch (Exception e) {
            log.error("sendTradeAppKafkaMsg切换收单机构发送消息失败:切换Id:{},商户号:{},异常信息:", change.getId(), change.getMerchant_sn(), e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("切换收单机构发送消息失败 商户号:%s,切换Id:%s,tradeAppId:%s,异常信息:%s ", change.getMerchant_sn(), change.getId(), tradeAppId, e.getMessage()));
        }
    }


    public void changeFail(McAcquirerChange change, Exception e) {
        final String memo = StringUtils.substring(e.getMessage(), 0, 240);
        changeDao.updateStatus(change, AcquirerChangeStatus.FAIL, "切换失败：" + memo);
    }

    public void sendNotice(String merchantId) {
        aopBiz.sendNoticeToAdmin(merchantId, "I0UDXL7CVVCT", "8JZS4HUEORRA", Collections.EMPTY_MAP);
    }

    /**
     * @param change
     * <AUTHOR>
     * @Description: 其他后置处理业务
     * @time 10:56
     */
    public void otherBizPost(McAcquirerChange change) {
        AbstractAcquirerChangeBiz sourceChangeBiz = null;
        try {
            sourceChangeBiz = applicationContext.getBean(change.getSource_acquirer() + "-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
        } catch (Exception e) {
            // 找不到对应的处理类并且是银行的话，尝试返回一个 commonBank 的类
            if (isBankOrg(change.getSource_acquirer())) {
                sourceChangeBiz = applicationContext.getBean("commonBank-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
            }
        }
        if (sourceChangeBiz == null) {
            log.warn("未找到对应的处理");
            return;
        }
        sourceChangeBiz.sourceAcquirerPostBiz(change);
        targetAcquirerPostBiz(change);
    }

    /**
     * 原收单机构切换成功后置处理
     *
     * @Description: 子类重写逻辑
     * @ime 11:40
     */
    protected void sourceAcquirerPostBiz(McAcquirerChange change) {

    }

    /**
     * 目标收单机构切换成功后置处理
     *
     * @param change
     */
    protected void targetAcquirerPostBiz(McAcquirerChange change) {

    }

    public void doCloseInvalidAcquirerStatus(McAcquirerChange change) {
        Boolean payChange = isPayChange(change);
        if (!payChange) {
            return;
        }
        String merchantSn = change.getMerchant_sn();
        try {
            //针对移动支付业务从通联切出,但是还有其他业务方再使用通联参数
            final SubBizParamsExample example = new SubBizParamsExample();
            example.or()
                    .andMerchant_snEqualTo(merchantSn)
                    .andTrade_app_idNotIn(Lists.newArrayList(subBizParamsBiz.getPayTradeAppId(), subBizParamsBiz.getT9TradeAppId()))
                    .andDeletedEqualTo(Boolean.FALSE);
            List<SubBizParams> subBizParams = subBizParamsMapper.selectByExample(example);
            //多业务中还在使用通联
            boolean match = subBizParams.stream().anyMatch(sub -> Objects.equals(sub.getProvider(), ProviderEnum.PROVIDER_TONGLIAN.getValue()));
            List<String> notClose = Lists.newArrayList();
            notClose.addAll(NOT_CLOSE_ACQUIRER);
            if (match) {
                notClose.add(AcquirerTypeEnum.TONG_LIAN.getValue());
            }
            //关闭原有收单机构状态,并排除目标收单机构
            final List<SyncMchStatusResp> respList = composeAcquirerBiz.handleInvalidAcquire(merchantSn, ValidStatusEnum.INVALID.getValue(), change.getTarget_acquirer(), notClose);
            log.info("切换收单机构处理未生效收单机构返回结果:{},商户号:{}", JSONObject.toJSONString(respList), merchantSn);
        } catch (Exception e) {
            log.error("换收单机构处理未生效收单机构异常, 商户号: {}", merchantSn, e);
        }
    }


    /**
     * 间连收单机构切换银行收单机构时删除不支持的交易方式的交易参数
     *
     * @param change
     * @param changedPayWays 切换参数成功的payway
     */
    private void deletedTradeParams(McAcquirerChange change, List<Integer> changedPayWays, boolean payChange, String tradeAppId) {
        String merchantSn = change.getMerchant_sn();

        //如有不支持的交易方式 删除交易参数 关闭交易权限
        List<Integer> payWays = Lists.newArrayList(
                PaywayEnum.ALIPAY.getValue(),
                PaywayEnum.WEIXIN.getValue(),
                PaywayEnum.UNIONPAY.getValue(),
                PaywayEnum.BESTPAY.getValue(),
                PaywayEnum.JD_WALLET.getValue()
        );
        payWays.removeAll(changedPayWays);
        if (CollectionUtils.isEmpty(payWays)) {
            return;
        }
        try {
            String merchantId = change.getMerchant_id();
            if (payChange) {
                deleteMerchantConfig(merchantId, payWays);

                MerchantProviderParams updateValue = new MerchantProviderParams()
                        .setStatus(ValidStatusEnum.INVALID.getValue())
                        .setMtime(System.currentTimeMillis());

                MerchantProviderParamsExample example = new MerchantProviderParamsExample();
                example.or().andMerchant_snEqualTo(merchantSn)
                        .andPaywayIn(payWays)
                        .andProviderGreaterThan(1000)
                        .andStatusEqualTo(ValidStatusEnum.VALID.getValue());
                merchantProviderParamsMapper.updateByExampleSelective(updateValue, example);
            } else {
                deleteMerchantAppConfig(merchantId, payWays, tradeAppId);
            }

        } catch (Exception e) {
            log.error("删除交易参数失败,商户号: {}", merchantSn, e);
        }
        //删除缓存
        supportService.removeCachedParams(merchantSn);
    }

    private void deleteMerchantConfig(String merchantId, List<Integer> payWays) {
        payWays.parallelStream().forEach(payWay -> {
            Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
            //更新交易参数 将对应的交易参数信息清空掉
            if (MapUtil.isNotEmpty(merchantConfig)) {
                Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName("provider_trade_params_key");
                String tradeParamsKey = MapUtils.getString(providerTradeParamsKey, MapUtils.getString(merchantConfig, MerchantConfig.PROVIDER));
                Map params = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS);
                if (WosaiMapUtils.isEmpty(params)) {
                    return;
                }
                params.remove(tradeParamsKey);
                tradeConfigService.updateMerchantConfig(CollectionUtil.hashMap(
                        MerchantConfig.PARAMS, params,
                        DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID)));
            }
        });
    }

    private void deleteMerchantAppConfig(String merchantId, List<Integer> payWays, String tradeAppId) {
        payWays.parallelStream().forEach(payWay -> {
            Map merchantConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payWay, tradeAppId);
            //更新交易参数 将对应的交易参数信息清空掉
            if (MapUtil.isNotEmpty(merchantConfig)) {
                Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName("provider_trade_params_key");
                String tradeParamsKey = MapUtils.getString(providerTradeParamsKey, MapUtils.getString(merchantConfig, MerchantConfig.PROVIDER));
                Map params = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS);
                if (WosaiMapUtils.isEmpty(params)) {
                    return;
                }
                params.remove(tradeParamsKey);
                tradeConfigService.updateMerchantAppConfig(CollectionUtil.hashMap(
                        MerchantConfig.PARAMS, params,
                        DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID)));
            }
        });
    }


    /**
     * 同步收单机构状态
     *
     * @param change
     */
    public void syncMchStatusToAcquirer(McAcquirerChange change) {
        String extra = change.getExtra();
        boolean forceChange = BeanUtil.getPropBoolean(JSONObject.parseObject(extra, Map.class), "forceChange", false);

        final String targetAcquirer = change.getTarget_acquirer();
        final String merchantSn = change.getMerchant_sn();
        final IAcquirerBiz targetAcquirerBiz = composeAcquirerBiz.getAcquirerBiz(targetAcquirer);
        final Boolean acquirerMchStatus = targetAcquirerBiz.getAcquirerMchStatus(merchantSn);
        SyncMchStatusResp syncMchStatusResp = new SyncMchStatusResp().setSuccess(true).setMessage("success");
        if (!acquirerMchStatus) {
            //打开目标收单机构商户状态
            syncMchStatusResp = targetAcquirerBiz.syncMchAndSubMchIdStatus(merchantSn, ValidStatusEnum.VALID.getValue());
        } else {
            syncMchStatusResp.setSubMchIdSyncResult(targetAcquirerBiz.syncSubMchIdStatus(merchantSn, ValidStatusEnum.VALID.getValue()));
        }
        //收单机构状态打开失败
        if (!syncMchStatusResp.isSuccess() && !forceChange) {
            throw new CommonPubBizException("同步收单机构状态失败" + syncMchStatusResp.getMessage());
        }
        changeDao.updateStatus(change, AcquirerChangeStatus.CHANGE_ACQUIRER_STATUS_SUCCESS, "收单机构状态同步成功");
    }

    /**
     * 获取ProviderCode
     *
     * @return
     */
    public abstract int getProviderCode(String acquirer);


    /**
     * 通道切换审批-切换储值业务至银行通道，取消储值业务交易参数
     *
     * @param merchantSn
     * @param targetAcquirer
     * @param tradeAppId
     */
    public void cancelStoredConfig(String merchantSn, String targetAcquirer, String tradeAppId) {
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        List<Map<String, Object>> appConfigList = tradeConfigService.getMerchantAppConfigByMerchantIdAndApp(merchantId, tradeAppId);
        String tradeAppName = subBizParamsBiz.getTradeAppNameById(tradeAppId);
        if (CollectionUtils.isEmpty(appConfigList)) {
            return;
        }
        //取消储值业务交易参数
        if (!modifyMerchantConfigAppId.contains(tradeAppId) && isBankOrg(targetAcquirer)) {
            subBizParamsBiz.doCancelCombo(merchantSn, tradeAppId, String.format("%s切换到银行取消套餐", tradeAppName));
        }

    }

    /**
     * 当移动支付业务切回间连时取消其他业务配置的套餐
     *
     * @param merchantSn
     */
    public void cancelOtherCombo(String merchantSn) {
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        List<Map<String, Object>> appConfigList = tradeConfigService.getMerchantAppConfigByMerchantIdAndApp(merchantId, null);
        if (CollectionUtils.isEmpty(appConfigList)) {
            log.info("cancelOtherCombo 商户号:{},appConfigList为空", merchantSn);
            return;
        }
        //取消其他业务交易参数
        Map<String, List<Map<String, Object>>> groupByAppId = appConfigList.stream().collect(Collectors.groupingBy(appConfig -> BeanUtil.getPropString(appConfig, MerchantAppConfig.APP_ID)));
        Set<String> appIds = groupByAppId.keySet();
        appIds.forEach(appId -> {
            subBizParamsBiz.doCancelCombo(merchantSn, appId, "移动支付业务切回间连取消其他业务套餐");
        });
    }

    public void configDigitalCurrency(String merchantSn) {// 开通数币受理默认空方法
    }
}
