package com.wosai.upay.job.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.wosai.data.jackson.RowDeserializerInstantiator;
import com.wosai.web.util.JsonUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;


/**
 * Created by lih<PERSON><PERSON> on 2018/6/1.
 */
@Component
public class RpcConfigUtil {

    private static ObjectMapper snakeObjectMapper;

    @Value("${x_env_flag}")
    public static String x_env_flag;


    static {
        snakeObjectMapper = JsonUtil.defaultRpcObjectMapper();
        snakeObjectMapper.setHandlerInstantiator(new RowDeserializerInstantiator());
    }


    public static JsonProxyFactoryBean getJsonProxyFactoryWareTracingBean(String serviceUrl, Class serviceInterface) {
        return getJsonProxyFactoryWareTracingBean(serviceUrl, serviceInterface, 1000, 3000);
    }

    public static JsonProxyFactoryBean getJsonProxyFactoryWareTracingMerchantContractBean(String serviceUrl, Class serviceInterface) {
        return getJsonProxyFactoryWareTracingBean(serviceUrl, serviceInterface, 1000, 120000);
    }

    public static JsonProxyFactoryBean getJsonProxyFactoryWareTracingBean(String serviceUrl, Class serviceInterface, int connectTimeOut, int readTimeOut) {
        JsonProxyFactoryBean factoryBean = new JsonProxyFactoryBean();
        factoryBean.setServiceUrl(serviceUrl);
        factoryBean.setServiceInterface(serviceInterface);
        factoryBean.setReadTimeoutMillis(readTimeOut);
        factoryBean.setConnectionTimeoutMillis(connectTimeOut);
        return factoryBean;
    }

    public static JsonProxyFactoryBean getJsonProxyFactoryBeanBySnakeCase(String serviceUrl, Class serviceInterface) {
        JsonProxyFactoryBean bean = getJsonProxyFactoryWareTracingBean(serviceUrl, serviceInterface);
        bean.setObjectMapper(snakeObjectMapper);
        return bean;
    }
}
