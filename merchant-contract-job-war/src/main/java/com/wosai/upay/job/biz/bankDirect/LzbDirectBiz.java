package com.wosai.upay.job.biz.bankDirect;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.paramContext.FeeRateUtil;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.model.ViewProcess;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.refactor.dao.MerchantAcquirerInfoDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantAcquirerInfoDO;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.wosai.upay.core.service.StoreService;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.wosai.upay.job.providers.PsbcProvider.replaceHttp;

@Component
@Slf4j
public class LzbDirectBiz extends AbstractBankDirectApplyBiz {

    @Value("${lzb_dev_code}")
    public String lzbDevCode;

    @Autowired
    private StoreService storeService;

    @Autowired
    private MerchantAcquirerInfoDAO merchantAcquirerInfoDAO;

    private static final String LAST_CONTRACT_ID = "last_contract_id";

    @Override
    protected String chooseGroupByContextAndDevCode(Map<String, Object> context, String devCode) {
        return McConstant.RULE_GROUP_LZB;
    }

    @Override
    protected Map<String, Object> completeContext(Map<String, Object> paramContext, BankDirectReq bankDirectReq) {
        Map merchantInfo = merchantService.getMerchantBySn(bankDirectReq.getMerchant_sn());
        if (merchantInfo == null) {
            throw new ContextParamException("商户" + bankDirectReq.getMerchant_sn() + "不存在");
        }
        List<Map> records = storeService.getStoreListByMerchantId(BeanUtil.getPropString(merchantInfo, "id"),
                new PageInfo(1, 1, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC))), null).getRecords();
        if (CollectionUtils.isEmpty(records) || MapUtils.isEmpty(records.get(0))) {
            throw new ContextParamException("商户" + "门店不存在");
        }
        paramContext.put("store", records.get(0));

        final Map formBody = JSONObject.parseObject(bankDirectReq.getForm_body(), Map.class);
        final List config = JSONObject.parseObject(BeanUtil.getPropString(formBody, "merchant_config"), List.class);
        paramContext.put("lzb_feeRate", config);
        paramContext.put("trade_combo_id", BeanUtil.getPropString(formBody, "trade_combo_id"));
        //放入业务标识
        paramContext.put("dev_code", bankDirectReq.getDev_code());
        //放入spa可以识别的费率信息
        final List<Map> list = ((List<Map>) config).stream().map(x -> {
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.WEIXIN.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.WECHAT_PAY_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.ALIPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.ALIPAY_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.UNIONPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_DEBIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        paramContext.putIfAbsent(ParamContextBiz.MERCHANT_FEE_RATES, list);
        Optional<MerchantAcquirerInfoDO> merchantAcquirerInfoDO = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(bankDirectReq.getMerchant_sn(), AcquirerTypeEnum.LZB.getValue());
        merchantAcquirerInfoDO.ifPresent(acquirerInfoDO -> paramContext.put(LAST_CONTRACT_ID, acquirerInfoDO.getAcquirerMerchantId()));
        return paramContext;
    }

    @Override
    public List<ViewProcess> initViewProcess(String merchantSn) {
        final List<ViewProcess> viewProcesses = preViewProcess(this.getAcquire());
        if (CollectionUtils.isEmpty(viewProcesses)) {
            return Lists.newArrayList();
        }
        //设置微信图片地址链接
        viewProcesses.forEach(x -> {
            if (Objects.equals(x.getExtra(), Boolean.TRUE)) {
                final String imageUrl = getWXImageUrl(getAcquire(), getProvider());
                x.setExtraMessage(imageUrl);
                x.setAliMessage(replaceHttp("https://images.wosaimg.com/43/94c324ceebb13328dd8d980818e6d3f4f57756.png"));
            }
        });
        return viewProcesses;
    }

    @Override
    protected Integer getBankRef(String dev_code) {
        return BankDirectApplyRefEnum.LZB.getValue();
    }

    @Override
    protected String getAcquire() {
        return AcquirerTypeEnum.LZB.getValue();
    }

    @Override
    protected Integer getProvider() {
        return ProviderEnum.PROVIDER_LZB.getValue();
    }

    @Override
    public String getDevCode() {
        return lzbDevCode;
    }
}
