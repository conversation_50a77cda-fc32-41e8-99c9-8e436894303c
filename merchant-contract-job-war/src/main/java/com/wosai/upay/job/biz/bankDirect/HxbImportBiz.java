package com.wosai.upay.job.biz.bankDirect;

import static com.wosai.upay.job.biz.comboparams.ProviderParamsHandle.SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.crow.api.model.query.SingleTagEntityRecord;
import com.wosai.data.crow.api.model.query.SingleVersionTagKV;
import com.wosai.data.crow.api.service.OnlineQueryService;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.market.boss.circle.user.api.UserFollowAndFansService;
import com.wosai.market.boss.circle.user.api.dto.request.follow.AutoFollowRequest;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.sales.core.model.Merchants;
import com.wosai.sales.core.service.IKeeperService;
import com.wosai.sales.merchant.business.bean.StoreInfoRequest;
import com.wosai.sales.merchant.business.model.AppInfoModel;
import com.wosai.sales.merchant.business.service.common.CommonMerchantInfoService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.service.*;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.biz.store.StoreBiz;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.acquirer.CheckChangeAcquirerResp;
import com.wosai.upay.job.model.acquirer.MerchantTradeConfig;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.providers.DefaultChangeTradeParamsBiz;
import com.wosai.upay.job.providers.HxbProvider;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.enums.UseStatusEnum;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.service.IcbcService;
import com.wosai.upay.merchant.contract.service.PabService;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/12/24
 */
@Component
@Slf4j
public class HxbImportBiz {

    private static final String appidConfigList = "[\n" +
            "    {\n" +
            "        \"sub_appid\":\"wxd2f16468474f61b8\",\n" +
            "        \"type\":2\n" +
            "    },\n" +
            "    {\n" +
            "        \"sub_appid\":\"wx72534f3638c59073\",\n" +
            "        \"type\":1\n" +
            "    },\n" +
            "    {\n" +
            "        \"sub_appid\":\"wxccbcac9a3ece5112\",\n" +
            "        \"type\":2\n" +
            "    }\n" +
            "]";

    @Autowired
    private MerchantProviderParamsMapper mapper;

    @Autowired
    @Lazy
    private AcquirerService acquirerService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private UserFollowAndFansService userFollowAndFansService;

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    private WechatAuthBiz wechatAuthBiz;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    @Autowired
    private OnlineQueryService onlineQueryService;

    @Autowired
    private MerchantBankService merchantBankService;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    CcbDirectBiz ccbDirectBiz;

    @Autowired
    private MerchantBusinessLicenseService licenseService;
    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private RsaKeyService rsaKeyService;

    @Value("${ccb_dev_code}")
    private String ccbDevCode;

    @Value("${ccb_trade_combo_id}")
    private String ccbTradeComboId;

    private static final String CCB_CLEARING_NUMBER = "************";


    @Value("${crow.merchant_entry_id}")
    private String merchantEntryId;
    @Value("${hxb_dev_code}")
    private String hxbDevCode;
    @Value("${icbc_dev_code}")
    private String icbcDevCode;
    @Value("${pab_dev_code}")
    private String pabDevCode;

    @Value("${hxb_check_tag_1}")
    private String hxbTag1;
    @Value("${hxb_app_id}")
    private String hxbAppId;
    @Value("${icbc_app_id}")
    private String icbcAppId;
    @Value("${ccb_app_id}")
    private String ccbAppId;
    @Value("${pab_app_id}")
    private String pabAppId;
    @Value("${pab_trade_combo_id}")
    private long pabTradeComboId;
    @Value("${zjtlcb_trade_combo_id}")
    private long zjtlcbTradeComboId;

    @Value("${scan_order}")
    private String scanOrder;

    @Autowired
    CommonMerchantInfoService commonMerchantInfoService;
    @Autowired
    AopBiz aopBiz;
    @Autowired
    com.wosai.upay.core.service.StoreService cStoreService;
    @Autowired
    DirectStatusBiz directStatusBiz;
    private static final Integer BUSINESS_ID = 2;

    @Autowired
    TradeComboDetailService tradeComboDetailService;
    @Autowired
    BusinessLogBiz businessLogBiz;

    @Autowired
    IcbcService icbcService;
    @Autowired
    private AgentAppidBiz agentAppidBiz;
    @Autowired
    PabService pabService;

    @Autowired
    private ProviderTerminalMapper providerTerminalMapper;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private ProviderTerminalIdBiz providerTerminalIdBiz;


    public static final String HXB_WEIXIN_CHANNELNO = "397879995";

    public static final String HXB_ALI_CHANNELNO = "2088011691288213";

    public static final String HXB_UNIONPAY_CHANNELNO = "hxb";

    public static final String PAB_WEIXIN_CHANNELNO = "615951102";

    public static final String PROVIDER_MCH_ID = "provider_mch_id";
    public static final String DEVELOP_APP_ID = "develop_app_id";
    public static final String PROVIDER_TERM_ID = "provider_term_id";
    public static final String TRADEPARAMS = "tradeParams";
    public static final String VERSION = "version";
    public static final String VERSION_NO = "2.0";
    //服务商商户编号
    public static final String HX_PROVIDER_SERVICE_ID = "provider_service_id";

    @Value("${hxb_multi_trade}")
    private Long hxbMultiTrade;

    @Autowired
    OfflineMultiTradeMapper offlineMultiTradeMapper;

    @Autowired
    SubBizParamsBiz subBizParamsBiz;

    @Autowired
    HxbProvider hxbProvider;

    @Autowired
    ProviderTerminalBiz providerTerminalBiz;
    @Autowired
    private StoreBiz storeBiz;
    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SupportService supportService;

    @Autowired
    DefaultChangeTradeParamsBiz defaultChangeTradeParamsBiz;
    @Autowired
    private FeeRateService feeRateService;
    @Autowired
    private IKeeperService keeperService;
    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;
    @Autowired
    @Qualifier("appidConfigThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor appidConfigThreadPoolTaskExecutor;

    @Transactional
    public void importHxbParamsV2(HxbParamsV2 hxbParamsV2) {
        Map merchant = merchantService.getMerchantBySn(hxbParamsV2.getMerchant_sn());
        HxStoreCheckResp resp = storeBiz.haveDiffProvinceStore(MapUtil.getString(merchant,DaoConstants.ID));
        if (resp.isCheckResult()) {
            throw new CommonPubBizException(resp.getCheckMsg());
        }
        importHxbPrecheck(hxbParamsV2, merchant);

        PaywayInfo paywayInfo = getPaywayInfo(merchant);
        //使用一级服务商代替
        Map<String, String> map = applicationApolloConfig.getHxOrgNoAppId();
        String developAppId = Optional.ofNullable(map)
                .map(x -> x.get("parent"))
                .orElseThrow(() -> new ContractBizException("服务商appId未配置"));
        hxbParamsV2.setDevelop_app_id(developAppId);
        deleteParams(hxbParamsV2.getMerchant_sn(), ProviderEnum.PROVIDER_HXB.getValue());
        // 重复导入的时候删除已经存在的终端数据
        deleteExistTerminal(hxbParamsV2.getMerchant_sn());
        saveProviderParams(hxbParamsV2);
        saveAliParams(hxbParamsV2, UseStatusEnum.NO_USE.getValue(), paywayInfo);
        saveWxParams(hxbParamsV2, UseStatusEnum.NO_USE.getValue(), paywayInfo);
        saveUnionOpenParams(hxbParamsV2, UseStatusEnum.NO_USE.getValue());
        try {
            BankDirectApply applyBySnAndDevCode = bankDirectApplyMapper.getApplyBySnAndDevCode(hxbParamsV2.getMerchant_sn(), hxbDevCode);
            if (applyBySnAndDevCode != null) {
                bankDirectApplyMapper.deleteByPrimaryKey(applyBySnAndDevCode.getId());
            }
            //将api入网正在进行中的task任务设为失败
            List<ContractTask> tasks = contractTaskMapper.selectProcessContractTasks(hxbParamsV2.getMerchant_sn(), "hxb");
            if (!CollectionUtils.isEmpty(tasks)) {
                tasks.forEach(task -> {
                    contractTaskMapper.deleteByPrimaryKey(task.getId());
                    contractSubTaskMapper.deleteByPtaskId(task.getId());
                });
            }
            //保存申请单
            BankDirectApply bankDirectApply = new BankDirectApply()
                    .setMerchant_sn(hxbParamsV2.getMerchant_sn())
                    .setDev_code(hxbDevCode)
                    .setStatus(BankDirectApplyConstant.Status.APPLYING)
                    //固定值,见 AbstractBankDirectApplyBiz.getBankRef()
                    .setBank_ref(BankDirectApplyRefEnum.HXB.getValue())
                    .setForm_body(JSON.toJSONString(CollectionUtil.hashMap("merchant_config", Arrays.asList(
                                    CollectionUtil.hashMap("status", MerchantConfig.STATUS_OPENED,
                                            "rate", hxbParamsV2.getFee_rate(),
                                            "payway", String.valueOf(PaywayEnum.ALIPAY.getValue())),
                                    CollectionUtil.hashMap("status", MerchantConfig.STATUS_OPENED,
                                            "rate", hxbParamsV2.getFee_rate(),
                                            "payway", String.valueOf(PaywayEnum.WEIXIN.getValue())),
                                    CollectionUtil.hashMap("status", MerchantConfig.STATUS_OPENED,
                                            "rate", hxbParamsV2.getFee_rate(),
                                            "payway", String.valueOf(PaywayEnum.UNIONPAY.getValue()))),
                            "from", "import",
                            "operate", hxbParamsV2.getOperate(),
                            //默认套餐id,见 AbstractBankDirectAcquirerChangeBiz.getDefaultComboId()
                            "trade_combo_id", 179)))
                    //认为进件成功
                    .setProcess_status(BankDirectApplyConstant.ProcessStatus.CONTRACT_SUCCESS)
                    .setExtra(JSON.toJSONString(CollectionUtil.hashMap("provider", ProviderEnum.PROVIDER_HXB.getValue(), "acquire", AcquirerTypeEnum.HXB.getValue())));
            bankDirectApplyMapper.insertSelective(bankDirectApply);
            businessLogBiz.recordBankLog(bankDirectApply);
            //TODO 插入业务开通
            createOrUpdateBizOpenInfo(hxbParamsV2.getMerchant_sn(), AppInfoModel.STATUS_PENDING, hxbAppId, null);
        } catch (Exception e) {
            log.error("importHxbParamsV2 error:{}", e);
        }
        autoFollowUser(hxbParamsV2.getMerchant_sn());
    }


    /**
     * 重复导入的时候删除已经存在的终端数据
     *
     * @param merchantSn
     */
    public void deleteExistTerminal(String merchantSn) {
        //由于存在重复导入所以删除原有记录避免
        final ProviderTerminal providerTerminal = new ProviderTerminal();
        providerTerminal.setMerchant_sn(merchantSn);
        providerTerminal.setProvider(ProviderEnum.PROVIDER_HXB.getValue());
        //该通道下终端关联的记录
        final List<ProviderTerminal> terminalList = providerTerminalMapper.selectByCondition(providerTerminal);
        if (!CollectionUtils.isEmpty(terminalList)) {
            terminalList.forEach(info -> providerTerminalMapper.deleteByPrimaryKey(info.getId()));
        }
    }

    private void importHxbPrecheck(HxbParamsV2 hxbParams, Map merchant) {
        checkAcquirer(merchant, AcquirerTypeEnum.HXB.getValue(), AcquirerTypeEnum.HXB.getText());
        checkHolder(merchant, hxbParams.getHolder_name(), hxbParams.getIdentify_end());
        checkIndustry(merchant);
        checkTagsForHxb(merchant);
        checkFeeRate(hxbParams.getFee_rate(), 179L);
        //长度小于15位报错
        checkProviderMchIdLength(hxbParams.getProvider_mch_id());
    }

    private void checkTagsForHxb(Map merchant) {

        SingleTagEntityRecord record = onlineQueryService.getSingleTagEntityRecordById(merchantEntryId, hxbTag1, Arrays.asList(MapUtils.getString(merchant, DaoConstants.ID)), false);
        if (record != null && record.getTag() != null) {
            SingleVersionTagKV tag = record.getTag();
            String tagValue = tag.getValue() == null ? null : (String) tag.getValue();
            //认为被打上了标签, 校验失败. (标签配置的value需为[华夏额度包])
            if (WosaiStringUtils.equals(tagValue, "华夏额度包")) {
                throw new CommonInvalidParameterException("该商户已参加华夏绑卡活动，不允许提交银行进件");
            }

        }
    }

    private void deleteParams(String merchantSn, int provider) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(provider);
        mapper.deleteByExample(example);
    }

    /**
     * 保存收单机构参数
     *
     * @param hxbParams
     */
    private void saveProviderParams(HxbParams hxbParams) {
        Map tradeParams = CollectionUtil.hashMap(
                "provider_mch_id", hxbParams.getProvider_mch_id(),
                "develop_app_id", hxbParams.getDevelop_app_id(),
                "provider_term_id", hxbParams.getProvider_term_id(),
                VERSION, VERSION_NO,
                HX_PROVIDER_SERVICE_ID, applicationApolloConfig.getHxParentServerOrgno()
        );

        Map extra = CollectionUtil.hashMap(
                "tradeParams", tradeParams
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(hxbParams.getMerchant_sn())
                .setOut_merchant_sn(hxbParams.getMerchant_sn())
                .setChannel_no("hxb")
                .setParent_merchant_id(hxbParams.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_HXB.getValue())
                .setPayway(PaywayEnum.ACQUIRER.getValue())
                .setProvider_merchant_id(hxbParams.getProvider_mch_id())
                .setPay_merchant_id(hxbParams.getProvider_mch_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setUpdate_status(1)
                .setContract_rule("hxb")
                .setRule_group_id("hxb")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis());
        mapper.insertSelective(params);
    }

    /**
     * 保存支付宝参数
     *
     * @param hxbParams
     */
    private void saveAliParams(HxbParams hxbParams, Integer status, PaywayInfo paywayInfo) {
        Map tradeParams = CollectionUtil.hashMap(
                "provider_mch_id", hxbParams.getProvider_mch_id(),
                "develop_app_id", hxbParams.getDevelop_app_id(),
                VERSION, VERSION_NO,
                HX_PROVIDER_SERVICE_ID, applicationApolloConfig.getHxParentServerOrgno()
        );

        Map extra = CollectionUtil.hashMap(
                "tradeParams", tradeParams
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(hxbParams.getMerchant_sn())
                .setOut_merchant_sn(hxbParams.getMerchant_sn())
                .setChannel_no("2088011691288213")
                .setParent_merchant_id(hxbParams.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_HXB.getValue())
                .setPayway(PaywayEnum.ALIPAY.getValue())
                .setProvider_merchant_id(hxbParams.getProvider_mch_id())
                .setPay_merchant_id(hxbParams.getAlipay_sub_mch_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(status)
                .setUpdate_status(1)
                .setContract_rule("hxb-1028-2")
                .setRule_group_id("hxb")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis())
                .setMerchant_name(paywayInfo.getMerchantName())
                .setAli_mcc(paywayInfo.getMcc());
        mapper.insertSelective(params);
    }

    /**
     * 保存微信参数
     *
     * @param hxbParams
     */
    private void saveWxParams(HxbParams hxbParams, Integer status, PaywayInfo paywayInfo) {
        Map tradeParams = CollectionUtil.hashMap(
                "provider_mch_id", hxbParams.getProvider_mch_id(),
                "develop_app_id", hxbParams.getDevelop_app_id(),
                "weixin_sub_mch_id", hxbParams.getWeixin_sub_mch_id(),
                VERSION, VERSION_NO,
                HX_PROVIDER_SERVICE_ID, applicationApolloConfig.getHxParentServerOrgno()
        );


        Map extra = CollectionUtil.hashMap(
                "tradeParams", tradeParams,
                "appid_config_list", JSON.parseArray(appidConfigList, Map.class)
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(hxbParams.getMerchant_sn())
                .setOut_merchant_sn(hxbParams.getMerchant_sn())
                .setChannel_no("397879995")
                .setParent_merchant_id(hxbParams.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_HXB.getValue())
                .setPayway(PaywayEnum.WEIXIN.getValue())
                .setProvider_merchant_id(hxbParams.getProvider_mch_id())
                .setPay_merchant_id(hxbParams.getWeixin_sub_mch_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_SUCCESS)
                .setWeixin_sub_appid("wx72534f3638c59073")
                .setWeixin_sub_mini_appid("wxccbcac9a3ece5112")
                .setStatus(status)
                .setUpdate_status(1)
                .setAuth_status(PayMchAuthStatusEnum.YES.getValue())
                .setContract_rule("hxb-1028-3")
                .setRule_group_id("hxb")
                .setGold_status(2)
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis())
                .setMerchant_name(paywayInfo.getMerchantName())
                .setWx_settlement_id(paywayInfo.getWxSettlementId());
        mapper.insertSelective(params);
    }

    /**
     * 保存云闪付参数
     *
     * @param hxbParams
     */
    private void saveUnionOpenParams(HxbParams hxbParams, Integer status) {
        Map tradeParams = CollectionUtil.hashMap(
                "provider_mch_id", hxbParams.getProvider_mch_id(),
                "develop_app_id", hxbParams.getDevelop_app_id(),
                "provider_term_id", hxbParams.getProvider_term_id(),
                VERSION, VERSION_NO,
                HX_PROVIDER_SERVICE_ID, applicationApolloConfig.getHxParentServerOrgno()
        );

        Map extra = CollectionUtil.hashMap(
                "tradeParams", tradeParams
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(hxbParams.getMerchant_sn())
                .setOut_merchant_sn(hxbParams.getMerchant_sn())
                .setChannel_no(ChannelEnum.HX_BANK.getValue())
                .setParent_merchant_id(hxbParams.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_HXB.getValue())
                .setPayway(PaywayEnum.UNIONPAY.getValue())
                .setProvider_merchant_id(hxbParams.getProvider_mch_id())
                .setPay_merchant_id(hxbParams.getProvider_term_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(status)
                .setUpdate_status(1)
                .setContract_rule("hxb-1028-17")
                .setRule_group_id("hxb")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis());
        mapper.insertSelective(params);
    }

    public void importIcbcParams(IcbcParams icbcParams, boolean immediately) {
        Map merchant = merchantService.getMerchantBySn(icbcParams.getMerchant_sn());
        importIcbxPrecheck(icbcParams, merchant);

        PaywayInfo paywayInfo = getPaywayInfo(merchant);
        String privateKeyId = null;
        if (WosaiStringUtils.isNotEmpty(icbcParams.getPrivate_key())) {
            String privateKeyName = "工行" + icbcParams.getApp_id() + "私钥";
            privateKeyId = createRsaKey(privateKeyName, icbcParams.getPrivate_key());
        }
        String publicKeyId = null;
        if (WosaiStringUtils.isNotEmpty(icbcParams.getPublic_key())) {
            String publicKeyName = "工行" + icbcParams.getApp_id() + "公钥";
            publicKeyId = createRsaKey(publicKeyName, icbcParams.getPublic_key());
        }

        deleteParams(icbcParams.getMerchant_sn(), ProviderEnum.PROVIDER_ICBC.getValue());
        Map tradeParams = CollectionUtil.hashMap(
                "app_id", icbcParams.getApp_id(),
                "mer_id", icbcParams.getProvider_mch_id()
        );
        if (WosaiStringUtils.isNotEmpty(privateKeyId)) {
            tradeParams.put(TransactionParam.PRIVATE_KEY, privateKeyId);
        }
        if (WosaiStringUtils.isNotEmpty(publicKeyId)) {
            tradeParams.put(TransactionParam.PUBLIC_KEY, publicKeyId);
        }
        if (!icbcParams.getShould_send_sub_app_id()) {
            tradeParams.put(TransactionParam.WEIXIN_SUB_APP_ID, "");
            tradeParams.put(TransactionParam.WEIXIN_MINI_SUB_APP_ID, "");
        }
        // 如果是非一户一密模式，要校验授权状态
        boolean checkAuth = WosaiStringUtils.isEmpty(icbcParams.getPrivate_key()) && WosaiStringUtils.isEmpty(icbcParams.getPublic_key());
        icbcSaveProviderParams(icbcParams, tradeParams);
        icbcSaveAliParams(icbcParams, paywayInfo, tradeParams);
        icbcSaveWxParams(icbcParams, paywayInfo, tradeParams);
        icbcSaveUnionOpenParams(icbcParams, tradeParams);
        try {
            BankDirectApply applyBySnAndDevCode = bankDirectApplyMapper.getApplyBySnAndDevCode(icbcParams.getMerchant_sn(), icbcDevCode);
            if (applyBySnAndDevCode != null) {
                bankDirectApplyMapper.deleteByPrimaryKey(applyBySnAndDevCode.getId());
            }

            //保存申请单
            BankDirectApply bankDirectApply = new BankDirectApply()
                    .setMerchant_sn(icbcParams.getMerchant_sn())
                    .setDev_code(icbcDevCode)

                    .setStatus(BankDirectApplyConstant.Status.APPLYING)
                    //固定值,见 AbstractBankDirectApplyBiz.getBankRef()
                    .setBank_ref(BankDirectApplyRefEnum.ICBC.getValue())
                    .setForm_body(JSON.toJSONString(CollectionUtil.hashMap("merchant_config", Arrays.asList(
                                    CollectionUtil.hashMap("status", MerchantConfig.STATUS_OPENED,
                                            "rate", icbcParams.getFee_rate(),
                                            "payway", String.valueOf(PaywayEnum.ALIPAY.getValue())),
                                    CollectionUtil.hashMap("status", MerchantConfig.STATUS_OPENED,
                                            "rate", icbcParams.getFee_rate(),
                                            "payway", String.valueOf(PaywayEnum.WEIXIN.getValue())),
                                    CollectionUtil.hashMap("status", MerchantConfig.STATUS_OPENED,
                                            "rate", icbcParams.getFee_rate(),
                                            "payway", String.valueOf(PaywayEnum.UNIONPAY.getValue()))),
                            "from", "import",
                            "operate", icbcParams.getOperate(),
                            //默认套餐id,见 AbstractBankDirectAcquirerChangeBiz.getDefaultComboId()
                            "trade_combo_id", 180)))
                    //认为进件成功
                    .setProcess_status(BankDirectApplyConstant.ProcessStatus.CONTRACT_SUCCESS)
                    .setExtra(JSON.toJSONString(CollectionUtil.hashMap(
                            BankDirectApplyConstant.Extra.PROVIDER, ProviderEnum.PROVIDER_ICBC.getValue(),
                            BankDirectApplyConstant.Extra.ACQUIRE, AcquirerTypeEnum.ICBC.getValue(),
                            BankDirectApplyConstant.Extra.CHANGE_IMMEDIATELY, icbcParams.getChange_immediately(),
                            BankDirectApplyConstant.Extra.CHECK_WEIXIN_AUTH, checkAuth,
                            BankDirectApplyConstant.Extra.CHECK_ALI_AUTH, checkAuth)));
            if (Objects.equals(true, icbcParams.getChange_immediately())) {
                // 当是立即执行时，则将bankDirectApply的状态设置为微信商家认证成功
                bankDirectApply.setProcess_status(BankDirectApplyConstant.ProcessStatus.WX_AUTH_SUCCESS);
            }
            bankDirectApplyMapper.insertSelective(bankDirectApply);
            businessLogBiz.recordBankLog(bankDirectApply);
            //TODO 插入业务开通
            createOrUpdateBizOpenInfo(icbcParams.getMerchant_sn(), AppInfoModel.STATUS_PENDING, icbcAppId, null);
            boolean config = applicationApolloConfig.getIcbcWxConfig();
            if (config) {
                //异步配置微信支付目录
                appidConfigThreadPoolTaskExecutor.submit(() -> {
                    WeixinConfig configs = agentAppidBiz.getConfig(icbcParams.getMerchant_sn(), ProviderEnum.PROVIDER_ICBC.getValue(), "*********", icbcParams.getWeixin_sub_mch_id());
                    icbcService.wechatSubDevConfig(configs);
                });
            }
        } catch (Exception e) {
        }
        autoFollowUser(icbcParams.getMerchant_sn());
    }

    private String createRsaKey(String name, String rsaKey) {
        Map rsaKeyByDigest = rsaKeyService.getRsaKeyByDigest(com.wosai.data.util.StringUtil.md5(rsaKey));
        if (WosaiMapUtils.isNotEmpty(rsaKeyByDigest)) {
            return WosaiMapUtils.getString(rsaKeyByDigest, "id");
        } else {
            Map map = rsaKeyService.create(CollectionUtil.hashMap("name", name, "data", rsaKey));
            return WosaiMapUtils.getString(map, "id");
        }
    }

    /**
     * 获取支付源信息
     *
     * @param merchant
     * @return
     */
    public PaywayInfo getPaywayInfo(Map merchant) {
        WechatAuthBiz.WechatAuthNameAndSettId merchantNameAndSettlementId = wechatAuthBiz.getMerchantNameAndSettlementId(BeanUtil.getPropString(merchant, Merchant.SN));

        String industry = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);

        MapSqlParameterSource parameters = new MapSqlParameterSource()
                .addValue("industry", industry);
        Map industryCode = namedParameterJdbcTemplate.queryForMap("select * from industry_code_v2 where industry_id = :industry", parameters);


        return new PaywayInfo()
                .setMerchantName(merchantNameAndSettlementId.getMerchantName())
                .setWxSettlementId(merchantNameAndSettlementId.getSettlementId())
                .setMcc(BeanUtil.getPropString(industryCode, "wm_aly_mcc"));
    }

    public PaywayInfo getPaywayInfoForZjtlcb(Map merchant) {
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap(MerchantBankAccountPre.MERCHANT_ID, merchantId, MerchantBankAccountPre.DEFAULT_STATUS, MerchantBankAccountPre.DEFAULT_STATUS_TRUE));
        String merchantName;
        String settlementId;
        if (Objects.nonNull(listResult) && WosaiCollectionUtils.isNotEmpty(listResult.getRecords())) {
            WechatAuthBiz.WechatAuthNameAndSettId merchantNameAndSettlementId = wechatAuthBiz.getMerchantNameAndSettlementId(BeanUtil.getPropString(merchant, Merchant.SN));
            merchantName = merchantNameAndSettlementId.getMerchantName();
            settlementId = merchantNameAndSettlementId.getSettlementId();
        } else {
            MerchantBusinessLicenseInfo license = licenseService.getMerchantBusinessLicenseByMerchantId(merchantId, ccbDevCode);
            Integer type = license.getType();
            String industry = BeanUtil.getPropString(merchant, "industry");
            if (type.equals(BusinessLicenseTypeEnum.MICRO.getValue())) {
                merchantName = "商户_" + CommonUtil.substring(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_NAME), 50);
            } else {
                merchantName = CommonUtil.substring(BeanUtil.getPropString(license, MerchantBusinessLicence.NAME), 50);
            }
            settlementId = wechatAuthBiz.getSettlementIdIgnoreNotFound(industry, type, merchantName);
        }



        String industry = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);

        MapSqlParameterSource parameters = new MapSqlParameterSource()
                .addValue("industry", industry);
        Map industryCode = namedParameterJdbcTemplate.queryForMap("select * from industry_code_v2 where industry_id = :industry", parameters);


        return new PaywayInfo()
                .setMerchantName(merchantName)
                .setWxSettlementId(settlementId)
                .setMcc(BeanUtil.getPropString(industryCode, "wm_aly_mcc"));
    }

    @Data
    @Accessors(chain = true)
    public static class PaywayInfo {
        private String merchantName;

        private String wxSettlementId;

        private String mcc;

    }


    private void autoFollowUser(String merchantSn) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        AutoFollowRequest autoFollowRequest = new AutoFollowRequest();
        autoFollowRequest.setBusinessId(BUSINESS_ID);
        autoFollowRequest.setTargetMerchantId(MapUtils.getString(merchant, "id"));
        userFollowAndFansService.autoFollowUser(autoFollowRequest);
    }


    private void importIcbxPrecheck(IcbcParams icbcParams, Map merchant) {
        checkAcquirer(merchant, AcquirerTypeEnum.ICBC.getValue(), AcquirerTypeEnum.ICBC.getText());
        checkHolder(merchant, icbcParams.getHolder_name(), icbcParams.getIdentify_end());
        checkIndustry(merchant);
        checkFeeRate(icbcParams.getFee_rate(), 180L);
        checkPublicKeyAndPrivateKey(icbcParams);
    }

    private void checkPublicKeyAndPrivateKey(IcbcParams icbcParams) {
        if (WosaiStringUtils.isNotEmpty(icbcParams.getPrivate_key())) {
            try {
                RsaSignature.getPrivateKeyFromPKCS8(RsaSignature.KEY_RSA, icbcParams.getPrivate_key());
            } catch (Exception e) {
                throw new CommonPubBizException("商户公私钥不正确");
            }
        }
        if (WosaiStringUtils.isNotEmpty(icbcParams.getPublic_key())) {
            try {
                RsaSignature.getPublicKeyFromX509(RsaSignature.KEY_RSA, icbcParams.getPublic_key());
            } catch (Exception e) {
                throw new CommonPubBizException("商户公私钥不正确");
            }
        }
    }

    /**
     * 费率检查
     *
     * @param feeRate
     * @param comboId
     */
    public void checkFeeRate(String feeRate, long comboId) {
        List<TradeComboDetailResult> detailResults = tradeComboDetailService.listByComboId(comboId);
        detailResults.forEach(detail -> {
            String feeRateMax = detail.getFeeRateMax();
            String feeRateMin = detail.getFeeRateMin();
            boolean range = rangeInDefined(Double.valueOf(feeRate), Double.valueOf(feeRateMin), Double.valueOf(feeRateMax));
            if (!range) {
                throw new CommonInvalidParameterException("导入失败、手续费费率有误");
            }
        });
    }

    /**
     * 校验华夏商户号长度是否符合
     * @param providerMchId
     */
    private void checkProviderMchIdLength(String providerMchId) {
        if(StrUtil.trim(providerMchId).length() < 15) {
            throw new CommonInvalidParameterException("商户号长度不足15位");
        }
    }

    public void checkFeeRate(String feeRate, int payway, long comboId) {
        List<TradeComboDetailResult> detailResults = tradeComboDetailService.listByComboId(comboId);
        detailResults.stream().filter(r -> r.getPayway() == payway).forEach(detail -> {
            String feeRateMax = detail.getFeeRateMax();
            String feeRateMin = detail.getFeeRateMin();
            boolean range = rangeInDefined(Double.valueOf(feeRate), Double.valueOf(feeRateMin), Double.valueOf(feeRateMax));
            if (!range) {
                throw new CommonInvalidParameterException("导入失败、手续费费率有误");
            }
        });
    }


    /**
     * 判断当前值是否在某个区间
     *
     * @param current 当前值
     * @param min     最小
     * @param max     最大
     * @return
     */
    public static boolean rangeInDefined(Double current, Double min, Double max) {
        return Math.max(min, current) == Math.min(current, max);
    }

    private void checkAcquirer(Map merchant, String acquirer, String acquirerName) {
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        String merchantAcquirer = acquirerService.getMerchantAcquirer(merchantSn);
        if (Objects.equals(acquirer, merchantAcquirer)) {
            throw new CommonInvalidParameterException("商户当前已在【" + acquirerName + "】通道，重复导入请先切换到其他通道");
        }
        CheckChangeAcquirerResp resp = acquirerService.checkChangeAcquirer(merchantSn, acquirer);
        if (!resp.isCan_change()) {
            throw new ContractBizException(resp.getMsg());
        }
    }

    private void checkHolder(Map merchant, String holderName, String identifyEnd) {
        Map bankAccount = merchantService.getMerchantBankAccountByMerchantId(MapUtil.getString(merchant, "id"));
        String holder = MapUtil.getString(bankAccount, "holder");
        String identity = MapUtil.getString(bankAccount, "identity");
        if (!(WosaiStringUtils.equals(holderName, holder) && identity.endsWith(identifyEnd))) {
            throw new CommonInvalidParameterException("账户持有人信息不相符");
        }
    }

    private void checkIndustry(Map merchant) {
        String industry = BeanUtil.getPropString(merchant, Merchant.INDUSTRY, "");
        List<String> specialIndustry = applicationApolloConfig.getSpecialIndustry();
        if (specialIndustry.contains(industry)) {
            throw new CommonInvalidParameterException("特殊行业禁止导入银行通道");
        }
    }

    private void icbcSaveProviderParams(IcbcParams icbcParams, Map tradeParams) {
        Map extra = CollectionUtil.hashMap(
                "tradeParams", tradeParams
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(icbcParams.getMerchant_sn())
                .setOut_merchant_sn(icbcParams.getMerchant_sn())
                .setChannel_no("icbc")
                .setParent_merchant_id(icbcParams.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_ICBC.getValue())
                .setPayway(PaywayEnum.ACQUIRER.getValue())
                .setProvider_merchant_id(icbcParams.getProvider_mch_id())
                .setPay_merchant_id(icbcParams.getProvider_mch_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setUpdate_status(1)
                .setContract_rule("icbc")
                .setRule_group_id("icbc")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis());
        mapper.insertSelective(params);
    }

    private void icbcSaveAliParams(IcbcParams icbcParams, PaywayInfo paywayInfo, Map tradeParams) {
        Map extra = CollectionUtil.hashMap(
                "tradeParams", tradeParams
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(icbcParams.getMerchant_sn())
                .setOut_merchant_sn(icbcParams.getMerchant_sn())
                .setChannel_no("2088011691288213")
                .setParent_merchant_id(icbcParams.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_ICBC.getValue())
                .setPayway(PaywayEnum.ALIPAY.getValue())
                .setProvider_merchant_id(icbcParams.getProvider_mch_id())
                .setPay_merchant_id(icbcParams.getAlipay_sub_mch_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setUpdate_status(1)
                .setContract_rule("icbc-1030-2")
                .setRule_group_id("icbc")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis())
                .setMerchant_name(paywayInfo.getMerchantName())
                .setAli_mcc(paywayInfo.getMcc());
        mapper.insertSelective(params);
    }

    private void icbcSaveWxParams(IcbcParams icbcParams, PaywayInfo paywayInfo, Map tradeParams) {
        Map extra = CollectionUtil.hashMap(
                "tradeParams", tradeParams,
                "appid_config_list", JSON.parseArray(appidConfigList, Map.class)
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(icbcParams.getMerchant_sn())
                .setOut_merchant_sn(icbcParams.getMerchant_sn())
                .setChannel_no("*********")
                .setParent_merchant_id(icbcParams.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_ICBC.getValue())
                .setPayway(PaywayEnum.WEIXIN.getValue())
                .setProvider_merchant_id(icbcParams.getProvider_mch_id())
                .setPay_merchant_id(icbcParams.getWeixin_sub_mch_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_SUCCESS)
                .setWeixin_sub_appid("wx72534f3638c59073")
                .setWeixin_sub_mini_appid("wxccbcac9a3ece5112")
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setUpdate_status(1)
                .setAuth_status(PayMchAuthStatusEnum.YES.getValue())
                .setContract_rule("icbc-1030-3")
                .setRule_group_id("icbc")
                .setGold_status(2)
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis())
                .setMerchant_name(paywayInfo.getMerchantName())
                .setWx_settlement_id(paywayInfo.getWxSettlementId());
        mapper.insertSelective(params);
    }

    private void icbcSaveUnionOpenParams(IcbcParams icbcParams, Map tradeParams) {
        Map extra = CollectionUtil.hashMap(
                "tradeParams", tradeParams
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(icbcParams.getMerchant_sn())
                .setOut_merchant_sn(icbcParams.getMerchant_sn())
                .setChannel_no("icbc")
                .setParent_merchant_id(icbcParams.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_ICBC.getValue())
                .setPayway(PaywayEnum.UNIONPAY.getValue())
                .setProvider_merchant_id(icbcParams.getProvider_mch_id())
                .setPay_merchant_id(icbcParams.getUnion_open_sub_mch_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setUpdate_status(1)
                .setContract_rule("icbc-1030-17")
                .setRule_group_id("icbc")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis());
        mapper.insertSelective(params);
    }

    public void fillPayAuthInfo(String merchantProviderParamsId) {
        MerchantProviderParams merchantProviderParams = mapper.selectByPrimaryKey(merchantProviderParamsId);

        MerchantProviderParams updateValue = new MerchantProviderParams()
                .setId(merchantProviderParamsId);
        if (Objects.equals(merchantProviderParams.getPayway(), PaywayEnum.ALIPAY.getValue()) &&
                WosaiStringUtils.isEmptyAny(merchantProviderParams.getMerchant_name(), merchantProviderParams.getAli_mcc())) {
            PaywayInfo paywayInfo = getPaywayInfo(merchantService.getMerchantBySn(merchantProviderParams.getMerchant_sn()));
            if (WosaiStringUtils.isEmpty(merchantProviderParams.getMerchant_name())) {
                updateValue.setMerchant_name(paywayInfo.getMerchantName());
            }
            if (WosaiStringUtils.isEmpty(merchantProviderParams.getAli_mcc())) {
                updateValue.setAli_mcc(paywayInfo.getMcc());
            }
            mapper.updateByPrimaryKeySelective(updateValue);
        } else if (Objects.equals(merchantProviderParams.getPayway(), PaywayEnum.WEIXIN.getValue()) &&
                WosaiStringUtils.isEmptyAny(merchantProviderParams.getMerchant_name(), merchantProviderParams.getWx_settlement_id())) {
            PaywayInfo paywayInfo = getPaywayInfo(merchantService.getMerchantBySn(merchantProviderParams.getMerchant_sn()));
            if (WosaiStringUtils.isEmpty(merchantProviderParams.getMerchant_name())) {
                updateValue.setMerchant_name(paywayInfo.getMerchantName());
            }
            if (WosaiStringUtils.isEmpty(merchantProviderParams.getWx_settlement_id())) {
                updateValue.setWx_settlement_id(paywayInfo.getWxSettlementId());
            }
            mapper.updateByPrimaryKeySelective(updateValue);
        }

    }

    /**
     * 导入华夏记录
     *
     * @param merchantSn
     * @param appStatus
     */
    public void createOrUpdateHxbBizOpenInfo(String merchantSn, Integer appStatus) {
        createOrUpdateBizOpenInfo(merchantSn, appStatus, hxbAppId, null);
    }

    /**
     * 导入工商记录
     *
     * @param merchantSn
     * @param appStatus
     */
    public void createOrUpdateIcbcBizOpenInfo(String merchantSn, Integer appStatus) {
        createOrUpdateBizOpenInfo(merchantSn, appStatus, icbcAppId, null);
    }


    /**
     * 用于银行直连线下导入记录后续状态变更
     *
     * @param merchantSn
     * @param devCode
     * @param applyStatus
     */
    public void createOrUpdateBizOpenInfo(String merchantSn, String devCode, Integer applyStatus) {
        //对应关系集合
        Map map = CollectionUtil.hashMap(hxbDevCode, hxbAppId, icbcDevCode, icbcAppId, pabDevCode, pabAppId,
                BankDirectApplyConstant.Status.SUCCESS, AppInfoModel.STATUS_SUCCESS, BankDirectApplyConstant.Status.FAIL, AppInfoModel.STATUS_FAIL);
        //获取数据
        String appId = BeanUtil.getPropString(map, devCode);
        Integer appStatus = MapUtils.getInteger(map, applyStatus);
        if (StringUtils.isBlank(appId) || Objects.isNull(appStatus)) {
            return;
        }
        //插入失败/成功记录
        createOrUpdateBizOpenInfo(merchantSn, appStatus, appId, null);
    }

    /**
     * 插入新纪录
     *
     * @param merchantSn
     * @param appStatus
     */
    public void createOrUpdateBizOpenInfo(String merchantSn, Integer appStatus, String appId, String maintainUserId) {
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        if (StringUtils.isBlank(maintainUserId)) {
            try {
                maintainUserId = aopBiz.getMaintainUserId(merchantId);
            } catch (Exception exception) {
                log.info("createBizOpenInfo商户号:{},找不到商户维护人", merchantSn);
                return;
            }
        }
        PageInfo pageInfo = new PageInfo(1, 1);
        pageInfo.setOrderBy(Lists.newArrayList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC)));
        ListResult listResult = cStoreService.getSimpleStoreListByMerchantIdFromSlaveDb(merchantId, pageInfo);
        Map storeMap = listResult.getRecords().get(0);
        if (MapUtils.isEmpty(storeMap)) {
            log.info("createBizOpenInfo商户号:{},找不到门店", merchantSn);
            return;
        }
        final String storeId = BeanUtil.getPropString(storeMap, DaoConstants.ID);
        StoreInfoRequest storeInfoRequest = new StoreInfoRequest();
        storeInfoRequest.setStoreId(storeId);
        storeInfoRequest.setMerchantId(merchantId);
        storeInfoRequest.setPlatform("excel导入");
        storeInfoRequest.setPlatformVersion("excel导入");
        storeInfoRequest.setPlatformDevice("excel导入");
        storeInfoRequest.setUserId(maintainUserId);
        storeInfoRequest.setKeeperId(maintainUserId);
        storeInfoRequest.setAppId(appId);
        storeInfoRequest.setAppStatus(appStatus);
        storeInfoRequest.setNeedAuditMerchant(0);
        storeInfoRequest.setNeedAuditStore(0);
        storeInfoRequest.setIsNew(2);
        storeInfoRequest.setIsFirstStore(Boolean.TRUE);
        if (Objects.equals(appStatus, AppInfoModel.STATUS_PENDING)) {
            storeInfoRequest.setAppCtime(new Date());
            storeInfoRequest.setAppSubmitTime(new Date());
        } else if (Lists.newArrayList(AppInfoModel.STATUS_FAIL, AppInfoModel.STATUS_SUCCESS).contains(appStatus)) {
            storeInfoRequest.setAppConfirmTime(new Date());
        }
        commonMerchantInfoService.createBizOpenInfo(storeInfoRequest);
    }

    public void importCcb(String merchantSn, String feeRate) {
        importCcbPreCheck(merchantSn);
        BankDirectReq directReq = new BankDirectReq();
        directReq.setMerchant_sn(merchantSn);
        directReq.setDev_code(ccbDirectBiz.getDevCode());
        //组装form_body
        HashMap<String, Object> formBodyMap = Maps.newHashMap();
        //费率
        List<Map> configList = Lists.newArrayList(String.valueOf(PaywayEnum.ALIPAY.getValue()), String.valueOf(PaywayEnum.WEIXIN.getValue()), String.valueOf(PaywayEnum.UNIONPAY.getValue())).stream()
                .map(payWay -> CollectionUtil.hashMap("rate", feeRate, "payway", payWay))
                .collect(Collectors.toList());
        formBodyMap.put("merchant_config", configList);
        //套餐Id
        formBodyMap.put("trade_combo_id", ccbTradeComboId);
        //建行卡号
        formBodyMap.put("bank_pre_id", BeanUtil.getPropString(getBankAccount(merchantSn), DaoConstants.ID));
        // crm_uesrId
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String ccbUserId = getCcbUserId(MapUtils.getString(merchant, DaoConstants.ID));
        formBodyMap.put("crm_uesrId", ccbUserId);
        //线下导入标记
        formBodyMap.put("from", "batch_import");
        directReq.setForm_body(JSONObject.toJSONString(formBodyMap));
        ContractResponse contractResponse = ccbDirectBiz.applyBankDirect(directReq);
        if (!contractResponse.isSuccess()) {
            throw new ContractBizException(contractResponse.getMsg());
        }
        // crm记录
        createOrUpdateBizOpenInfo(merchantSn, AppInfoModel.STATUS_PENDING, ccbAppId, ccbUserId);
    }

    /**
     * 获取ccb原始维护人的用户id
     */
    public String getCcbUserId(String merchantId) {
        List<Map<String, Object>> keepers = keeperService.findKeepers(merchantId, Merchants.class, true);
        if (WosaiCollectionUtils.isEmpty(keepers)) {
            throw new CommonPubBizException("未查询到提交人信息: " + merchantId);
        }
        Map<String, Object> dataMap = keepers.get(0);
        return MapUtils.getString(dataMap, DaoConstants.ID);

    }

    /**
     * 导入商户是否满足要求1:绑定了建行卡
     *
     * @param merchantSn
     * @throws CommonPubBizException
     */
    public void importCcbPreCheck(String merchantSn) {
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        if (CollectionUtils.isEmpty(merchant)) {
            throw new CommonPubBizException("商户不存在");
        }
        boolean present = Optional.ofNullable(contractStatusMapper.selectByMerchantSn(merchantSn)).filter(contract -> Objects.equals(contract.getAcquirer(), AcquirerTypeEnum.CCB.getValue())).isPresent();
        if (present) {
            throw new CommonPubBizException("当前商户已在建行");
        }
        Map bankAccount = getBankAccount(merchantSn);
        if (CollectionUtils.isEmpty(bankAccount)) {
            throw new CommonPubBizException("没有找到绑定法人身份证的建设银行银行卡");
        }
        BankDirectApply apply = bankDirectApplyMapper.getApplyBySnAndDevCode(merchantSn, ccbDirectBiz.getDevCode());
        if (Objects.nonNull(apply) && !apply.getStatus().equals(BankDirectApplyConstant.Status.FAIL)) {
            throw new CommonPubBizException("存在进行中的任务");
        }
    }

    public Map getBankAccount(String merchantSn) {
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        final String merchantId = BeanUtil.getPropString(merchant, com.wosai.upay.common.dao.DaoConstants.ID);
        List<Map> bankAccounts = merchantBankService.findMerchantBankAccountPres(new PageInfo(1, 10),
                CollectionUtil.hashMap(MerchantBankAccountPre.MERCHANT_ID, merchantId)).getRecords();
        MerchantBusinessLicenseInfo licenseInfo = licenseService.getMerchantBusinessLicenseByMerchantId(merchantId, null);
        String clearingNumber = getClearingNumberByDevCode(ccbDevCode);
        if (WosaiStringUtils.isEmpty(clearingNumber)) {
            return Maps.newHashMap();
        }
        Map bankAccount = bankAccounts.stream().filter(account -> licenseInfo.getLegal_person_id_number().equals(BeanUtil.getPropString(account, MerchantBankAccountPre.IDENTITY)))
                .filter(account -> Objects.equals(BeanUtil.getPropString(account, MerchantBankAccountPre.CLEARING_NUMBER), clearingNumber))
                .findFirst().orElseGet(HashMap::new);
        return bankAccount;
    }

    private String getClearingNumberByDevCode(String devCode) {
        if (ccbDevCode.equals(devCode)) {
            return CCB_CLEARING_NUMBER;
        }
        return null;
    }


    /**
     * 线下导入华夏多业务参数
     *
     * @param hxbParamsV2
     */
    @Transactional(rollbackFor = Exception.class)
    public void importHxbMultiTradeParams(HxbParamsV2 hxbParamsV2) {
        //使用一级服务商AppId
        Map<String, String> map = applicationApolloConfig.getHxOrgNoAppId();
        String developAppId = Optional.ofNullable(map)
                .map(x -> x.get("parent"))
                .orElseThrow(() -> new ContractBizException("服务商appId未配置"));
        hxbParamsV2.setDevelop_app_id(developAppId);
        String merchantSn = hxbParamsV2.getMerchant_sn();
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        //前置校验
        preCheck(hxbParamsV2, merchant);
        //绑定华夏商家级别终端
        String terminalId = bindHxbMerchantLevelTerminal(hxbParamsV2);
        //保存导入参数
        OfflineMultiTrade offlineMultiTrade = saveDate(hxbParamsV2, terminalId);
        //当前移动支付使用华夏银行的话,将扫码点单设置为华夏银行参数
        String tradeAppId = scanOrder;
        //payWay对应的子商户号Map
        Map<String, String> payWaySubIdMap = ImmutableMap.of(String.valueOf(PaywayEnum.ALIPAY.getValue()), hxbParamsV2.getAlipay_sub_mch_id(),
                String.valueOf(PaywayEnum.WEIXIN.getValue()), hxbParamsV2.getWeixin_sub_mch_id(),
                String.valueOf(PaywayEnum.UNIONPAY.getValue()), hxbParamsV2.getProvider_term_id());
        //payWay对应的渠道号Map
        Map<String, String> payWayChannelNoMap = ImmutableMap.of(String.valueOf(PaywayEnum.ALIPAY.getValue()), HXB_ALI_CHANNELNO,
                String.valueOf(PaywayEnum.WEIXIN.getValue()), HXB_WEIXIN_CHANNELNO,
                String.valueOf(PaywayEnum.UNIONPAY.getValue()), HXB_UNIONPAY_CHANNELNO);

        setOfflineMultiTradeParam(merchantSn, tradeAppId,
                payWaySubIdMap,
                payWayChannelNoMap,
                AcquirerTypeEnum.HXB.getValue(),
                offlineMultiTrade, hxbParamsV2.getFee_rate());
    }


    @Transactional
    public void afterHxbChangeParams(String merchantSn) {
        //当前移动支付使用华夏银行的话,将扫码点单设置为华夏银行参数
        String tradeAppId = scanOrder;
        //获取保存的参数
        OfflineMultiTrade offlineMultiTrade = offlineMultiTradeMapper.selectByTradeAppId(merchantSn, tradeAppId, String.valueOf(ProviderEnum.PROVIDER_HXB.getValue()));
        if (Objects.isNull(offlineMultiTrade)) {
            return;
        }
        String formBody = offlineMultiTrade.getForm_body();
        HxbParamsV2 hxbParamsV2 = JSONObject.parseObject(formBody, HxbParamsV2.class);
        //payWay对应的子商户号Map
        Map<String, String> payWaySubIdMap = ImmutableMap.of(String.valueOf(PaywayEnum.ALIPAY.getValue()), hxbParamsV2.getAlipay_sub_mch_id(),
                String.valueOf(PaywayEnum.WEIXIN.getValue()), hxbParamsV2.getWeixin_sub_mch_id(),
                String.valueOf(PaywayEnum.UNIONPAY.getValue()), hxbParamsV2.getProvider_term_id());
        //payWay对应的渠道号Map
        Map<String, String> payWayChannelNoMap = ImmutableMap.of(String.valueOf(PaywayEnum.ALIPAY.getValue()), HXB_ALI_CHANNELNO,
                String.valueOf(PaywayEnum.WEIXIN.getValue()), HXB_WEIXIN_CHANNELNO,
                String.valueOf(PaywayEnum.UNIONPAY.getValue()), HXB_UNIONPAY_CHANNELNO);


        setOfflineMultiTradeParam(merchantSn, tradeAppId,
                payWaySubIdMap,
                payWayChannelNoMap,
                AcquirerTypeEnum.HXB.getValue(),
                offlineMultiTrade, hxbParamsV2.getFee_rate());
    }


    /**
     * 设置华夏银行多业务参数
     *
     * @param merchantSn     商户号
     * @param tradeAppId     业务方ID
     * @param payWaySubIdMap 商户号
     */
    private void setOfflineMultiTradeParam(String merchantSn,
                                           String tradeAppId,
                                           Map<String, String> payWaySubIdMap,
                                           Map<String, String> payWayChannelNoMap,
                                           String acquirer,
                                           OfflineMultiTrade offlineMultiTrade,
                                           String feeRate) {
        //获取保存的参数
        if (Objects.isNull(offlineMultiTrade)) {
            return;
        }
        int provider = Integer.valueOf(offlineMultiTrade.getProvider());
        ContractStatus contractStatus = Optional.ofNullable(contractStatusMapper.selectByMerchantSn(merchantSn))
                .orElseGet(ContractStatus::new);
        //当前移动支付不在华夏
        if (!Objects.equals(contractStatus.getAcquirer(), acquirer)) {
            return;
        }

        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map<String, Object> extraMap = offlineMultiTrade.getExtraMap();
        //支付方式和基础交易参数的Map
        Map<String, Map<String, String>> payWayParamMap = (Map<String, Map<String, String>>) MapUtils.getMap(extraMap, TRADEPARAMS);
        //将华夏扫码点单参数写入merchant_app_config
        payWaySubIdMap.forEach((k, v) -> {
            String channelNo = payWayChannelNoMap.get(k);
            String agentName = agentAppidBiz.getAgentName(Integer.valueOf(k), ProviderEnum.PROVIDER_HXB.getValue(), channelNo, BeanUtil.getPropString(merchant, Merchant.CITY));
            if (StringUtil.empty(agentName)) {
                log.error("setMultiTradeParam agent name is null merchantSn:{},channelNo:{}", merchantSn, channelNo);
                return;
            }
            MerchantTradeConfig byPassTradeConfig = new MerchantTradeConfig();
            byPassTradeConfig.setB2c_formal(0)
                    .setC2b_formal(0)
                    .setWap_formal(0)
                    .setMini_formal(0)
                    .setApp_formal(0)
                    .setExtend2_formal(0)
                    .setPayway(Integer.valueOf(k))
                    .setProvider(provider)
                    .setB2c_agent_name(agentName)
                    .setC2b_agent_name(agentName)
                    .setApp_agent_name(agentName)
                    .setWap_agent_name(agentName)
                    .setMini_agent_name(agentName)
                    .setH5_agent_name(agentName);
            //设置当前payWay对应的params参数
            Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName(SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY);
            String tradeParamKey = BeanUtil.getPropString(providerTradeParamsKey, String.valueOf(provider));
            byPassTradeConfig.setParams(CollectionUtil.hashMap(tradeParamKey, MapUtils.getMap(payWayParamMap, k)));
            //是否已经存在对应数据
            Map<String, Object> oldAppConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, Integer.valueOf(k), tradeAppId);
            log.info("setMultiTradeParam,merchantSn:{},oldAppConfig:{}", merchantSn, JSONObject.toJSONString(oldAppConfig));
            Map map = new MyObjectMapper().convertValue(byPassTradeConfig, Map.class);
            map.put(MerchantAppConfig.APP_ID, tradeAppId);
            map.put(MerchantAppConfig.MERCHANT_ID, merchantId);
            //补全交易参数信息
            if (MapUtils.isNotEmpty(oldAppConfig)) {
                map.put(DaoConstants.ID, BeanUtil.getPropString(oldAppConfig, DaoConstants.ID));
                //在MerchantAppConfig.PARAMS中追加最新的参数
                Map params = MapUtils.getMap(oldAppConfig, MerchantAppConfig.PARAMS);
                params.putAll(byPassTradeConfig.getParams());
                map.put(MerchantAppConfig.PARAMS, params);
                tradeConfigService.updateMerchantAppConfig(map);
            } else {
                tradeConfigService.createMerchantAppConfig(map);
            }
            //记录在本地sub_biz_params表中
            subBizParamsBiz.updateSubBizParamsForBankOffline(merchantSn, tradeAppId, provider, MapUtils.getString(payWaySubIdMap, k), Integer.valueOf(k));
        });
        //设置套餐
        applyComb(merchantSn, tradeAppId, feeRate);
        supportService.removeCachedParams(merchantSn);


    }

    /**
     * 为商户指定业务设置套餐
     *
     * @param merchantSn
     * @param tradeAppId
     * @param feeRate
     */
    private void applyComb(String merchantSn, String tradeAppId, String feeRate) {
        //设置套餐
        Map<String, String> applyFeeRateMap = CollectionUtil.hashMap(
                String.valueOf(PaywayEnum.ALIPAY.getValue()), feeRate,
                String.valueOf(PaywayEnum.WEIXIN.getValue()), feeRate,
                String.valueOf(PaywayEnum.UNIONPAY.getValue()), feeRate
        );
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(merchantSn)
                .setAuditSn(String.format("银行线下导入%s", subBizParamsBiz.getTradeAppNameById(tradeAppId)))
                .setTradeComboId(hxbMultiTrade)
                .setApplyFeeRateMap(applyFeeRateMap);
        try {
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
            supportService.removeCachedParams(merchantSn);
        } catch (Exception e) {
            log.error("银行线下导入设置多业务套餐异常,merchantSn:{},tradeAppId:{},comboId:{},异常信息:{}", merchantSn, tradeAppId, hxbMultiTrade, e);
        }
    }

    /**
     * 绑定华夏商家级别终端并返回生成的银联终端号
     *
     * @param hxbParamsV2 生成的银联终端号
     */
    private String bindHxbMerchantLevelTerminal(HxbParamsV2 hxbParamsV2) {
        String merchantSn = hxbParamsV2.getMerchant_sn();
        String termNo = providerTerminalIdBiz.getProviderTerminalIdByMerchantSn();

        ImmutableMap.of(String.valueOf(PaywayEnum.ALIPAY.getValue()), hxbParamsV2.getAlipay_sub_mch_id(),
                        String.valueOf(PaywayEnum.WEIXIN.getValue()), hxbParamsV2.getWeixin_sub_mch_id(),
                        String.valueOf(PaywayEnum.UNIONPAY.getValue()), hxbParamsV2.getProvider_term_id())
                .forEach((k, v) -> {
                            try {
                                AddTermInfoDTO infoDTO = new AddTermInfoDTO();
                                infoDTO.setDeviceId(termNo);
                                infoDTO.setSubMchId(v);
                                infoDTO.setMerchantSn(merchantSn);
                                //没有门店SN 默认取第一家门店
                                String storeSn = storeBiz.getFirstStoreSnByMerchantSn(merchantSn);
                                infoDTO.setStoreSn(storeSn);
                                //指定银行商户号
                                infoDTO.setProviderMerNo(hxbParamsV2.getProvider_mch_id());
                                hxbProvider.boundTerminal(infoDTO, Integer.valueOf(k), null);
                            } catch (Exception exception) {
                                log.error("bindHxbMerchantLevelTerminal merchantSn:{},payway:{},error:{}", merchantSn, k, exception);
                            }
                        }
                );
        //创建大终端
        providerTerminalBiz.bankMultiTradeMerchantTerminal(hxbParamsV2.getProvider_mch_id(), termNo,
                ProviderEnum.PROVIDER_HXB.getValue());
        return termNo;

    }


    /**
     * 组装并保存数据
     *
     * @param hxbParamsV2
     */
    private OfflineMultiTrade saveDate(HxbParamsV2 hxbParamsV2, String terminalId) {
        OfflineMultiTrade offlineMultiTrade = new OfflineMultiTrade();
        offlineMultiTrade.setMerchant_sn(hxbParamsV2.getMerchant_sn());
        offlineMultiTrade.setBank_merchant_sn(hxbParamsV2.getProvider_mch_id());
        offlineMultiTrade.setProvider(String.valueOf(ProviderEnum.PROVIDER_HXB.getValue()));
        offlineMultiTrade.setTrade_app_id(scanOrder);
        offlineMultiTrade.setForm_body(JSONObject.toJSONString(hxbParamsV2));
        offlineMultiTrade.setTerminal_id(terminalId);
        //将必要信息组装成交易参数
        //支付宝
        Map aliTradeParams = CollectionUtil.hashMap(
                PROVIDER_MCH_ID, hxbParamsV2.getProvider_mch_id(),
                DEVELOP_APP_ID, hxbParamsV2.getDevelop_app_id(),
                TransactionParam.ALIPAY_SUB_MCH_ID, hxbParamsV2.getAlipay_sub_mch_id(),
                VERSION, VERSION_NO,
                HX_PROVIDER_SERVICE_ID, applicationApolloConfig.getHxParentServerOrgno()
        );
        //微信
        Map WxTradeParams = CollectionUtil.hashMap(
                PROVIDER_MCH_ID, hxbParamsV2.getProvider_mch_id(),
                DEVELOP_APP_ID, hxbParamsV2.getDevelop_app_id(),
                TransactionParam.WEIXIN_SUB_MCH_ID, hxbParamsV2.getWeixin_sub_mch_id(),
                VERSION, VERSION_NO,
                HX_PROVIDER_SERVICE_ID, applicationApolloConfig.getHxParentServerOrgno()
        );
        //云闪付
        Map unionPayTradeParams = CollectionUtil.hashMap(
                PROVIDER_MCH_ID, hxbParamsV2.getProvider_mch_id(),
                DEVELOP_APP_ID, hxbParamsV2.getDevelop_app_id(),
                PROVIDER_TERM_ID, hxbParamsV2.getProvider_term_id(),
                VERSION, VERSION_NO,
                HX_PROVIDER_SERVICE_ID, applicationApolloConfig.getHxParentServerOrgno()
        );

        Map extra = CollectionUtil.hashMap(
                TRADEPARAMS, CollectionUtil.hashMap(String.valueOf(PaywayEnum.ALIPAY.getValue()), aliTradeParams,
                        String.valueOf(PaywayEnum.WEIXIN.getValue()), WxTradeParams,
                        String.valueOf(PaywayEnum.UNIONPAY.getValue()), unionPayTradeParams
                )
        );
        offlineMultiTrade.setExtra(JSONObject.toJSONString(extra));
        offlineMultiTradeMapper.insertSelective(offlineMultiTrade);
        return offlineMultiTrade;
    }


    /**
     * 华夏多业务前置校验
     *
     * @param hxbParamsV2
     * @param merchant
     */
    public void preCheck(HxbParamsV2 hxbParamsV2, Map merchant) {
        String merchantSn = hxbParamsV2.getMerchant_sn();
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        MerchantBusinessLicenseInfo license = licenseService.getMerchantBusinessLicenseByMerchantId(merchantId, null);
        Integer type = license.getType();
        //入网银行的个体工商户
        if (!Objects.equals(type, BusinessLicenseTypeEnum.INDIVIDUAL.getValue())) {
            throw new ContractBizException("只支持个体工商户");
        }
        //商户是否间连扫码开通成功
        ContractStatus contractStatus = Optional.ofNullable(contractStatusMapper.selectByMerchantSn(merchantSn))
                .orElseGet(ContractStatus::new);
        if (!Objects.equals(contractStatus.getStatus(), ContractStatus.STATUS_SUCCESS)) {
            throw new ContractBizException("商户间连扫码未开通");
        }
        //扫码点单是否开通成功
        List<String> merchantAppOpenResults = defaultChangeTradeParamsBiz.getMerchantAppOpenResults(merchantId);
        boolean contains = merchantAppOpenResults.contains(scanOrder);
        if (!contains) {
            throw new ContractBizException("商户扫码点单未开通");
        }
        checkHolder(merchant, hxbParamsV2.getHolder_name(), hxbParamsV2.getIdentify_end());
        checkFeeRate(hxbParamsV2.getFee_rate(), hxbMultiTrade);
    }


    public void importPabParams(PabParam pabParam) {
        Map merchant = merchantService.getMerchantBySn(pabParam.getMerchant_sn());
        importpabPrecheck(pabParam, merchant);

        PaywayInfo paywayInfo = getPaywayInfo(merchant);
        deleteParams(pabParam.getMerchant_sn(), ProviderEnum.PROVIDER_PAB.getValue());
        pabSaveProviderParams(pabParam);
        pabSaveAliParams(pabParam, paywayInfo);
        pabSaveWxParams(pabParam, paywayInfo);
        pabSaveUnionOpenParams(pabParam);

        try {
            BankDirectApply applyBySnAndDevCode = bankDirectApplyMapper.getApplyBySnAndDevCode(pabParam.getMerchant_sn(), pabDevCode);
            if (applyBySnAndDevCode != null) {
                bankDirectApplyMapper.deleteByPrimaryKey(applyBySnAndDevCode.getId());
            }
            //保存申请单
            BankDirectApply bankDirectApply = new BankDirectApply()
                    .setMerchant_sn(pabParam.getMerchant_sn())
                    .setDev_code(pabDevCode)

                    .setStatus(20)
                    //固定值,见 AbstractBankDirectApplyBiz.getBankRef()
                    .setBank_ref(6)
                    .setForm_body(JSON.toJSONString(CollectionUtil.hashMap("merchant_config", Arrays.asList(
                            CollectionUtil.hashMap("status", 1,
                                    "rate", pabParam.getFee_rate(),
                                    "payway", "2"),
                            CollectionUtil.hashMap("status", 1,
                                    "rate", pabParam.getFee_rate(),
                                    "payway", "3"),
                            CollectionUtil.hashMap("status", 1,
                                    "rate", pabParam.getFee_rate(),
                                    "payway", "17")),
                            "from", "import",
                            "operate", pabParam.getOperate(),
                            //默认套餐id,见 AbstractBankDirectAcquirerChangeBiz.getDefaultComboId()
                            "trade_combo_id", pabTradeComboId)))
                    //直接保存一条成功的记录，为了切换后设置费率能拿到对应的值
                    .setProcess_status(50)
                    .setExtra(JSON.toJSONString(CollectionUtil.hashMap("provider", PayParamsModel.PROVIDER_PAB, "acquire", McConstant.ACQUIRER_PAB)));
            bankDirectApplyMapper.insertSelective(bankDirectApply);
            acquirerService.applyChangeAcquirer(pabParam.getMerchant_sn(), AcquirerTypeEnum.PAB.getValue());
            //配置微信支付目录
            WeixinConfig configs = agentAppidBiz.getConfig(pabParam.getMerchant_sn(), ProviderEnum.PROVIDER_PAB.getValue(), PAB_WEIXIN_CHANNELNO, pabParam.getWeixin_sub_mch_id());
            pabService.wechatSubDevConfig(configs);
            createOrUpdateBizOpenInfo(pabParam.getMerchant_sn(), AppInfoModel.STATUS_SUCCESS, pabAppId, null);
        } catch (Exception e) {
            log.error("参数导入异常 merchantSn: {}, {}", pabParam.getMerchant_sn(), e);
        }
        autoFollowUser(pabParam.getMerchant_sn());
    }

    private void importpabPrecheck(PabParam pabParam, Map merchant) {
        checkAcquirer(merchant, AcquirerTypeEnum.PAB.getValue(), AcquirerTypeEnum.PAB.getText());
        checkHolder(merchant, pabParam.getHolder_name(), pabParam.getIdentify_end());
        checkIndustry(merchant);
        checkFeeRate(pabParam.getFee_rate(), pabTradeComboId);
    }


    /**
     * 保存收单机构参数
     *
     * @param pabParam
     */
    private void pabSaveProviderParams(PabParam pabParam) {
        Map tradeParams = CollectionUtil.hashMap(
                "provider_mch_id", pabParam.getProvider_mch_id(),
                "b2c_terminal_id", pabParam.getProvider_term_id_b2c(),
                "other_terminal_id", pabParam.getProvider_term_id_c2b()
        );

        Map extra = CollectionUtil.hashMap(
                "pab_up_trade_params", tradeParams
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(pabParam.getMerchant_sn())
                .setOut_merchant_sn(pabParam.getMerchant_sn())
                .setChannel_no("pab")
                .setParent_merchant_id(pabParam.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_PAB.getValue())
                .setPayway(PaywayEnum.ACQUIRER.getValue())
                .setProvider_merchant_id(pabParam.getProvider_mch_id())
                .setPay_merchant_id(pabParam.getProvider_mch_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setUpdate_status(1)
                .setContract_rule("pab")
                .setRule_group_id("pab")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis());
        mapper.insertSelective(params);
    }


    private void pabSaveAliParams(PabParam pabParam, PaywayInfo paywayInfo) {
        Map tradeParams = CollectionUtil.hashMap(
                "provider_mch_id", pabParam.getProvider_mch_id(),
                "alipay_sub_mch_id", pabParam.getAlipay_sub_mch_id(),
                "b2c_terminal_id", pabParam.getProvider_term_id_b2c(),
                "other_terminal_id", pabParam.getProvider_term_id_c2b()
        );

        Map extra = CollectionUtil.hashMap(
                "pab_up_trade_params", tradeParams
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(pabParam.getMerchant_sn())
                .setOut_merchant_sn(pabParam.getMerchant_sn())
                .setChannel_no("2088011691288213")
                .setParent_merchant_id(pabParam.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_PAB.getValue())
                .setPayway(PaywayEnum.ALIPAY.getValue())
                .setProvider_merchant_id(pabParam.getProvider_mch_id())
                .setPay_merchant_id(pabParam.getAlipay_sub_mch_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setAuth_status(PayMchAuthStatusEnum.YES.getValue())
                .setUpdate_status(1)
                .setContract_rule("pab-1040-2")
                .setRule_group_id("pab")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis())
                .setMerchant_name(paywayInfo.getMerchantName())
                .setAli_mcc(paywayInfo.getMcc());
        mapper.insertSelective(params);
    }

    private void pabSaveWxParams(PabParam pabParam, PaywayInfo paywayInfo) {
        Map extra = CollectionUtil.hashMap(
                "pab_up_trade_params", CollectionUtil.hashMap(
                        PROVIDER_MCH_ID, pabParam.getProvider_mch_id(),
                        "weixin_sub_mch_id", pabParam.getWeixin_sub_mch_id(),
                        "b2c_terminal_id", pabParam.getProvider_term_id_b2c(),
                        "other_terminal_id", pabParam.getProvider_term_id_c2b()
                ),
                "appid_config_list", JSON.parseArray(appidConfigList, Map.class)
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(pabParam.getMerchant_sn())
                .setOut_merchant_sn(pabParam.getMerchant_sn())
                .setChannel_no(PAB_WEIXIN_CHANNELNO)
                .setParent_merchant_id(pabParam.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_PAB.getValue())
                .setPayway(PaywayEnum.WEIXIN.getValue())
                .setProvider_merchant_id(pabParam.getProvider_mch_id())
                .setPay_merchant_id(pabParam.getWeixin_sub_mch_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_SUCCESS)
                .setWeixin_sub_appid("wx72534f3638c59073")
                .setWeixin_sub_mini_appid("wxccbcac9a3ece5112")
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setUpdate_status(1)
                .setAuth_status(PayMchAuthStatusEnum.YES.getValue())
                .setContract_rule("pab-1040-3")
                .setRule_group_id("pab")
                .setGold_status(2)
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis())
                .setMerchant_name(paywayInfo.getMerchantName())
                .setWx_settlement_id(paywayInfo.getWxSettlementId());
        mapper.insertSelective(params);
    }

    private void pabSaveUnionOpenParams(PabParam pabParam) {
        Map extra = CollectionUtil.hashMap(
                "pab_up_trade_params", CollectionUtil.hashMap(
                        "provider_mch_id", pabParam.getProvider_mch_id(),
                        "b2c_terminal_id", pabParam.getProvider_term_id_b2c(),
                        "other_terminal_id", pabParam.getProvider_term_id_c2b()
                )
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(pabParam.getMerchant_sn())
                .setOut_merchant_sn(pabParam.getMerchant_sn())
                .setChannel_no("pab")
                .setParent_merchant_id(pabParam.getProvider_mch_id())
                .setProvider(ProviderEnum.PROVIDER_PAB.getValue())
                .setPayway(PaywayEnum.UNIONPAY.getValue())
                .setProvider_merchant_id(pabParam.getProvider_mch_id())
                .setPay_merchant_id(pabParam.getUnion_open_sub_mch_id())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setUpdate_status(1)
                .setContract_rule("pab-1040-17")
                .setRule_group_id("pab")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis());
        mapper.insertSelective(params);
    }


    @Transactional(rollbackFor = Exception.class)
    public void importZjtlcbParams(ZjtlcbParams zjtlcbParams) {
        // 1.1校验费率信息
        Map merchant = merchantService.getMerchantBySn(zjtlcbParams.getMerchantSn());
        checkFeeRate(zjtlcbParams.getAliFeeRate(), PaywayEnum.ALIPAY.getValue(), zjtlcbTradeComboId);
        checkFeeRate(zjtlcbParams.getWxFeeRate(), PaywayEnum.WEIXIN.getValue(), zjtlcbTradeComboId);
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(zjtlcbParams.getMerchantSn());
        // 如果为空，则表明可能是首次即开泰隆银行
        if (contractStatus == null) {
            ContractStatus insert = new ContractStatus()
                    .setMerchant_sn(zjtlcbParams.getMerchantSn())
                    .setAcquirer(AcquirerTypeEnum.ZJTLCB.getValue())
                    .setStatus(ContractStatus.STATUS_SUCCESS);
            contractStatusMapper.insertSelective(insert);
        } else {
            // 1.2判断该商户是否已经在泰隆银行了
            checkAcquirer(merchant, AcquirerTypeEnum.ZJTLCB.getValue(), AcquirerTypeEnum.ZJTLCB.getText());
        }
        // 2.保存参数&切换参数
        changeTradeParams(zjtlcbParams, merchant);
        // 4.设置费率套餐
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(zjtlcbParams.getMerchantSn())
                .setTradeComboId(zjtlcbTradeComboId)
                .setAuditSn("银行业务开通成功设置费率")
                .setApplyPartialPayway(Boolean.TRUE)
                .setApplyFeeRateMap(CollectionUtil.hashMap(
                        String.valueOf(PaywayEnum.ALIPAY.getValue()), zjtlcbParams.getAliFeeRate(),
                        String.valueOf(PaywayEnum.WEIXIN.getValue()), zjtlcbParams.getWxFeeRate(),
                        String.valueOf(PaywayEnum.UNIONPAY.getValue()), zjtlcbParams.getWxFeeRate()
                ));
        feeRateService.applyFeeRateOne(applyFeeRateRequest);
    }

    public void changeTradeParams(ZjtlcbParams zjtlcbParams, Map merchant) {
        try {
            doChangeTradeParams(zjtlcbParams, merchant);
        } catch (Exception e) {
            log.error("zjtlcb changeAcquirer error, rollback to source acquirer trade params {}", zjtlcbParams.getMerchantSn(), e);
            merchantProviderParamsDAO
                    .listByMerchantSnAndStatus(zjtlcbParams.getMerchantSn(), UseStatusEnum.IN_USE.getValue())
                    .stream()
                    .filter(t -> Objects.nonNull(t.getPayway()) && !Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                    .forEach(paramsDO -> {
                        try {
                            merchantProviderParamsService.setDefaultMerchantProviderParamsByTradeApp(paramsDO.getId(), null, "change failed rollback", subBizParamsBiz.getPayTradeAppId());
                        } catch (Exception exception) {
                            log.error("回滚交易参数失败, paramsId = {}", paramsDO.getId(), exception);
                        }
                    });
            throw new ContractBizException("切交易参数异常: " + e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doChangeTradeParams(ZjtlcbParams zjtlcbParams, Map merchant) {
        PaywayInfo paywayInfo = getPaywayInfoForZjtlcb(merchant);
        deleteParams(zjtlcbParams.getMerchantSn(), ProviderEnum.PROVIDER_ZJTLCB.getValue());

        zjtlcbSaveProviderParams(zjtlcbParams);
        MerchantProviderParams wxParams = zjtlcbSaveWxParams(zjtlcbParams, paywayInfo);
        MerchantProviderParams aliParams = zjtlcbSaveAliParams(zjtlcbParams, paywayInfo);
        MerchantProviderParams unionPayParams = zjtlcbSaveUnionParams(zjtlcbParams, paywayInfo);
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(zjtlcbParams.getMerchantSn());
        ContractStatus updateValue = new ContractStatus()
                .setId(contractStatus.getId())
                .setAcquirer(AcquirerTypeEnum.ZJTLCB.getValue());
        contractStatusMapper.updateByPrimaryKeySelective(updateValue);
        // 切参数
        defaultChangeTradeParamsBiz.changeTradeParams(wxParams, null, false, subBizParamsBiz.getPayTradeAppId());
        defaultChangeTradeParamsBiz.changeTradeParams(aliParams, null, false, subBizParamsBiz.getPayTradeAppId());
        defaultChangeTradeParamsBiz.changeTradeParams(unionPayParams, null, false, subBizParamsBiz.getPayTradeAppId());
    }

    /**
     * 保存收单机构参数
     *
     * @param zjtlcbParams 浙江泰隆银行
     */
    private void zjtlcbSaveProviderParams(ZjtlcbParams zjtlcbParams) {
        Map tradeParams = CollectionUtil.hashMap(
                TransactionParam.PROVIDER_MCH_ID, zjtlcbParams.getProviderMchId()
        );

        Map extra = CollectionUtil.hashMap(
                "zjtlcb_up_trade_params", tradeParams
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(zjtlcbParams.getMerchantSn())
                .setOut_merchant_sn(zjtlcbParams.getMerchantSn())
                .setChannel_no("zjtlcb")
                .setParent_merchant_id(zjtlcbParams.getProviderMchId())
                .setProvider(ProviderEnum.PROVIDER_ZJTLCB.getValue())
                .setPayway(PaywayEnum.ACQUIRER.getValue())
                .setProvider_merchant_id(zjtlcbParams.getProviderMchId())
                .setPay_merchant_id(zjtlcbParams.getProviderMchId())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setUpdate_status(1)
                .setContract_rule("zjtlcb")
                .setRule_group_id("zjtlcb")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis());
        mapper.insertSelective(params);
    }

    private MerchantProviderParams zjtlcbSaveUnionParams(ZjtlcbParams zjtlcbParams, PaywayInfo paywayInfo) {
        Map tradeParams = CollectionUtil.hashMap(
                TransactionParam.PROVIDER_MCH_ID, zjtlcbParams.getProviderMchId()
        );

        Map extra = CollectionUtil.hashMap(
                "zjtlcb_up_trade_params", tradeParams
        );

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setMerchant_sn(zjtlcbParams.getMerchantSn())
                .setOut_merchant_sn(zjtlcbParams.getMerchantSn())
                .setChannel_no("zjtlcb")
                .setParent_merchant_id(zjtlcbParams.getProviderMchId())
                .setProvider(ProviderEnum.PROVIDER_ZJTLCB.getValue())
                .setPayway(PaywayEnum.UNIONPAY.getValue())
                .setProvider_merchant_id(zjtlcbParams.getProviderMchId())
                .setPay_merchant_id(zjtlcbParams.getProviderMchId())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setUpdate_status(1)
                .setContract_rule("zjtlcb-1043-17")
                .setRule_group_id("zjtlcb")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis());
        mapper.insertSelective(params);
        return params;
    }

    private MerchantProviderParams zjtlcbSaveWxParams(ZjtlcbParams zjtlcbParams, PaywayInfo payWayInfo) {
        Map extra = CollectionUtil.hashMap(
                "zjtlcb_up_trade_params", CollectionUtil.hashMap(
                        TransactionParam.PROVIDER_MCH_ID, zjtlcbParams.getProviderMchId(),
                        TransactionParam.WEIXIN_SUB_MCH_ID, zjtlcbParams.getWeixinSubMchId()
                ),
                "appid_config_list", JSON.parseArray(appidConfigList, Map.class)
        );
        String paramsId = UUID.randomUUID().toString();

        MerchantProviderParams params = new MerchantProviderParams()
                .setId(paramsId)
                .setMerchant_sn(zjtlcbParams.getMerchantSn())
                .setOut_merchant_sn(zjtlcbParams.getMerchantSn())
                .setChannel_no("641700724")
                .setParent_merchant_id(zjtlcbParams.getProviderMchId())
                .setProvider(ProviderEnum.PROVIDER_ZJTLCB.getValue())
                .setPayway(PaywayEnum.WEIXIN.getValue())
                .setProvider_merchant_id(zjtlcbParams.getProviderMchId())
                .setPay_merchant_id(zjtlcbParams.getWeixinSubMchId())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_SUCCESS)
                .setWeixin_sub_appid("wx72534f3638c59073")
                .setWeixin_sub_mini_appid("wxccbcac9a3ece5112")
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setUpdate_status(1)
                .setAuth_status(PayMchAuthStatusEnum.YES.getValue())
                .setContract_rule("zjtlcb-1043-3")
                .setRule_group_id("zjtlcb")
                .setGold_status(2)
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis())
                .setMerchant_name(payWayInfo.getMerchantName())
                .setWx_settlement_id(payWayInfo.getWxSettlementId());
        mapper.insertSelective(params);
        return params;
    }

    private MerchantProviderParams zjtlcbSaveAliParams(ZjtlcbParams zjtlcbParams, PaywayInfo paywayInfo) {
        Map tradeParams = CollectionUtil.hashMap(
                TransactionParam.PROVIDER_MCH_ID, zjtlcbParams.getProviderMchId(),
                TransactionParam.ALIPAY_SUB_MCH_ID, zjtlcbParams.getAlipaySubMchId()
        );

        Map extra = CollectionUtil.hashMap(
                "zjtlcb_up_trade_params", tradeParams
        );

        String paramsId = UUID.randomUUID().toString();
        MerchantProviderParams params = new MerchantProviderParams()
                .setId(paramsId)
                .setMerchant_sn(zjtlcbParams.getMerchantSn())
                .setOut_merchant_sn(zjtlcbParams.getMerchantSn())
                .setChannel_no("2088011691288213")
                .setParent_merchant_id(zjtlcbParams.getProviderMchId())
                .setProvider(ProviderEnum.PROVIDER_ZJTLCB.getValue())
                .setPayway(PaywayEnum.ALIPAY.getValue())
                .setProvider_merchant_id(zjtlcbParams.getProviderMchId())
                .setPay_merchant_id(zjtlcbParams.getAlipaySubMchId())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setAuth_status(PayMchAuthStatusEnum.YES.getValue())
                .setUpdate_status(1)
                .setContract_rule("zjtlcb-1043-2")
                .setRule_group_id("zjtlcb")
                .setExtra(CommonUtil.map2Bytes(extra))
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis())
                .setMerchant_name(paywayInfo.getMerchantName())
                .setAli_mcc(paywayInfo.getMcc());
        mapper.insertSelective(params);
        return params;
    }


}
