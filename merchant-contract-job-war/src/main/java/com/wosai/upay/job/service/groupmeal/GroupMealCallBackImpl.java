package com.wosai.upay.job.service.groupmeal;


import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.mapper.BlueSeaTaskMapper;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.model.MchSnapshot;
import com.wosai.upay.job.payactivity.biz.AlipayUniversityBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@AutoJsonRpcServiceImpl
@Service
public class GroupMealCallBackImpl implements GroupMealCallBack {

    @Autowired
    private BlueSeaTaskMapper blueSeaTaskMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private AlipayUniversityBiz alipayUniversityBiz;

    @Override
    public boolean callBack(String changeOrderId, String shopId) {

        BlueSeaTask task = blueSeaTaskMapper.selectByChangeOrderId(changeOrderId);
        if (task != null) {
            MchSnapshot mchSnapshot = task.getMchSnapshot();
            String shopIdFromTask = mchSnapshot.getShopId();
            Map<String, String> fromBody = JSON.parseObject(task.getForm_body(), Map.class);

            if (WosaiStringUtils.equals(shopId, shopIdFromTask) && task.getType() == 5 && WosaiStringUtils.equals("group_meal", fromBody.get("merchant_type"))) {
                return true;
            }
        }

        return false;
    }

    @Override
    public String check(String from, String to) {

        String sql = "select * from bluesea_task where create_at >= ? and create_at <= ? and type = 5 and status = 19";


        List<BlueSeaTask> query = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(BlueSeaTask.class), from, to);
        log.info("本次查询到{}条符合条件的数据, 准备清洗", query.size());
        new Thread(new Runnable() {
            @Override
            public void run() {
                for (BlueSeaTask blueSeaTask : query) {

                    try {
                        alipayUniversityBiz.checkGroupMeal(blueSeaTask);
                        log.info("操作成功,未发生异常 商户id {} , 商户号 {}", blueSeaTask.getMerchant_id(), blueSeaTask.getMerchant_sn());
                    } catch (Exception e) {
                        log.error("校验商户code出现异常,任务id {} 商户id {} , 商户号 {}", blueSeaTask.getId(), blueSeaTask.getMerchant_id(), blueSeaTask.getMerchant_sn(), e);
                    }
                }
            }
        }).start();


        return "success";
    }
}
