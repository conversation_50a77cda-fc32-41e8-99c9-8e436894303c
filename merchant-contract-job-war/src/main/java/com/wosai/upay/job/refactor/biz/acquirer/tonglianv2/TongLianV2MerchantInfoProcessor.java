package com.wosai.upay.job.refactor.biz.acquirer.tonglianv2;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * 通联v2商户信息处理
 *
 * <AUTHOR>
 * @date 2024/7/25 17:37
 */
@Component
public class TongLianV2MerchantInfoProcessor {

    @Resource
    private ContractParamsBiz contractParamsBiz;

    @Resource(type = TongLianV2Service.class)
    private TongLianV2Service tongLianV2Service;

    @Resource
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    /**
     * 获取通联V2商户信息
     * todo map -> java bean (后期统一重构）
     *
     * @param merchantSn 商户号
     * @return 商户信息
     */
    public Optional<Map<String, Object>> getMerchantInfo(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Optional.empty();
        }
        try {
            MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
            TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByParams(acquirerParams, TongLianV2Param.class);
            ContractResponse contractResponse = tongLianV2Service.queryMerchantInfo(merchantSn, tongLianV2Param);
            if (contractResponse == null || !contractResponse.isSuccess() || contractResponse.getResponseParam() == null) {
                return Optional.empty();
            }
            return Optional.of(contractResponse.getResponseParam());
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    /**
     * 获取海科商户银行卡号
     *
     * @param merchantSn 商户号
     * @return 银行卡号
     */
    public Optional<String> getBankAccountNo(String merchantSn) {
        Optional<Map<String, Object>> merchantInfo = getMerchantInfo(merchantSn);
        return merchantInfo.map(stringObjectMap -> MapUtils.getString(stringObjectMap, "acctid"));
    }
}
