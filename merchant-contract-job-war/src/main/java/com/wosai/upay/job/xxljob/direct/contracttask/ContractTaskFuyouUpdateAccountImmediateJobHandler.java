package com.wosai.upay.job.xxljob.direct.contracttask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.AddAffectStatusSuccessTaskCountBiz;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.service.TaskResultServiceImpl;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.wosai.upay.job.providers.FuyouProvider.EFFECTIVE_ACNT;

/**
 * xxl_job_desc: ContractTask-富友换卡立即成功
 * 富友换卡立即成功
 * <AUTHOR>
 * @date 2025/4/27
 */
@Slf4j
@Component("ContractTaskFuyouUpdateAccountImmediateJobHandler")
public class ContractTaskFuyouUpdateAccountImmediateJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private FuyouService fuyouService;
    @Autowired
    private TaskResultServiceImpl taskResultService;
    @Autowired
    private AddAffectStatusSuccessTaskCountBiz addAffectStatusSuccessTaskCountBiz;
    @Autowired
    private ChatBotUtil chatBotUtil;

    private static final String FUYOU_MODIFY_SUCCESS = "04";

    private static final String FUYOU_MODIFY_FAIL = "00";

    @Override
    public String getLockKey() {
        return "ContractTaskFuyouUpdateAccountImmediateJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            Calendar calendar = Calendar.getInstance();

            // 设置为当天凌晨
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date todayNight = calendar.getTime();
            List<ContractSubTask> contractSubTaskList = contractSubTaskMapper.selectFuyouUpdateQueryTask(StringUtil.formatDate(todayNight.getTime()),
                    StringUtil.formatDate(new Date().getTime())
            );
            if (CollectionUtils.isEmpty(contractSubTaskList)) {
                return;
            }
            for (ContractSubTask subTask : contractSubTaskList) {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                Long subTaskId = subTask.getId();
                try {
                    MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
                    dto.createCriteria().andMerchant_snEqualTo(subTask.getMerchant_sn()).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue());
                    MerchantProviderParams params = merchantProviderParamsMapper.selectByExample(dto).get(0);
                    // 变更单id与富友商户号一致，则表示是富友4.2接口提交的变更，即updateMerchant，不需要查询变更单结果，接口成功即通过
                    if (params.getProvider_merchant_id().equals(subTask.getContract_id())) {
                        continue;
                    }
                    ContractResponse response = fuyouService.queryBusinessAuditStatus(subTask.getMerchant_sn(), params.getProvider_merchant_id(), subTask.getContract_id(), "RZ");
                    if (response.isSystemFail()) {
                        continue;
                    }
                    if (response.isBusinessFail()) {
                        Map reMsg = CollectionUtil.hashMap("channel", subTask.getChannel(), "message", response.getMessage(), "result", response.getMessage());
                        taskResultService.changeStatusAndResultV2(subTask.getP_task_id(), subTask.getId(), TaskStatus.FAIL.getVal(), JSON.toJSONString(reMsg), false);
                        continue;
                    }
                    if (response.isSuccess()) {
                        Map responseParam = response.getResponseParam();
                        if (FUYOU_MODIFY_FAIL.equals(BeanUtil.getPropString(responseParam, "modify_st"))) {
                            responseParam.put("result", BeanUtil.getPropString(responseParam, "modify_deal_msg"));
                            subTask.setResponse_body(JSONObject.toJSONString(responseParam));
                            subTask.setResult(BeanUtil.getPropString(responseParam, "modify_deal_msg"));
                            subTask.setStatus(TaskStatus.FAIL.getVal());
                            contractSubTaskMapper.updateByPrimaryKey(subTask);
                            taskResultService.changeStatusAndResultV2(subTask.getP_task_id(), subTask.getId(), TaskStatus.FAIL.getVal(), JSONObject.toJSONString(responseParam), false);
                        } else if (FUYOU_MODIFY_SUCCESS.equals(BeanUtil.getPropString(responseParam, "modify_st"))) {
                            if (Objects.equals(EFFECTIVE_ACNT, BeanUtil.getPropString(responseParam, "is_effective_acnt", ""))) {
                                subTask.setStatus(SubTaskStatus.SUCCESS.getVal());
                                contractSubTaskMapper.setEnableScheduleByDepId(subTask.getId());
                                contractSubTaskMapper.updateByPrimaryKey(subTask);
                                if (subTask.getStatus_influ_p_task() == 1) {
                                    addAffectStatusSuccessTaskCountBiz.addAffectStatusSuccessTaskCount(subTask.getP_task_id());
                                } else {
                                    continue;
                                }
                                taskResultService.changeStatusAndResultV2(subTask.getP_task_id(), subTask.getId(), contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id()).getStatus(), null, false);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("ContractTaskFuyouUpdateAccountJobHandler queryFuyouUpdateStatusImmediate error subTaskId:{}：", subTaskId, e);
                    chatBotUtil.sendMessageToContractWarnChatBot("queryFuyouUpdateStatusImmediate error " + subTaskId + ExceptionUtil.getThrowableMsg(e));
                }
            }
        } catch (Exception e) {
            log.error("ContractTaskFuyouUpdateAccountJobHandler queryFuyouUpdateStatusImmediate error：", e);
            chatBotUtil.sendMessageToContractWarnChatBot("queryFuyouUpdateStatusImmediate error" + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
