package com.wosai.upay.job.refactor.biz.acquirer.guotong;

import com.shouqianba.cua.enums.contract.AcquirerMerchantStatusEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.model.guotong.GuotongMerchantInfoModel;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/1/24
 */
@Service
public class GuotongAcquirerFacade extends AbstractAcquirerHandler {

    @Resource
    private GuotongMerchantContractProcessor guotongMerchantContractProcessor;


    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.GUOTONG;
    }


    @Override
    public boolean isBankCardConsistentWithSqb(String merchantSn) {
        return acquirerCommonTemplate.checkBankCardConsistentWithSqb(
                getTypeValue(), merchantSn,
                t -> guotongMerchantContractProcessor.getBankAccountNo(t).orElse(null),
                null
        );
    }

    /**
     * 获取商户所在收单机构的商户状态
     *
     * @param merchantSn 商户号
     * @return 商户状态
     */
    @Override
    public AcquirerMerchantStatusEnum getAcquirerMerchantStatus(String merchantSn) {
        Optional<GuotongMerchantInfoModel> merchantInfo = guotongMerchantContractProcessor.getMerchantInfo(merchantSn);
        if (!merchantInfo.isPresent()) {
            return AcquirerMerchantStatusEnum.CLOSE;
        } else {
            GuotongMerchantInfoModel guotongMerchantInfoModel = merchantInfo.get();
            return Objects.equals(guotongMerchantInfoModel.getActiveStatus(), "0")
                    ? AcquirerMerchantStatusEnum.NORMAL : AcquirerMerchantStatusEnum.CLOSE;
        }
    }
}
