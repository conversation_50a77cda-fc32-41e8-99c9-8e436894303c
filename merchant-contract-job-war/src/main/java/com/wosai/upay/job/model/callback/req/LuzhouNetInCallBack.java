package com.wosai.upay.job.model.callback.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 泸州银行方商户入网成功后，泸州银行使用该请求 请求我们配置的回调接口。
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LuzhouNetInCallBack extends LuzhouCallBackReqBasic {
    /**
     * 门店号，由泸州银行分配
     */
    private String storeNo;
    /**
     * 商户(渠道商,这里指我们sqb)对应的sm3key。由泸州银行分发
     */
    private String mchtPrivateKey;

}
