package com.wosai.upay.job.xxljob.direct.pendingtask;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.databus.event.merchant.config.FeeRateEvent;
import com.wosai.databus.event.merchant.config.Field;
import com.wosai.databus.event.merchant.config.MerchantAppConfigChangeEvent;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.consumer.DataSyncHandler;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.PendingTasksMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.PendingTasks;
import com.wosai.upay.job.model.DTSBean;
import com.wosai.upay.job.service.SelfHelpNetInEventService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * xxl_job_desc: PendingTaskAppOrNowCreateFeeRateEventJobHandler
 * <AUTHOR>
 * @date 2025/4/25
 */
@Slf4j
@Component("PendingTaskAppOrNowCreateFeeRateEventJobHandler")
public class PendingTaskAppOrNowCreateFeeRateEventJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private PendingTasksMapper pendingTasksMapper;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private SelfHelpNetInEventService selfHelpNetInEventService;
    @Autowired
    private DataSyncHandler dataSyncHandler;


    @Override
    public String getLockKey() {
        return "PendingTaskAppOrNowCreateFeeRateEventJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        Date searchDate = DateUtils.addDays(new Date(), -1);
        Date endDate = DateUtils.addMinutes(new Date(), -1);
        try {
            // 查询前一天的费率变更临时任务，生成进件任务
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
            searchDate = simpleDateFormat.parse(simpleDateFormat.format(searchDate));
            // 根据创建时间查询所需数据，每次只处理 PENDING_TASKS_PAGE_ZIZE 条数据
            List<PendingTasks> pendingTasksList = pendingTasksMapper.selectFeeRateAfterTargetDate(searchDate, endDate, param.getBatchSize());
            while (!CollectionUtils.isEmpty(pendingTasksList)) {
                for (PendingTasks pendingTasks : pendingTasksList) {
                    try {
                        if (ShutdownSignal.isShuttingDown()) {
                            return;
                        }
                        if (PendingTasks.TYPE_APP_FEERATE.equals(pendingTasks.getEvent_type())) {
                            dealAppFeeRateEvent(pendingTasks);
                        } else {
                            dealFeeRateEvent(pendingTasks);
                        }
                        updateResult(pendingTasks);
                    } catch (Exception e) {
                        log.error("createTasksByPendingTask req:{}", JSONObject.toJSONString(pendingTasks), e);
                        try {
                            pendingTasks.setStatus(PendingTasks.STATUS_FAIL);
                            pendingTasks.setResult(e.getMessage());
                            updateResult(pendingTasks);
                        } catch (Exception e1) {
                            log.error("createTasksByPendingTask update record error req:{}", JSONObject.toJSONString(pendingTasks), e1);
                        }
                    }
                }
                pendingTasksList = pendingTasksMapper.selectFeeRateAfterTargetDate(searchDate, endDate, param.getBatchSize());
            }
        } catch (Exception e) {
            log.error("executeTasksByPendingTaskSchedule", e);
            chatBotUtil.sendMessageToContractWarnChatBot("费率变更事件生成进件事件处理异常 " + ExceptionUtil.getThrowableMsg(e));
        }
    }

    private void updateResult(PendingTasks pendingTasks) {
        PendingTasks updateValue = new PendingTasks()
                .setId(pendingTasks.getId())
                .setStatus(pendingTasks.getStatus())
                .setResult(pendingTasks.getResult());
        pendingTasksMapper.updateByPrimaryKeySelective(updateValue);
    }

    private void dealAppFeeRateEvent(PendingTasks pendingTasks) {

        MerchantAppConfigChangeEvent feeRateEvent = JSONObject.toJavaObject(JSONObject.parseObject(pendingTasks.getEvent_msg()), MerchantAppConfigChangeEvent.class);
        int payway = feeRateEvent.getPayway();
        long seq = feeRateEvent.getSeq();
        String merchantId = BeanUtil.getPropString(feeRateEvent.getAfter(), "merchant_id");
        Map merchant = merchantService.getMerchant(merchantId);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        log.info("处理商户  :{} 费率变更消息,数据总线的seq为 :{}", merchantSn, seq);
        if (contractStatus == null || contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS) {
            log.info("商户{}未入网成功,不生成此次变更事件,数据总线的seq为:{}", merchantSn, seq);
            pendingTasks.setStatus(PendingTasks.STATUS_FAIL);
            pendingTasks.setResult("商户未入网成功,不生成此次变更事件");
            return;
        }
        Map<String, Object> message = new HashMap<>();
        message.put(DTSBean.KEY_TABLE_NAME, "merchant_config");
        Map<String, Object> dataChange = feeRateEvent.getAfter();
        List<Map> dataList = new ArrayList<>();
        dataList.add(dataChange);
        log.info("need to sync merchant {} fee data change to lkl,date change : {}", merchantSn, dataChange);
        selfHelpNetInEventService.saveSelfHelpNetInEvent(merchantSn, ContractEvent.OPT_TYPE_MERCHANT_FEERATE, message, dataList, null);
        pendingTasks.setStatus(PendingTasks.STATUS_SUCCESS);
        pendingTasks.setResult("成功");
    }

    private void dealFeeRateEvent(PendingTasks pendingTasks) {
        FeeRateEvent feeRateEvent = JSONObject.toJavaObject(JSONObject.parseObject(pendingTasks.getEvent_msg()), FeeRateEvent.class);
        int payway = feeRateEvent.getPayway();
        long seq = feeRateEvent.getSeq();
        String merchantId = feeRateEvent.getMerchantId();
        Map merchant = merchantService.getMerchant(merchantId);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        log.info("处理商户  :{} 费率变更消息,数据总线的seq为 :{}", merchantSn, seq);
        if (contractStatus == null || contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS) {
            log.info("商户{}未入网成功,不生成此次变更事件,数据总线的seq为:{}", merchantSn, seq);
            pendingTasks.setStatus(PendingTasks.STATUS_FAIL);
            pendingTasks.setResult("商户未入网成功,不生成此次变更事件");
            return;
        }
        Map<String, Object> message = new HashMap<>();
        message.put(DTSBean.KEY_TABLE_NAME, "merchant_config");
        Map<String, Object> dataChange = new HashMap<>();
        filterUnnormalNews(merchantSn, feeRateEvent.getB2cFeeRate(), dataChange, dataSyncHandler.merchantConfigChangeFields.get(0));
        filterUnnormalNews(merchantSn, feeRateEvent.getC2bFeeRate(), dataChange, dataSyncHandler.merchantConfigChangeFields.get(1));
        filterUnnormalNews(merchantSn, feeRateEvent.getWapFeeRate(), dataChange, dataSyncHandler.merchantConfigChangeFields.get(2));
        filterUnnormalNews(merchantSn, feeRateEvent.getMiniFeeRate(), dataChange, dataSyncHandler.merchantConfigChangeFields.get(3));
        Map judgeMsg = dataSyncHandler.needToSyncMerchantFeerateChange(payway, dataChange, dataSyncHandler.merchantConfigChangeFields);
        log.info("need to sync merchant {} fee data change to lkl,date change : {}", merchantSn, dataChange);
        selfHelpNetInEventService.saveSelfHelpNetInEvent(merchantSn, ContractEvent.OPT_TYPE_MERCHANT_FEERATE, message, (List) judgeMsg.get("dataChangeList"), null);
        pendingTasks.setStatus(PendingTasks.STATUS_SUCCESS);
        pendingTasks.setResult("成功");
    }

    private void filterUnnormalNews(String merchantSn, Field field, Map<String, Object> dataChange, String merchantConfigChangeField) {
        log.info("商户  :{} 变更消息，变更前值：{} ，当前值 :{}", merchantSn, field.getPre(), field.getCur());
        dataChange.put(field.getClass().getName(), field.getClass().getName());
        if (!Objects.equals(field.getCur(), field.getPre())) {
            dataChange.put(merchantConfigChangeField, merchantConfigChangeField);

        }
    }
}
