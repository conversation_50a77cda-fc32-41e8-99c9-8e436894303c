package com.wosai.upay.job.model.DO;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class PendingTasks {

    public static final String TYPE_LKL_UPLOAD = "0";
    public static final String TYPE_LKL_WM = "1";
    public static final String TYPE_WECHAT_SUBMIT = "2";
    public static final String TYPE_FEERATE = "5";
    public static final String TYPE_APP_FEERATE = "7";
    public static final String TYPE_NOW_FEERATE = "8";
    public static final String TYPE_UPDATE_BANK_ACCOUNT_MESSAGE = "6";
    public static final String TYPE_BANK_POS_QUERY = "9";
    /**
     * 查询提现结果
     */
    public static final String TYPE_WITHDRAW_QUERY = "10";
    public static final int STATUS_PENDING = 0;
    public static final int STATUS_PROGRESS = 1;
    public static final int STATUS_SUCCESS = 5;
    public static final int STATUS_FAIL = 6;

    private Long id;

    private String sn;

    private String event_type;

    private Integer status;

    private Date create_at;

    private Date update_at;

    private Long version;

    private String event_msg;

    private String result;

}