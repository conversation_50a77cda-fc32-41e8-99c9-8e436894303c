package com.wosai.upay.job.xxljob.direct.multievent;

import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.MultiEventBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MultiProviderContractEventMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.service.TaskResultServiceImpl;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * xxl_job_desc: MultiEvent-设置事件为失败
 * 设置次通道未默认通道
 * 如果tonglian的task也失败了，lklv3的task也失败了 <p>
 * 两个线程同时进入到了{@link com.wosai.upay.job.service.TaskResultServiceImpl#getOtherOneContractTask(ContractTask, MultiProviderContractEvent)} 中，会发现另外一个任务是审核中，不会改变event和contract_status的状态<p>
 * 这个定时任务就是为了补偿这种情况
 * <AUTHOR>
 * @date 2025/4/27
 */
@Slf4j
@Component("MultiEventSetFailHandler")
public class MultiEventSetFailHandler extends AbstractDirectJobHandler {

    @Autowired
    private MultiProviderContractEventMapper multiEventMapper;
    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private MultiEventBiz multiEventBiz;
    @Autowired
    private TaskResultServiceImpl taskResultService;

    @Override
    public String getLockKey() {
        return "MultiEventSetFailHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            final List<MultiProviderContractEvent> events = multiEventMapper.selectProcessingNetInMultiEvents(StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
            if (CollectionUtils.isEmpty(events) || Objects.isNull(events.get(0))) {
                return;
            }
            for (MultiProviderContractEvent event : events) {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                try {
                    if (event.getSecondary_task_id() == null) {
                        continue;
                    }
                    // 如果两个都是失败，而event和contract_status是处理中,则重新处理一下
                    ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(event.getMerchant_sn());
                    ContractTask primaryTask = contractTaskMapper.selectByPrimaryKey(event.getPrimary_task_id());
                    ContractTask secondTask = contractTaskMapper.selectByPrimaryKey(event.getSecondary_task_id());
                    if (primaryTask.getStatus().equals(TaskStatus.FAIL.getVal()) && secondTask.getStatus().equals(TaskStatus.FAIL.getVal()) && contractStatus.getStatus() == ContractStatus.STATUS_PROCESS) {
                        log.info("{} multi_event 审核中, 两个进件任务均已经失败", event.getMerchant_sn());
                        taskResultService.setDefaultProvider(primaryTask, primaryTask.getResult(), TaskStatus.FAIL.getVal(), event);
                    }
                } catch (Exception e) {
                    log.error("setMultiEventToFail process error", e);
                    chatBotUtil.sendMessageToContractWarnChatBot(event.getMerchant_sn() + "双通道均失败补偿任务处理异常 " + ExceptionUtil.getThrowableMsg(e));
                }
            }
        } catch (Exception e) {
            log.error("setMultiEventToFail process error", e);
            chatBotUtil.sendMessageToContractWarnChatBot("双通道均失败补偿任务处理异常 " + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
