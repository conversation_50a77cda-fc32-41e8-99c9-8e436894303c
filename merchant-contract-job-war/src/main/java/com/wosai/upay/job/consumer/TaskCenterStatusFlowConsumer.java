package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.enums.contract.ScheduleStatusEnum;
import com.shouqianba.cua.utils.stream.ExtCollectors;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.taskCenter.*;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.dto.request.BankAutoChangeToThirdPartyReqDTO;
import com.wosai.upay.job.refactor.dao.InternalScheduleMainTaskDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.bo.BankAutoChangeToThirdPartyTaskFormBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleMainTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.refactor.service.impl.InterScheduleTaskServiceImpl;
import com.wosai.upay.job.refactor.utils.ThreadPoolWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务中心状态流转监听
 *
 * <AUTHOR>
 * @date 2024/6/20 14:41
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class TaskCenterStatusFlowConsumer extends AbstractDataBusConsumer {

    @Resource
    private InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private InterScheduleTaskServiceImpl interScheduleTaskService;

    @Resource
    private McProviderDAO mcProviderDAO;

    @Resource
    private ContractStatusMapper contractStatusMapper;

    /**
     * 处理任务状态流转
     *
     */
    @KafkaListener(topics = "callback_OSP_task-center-cua", containerFactory = "merchantActiveKafkaListenerContainerFactory")
    public void handleTaskStatusFlow(ConsumerRecord<String, GenericRecord> record) {
        handle(record);
    }


    /**
     * 各类event处理
     * <a href="https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********">详见文档查看各种类型</a>
     *
     * @param event 数据类型
     */
    @Override
    protected void doHandleEvent(AbstractEvent event) {
        if (event instanceof SpTaskCenterTaskSubmitEvent) {
            SpTaskCenterTaskSubmitEvent createEvent = (SpTaskCenterTaskSubmitEvent) event;
            String devConfigEventName = createEvent.getDevConfigEventName();
            if (StringUtils.equals(devConfigEventName, "bankErrorAutoChangeToThirdParty")) {
                // 防止超时消息重复投递
                ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> handlerBankRepairedEvent(createEvent));
            }
        }
    }

    private void handlerBankRepairedEvent(SpTaskCenterTaskSubmitEvent createEvent) {
        Long taskInstanceId = createEvent.getTaskInstanceId();
        BankAutoChangeToThirdPartyTaskFormBO formBO = JSON.parseObject(JSON.toJSONString(createEvent.getFormBusinessInfo()), BankAutoChangeToThirdPartyTaskFormBO.class);
        if (StringUtils.equals(formBO.getBankProcessResult(), "分行已解决")) {
            Optional<String> merchantSnOpt = merchantProviderParamsDAO.listByProviderMerchantId(formBO.getBankMerchantId()).stream().map(MerchantProviderParamsDO::getMerchantSn).findAny();
            if (!merchantSnOpt.isPresent()) {
                log.error("商户不存在,银行渠道商户号: {}", formBO.getBankMerchantId());
                return;
            }
            String merchantSn = merchantSnOpt.get();
            Map<String, InternalScheduleMainTaskDO> taskMap = internalScheduleMainTaskDAO
                    .listBySnAndType(merchantSn, InternalScheduleTaskTypeEnum.BANK_EXCEPTION_CHANGE_TO_THIRD_PARTY.getValue())
                    .stream()
                    .filter(t -> StringUtils.isNotBlank(t.getContext())
                            && Objects.equals(t.getStatus(), InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue()))
                    .collect(ExtCollectors.toMap(mainTaskDO -> {
                        BankAutoChangeToThirdPartyReqDTO changeReq = JSON.parseObject(mainTaskDO.getContext(), BankAutoChangeToThirdPartyReqDTO.class);
                        return changeReq.getTaskInstanceId();
                    }, t -> t, (k1, k2) -> k2));
            if (!taskMap.containsKey(taskInstanceId.toString())) {
                log.warn("银行处理成功，但是没有找到对应的银行切三方任务, 商户号: {}, 工单任务id: {}", merchantSn, taskInstanceId);
                return;
            }
            insertThirdPartyAutoChangeToBankTask(merchantSn, taskMap.get(taskInstanceId.toString()));
        }
    }


    public void insertThirdPartyAutoChangeToBankTask(String merchantSn, InternalScheduleMainTaskDO bankChangeToThirdMainTaskDO) {
        BankAutoChangeToThirdPartyReqDTO changeReq = JSON.parseObject(bankChangeToThirdMainTaskDO.getContext(), BankAutoChangeToThirdPartyReqDTO.class);
        Set<String> traAppIds = changeReq.getTraAppIds();
        String provider = changeReq.getProvider();
        Optional<McProviderDO> providerOpt = mcProviderDAO.getByProvider(provider);
        if (!providerOpt.isPresent()) {
            log.error("provider不存在, provider: {}", provider);
            return;
        }
        McProviderDO mcProviderDO = providerOpt.get();
        InternalScheduleMainTaskDO mainTaskDO = new InternalScheduleMainTaskDO();
        mainTaskDO.setMerchantSn(merchantSn);
        mainTaskDO.setType(InternalScheduleTaskTypeEnum.BANK_RECOVER_CHANGE_TO_ORIGINAL.getValue());
        mainTaskDO.setAffectStatusSubTaskNum(1);
        HashMap<String, Object> mainTaskContext = Maps.newHashMap();
        String acquirer = contractStatusMapper.selectByMerchantSn(merchantSn).getAcquirer();
        if (StringUtils.equals(acquirer, mcProviderDO.getAcquirer())) {
            log.warn("当前商户所在收单机构: {}」，不用发起切收单机构任务", mcProviderDO.getAcquirer());
            return;
        }
        mainTaskContext.put("originalAcquirer", acquirer);
        mainTaskContext.put("targetBank", mcProviderDO.getAcquirer());
        mainTaskContext.put("traAppIds", traAppIds);
        mainTaskDO.setContext(JSON.toJSONString(mainTaskContext));
        mainTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.NOT_CAN.getValue());
        interScheduleTaskService.insertTasks(mainTaskDO, null);
        List<InternalScheduleSubTaskDO> subTaskDOS = interScheduleTaskService.initSubTask(traAppIds.size(), mainTaskDO.getId(),
                InternalScheduleTaskTypeEnum.BANK_RECOVER_CHANGE_TO_ORIGINAL.getValue());
        List<String> traAppIdsList = traAppIds.stream().sorted().collect(Collectors.toList());
        for (int i = 0; i < traAppIdsList.size(); i++) {
            InternalScheduleSubTaskDO subTaskDO = subTaskDOS.get(i);
            HashMap<String, Object> subContext = Maps.newHashMap();
            subContext.put("targetBank", mcProviderDO.getAcquirer());
            subContext.put("traAppId", traAppIdsList.get(i));
            subTaskDO.setMerchantSn(mainTaskDO.getMerchantSn());
            subTaskDO.setContext(JSON.toJSONString(subContext));
            subTaskDO.setPriority(i);
            if (i == 0) {
                subTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
                subTaskDO.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
            }
            if (i > 0) {
                InternalScheduleSubTaskDO previousSubTask = subTaskDOS.get(i - 1);
                subTaskDO.setDependOnSubTaskId(previousSubTask.getId());
                subTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.NOT_CAN.getValue());
                subTaskDO.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.NO.getValue());
            }
        }
        mainTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
        interScheduleTaskService.batchUpdateTasks(mainTaskDO, subTaskDOS);
        log.info("银行处理成功，发起回切任务, 商户号: {}, 回切银行: {}", merchantSn, mcProviderDO.getAcquirer());
    }
}
