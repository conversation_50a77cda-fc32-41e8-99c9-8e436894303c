package com.wosai.upay.job.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.bankDirect.LzbDirectBiz;
import com.wosai.upay.job.enume.ProviderTerminalBindLevel;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.callback.req.LuzhouAuditPassCallBack;
import com.wosai.upay.job.model.callback.req.LuzhouNetInCallBack;
import com.wosai.upay.job.model.callback.res.LuzhouCallBackRes;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.providerterminal.ProviderTerminalAcquirerMerchantInfo;
import com.wosai.upay.job.model.providerterminal.ProviderTerminalAddRequest;
import com.wosai.upay.job.providers.LzbProvider;
import com.wosai.upay.job.refactor.dao.MerchantAcquirerInfoDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantAcquirerInfoDO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.LuZhouParam;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import static com.wosai.upay.job.model.CommonModel.PROVIDER_MCH_ID;
import static com.wosai.upay.merchant.contract.model.MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL;

@Component
@Slf4j
public class LuzhouCallBackService {
    private LuZhouParam luZhouParam;
    private final ContractParamsBiz baseBiz;
    private final ContractSubTaskMapper contractSubTaskMapper;
    private final QueryContractStatusHandler queryContractStatusHandler;
    private final LzbProvider lzbProvider;
    private final MerchantAcquirerInfoDAO merchantAcquirerInfoDAO;
    private final MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Lazy
    private final LzbDirectBiz lzbDirectBiz;
    private final BankDirectApplyMapper bankDirectApplyMapper;
    private final ProviderTerminalSerivce providerTerminalSerivce;

    private static final String PROVIDER_STORE_SN = "provider_store_sn";
    private static final String LZCCB_SM_2_PKEY = "lzccb_sm2_pkey";
    private static final String LZCCB_SM_3_KEY = "lzccb_sm3_key";

    public LuzhouCallBackService(ContractParamsBiz baseBiz,
                                 ContractSubTaskMapper contractSubTaskMapper,
                                 QueryContractStatusHandler queryContractStatusHandler,
                                 LzbProvider lzbProvider,
                                 MerchantAcquirerInfoDAO merchantAcquirerInfoDAO,
                                 @Autowired(required = false) LzbDirectBiz lzbDirectBiz,
                                 BankDirectApplyMapper bankDirectApplyMapper,
                                 ProviderTerminalSerivce providerTerminalSerivce,
                                 MerchantProviderParamsMapper merchantProviderParamsMapper) {
        this.baseBiz = baseBiz;
        this.contractSubTaskMapper = contractSubTaskMapper;
        this.queryContractStatusHandler = queryContractStatusHandler;
        this.lzbProvider = lzbProvider;
        this.merchantAcquirerInfoDAO = merchantAcquirerInfoDAO;
        this.lzbDirectBiz = lzbDirectBiz;
        this.bankDirectApplyMapper = bankDirectApplyMapper;
        this.providerTerminalSerivce = providerTerminalSerivce;
        this.merchantProviderParamsMapper = merchantProviderParamsMapper;
    }

    @PostConstruct
    public void init() {
        luZhouParam = baseBiz.buildContractParams("lzb", LuZhouParam.class);
    }

    public LuzhouCallBackRes handleAuditPassCallBack(LuzhouAuditPassCallBack callBack) {
        String contractId = callBack.getMerchantNo();
        if (StringUtils.isEmpty(contractId)) {
            log.error("泸州银行商户进件状态回调处理,contractId为空，商户号:{}，回调参数{}", callBack.getMerchantNo(), JSONObject.toJSONString(callBack));
            return LuzhouCallBackRes.success(luZhouParam.getSm2pkey());
        }
        log.info("泸州银行审核回调处理，商户门店号:{}，回调参数{}", contractId, JSONObject.toJSONString(callBack));
        ContractSubTask subTask = contractSubTaskMapper.selectByContractId(contractId);
        if (subTask == null) {
            log.error("泸州银行回调未找到对应的子任务,回调参数:{}", JSONObject.toJSONString(callBack));
            return LuzhouCallBackRes.fail("泸州银行回调未找到对应的子任务");
        }
        if (callBack.isAuditPass()) {
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask().setId(subTask.getId())
                            .setResponse_body(JSON.toJSONString(callBack))
                            .setResult("泸州银行审核通过，待商户微信/支付宝实名认证及短信签约")
            );
            final Long pTaskId = subTask.getP_task_id();
            final BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(pTaskId);
            lzbDirectBiz.recordViewProcess(apply, 11, new Date());
        } else {
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask().setId(subTask.getId())
                            .setStatus(TaskStatus.FAIL.getVal())
                            .setResponse_body(JSON.toJSONString(callBack))
                            .setResult("泸州银行审核不通过")
            );
            ContractResponse contractResponse = new ContractResponse();
            contractResponse.setCode(460);
            contractResponse.setMessage(callBack.getCheckMsg());
            contractResponse.setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(callBack), Map.class));
            this.doHandle(contractId, contractResponse);
        }
        return LuzhouCallBackRes.success(luZhouParam.getSm2pkey());
    }

    public void handleNetInCallBack(LuzhouNetInCallBack callBack, String privateKey) {
        String contractId = callBack.getMerchantNo();
        if (StringUtils.isEmpty(contractId)) {
            log.error("泸州银行商户进件状态回调处理,contractId为空，商户门店号:{}，回调参数{}", callBack.getStoreNo(), JSONObject.toJSONString(callBack));
            return;
        }
        log.info("泸州银行商户进件状态回调处理，商户门店号:{}，回调参数{}", callBack.getStoreNo(), JSONObject.toJSONString(callBack));
        ContractSubTask subTask = contractSubTaskMapper.selectByContractId(contractId);
        if (subTask == null) {
            log.error("泸州银行回调未找到对应的子任务,回调参数:{}", JSONObject.toJSONString(callBack));
            return;
        }
        if (subTask.getStatus() == 5) {
            log.info("该回调已成功处理,回调参数:{}", JSONObject.toJSONString(callBack));
            return;
        }
        this.checkIfSavedLuzhouMerchantInfo(callBack, subTask.getMerchant_sn(), contractId);
        handleQueryRes(callBack.getStoreNo(), subTask, contractId, callBack.getMchtPrivateKey(), privateKey);
    }

    private void handleQueryRes(String storeNo, ContractSubTask subTask, String contractId, String sm3PrivateKey, String sm2PrivateKey) {
        MerchantProviderParams merchantProviderParams = saveProviderParams(contractId, subTask, storeNo, sm3PrivateKey, sm2PrivateKey);
        providerTerminalSerivce.addProviderTerminal(new ProviderTerminalAddRequest()
                .setMerchantSn(subTask.getMerchant_sn())
                .setBindLevel(ProviderTerminalBindLevel.MERCHANT)
                .setAcquirerMerchantInfo(new ProviderTerminalAcquirerMerchantInfo()
                        .setMerchantSn(merchantProviderParams.getMerchant_sn())
                        .setAcquirerMerchantId(merchantProviderParams.getPay_merchant_id())
                        .setProvider(ProviderEnum.PROVIDER_LZB)));
        ContractResponse contractResponse = new ContractResponse();
        contractResponse.setCode(200);
        this.doHandle(contractId, contractResponse);
    }

    /**
     * 保存支付方式为0的提供商参数，并返回一个表示操作成功的进件响应对象。并且新增商户级别的终端
     * 这里入网回调默认就只能是成功的回调.所以手动设置一个成功的响应对象.
     *
     * @param contractId      进件ID
     * @param contractSubTask 进件子任务对象
     * @param storeNo         店铺编号
     * @param sm3PrivateKey   SM3私钥
     * @param sm2PrivateKey   SM2私钥
     * @return 表示操作成功的合同响应对象
     */
    public MerchantProviderParams saveProviderParams(String contractId, ContractSubTask contractSubTask, String storeNo, String sm3PrivateKey, String sm2PrivateKey) {
        String merchantSn = contractSubTask.getMerchant_sn();
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LZB.getValue());
        //providerParams表中有数据 说明已经处理过 直接返回
        if (Objects.nonNull(acquirerParams)) {
            return acquirerParams;
        }
        //payway==0
        long currentTimeMillis = System.currentTimeMillis();
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams();
        merchantProviderParams.setId(UUID.randomUUID().toString());
        merchantProviderParams.setMerchant_sn(contractSubTask.getMerchant_sn());
        merchantProviderParams.setOut_merchant_sn(contractId);
        merchantProviderParams.setChannel_no(luZhouParam.getChannel_no());
        merchantProviderParams.setParent_merchant_id(contractId);
        merchantProviderParams.setProvider(Integer.valueOf(luZhouParam.getProvider()));
        merchantProviderParams.setProvider_merchant_id(contractId);

        merchantProviderParams.setRule_group_id(contractSubTask.getRule_group_id());
        merchantProviderParams.setContract_rule(contractSubTask.getContract_rule());
        merchantProviderParams.setUpdate_status(1);

        merchantProviderParams.setPayway(PaywayEnum.ACQUIRER.getValue());
        merchantProviderParams.setPay_merchant_id(contractId);
        merchantProviderParams.setParams_config_status(PARAMS_CONFIG_STATUS_NULL);
        merchantProviderParams.setExtra(CommonUtil.map2Bytes(
                CollectionUtil.hashMap(
                        CommonModel.TRADE_PARAMS, CollectionUtil.hashMap(
                                PROVIDER_MCH_ID, contractId,
                                PROVIDER_STORE_SN, storeNo,
                                LZCCB_SM_2_PKEY, sm2PrivateKey,
                                LZCCB_SM_3_KEY, sm3PrivateKey
                        )
        )));
        merchantProviderParams.setStatus(1);
        merchantProviderParams.setCtime(currentTimeMillis);
        merchantProviderParams.setMtime(currentTimeMillis);
        merchantProviderParamsMapper.insertSelective(merchantProviderParams);
        return merchantProviderParams;
    }


    public void doHandle(String contractId, ContractResponse contractResponse) {
        if (StrUtil.isBlank(contractId)) {
            return;
        }
        //子任务
        ContractSubTask subTask = contractSubTaskMapper.selectByContractId(contractId);
        //处理状态
        HandleQueryStatusResp statusResp = lzbProvider.doHandleContractStatus(subTask, contractResponse);
        // 如果回调成功,则产生增网增终的任务
        if (!statusResp.isSuccess()) {
            log.error("泸州银行商户进件状态回调处理失败,contractId:{},回调参数:{}", contractId, JSONObject.toJSONString(contractResponse));
        }
        //任务状态
        queryContractStatusHandler.handTaskAndSubTask(subTask, statusResp);
    }

    private void checkIfSavedLuzhouMerchantInfo(LuzhouNetInCallBack callBack, String merchantSn, String contractId) {
        Optional<MerchantAcquirerInfoDO> merchantSnAndAcquirer = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(merchantSn, AcquirerTypeEnum.LZB.getValue());
        if (!merchantSnAndAcquirer.isPresent()) {
            throw new ContractBizException("商户" + merchantSn + "未保存泸州银行商户信息,回调执行失败");
        }
        if (!merchantSnAndAcquirer.get().getAcquirerMerchantId().equals(contractId)) {
            throw new ContractBizException("保存的泸州银行商户信息与回调不一致,回调执行失败.保存的id是: "
                    + merchantSnAndAcquirer.get().getAcquirerMerchantId() + "回调的id是: " + contractId);
        }
        MerchantAcquirerInfoDO merchantAcquirerInfoDO = merchantSnAndAcquirer.get();
        merchantAcquirerInfoDO.setMerchantInfo(JSONObject.toJSONString(callBack));
        merchantAcquirerInfoDAO.insertOrUpdateOne(merchantAcquirerInfoDO);
    }
}
