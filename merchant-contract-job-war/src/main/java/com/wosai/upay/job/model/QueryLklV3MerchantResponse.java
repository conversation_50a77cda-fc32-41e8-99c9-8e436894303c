package com.wosai.upay.job.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2022/6/21 14:12
 */
@NoArgsConstructor
@Data
public class QueryLklV3MerchantResponse {


    @JSONField(name = "ver")
    private String ver;
    @JSONField(name = "appid")
    private String appid;
    @JSONField(name = "respData")
    private RespData respData;
    @JSONField(name = "retCode")
    private String retCode;
    @JSONField(name = "retMsg")
    private String retMsg;
    @JSONField(name = "cmdRetCode")
    private String cmdRetCode;
    @JSONField(name = "reqId")
    private String reqId;
    @JSONField(name = "timestamp")
    private Long timestamp;

    @NoArgsConstructor
    @Data
    public static class RespData {
        @JSONField(name = "larIdType")
        private String larIdType;
        @JSONField(name = "merCupNo")
        private String merCupNo;
        @JSONField(name = "settlePeriod")
        private String settlePeriod;
        @JSONField(name = "merContactName")
        private String merContactName;
        @JSONField(name = "acctIdType")
        private String acctIdType;
        @JSONField(name = "merRegAddr")
        private String merRegAddr;
        @JSONField(name = "openningBankName")
        private String openningBankName;
        @JSONField(name = "merRegDistCode")
        private String merRegDistCode;
        @JSONField(name = "mccCode")
        private String mccCode;
        @JSONField(name = "larIdcard")
        private String larIdcard;
        @JSONField(name = "acctNo")
        private String acctNo;
        @JSONField(name = "orgCode")
        private String orgCode;
        @JSONField(name = "acctTypeCode")
        private String acctTypeCode;
        @JSONField(name = "merInnerNo")
        private String merInnerNo;
        @JSONField(name = "larName")
        private String larName;
        @JSONField(name = "merStatus")
        private String merStatus;
        @JSONField(name = "openningBankCode")
        private String openningBankCode;
        @JSONField(name = "orderNo")
        private String orderNo;
        @JSONField(name = "merSettleMode")
        private String merSettleMode;
        @JSONField(name = "merBizName")
        private String merBizName;
        @JSONField(name = "acctName")
        private String acctName;
        @JSONField(name = "acctIdcard")
        private String acctIdcard;
        @JSONField(name = "acctIdDt")
        private String acctIdDt;
        @JSONField(name = "larIdcardExpDt")
        private String larIdcardExpDt;
        @JSONField(name = "merRegName")
        private String merRegName;
        @JSONField(name = "merContactMobile")
        private String merContactMobile;
        @JSONField(name = "clearingBankCode")
        private String clearingBankCode;
        @JSONField(name = "feeData")
        private List<FeeData> feeData;
        @JSONField(name = "shopData")
        private List<ShopData> shopData;
        @JSONField(name = "termData")
        private List<TermData> termData;
        @JSONField(name = "limitData")
        private List<LimitData> limitData;

        @NoArgsConstructor
        @Data
        public static class FeeData {
            @JSONField(name = "feeRateTypeName")
            private String feeRateTypeName;
            @JSONField(name = "feeUpperAmtPcnt")
            private String feeUpperAmtPcnt;
            @JSONField(name = "feeRateStDt")
            private String feeRateStDt;
            @JSONField(name = "feeRateTypeCode")
            private String feeRateTypeCode;
            @JSONField(name = "feeLowerAmtPcnt")
            private String feeLowerAmtPcnt;
            @JSONField(name = "termNo")
            private String termNo;
            @JSONField(name = "feeRatePct")
            private String feeRatePct;
        }

        @NoArgsConstructor
        @Data
        public static class ShopData {
            @JSONField(name = "shopStatus")
            private String shopStatus;
            @JSONField(name = "shopContactMobile")
            private String shopContactMobile;
            @JSONField(name = "shopDistCode")
            private String shopDistCode;
            @JSONField(name = "shopAddr")
            private String shopAddr;
            @JSONField(name = "shopName")
            private String shopName;
            @JSONField(name = "shopId")
            private String shopId;
            @JSONField(name = "shopContactName")
            private String shopContactName;
        }

        @NoArgsConstructor
        @Data
        public static class TermData {
            @JSONField(name = "busiTypeName")
            private String busiTypeName;
            @JSONField(name = "termId")
            private String termId;
            @JSONField(name = "productCode")
            private String productCode;
            @JSONField(name = "productId")
            private Integer productId;
            @JSONField(name = "devSerialNo")
            private String devSerialNo;
            @JSONField(name = "busiStatus")
            private String busiStatus;
            @JSONField(name = "busiTypeCode")
            private String busiTypeCode;
            @JSONField(name = "shopId")
            private String shopId;
            @JSONField(name = "termNo")
            private String termNo;
            @JSONField(name = "activeNo")
            private String activeNo;
            @JSONField(name = "productName")
            private String productName;
        }

        @NoArgsConstructor
        @Data
        public static class LimitData {
            @JSONField(name = "limitTypeCodeName")
            private String limitTypeCodeName;
            @JSONField(name = "limitAmtPday")
            private String limitAmtPday;
            @JSONField(name = "limitAmtPmon")
            private String limitAmtPmon;
            @JSONField(name = "limitTypeCode")
            private String limitTypeCode;
            @JSONField(name = "limitAmtPcnt")
            private String limitAmtPcnt;
        }
    }
}
