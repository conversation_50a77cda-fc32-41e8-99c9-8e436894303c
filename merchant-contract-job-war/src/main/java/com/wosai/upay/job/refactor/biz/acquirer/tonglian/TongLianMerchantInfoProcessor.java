package com.wosai.upay.job.refactor.biz.acquirer.tonglian;

import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianParam;
import com.wosai.upay.merchant.contract.service.TongLianService;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * 通联商户信息处理
 *
 * <AUTHOR>
 * @date 2024/7/25 17:37
 */
@Component
public class TongLianMerchantInfoProcessor {

    @Resource
    private ParamContextBiz paramContextBiz;

    @Resource
    private ContractParamsBiz contractParamsBiz;

    @Resource(type = TongLianService.class)
    private TongLianService tongLianService;

    /**
     * 获取通联商户信息
     * todo map -> java bean (后期统一重构）
     *
     * @param merchantSn 商户号
     * @return 商户信息
     */
    public Optional<Map<String, Object>> getMerchantInfo(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Optional.empty();
        }
        try {
            Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN.getValue(), TongLianParam.class);
            final ContractResponse contractResponse = tongLianService.queryMerchant(contextParam, tongLianParam);
            if (contractResponse == null || !contractResponse.isSuccess() || contractResponse.getResponseParam() == null) {
                return Optional.empty();
            }
            return Optional.of(contractResponse.getResponseParam());
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    /**
     * 获取海科商户银行卡号
     *
     * @param merchantSn 商户号
     * @return 银行卡号
     */
    public Optional<String> getBankAccountNo(String merchantSn) {
        Optional<Map<String, Object>> merchantInfo = getMerchantInfo(merchantSn);
        return merchantInfo.map(stringObjectMap -> MapUtils.getString(stringObjectMap, "acctid"));
    }
}
