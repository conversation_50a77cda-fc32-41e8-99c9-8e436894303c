package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.CcbConfigBiz;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.ccbConfig.CcbConfig;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * xxl_job_desc: 银行直连-发起切换
 */
@Slf4j
@Component("BankDirectChangeAcquireJobHandler")
public class BankDirectChangeAcquireJobHandler extends AbstractBankDirectJobHandler {

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    private BankService bankService;
    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private AcquirerService acquirerService;
    @Autowired
    private CcbConfigBiz ccbConfigBiz;
    @Value("${bank_delay_minute_time}")
    private Integer bankDelayMinuteTime;

    @Override
    public String getLockKey() {
        return "BankDirectChangeAcquireJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            final List<BankDirectApply> applyList = bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(
                    Lists.newArrayList(BankDirectApplyConstant.ProcessStatus.WX_AUTH_SUCCESS),
                    StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()),
                    param.getBatchSize()
            );

            applyList.forEach(apply -> {
                try {
                    if (ShutdownSignal.isShuttingDown()) {
                        return;
                    }
                    //目标收单机构
                    final Map<String, Object> extraMap = apply.getExtraMap();
                    final String acquire = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.ACQUIRE);
                    if (Objects.equals(AcquirerTypeEnum.CCB.getValue(), acquire) && ccbChangeAcquirer(apply, extraMap)) {
                        return;
                    }
                    //银行卡前置校验
                    //构导致银行卡校验异常.将该跟任务推迟到下一个切换收单机构的周期
                    final String bankPreId = BeanUtil.getPropString(JSONObject.parseObject(apply.getForm_body(), Map.class), BankDirectApplyConstant.BANK_PRE_ID);
                    if (!StringUtils.isEmpty(bankPreId)) {
                        final Map checkResult = bankService.preCheckReplaceBankAccountForBankDirect(bankPreId);
                        if (!BeanUtil.getPropBoolean(checkResult, "allow")) {
                            if (Objects.equals(BeanUtil.getPropString(checkResult, "message"), "银行卡不存在")) {
                                //修改direct_status(失败)表状态和bank_direct_apply(status-30失败,processStatus-99失败)表状态
                                modifyStatus(apply.getMerchant_sn(), apply, BankDirectApplyConstant.DirectStatus.FAIL, BankDirectApplyConstant.Status.FAIL, "银行卡不存在", apply.getDev_code(), BankDirectApplyConstant.ProcessStatus.FAIL);
                                return;
                            }
                            //其他银行卡校验不通过时先延迟一下
                            bankDirectApplyMapper.updateByPrimaryKeySelective(new BankDirectApply().setId(apply.getId()).setResult(BeanUtil.getPropString(checkResult, "message")).setPriority(DateUtils.addMinutes(apply.getPriority(), bankDelayMinuteTime)));
                            return;
                        }
                    }
                    ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(apply.getMerchant_sn());
                    if (Objects.equals(contractStatus.getAcquirer(), acquire)) {
                        modifyStatus(apply.getMerchant_sn(), apply, BankDirectApplyConstant.DirectStatus.SUCCESS, BankDirectApplyConstant.Status.SUCCESS, "成功", apply.getDev_code(), BankDirectApplyConstant.ProcessStatus.SUCCESS);
                    } else {
                        acquirerService.applyChangeAcquirer(apply.getMerchant_sn(), acquire, Objects.equals(true, WosaiMapUtils.getBoolean(extraMap, BankDirectApplyConstant.Extra.CHANGE_IMMEDIATELY)));
                        //实名成功将process_status字段状态改成40-已发起切换收单机构
                        modifyBankDirectApply(apply, BankDirectApplyConstant.ProcessStatus.APPLYING_CHANGE_ACQUIRE);
                    }
                } catch (Exception e) {
                    //切换收单机构会校验银行卡状态,特别是对于富友换卡第二天才生效的收单机构,需要推迟一下
                    if ("商户银行卡正在变更中".equals(e.getMessage())) {
                        delayApply(apply, 30);
                        return;
                    }
                    //某个商户异常单独处理
                    log.error("银行直连商户:{}发起切换收单机构异常", apply.getMerchant_sn(), e);
                    chatBotUtil.sendMessageToContractWarnChatBot(String.format("银行直连商户:%s,发起切换收单机构异常:%s", apply.getMerchant_sn(), e.getMessage()));
                    //修改direct_status(失败)表状态和bank_direct_apply(status-30失败,processStatus-99失败)表状态
                    modifyStatus(apply.getMerchant_sn(), apply, BankDirectApplyConstant.DirectStatus.FAIL, BankDirectApplyConstant.Status.FAIL, e.getLocalizedMessage(), apply.getDev_code(), BankDirectApplyConstant.ProcessStatus.FAIL);
                    final String bankPreId = BeanUtil.getPropString(JSONObject.parseObject(apply.getForm_body(), Map.class), BankDirectApplyConstant.BANK_PRE_ID);
                    deletedMerchantBankAccountPre(bankPreId, "发起切换收单机构失败");
                }
            });
        } catch (Exception e) {
            log.error("changeAcquire exception", e);
            chatBotUtil.sendMessageToContractWarnChatBot(
                    String.format("银行直连商户切换收单机构异常:%s", e.getMessage())
            );
        }
    }

    private boolean ccbChangeAcquirer(BankDirectApply apply, Map<String, Object> extraMap) {
        //建行根据配置信息决定是否切换通道
        Map merchant = merchantService.getMerchantByMerchantSn(apply.getMerchant_sn());
        String districtCode = BeanUtil.getPropString(merchant, Merchant.DISTRICT_CODE);
        CcbConfig ccbConfig = ccbConfigBiz.getCcbConfigByDistrictCode(districtCode.substring(0, districtCode.length() - 2) + "00");
        if (ccbConfig == null) {
            ccbConfig = ccbConfigBiz.getCcbConfigByDistrictCode(districtCode.substring(0, districtCode.length() - 4) + "0000");
        }
        // 配置不自动切换收单机构，如果ccbConfig没配置无法入网，所以不考虑没配置的情况下是否切换的场景
        if (ccbConfig != null) {
            String from = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.DATA_FROM);
            if (!ccbConfig.getIs_auto_change() && !Objects.equals(from, "batch_import")) {
                //修改direct_status(2成功)表状态和bank_direct_apply(status-20成功,processStatus-50成功)表状态
                modifyStatus(apply.getMerchant_sn(), apply, BankDirectApplyConstant.DirectStatus.SUCCESS, BankDirectApplyConstant.Status.SUCCESS, "建行紧急需求缩短流程", apply.getDev_code(), BankDirectApplyConstant.ProcessStatus.SUCCESS);
                return true;
            }
            // 根据配置的推迟时间，将切通道任务推迟
            int delayDay = ccbConfig.getDelay_day();
            if (delayDay > 1) {
                Map extra = apply.getExtraMap();
                // 如果无delayDate，表示第一次扫描到，计算预计执行时间并将其保存
                if (StringUtil.empty((String) extra.get("delayDate"))) {
                    LocalDateTime delayDate = LocalDateTime.now().plusDays(delayDay - 1);
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:SS");
                    String delayDateStr = delayDate.format(formatter);
                    extra.put("delayDate", delayDateStr);
                    apply.setExtra(JSONObject.toJSONString(extra));
                    delayApplyByDelayDayAndExtra(apply, delayDay - 1);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 推迟任务
     *
     * @param apply
     * @param delayDay 分钟
     */
    private void delayApplyByDelayDayAndExtra(BankDirectApply apply, int delayDay) {
        final Long applyId = apply.getId();
        final BankDirectApply directApply = new BankDirectApply();
        directApply.setId(applyId);
        directApply.setPriority(DateUtils.addDays(new Date(), delayDay));
        directApply.setExtra(apply.getExtra());
        bankDirectApplyMapper.updateByPrimaryKeySelective(directApply);
    }
}