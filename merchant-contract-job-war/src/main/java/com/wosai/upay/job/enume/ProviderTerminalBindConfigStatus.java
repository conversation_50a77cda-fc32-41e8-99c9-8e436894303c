package com.wosai.upay.job.enume;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: ProviderTerminalBindConfigStatus
 * <AUTHOR>
 * @Date 2023/4/12 16:49
 **/

@Getter
@AllArgsConstructor
public enum ProviderTerminalBindConfigStatus {

    BIND_SUCCESS(1),
    BIND_FAIL(2),
    UNBIND(3),
    PENDING(4),
    DELETE(5);

    private Integer status;

    public static ProviderTerminalBindConfigStatus fromStatus(Integer val) {
        if (val == null) {
            return null;
        }
        for (ProviderTerminalBindConfigStatus status : ProviderTerminalBindConfigStatus.values()) {
            if (status.getStatus().equals(val)) {
                return status;
            }
        }
        return null;
    }

    public static Boolean isSuccess(ProviderTerminalBindConfigStatus status) {
        return BIND_SUCCESS.equals(status);
    }

    public static Boolean isSuccess(Integer status) {
        return isSuccess(fromStatus(status));
    }

}
