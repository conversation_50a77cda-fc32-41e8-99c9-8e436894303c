package com.wosai.upay.job.biz;

import com.google.common.hash.HashCode;
import com.google.common.hash.Hashing;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.merchant.contract.model.provider.LklV3WechatParam;
import com.wosai.upay.merchant.contract.model.provider.WanmaWeixinParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 进件拉卡拉商户 新老接口灰度
 * <AUTHOR>
 * @Date: 2022/3/14 11:42 上午
 */
@Component
@Slf4j
public class GrayMerchantSnBiz {
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private ContractParamsBiz contractParamsBiz;

    /**
     * 此商户是否走拉卡拉新接口
     * all_merchant_lkl_old true  走老接口
     * all_merchant_lkl_old false  all_merchant_lkl_new true 走新接口
     * all_merchant_lkl_old false  all_merchant_lkl_new false 灰度比例
     *
     * @param merchantSn 商户号
     * @return true 走新接口 false 老接口
     */
    public boolean newLklInterface(String merchantSn) {
        //是否全部走老接口
        boolean allMerchantOld = applicationApolloConfig.getAllMerchantLklOld();
        if (allMerchantOld) {
            return false;
        }
        //是否全部走新接口
        boolean allMerchantNew = applicationApolloConfig.getAllMerchantLklNew();
        if (allMerchantNew) {
            return true;
        }

        //白名单 走新接口
        String newInterfaceMerchantSns = applicationApolloConfig.getMerchantLklNew();
        if (newInterfaceMerchantSns.contains(merchantSn)) {
            return true;
        }
        //灰度比例
        int gray = applicationApolloConfig.getGrayScaleLklNew();
        //灰度比例>getHash()
        return gray > getHash(merchantSn);
    }


    /**
     * [0,99]
     *
     * @param merchantSn
     * @return
     */
    public static int getHash(String merchantSn) {
        String s = DigestUtils.md5Hex(merchantSn);
        HashCode hashCode = HashCode.fromString(s);
        return Hashing.consistentHash(hashCode, 100);
    }

    public LklV3WechatParam getLklV3WechatParam(WanmaWeixinParam wanMa) {
        LklV3WechatParam lklV3Wechat = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3WechatParam.class);

        lklV3Wechat.setProvider(wanMa.getProvider());
        lklV3Wechat.setContract_rule(wanMa.getContract_rule());
        lklV3Wechat.setRule_group_id(wanMa.getRule_group_id());
        lklV3Wechat.setPayway_channel_no(wanMa.getPayway_channel_no());
        lklV3Wechat.setChannel_no(wanMa.getChannel_no());
        lklV3Wechat.setMch_id(wanMa.getMch_id());
        lklV3Wechat.setWmOrgId(wanMa.getChannel_no());
        lklV3Wechat.setPayway(wanMa.getPayway());

        return lklV3Wechat;
    }

}
