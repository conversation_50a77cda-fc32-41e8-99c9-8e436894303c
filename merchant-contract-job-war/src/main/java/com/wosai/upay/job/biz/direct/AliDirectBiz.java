package com.wosai.upay.job.biz.direct;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alipay.api.request.AlipayOpenAgentOrderQueryRequest;
import com.alipay.api.response.AlipayOpenAgentOrderQueryResponse;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.pub.alipay.authinto.exception.ExceptionBase;
import com.wosai.pub.alipay.authinto.model.AlipayOAuth;
import com.wosai.pub.alipay.authinto.model.AlipayOAuthHistory;
import com.wosai.pub.alipay.authinto.model.Details;
import com.wosai.pub.alipay.authinto.model.Store;
import com.wosai.pub.alipay.authinto.service.AlipayStoreService;
import com.wosai.pub.alipay.authinto.service.StoreService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.enums.TradeComboStatusEnum;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.upay.bank.info.api.dto.IndustryConfigResDto;
import com.wosai.upay.bank.info.api.model.industryconfig.IndustryConfigQueryDTO;
import com.wosai.upay.bank.info.api.service.IndustryConfigService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.AliDirectApplyStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.AliDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
//import com.wosai.upay.job.mapper.IndustryCodeV2Mapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.AliDirectApply;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.model.direct.AliAuthorizeCallBack;
import com.wosai.upay.job.model.direct.AliDirectReq;
import com.wosai.upay.job.model.direct.AliDirectStatusCode;
import com.wosai.upay.job.model.direct.ApplyStatusResp;
import com.wosai.upay.job.model.directparams.AlipayV2DirectParams;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.directPay.AlipayOpenAgentOrderQueryReq;
import com.wosai.upay.merchant.contract.service.AliPayDirectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/12/22
 */
@Slf4j
@Component
public class AliDirectBiz {

    @Autowired
    private AliPayDirectService aliPayDirectService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    @Lazy
    private MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    private TradeComboDetailService comboDetailService;

    @Autowired
    private AlipayStoreService alipayStoreService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private FeeRateService feeRateService;

    @Autowired
    private AliDirectApplyMapper aliDirectApplyMapper;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    private DirectStatusBiz directStatusBiz;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private ErrorCodeManageBiz errorCodeManageBiz;

    @Autowired
    private IndustryConfigService industryConfigService;

    @Value("${ali.direct}")
    private String aliDirectDevCode;

    private static final long FOUR_HOURS = 4 * 60 * 60 * 1000;

    /**
     * 支付宝状态和内部申请单状态的映射关系
     * <p>
     * MERCHANT_INFO_HOLD 暂存，提交事务出现业务校验异常时，会暂存申请单信息，可以调用业务接口修正参数，并重新提交
     * <p>
     * MERCHANT_AUDITING 审核中，申请信息正在人工审核中
     * <p>
     * MERCHANT_CONFIRM 待商户确认，申请信息审核通过，等待联系人确认签约或授权
     * <p>
     * MERCHANT_CONFIRM_SUCCESS 商户确认成功，商户同意签约或授权
     * <p>
     * MERCHANT_CONFIRM_TIME_OUT 商户超时未确认，如果商户受到确认信息15天内未确认，则需要重新提交申请信息
     * <p>
     * MERCHANT_APPLY_ORDER_CANCELED 审核失败或商户拒绝，申请信息审核被驳回，或者商户选择拒绝签约或授权
     */
    public static final Map<String, AliDirectApplyStatus> ALI_STATUS_MAPPING = CollectionUtil.hashMap(
            "MERCHANT_INFO_HOLD", AliDirectApplyStatus.APPLY_REJECTED,
            "MERCHANT_AUDITING", AliDirectApplyStatus.IN_ALI_AUDITING,
            "MERCHANT_CONFIRM", AliDirectApplyStatus.WAIT_FOR_SIGN,
            "MERCHANT_CONFIRM_SUCCESS", AliDirectApplyStatus.WAIT_FOR_AUTHORIZATION,
            "MERCHANT_CONFIRM_TIME_OUT", AliDirectApplyStatus.APPLY_REJECTED,
            "MERCHANT_APPLY_ORDER_CANCELED", AliDirectApplyStatus.APPLY_REJECTED
    );

    /**
     * 提交申请之前进行校验
     *
     * @param merchantSn 商户号
     * @param devCode    应用标识
     */
    public void preCheck(String merchantSn, String devCode) {
        //1 获取对应的contract_task类型
        String contractTaskType = getTaskTypeByDevCode(devCode);
        if (WosaiStringUtils.isEmpty(contractTaskType)) {
            throw new CommonInvalidParameterException("该应用不可以开通支付宝直连");
        }
        //2 是否存在审核中或待审核的contract_task
        ContractTask contractTask = contractTaskMapper.selectProcessTaskByMerchantSn(merchantSn, contractTaskType);
        if (contractTask != null) {
            throw new CommonInvalidParameterException("存在未结束的直连申请任务");
        }
        //3 是否存在审核中的申请单
        AliDirectApply aliDirectApply = aliDirectApplyMapper.selectProcessApplyByMerchantSn(merchantSn);
        if (aliDirectApply != null) {
            throw new CommonInvalidParameterException("存在未完成的申请单");
        }
        //4 是否以已经申请成功
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode(merchantSn, devCode);
        if (directStatus != null && directStatus.getStatus() == DirectStatus.STATUS_SUCCESS) {
            throw new CommonInvalidParameterException("该商户已开通成功");
        }
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, devCode);
        if (merchantInfo == null) {
            throw new CommonInvalidParameterException(merchantSn + "不存在");
        }
        IndustryConfigQueryDTO dto = new IndustryConfigQueryDTO();
        dto.setIndustryId(merchantInfo.getIndustry());
        IndustryConfigResDto resDto = industryConfigService.queryIndustryConfigWithBeanResult(dto);
        if (Objects.isNull(resDto)) {
            throw new CommonInvalidParameterException(merchantInfo.getIndustry() + "不允许申请支付宝直连");
        }
        IndustryConfigResDto.Ali ali = resDto.getAli();
        if (Objects.isNull(ali)) {
            throw new CommonInvalidParameterException(merchantInfo.getIndustry() + "不允许申请支付宝直连");
        }
        IndustryConfigResDto.Ali.AliDirect aliDirect = ali.getAliDirect();
        if (Objects.isNull(aliDirect)) {
            throw new CommonInvalidParameterException(merchantInfo.getIndustry() + "不允许申请支付宝直连");
        }
        if (StringUtils.isEmpty(aliDirect.getBusinessCategory())) {
            throw new CommonInvalidParameterException(merchantInfo.getIndustry() + "不允许申请支付宝直连");
        }
//        IndustryCodeV2 industryCodeV2 = industryCodeV2Mapper.getIndustryCodeV2ByIndustryId(merchantInfo.getIndustry());
//        Map aliDirectIndustry = applicationApolloConfig.getAliDirectIndustry();
//        if (!aliDirectIndustry.containsKey(industryCodeV2.getWm_aly_mcc())) {
//            throw new CommonInvalidParameterException(merchantInfo.getIndustry() + "不允许申请支付宝直连");
//        }

    }

    private String getTaskTypeByDevCode(String devCode) {
        if (aliDirectDevCode.equals(devCode)) {
            return ProviderUtil.ALI_DIRECT_APPLY;
        }
        return null;
    }

    /**
     * 创建申请单 报备任务 报备总状态
     *
     * @param aliDirectReq
     * @param paramContext
     */
    @Transactional(rollbackFor = Exception.class)
    public void createTaskAndApplyAndStatus(AliDirectReq aliDirectReq, Map<String, Object> paramContext) {
        ContractTask contractTask = new ContractTask().setMerchant_sn(aliDirectReq.getMerchant_sn()).setMerchant_name(BeanUtil.getPropString(paramContext.get(ParamContextBiz.KEY_MERCHANT), Merchant.NAME))
                .setAffect_sub_task_count(0).setAffect_status_success_task_count(0).setEvent_context(JSON.toJSONString(paramContext)).setType(ProviderUtil.ALI_DIRECT_APPLY);
        //可以发送神策消息
        contractTaskBiz.insert(contractTask);
        AliDirectApply aliDirectApply = new AliDirectApply().setMerchant_sn(aliDirectReq.getMerchant_sn())
                .setTask_id(contractTask.getId()).setForm_body(JSON.toJSONString(aliDirectReq));
        aliDirectApplyMapper.insertSelective(aliDirectApply);
        directStatusBiz.createOrUpdateDirectStatus(aliDirectApply.getMerchant_sn(), aliDirectReq.getDev_code(), DirectStatus.STATUS_PENDING, null);
    }

    public ApplyStatusResp getLatestStatusFromAli(String merchantSn, String platform) {
        AliDirectApply aliDirectApply = aliDirectApplyMapper.selectLatestApplyByMerchantSn(merchantSn);
        AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse> response = aliPayDirectService.alipayOpenAgentOrderQuery(new AlipayOpenAgentOrderQueryReq().setBatchNo(aliDirectApply.getBatch_no()));
        AliDirectApplyStatus applyStatus = ALI_STATUS_MAPPING.get(response.getResp().getOrderStatus());
        if (applyStatus == null) {
            throw new CommonInvalidParameterException("查询支付宝返回未知状态");
        }
        //状态没变
        if (applyStatus.getVal().equals(aliDirectApply.getStatus())) {
            return getAliDirectContractMemo(contractTaskMapper.selectByPrimaryKey(aliDirectApply.getTask_id()), platform);
        }
        doAfterStatusChanged(response.getResp(), applyStatus, aliDirectApply, false);
        return getAliDirectContractMemo(contractTaskMapper.selectByPrimaryKey(aliDirectApply.getTask_id()), platform);
    }

    /**
     * 状态发生更改之后的操作,主动查询的结果，成功的状态在收到支付宝回调之后更改
     *
     * @param response           查询支付宝的结果
     * @param applyStatus        申请单状态
     * @param aliDirectApply     申请单
     * @param needUpdatePriority 是否需要更新priority，手动查询不用更新
     */
    @Transactional(rollbackFor = Exception.class)
    public void doAfterStatusChanged(AlipayOpenAgentOrderQueryResponse response, AliDirectApplyStatus applyStatus, AliDirectApply aliDirectApply, boolean needUpdatePriority) {
        AliDirectApply updateApply = new AliDirectApply().setId(aliDirectApply.getId());
        if (needUpdatePriority) {
            updateApply.setPriority(new Date(System.currentTimeMillis() + FOUR_HOURS));
        }
        ContractTask contractTask = new ContractTask().setId(aliDirectApply.getTask_id());
        //失败
        if (applyStatus.getVal().equals(AliDirectApplyStatus.APPLY_REJECTED.getVal())) {
            String rejectReason = response.getRejectReason();
            updateApply.setStatus(applyStatus.getVal()).setResult(rejectReason).setResponse_body(JSON.toJSONString(response));
            contractTask.setStatus(TaskStatus.FAIL.getVal())
                    .setResult(JSON.toJSONString(CollectionUtil.hashMap("channel", ProviderUtil.ALI_DIRECT, "message", rejectReason)));
            aliDirectApplyMapper.updateByPrimaryKeySelective(updateApply);
            contractTaskBiz.update(contractTask);
            directStatusBiz.createOrUpdateDirectStatus(aliDirectApply.getMerchant_sn(), aliDirectDevCode, DirectStatus.STATUS_BIZ_FAIL, rejectReason);
        }
        //待签约，返回值的签约链接设置进去，可能有用
        if (applyStatus.getVal().equals(AliDirectApplyStatus.WAIT_FOR_SIGN.getVal())) {
            updateApply.setStatus(applyStatus.getVal()).setSign_url(response.getConfirmUrl());
            aliDirectApplyMapper.updateByPrimaryKeySelective(updateApply);
            directStatusBiz.sendStatusChangeMessage(aliDirectApply.getMerchant_sn(), aliDirectDevCode, DirectStatus.STATUS_WAIT_FOR_SIGN, null);
        }
        //签约完成之后等待授权
        if (applyStatus.getVal().equals(AliDirectApplyStatus.WAIT_FOR_AUTHORIZATION.getVal())) {
            updateApply.setStatus(applyStatus.getVal());
            aliDirectApplyMapper.updateByPrimaryKeySelective(updateApply);
            directStatusBiz.sendStatusChangeMessage(aliDirectApply.getMerchant_sn(), aliDirectDevCode, DirectStatus.STATUS_IN_OPENING_PERMISSION, null);
        }
    }

    public ApplyStatusResp getAliDirectContractMemo(ContractTask contractTask, String platform) {
        if (contractTask == null) {
            return new ApplyStatusResp()
                    .setContract_code(AliDirectStatusCode.NO_TASK.getCode())
                    .setContract_memo(AliDirectStatusCode.NO_TASK.getMsg())
                    .setStatus(DirectStatus.STATUS_PROCESS);
        }
        if (contractTask.getStatus().equals(TaskStatus.PENDING.getVal())) {
            return new ApplyStatusResp()
                    .setContract_code(AliDirectStatusCode.PENDING_TASK.getCode())
                    .setContract_memo(AliDirectStatusCode.PENDING_TASK.getMsg())
                    .setStatus(DirectStatus.STATUS_PROCESS);
        }
        //审核中
        if (contractTask.getStatus().equals(TaskStatus.PROGRESSING.getVal())) {
            ApplyStatusResp resp = getProcessMemoByApply(contractTask.getMerchant_sn(), platform);
            resp.setStatus(DirectStatus.STATUS_PROCESS);
            return resp;
        } else if (TaskStatus.FAIL.getVal().equals(contractTask.getStatus())) {
            ApplyStatusResp resp = getFailMemo(contractTask, platform);
            resp.setStatus(DirectStatus.STATUS_BIZ_FAIL);
            return resp;
        } else {
            return new ApplyStatusResp()
                    .setContract_code(AliDirectStatusCode.ALI_TASK_SUCCESS.getCode())
                    .setContract_memo(AliDirectStatusCode.ALI_TASK_SUCCESS.getMsg())
                    .setStatus(DirectStatus.STATUS_SUCCESS);
        }
    }


    public ApplyStatusResp getAliDirectContractMemo(String merchantSn, String platform) {
        ContractTask contractTask = contractTaskMapper.getBySnAndType(merchantSn, ProviderUtil.ALI_DIRECT_APPLY);
        return getAliDirectContractMemo(contractTask, platform);
    }

    /**
     * 获取失败的文案信息
     *
     * @param contractTask
     * @param platform
     * @return
     */
    private ApplyStatusResp getFailMemo(ContractTask contractTask, String platform) {
        Map result = JSON.parseObject(contractTask.getResult(), Map.class);
        ApplyStatusResp applyStatusResp = new ApplyStatusResp();
        if (CollectionUtils.isEmpty(result)) {
            applyStatusResp.setContract_code(AliDirectStatusCode.UNKNOWN_CODE.getCode());
            applyStatusResp.setContract_memo(AliDirectStatusCode.UNKNOWN_CODE.getMsg());
            return applyStatusResp;
        }
        String channel = BeanUtil.getPropString(result, "channel");
        String message = BeanUtil.getPropString(result, "message");
        message = WosaiStringUtils.isNotEmpty(message) ? message : "未知失败原因";
        if (ProviderUtil.SHOUQIANBA_CHANNEL.equals(channel)) {
            applyStatusResp.setContract_code(AliDirectStatusCode.UNKNOWN_CODE.getCode());
            applyStatusResp.setContract_memo(message);
            return applyStatusResp;
        }
        String type = platform + "_msg";
        final ErrorInfo errorInfo = errorCodeManageBiz.getPromptMessageFromErrorCodeManager(type, message, ErrorCodeManageBiz.PLATFORM_ALI_DIRECT);
        applyStatusResp.setContract_code(errorInfo.getCode());
        applyStatusResp.setContract_memo(errorInfo.getMsg());
        return applyStatusResp;
    }

    /**
     * 获取审核中的文案
     *
     * @param merchantSn 商户号
     * @param platform   查询平台
     * @return 文案和code
     */
    private ApplyStatusResp getProcessMemoByApply(String merchantSn, String platform) {
        AliDirectApply aliDirectApply = aliDirectApplyMapper.selectLatestApplyByMerchantSn(merchantSn);
        ApplyStatusResp applyStatusResp = new ApplyStatusResp();
        //待提交、已提交、审核中、待签约、待授权 两种需要转译文案
        AliDirectApplyStatus applyStatus = AliDirectApplyStatus.fromValuetoStatus(aliDirectApply.getStatus());
        Map processingContractMap = applicationApolloConfig.getProcessingDirectMessage();
        List<Map> aliMessages = (List<Map>) processingContractMap.get(ProviderUtil.ALI_DIRECT);
        if (WosaiCollectionUtils.isEmpty(aliMessages)) {
            applyStatusResp.setContract_code(AliDirectStatusCode.ALI_AUDITING.getCode());
            applyStatusResp.setContract_memo(AliDirectStatusCode.ALI_AUDITING.getMsg());
            return applyStatusResp;
        }
        for (Map config : aliMessages) {
            String errorMsg = BeanUtil.getPropString(config, "error_msg");
            if (errorMsg.contains(applyStatus.getContext())) {
                applyStatusResp.setContract_code(BeanUtil.getPropString(config, "error_code"));
                applyStatusResp.setContract_memo(BeanUtil.getPropString(config, platform + "_msg"));
                return applyStatusResp;
            }
        }
        applyStatusResp.setContract_code(AliDirectStatusCode.ALI_AUDITING.getCode());
        applyStatusResp.setContract_memo(AliDirectStatusCode.ALI_AUDITING.getMsg());
        return applyStatusResp;

    }

    /**
     * 校验失败创建一个失败的task吧
     *
     * @param aliDirectReq 请求参数
     * @param message      失败原因
     */
    @Transactional(rollbackFor = Exception.class)
    public void createFailTask(AliDirectReq aliDirectReq, String message) {
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(aliDirectReq.getMerchant_sn(), aliDirectDevCode);
        ContractTask contractTask = new ContractTask().setMerchant_sn(aliDirectReq.getMerchant_sn()).setMerchant_name(BeanUtil.getPropString(merchantInfo, Merchant.NAME))
                .setAffect_sub_task_count(0).setAffect_status_success_task_count(0).setType(ProviderUtil.ALI_DIRECT_APPLY).setStatus(TaskStatus.FAIL.getVal())
                .setResult(JSON.toJSONString(CollectionUtil.hashMap("channel", ProviderUtil.SHOUQIANBA_CHANNEL, "message", message)));
        //可以发送神策消息
        contractTaskBiz.insert(contractTask);
        directStatusBiz.createOrUpdateDirectStatus(aliDirectReq.getMerchant_sn(), aliDirectReq.getDev_code(), DirectStatus.STATUS_BIZ_FAIL, message);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addAliDirectConfig(AliAuthorizeCallBack aliAuthorizeCallBack) {
        AliDirectApply aliDirectApply = aliDirectApplyMapper.selectProcessApplyByBatchNo(aliAuthorizeCallBack.getBatchNo());
        if (aliDirectApply == null) {
            log.info("不存在审核中的支付宝直连申请:{}", aliAuthorizeCallBack.getBatchNo());
            return;
        }
        if (Objects.equals(aliDirectApply.getStatus(), AliDirectApplyStatus.APPLY_ACCEPTED.getVal())) {
            log.info("支付宝直连申请已成功 {}", aliAuthorizeCallBack.getBatchNo());
            return;
        }
        // 这里把参数等保存到alipay-authinto这个服务中去
        saveToAuthInto(aliAuthorizeCallBack, aliDirectApply);

        AliDirectReq aliDirectReq = JSON.parseObject(aliDirectApply.getForm_body(), AliDirectReq.class);
        AlipayV2DirectParams directParams = this.buildAliDirectParams(aliDirectApply, aliDirectReq, aliAuthorizeCallBack);
        merchantProviderParamsService.addAlipayV2DirectParams(directParams);
        aliDirectApplyMapper.updateByPrimaryKeySelective(
                new AliDirectApply()
                        .setId(aliDirectApply.getId())
                        .setResponse_body(JSON.toJSONString(aliAuthorizeCallBack))
                        .setStatus(AliDirectApplyStatus.APPLY_ACCEPTED.getVal())
        );
        contractTaskBiz.update(
                new ContractTask()
                        .setId(aliDirectApply.getTask_id())
                        .setStatus(TaskStatus.SUCCESS.getVal())
        );
        directStatusBiz.createOrUpdateDirectStatus(aliDirectApply.getMerchant_sn(), aliDirectDevCode, DirectStatus.STATUS_SUCCESS, null);
        applyFeeRate(aliDirectApply, aliDirectReq);
    }

    private void applyFeeRate(AliDirectApply aliDirectApply, AliDirectReq aliDirectReq) {
        long comboId = aliDirectReq.getApp_info().getTrade_combo_id();
        ApplyFeeRateRequest feeRate = new ApplyFeeRateRequest()
                .setMerchantSn(aliDirectApply.getMerchant_sn())
                .setAuditSn(aliDirectApply.getMerchant_sn())
                .setTradeComboId(comboId);

        Map<String, String> applyFeeRateMap = new HashMap<>(1);
        applyFeeRateMap.put(String.valueOf(PaywayEnum.ALIPAY.getValue()), aliDirectReq.getApp_info().getFee_rate());

        feeRate.setApplyFeeRateMap(applyFeeRateMap);
        try {
            feeRateService.applyFeeRateOne(feeRate);
        } catch (Exception e) {
            log.error("开通支付宝直连成功,设置套餐失败,商户号:{}", aliDirectApply.getMerchant_sn(), e);
        }
    }

    private void saveToAuthInto(AliAuthorizeCallBack aliAuthorizeCallBack, AliDirectApply aliDirectApply) {
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(aliDirectApply.getMerchant_sn(), aliDirectDevCode);
        Store storeReq = new Store();
        storeReq.setId(merchantInfo.getId());
        storeReq.setType(Store.StoreType.Merchant);
        storeReq.setParentId("0");
        storeReq.setDeleted(false);
        storeReq.setBindType(Store.BindType.ALIPAY_DIRECT);
        Details details = new Details();
        details.setName(merchantInfo.getName());
        storeReq.setDetails(details);

        AlipayOAuth alipayOAuth = new AlipayOAuth();
        alipayOAuth.setAudit(AlipayOAuth.AuthOrTokenAudit.VALID);
        alipayOAuth.setEvent(AlipayOAuth.AuthOrTokenEvent.AUTH);
        alipayOAuth.setAppId(aliAuthorizeCallBack.getAuthAppId());
        alipayOAuth.setUserId(aliAuthorizeCallBack.getUserId());
        alipayOAuth.setAppAuthToken(aliAuthorizeCallBack.getAppAuthToken());
        alipayOAuth.setRefreshToken(aliAuthorizeCallBack.getAppRefreshToken());
        alipayOAuth.setExpirationTime(aliAuthorizeCallBack.getExpiresIn() * 1000 + System.currentTimeMillis());
        alipayOAuth.setReExpirationTime(aliAuthorizeCallBack.getReExpiresIn() * 1000 + System.currentTimeMillis());
        try {
            Store store = storeService.createStore(storeReq);
            alipayStoreService.saveAlipayOAuthHistory(new AlipayOAuthHistory(alipayOAuth));
            alipayStoreService.syncAuthInfo(store, alipayOAuth, false, false);
        } catch (ExceptionBase exceptionBase) {
            log.error("保存支付宝直连参数到authinto失败:batch_no{}", aliAuthorizeCallBack.getBatchNo(), exceptionBase);
            throw new CommonInvalidParameterException("保存支付宝授权结果失败");
        }
    }

    private AlipayV2DirectParams buildAliDirectParams(AliDirectApply aliDirectApply, AliDirectReq aliDirectReq, AliAuthorizeCallBack aliAuthorizeCallBack) {
        String merchantId = merchantService.getMerchantBySn(aliDirectApply.getMerchant_sn(), aliDirectDevCode).getId();
        String feeRate = aliDirectReq.getApp_info().getFee_rate();
        // 获取套餐信息
        List<TradeComboDetailResult> comboDetail = comboDetailService.listByComboId(aliDirectReq.getApp_info().getTrade_combo_id());
        Optional<TradeComboDetailResult> detailResult = comboDetail.stream().filter(r -> PaywayEnum.ALIPAY.getValue().equals(r.getPayway())).findFirst();
        if (!detailResult.isPresent()) {
            log.error("商户号:{} 该套餐不存在支付宝费率:{}", aliDirectReq.getMerchant_sn(), aliDirectReq.getApp_info().getTrade_combo_id());
            throw new CommonPubBizException(aliDirectReq.getApp_info().getTrade_combo_id() + "套餐不存在支付宝费率");
        }
        TradeComboDetailResult tradeComboDetailResult = detailResult.get();

        String appAuthToken = aliAuthorizeCallBack.getAppAuthToken();
        String authAppId = aliAuthorizeCallBack.getAuthAppId();
        String userId = aliAuthorizeCallBack.getUserId();
        AlipayV2DirectParams directParams = new AlipayV2DirectParams();

        //wap 支付参数
        if (tradeComboDetailResult.getWapStatus() == TradeComboStatusEnum.ENABLE.getCode()) {
            AlipayV2DirectParams.AlipayV2Params wap = new AlipayV2DirectParams.AlipayV2Params();
            wap.setApp_auth_token(appAuthToken);
            wap.setAuth_app_id(authAppId);
            wap.setMch_id(userId);
            wap.setFee_rate(feeRate);
            directParams.setAlipay_wap_v2_trade_params(wap);
        }

        //BSC CSB 支付参数
        if (tradeComboDetailResult.getB2cStatus() == TradeComboStatusEnum.ENABLE.getCode()) {
            AlipayV2DirectParams.AlipayV2Params b2cAndC2b = new AlipayV2DirectParams.AlipayV2Params();
            b2cAndC2b.setApp_auth_token(appAuthToken);
            b2cAndC2b.setAuth_app_id(authAppId);
            b2cAndC2b.setMch_id(userId);
            b2cAndC2b.setFee_rate(feeRate);
            directParams.setAlipay_v2_trade_params(b2cAndC2b);
        }
        directParams.setMerchant_sn(aliDirectApply.getMerchant_sn());
        directParams.setMerchant_id(merchantId);
        return directParams;
    }
}
