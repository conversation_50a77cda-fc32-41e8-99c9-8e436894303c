package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.ClearTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.crmdatabus.merchantBusinessOpen.appInfo.DataBusAppInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeAppService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.request.CancelFeeRateRequest;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.trade.service.result.TradeAppResult;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.avro.TradeAppAcquirerChange;
import com.wosai.upay.job.biz.acquirer.AbstractAcquirerChangeBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.acquirer.IAcquirerBiz;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.SubBizParamsMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.MultiOpenCheckResp;
import com.wosai.upay.job.model.acquirer.SyncMchStatusResp;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.subBizParams.SubBizConfig;
import com.wosai.upay.job.model.subBizParams.SubBizParams;
import com.wosai.upay.job.model.subBizParams.SubBizParamsExample;
import com.wosai.upay.job.providers.DefaultChangeTradeParamsBiz;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McContractRuleDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.model.bo.OnlinePaymentApplyComboDetailBO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import com.wosai.upay.job.refactor.model.entity.OpenOnlinePaymentApplyDO;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ThreadLocalUtil;
import com.wosai.upay.job.volcengine.dataCenter.DataCenterProducer;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.service.TaskResultServiceImpl.BELONG_LKLV3;
import static com.wosai.upay.job.util.ProviderUtil.REDIS_KEY_EXPIRE_THIRTY_MINUTES;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2022/12/29 17:45
 */
@Component
@Slf4j
public class SubBizParamsBiz {
    @Autowired
    private TradeAppService tradeAppService;
    @Autowired
    private SubBizParamsMapper subBizParamsMapper;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    MerchantProviderParamsBiz merchantProviderParamsBiz;
    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    MerchantService merchantService;
    @Autowired
    ContractStatusMapper contractStatusMapper;
    @Autowired
    private ChangeTradeParamsBiz tradeParamsBiz;
    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private FeeRateService feeRateService;
    @Autowired
    private SupportService supportService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private TradeComboDetailService tradeComboDetailService;

    @Autowired
    @Lazy
    private OnlinePaymentBiz onlinePaymentBiz;

    @Lazy
    @Autowired
    AcquirerService acquirerService;
    @Resource(name = "kafkaTemplate")
    private KafkaTemplate<String, Object> kafkaTemplate;
    private static final String TRADE_APP_ACQUIRER_CHANGE_TOPIC = "events_CUA_tradeApp-acquirer-change";

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private ComposeAcquirerBiz acquirerBiz;

    @Autowired
    private ProviderFactory providerFactory;

    @Lazy
    @Autowired
    MerchantProviderParamsService merchantProviderParamsService;

    @Lazy
    @Autowired
    CommonEventHandler commonEventHandler;

    @Autowired
    private McContractRuleDAO mcContractRuleDAO;

    @Autowired
    ComposeAcquirerBiz composeAcquirerBiz;

    @Autowired
    private DefaultChangeTradeParamsBiz defaultChangeTradeParamsBiz;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Autowired
    private McProviderDAO mcProviderDAO;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private DataCenterProducer dataCenterProducer;

    @Autowired
    private com.wosai.upay.core.service.MerchantService coreMerchantService;

    @Resource
    private AcquirerFacade acquirerFacade;



    public static final List<Integer> LKL_V3_LIST = Lists.newArrayList(ProviderEnum.PROVIDER_LAKALA_V3.getValue(), ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue(), ProviderEnum.PROVIDER_LKLWANMA.getValue(),
            ProviderEnum.PROVIDER_LKLORG.getValue(), ProviderEnum.PROVIDER_UION_OPEN.getValue(), ProviderEnum.PROVIDER_NUCC.getValue(), ProviderEnum.PROVIDER_LKL_OPEN.getValue());


    public static final String BANK_CARD_NOT_CONSISTENCE_ERROR_MSG = "商户收钱吧银行卡与收单机构银行卡不一致";


    @Value("${payment}")
    private String payment;

    @Value("${card_payment}")
    private String cardPayment;

    @Value("${bank_cooperation}")
    private String bankCooperation;

    @Value("${online}")
    private String online;

    @Value("${cross_city_payment}")
    private String crossCityPayment;

    @Value("${mobile_pos}")
    private String mobilePos;

    @Value("${campus_food_delivery}")
    private String campusFoodDelivery;


    /**
     * 支付业务和银行合作数据需要写在merchant-config表
     */

    public List<String> modifyMerchantConfigAppId;

    @PostConstruct
    public void init(){
        modifyMerchantConfigAppId = Lists.newArrayList(payment, bankCooperation);
    }

    /**
     * 移动支付业务
     *
     * @return
     */
    public String getPayTradeAppId() {
        return this.payment;
    }

    /**
     * 刷卡收款
     *
     * @return
     */
    public String getT9TradeAppId() {
        return this.cardPayment;
    }


    /**
     * 银行合作	业务
     *
     * @return
     */
    public String getBankTradeAppId() {
        return this.bankCooperation;
    }

    public String getOnlinePaymentTradeAppId() {
        return this.online;
    }

    public String getCrossCityPaymentTradeAppId() {
        return this.crossCityPayment;
    }

    public String getMobilePosAppId() {
        return this.mobilePos;
    }


    /**
     * 获取业务方名称
     *
     * @param tradeAppId
     * @return
     */
    public String getTradeAppNameById(String tradeAppId) {
        TradeAppResult tradeApp = tradeAppService.queryTradeAppById(Long.valueOf(tradeAppId));
        return tradeApp == null ? "" : tradeApp.getName();
    }


    /**
     * 根据 tradeAppId,provider 判断该表是全部更新还是部分更新
     *
     * @param merchantSn             商户号
     * @param tradeAppId             业务方
     * @param provider               机构方
     * @param merchantProviderParams 交易参数
     */
    public void updateSubBizParams(String merchantSn, String tradeAppId, Integer provider, MerchantProviderParams merchantProviderParams) {
        final boolean lklV3 = LKL_V3_LIST.contains(provider);
        if (lklV3) {
            provider = ProviderEnum.PROVIDER_LAKALA_V3.getValue();
        }

        //支付业务并且支持智慧经营,需要将所有业务除了银行卡刷卡,微信支付宝直连都变成和移动支付业务相同的信息
        if (modifyMerchantConfigAppId.contains(tradeAppId) && supportSmart().contains(provider)) {
            //先判断有没有当前业务方对应的参数,如果没有则插入
            final SubBizParamsExample tradeAppIdExample = new SubBizParamsExample();
            tradeAppIdExample.or()
                    .andMerchant_snEqualTo(merchantSn)
                    .andProviderNotIn(Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()))
                    .andDeletedEqualTo(Boolean.FALSE)
                    .andTrade_app_idEqualTo(tradeAppId);
            final List<SubBizParams> tradeAppIdSubBizParams = subBizParamsMapper.selectByExampleWithBLOBs(tradeAppIdExample);
            if (CollectionUtils.isEmpty(tradeAppIdSubBizParams)) {
                //插入
                final Map providerParams = CollectionUtil.hashMap(String.valueOf(provider), Lists.newArrayList(merchantProviderParams.getId()));
                final SubBizParams bizParams = new SubBizParams()
                        .setMerchant_sn(merchantSn)
                        .setTrade_app_id(tradeAppId)
                        .setProvider(provider)
                        .setExtra(JSONObject.toJSONString(providerParams));
                subBizParamsMapper.insertSelective(bizParams);
            }
            //修改非移动支付的交易参数
            final SubBizParamsExample example = new SubBizParamsExample();
            example.or()
                    .andMerchant_snEqualTo(merchantSn)
                    .andProviderNotIn(Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()))
                    .andDeletedEqualTo(Boolean.FALSE)
                    .andTrade_app_idNotIn(Arrays.asList(getT9TradeAppId(), getOnlinePaymentTradeAppId(), getCrossCityPaymentTradeAppId(),getMobilePosAppId()));
            List<SubBizParams> subBizParams = subBizParamsMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(subBizParams)) {
                return;
            }
            //当前白条只支持移动支付,所以为了业务管理和交易的数据完整性只有移动支付记录白条参数 参见"多业务不支持京东白条"注释
            if(Objects.equals(merchantProviderParams.getPayway(),PaywayEnum.JD_WALLET.getValue())) {
                subBizParams =  subBizParams.stream()
                        .filter(param -> Objects.equals(param.getTrade_app_id(),getPayTradeAppId()))
                        .collect(Collectors.toList());
            }
            Integer finalProvider = provider;
            subBizParams.stream().forEach(param -> {
                //找到对应之前的支付宝/微信间连参数Id
                List<String> lastUseParams = Optional.ofNullable((List<String>) param.getExtraMap().get(String.valueOf(finalProvider))).orElseGet(ArrayList::new);
                final String invalidParamId = lastUseParams.stream().map(id -> {
                    final MerchantProviderParams providerParams = merchantProviderParamsMapper.selectByPrimaryKey(id);
                    if (Objects.equals(Optional.ofNullable(providerParams).orElseGet(MerchantProviderParams::new).getPayway(), merchantProviderParams.getPayway())) {
                        return providerParams.getId();
                    }
                    return null;
                }).filter(Objects::nonNull).findFirst().orElse(null);
                //删除重复的参数主键ID
                lastUseParams.remove(invalidParamId);
                //添加最新的
                lastUseParams.add(merchantProviderParams.getId());
                //重新更新信息
                param.setProvider(finalProvider);
                final Map providerParams = CollectionUtil.hashMap(String.valueOf(param.getProvider()), lastUseParams);
                param.setExtra(JSONObject.toJSONString(providerParams));
                param.setUpdate_at(new Date());
                subBizParamsMapper.updateByPrimaryKeySelective(param);
            });
        } else {
            final SubBizParamsExample example = new SubBizParamsExample();
            example.or()
                    .andMerchant_snEqualTo(merchantSn)
                    .andTrade_app_idEqualTo(tradeAppId)
                    .andProviderNotIn(Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()))
                    .andDeletedEqualTo(Boolean.FALSE);

            final List<SubBizParams> subBizParams = subBizParamsMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(subBizParams)) {
                //插入
                final Map providerParams = CollectionUtil.hashMap(String.valueOf(provider), Lists.newArrayList(merchantProviderParams.getId()));
                final SubBizParams bizParams = new SubBizParams()
                        .setMerchant_sn(merchantSn)
                        .setTrade_app_id(tradeAppId)
                        .setProvider(provider)
                        .setExtra(JSONObject.toJSONString(providerParams));
                subBizParamsMapper.insertSelective(bizParams);
            } else {
                //更新
                Integer finalProvider = provider;
                subBizParams.stream().forEach(param -> {
                    param.setProvider(finalProvider);
                    //找到对应之前的支付宝/微信间连参数Id
                    List<String> lastUseParams = Optional.ofNullable((List<String>) param.getExtraMap().get(String.valueOf(finalProvider))).orElseGet(ArrayList::new);
                    final String invalidParamId = lastUseParams.stream().map(id -> {
                        final MerchantProviderParams providerParams = Optional.ofNullable(merchantProviderParamsMapper.selectByPrimaryKey(id))
                                .orElseGet(MerchantProviderParams::new);
                        if (Objects.equals(providerParams.getPayway(), merchantProviderParams.getPayway())) {
                            return providerParams.getId();
                        }
                        return null;
                    }).filter(Objects::nonNull).findFirst().orElse(null);
                    //删除重复的参数主键ID
                    lastUseParams.remove(invalidParamId);
                    lastUseParams.add(merchantProviderParams.getId());
                    final Map providerParams = CollectionUtil.hashMap(String.valueOf(finalProvider), Sets.newHashSet(lastUseParams));
                    param.setExtra(JSONObject.toJSONString(providerParams));
                    param.setUpdate_at(new Date());
                    subBizParamsMapper.updateByPrimaryKeySelective(param);
                });
            }
        }

    }

    /**
     * 直连开通以后,记录直连信息
     *
     * @param merchantSn 商户号
     * @param payWay     支付方式
     */
    public void doRecordDirectSubBizParams(String merchantSn, Integer payWay) {
        //获取最新直连参数
        final MerchantProviderParamsDto directParams = merchantProviderParamsBiz.getDirectParams(merchantSn, payWay, payWay);
        if (Objects.isNull(directParams)) {
            return;
        }
        final SubBizParamsExample example = new SubBizParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(payWay)
                .andTrade_app_idEqualTo(getPayTradeAppId())
                .andDeletedEqualTo(Boolean.FALSE);
        final List<SubBizParams> subBizParams = subBizParamsMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(subBizParams)) {
            //插入
            final Map providerParams = CollectionUtil.hashMap(String.valueOf(payWay), Lists.newArrayList(directParams.getId()));
            final SubBizParams bizParams = new SubBizParams()
                    .setMerchant_sn(merchantSn)
                    .setTrade_app_id(getPayTradeAppId())
                    .setProvider(payWay)
                    .setExtra(JSONObject.toJSONString(providerParams));
            subBizParamsMapper.insertSelective(bizParams);
        } else {
            //更新
            final SubBizParams bizParam = subBizParams.get(0);
            bizParam.setProvider(payWay);
            List<String> lastUseParams = Optional.ofNullable((List<String>) bizParam.getExtraMap().get(String.valueOf(payWay))).orElseGet(ArrayList::new);
            Set<String> realSet = lastUseParams.stream().collect(Collectors.toSet());
            realSet.add(directParams.getId());
            final Map providerParams = CollectionUtil.hashMap(String.valueOf(payWay), realSet);
            bizParam.setExtra(JSONObject.toJSONString(providerParams));
            bizParam.setUpdate_at(new Date());
            subBizParamsMapper.updateByPrimaryKeySelective(bizParam);
        }

    }


    /**
     * 监听到智慧经营开通成功后配置智慧经营相关参数
     *
     * @param data
     */
    public void openSmart(DataBusAppInfo data) {
        log.info("openSmart data:{}", JSONObject.toJSONString(data));
        //业务方标识这是crm的唯一标识区别业务方tradeAppId
        String appId = data.getAppId();
        String merchantId = data.getMerchantId();
        //防止并发
        String key = appId + merchantId;
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(key, value, REDIS_KEY_EXPIRE_THIRTY_MINUTES)) {
            log.info("重复消费:{}", data.getMerchantId());
            return;
        }
        try {
            Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
            Map subBizInfo = WosaiMapUtils.getMap(appIdSubBizMap, appId);
            if (WosaiMapUtils.isEmpty(subBizInfo)) {
                String message = String.format("appId:%s,没有找到对应业务,appIdSubBizMap:%s",
                        appId,
                        JSONObject.toJSONString(appIdSubBizMap));
                throw new CommonPubBizException(message);
            }
            SubBizConfig subBizConfig = JSON.parseObject(JSON.toJSONString(subBizInfo), SubBizConfig.class);
            //商户信息
            MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
            String merchantSn = merchant.getSn();
            List whitelist = applicationApolloConfig.getMultiBusinessWhitelist();
            if (!CollectionUtils.isEmpty(whitelist) && whitelist.contains(merchantSn)) {
                String message = String.format("当前商户在白名单中whitelist:%s", JSONObject.toJSONString(whitelist));
                throw new CommonPubBizException(message);
            }
            String tradeAppId = subBizConfig.getMappingTradeAppId();
            //检查是否进件成功
            ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
            if (Objects.isNull(contractStatus) || !Objects.equals(contractStatus.getStatus(), ContractStatus.STATUS_SUCCESS)) {
                throw new CommonPubBizException("当前间连扫码未开通成功");
            }
            if (AcquirerTypeEnum.ZJTLCB.getValue().equals(contractStatus.getAcquirer())) {
                log.info("泰隆银行不处理多业务多通道: {}", merchantSn);
                return;
            }

            //检查间连扫码通道是否支持新开业务
            String acquirer = contractStatus.getAcquirer();
            McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
            //是否支持智慧经营
            boolean support = supportSmart().contains(Integer.valueOf(mcAcquirerDO.getProvider())) && checkBankCardConsistence(merchantSn, acquirer);
            AbstractAcquirerChangeBiz changeBiz;
            List<MerchantProviderParams> paramsList;
            if (!support) {
                //优先选择已经实名的收单机构
                acquirer = chooseIndirectAcquire(merchantSn, null,Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()));
                if (StringUtils.isEmpty(acquirer)) {
                    throw new CommonPubBizException("当前移动支付业务所在收单机构不支持智慧经营且没有找到可支持的间连支付通道");
                }
                final String mappingTradeName = subBizConfig.getMappingTradeName();
                //校验通道能力(微信和支付宝必须都要实名)
                checkAcquireAbility(mappingTradeName, merchantSn, acquirer);
                changeBiz = applicationContext.getBean(acquirer + "-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
                paramsList = Optional.ofNullable(changeBiz.getDefaultChangeParamsForSubBiz(merchantSn))
                        .orElseGet(ArrayList::new);
            } else {
                //当前移动支付业务对应的收单机构支持智慧经营时就直接取移动支付正在使用的参数,避免重新获取参数时由于存在多个不同时间创建的子商户号导致数据有问题
                paramsList = getUsingParams(merchantSn);
            }
            if (CollectionUtils.isEmpty(paramsList)) {
                throw new CommonPubBizException("缺少支付宝/微信交易参数");
            }
            //写入sub_biz_param
            paramsList.stream().forEach(param -> tradeParamsBiz.openSmartTradeParams(param, null, false, tradeAppId));
            if (!CollectionUtils.isEmpty(paramsList)) {
                //开通智慧经营发送消息 https://jira.wosai-inc.com/browse/CUA-6713
                sendTradeAppKafkaMsg(merchantSn, acquirer, tradeAppId);
            }
            //主动打开商户状态
            acquirerService.syncMchStatusToTargetAcquirer(merchantSn, ValidStatusEnum.VALID.getValue(), acquirer);
            //写入费率,但是在这个需求中取消了储值的配置https://jira.wosai-inc.com/browse/CUA-5532
            doCombo(merchantSn, subBizConfig);
        } finally {
            redisLock.unlock(key, value);
        }
    }


    public MultiOpenCheckResp checkOpenSmart (DataBusAppInfo data) {
        MultiOpenCheckResp resp = new MultiOpenCheckResp();
        resp.setAllowOpen(true);
        try {
            Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
            Map subBizInfo = WosaiMapUtils.getMap(appIdSubBizMap, data.getAppId());
            if (WosaiMapUtils.isEmpty(subBizInfo)) {
                String message = String.format("appId:%s,没有找到对应业务,appIdSubBizMap:%s",
                        data.getAppId(),
                        JSONObject.toJSONString(appIdSubBizMap));
                throw new CommonPubBizException(message);
            }
            SubBizConfig subBizConfig = JSON.parseObject(JSON.toJSONString(subBizInfo), SubBizConfig.class);
            String merchantId = data.getMerchantId();
            //商户信息
            MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
            String merchantSn = merchant.getSn();
            List whitelist = applicationApolloConfig.getMultiBusinessWhitelist();
            if (!CollectionUtils.isEmpty(whitelist) && whitelist.contains(merchantSn)) {
                String message = String.format("当前商户在白名单中whitelist:%s", JSONObject.toJSONString(whitelist));
                throw new CommonPubBizException(message);
            }
            String tradeAppId = subBizConfig.getMappingTradeAppId();
            //检查是否进件成功
            ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
            if (Objects.isNull(contractStatus) || !Objects.equals(contractStatus.getStatus(), ContractStatus.STATUS_SUCCESS)) {
                throw new CommonPubBizException("当前间连扫码未开通成功");
            }
            if (AcquirerTypeEnum.ZJTLCB.getValue().equals(contractStatus.getAcquirer())) {
                throw new CommonPubBizException("泰隆银行不处理多业务多通道");
            }

            //检查间连扫码通道是否支持新开业务
            String acquirer = contractStatus.getAcquirer();
            McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
            //是否支持智慧经营
            boolean support = supportSmart().contains(Integer.valueOf(mcAcquirerDO.getProvider())) && checkBankCardConsistence(merchantSn, acquirer);
            AbstractAcquirerChangeBiz changeBiz;
            List<MerchantProviderParams> paramsList;
            if (!support) {
                //优先选择已经实名的收单机构
                acquirer = chooseIndirectAcquire(merchantSn, null,Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()));
                if (StringUtils.isEmpty(acquirer)) {
                    throw new CommonPubBizException("当前移动支付业务所在收单机构不支持智慧经营且没有找到可支持的间连支付通道");
                }
                final String mappingTradeName = subBizConfig.getMappingTradeName();
                //校验通道能力(微信和支付宝必须都要实名)
                checkAcquireAbility(mappingTradeName, merchantSn, acquirer);
                changeBiz = applicationContext.getBean(acquirer + "-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
                paramsList = Optional.ofNullable(changeBiz.getDefaultChangeParamsForSubBiz(merchantSn))
                        .orElseGet(ArrayList::new);
            } else {
                //当前移动支付业务对应的收单机构支持智慧经营时就直接取移动支付正在使用的参数,避免重新获取参数时由于存在多个不同时间创建的子商户号导致数据有问题
                paramsList = getUsingParams(merchantSn);
            }
            if (CollectionUtils.isEmpty(paramsList)) {
                throw new CommonPubBizException("缺少支付宝/微信交易参数");
            }


            //同一时间只能有一个间连通道
            paramsList.stream().forEach(param -> this.checkOnlyOneIndirect(merchantSn, param.getProvider()));

            //主动打开商户状态
            acquirerService.syncMchStatusToTargetAcquirer(merchantSn, ValidStatusEnum.VALID.getValue(), acquirer);
        } catch (Exception e) {
            resp.setAllowOpen(false);
            resp.setMsg(e.getMessage());
        }
        return resp;
    }


    /**
     * 获取使用参数
     *
     * @param merchantSn 商户编号
     */
    private List<MerchantProviderParams> getUsingParams(String merchantSn) {
        // 创建商户参数示例
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        // 设置查询条件
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayIn(Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.UNIONPAY.getValue()))
                .andProviderNotIn(Lists.newArrayList(ProviderEnum.ALI_PAY.getValue(), ProviderEnum.WEI_XIN.getValue()))
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andDeletedEqualTo(false);
        // 查询参数列表
        List<MerchantProviderParams> params = Optional.ofNullable(merchantProviderParamsMapper.selectByExample(example))
                .orElse(Lists.newArrayList());
        return params;
    }

    /**
     * 判断当前通道是否可用
     *
     * @param mappingTradeName 应用名称
     * @param merchantSn       商户号
     * @param acquirer         收单机构
     * @return
     */
    public void checkAcquireAbility(String mappingTradeName, String merchantSn, String acquirer) throws CommonPubBizException {
        //如果商户已经开了多业务,虽然没有认证但是有多业务在用，可以认为有交易权限,不需要校验实名 https://jira.wosai-inc.com/browse/CUA-9518
        List<Integer> usingIndirectList = getUsingIndirectProviderList(merchantSn);
        if (!CollectionUtils.isEmpty(usingIndirectList)) {
            //已经开通多业务了,优先选择多业务正在使用的间连收单机构
            final McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByProvider(String.valueOf(usingIndirectList.get(0)));
            String subUsingAcquire = mcAcquirerDO.getAcquirer();
            //其他多业务正在使用的收单机构和新开通业务要使用的收单机构是否一致?如果一致就不用校验微信/支付宝实名,否则需要校验
            if(Objects.equals(subUsingAcquire,acquirer)) {
                return;
            }
        }

        AbstractAcquirerChangeBiz changeBiz = applicationContext.getBean(acquirer + "-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
        List<MerchantProviderParams> paramsList = Optional.ofNullable(changeBiz.getDefaultChangeParamsForSubBiz(merchantSn))
                .orElseGet(ArrayList::new);
        //微信&&支付宝存在未实名
        List<MerchantProviderParams> unAuthList = paramsList.parallelStream().filter(param -> Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()).contains(param.getPayway())).
                filter(param -> PayMchAuthStatusEnum.NOT.getValue().equals(param.getAuth_status()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(unAuthList)) {
            String join = Joiner.on(",").join(unAuthList.parallelStream().map(unAuth -> unAuth.getPay_merchant_id()).collect(Collectors.toList()));
            //不需考虑线程安全
            StringBuilder builder = new StringBuilder(join);
            builder.append("支付源子商号没有实名认证");
            //钉钉提醒
            final String failMessage = builder.toString();
            dingNotice(merchantSn, mappingTradeName, acquirer, failMessage);
            final String message = String.format("失败:准备生效通道:%s,开通应用:%s,%s", acquirer, mappingTradeName, failMessage);
            throw new CommonPubBizException(message);
        }
        IAcquirerBiz acquirerBiz = composeAcquirerBiz.getAcquirerBiz(acquirer);
        //当前再用收单机构状态
        final Boolean open = acquirerBiz.getAcquirerMchStatus(merchantSn);
        if (!open) {
            //主动打开商户状态
            SyncMchStatusResp resp = acquirerService.syncMchStatusToTargetAcquirer(merchantSn, ValidStatusEnum.VALID.getValue(), acquirer);
            if (!resp.isSuccess()) {
                //钉钉提醒
                dingNotice(merchantSn, mappingTradeName, acquirer, "尝试打开收单机构状态失败," + resp.getMessage());
                final String message = String.format("准备要生效通道:%s,开通应用:%s,尝试打开收单机构状态失败", acquirer, mappingTradeName);
                throw new CommonPubBizException(message);
            }
        }
    }

    /**
     * 判断银行卡是否一致
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     * @return true-一致
     */
    public boolean checkBankCardConsistence(String merchantSn, String acquirer) {
        try {
            return acquirerFacade.getSharedAbilityByAcquirer(acquirer).map(t -> t.isBankCardConsistentWithSqb(merchantSn)).orElse(Boolean.TRUE);
        } catch (Exception e) {
            log.error("checkBankCardConsistence error, merchantSn:{}, acquirer:{}", merchantSn, acquirer, e);
            return false;
        }
    }


    /**
     * 选择收单机构
     *
     * @param merchantSn  商户号
     * @param excludeList 指定不使用哪些收单机构
     * @param payWayList 哪些payway必须需要实名
     * @return
     */
    public String chooseIndirectAcquire(String merchantSn, List<String> excludeList,List<Integer> payWayList) {
        //如果商户已经开了多业务,为了确保只有一个间连收单机构生效,所以要优先选择正在使用的间连收单机构
        List<Integer> usingIndirectList = getUsingIndirectProviderList(merchantSn);
        if (!CollectionUtils.isEmpty(usingIndirectList)) {
            //已经开通多业务了,优先选择正在使用的间连收单机构
            final McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByProvider(String.valueOf(usingIndirectList.get(0)));
            String acquire = mcAcquirerDO.getAcquirer();
            if (!CollectionUtils.isEmpty(excludeList) && excludeList.contains(acquire)) {
                return null;
            }
            if (!checkBankCardConsistence(merchantSn, acquire)) {
                return null;
            }
            return acquire;
        }
        //商户报备了哪些收单机构
        List<String> acquires = getContractIndirectAcquireList(merchantSn);
        //去除特殊机构
        if (!CollectionUtils.isEmpty(excludeList) && !CollectionUtils.isEmpty(acquires)) {
            acquires.removeAll(excludeList);
        }
        if (CollectionUtils.isEmpty(acquires)) {
            return null;
        }
        acquires.removeIf(t -> !checkBankCardConsistence(merchantSn, t));
        //在每个收单机构的实名认证状态
        Map<String, Boolean> authMap = Maps.newHashMap();
        acquires.parallelStream().forEach(ac -> {
            AbstractAcquirerChangeBiz changeBiz = applicationContext.getBean(ac + "-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
            List<MerchantProviderParams> paramsList = Optional.ofNullable(changeBiz.getDefaultChangeParamsForSubBiz(merchantSn))
                    .orElseGet(ArrayList::new);
            if (CollectionUtils.isEmpty(paramsList)) {
                log.warn(String.format("商户号%s,收单机构%s,缺少支付宝/微信交易参数", merchantSn, ac));
                return;
            }
            //对应payway必须实名
            List<MerchantProviderParams> unAuthList = paramsList.parallelStream()
                    .filter(param -> payWayList.contains(param.getPayway())).
                    filter(param -> PayMchAuthStatusEnum.NOT.getValue().equals(param.getAuth_status()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(unAuthList)) {
                authMap.put(ac, Boolean.TRUE);
            } else {
                authMap.put(ac, Boolean.FALSE);
            }
        });
        // 排序
        final List<String> PRIORITY_ORDER = applicationApolloConfig.getAcquireOrder();
        Collections.sort(acquires, (o1, o2) -> compare(o1, o2, PRIORITY_ORDER, authMap));
        if (!CollectionUtils.isEmpty(acquires)) {
            return acquires.get(0);
        }
        return null;
    }

    /**
     * 商户报备了哪些收单机构
     *
     * @param merchantSn
     * @return
     */
    @NotNull
    public List<String> getContractIndirectAcquireList(String merchantSn) {
        //1,当前商户报过的所有收单机构
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andDeletedEqualTo(false);
        List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);
        final List<MerchantProviderParams> acParams = commonEventHandler.distinctByRule(records);
        //mc_rule_group表group_id与acquire对应关系
        List<McContractRuleDO> ruleList = mcContractRuleDAO.listAllRule();
        //规则和acquire对应关系
        final Map<String, String> ruleAcquireMap = ruleList.parallelStream()
                .collect(Collectors.toMap(x -> x.getRule(), x -> x.getAcquirer(), (val1, val2) -> val1));
        List<String> indirectAcquireList = mcAcquirerDAO.getIndirectAcquirerList();
        //成功入网的间连收单机构
        List<String> acquires = contractIndirectAcquireList(acParams, ruleAcquireMap, indirectAcquireList);
        return acquires;
    }

    /**
     * 对间接获取的商户参数列表进行排序
     *
     * @param acParams            商户参数列表
     * @param ruleAcquireMap      规则与收单机构的映射关系
     * @param indirectAcquireList 间接获取的商户参数列表
     * @return 排序后的收单机构列表
     */
    @NotNull
    private List<String> contractIndirectAcquireList(List<MerchantProviderParams> acParams, Map<String, String> ruleAcquireMap, List<String> indirectAcquireList) {
        List<String> acquires = acParams.parallelStream()
                .filter(ac -> indirectAcquireList.contains(ac.getContract_rule()) && !Objects.equals(ac, AcquirerTypeEnum.TONG_LIAN.getValue()))
                .map(param -> {
                    //规则
                    final String contractRule = param.getContract_rule();
                    //收单机构
                    final String acquirer = BeanUtil.getPropString(ruleAcquireMap, contractRule);
                    return acquirer;
                }).distinct().collect(Collectors.toList());
        return acquires;
    }


    /**
     * 比较两个对象的优先级
     *
     * @param o1 第一个对象
     * @param o2 第二个对象
     * @return 如果o1的优先级高于o2，则返回-1；如果o1的优先级低于o2，则返回1；如果两个对象的优先级相等，则返回0
     */
    public static int compare(Object o1, Object o2, List<String> orderList, Map<String, Boolean> map) {
        // 获取 o1 对应的布尔值，默认为 false
        boolean o1Value = map.getOrDefault(o1, false);
        // 获取 o2 对应的布尔值，默认为 false
        boolean o2Value = map.getOrDefault(o2, false);

        // 如果 o1 值为 true 而 o2 值不为 true，则 o1 优先级较高，返回 -1
        if (o1Value && !o2Value) {
            return -1;
        }

        // 如果 o1 值不为 true 而 o2 值为 true，则 o1 优先级较低，返回 1
        if (!o1Value && o2Value) {
            return 1;
        }

        // 按照优先级顺序查找元素的位置
        int index1 = orderList.indexOf(o1);
        int index2 = orderList.indexOf(o2);

        // 如果两个元素都在优先级列表里，则按照它们在列表中的位置排序
        if (index1 != -1 && index2 != -1) {
            return Integer.compare(index1, index2);
        }

        // 如果其中一个不在列表中，则将其视为最低优先级
        if (index1 == -1 && index2 != -1) {
            return 1;
        }
        if (index1 != -1 && index2 == -1) {
            return -1;
        }

        // 如果两者都不在列表中，认为它们相等
        return 0;
    }


    /**
     * provider是否支持智慧经营
     *
     * @return
     */
    public List<Integer> supportSmart() {
        final List changeAll = applicationApolloConfig.getChangeAll();
        return changeAll;
    }


    public void doCombo(String merchantSn, SubBizConfig subBizConfig) {
        Long comboId = subBizConfig.getMappingComboId();
        if (Objects.equals(comboId, 0L) || Objects.isNull(comboId)) {
            log.info("商户号:{},开通智慧经营没有找到配置的套餐tradeAppId:{}", merchantSn, comboId);
            return;
        }
        List<String> indirectAcquireList = mcAcquirerDAO.getIndirectAcquirerList();
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        //如果移动支付业务是收银宝是复用移动支付业务费率套餐
        if (AcquirerTypeEnum.TONG_LIAN_V2.getValue().equals(contractStatus.getAcquirer())) {
            return;
        }
        if (!subBizConfig.isForceCombo()) {
            //当前移动支付业务所在通道为间连时,复用移动支付业务费率套餐
            boolean present = Optional.ofNullable(contractStatus)
                    .filter(contract -> Objects.equals(contract.getStatus(), ContractStatus.STATUS_SUCCESS) && indirectAcquireList.contains(contract.getAcquirer()))
                    .isPresent();
            if (present) {
                return;
            }
        }

        List<TradeComboDetailResult> comboDetails = tradeComboDetailService.listByComboId(comboId);
        Map feeRateMap = new HashMap();
        if (MapUtils.isEmpty(subBizConfig.getFeeRate())) {
            for (TradeComboDetailResult comboDetail : comboDetails) {
                feeRateMap.put(String.valueOf(comboDetail.getPayway()), comboDetail.getFeeRateMax());
            }
        } else {
            feeRateMap = subBizConfig.getFeeRate();
        }
        // 设置新套餐
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(merchantSn)
                .setAuditSn(String.format("业务管理发起:%s", subBizConfig.getMappingTradeName()))
                .setTradeComboId(comboId)
                .setApplyFeeRateMap(feeRateMap);
        try {
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
            log.info("设置多业务套餐 doCombo merchantSn:{},applyFeeRateRequest:{}", merchantSn, JSONObject.toJSONString(applyFeeRateRequest));
            supportService.removeCachedParams(merchantSn);
        } catch (Exception e) {
            log.error("设置多业务套餐异常,merchantSn:{},tradeApp:{},comboId:{},异常信息:{}", merchantSn, subBizConfig.getMappingTradeName(), comboId, e);
        }

    }

    public void doComboByPayway(String merchantSn, SubBizConfig subBizConfig, int payway) {
        Long comboId = subBizConfig.getMappingComboId();
        if (Objects.equals(comboId, 0L)) {
            log.info("商户号:{},开通智慧经营没有找到配置的套餐tradeAppId:{}", merchantSn, comboId);
            return;
        }
        List<String> indirectAcquireList = mcAcquirerDAO.getIndirectAcquirerList();
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        //如果移动支付业务是收银宝是复用移动支付业务费率套餐
        if (AcquirerTypeEnum.TONG_LIAN_V2.getValue().equals(contractStatus.getAcquirer())) {
            return;
        }
        if (!subBizConfig.isForceCombo()) {
            //当前移动支付业务所在通道为间连时,复用移动支付业务费率套餐
            boolean present = Optional.ofNullable(contractStatus)
                    .filter(contract -> Objects.equals(contract.getStatus(), ContractStatus.STATUS_SUCCESS) && indirectAcquireList.contains(contract.getAcquirer()))
                    .isPresent();
            if (present && !Objects.equals(payway, PaywayEnum.FOOD_CARD.getValue())) {
                return;
            }
        }

        Map feeRateMap = new HashMap();
        if (MapUtils.isEmpty(subBizConfig.getFeeRate())) {
            List<TradeComboDetailResult> comboDetails = tradeComboDetailService.listByComboId(comboId);
            for (TradeComboDetailResult comboDetail : comboDetails) {
                if (comboDetail.getPayway() == payway) {
                    feeRateMap.put(String.valueOf(comboDetail.getPayway()), comboDetail.getFeeRateMax());
                }
            }
        } else {
            feeRateMap.put(String.valueOf(payway), WosaiMapUtils.getString(subBizConfig.getFeeRate(), String.valueOf(payway)));
        }
        // 设置新套餐
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(merchantSn)
                .setAuditSn(String.format("业务管理发起:%s", subBizConfig.getMappingTradeName()))
                .setTradeComboId(comboId)
                .setApplyPartialPayway(true)
                .setApplyFeeRateMap(feeRateMap);
        try {
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
            log.info("设置多业务套餐 doCombo merchantSn:{},applyFeeRateRequest:{}", merchantSn, JSONObject.toJSONString(applyFeeRateRequest));
            supportService.removeCachedParams(merchantSn);
        } catch (Exception e) {
            log.error("设置多业务套餐异常,merchantSn:{},tradeApp:{},comboId:{},异常信息:{}", merchantSn, subBizConfig.getMappingTradeName(), comboId, e);
        }

    }

    public void doCancelCombo(String merchantSn, String tradeAppId, String auditSn) {
        String appName = getTradeAppNameById(tradeAppId);
        //设置费率套餐
        Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
        List<Map> values = JSONArray.parseArray(JSONObject.toJSONString(appIdSubBizMap.values()), Map.class);
        Map<String, Map> tradeAppIdConfig = values.stream().collect(Collectors.toMap(sub -> MapUtils.getString(sub, "mappingTradeAppId"), sub -> sub, (k1, k2) -> k1));
        Map map = tradeAppIdConfig.get(tradeAppId);
        if (MapUtils.isEmpty(map)) {
            log.info("doCancelCombo empty,appName:{},merchantSn:{},tradeAppId:{}", appName, merchantSn, tradeAppId);
            return;
        }
        long comboId = BeanUtil.getPropLong(map, "mappingComboId", 0);
        if (Objects.equals(comboId, 0L) || Objects.isNull(comboId)) {
            log.info("商户号:{},开通智慧经营没有找到配置的套餐tradeAppId:{}", merchantSn, comboId);
            return;
        }
        CancelFeeRateRequest rateRequest = new CancelFeeRateRequest();
        rateRequest.setMerchantSn(merchantSn);
        rateRequest.setTradeComboId(comboId);
        rateRequest.setAuditSn(auditSn);
        try {
            feeRateService.cancelFeeRate(rateRequest);
            supportService.removeCachedParams(merchantSn);
        } catch (Exception exception) {
            log.error("取消多业务套餐异常,merchantSn:{},tradeApp:{},comboId:{},异常信息:{}", merchantSn, appName, comboId, exception);
        }

    }


    /**
     * 删除相应payway的直连参数
     *
     * @param merchantSn
     * @param payway
     */
    public void deleteDirectSubBizParams(String merchantSn, int payway) {
        String payTradeAppId = getPayTradeAppId();
        final SubBizParamsExample example = new SubBizParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(payway)
                .andTrade_app_idEqualTo(payTradeAppId)
                .andDeletedEqualTo(Boolean.FALSE);
        final List<SubBizParams> subBizParams = subBizParamsMapper.selectByExampleWithBLOBs(example);
        subBizParams.stream().forEach(param -> subBizParamsMapper.deleteByPrimaryKey(param.getId()));
    }


    /**
     * 删除相应对应appId的参数
     *
     * @param merchantSn 商户号
     * @param tradeAppId 交易方
     */
    public void deleteParamsByTradeAppId(String merchantSn, String tradeAppId) {
        final List<SubBizParams> subBizParams = Optional.ofNullable(getParamsByTradeAppId(merchantSn, tradeAppId))
                .orElseGet(Collections::emptyList);
        subBizParams.stream().forEach(param -> subBizParamsMapper.deleteByPrimaryKey(param.getId()));
    }

    /**
     * 获取对应appId的参数
     *
     * @param merchantSn
     * @param tradeAppId
     * @return
     */
    public List<SubBizParams> getParamsByTradeAppId(String merchantSn, String tradeAppId) {
        final SubBizParamsExample example = new SubBizParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andTrade_app_idEqualTo(tradeAppId)
                .andDeletedEqualTo(Boolean.FALSE);
        final List<SubBizParams> subBizParams = subBizParamsMapper.selectByExampleWithBLOBs(example);
        return subBizParams;
    }


    /**
     * 失败发钉钉提醒
     *
     * @param merchantSn  商户号
     * @param application 开通应用
     * @param acquire     生效通道
     * @param failMessage 失败原因
     */
    public void dingNotice(String merchantSn, String application, String acquire, String failMessage) {
        final String message = String.format(
                "商户号：%s\n" +
                        "开通应用：%s\n" +
                        "生效通道：%s\n" +
                        "失败原因：%s\n"
                , merchantSn, application, acquire, failMessage);
        chatBotUtil.sendMessageToMultiBizChatBot(message);
    }


    /**
     * 为指定商户配置花呗分期
     *
     * @param merchantSn
     */
    public void openHuaBei(String merchantSn) {
        List indirectAcquireList = mcAcquirerDAO.getIndirectAcquirerList();
        //是不是正在使用银行通道
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        String acquirer = contractStatus.getAcquirer();
        if (Objects.isNull(contractStatus) || !Objects.equals(contractStatus.getStatus(), ContractStatus.STATUS_SUCCESS)
                || indirectAcquireList.contains(acquirer)) {
            log.info("openHuaBei 不需配置 商户号:{},contractStatus:{}", merchantSn, JSONObject.toJSONString(contractStatus));
            throw new CommonPubBizException(String.format("商户号:%s,当前通道:%s", merchantSn, acquirer));
        }
        Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
        Map subBizInfo = WosaiMapUtils.getMap(appIdSubBizMap, "huabei");
        if (WosaiMapUtils.isEmpty(subBizInfo)) {
            log.info("openSmart 没有找到对应业务,商户号:{},appId:{},appIdSubBizMap:{}", merchantSn, "huabei", JSONObject.toJSONString(appIdSubBizMap));
            throw new CommonPubBizException("appId_subBiz 中没有配置花呗业务信息");
        }
        //优先选择已经实名的收单机构
        acquirer = chooseIndirectAcquire(merchantSn, null,Lists.newArrayList(PaywayEnum.ALIPAY.getValue()));
        if (StringUtils.isEmpty(acquirer)) {
            throw new CommonPubBizException("当前移动支付业务所在收单机构不支持花呗且没有找到可支持的间连支付通道");
        }
        //支付宝必须都要实名
        AbstractAcquirerChangeBiz changeBiz = applicationContext.getBean(acquirer + "-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
        String finalAcquirer = acquirer;
        MerchantProviderParams huaBeiParam = Optional.ofNullable(changeBiz.getDefaultChangeParamsForSubBiz(merchantSn)).orElseGet(ArrayList::new)
                .stream()
                .filter(param -> Objects.equals(param.getPayway(), PaywayEnum.ALIPAY.getValue()) && PayMchAuthStatusEnum.YES.getValue().equals(param.getAuth_status()))
                .findFirst()
                .orElseThrow(() -> new CommonPubBizException(String.format("商户号:%s,找不到%s通道下实名的支付宝参数", merchantSn, finalAcquirer)));
        String tradeAppId = BeanUtil.getPropString(subBizInfo, "mappingTradeAppId");
        tradeParamsBiz.openSmartTradeParams(huaBeiParam, null, false, tradeAppId);
        //主动打开商户状态
        acquirerService.syncMchStatusToTargetAcquirer(merchantSn, ValidStatusEnum.VALID.getValue(), acquirer);
    }


    /**
     * 关闭指定商户花呗分期
     *
     * @param merchantSn
     */
    public void closeHuaBei(String merchantSn) {
        Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
        Map subBizInfo = WosaiMapUtils.getMap(appIdSubBizMap, "huabei");
        String tradeAppId = BeanUtil.getPropString(subBizInfo, "mappingTradeAppId");
        if (WosaiMapUtils.isEmpty(subBizInfo)) {
            log.info("openSmart 没有找到对应业务,商户号:{},appId:{},appIdSubBizMap:{}", merchantSn, "huabei", JSONObject.toJSONString(appIdSubBizMap));
            return;
        }
        //删除本地记录
        deleteParamsByTradeAppId(merchantSn, tradeAppId);
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        List<Map<String, Object>> appConfigList = tradeConfigService.getMerchantAppConfigByMerchantIdAndApp(merchant.getId(), tradeAppId);
        if (CollectionUtils.isEmpty(appConfigList)) {
            return;
        }
        //取消花呗业务交易参数
        appConfigList.stream().forEach(appConfig -> tradeConfigService.deleteMerchantAppConfig(BeanUtil.getPropString(appConfig, "id")));
        //删除缓存
        supportService.removeCachedParams(merchantSn);
        //取消套餐
        doCancelCombo(merchantSn, tradeAppId, String.format("%s主动关闭银行花呗分期取消套餐", merchantSn));

    }

    /**
     * 多业务开通时发送消息
     *
     * @param merchantSn     商户号
     * @param targetAcquirer 收单机构
     * @param tradeAppId     业务方
     */
    public void sendTradeAppKafkaMsg(String merchantSn, String targetAcquirer, String tradeAppId) {
        try {
            if (Lists.newArrayList(merchantSn, targetAcquirer, tradeAppId).stream().anyMatch(Objects::isNull)) {
                return;
            }
            MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
            String sourceAcquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
            TradeAppAcquirerChange tradeAppAcquirerChange = new TradeAppAcquirerChange(merchantSn, merchant.getId(), sourceAcquirer, targetAcquirer, getBankChannelFlag(targetAcquirer), tradeAppId);
            kafkaTemplate.send(TRADE_APP_ACQUIRER_CHANGE_TOPIC, tradeAppAcquirerChange);
            log.info("多业务开通sendTradeAppKafkaMsg消息成功: tradeAppId:{},商户号:{}", tradeAppId, merchantSn);
            if (Objects.equals(tradeAppId, getPayTradeAppId())) {
                dataCenterProducer.publishProfile(merchant.getId(), CollectionUtil.hashMap(
                        "pay_acquire", targetAcquirer
                ));

            } else {
                dataCenterProducer.publishProfile(merchant.getId(), CollectionUtil.hashMap(
                        "business_acquire", targetAcquirer
                ));
            }
        } catch (Exception e) {
            log.error("多业务开通sendTradeAppKafkaMsg消息失败:tradeAppId:{},商户号:{},异常信息:{}", tradeAppId, merchantSn, e);
        }
    }

    /**
     * 根据收单机构获取是否银行通道的标识
     *
     * @param acquirer
     * @return 0:非银行通道 1:银行通道
     */
    public String getBankChannelFlag(String acquirer) {
        List indirectAcquireList = mcAcquirerDAO.getIndirectAcquirerList();
        return indirectAcquireList.contains(acquirer) ? "0" : "1";
    }


    /**
     * 判断商户某一通道是否正在多业务
     */
    public boolean isInSubBiz(String merchantSn, String acquire) {
        SubBizParamsExample example = new SubBizParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andDeletedEqualTo(false);

        List<SubBizParams> subBizParams = subBizParamsMapper.selectByExample(example);

        return subBizParams.stream().anyMatch(param -> providerFactory.convertToBeanName(String.valueOf(param.getProvider())).contains(acquire));
    }

    public boolean isSubBiz(String merchantSn) {
        SubBizParamsExample example = new SubBizParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andDeletedEqualTo(false);

        List<SubBizParams> subBizParams = subBizParamsMapper.selectByExample(example);

        //大于1个时认为需要提交job
        return subBizParams.stream().map(SubBizParams::getProvider).distinct().count() > 1;
    }


    /**
     * 根据 tradeAppId,provider 判断该表是全部更新还是部分更新,特别注意由于这种场景下存入的是银行商户的对应的子商户号,并不是以前的merchant_provider_params表主键所以在spa展示接口要做兼容
     *
     * @param merchantSn    商户号
     * @param tradeAppId    业务方
     * @param provider      机构方
     * @param payMerchantId 子商户号
     */
    public void updateSubBizParamsForBankOffline(String merchantSn,
                                                 String tradeAppId,
                                                 Integer provider,
                                                 String payMerchantId,
                                                 Integer payWay) {
        final SubBizParamsExample example = new SubBizParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andTrade_app_idEqualTo(tradeAppId)
                .andProviderNotIn(Lists.newArrayList(ProviderEnum.ALI_PAY.getValue(), ProviderEnum.WEI_XIN.getValue()))
                .andDeletedEqualTo(Boolean.FALSE);

        final List<SubBizParams> subBizParams = subBizParamsMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(subBizParams)) {
            //插入
            final Map providerParams = CollectionUtil.hashMap(String.valueOf(provider), Lists.newArrayList(payMerchantId));
            final SubBizParams bizParams = new SubBizParams()
                    .setMerchant_sn(merchantSn)
                    .setTrade_app_id(tradeAppId)
                    .setProvider(provider)
                    .setExtra(JSONObject.toJSONString(providerParams));
            subBizParamsMapper.insertSelective(bizParams);
        } else {
            //更新
            Integer finalProvider = provider;
            subBizParams.stream().forEach(param -> {
                param.setProvider(finalProvider);
                //找到对应之前的支付宝/微信间连参数Id
                List<String> lastUseParams = Optional.ofNullable((List<String>) param.getExtraMap().get(String.valueOf(finalProvider))).orElseGet(ArrayList::new);
                //删除重复的参数主键ID
                if (lastUseParams.contains(payMerchantId)) {
                    return;
                }
                lastUseParams.add(payMerchantId);
                final Map providerParams = CollectionUtil.hashMap(String.valueOf(finalProvider), Sets.newHashSet(lastUseParams));
                param.setExtra(JSONObject.toJSONString(providerParams));
                param.setUpdate_at(new Date());
                subBizParamsMapper.updateByPrimaryKeySelective(param);
            });
        }
    }


    public void openCrossCityPayment(String merchantSn, Integer payway) {
        Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
        Map subBizInfo = WosaiMapUtils.getMap(appIdSubBizMap, "crossCityPayment");
        if (WosaiMapUtils.isEmpty(subBizInfo)) {
            log.info("openSmart 没有找到对应业务,商户号:{},appId:{},appIdSubBizMap:{}", merchantSn, "crossCityPayment", JSONObject.toJSONString(appIdSubBizMap));
            throw new CommonPubBizException("appId_subBiz 中没有配置跨城收款业务信息");
        }
        // 查询是否有线上收款的微信交易参数
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (Objects.isNull(contractStatus) || contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS) {
            throw new ContractBizException("商户进件未通过，不允许开通跨城收款");
        }
        Optional<OpenOnlinePaymentApplyDO> openOnlinePaymentApplyDO = onlinePaymentBiz.queryCurrentAcquirerSuccessApply(merchantSn, payway);
        if (!openOnlinePaymentApplyDO.isPresent()) {
            throw new ContractBizException("当前收单机构下未找到开通成功的申请单");
        }
        OpenOnlinePaymentApplyDO currentAcquirerSuccessApply = openOnlinePaymentApplyDO.get();
        MerchantProviderParams onlinePaymentParam = merchantProviderParamsMapper.getByPayMerchantId(currentAcquirerSuccessApply.getPayMerchantId());
        if (Objects.isNull(onlinePaymentParam)) {
            throw new ContractBizException("商户未找到跨城收款交易参数");
        }
        String tradeAppId = BeanUtil.getPropString(subBizInfo, "mappingTradeAppId");
        tradeParamsBiz.openSmartTradeParams(onlinePaymentParam, null, false, tradeAppId);

        OnlinePaymentApplyComboDetailBO crossCityComboDetail = currentAcquirerSuccessApply.getCrossCityPaymentComboDetail();
        if (Objects.nonNull(crossCityComboDetail)) {
            String configComboId = BeanUtil.getPropString(subBizInfo, "mappingComboId");
            String configFeeRate = BeanUtil.getPropString(subBizInfo, "feeRate." + currentAcquirerSuccessApply.getPayway());
            if (!Objects.equals(crossCityComboDetail.getTradeComboId(), configComboId) || !Objects.equals(crossCityComboDetail.getFeeRate(), configFeeRate)) {
                ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                        .setMerchantSn(currentAcquirerSuccessApply.getMerchantSn())
                        .setAuditSn(String.format("业务管理发起:重新开通%s", BeanUtil.getPropString(subBizInfo, "mappingTradeName")))
                        .setTradeComboId(Long.valueOf(crossCityComboDetail.getTradeComboId()))
                        .setApplyPartialPayway(true)
                        .setApplyFeeRateMap(CollectionUtil.hashMap(
                                String.valueOf(currentAcquirerSuccessApply.getPayway()), crossCityComboDetail.getFeeRate()
                        ));
                feeRateService.applyFeeRateOne(applyFeeRateRequest);
            }
        }
    }

    /**
     * 切换收单机构检测是否会出现多个间连收单机构
     *
     * @param merchantSn 商户SN
     * @return 是否支持
     */
    public void multiCheckWhenChange(String merchantSn, String targetAcquirer, String tradeAppId) {
        if (ThreadLocalUtil.isDoingMicroUpgrade()) {
            log.info("商户:{}, targetAcquirer:{}, 小微升级场景，不校验多个间连收单机构", merchantSn, targetAcquirer);
            return;
        }
        final McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(targetAcquirer);
        //清算类型  1间接清算   2直接清算
        final Integer clearType = mcAcquirerDO.getClearType();
        //切换到非间连收单机构不用考虑
        if (Objects.equals(clearType, ClearTypeEnum.DIRECT.getValue())) {
            return;
        }
        final Integer provider = Integer.valueOf(mcAcquirerDO.getProvider());
        // 创建非当前业务的其他所有业务方
        final SubBizParamsExample example = new SubBizParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andTrade_app_idNotEqualTo(tradeAppId)
                .andDeletedEqualTo(Boolean.FALSE);
        // 查询数据
        List<SubBizParams> subBizParamsList = subBizParamsMapper.selectByExample(example);
        //不存在刷卡收款
        final boolean noT9 = subBizParamsList.stream()
                .noneMatch(sub -> Objects.equals(sub.getTrade_app_id(), getT9TradeAppId()));
        final boolean noOnline = subBizParamsList.stream()
                .noneMatch(sub -> Objects.equals(sub.getTrade_app_id(), getOnlinePaymentTradeAppId()));
        //不存在手机POS
        final boolean noMobilePos = subBizParamsList.stream()
                .noneMatch(sub -> Objects.equals(sub.getTrade_app_id(), getMobilePosAppId()));
        //当前移动支付业务切换到间连时会统一参数所以不会存在多个间连通道
        if (modifyMerchantConfigAppId.contains(tradeAppId) && noT9 && noOnline && noMobilePos) {
            return;
        }
        // 其他业务是否还包含其他间连通道
        List<Integer> excludedProviders =
                Arrays.asList(ProviderEnum.ALI_PAY.getValue(), ProviderEnum.WEI_XIN.getValue());
        List<Integer> providerList = subBizParamsList.stream()
                .map(SubBizParams::getProvider)
                .filter(pro -> !mcProviderDAO.isBankProvider(String.valueOf(pro))
                        && !excludedProviders.contains(pro))
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(providerList) && (providerList.size() > 1 || !providerList.contains(provider))) {
            throw new CommonInvalidParameterException("禁止切换收单机构:切换后一个商户会对应多个间连收单机构");
        }
    }


    /**
     * 设置默认的多业务参数
     *
     * @param payMchId 支付商户ID
     */
    public void setDefaultForMulti(String payMchId) {
        // 根据支付商户ID获取商户参数
        MerchantProviderParams param = Optional.ofNullable(merchantProviderParamsMapper.selectByPayMerchantId(payMchId))
                .orElseGet(MerchantProviderParams::new);
        Integer provider = param.getProvider();
        if (Objects.isNull(provider)) {
            return;
        }
        String merchantSn = param.getMerchant_sn();
        // 获取当前商户是否存该收单机构对应的多业务
        final SubBizParamsExample example = new SubBizParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(LKL_V3_LIST.contains(provider) ? ProviderEnum.PROVIDER_LAKALA_V3.getValue() : provider)
                .andTrade_app_idNotIn(Lists.newArrayList(getPayTradeAppId(), getBankTradeAppId(), getOnlinePaymentTradeAppId(), getCrossCityPaymentTradeAppId(),getT9TradeAppId()))
                .andDeletedEqualTo(Boolean.FALSE);
        List<SubBizParams> subBizParams = subBizParamsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(subBizParams)) {
            return;
        }
        // 遍历多业务参数，调用changeTradeParamsWithoutCheckAcquirer方法设置默认的交易参数
        subBizParams.forEach(sub -> defaultChangeTradeParamsBiz.changeTradeParamsWithoutCheckAcquirer(param, null, Boolean.FALSE, sub.getTrade_app_id()));
    }


    /**
     * 校验当前商户使用的间连收单机构和要开通的业务使用的收单机构是同一个
     * 避免出现同时生效多个间连收单机构
     *
     * @param merchantSn 商户SN
     * @param provider   提供者
     * @return 是否只包含一个商户SN和提供者
     */
    public void checkOnlyOneIndirect(String merchantSn, Integer provider) {
        provider = BELONG_LKLV3.contains(provider) ? ProviderEnum.PROVIDER_LAKALA_V3.getValue() : provider;
        Boolean bankProvider = mcProviderDAO.isBankProvider(String.valueOf(provider));
        if (bankProvider) {
            return;
        }
        List<Integer> indirectProviderSet = getUsingIndirectProviderList(merchantSn);
        //还没有使用间连多通道或者通道相同
        if (CollectionUtils.isEmpty(indirectProviderSet) || (indirectProviderSet.size() == 1 && indirectProviderSet.contains(provider))) {
            return;
        }
        throw new com.wosai.common.exception.CommonPubBizException("只允许一个间连收单机构存在,禁止开通业务");
    }

    /**
     * 当前正在使用的间连收单机构
     *
     * @param merchantSn
     * @return
     */
    @NotNull
    private List<Integer> getUsingIndirectProviderList(String merchantSn) {
        // 创建查询条件
        final SubBizParamsExample example = new SubBizParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn).andDeletedEqualTo(Boolean.FALSE);
        // 查询数据
        List<SubBizParams> subBizParamsList = subBizParamsMapper.selectByExample(example);

        // 是否还包含其他间连通道
        List<Integer> excludedProviders = Arrays.asList(ProviderEnum.ALI_PAY.getValue(), ProviderEnum.WEI_XIN.getValue());

        List<Integer> indirectProviderList = subBizParamsList.stream()
                .map(SubBizParams::getProvider)
                .filter(pro -> !excludedProviders.contains(pro)
                        && !mcProviderDAO.isBankProvider(String.valueOf(pro))
                ).distinct()
                .collect(Collectors.toList());
        return indirectProviderList;
    }

    /**
     * 关闭跨城收款：取消套餐+删除参数+删除申请单
     * @param merchantSn
     * @param payway
     */
    @Transactional(rollbackFor = Exception.class)
    public void closeCrossCityPayment(String merchantSn, Integer payway) {
        //校验，当前收单机构下收否有开通成功的申请单
        Optional<OpenOnlinePaymentApplyDO> openOnlinePaymentApplyDO = onlinePaymentBiz.queryCurrentAcquirerSuccessApply(merchantSn, payway);
        if (!openOnlinePaymentApplyDO.isPresent()) {
            throw new ContractBizException("商户所在收单机构在当前支付源下没有申请成功的记录");
        }
        OpenOnlinePaymentApplyDO apply = openOnlinePaymentApplyDO.get();
        if (!apply.isEffect()) {
            return;
        }
        //1.保存费率
        Tuple2<ListMchFeeRateResult, ListMchFeeRateResult> tradeAppFeeRate = queryMchEffectOnlineAndCrossCityFeeRate(merchantSn, payway);
        onlinePaymentBiz.saveFeeRateAndInvalidApply(apply, tradeAppFeeRate);
        //2.取消费率
        doCancelFeeRate(merchantSn, payway, tradeAppFeeRate);
        //3.删除参数
        doDeleteSubBizParams(merchantSn, payway);

    }

    private void doDeleteSubBizParams(String merchantSn, Integer payway) {
        String onlinePaymentTradeAppId = getOnlinePaymentTradeAppId();
        String crossCityPaymentTradeAppId = getCrossCityPaymentTradeAppId();
        final SubBizParamsExample example = new SubBizParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andTrade_app_idIn(Arrays.asList(onlinePaymentTradeAppId, crossCityPaymentTradeAppId))
                .andDeletedEqualTo(Boolean.FALSE);
        List<SubBizParams> subBizParams = subBizParamsMapper.selectByExampleWithBLOBs(example);
        for (SubBizParams subBizParam : subBizParams) {
            Map<String, Object> extraMap = subBizParam.getExtraMap();
            processExtraMap(extraMap, payway);
            if (WosaiMapUtils.isNotEmpty(extraMap)) {
                updateSubBizParamsExtra(subBizParam.getId(), JSON.toJSONString(extraMap));
            } else {
                subBizParamsMapper.deleteByPrimaryKey(subBizParam.getId());
            }
        }
    }

    private Tuple2<ListMchFeeRateResult, ListMchFeeRateResult> queryMchEffectOnlineAndCrossCityFeeRate(String merchantSn, Integer payway) {
        Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
        List<ListMchFeeRateResult> listMchFeeRateResults = feeRateService.listMchEffectFeeRates(merchantSn).stream().filter(r -> r.getPayWay().equals(payway)).collect(Collectors.toList());
        String onlineAppName = BeanUtil.getPropString(appIdSubBizMap, "onlinePayment.tradeName");
        String crossCityAppName = BeanUtil.getPropString(appIdSubBizMap, "crossCityPayment.tradeName");
        ListMchFeeRateResult onlineFeeRate = null;
        ListMchFeeRateResult crossCityFeeRate = null;
        for (ListMchFeeRateResult listMchFeeRateResult : listMchFeeRateResults) {
            if (listMchFeeRateResult.getTradeAppName().equals(onlineAppName)) {
                onlineFeeRate = listMchFeeRateResult;
            }
            if (listMchFeeRateResult.getTradeAppName().equals(crossCityAppName)) {
                crossCityFeeRate = listMchFeeRateResult;
            }
        }
        return new Tuple2<>(onlineFeeRate, crossCityFeeRate);
    }

    private void doCancelFeeRate(String merchantSn, Integer payway, Tuple2<ListMchFeeRateResult, ListMchFeeRateResult> tradeAppFeeRate) {
        if (Objects.nonNull(tradeAppFeeRate.get_1())) {
            feeRateService.cancelFeeRate(new CancelFeeRateRequest()
                    .setMerchantSn(merchantSn)
                    .setTradeComboId(tradeAppFeeRate.get_1().getTradeComboId())
                    .setPayWays(Collections.singletonList(payway))
                    .setAuditSn("关闭跨城收款，取消费率套餐"));
        }
        if (Objects.nonNull(tradeAppFeeRate.get_2())) {
            feeRateService.cancelFeeRate(new CancelFeeRateRequest()
                    .setMerchantSn(merchantSn)
                    .setTradeComboId(tradeAppFeeRate.get_2().getTradeComboId())
                    .setPayWays(Collections.singletonList(payway))
                    .setAuditSn("关闭跨城收款，取消费率套餐"));
        }
    }

    private void processExtraMap(Map<String, Object> extraMap, Integer payway) {
        extraMap.entrySet().removeIf(entry -> {
            List<String> paramIds = (List<String>) entry.getValue();
            paramIds.removeIf(paramId -> {
                MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPrimaryKey(paramId);
                return merchantProviderParams != null && payway.equals(merchantProviderParams.getPayway());
            });
            return paramIds.isEmpty();
        });
    }

    private void updateSubBizParamsExtra(Long id, String extra) {
        SubBizParams update = new SubBizParams();
        update.setId(id);
        update.setExtra(extra);
        subBizParamsMapper.updateByPrimaryKeySelective(update);
    }


    public List<SubBizParams> getSubBizParams(String merchantSn, String tradeAppId, Integer provider) {
        final SubBizParamsExample example = new SubBizParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andTrade_app_idEqualTo(tradeAppId)
                .andProviderEqualTo(provider)
                .andDeletedEqualTo(Boolean.FALSE);
        return subBizParamsMapper.selectByExample(example);
    }


    public String getCampsFoodDelivery() {
        return this.campusFoodDelivery;
    }
}
