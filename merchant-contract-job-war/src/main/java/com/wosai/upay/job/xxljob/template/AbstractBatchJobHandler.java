package com.wosai.upay.job.xxljob.template;

import com.shouqianba.cua.utils.thread.ThreadPoolWorker;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;

import static com.wosai.upay.job.xxljob.model.BatchExecTypeEnum.ASYNC_PARALLEL;
import static com.wosai.upay.job.xxljob.model.BatchExecTypeEnum.ASYNC_SERIAL;
import static com.wosai.upay.job.xxljob.model.BatchExecTypeEnum.SYNC_PARALLEL;
import static com.wosai.upay.job.xxljob.model.BatchExecTypeEnum.SYNC_SERIAL;

@Slf4j
@Service
public abstract class AbstractBatchJobHandler<T> implements BatchJobHandler {
    @Autowired
    protected RedissonClient redissonClient;
    @Autowired
    @Qualifier("batchJobHandlerThreadPool")
    private JobThreadPoolExecutor batchJobHandlerThreadPool;

    private static final String HOST_NAME_UNIQ_KEY;

    static {
        String hostName;
        try {
            hostName = InetAddress.getLocalHost().getHostName();
            HOST_NAME_UNIQ_KEY = hostName.replace("merchant-contract-job-", "");
        } catch (UnknownHostException e) {
            throw new ExceptionInInitializerError("Failed to initialize HOST_NAME_UNIQ_KEY");
        }
    }

    @Override
    public void handle(BatchJobParam param) {
        if (SYNC_SERIAL.equals(param.getExecType())) {
            executeWithSerial(param);
        } else if (SYNC_PARALLEL.equals(param.getExecType())) {
            executeWithParallel(param);
        } else if (ASYNC_SERIAL.equals(param.getExecType())) {
            getExecutor().submit(RunnableWrapper.of(() -> executeWithSerial(param)));
        } else if (ASYNC_PARALLEL.equals(param.getExecType())) {
            getExecutor().submit(RunnableWrapper.of(() -> executeWithParallel(param)));
        } else {
            throw new IllegalArgumentException("执行模式不正确");
        }
    }

    private void executeWithSerial(BatchJobParam param) {
        String simpleName = this.getClass().getSimpleName();
        RLock globalLock = redissonClient.getLock(String.format("%s:%s", HOST_NAME_UNIQ_KEY, simpleName));
        try {
            if (globalLock.tryLock()) {
                List<T> dataList = queryTaskItems(param);
                dataList.forEach(this::doHandle);
            }
        } catch (Exception e) {
            log.error("{} executeWithSerial error", simpleName, e);
        } finally {
            releaseGlobalLock(globalLock);
        }
    }

    private void executeWithParallel(BatchJobParam param) {
        String simpleName = this.getClass().getSimpleName();
        RLock globalLock = redissonClient.getLock(String.format("%s:%s", HOST_NAME_UNIQ_KEY, simpleName));
        try {
            if (globalLock.tryLock()) {
                List<T> dataList = queryTaskItems(param);
                // TODO 这里最好自定义线程池，避免内存溢出
                ThreadPoolWorker<Object> threadPoolWorker = ThreadPoolWorker.of();
                for (T data : dataList) {
                    threadPoolWorker.addWork(() -> doHandle(data));
                }
                threadPoolWorker.doWorks();
            }
        } catch (Exception e) {
            log.error("{} executeWithParallel error", simpleName, e);
        } finally {
            releaseGlobalLock(globalLock);
        }
    }

    protected void doHandle(T data) {
        if (ShutdownSignal.isShuttingDown()) {
            return;
        }
        RLock lock = redissonClient.getLock(getLockKey(data));
        try {
            if (lock.tryLock()) {
                // 这里可能会出现重复数据，需要在方法内部重新判断是否需要处理
                doHandleSingleData(data);
            }
        } catch (Exception e) {
            log.error("{} batch task execute error", this.getClass().getSimpleName(), e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void releaseGlobalLock(RLock lock) {
        if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    protected JobThreadPoolExecutor getExecutor() {
        return batchJobHandlerThreadPool;
    }

    public abstract List<T> queryTaskItems(BatchJobParam param);

    public abstract String getLockKey(T t);

    public abstract void doHandleSingleData(T t);
}
