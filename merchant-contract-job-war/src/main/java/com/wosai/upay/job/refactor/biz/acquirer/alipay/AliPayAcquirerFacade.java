package com.wosai.upay.job.refactor.biz.acquirer.alipay;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;



/**
 * 支付宝收单机构门面服务
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
@Slf4j
public class AliPayAcquirerFacade extends AbstractAcquirerHandler {


    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.ALI_PAY;
    }
}
