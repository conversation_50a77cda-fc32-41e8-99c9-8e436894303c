package com.wosai.upay.job.xxljob.direct.authandcombo;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.common.exception.CommonDataObjectNotExistsException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.service.SystemService;
import com.wosai.upay.job.Constants.MerchantChangeDataConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.AuthAndComboTaskBiz;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.mapper.AuthAndComboTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.AuthAndComboTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * xxl_job_desc: 授权和费率套餐任务-查询授权状态
 * <AUTHOR>
 * @date 2025/4/17
 */
@Slf4j
@Component("AuthAndComboQueryJobHandler")
public class AuthAndComboQueryJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private AuthAndComboTaskMapper authAndComboTaskMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private WechatAuthBiz wechatAuthBiz;
    @Autowired
    private AuthAndComboTaskBiz authAndComboTaskBiz;

    @Override
    public String getLockKey() {
        return "AuthAndComboQueryJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        Integer queryLimit = param.getBatchSize();
        //只查一周以内的数据
        List<AuthAndComboTask> authAndComboTasks = authAndComboTaskMapper.selectWaitForAuth(new Date(System.currentTimeMillis() - param.getQueryTime()), queryLimit);
        if (WosaiCollectionUtils.isEmpty(authAndComboTasks)) {
            return;
        }

        authAndComboTasks.forEach(task -> {
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                MerchantProviderParams setDefault = merchantProviderParamsMapper.selectByPayMerchantId(task.getSub_mch_id());
                if (setDefault == null || setDefault.getDeleted()) {
                    throw new CommonDataObjectNotExistsException("交易参数不存在");
                }
                Boolean authStatus = wechatAuthBiz.getAuthStatus(setDefault, setDefault.getProvider());

                //认证成功
                if (authStatus != null && authStatus) {
                    if (!Objects.equals(setDefault.getProvider(), ProviderEnum.PROVIDER_FUYOU.getValue())) {
                        authAndComboTaskBiz.changeTradeParamsAndApplyCombo(task, setDefault);
                    }
                    authAndComboTaskBiz.changeStatusAndSendMessage(task, MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_SUCCEED, null);
                }
            } catch (Exception e) {
                log.error("商户信息变更查询微信认证状态/切参数异常 ,sn: {} ,id:{}", task.getMerchant_sn(), task.getId(), e);
                authAndComboTaskBiz.changeStatusAndSendMessage(task, MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_FAIL, e.getMessage());
            }

        });
    }
}
