package com.wosai.upay.job.xxljob.direct;

import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.constant.CommonConstants;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * xxl_job_desc: 重新调度系统异常的任务
 * <AUTHOR>
 * @date 2025/4/11
 */
@Slf4j
@Component("SystemExceptionJobHandler")
public class SystemExceptionJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractTaskBiz contractTaskBiz;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public String getLockKey() {
        return "SystemExceptionJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            List<ContractTask> contractTasks = contractTaskMapper.selectSystemExceptionTask(CommonConstants.SYSTEM_ERROR_PRIORITY, param.getBatchSize());
            for (ContractTask contractTask : contractTasks) {
                Long version = contractTask.getVersion();
                version++;
                contractTaskBiz.update(new ContractTask().setId(contractTask.getId()).setVersion(version));
            }
        } catch (Exception e) {
            log.error("系统异常任务更新优先级失败：", e);
            chatBotUtil.sendMessageToContractWarnChatBot("系统异常任务更新优先级失败" + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
