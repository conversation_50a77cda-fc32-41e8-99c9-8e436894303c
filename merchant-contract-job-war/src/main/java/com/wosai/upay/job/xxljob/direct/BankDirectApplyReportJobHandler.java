package com.wosai.upay.job.xxljob.direct;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wosai.common.utils.WosaiDateTimeUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.sales.core.model.User;
import com.wosai.sales.core.service.UserService;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static javax.management.timer.Timer.ONE_DAY;

/**
 * xxl_job_desc: 银行直连报表
 * @Description: 银行直连统计
 * <AUTHOR>
 * @Date 2021/10/27 09:34
 */
@Component("BankDirectApplyReportJobHandler")
@Slf4j
public class BankDirectApplyReportJobHandler extends AbstractDirectJobHandler {

    private static final String KEY = "BankDirectApplyReportBiz:sendMail";
    @Value("${psbc_business_dev_code}")
    public String psbcBusinessDevCode;

    @Value("${cgb_dev_code}")
    public String cgbDevCode;

    @Value("${ccb_dev_code}")
    public String ccbDevCode;

    @Value("${hxb_dev_code}")
    public String hxbDevCode;

    @Autowired
    MerchantService merchantService;
    @Autowired
    UserService userService;
    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;
    @Autowired
    private TemplateEngine templateEngine;
    @Value("${mail-gateway}")
    private String mailGateway;
    @Autowired
    Environment environment;

    @Override
    public String getLockKey() {
        return KEY;
    }

    @Override
    public void execute(DirectJobParam param) {
        sendSuccessMail();
    }

    /**
     * @Author: zhmh
     * @Description: 6点运行
     * @time: 14:37 2021/1/25
     */
    public void sendSuccessMail() {
        try {
            RestTemplate restTemplate = new RestTemplate();
            Map request = CollectionUtil.hashMap(
                    //TODO 模板Id
                    "id", 212,
                    "content", buildContent()
            );
            // fine TODO
            if (!ArrayUtils.isEmpty(environment.getActiveProfiles()) && !Objects.equals(environment.getActiveProfiles()[0], "prod")) {//添加测试环境邮件发送人
                request.put("to", "<EMAIL>,<EMAIL>,<EMAIL>");
            }
            Map response = restTemplate.postForObject(mailGateway, request, Map.class);
            if (0 == BeanUtil.getPropInt(response, "errcode")) {
                log.info("BankDirectApplyReportBiz SendSuccessMail success");
            } else {
                log.info("BankDirectApplyReportBiz SendSuccessMail fail : {}", JSON.toJSONString(response));
            }
        } catch (Exception e) {
            log.error("BankDirectApplyReportBiz SendSuccessMail error", e);
        }
    }

    public String buildContent() {
        long dayStart = WosaiDateTimeUtils.getDayStart(System.currentTimeMillis());
        final List<BankDirectApply> applyList = bankDirectApplyMapper.listByStatusAndMtimeLimit(BankDirectApplyConstant.Status.SUCCESS, WosaiDateTimeUtils.longToString(dayStart), WosaiDateTimeUtils.longToString(dayStart + ONE_DAY), 1000);
        //根据devCode分组
        final Map<String, List<BankDirectApply>> collect = applyList.parallelStream().collect(Collectors.groupingBy(apply -> apply.getDev_code()));
        //邮储
        final List<BankDirectApply> psbcList = collect.get(psbcBusinessDevCode);
        //广发
        final List<BankDirectApply> cgbList = collect.get(cgbDevCode);
        //建行
        final List<BankDirectApply> ccbList = collect.get(ccbDevCode);
        //华夏银行
        final List<BankDirectApply> hxbList = collect.get(hxbDevCode);
        Context context = new Context();
        context.setVariable("psbcData", generateContent(psbcList));
        context.setVariable("cgbData", generateContent(cgbList));
        context.setVariable("ccbData", generateContent(ccbList));
        context.setVariable("hxbData", generateContent(hxbList));
        String text = templateEngine.process("bankTemplate", context);
        return text;
    }

    private List<Map> generateContent(List<BankDirectApply> applyList) {
        if (CollectionUtils.isEmpty(applyList)) {
            return Lists.newArrayList();
        }
        return applyList.parallelStream().map(apply -> {
            //推广人|手机号
            final String crmUserId = BeanUtil.getPropString(JSONObject.parseObject(apply.getForm_body(), Map.class), BankDirectApplyConstant.CRM_UESRID);
            final Map user = userService.getUser(crmUserId);
            final String cellphone = BeanUtil.getPropString(user, User.CELLPHONE);
            final String linkman = BeanUtil.getPropString(user, User.LINKMAN);
            final String organizationNames = BeanUtil.getPropString(user, "organization_names");
            final String merchantSn = apply.getMerchant_sn();
            final MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
            return CollectionUtil.hashMap(
                    "merchant_sn", merchantSn,
                    "name", merchant.getName(),
                    "city", merchant.getCity(),
                    "linkman", linkman,
                    "cellphone", cellphone,
                    "organizationNames", organizationNames,
                    "time", StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", apply.getPriority()),
                    "result", "切换成功"
            );
        }).collect(Collectors.toList());
    }
}
