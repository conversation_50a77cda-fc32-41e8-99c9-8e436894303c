package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.CcbConfigExample;
import com.wosai.upay.job.model.ccbConfig.CcbConfig;

import java.util.List;

public interface CcbConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CcbConfig record);

    //需要返回id
    int insertSelective(CcbConfig record);

    List<CcbConfig> selectByExampleWithBLOBs(CcbConfigExample example);

    List<CcbConfig> selectByExample(CcbConfigExample example);

    CcbConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CcbConfig record);

    int updateByPrimaryKeyWithBLOBs(CcbConfig record);

    int updateByPrimaryKey(CcbConfig record);


    List<CcbConfig> selectAll();
}