package com.wosai.upay.job.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.workflow.bean.PageResult;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.CustomerPlatformStatusEnum;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractInfoReq;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.PayParamsModel;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.model.dto.ContractInfoDTO;
import com.wosai.upay.job.model.dto.WeChatUpgradeInfoDTO;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.util.PageInfoHelper;
import com.wosai.upay.job.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * @Author: lishuangqiang
 * @Date: 2019/4/1
 * @Description:
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class ContractTaskForCustomerPlatformImpl implements ContractTaskForCustomerPlatform {

    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Value("${customer_platform_address_url1}")
    private String url1;

    @Value("${customer_platform_address_url2}")
    private String url2;

    @Autowired
    private RuleContext ruleContext;
    @Lazy
    @Autowired
    private ContractTaskResultServiceImpl contractTaskResultService;

    @Autowired
    private ComposeAcquirerBiz acquirerBiz;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    private static String ACQUIEER_JIANLIAN_YET = "未开通支付业务";

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Override
    public PageResult<ContractInfoDTO> getContractTaskForCustomer(ContractInfoReq req) {
        PageInfo pageInfo = new PageInfo();
        BeanUtils.copyProperties(req, pageInfo);
        pageInfo = PageInfoHelper.checkoutPageInfo(pageInfo);
        final Page<Object> page = PageHelper.startPage(pageInfo.getPage(), pageInfo.getPageSize());
        final String merchantSn = req.getMerchantSn();
        Integer status = req.getStatus();
        String type = req.getType();
        ArrayList<String> typeList;
        if (StringUtils.isEmpty(type)) {
            typeList = Lists.newArrayList("新增商户入网", "更新商户基本信息", "结算账户变更", "重新报备", "更新商户费率", "更新营业执照", "增网增终");
        } else {
            typeList = Lists.newArrayList(type);
        }
        List<ContractTask> resultData = contractTaskMapper.selectListByParams(merchantSn, status, typeList, null, null);
        final List<ContractInfoDTO> infoDTOS = resultData.parallelStream().map(task -> {
            final ContractInfoDTO dto = new ContractInfoDTO();
            //提交时间
            dto.setSubmitTime(StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", task.getCreate_at()));
            //收单机构
            final String ruleGroupId = task.getRule_group_id();
            if (!StringUtils.isEmpty(ruleGroupId)) {
                RuleGroup ruleGroup = ruleContext.getRuleGroup(ruleGroupId);
                //规则组对应的收单机构
                String acquirer = ruleGroup.getAcquirer();
                //兼容切换收单机构
                if (StringUtils.isEmpty(acquirer) && StringUtils.startsWithIgnoreCase(ruleGroupId, ContractRuleConstants.CHANGE_ACQUIRER_RULE_GROUP_FEATURE)) {
                    ruleGroup = ruleContext.getRuleGroup(ruleGroupId.replace(ContractRuleConstants.CHANGE_ACQUIRER_RULE_GROUP_FEATURE, ""));
                    acquirer = ruleGroup.getAcquirer();
                }
                if (!StringUtils.isEmpty(acquirer)) {
                    //acquirer对应的中文名称
                    final String acquireName = mcAcquirerDAO.getAcquirerName(acquirer);
                    //收单机构
                    dto.setAcquire(acquireName);
                }
            }
            //进件类型
            dto.setType(task.getType());
            //进件状态
            dto.setStatus(CustomerPlatformStatusEnum.getMessage(task.getStatus()));
            String contract_memo = contractTaskResultService.getContractMemo(task);
            //状态描述
            dto.setStatusDesc(contract_memo);
            //详情地址
            dto.setUrl1(String.format(url1, task.getId(), task.getMerchant_sn()));
            dto.setUrl2(String.format(url2, task.getId(), task.getMerchant_sn(), task.getId()));
            return dto;
        }).collect(Collectors.toList());
        return new PageResult(pageInfo.getPage(), pageInfo.getPageSize(), page.getTotal(), infoDTOS);
    }

    @Override
    public PageResult<WeChatUpgradeInfoDTO> getWeChatUpgradeInfoForCustomer(ContractInfoReq req) {
        return null;
    }

    @Override
    public Map getMerchantAcquirerInfo(@Valid ContractInfoReq req) {
        ArrayList<String> result = new ArrayList<>();
        String merchantSn = req.getMerchantSn();
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn);
        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            result.add(ACQUIEER_JIANLIAN_YET);
        } else {
            try {
                String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
                String name = mcAcquirerDAO.getAcquirerName(acquirer);
                result.add(name);
            } catch (CommonPubBizException e) {
                //do nothing
            }
            MerchantProviderParams wechatDirect = merchantProviderParamsMapper.getDirectParam(merchantSn, PaywayEnum.WEIXIN.getValue());
            if (Objects.nonNull(wechatDirect)) {
                result.add(PayParamsModel.PAYWAY_WEIXIN_NAME);
            }
            MerchantProviderParams alipayDirect = merchantProviderParamsMapper.getDirectParam(merchantSn, PaywayEnum.ALIPAY.getValue());
            if (Objects.nonNull(alipayDirect)) {
                result.add(PayParamsModel.PAYWAY_ALIPAY_NAME_JUST);
            }
        }
        return CollectionUtil.hashMap("acquirer", StringUtils.join(result.toArray(), ","));
    }

}
