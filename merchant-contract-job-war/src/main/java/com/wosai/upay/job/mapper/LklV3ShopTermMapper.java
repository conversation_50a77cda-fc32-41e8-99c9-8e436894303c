package com.wosai.upay.job.mapper;


import com.wosai.upay.job.model.LklV3ShopTerm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LklV3ShopTermMapper {

    int insert(LklV3ShopTerm record);

    LklV3ShopTerm selectByPrimaryKey(String id);

    int updateByPrimaryKey(LklV3ShopTerm record);

    List<LklV3ShopTerm> selectByMerchantSn(@Param("sn") String merchantSn);

    List<LklV3ShopTerm> selectByMerchantSnAndMerInnerNo(@Param("sn") String merchantSn, @Param("merInnerNo") String merInnerNo);

    List<LklV3ShopTerm> selectListByStoreSn(@Param("sn") String storeSn);

    LklV3ShopTerm selectByStoreSnAndMerInnerNo(@Param("sn") String storeSn, @Param("merInnerNo") String merInnerNo);

    LklV3ShopTerm selectByShopId(@Param("id") String shopId);

    LklV3ShopTerm selectByStoreSn(@Param("sn") String storeSn);

    int deleteByPrimaryKey(String id);
}