package com.wosai.upay.job.biz.keepalive;

import com.shouqianba.cua.enums.businesslog.LogPlatformEnum;
import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.keepalive.validation.engine.KeepAliveValidateEngine;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveValidationScenarioEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidationResult;
import com.wosai.upay.job.externalservice.aop.AopClient;
import com.wosai.upay.job.model.keepalive.KeepAliveOperateInfo;
import com.wosai.upay.job.model.keepalive.KeepAliveConfigResult;
import com.wosai.upay.job.model.keepalive.KeepAliveConfigRequest;
import com.wosai.upay.job.model.keepalive.KeepAliveValidateRequest;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.MerchantKeepaliveConfigDAO;
import com.wosai.upay.job.refactor.dao.ProviderParamsKeepaliveTaskDAO;
import com.wosai.upay.job.refactor.model.bo.KeepAliveOperateInfoBO;
import com.wosai.upay.job.refactor.model.bo.KeepAliveTaskResultBO;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.MerchantKeepaliveConfigDO;
import com.wosai.upay.job.refactor.model.entity.ProviderParamsKeepaliveTaskDO;
import com.wosai.upay.job.util.DateUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/8/16
 */
@Component
public class KeepAliveConfigBiz {

    @Autowired
    private MerchantKeepaliveConfigDAO merchantKeepaliveConfigDAO;
    @Autowired
    private ProviderParamsKeepaliveTaskDAO providerParamsKeepaliveTaskDAO;
    @Autowired
    private ContractStatusDAO contractStatusDAO;
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    private KeepAliveTaskBiz keepAliveTaskBiz;
    @Autowired
    private AopClient aopClient;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private BusinessLogBiz businessLogBiz;
    @Autowired
    private KeepAliveValidateEngine ruleEngine;

    @Transactional(rollbackFor = Exception.class)
    public void enableKeepAliveConfig(KeepAliveConfigRequest request, LogParamsDto logParamsDto) {
        // 规则校验
        Map merchant = merchantService.getMerchantBySn(request.getMerchantSn());
        if (!LogPlatformEnum.SPA.getCode().equals(logParamsDto.getLogPlatformEnum().getCode())) {
            KeepAliveValidationResult validate = ruleEngine.validate(KeepAliveValidateContext.createForOpenCircle(merchant), KeepAliveValidationScenarioEnum.OPEN_CIRCLE);
            if (!validate.isSuccess()) {
                throw new ContractBizException(validate.getFailureReason());
            }
        }
        // 开启配置
        merchantKeepaliveConfigDAO.enableKeepAliveConfig(request.getMerchantSn(), logParamsDto);
        // 创建保活任务
        keepAliveTaskBiz.createKeepAliveTasks(request.getMerchantSn(), KeepAliveTaskResultBO.createResultFromAPI(logParamsDto.getLogPlatformEnum().getCode(), logParamsDto.getUserName(), logParamsDto.getRemark()));
        // 记录商户日志
        businessLogBiz.recordKeepAliveConfigLog(WosaiMapUtils.getString(merchant, DaoConstants.ID), true, logParamsDto);
        // 发送通知
        Optional<ContractStatusDO> contractStatusDO = contractStatusDAO.getByMerchantSn(request.getMerchantSn());
        if (contractStatusDO.isPresent()) {
            McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(contractStatusDO.get().getAcquirer());
            if (!mcAcquirerDO.getType().equals(AcquirerOrgTypeEnum.THIRD_PARTY.getValue())) {
                aopClient.sendEnableKeepAliveConfig(request.getMerchantSn());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void disableKeepAliveConfig(KeepAliveConfigRequest request, LogParamsDto logParamsDto) {
        Map merchant = merchantService.getMerchantBySn(request.getMerchantSn());
        merchantKeepaliveConfigDAO.disableKeepAliveConfig(request.getMerchantSn(), logParamsDto);
        keepAliveTaskBiz.closeKeepAliveTasksWhenCloseConfig(request.getMerchantSn(), KeepAliveTaskResultBO.createTaskCancelledResultFromAPI(logParamsDto.getLogPlatformEnum().getCode(), logParamsDto.getUserName(), logParamsDto.getRemark()));
        // 记录商户日志
        businessLogBiz.recordKeepAliveConfigLog(WosaiMapUtils.getString(merchant, DaoConstants.ID), false, logParamsDto);
        // 发送通知
        aopClient.sendDisableKeepAliveConfig(request.getMerchantSn());
    }




    public KeepAliveConfigResult queryKeepAliveConfig(String merchantSn) {
        MerchantKeepaliveConfigDO config = merchantKeepaliveConfigDAO.selectByMerchantSn(merchantSn);

        KeepAliveConfigResult result = new KeepAliveConfigResult();
        if (config != null) {
            // 使用充血模型的方法获取操作信息
            KeepAliveOperateInfoBO keepAliveOperateInfoBO = config.getOperateInfoBO();
            result.setMerchantSn(config.getMerchantSn());
            result.setStatus(config.getStatus());
            if (keepAliveOperateInfoBO != null) {
                result.setOperateInfo(new KeepAliveOperateInfo()
                        .setUserId(keepAliveOperateInfoBO.getUserId())
                        .setUserName(keepAliveOperateInfoBO.getUserName())
                        .setPlatform(keepAliveOperateInfoBO.getPlatform())
                        .setRemark(keepAliveOperateInfoBO.getRemark()));
            }
            result.setCtime(DateUtil.DATE_TIME_FORMATTER.format(config.getCtime()));
            result.setMtime(DateUtil.DATE_TIME_FORMATTER.format(config.getMtime()));
        }
        return result;
    }

    public KeepAliveValidationResult validateKeepAlive(KeepAliveValidateRequest request) {
        KeepAliveValidationScenarioEnum keepAliveValidationScenarioEnum = KeepAliveValidationScenarioEnum.fromCode(request.getScenario());
        Map merchant = merchantService.getMerchantBySn(request.getMerchantSn());
        KeepAliveValidateContext context;
        if (KeepAliveValidationScenarioEnum.OPEN_CIRCLE.equals(keepAliveValidationScenarioEnum)) {
            context = KeepAliveValidateContext.createForOpenCircle(merchant);
        } else {
            Optional<ProviderParamsKeepaliveTaskDO> taskDO = providerParamsKeepaliveTaskDAO.selectByTaskId(Long.parseLong(request.getTaskId()));
            context = KeepAliveValidateContext.createForPreCircleOrExecute(merchant, keepAliveValidationScenarioEnum, taskDO.get());
        }
        return ruleEngine.validate(context, keepAliveValidationScenarioEnum);
    }
}
