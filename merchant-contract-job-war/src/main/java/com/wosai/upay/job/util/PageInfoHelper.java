package com.wosai.upay.job.util;

import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.exception.CommonInvalidParameterException;

/**
 * PageInfoHelper
 *
 * <AUTHOR>
 * @date 2019-07-23 14:43
 */
public final class PageInfoHelper {
    /**
     * 校验分页参数是否合法，如果不合法则抛异常，否则什么也不做
     *
     * @param pageInfo 分页参数
     */
    public static PageInfo checkoutPageInfo(PageInfo pageInfo) {
        if (pageInfo == null) {
            pageInfo = new PageInfo(1, 10);
        } else if (pageInfo.getPage() == null) {
            pageInfo.setPage(1);
        } else if (pageInfo.getPageSize() == null) {
            pageInfo.setPageSize(10);
        }
        if (pageInfo.getPage() <= 0) {
            throw new CommonInvalidParameterException("分页查询：页码必须大于0");
        }
        if (pageInfo.getPageSize() <= 0) {
            throw new CommonInvalidParameterException("分页查询：每页条数必须大于0");
        }
        if (pageInfo.getPageSize() > 1000) {
            throw new CommonInvalidParameterException("分页查询：每页条数不能大于1000");
        }
        return pageInfo;
    }
}