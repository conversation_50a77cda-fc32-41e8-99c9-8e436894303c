package com.wosai.upay.job.xxljob.direct.contracttask;

import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.side.service.GeneralRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * xxl_job_desc: 进件任务-更新营业执照信息
 * 更新商户营业执照信息
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("ContractTaskUpdateBusinessLicenseJobHandler")
public class ContractTaskUpdateBusinessLicenseJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private GeneralRuleService generalRuleService;
    @Autowired
    private SubTaskHandlerContext subTaskHandlerContext;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public String getLockKey() {
        return "ContractTaskUpdateBusinessLicenseJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            List<ContractTask> contractTasks = contractTaskMapper.selectUpdateBusinessLicenseTodo(StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
            if (CollectionUtils.isEmpty(contractTasks)) {
                return;
            }
            String reviewComplete = generalRuleService.getFirstWeekDayAfterDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            for (ContractTask contractTask : contractTasks) {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                if (contractTask.getStatus() == 0) {
                    //处理预计审核完成时间
                    contractTask.setComplete_at(StringUtil.parseDate(reviewComplete));
                }
                List<ContractSubTask> contractSubTaskList = contractSubTaskMapper
                        .selectByMerchantSnAndPTaskIdAndScheduleStatus(contractTask.getMerchant_sn(), contractTask.getId(), 1);
                if (CollectionUtils.isEmpty(contractSubTaskList)) {
                    log.info("更新商户营业执照信息 contractTask:id {} merchant_sn {} 暂无可处理子任务", contractTask.getId(), contractTask.getMerchant_sn());
                }
                for (ContractSubTask contractSubTask : contractSubTaskList) {
                    if (ProviderUtil.CHANNELS.contains(contractSubTask.getChannel()) && !StringUtils.isEmpty(contractSubTask.getContract_id())) {
                        //拉卡拉或者银联商务请求等待回调
                        continue;
                    }
                    subTaskHandlerContext.handle(contractTask, contractSubTask);
                }
            }
        } catch (Exception e) {
            log.error("ContractTaskUpdateBusinessLicenseJobHandler processUpdateBusinessLicense error", e);
            chatBotUtil.sendMessageToContractWarnChatBot("processUpdateBusinessLicense error" + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
