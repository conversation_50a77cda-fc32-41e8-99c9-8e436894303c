package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.SwitchService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantAppConfig;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.TradeManageBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.SubBizParamsMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.acquirer.MerchantTradeConfig;
import com.wosai.upay.job.model.directparams.WeixinDirectParams;
import com.wosai.upay.job.model.subBizParams.SubBizParams;
import com.wosai.upay.job.model.subBizParams.SubBizParamsExample;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.service.WeiXinDirectService;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.asm.Advice;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.jws.Oneway;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: haochen
 * @date: 2025/03/27
 * @Description: 新增b2b交易参数
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class B2BConfigServiceImpl implements B2BConfigService{


    @Autowired
    SwitchService switchService;

    @Autowired
    WeiXinDirectService weiXinDirectService;

    @Autowired
    MerchantService merchantService;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    TradeConfigService tradeConfigService;

    @Autowired
    SubBizParamsMapper subBizParamsMapper;

    @Autowired
    private FeeRateService feeRateService;

    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;

    @Value("${b2b.appid}")
    private String b2bAppId;

    @Value("${trade.comboId}")
    private Long b2bTradeComboId;


    @Value("${trade.baseComboId}")
    private Long baseTradeComboId;


    @Override
    public ContractResponse addB2BWechatConfig(String merchantSn, String appid, String payMerchantId, String appKey, String feeRate) {
        try {
            log.info("微信小程序B2b支付配置:merchantSn:{},appid:{},payMerchantId:{},appKey:{},feeRate:{}", merchantSn, appid, payMerchantId, appKey, feeRate);
            Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
            //构建交易基础参数
            WeixinDirectParams.WeixinTradeParams weixinTradeParams = new WeixinDirectParams.WeixinTradeParams();
            weixinTradeParams.setWeixin_sub_appid(appid);
            weixinTradeParams.setWeixin_sub_mch_id(payMerchantId);
            weixinTradeParams.setFee_rate("0.6");
            WeixinDirectParams weixinDirectParams = new WeixinDirectParams();
            weixinDirectParams.setWeixin_mini_trade_params(weixinTradeParams);
            weixinDirectParams.setMerchant_sn(merchantSn);
            weixinDirectParams.setMerchant_id(BeanUtil.getPropString(merchant, "id"));
            merchantProviderParamsService.addWeixinDirectParams(weixinDirectParams);
            List<ListMchFeeRateResult> rateResults = feeRateService.listMchEffectFeeRates(merchantSn);
            List<ListMchFeeRateResult> basePay = rateResults.stream().filter(result -> Objects.equals("移动支付业务", result.getTradeAppName()) && PaywayEnum.WEIXIN.getValue().equals(result.getPayWay()))
                    .collect(Collectors.toList());
            if (basePay.size() == 0) {
                ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                        .setMerchantSn(merchantSn)
                        .setTradeComboId(baseTradeComboId)
                        .setAuditSn("微信小程序B2b支付配置标准费率");

                Map<String, String> applyFeeRateMap = new HashMap<>();
                String payway = String.valueOf(PaywayEnum.WEIXIN.getValue());
                applyFeeRateMap.put(payway, "0.6");
                // 设置费率并提交
                applyFeeRateRequest.setApplyPartialPayway(true);
                applyFeeRateRequest.setApplyFeeRateMap(applyFeeRateMap);
                feeRateService.applyFeeRateOne(applyFeeRateRequest);
            }
            com.wosai.upay.merchant.contract.model.ContractResponse response = weiXinDirectService.changeB2BFeeRate(payMerchantId, feeRate, appid);
            if (!response.isSuccess()) {
                return new ContractResponse().setSuccess(false).setMsg(response.getMessage());
            }
            Map switchRequest = new HashMap();
            switchRequest.put("merchant_id", BeanUtil.getPropString(merchant, "id"));
            switchRequest.put("type", 20);
            switchService.openCommonSwitchRemoveCache(switchRequest);
            //以下是指定业务方配置对应的MerchantAppConfig数据
            //获取MerchantAppConfig表对应业务方的数据
            Map<String, Object> oldAppConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(BeanUtil.getPropString(merchant, "id"), PaywayEnum.WEIXIN.getValue(), b2bAppId);
            //构建交易基础参数
            Map tradeParams = new HashMap();
            tradeParams.put("weixin_sub_appid", appid);
            tradeParams.put("weixin_sub_mch_id", payMerchantId);
            tradeParams.put("weixin_appkey", appKey);
            tradeParams.put("product_code", "WEIXIN_B2B");
            Map config = new HashMap();
            config.put("weixin_mini_trade_params", tradeParams);
            Map map = new HashMap();
            map.put(MerchantAppConfig.APP_ID, b2bAppId);
            map.put(MerchantAppConfig.PAYWAY, PaywayEnum.WEIXIN.getValue());
            map.put(MerchantAppConfig.MINI_STATUS, 1);
            map.put(MerchantAppConfig.MINI_FEE_RATE, feeRate);
            map.put(MerchantAppConfig.MERCHANT_ID, BeanUtil.getPropString(merchant, "id"));
            //补全交易参数信息
            if (MapUtils.isNotEmpty(oldAppConfig)) {
                map.put(DaoConstants.ID, BeanUtil.getPropString(oldAppConfig, DaoConstants.ID));
                //在MerchantAppConfig.PARAMS中追加最新的参数
                Map params = MapUtils.getMap(oldAppConfig, MerchantAppConfig.PARAMS);
                params.putAll(config);
                map.put(MerchantAppConfig.PARAMS, params);
                log.info("updateB2BMerchantAppConfig,merchantSn:{},ByPassTradeConfig:{}", merchantSn, JSONObject.toJSONString(map));
                tradeConfigService.updateMerchantAppConfig(map);
            } else {
                map.put(MerchantAppConfig.PARAMS, config);
                log.info("insertB2BMerchantAppConfig,merchantSn:{},ByPassTradeConfig:{}", merchantSn, JSONObject.toJSONString(map));
                tradeConfigService.createMerchantAppConfig(map);
            }
            MerchantProviderParams b2bParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
            if (b2bParams == null) {
                return new ContractResponse().setSuccess(false).setMsg("保存子商户号信息失败");
            }
            final Map providerParams = CollectionUtil.hashMap(String.valueOf(PaywayEnum.WEIXIN.getValue()), Lists.newArrayList(b2bParams.getId()));

            final SubBizParamsExample tradeAppIdExample = new SubBizParamsExample();
            tradeAppIdExample.or()
                    .andMerchant_snEqualTo(merchantSn)
                    .andProviderEqualTo(PaywayEnum.WEIXIN.getValue())
                    .andDeletedEqualTo(Boolean.FALSE)
                    .andTrade_app_idEqualTo(b2bAppId);
            final List<SubBizParams> tradeAppIdSubBizParams = subBizParamsMapper.selectByExampleWithBLOBs(tradeAppIdExample);
            if (tradeAppIdSubBizParams == null || tradeAppIdSubBizParams.size() == 0) {
                final SubBizParams bizParams = new SubBizParams()
                        .setMerchant_sn(merchantSn)
                        .setTrade_app_id(b2bAppId)
                        .setProvider(PaywayEnum.WEIXIN.getValue())
                        .setExtra(JSONObject.toJSONString(providerParams));
                subBizParamsMapper.insertSelective(bizParams);
            }
            ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                    .setMerchantSn(merchantSn)
                    .setTradeComboId(b2bTradeComboId)
                    .setAuditSn("微信小程序B2b支付配置");

            Map<String, String> applyFeeRateMap = new HashMap<>();
            String payway = String.valueOf(PaywayEnum.WEIXIN.getValue());
            applyFeeRateMap.put(payway, feeRate);
            // 设置费率并提交
            applyFeeRateRequest.setApplyPartialPayway(true);
            applyFeeRateRequest.setApplyFeeRateMap(applyFeeRateMap);
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
            updateMerchantConfig(MapUtils.getString(merchant, DaoConstants.ID), appKey);
        }catch (Exception e){
            log.error("添加b2b交易参数配置异常:{}", payMerchantId, e);
            new ContractResponse().setSuccess(false).setMsg("操作失败");
        }
        return new ContractResponse().setSuccess(true).setMsg("操作成功");
    }

    private void updateMerchantConfig(String merchantId, String appKey) {
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.WEIXIN.getValue());
        if (MapUtils.isEmpty(merchantConfig)) {
            log.warn("未找到移动支付业务配置, merchantId: {}, payway: {}", merchantId, PaywayEnum.WEIXIN.getValue());
            return;
        }
        Map merchantConfigParamsMap = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS);
        if (MapUtils.isEmpty(merchantConfigParamsMap)) {
            return;
        }
        Map weixinMiniTradeParams = MapUtils.getMap(merchantConfigParamsMap, TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
        if (MapUtils.isEmpty(weixinMiniTradeParams)) {
            return;
        }
        weixinMiniTradeParams.put("weixin_appkey", appKey);
        weixinMiniTradeParams.put("product_code", "WEIXIN_B2B");
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(MerchantConfig.PARAMS, merchantConfigParamsMap);
        updateMap.put(MerchantConfig.MERCHANT_ID, merchantId);
        updateMap.put(DaoConstants.ID, MapUtils.getString(merchantConfig, DaoConstants.ID));
        tradeConfigService.updateMerchantConfig(updateMap);
    }

}
