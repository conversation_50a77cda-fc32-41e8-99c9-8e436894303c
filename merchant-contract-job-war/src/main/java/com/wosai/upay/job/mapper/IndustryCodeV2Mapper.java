package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.IndustryCodeV2;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Description: industry_code_v2表,记录收钱吧与各机构行业对应关系表
 * <AUTHOR>
 * @Date 2020/8/27 7:02 PM
 **/
public interface IndustryCodeV2Mapper {

    @Select("select * from industry_code_v2 where industry_id=#{industry_id}")
    IndustryCodeV2 getIndustryCodeV2ByIndustryId(@Param("industry_id") String industryId);

}