package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.direct.AliDirectBiz;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.mapper.AliDirectApplyMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.DO.AliDirectApply;
import com.wosai.upay.job.model.direct.AliAuthorizeCallBack;
import com.wosai.upay.job.model.direct.AliDirectApplyDto;
import com.wosai.upay.job.model.direct.AliDirectReq;
import com.wosai.upay.job.model.direct.ApplyStatusResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/22
 */
@AutoJsonRpcServiceImpl
@Service
public class AliDirectApplyServiceImpl implements AliDirectApplyService {

    @Autowired
    private AliDirectBiz aliDirectBiz;

    @Autowired
    private ParamContextBiz paramContextBiz;

    @Autowired
    private AliDirectApplyMapper aliDirectApplyMapper;

    @Autowired
    private RedisLock redisLock;

    private static final String LOCK_KEY = "aliDirect:";

    private static final int LOCK_TIME = 10;

    @Override
    public ContractResponse applyAliDirectPay(AliDirectReq aliDirectReq) {
        ContractResponse contractResponse = new ContractResponse();
        Map<String, Object> paramContext;
        try {
            aliDirectBiz.preCheck(aliDirectReq.getMerchant_sn(), aliDirectReq.getDev_code());
            paramContext = paramContextBiz.buildAliParamContext(aliDirectReq);
        } catch (CommonInvalidParameterException | ContextParamException exception) {
            aliDirectBiz.createFailTask(aliDirectReq, exception.getMessage());
            return contractResponse.setSuccess(false).setMsg(exception.getMessage());
        }
        aliDirectBiz.createTaskAndApplyAndStatus(aliDirectReq, paramContext);
        return contractResponse.setSuccess(true);
    }

    @Override
    public ApplyStatusResp queryApplyStatus(String merchantSn, String platform) {
        String key = LOCK_KEY + merchantSn;
        if (!redisLock.lock(key, key, LOCK_TIME)) {
            throw new CommonPubBizException("请求频繁，请稍后重试");
        }
        return aliDirectBiz.getLatestStatusFromAli(merchantSn, platform);
    }

    @Override
    public ApplyStatusResp getApplyStatusByType(String merchantSn, String platform) {
        return aliDirectBiz.getAliDirectContractMemo(merchantSn, platform);
    }

    @Override
    public void authorizeMerchant(AliAuthorizeCallBack aliAuthorizeCallBack) {
        aliDirectBiz.addAliDirectConfig(aliAuthorizeCallBack);
    }

    @Override
    public AliDirectApplyDto getDirectApplyByTaskId(long taskId) {
        AliDirectApply aliDirectApply = aliDirectApplyMapper.selectApplyByTaskId(taskId);
        if (aliDirectApply == null) {
            return null;
        }
        AliDirectApplyDto applyDto = new AliDirectApplyDto();
        BeanUtils.copyProperties(aliDirectApply, applyDto);
        return applyDto;
    }
}
