package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import com.wosai.upay.job.service.T9Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 刷卡业务规则
 *
 * <AUTHOR>
 * @date 2025/8/28
 */
@Component
public class BankPosRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private T9Service t9Service;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            Boolean t9PosStatus = t9Service.getT9PosStatus(context.getMerchantSn());
            if (Boolean.TRUE.equals(t9PosStatus)) {
                return createFailureResult(
                        "商户开通了刷卡业务，不允许执行",
                        "CUSTOM_WITHDRAW_BLOCKED");
            }
            return createSuccessResult("商户未开通刷卡业务，通过检查");

        } catch (Exception e) {
            logger.error("执行刷卡业务规则检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.BANK_POS;
    }
}
