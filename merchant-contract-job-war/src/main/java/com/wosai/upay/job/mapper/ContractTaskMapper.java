package com.wosai.upay.job.mapper;

import com.wosai.upay.job.interceptor.DaoLog;
import com.wosai.upay.job.model.ContractTask;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

public interface ContractTaskMapper {

    @DaoLog
    int insert(ContractTask record);

    ContractTask selectByPrimaryKey(Long id);

    @DaoLog
    int updateByPrimaryKey(ContractTask record);

    @DaoLog
    int restartHXTask(@Param("id") Long id);

    List<ContractTask> selectByParams(@Param("merchant_sn") String merchant_sn, @Param("status") Integer status, @Param("type") String type, @Param("create_at_start") String create_at_start, @Param("create_at_end") String create_at_end);

    List<ContractTask> selectListByParams(@Param("merchant_sn") String merchant_sn, @Param("status") Integer status, @Param("types") List<String> type, @Param("create_at_start") String create_at_start, @Param("create_at_end") String create_at_end);

    /**
     * 查询商户正在进行中的互斥任务
     *
     * @param merchantSn 商户号
     * @param types      互斥任务类型
     * @return 只在执行或待执行的任务列表
     */
    List<ContractTask> selectProcessingMutexTasks(@Param("merchant_sn") String merchantSn, @Param("types") Set<String> types);

    List<ContractTask> selectIndirectContractTaskTodo(String start, Integer limit,List<String> ruleGroupIds);

    List<ContractTask> selectBankContractTaskTodo(String start, Integer limit,List<String> ruleGroupIds);

    List<ContractTask> selectUpdateFeeRateTodo(String start, Integer limit);

    List<ContractTask> selectUpdateBasicTodo(String start, Integer limit);

    List<ContractTask> selectUpdateCardTodo(String start, Integer limit);

    List<ContractTask> selectPicUploadTo(String start, Integer limit);

    List<ContractTask> selectShopTermTodo(String start, Integer limit);

    List<ContractTask> selectTaskTodoByMerchantSn(String merchantSn);

    ContractTask selectForTipsByMerchantSn(String merchantSn);

    ContractTask selectLastByMerchantSn(String merchantSn);

    /**
     * 添加影响总任务的子任务成功数量 如果达到总数时 更新状态status
     */
    @DaoLog
    int addAffectStatusSuccessTaskCount(@Param("id") Long id);

    /**
     * 减少影响总任务的子任务数量 如果达到总数时 更新状态status
     * affect_sub_task_count
     */
    @DaoLog
    int reduceAffectSubTaskCount(@Param("id") Long id);

    int selectWarnCount(String start, String end);

    List<ContractTask> selectSystemExceptionTask(String start, Integer limit);

    @DaoLog
    int updatePriority(@Param("priority") String priority, @Param("taskId") Long taskId);

    @DaoLog
    int updatePriorityAndResult(@Param("taskId") Long taskId, @Param("priority") String priority, @Param("result") String result);

    ContractTask getUpgradeTaskByMerchangtSn(@Param("merchantSn") String merchantSn, @Param("type") String type);

    @Select("select * from contract_task where merchant_sn=#{merchantSn} and type=#{type} order by priority desc limit 1")
    ContractTask getBySnAndType(@Param("merchantSn") String merchantSn, @Param("type") String type);

    @Select("select * from contract_task where merchant_sn=#{merchantSn} and type=#{type} order by priority desc")
    List<ContractTask> getContractsBySnAndType(@Param("merchantSn") String merchantSn, @Param("type") String type);

    List<ContractTask> selectReactivateContractTaskTodo(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("queryLimit") Integer queryLimit);

    ContractTask selectProcessTaskByMerchantSn(@Param("merchantSn") String merchantSn, @Param("type") String type);

    @Select("select * from contract_task where merchant_sn=#{merchantSn} and type=#{type} and status=#{status} order by create_at desc limit 1")
    ContractTask getBySnAndTypeAndStatus(@Param("merchantSn") String merchantSn, @Param("type") String type, @Param("status") int status);

    @Select("select * from contract_task where merchant_sn=#{merchantSn} and type in ('新增商户入网','结算账户变更','更新商户基本信息','更新营业执照','商户信息变更') and status=6 order by create_at desc limit 1")
    ContractTask getCanReSubmitTask(@Param("merchantSn") String merchantSn);

    @DaoLog
    @Update("update contract_task set status =#{status} where id =#{id}")
    int updateStatusById(@Param("status") int status, @Param("id") Long id);

    @Select("select * from contract_task  where priority>#{start,jdbcType=TIMESTAMP} and status in (0, 1) and type = '更新营业执照' order by priority desc LIMIT #{queryLimit}")
    List<ContractTask> selectUpdateBusinessLicenseTodo(@Param("start") String start, @Param("queryLimit") Integer queryLimit);

    @Select("select * from contract_task where merchant_sn = #{merchantSn} and type='新增商户入网' order by create_at;")
    List<ContractTask> selectContractTasks(@Param("merchantSn") String merchantSn);

    @Select("select * from contract_task where merchant_sn = #{merchantSn} and rule_group_id = #{ruleGroupId} and type='新增商户入网' and status in (0,1);")
    List<ContractTask> selectProcessContractTasks(@Param("merchantSn") String merchantSn, @Param("ruleGroupId") String ruleGroupId);

    //selectContractTaskTodo
    List<ContractTask> selectMerchantChangeDataTasks(String start, Integer limit);

    // fix CUA-10673。强制走 idx_sn_status索引
    @Select("select * from contract_task force index (`idx_sn_status`) where merchant_sn=#{merchantSn} and type=#{type} order by create_at desc limit 1")
    ContractTask getBySnAndTypeByCreateDesc(@Param("merchantSn") String merchantSn, @Param("type") String type);

    @Select("select * from contract_task where merchant_sn=#{merchantSn} and type=#{type} and status = #{status} order by create_at desc limit 1")
    ContractTask getBySnAndTypeAndStatusByCreateDesc(@Param("merchantSn") String merchantSn, @Param("type") String type, @Param("status") Integer status);

    @Select("select * from contract_task where merchant_sn=#{merchantSn} and type='新增商户入网' and status = 6 and rule_group_id = 'hxb' order by create_at desc limit 1")
    ContractTask getHXFailTask(@Param("merchantSn") String merchantSn);

    @DaoLog
    int deleteByPrimaryKey(Long id);

}