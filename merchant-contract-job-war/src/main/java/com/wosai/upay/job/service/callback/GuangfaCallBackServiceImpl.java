package com.wosai.upay.job.service.callback;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractSubTaskAndParam;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.providers.CgbProvider;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.GuangFaParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 广发回调处理
 * <AUTHOR>
 * @Date: 2021/6/28 4:14 下午
 */
@AutoJsonRpcServiceImpl
@Component
public class GuangfaCallBackServiceImpl implements GuangfaCallBackService {
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ContractParamsBiz baseBiz;
    @Autowired
    private QueryContractStatusHandler queryContractStatusHandler;
    @Autowired
    private CgbProvider cgb;


    /**
     * 查询
     *
     * @param contractId
     * @return
     */
    @Override
    public ContractSubTaskAndParam getContractSubTaskAndParam(String contractId) {
        ContractSubTask contractSubTask = contractSubTaskMapper.selectByContractId(contractId);
        GuangFaParam guangFaParam = baseBiz.buildContractParamsByContractSubTask(contractSubTask, GuangFaParam.class);
        return new ContractSubTaskAndParam().setContractSubTask(contractSubTask).setChannelParam(guangFaParam);
    }

    /**
     * 回调处理
     *
     * @param contractId
     * @param contractResponse
     * @return
     */
    @Override
    public boolean guangfaCallBackHandle(String contractId, ContractResponse contractResponse) {
        //子任务
        ContractSubTask subTask = contractSubTaskMapper.selectByContractId(contractId);
        //处理状态
        HandleQueryStatusResp statusResp = cgb.doHandleContractStatus(subTask, contractResponse);
        //任务状态
        queryContractStatusHandler.handTaskAndSubTask(subTask,statusResp);
        return true;
    }

    /**
     * 获取广发默认配置
     *
     * @return
     */
    @Override
    public GuangFaParam getDefaultParam() {
        ContractSubTask contractSubTask = new ContractSubTask();
        contractSubTask.setContract_rule(McConstant.RULE_GROUP_CGB);
        contractSubTask.setRule_group_id(McConstant.RULE_GROUP_CGB);
        return baseBiz.buildContractParamsByContractSubTask(contractSubTask, GuangFaParam.class);
    }
}
