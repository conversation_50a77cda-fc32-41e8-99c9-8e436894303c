package com.wosai.upay.job.handlers;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McChannelDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsExtDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McChannelDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsExtDO;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description: 银联商户报备结果查询
 * <AUTHOR>
 * @Date 2023/4/7 15:53
 **/
@Component
@Order(91)
@Slf4j
public class ProviderMerchantResultHandler extends AbstractSubTaskHandler {

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private McChannelDAO mcChannelDAO;
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private MerchantProviderParamsExtDAO merchantProviderParamsExtDAO;

    @Override
    protected void handleError(ContractTask task, ContractSubTask subTask, Exception e) throws Exception {
        handleResult(new ContractResponse().setCode(500).setMessage(e.toString()), subTask);
    }

    @Override
    public void doHandle(ContractTask task, ContractSubTask subTask) {
        String merchantSn = task.getMerchant_sn();
        Optional<McChannelDO> mcChannel = mcChannelDAO.getMcChannelByChannel(subTask.getChannel());
        if (!mcChannel.isPresent()) {
            throw new ContractBizException("子任务的报备渠道未找到");
        }
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(mcChannel.get().getAcquirer());
        MerchantProviderParams params = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, Integer.valueOf(mcAcquirerDO.getProvider()));
        if (Objects.isNull(params) || Objects.isNull(params.getProvider_merchant_id())) {
            throw new ContractBizException("子类还未实现收单机构报备查询逻辑");
        }
        BasicProvider provider = providerFactory.getProviderByRule(subTask.getContract_rule());
        ContractResponse contractResponse = provider.queryMerchantContractResult(params.getProvider_merchant_id());
        handleResult(contractResponse, subTask);
    }

    @Override
    public boolean supports(ContractTask task, ContractSubTask subTask) {
        return ProviderUtil.SUB_TASK_TASK_TYPE_UNION_MER_QUERY.equals(subTask.getTask_type());
    }

    @Override
    public void handleResult(ContractResponse response, ContractSubTask subTask) {
        ContractSubTask update = new ContractSubTask()
                .setId(subTask.getId())
                .setResult(response.getMessage())
                .setResponse_body(JSON.toJSONString(response.getResponseParam()))
                .setRequest_body(JSON.toJSONString(response.getRequestParam()))
                .setPriority(new Date());
        if (response.isSuccess()) {
            String status = WosaiMapUtils.getString(response.getTradeParam(), "status");
            if (MerchantProviderParamsExtDO.UNION_PAY_SUCCESS.equals(status) || MerchantProviderParamsExtDO.UNION_PAY_DELETE.equals(status)) {
                update.setStatus(SubTaskStatus.SUCCESS.getVal());
            } else {
                update.setStatus(SubTaskStatus.FAIL.getVal());
            }
        }
        if (response.isBusinessFail()) {
            update.setStatus(SubTaskStatus.FAIL.getVal());
        }
        if (response.isSystemFail()) {
            String rule = subTask.getContract_rule();
            Integer retried = subTask.getRetry();
            ContractRule contractRule = ruleContext.getContractRule(rule);
            retried++;
            update.setRetry(retried);
            if (retried >= contractRule.getRetry()) {
                update.setStatus(SubTaskStatus.FAIL.getVal());
            }
        }
        saveOrUpdateParamsExt(response, subTask);
        contractSubTaskMapper.updateByPrimaryKey(update);
    }

    private void saveOrUpdateParamsExt(ContractResponse response, ContractSubTask subTask) {
        Optional<MerchantProviderParamsDO> merChantProviderParams = getUnionPayParams(subTask);
        if (!merChantProviderParams.isPresent()) {
            return;
        }
        MerchantProviderParamsDO unionPayParams = merChantProviderParams.get();
        MerchantProviderParamsExtDO extDO = new MerchantProviderParamsExtDO();
        if (response.isSuccess()) {
            String status = WosaiMapUtils.getString(response.getTradeParam(), "status");
            extDO.setParamId(unionPayParams.getId());
            extDO.setExtField1(status);
            extDO.setExtField2(unionPayParams.getPayMerchantId());
            extDO.setType(MerchantProviderParamsExtDO.UNION_PAY);
            if (MerchantProviderParamsExtDO.UNION_PAY_DELETE.equals(status)) {
                extDO.setExtra(JSON.toJSONString(CollectionUtil.hashMap("message", "已注销")));
            } else {
                extDO.setExtra(JSON.toJSONString(CollectionUtil.hashMap("message", response.getMessage())));
            }
        } else if (response.isBusinessFail()) {
            extDO.setParamId(unionPayParams.getId());
            extDO.setExtField1(MerchantProviderParamsExtDO.UNION_PAY_FAIL);
            extDO.setExtField2(unionPayParams.getPayMerchantId());
            extDO.setType(MerchantProviderParamsExtDO.UNION_PAY);
            extDO.setExtra(JSON.toJSONString(CollectionUtil.hashMap("message", response.getMessage())));
        } else {
            return;
        }
        Optional<MerchantProviderParamsExtDO> unionPayStatus = merchantProviderParamsExtDAO.getUnionPayStatus(unionPayParams.getId());
        if (unionPayStatus.isPresent()) {
            extDO.setId(unionPayStatus.get().getId());
            merchantProviderParamsExtDAO.updateMerchantParams(extDO);
        } else {
            merchantProviderParamsExtDAO.saveMerchantParametersExt(extDO);
        }
    }

    private Optional<MerchantProviderParamsDO> getUnionPayParams(ContractSubTask subTask) {
        Optional<McChannelDO> mcChannelDO = mcChannelDAO.getMcChannelByChannel(subTask.getChannel());
        if (!mcChannelDO.isPresent()) {
            throw new ContractBizException("子任务的报备渠道未找到");
        }
        // 为了兼容1017
        List<McChannelDO> mcChannels = mcChannelDAO.getMcChannelByAcquirer(mcChannelDO.get().getAcquirer(), PaywayEnum.UNIONPAY.getValue());
        for (McChannelDO mcChannel : mcChannels) {
            Optional<MerchantProviderParamsDO> merChantProviderParams = merchantProviderParamsDAO.getMerChantProviderParams(subTask.getMerchant_sn(), mcChannel.getChannelNo(), PaywayEnum.UNIONPAY.getValue());
            if (merChantProviderParams.isPresent()) {
                return merChantProviderParams;
            }
        }
        return Optional.empty();

    }

}