package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.AntShopTask;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AntShopTaskMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AntShopTask record);

    int insertSelective(AntShopTask record);

    AntShopTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AntShopTask record);

    int updateByPrimaryKeyWithBLOBs(AntShopTask record);

    int updateByPrimaryKey(AntShopTask record);

    /**
     * @param statusList   状态集合
     * @param businessType 业务类型集合
     * @param queryLimit   查询数量
     * @param startTime    起始时间
     * @param endTime      结束时间
     * @return
     * @Author: zhmh
     * @Description: 查询满足条件的集合
     * @time: 16:06 2021/2/24
     */
    List<AntShopTask> selectByStatus(@Param("status") List<Integer> statusList, @Param("type") List<Integer> businessType, @Param("limit") int queryLimit, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<AntShopTask> selectByStoreSn(@Param("merchantSn") String merchantSn, @Param("storeSn") String storeSn,@Param("aliMchId") String aliMchId);

    /**
     * 根据商户号和支付宝商户号查询指定状态的数据
     * @param merchantSn 商户号
     * @param aliMchId   支付宝商户号
     * @param status     状态
     * @return           任务
     */
    AntShopTask selectByMerchantSnAndALiMchId(@Param("merchantSn") String merchantSn, @Param("aliMchId") String aliMchId, @Param("status") Integer status);

    /**
     * 根据商户号删除失败的任务
     * @param merchantSn
     * @return
     */
    int deleteByMerchantSn(@Param("merchantSn") String merchantSn);

    @Select("select * from ant_shop_task where merchant_sn=#{merchantSn} and status !=6")
    List<AntShopTask> selectByMerchantSn(@Param("merchantSn") String merchantSn);


    @Select("select * from ant_shop_task where store_sn=#{storeSn} order by order by priority desc limit 1")
    AntShopTask selectOnlyByStoreSn(@Param("storeSn") String storeSn);

    @Select("select * from ant_shop_task where ant_shop_order_id=#{antShopOrderId} order by order by priority desc limit 1")
    AntShopTask selectByAntShopOrderId(@Param("antShopOrderId") String antShopOrderId);
}