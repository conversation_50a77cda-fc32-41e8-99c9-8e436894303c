package com.wosai.upay.job.xxljob.direct.weixindirect;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.enume.WeixinDirectApplyStatus;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.WeixinDirectApplyMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.WeixinDirectApply;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.monitor.MonitorObject;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.weixin.DirectApplymentStatusResp;
import com.wosai.upay.merchant.contract.service.WeiXinDirectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * xxl_job_desc: 微信直连-提交申请单
 * <AUTHOR>
 * @date 2025/4/11
 */
@Slf4j
@Component("WeixinDirectSubmitApplyJobHandler")
public class WeixinDirectSubmitApplyJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private WeiXinDirectService weiXinDirectService;

    @Autowired
    private WeixinDirectApplyMapper applyMapper;

    @Autowired
    private DirectStatusBiz directStatusBiz;

    @Autowired
    private ContractTaskBiz taskBiz;

    @Autowired
    private Environment environment;

    @Autowired
    private ContractTaskMapper taskMapper;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private MonitorLog monitorLog;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Value("${weixin.direct.online}")
    private String weixinDirectOnline;


    private static final long FIVE_MINUTE = 5 * 60 * 1000;

    @Override
    public String getLockKey() {
        return "WeixinDirectSubmitApplyJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        long current = System.currentTimeMillis();
        List<WeixinDirectApply> applyList = applyMapper.getAppliesByPriorityAndStatus(
                DateFormatUtils.format(current - param.getQueryTime(), "yyyy-MM-dd HH:mm:ss"),
                DateFormatUtils.format(current, "yyyy-MM-dd HH:mm:ss"),
                Lists.newArrayList(WeixinDirectApplyStatus.UN_SUBMIT.getVal()),
                param.getBatchSize());
        applyList.forEach(apply -> {
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                ContractTask task = taskMapper.selectByPrimaryKey(apply.getTask_id());
                Map contextParam = JSON.parseObject(task.getEvent_context(), Map.class);
                submitDirectApply(apply, contextParam);
            } catch (Throwable e) {
                log.error("submit apply error {} ", apply.getMerchant_sn(), e);
                chatBotUtil.sendMessageToContractWarnChatBot(apply.getMerchant_sn() + "提交微信直连申请单失败" + ExceptionUtil.getThrowableMsg(e));
            }
        });
    }

    private void submitDirectApply(WeixinDirectApply apply, Map contextParam) {
        String merchantSn = apply.getMerchant_sn();
        long start = System.currentTimeMillis();
        int code = 200;
        String message = "";
        try {
            //1. 组织参数
            contextParam.put(ParamContextBiz.KEY_BUSINESS_CODE, generateBusinessCode(apply));
            ContractResponse contractResponse = weiXinDirectService.applyment(contextParam, this.getDirectScene(apply.getDev_code()));
            code = contractResponse.getCode();
            message = contractResponse.getMessage();
            if (code == HttpStatus.SC_OK) {
                //3.1  成功 更新申请单为微信审核中  报备任务为审核中 报备状态为审核中
                transactionTemplate.executeWithoutResult(status -> {
                    applyMapper.submitResult(apply.getId(), JSON.toJSONString(contractResponse.getRequestParam()), JSON.toJSONString(contractResponse.getResponseParam()), null, WeixinDirectApplyStatus.IN_WEIXIN_AUDITING.getVal());
                    taskBiz.update(new ContractTask().setId(apply.getTask_id()).setStatus(TaskStatus.PROGRESSING.getVal()));
                    directStatusBiz.createOrUpdateDirectStatus(merchantSn, apply.getDev_code(), DirectStatus.STATUS_PROCESS, null);
                });
            } else {
                if (code == HttpStatus.SC_INTERNAL_SERVER_ERROR || code == 429) {
                    //3.2.2 500
                    log.error("提交微信直连申请单返回500:{} {}", merchantSn, apply.getId());
                    chatBotUtil.sendMessageToContractWarnChatBot(apply.getMerchant_sn() + "提交微信直连申请单返回500" + message);
                    applyMapper.updateByPrimaryKeySelective(new WeixinDirectApply().setId(apply.getId()).setPriority(new Date(System.currentTimeMillis() + FIVE_MINUTE)));
                } else {
                    //3.2.1 非500，更新申请单为失败 报备任务失败 间连状态为失败
                    String finalMessage = message;
                    transactionTemplate.executeWithoutResult(status -> {
                        applyMapper.submitResult(apply.getId(), JSON.toJSONString(contractResponse.getRequestParam()), JSON.toJSONString(contractResponse.getResponseParam()), finalMessage, WeixinDirectApplyStatus.APPLY_REJECTED.getVal());
                        taskBiz.update(new ContractTask().setId(apply.getTask_id()).setStatus(TaskStatus.FAIL.getVal()).setResult(JSON.toJSONString(CollectionUtil.hashMap("channel", ProviderUtil.WEIXIN_DIRECT, "message", finalMessage))));
                        directStatusBiz.createOrUpdateDirectStatus(merchantSn, apply.getDev_code(), DirectStatus.STATUS_BIZ_FAIL, finalMessage);
                    });
                }
            }
        } finally {
            monitorLog.recordObject(new MonitorObject()
                    .setSn(merchantSn)
                    .setEvent(MonitorObject.WEIXIN_DIRECT_APPLY)
                    .setCost(System.currentTimeMillis() - start)
                    .setStatus(code)
                    .setMessage(message));
        }
    }

    private String generateBusinessCode(WeixinDirectApply apply) {
        String prefix;
        if ((Arrays.toString(environment.getActiveProfiles()).contains("prod"))) {
            prefix = "prod";
        } else {
            prefix = "test";
        }
        WeixinDirectApply latestFailedApply = applyMapper.selectLatestFailedApplyByMerchantSn(apply.getMerchant_sn(), apply.getDev_code(), 1);
        if (latestFailedApply == null) {
            return prefix + apply.getMerchant_sn();
        }
        if (WosaiStringUtils.isNotEmpty(latestFailedApply.getResponse_body())) {
            DirectApplymentStatusResp directApplymentStatusResp = JSON.parseObject(latestFailedApply.getResponse_body(), DirectApplymentStatusResp.class);
            // 如果上次失败原因是撤销，则要新搞一个business_code, 申请单被撤销之后是不能复用的
            if (WosaiStringUtils.isNotEmpty(directApplymentStatusResp.getApplyment_state()) && "APPLYMENT_STATE_CANCELED".equals(directApplymentStatusResp.getApplyment_state())) {
                return prefix + apply.getMerchant_sn() + apply.getId();
            }
        }
        // 返回上次上送的business_code
        Map requestBody = JSON.parseObject(latestFailedApply.getRequest_body(), Map.class);
        return BeanUtil.getPropString(requestBody, "business_code");
    }

    private String getDirectScene(String devCode) {
        if (weixinDirectOnline.equals(devCode)) {
            return "SALES_SCENES_APP";
        }
        return "SALES_SCENES_STORE";
    }
}
