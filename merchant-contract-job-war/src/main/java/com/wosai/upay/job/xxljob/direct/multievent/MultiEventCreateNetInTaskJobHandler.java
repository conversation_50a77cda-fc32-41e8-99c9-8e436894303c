package com.wosai.upay.job.xxljob.direct.multievent;

import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.handlers.MultiProviderEventHandlerContext;
import com.wosai.upay.job.mapper.MultiProviderContractEventMapper;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

import static com.wosai.upay.job.util.StringUtil.formatDate;

/**
 * xxl_job_desc: MultiEvent-创建多通道入网任务
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("MultiEventCreateNetInTaskJobHandler")
public class MultiEventCreateNetInTaskJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private MultiProviderContractEventMapper multiEventMapper;
    @Autowired
    private MultiProviderEventHandlerContext multiProviderEventHandlerContext;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public String getLockKey() {
        return "MultiEventCreateNetInTaskJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            final List<MultiProviderContractEvent> events = multiEventMapper.selectPendingNetInMultiEvents(formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
            if (CollectionUtils.isEmpty(events) || Objects.isNull(events.get(0))) {
                return;
            }
            for (MultiProviderContractEvent event : events) {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                try {
                    multiProviderEventHandlerContext.handle(event);
                } catch (Exception e) {
                    log.error("MultiEventCreateNetInTaskJobHandler process error", e);
                    chatBotUtil.sendMessageToContractWarnChatBot(event.getMerchant_sn() + "入网事件处理异常 " + ExceptionUtil.getThrowableMsg(e));
                }
            }
        } catch (Exception e) {
            log.error("MultiEventCreateNetInTaskJobHandler process error", e);
            chatBotUtil.sendMessageToContractWarnChatBot("入网事件处理异常 " + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
