package com.wosai.upay.job.enume;

import com.wosai.common.utils.WosaiDateTimeUtils;
import com.wosai.upay.job.constant.CommonConstants;
import lombok.Getter;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static javax.management.timer.Timer.*;

@Getter
public enum ChannelDelayQueryConfigEnum {
    // 预定义渠道配置
    HXB(60 * ONE_MINUTE, 4 * ONE_HOUR, ONE_DAY),
    GUOTONG(5 * ONE_MINUTE, 4 * ONE_HOUR, ONE_DAY),
    // 默认配置（必须放在最后）
    DEFAULT(30 * ONE_MINUTE, 4 * ONE_HOUR, ONE_DAY);

    private static final Map<String, ChannelDelayQueryConfigEnum> CHANNEL_MAP = new HashMap<>();

    static {
        // 初始化渠道映射
        for (ChannelDelayQueryConfigEnum config : values()) {
            CHANNEL_MAP.put(config.name(), config);
        }
    }

    private final long priorityA;  // 0-14天
    private final long priorityB;  // 14-30天
    private final long priorityC;  // 30-90天

    ChannelDelayQueryConfigEnum(long a, long b, long c) {
        this.priorityA = a;
        this.priorityB = b;
        this.priorityC = c;
    }

    public static ChannelDelayQueryConfigEnum getConfig(String channel) {
        return CHANNEL_MAP.getOrDefault(channel.toUpperCase(), DEFAULT);
    }

    public static Date getNextPriority(long timeMills, String channel) {
        long nextTimeSkip = -1;
        ChannelDelayQueryConfigEnum config = getConfig(channel);
        if (timeMills + 14 * ONE_DAY > System.currentTimeMillis()) {
            nextTimeSkip = config.getPriorityA();
        } else if (timeMills + 30 * ONE_DAY > System.currentTimeMillis()) {
            nextTimeSkip = config.getPriorityB();
        } else if (timeMills + 90 * ONE_DAY > System.currentTimeMillis()) {
            nextTimeSkip = config.getPriorityC();
        }

        if (nextTimeSkip > 0) {
            return new Date(System.currentTimeMillis() + nextTimeSkip);
        }

        try {
            return WosaiDateTimeUtils.parseDate(CommonConstants.PAUSE_PRIORITY);
        } catch (Exception e) {
            return new Date(System.currentTimeMillis() + ONE_HOUR);
        }
    }
}
