package com.wosai.upay.job.biz;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.*;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.core.bean.model.TermInfo;
import com.wosai.upay.core.bean.model.TradeExtConfigContentModel;
import com.wosai.upay.core.bean.request.TradeExtConfigCreateRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigQueryRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigUpdateRequest;
import com.wosai.upay.core.bean.response.TradeExtConfigQueryResponse;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.store.StoreBiz;
import com.wosai.upay.job.enume.ProviderTerminalBindConfigStatus;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.DO.PendingTasks;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import com.wosai.upay.job.model.ProviderTerminal;
import com.wosai.upay.job.model.ProviderTerminalBindConfig;
import com.wosai.upay.job.model.dto.ProviderTerminalContext;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.dao.ProviderTerminalDAO;
import com.wosai.upay.job.repository.ProviderTerminalTaskRepository;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractSysException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.terminal.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.wosai.upay.job.constant.ProviderTerminalConstants.FAIL;
import static com.wosai.upay.job.constant.ProviderTerminalConstants.SUCCESS;
import static com.wosai.upay.job.service.TaskResultServiceImpl.BELONG_LKLV3;

/**
 * @Description: 收单机构终端业务处理
 * <AUTHOR>
 * @Date: 2022/3/2 9:32 上午
 */
@Component
@Slf4j
public class ProviderTerminalBiz {
    @Autowired
    private ProviderTerminalMapper terminalMapper;

    @Autowired
    private ProviderFactory providerFactory;
    @Autowired
    private MonitorLog monitorLog;
    @Autowired
    private StoreBiz storeBiz;
    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private ProviderTerminalTaskRepository taskRepository;
    @Autowired
    ProviderTerminalMapper providerTerminalMapper;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    StoreService storeService;
    @Autowired
    private SupportService supportService;

    @Autowired
    ProviderTerminalBindConfigMapper providerTerminalBindConfigMapper;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    TerminalService terminalService;


    @Autowired
    private PendingTasksMapper pendingTasksMapper;
    @Autowired
    private ProviderTerminalDAO providerTerminalDAO;



    public static final String QM50 = "****************";

    public static final String FAIL_FORMAT = "商户SN:%s,子商户号:%s,原因:%s";
    private static final String BIND_TERMINAL_CONFIG = "子商户号绑定终端信息记录异常,商户SN:%s,终端号:%s,原因:%s";
    private static final String UNBIND_TERMINAL_CONFIG = "子商户号绑定终端信息记录异常,商户SN:%s,终端号:%s,原因:%s";

    /**
     * 使用银行多业务时终端绑定时,对应的银行终端类型
     */
    public static final Integer SN_TYPE_BANK_MERCHANT = 3;

    /**
     * 子商户下 绑定所有终端
     *
     * @param task
     */
    public void boundTerminal(ProviderTerminalTask task) {
        ProviderTerminalContext context = JSONObject.parseObject(task.getContext(), ProviderTerminalContext.class);
        //校验
        baseCheckBoundTerminal(context);
        Integer provider = context.getProvider();
        String subMerchant = context.getSubMerchant();

        BasicProvider handleProvider = providerFactory.getProvider(String.valueOf(provider));
        if (handleProvider == null) {
            throw new ContractBizException("未找到处理类");
        }

        final String merchantSn = task.getMerchant_sn();
        int bizExceptionCount = 0;
        int systemExceptionCount = 0;
        List<ProviderTerminal> terminalList;
        // CUA-10143 一个子商户号可能对应多个商户，为了防止查询错误，加上子商户号对应的商户号进行查询
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantIdAndMerchantSn(subMerchant, WosaiStringUtils.isNotEmpty(context.getBrandMerchantSn()) ? context.getBrandMerchantSn() : merchantSn);
        if (Objects.isNull(merchantProviderParams)) {
            throw new ContractBizException("子商户号交易参数不存在");
        }
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getParamsByProviderMerchantIdAndProviderAndPayWay(merchantProviderParams.getProvider_merchant_id(), BELONG_LKLV3.contains(provider) ? String.valueOf(ProviderEnum.PROVIDER_LAKALA_V3.getValue()) : String.valueOf(provider), PaywayEnum.ACQUIRER.getValue().toString());
        if (Objects.isNull(acquirerParams)) {
            throw new ContractBizException("收单机构交易参数不存在");
        }
        //根据task的任务类型判断是否需要更新所有provider_terminal中的信息 避免例如门店绑定也把provider_terminal都绑定,只有 BOUND_ALL_TERMINAL(1, "新增子商户号绑定所有终端"),才会绑定所有终端
        if (!Objects.equals(task.getType(), ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType())) {
            terminalList = Optional.ofNullable(terminalMapper.selectByCondition(
                                    new ProviderTerminal()
                                            .setMerchant_sn(merchantSn)
                                            .setProvider_terminal_id(context.getProviderTerminalId())
                                            .setAcquirer_merchant_id(acquirerParams.getPay_merchant_id())
                            )
                    ).orElseGet(ArrayList::new)
                    .stream()
                    .filter(term -> Objects.equals(term.getProvider_terminal_id(), context.getProviderTerminalId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(terminalList)) {
                log.warn("非新增子商户号绑定终端任务id:{}未找到对应的终端信息", task.getId());
            }
        } else {
            terminalList = terminalMapper.selectByMerchantSnAndProviderAndAcquirerMerchantId(merchantSn, BELONG_LKLV3.contains(provider) ? ProviderEnum.PROVIDER_LAKALA_V3.getValue() : provider, acquirerParams.getPay_merchant_id());
            //存量商户处理
            if (WosaiCollectionUtils.isEmpty(terminalList)) {
                //获取存量商户的终端号
                String termNo = handleProvider.getMerchantTermNo(merchantSn);
                if (StringUtils.isEmpty(termNo)) {
                    throw new ContractBizException("存量商户没有找到对应的终端号");
                }
                //创建大终端
                merchantConnectionProviderTerminal(merchantSn, termNo, acquirerParams.getPay_merchant_id(),
                        BELONG_LKLV3.contains(provider) ? ProviderEnum.PROVIDER_LAKALA_V3.getValue() : provider);
                return;
            }
        }
        //终端绑定
        for (ProviderTerminal terminalInfo : terminalList) {
            try {
                // 如果终端类型和子商户号类型不匹配就不绑定
                boolean isOnlineMatch = ProviderTerminal.ONLINE.equals(terminalInfo.getType())
                        && merchantProviderParams.isOnlineParams();

                boolean isOfflineMatch = ProviderTerminal.OFFLINE.equals(terminalInfo.getType())
                        && !merchantProviderParams.isOnlineParams();

                if (isOnlineMatch || isOfflineMatch) {
                    doBoundTerminal(task, context, terminalInfo, handleProvider);
                }
            } catch (ContractBizException e) {
                log.error("商户:{} 子商户号:{} 收单机构终端ID{} 子商户号绑定终端业务异常",
                        merchantSn, subMerchant, terminalInfo.getProvider_terminal_id(), e);
                bizExceptionCount++;
            } catch (Exception e) {
                systemExceptionCount++;
                log.error("商户:{} 子商户号:{} 收单机构终端ID{} 子商户号绑定终端系统异常",
                        merchantSn, subMerchant, terminalInfo.getProvider_terminal_id(), e);
            }
        }

        //task 状态处理
        if (systemExceptionCount == 0 && bizExceptionCount == 0) {
            //处理成功
            taskRepository.updateTaskStatusById(task.getId(), SUCCESS, "子商户号绑定终端成功");
        } else if (systemExceptionCount > 0) {
            taskRepository.exitBoundTerminalException(task, subMerchant);
        } else {
            //业务异常
            taskRepository.updateTaskStatusById(task.getId(), FAIL, "子商户号绑定终端失败");
        }

    }


    /**
     * 解绑所有终端
     *
     * @param task
     */
    public void unbindTerminal(ProviderTerminalTask task) {
        ProviderTerminalContext context = JSONObject.parseObject(task.getContext(), ProviderTerminalContext.class);
        //校验
        baseCheckBoundTerminal(context);
        final String providerTerminalId = context.getProviderTerminalId();
        if (Objects.isNull(providerTerminalId)) {
            throw new ContractBizException("收单机构终端Id不存在");
        }
        final String storeSn = context.getSqbStoreSn();
        if (Objects.isNull(storeSn)) {
            throw new ContractBizException("收钱吧门店号不存在");
        }
        Integer provider = context.getProvider();
        BasicProvider handleProvider = providerFactory.getProvider(String.valueOf(provider));
        if (handleProvider == null) {
            throw new ContractBizException("未找到处理类");
        }
        final String merchantSn = task.getMerchant_sn();
        final String subMerchant = task.getContextInfo().getSubMerchant();
        final String terminalId = task.getContextInfo().getProviderTerminalId();
        int bizExceptionCount = 0;
        int systemExceptionCount = 0;
        //终端解绑
        try {
            doUnbindTerminal(task, context, handleProvider);
        } catch (ContractBizException e) {
            bizExceptionCount++;
            log.error("商户:{} 子商户号:{} 收单机构终端ID{} 子商户号解绑业务异常",
                    merchantSn, subMerchant, terminalId, e);
        } catch (Exception e) {
            systemExceptionCount++;
            log.error("商户:{} 子商户号:{} 收单机构终端ID{} 子商户号解绑业务异常",
                    merchantSn, subMerchant, terminalId, e);
        }
        //task 状态处理
        if (systemExceptionCount == 0 && bizExceptionCount == 0) {
            //处理成功
            taskRepository.updateTaskStatusById(task.getId(), SUCCESS, "子商户号解绑终端成功");
        } else if (systemExceptionCount > 0) {
            taskRepository.exitBoundTerminalException(task, subMerchant);
        } else {
            //业务异常
            taskRepository.updateTaskStatusById(task.getId(), FAIL, "子商户号解绑终端失败");
        }

    }

    public void updateTerminalByStoreSn(String storeSn) {
        if (StringUtil.empty(storeSn)) {
            return;
        }
        List<ProviderTerminal> terminals = providerTerminalMapper.selectByStoreSn(storeSn);
        terminals.forEach(terminal -> updateTerminal(terminal));
    }

    public void updateTerminal(ProviderTerminal providerTerminal) {
        if (StringUtil.empty(providerTerminal.getBound_sub_mch_ids())) {
            return;
        }
        String[] subMchIds = providerTerminal.getBound_sub_mch_ids().split(",");
        for (String subMchId : subMchIds) {
            doUpdateTerminal(providerTerminal, subMchId);
        }

    }

    private void doUpdateTerminal(ProviderTerminal providerTerminal, String subMchId) {
        try {
            MerchantProviderParams params = merchantProviderParamsMapper.getByPayMerchantId(subMchId);
            if (params == null) {
                return;
            }
            BasicProvider handleProvider = providerFactory.getProvider(String.valueOf(params.getProvider()));
            UpdateTermInfoDTO updateTermInfoDTO = new UpdateTermInfoDTO();
            updateTermInfoDTO.setDeviceId(providerTerminal.getProvider_terminal_id());
            updateTermInfoDTO.setMerchantSn(params.getMerchant_sn());
            updateTermInfoDTO.setStoreSn(providerTerminal.getStore_sn());
            updateTermInfoDTO.setSubMchId(subMchId);
            handleProvider.updateTerminal(updateTermInfoDTO, params.getPayway(), providerTerminal.getTerminal_sn());
        } catch (Exception e) {
            log.warn("更新终端信息异常:", e);
        }

    }

    /**
     * 创建商户级别的provider_terminal
     *
     * @param merchantSn         商户号
     * @param providerTerminalId 终端号
     * @param acquirerMerchantId 收单机构商户号
     * @param provider           通道
     */
    @Transactional(rollbackFor = Exception.class)
    public void createMerchantProviderTerminal(String merchantSn, String providerTerminalId, String acquirerMerchantId, int provider) {
        //新增 provider_terminal表
        ProviderTerminal terminal = new ProviderTerminal();
        terminal.setMerchant_sn(merchantSn);
        terminal.setProvider(provider);
        terminal.setProvider_terminal_id(providerTerminalId);
        terminal.setAcquirer_merchant_id(acquirerMerchantId);
        terminalMapper.insertSelective(terminal);
    }


    /**
     * 商户纬度 关联收单机构终端ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void merchantConnectionProviderTerminal(String merchantSn, String providerTerminalId, String acquirerMerchantId, int provider) {
        //新增 provider_terminal表
        ProviderTerminal terminal = new ProviderTerminal();
        terminal.setMerchant_sn(merchantSn);
        terminal.setProvider(provider);
        terminal.setProvider_terminal_id(providerTerminalId);
        terminal.setAcquirer_merchant_id(acquirerMerchantId);
        terminalMapper.insertSelective(terminal);

        //查询是否存在
        TradeExtConfigQueryRequest queryRequest = new TradeExtConfigQueryRequest();
        queryRequest.setProvider(provider);
        queryRequest.setSn(merchantSn);
        queryRequest.setSnType(TradeExtConfigQueryRequest.SN_TYPE_MERCHANT);
        TradeExtConfigQueryResponse queryResponse = tradeConfigService.queryTradeExtConfig(queryRequest);
        TradeExtConfigContentModel content = new TradeExtConfigContentModel();
        content.setTermId(providerTerminalId);
        if (Objects.isNull(queryResponse)) {
            //调用交易新增接口
            TradeExtConfigCreateRequest request = new TradeExtConfigCreateRequest();
            request.setSn(merchantSn);
            //0：商户、1：门店、2：终端
            request.setSnType(TradeExtConfigCreateRequest.SN_TYPE_MERCHANT);
            request.setProvider(provider);
            request.setContent(content);
            tradeConfigService.createTradeExtConfig(request);
        } else {
            //调用交易修改接口
            TradeExtConfigUpdateRequest tradeExtConfigUpdateRequest = new TradeExtConfigUpdateRequest();
            tradeExtConfigUpdateRequest.setProvider(provider);
            tradeExtConfigUpdateRequest.setSn(merchantSn);
            tradeExtConfigUpdateRequest.setSnType(TradeExtConfigUpdateRequest.SN_TYPE_MERCHANT);
            tradeExtConfigUpdateRequest.setContent(content);
            tradeConfigService.updateTradeExtConfig(tradeExtConfigUpdateRequest);
        }
        //清除交易缓存
        supportService.removeCachedParams(merchantSn);
    }

    /**
     * 终端纬度 关联收单机构终端ID
     *
     * @param merchantSn         商户号
     * @param providerTerminalId 收单机构终端号
     * @param provider           收单机构对应的收钱吧提供者
     * @param terminalAppid      收钱吧终端
     * @param terminalSn
     * @param storeSn
     */
    @Transactional(rollbackFor = Exception.class)
    public void sqbTerminalConnectionProviderTerminal(String merchantSn, String providerTerminalId, String acquirerMerchantId, int provider
            , String terminalAppid, String terminalSn, String storeSn) {
        ProviderTerminal terminal = new ProviderTerminal();
        terminal.setMerchant_sn(merchantSn);
        terminal.setProvider(provider);
        terminal.setProvider_terminal_id(providerTerminalId);
        terminal.setAcquirer_merchant_id(acquirerMerchantId);
        terminal.setTerminal_appid(terminalAppid);
        // 区分这是一个线上终端还是线下终端
        boolean isOnlineTerminal = applicationApolloConfig.getOnlineVendorAppids().contains(terminalAppid);
        if (isOnlineTerminal) {
            terminal.setType(ProviderTerminal.ONLINE);
        } else {
            terminal.setType(ProviderTerminal.OFFLINE);
        }
        terminal.setTerminal_sn(terminalSn);
        terminal.setStore_sn(storeSn);
        terminalMapper.insertSelective(terminal);
    }


    /**
     * 创建或者更新终端级别trade_extra_config
     *
     * @param merchantSn
     * @param providerTerminalId
     * @param provider
     * @param terminalAppid
     * @param terminalSn
     */
    public void createOrUpdateTerminalExtra(String merchantSn, String providerTerminalId, int provider, String terminalAppid, String terminalSn) {
        if (StringUtils.isBlank(terminalSn)) {
            return;
        }
        //终端信息
        Map terminalMap = terminalService.getTerminalByTerminalSn(terminalSn);
        //简易智能新pos返回两个终端号但是只需要绑定普通的那个终端号
        List<String> simpleSuperPosList = Lists.newArrayList(applicationApolloConfig.getSimpleSuperPos());
        Boolean simpleSuperPos = simpleSuperPosList.contains(terminalAppid);
        Boolean needTermInfo = Boolean.FALSE;
        TermInfo termInfo = new TermInfo();
        if (simpleSuperPos && Objects.equals(provider, ProviderEnum.PROVIDER_LAKALA_V3.getValue())) {
            termInfo.setTermType(LklTermType.TYPE_04.getValue());
            termInfo.setTermId(providerTerminalId);
            termInfo.setSerialNum(BeanUtil.getPropString(terminalMap, Terminal.DEVICE_FINGERPRINT));
            needTermInfo = Boolean.TRUE;
        }
        //富友银行卡刷卡设备终端类型写入交易
        List<String> fYSuperPosList = Lists.newArrayList(applicationApolloConfig.getFySimpleSuperPos());
        Boolean fYSuperPos = fYSuperPosList.contains(terminalAppid);
        if (fYSuperPos && Objects.equals(provider, ProviderEnum.PROVIDER_FUYOU.getValue())) {
            termInfo.setTermType(FuYouTermType.TYPE_04.getValue());
            termInfo.setTermId(providerTerminalId);
            termInfo.setSerialNum(BeanUtil.getPropString(terminalMap, Terminal.DEVICE_FINGERPRINT));
            needTermInfo = Boolean.TRUE;
        }

        if (Objects.equals(provider, ProviderEnum.PROVIDER_HXB.getValue())
                && Objects.equals(QM50, BeanUtil.getPropString(terminalMap, Terminal.VENDOR_APP_APPID))) {
            termInfo.setTermType(HxbTermType.TYPE_10.getValue());
            termInfo.setTermId(providerTerminalId);
            termInfo.setSerialNum(BeanUtil.getPropString(terminalMap, Terminal.DEVICE_FINGERPRINT));
            needTermInfo = Boolean.TRUE;
        }

        //查询是否存在
        final TradeExtConfigQueryRequest queryRequest = new TradeExtConfigQueryRequest();
        queryRequest.setSn(terminalSn);
        queryRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_TERMINAL);
        queryRequest.setProvider(provider);
        final TradeExtConfigQueryResponse tradeExtConfig = tradeConfigService.queryTradeExtConfig(queryRequest);
        log.info("queryTradeExtConfig  request{}, response{},", JSONObject.toJSONString(queryRequest), JSONObject.toJSONString(tradeExtConfig));
        if (!Objects.isNull(tradeExtConfig)) {
            TradeExtConfigUpdateRequest updateRequest = new TradeExtConfigUpdateRequest();
            updateRequest.setSn(terminalSn);
            //0：商户、1：门店、2：终端
            updateRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_TERMINAL);
            updateRequest.setProvider(provider);
            TradeExtConfigContentModel content = new TradeExtConfigContentModel();
            content.setTermId(providerTerminalId);
            if (needTermInfo) {
                content.setTermInfo(termInfo);
                //支付组限制如果更新的时候有了termInfo就不能有termId了
                content.setTermId(null);
            }
            updateRequest.setContent(content);
            tradeConfigService.updateTradeExtConfig(updateRequest);
        } else {
            //调用交易接口
            TradeExtConfigCreateRequest request = new TradeExtConfigCreateRequest();
            request.setSn(terminalSn);
            //0：商户、1：门店、2：终端
            request.setSnType(TradeExtConfigCreateRequest.SN_TYPE_TERMINAL);
            request.setProvider(provider);
            TradeExtConfigContentModel content = new TradeExtConfigContentModel();
            content.setTermId(providerTerminalId);
            if (needTermInfo) {
                content.setTermInfo(termInfo);
            }
            request.setContent(content);
            tradeConfigService.createTradeExtConfig(request);
        }
        //清除交易缓存
        supportService.removeCachedParams(merchantSn);
    }


    /**
     * 删除trade_extra_config中的TermInfo
     *
     * @param merchantSn
     * @param provider
     * @param terminalSn
     */
    public void deleteContentTermInfo(String merchantSn, int provider, String terminalSn) {
        //查询是否存在
        final TradeExtConfigQueryRequest queryRequest = new TradeExtConfigQueryRequest();
        queryRequest.setSn(terminalSn);
        queryRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_TERMINAL);
        queryRequest.setProvider(provider);
        final TradeExtConfigQueryResponse tradeExtConfig = tradeConfigService.queryTradeExtConfig(queryRequest);
        if (Objects.isNull(tradeExtConfig)) {
            return;
        } else {
            TradeExtConfigUpdateRequest updateRequest = new TradeExtConfigUpdateRequest();
            updateRequest.setSn(tradeExtConfig.getSn());
            //0：商户、1：门店、2：终端
            updateRequest.setSnType(tradeExtConfig.getSnType());
            updateRequest.setProvider(tradeExtConfig.getProvider());
            //老content
            TradeExtConfigContentModel oldContent = tradeExtConfig.getContent();
            //新content
            TradeExtConfigContentModel newContent = new TradeExtConfigContentModel();
            TermInfo termInfo = oldContent.getTermInfo();
            newContent.setTermId(termInfo.getTermId());
            newContent.setLimitPayer(oldContent.getLimitPayer());
            updateRequest.setContent(newContent);
            tradeConfigService.updateTradeExtConfig(updateRequest);
        }
        //清除交易缓存
        supportService.removeCachedParams(merchantSn);
    }


    /**
     * 门店纬度 关联收单机构终端ID
     *
     * @param merchantSn         商户号
     * @param providerTerminalId 收单机构终端号
     * @param provider           收单机构对应的收钱吧提供者
     * @param storeSn            收钱吧门店号
     */
    @Transactional(rollbackFor = Exception.class)
    public void sqbStoreTerminalConnectionProviderTerminal(String merchantSn, String providerTerminalId, String acquirerMerchantId, int provider, String storeSn) {
        //新增 provider_terminal表
        ProviderTerminal terminal = new ProviderTerminal();
        terminal.setMerchant_sn(merchantSn);
        terminal.setProvider(provider);
        terminal.setProvider_terminal_id(providerTerminalId);
        terminal.setAcquirer_merchant_id(acquirerMerchantId);
        terminal.setStore_sn(storeSn);
        terminalMapper.insertSelective(terminal);
    }

    /**
     * 创建或者更新门店级别trade_extra_config
     *
     * @param providerTerminal 收单机构终端信息
     * @param context          绑定任务上下文信息
     */
    public void createOrUpdateStoreExtra(ProviderTerminal providerTerminal, ProviderTerminalContext context, String storeSn, Integer payWay) {
        if (StringUtils.isBlank(storeSn)) {
            return;
        }
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(providerTerminal.getAcquirer_merchant_id());
        // 如果acquirerMerchantId和商户号不是同一个，那就说明这个是品牌的，则使用门店号+子商户号作为sn。品牌商户支付宝和微信都要分别写入
        if (!WosaiStringUtils.equals(providerTerminal.getMerchant_sn(), merchantProviderParams.getMerchant_sn())) {
            doHandleBrandBindResult(providerTerminal, storeSn, context);
            return;
        }
        if (!Objects.equals(payWay, PaywayEnum.WEIXIN.getValue())) {
            return;
        }
        //查询是否存在
        final TradeExtConfigQueryRequest queryRequest = new TradeExtConfigQueryRequest();
        queryRequest.setSn(storeSn);
        queryRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_STORE);
        queryRequest.setProvider(providerTerminal.getProvider());
        final TradeExtConfigQueryResponse tradeExtConfig = tradeConfigService.queryTradeExtConfig(queryRequest);
        if (!Objects.isNull(tradeExtConfig)) {
            TradeExtConfigUpdateRequest updateRequest = new TradeExtConfigUpdateRequest();
            updateRequest.setSn(storeSn);
            //0：商户、1：门店、2：终端
            updateRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_STORE);
            updateRequest.setProvider(providerTerminal.getProvider());
            TradeExtConfigContentModel content = new TradeExtConfigContentModel();
            content.setTermId(providerTerminal.getProvider_terminal_id());
            updateRequest.setContent(content);
            tradeConfigService.updateTradeExtConfig(updateRequest);
        } else {
            //调用交易接口
            TradeExtConfigCreateRequest request = new TradeExtConfigCreateRequest();
            request.setSn(storeSn);
            //0：商户、1：门店、2：终端
            request.setSnType(TradeExtConfigCreateRequest.SN_TYPE_STORE);
            request.setProvider(providerTerminal.getProvider());
            TradeExtConfigContentModel content = new TradeExtConfigContentModel();
            content.setTermId(providerTerminal.getProvider_terminal_id());
            request.setContent(content);
            tradeConfigService.createTradeExtConfig(request);
        }
        //清除交易缓存
        supportService.removeCachedParams(providerTerminal.getMerchant_sn());
    }

    private void doHandleBrandBindResult(ProviderTerminal providerTerminal, String storeSn, ProviderTerminalContext context) {
        // 则使用门店号+子商户号作为sn。品牌商户支付宝和微信都要分别写入
        String sn = String.format("%s:%s", storeSn, context.getSubMerchant());
        final TradeExtConfigQueryRequest queryRequest = new TradeExtConfigQueryRequest();
        queryRequest.setSn(sn);
        queryRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_STORE_SUB_MCH);
        queryRequest.setProvider(providerTerminal.getProvider());
        final TradeExtConfigQueryResponse tradeExtConfig = tradeConfigService.queryTradeExtConfig(queryRequest);
        if (!Objects.isNull(tradeExtConfig)) {
            TradeExtConfigUpdateRequest updateRequest = new TradeExtConfigUpdateRequest();
            updateRequest.setSn(sn);
            updateRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_STORE_SUB_MCH);
            updateRequest.setProvider(providerTerminal.getProvider());
            TradeExtConfigContentModel content = new TradeExtConfigContentModel();
            content.setTermId(providerTerminal.getProvider_terminal_id());
            updateRequest.setContent(content);
            tradeConfigService.updateTradeExtConfig(updateRequest);
        } else {
            TradeExtConfigCreateRequest request = new TradeExtConfigCreateRequest();
            request.setSn(sn);
            request.setSnType(TradeExtConfigCreateRequest.SN_TYPE_STORE_SUB_MCH);
            request.setProvider(providerTerminal.getProvider());
            TradeExtConfigContentModel content = new TradeExtConfigContentModel();
            content.setTermId(providerTerminal.getProvider_terminal_id());
            request.setContent(content);
            tradeConfigService.createTradeExtConfig(request);
        }
    }

    public void initLklBankCardTermNoTask(String merchantSn, String termNo, String vendorAppAppid, String storeSn, String terminalSn) {
        MerchantProviderParams params = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        if (Objects.isNull(params)) {
            return;
        }
        final String subMerchant = params.getProvider_merchant_id();
        insertPosBankQuery(merchantSn, storeSn, terminalSn, termNo, subMerchant);
        //生产pos银行卡刷卡记录
        ProviderTerminal providerTerminal = new ProviderTerminal()
                .setMerchant_sn(merchantSn)
                .setTerminal_appid(vendorAppAppid)
                .setProvider(ProviderEnum.PROVIDER_LAKALA_V3.getValue())
                .setProvider_terminal_id(termNo)
                .setStore_sn(storeSn)
                .setTerminal_sn(terminalSn);
        insertTerminalBindResult(providerTerminal, subMerchant, PaywayEnum.BANK_CARD.getValue(), null, ProviderTerminalBindConfigStatus.PENDING);
    }

    public void syncTermResultQuery(MerchantProviderParams params, ProviderTerminalBindConfig config) {
        BasicProvider provider = providerFactory.getProviderByRule(params.getContract_rule());
        ContractResponse response = provider.queryTermContractResult(params.getProvider_merchant_id(), config.getProvider_terminal_id());
        if (response.isSystemFail()) {
            return;
        }
        boolean remoteResult = response.isSuccess();
        String tradeStatus = WosaiMapUtils.getString(response.getTradeParam(), "status");
        ProviderTerminalBindConfigStatus status = remoteResult ? ProviderTerminalBindConfigStatus.BIND_SUCCESS :
                LakalaConstant.UNION_PAY_DELETE.equals(tradeStatus) ? ProviderTerminalBindConfigStatus.DELETE : ProviderTerminalBindConfigStatus.UNBIND;
        updateTerminalBindResult(config, response, status);
    }

    /**
     * 绑定终端
     *
     * @param task
     * @param context
     * @param terminalInfo
     * @param handleProvider
     * @return
     */
    private void doBoundTerminal(ProviderTerminalTask task, ProviderTerminalContext context, ProviderTerminal terminalInfo, BasicProvider handleProvider) {
        log.info("商户:{} 任务id:{} 子商户号绑定终端开始", task.getMerchant_sn(), task.getId());
        //不是一个商户不用处理,避免数据库不支持大小写的数据导致错误,这算是兜底逻辑
        if (!Objects.equals(task.getMerchant_sn(), terminalInfo.getMerchant_sn())) {
            return;
        }
        String boundSubMchIds = terminalInfo.getBound_sub_mch_ids();
        String subMerchant = context.getSubMerchant();
        Integer payWay = context.getPayWay();

        if (StringUtils.isNotBlank(boundSubMchIds) && boundSubMchIds.contains(subMerchant)) {
            log.warn("终端已经绑定完成，taskId:{}商户:{} 子商户号:{} 已绑定终端ID:{}", task.getId(), task.getMerchant_sn(), subMerchant, terminalInfo.getProvider_terminal_id());
            modifyTradeExtra(task, terminalInfo, payWay);
            return;
        }
        //参数组装
        ContractResponse terminalResp;
        if ((PaywayEnum.UNIONPAY.getValue().equals(payWay) && ProviderEnum.PROVIDER_UION_OPEN.getValue().equals(context.getProvider()))
                || (Objects.equals(PaywayEnum.UNIONPAY.getValue(), payWay) && Objects.equals(ProviderEnum.PROVIDER_LKL_OPEN.getValue(), context.getProvider()))) {
            String merCupNo = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(task.getMerchant_sn(), ProviderEnum.PROVIDER_LAKALA_V3.getValue()).getProvider_merchant_id();
            terminalResp = handleProvider.queryTermContractResult(merCupNo, terminalInfo.getProvider_terminal_id());
            //拉卡拉银联绑定未成功重试
            handleUnionPayTerminalResponse(terminalResp, task, terminalInfo, subMerchant);
        } else {
            AddTermInfoDTO termInfoDTO = assembleBoundTerminal(task, terminalInfo, subMerchant);
            terminalResp = handleProvider.boundTerminal(termInfoDTO, payWay, context.getTerminalSn());
        }
        log.info("商户:{} 子商户号:{} 门店号:{} 收单机构终端ID{} 子商户号绑定终端返回值:{}",
                task.getMerchant_sn(), subMerchant, terminalInfo.getStore_sn(), terminalInfo.getProvider_terminal_id(), terminalResp);
        try {
            saveTerminalConfigAfterBind(terminalInfo, subMerchant, payWay, terminalResp);
        } catch (Exception e) {
            log.warn(String.format(BIND_TERMINAL_CONFIG, task.getMerchant_sn(), terminalInfo.getProvider_terminal_id(), e.getMessage()));
        }
        //根据结果后续处理
        if (terminalResp.isSuccess()) {
            //更新终端数据到交易组,这里只会更新门店级别和收钱吧终端级别
            modifyTradeExtra(task, terminalInfo, payWay);
            //再次查询获取已绑定的子商户号
            ProviderTerminal terminal = terminalMapper.selectByPrimaryKey(terminalInfo.getId());
            if (StrUtil.contains(terminal.getBound_sub_mch_ids(), subMerchant)) {
                //存在则不记录
                return;
            }
            String newBoundSubMchIds = StringUtils.isBlank(terminal.getBound_sub_mch_ids()) ? subMerchant : terminal.getBound_sub_mch_ids() + "," + subMerchant;
            terminalMapper.updateBoundSubMchIdsById(newBoundSubMchIds, terminalInfo.getId());
        } else if (terminalResp.isBusinessFail()) {
            monitorLog.recordMonitor("子商户号绑定终端业务异常", String.format(FAIL_FORMAT, task.getMerchant_sn(), subMerchant, terminalResp.getMessage()));
            throw new ContractBizException(terminalResp.getMessage());
        } else {
            throw new ContractSysException("调用contract接口返回500 : " + terminalResp.getMessage());
        }
    }

    /**
     * 处理银联终端报备逻辑
     *
     * @param terminalResp 终端响应
     * @param task         任务对象
     * @param terminalInfo 终端信息
     * @param subMerchant  子商户号
     */
    private void handleUnionPayTerminalResponse(ContractResponse terminalResp,
                                                ProviderTerminalTask task,
                                                ProviderTerminal terminalInfo,
                                                String subMerchant) {
        if (terminalResp == null) {
            log.warn("终端响应为空，商户:{}", task.getMerchant_sn());
            return;
        }

        // 仅处理未报备成功的情况
        if (!StrUtil.contains(terminalResp.getMessage(), "银联终端报备记录为空")) {
            return;
        }

        // 首次重试插入记录
        if (task.getRetry() < 1) {
            insertTerminalBindResult(terminalInfo, subMerchant,
                    PaywayEnum.UNIONPAY.getValue(), null,
                    ProviderTerminalBindConfigStatus.PENDING);
        }

        // 允许重试时记录日志并抛异常
        if (task.getRetry() < 9) {
            log.info("银联终端未报备 商户:{} 子商户号:{} 门店号:{} 终端ID:{} 响应:{}",
                    task.getMerchant_sn(), subMerchant,
                    terminalInfo.getStore_sn(), terminalInfo.getProvider_terminal_id(),
                    terminalResp);
            throw new ContractSysException("银联终端报备记录为空");
        }
    }


    private void saveTerminalConfigAfterBind(ProviderTerminal providerTerminal, String subMerchant, Integer payway, ContractResponse response) {
        if (response.isSystemFail()) {
            return;
        }
        String tradeStatus = WosaiMapUtils.getString(response.getTradeParam(), "status");
        ProviderTerminalBindConfigStatus status = response.isSuccess() ? ProviderTerminalBindConfigStatus.BIND_SUCCESS :
                LakalaConstant.UNION_PAY_DELETE.equals(tradeStatus) ? ProviderTerminalBindConfigStatus.DELETE : ProviderTerminalBindConfigStatus.BIND_FAIL;
        ProviderTerminalBindConfig config = providerTerminalBindConfigMapper.selectByProviderTerminalIdAndSubMchId(providerTerminal.getProvider_terminal_id(), subMerchant);
        if (Objects.isNull(config)) {
            insertTerminalBindResult(providerTerminal, subMerchant, payway, response, status);
        } else {
            updateTerminalBindResult(config, response, status);
        }
    }

    private void saveTerminalConfigAfterUnbind(ProviderTerminal providerTerminal, String subMerchant, ContractResponse response) {
        if (!response.isSuccess()) {
            return;
        }
        ProviderTerminalBindConfig config = providerTerminalBindConfigMapper.selectByProviderTerminalIdAndSubMchId(providerTerminal.getProvider_terminal_id(), subMerchant);
        if (Objects.isNull(config)) {
            return;
        }
        if (!ProviderTerminalBindConfigStatus.BIND_SUCCESS.getStatus().equals(config.getStatus())) {
            return;
        }
        updateTerminalBindResult(config, response, ProviderTerminalBindConfigStatus.UNBIND);
    }

    private void insertTerminalBindResult(ProviderTerminal providerTerminal, String subMerchant, Integer payway, ContractResponse response, ProviderTerminalBindConfigStatus status) {
        ProviderTerminalBindConfig config = new ProviderTerminalBindConfig(providerTerminal);
        response = Optional.ofNullable(response).orElseGet(ContractResponse::new);
        config.setPayway(payway)
                .setSub_mch_id(subMerchant)
                .setRequest_body(JSON.toJSONString(response.getRequestParam()))
                .setResponse_body(JSON.toJSONString(response.getResponseParam()))
                .setResult(response.getMessage())
                .setStatus(status.getStatus())
                .setId(UUID.randomUUID().toString());
        providerTerminalBindConfigMapper.insertSelective(config);
    }


    /**
     * 保存成功绑定的终端信息
     * @param providerTerminal
     * @param subMerchant
     * @param payway
     * @param response
     */
    public void insertSuccessTerminalBindResult(ProviderTerminal providerTerminal, String subMerchant, Integer payway, ContractResponse response) {
        ProviderTerminalBindConfig config = new ProviderTerminalBindConfig(providerTerminal);
        response = Optional.ofNullable(response).orElseGet(ContractResponse::new);
        config.setPayway(payway)
                .setSub_mch_id(subMerchant)
                .setRequest_body(JSON.toJSONString(response.getRequestParam()))
                .setResponse_body(JSON.toJSONString(response.getResponseParam()))
                .setResult("成功")
                .setStatus(ProviderTerminalBindConfigStatus.BIND_SUCCESS.getStatus())
                .setId(UUID.randomUUID().toString());
        providerTerminalBindConfigMapper.insertSelective(config);
    }


    private void updateTerminalBindResult(ProviderTerminalBindConfig config, ContractResponse response, ProviderTerminalBindConfigStatus status) {
        //数据库状态
        final Integer dbStatus = config.getStatus();
        final Integer currentStatus = status.getStatus();
        if (dbStatus.equals(currentStatus)) {
            return;
        }
        ProviderTerminalBindConfig update = new ProviderTerminalBindConfig()
                .setId(config.getId())
                .setResult(response.getMessage())
                .setRequest_body(JSON.toJSONString(response.getRequestParam()))
                .setResponse_body(JSON.toJSONString(response.getResponseParam()))
                .setStatus(status.getStatus());
        providerTerminalBindConfigMapper.updateByPrimaryKey(update);
    }

    /**
     * 解绑终端
     *
     * @param task
     * @param context
     * @param handleProvider
     * @return
     */
    private void doUnbindTerminal(ProviderTerminalTask task, ProviderTerminalContext context, BasicProvider handleProvider) {
        String subMerchant = context.getSubMerchant();
        Integer payWay = context.getPayWay();
        final String terminalSn = context.getTerminalSn();
        //参数组装
        LogOutTermInfoDTO termInfoDTO = buildDTO(task, context, subMerchant);
        ContractResponse terminalResp = handleProvider.unbindTerminal(termInfoDTO, payWay, context.getTerminalSn());
        final String providerTerminalId = context.getProviderTerminalId();
        log.info("商户:{} 子商户号:{} 收单机构终端ID{} 子商户号解绑返回值:{}",
                task.getMerchant_sn(), subMerchant, providerTerminalId, JSONObject.toJSONString(terminalResp));

        if (terminalResp.isSuccess()) {
            //再次查询获取已绑定的子商户号
            ProviderTerminal providerTerminal = new ProviderTerminal()
                    .setProvider_terminal_id(providerTerminalId)
                    .setMerchant_sn(task.getMerchant_sn())
                    .setStore_sn(context.getSqbStoreSn())
                    .setProvider(BELONG_LKLV3.contains(context.getProvider()) ? ProviderEnum.PROVIDER_LAKALA_V3.getValue() : context.getProvider());
            //不存在不需要解绑
            final List<ProviderTerminal> terminalList = providerTerminalMapper.selectByCondition(providerTerminal);
            if (CollectionUtils.isEmpty(terminalList)) {
                return;
            }
            ProviderTerminal terminal;
            if (!StringUtils.isEmpty(terminalSn)) {
                terminal = terminalList.stream().filter(ter -> Objects.equals(ter.getTerminal_sn(), terminalSn)).findAny().orElse(null);
            } else {
                terminal = terminalList.stream().filter(ter -> Objects.nonNull(ter.getTerminal_sn())).findAny().orElse(null);
            }
            if (Objects.isNull(terminal)) {
                return;
            }
            try {
                saveTerminalConfigAfterUnbind(terminal, subMerchant, terminalResp);
            } catch (Exception e) {
                log.warn(String.format(UNBIND_TERMINAL_CONFIG, task.getMerchant_sn(), terminal.getProvider_terminal_id(), e.getMessage()), e);
            }
            //没有绑定子商户号不需要解绑
            if (StringUtils.isBlank(terminal.getBound_sub_mch_ids())) {
                terminalMapper.deleteByPrimaryKey(terminal.getId());
                return;
            }
            //删除
            final List<String> subMerchantList = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(terminal.getBound_sub_mch_ids());
            final ArrayList<String> newList = Lists.newArrayList(subMerchantList);
            if (subMerchantList.contains(subMerchant)) {
                //成功就删除对应的子商户号
                newList.remove(subMerchant);
                if (CollectionUtils.isEmpty(newList)) {
                    //所有子商户号删除成功
                    terminalMapper.deleteByPrimaryKey(terminal.getId());
                } else {
                    terminalMapper.updateBoundSubMchIdsById(Joiner.on(",").join(newList), terminal.getId());
                }
            }
        } else if (terminalResp.isBusinessFail()
                && StringUtils.isNotBlank(terminalResp.getMessage())
                && terminalResp.getMessage().contains("该终端不存在，请进行新增操作")) {
            //抛出系统异常,在上层中会被捕获并且重试
            throw new ContractSysException(terminalResp.getMessage());
        } else if (terminalResp.isBusinessFail()) {
            monitorLog.recordMonitor("子商户号解绑终端业务异常", String.format(FAIL_FORMAT, task.getMerchant_sn(), subMerchant, terminalResp.getMessage()));
            throw new ContractBizException(terminalResp.getMessage());
        } else if (terminalResp.isSystemFail()) {
            //系统异常延迟一段时间
            throw new ContractSysException("调用contract 接口 返回500" + terminalResp.getMessage());
        }
    }

    /**
     * 基础校验
     *
     * @param context
     */
    private void baseCheckBoundTerminal(ProviderTerminalContext context) {
        if (context == null) {
            throw new ContractBizException("task 处理上下文不能为空");
        }
        if (context.getProvider() == null) {
            throw new ContractBizException("收单机构provider不能为空");
        }
        //兼容之前华夏线下导入云闪付数据记录子商户号
        if (StringUtils.isBlank(context.getSubMerchant())
                && !Objects.equals(context.getProvider(), ProviderEnum.PROVIDER_HXB.getValue())) {
            throw new ContractBizException("子商户号不能为空");
        }
        if (context.getPayWay() == null) {
            throw new ContractBizException("payWay不能为空");
        }
    }

    /**
     * 参数组装
     *
     * @param terminalTask
     * @param terminalInfo
     * @param subMerchant
     * @return
     */
    public AddTermInfoDTO assembleBoundTerminal(ProviderTerminalTask terminalTask, ProviderTerminal terminalInfo, String subMerchant) {
        AddTermInfoDTO termInfoDTO = new AddTermInfoDTO();
        termInfoDTO.setSubMchId(subMerchant);
        termInfoDTO.setMerchantSn(terminalTask.getMerchant_sn());
        termInfoDTO.setDeviceId(terminalInfo.getProvider_terminal_id());
        termInfoDTO.setProviderMerNo(terminalInfo.getAcquirer_merchant_id());

        //没有门店SN 默认取第一家门店
        String storeSn = terminalInfo.getStore_sn();
        if (StringUtils.isBlank(storeSn)) {
            storeSn = storeBiz.getFirstStoreSnByMerchantSn(terminalTask.getMerchant_sn());
        }

        termInfoDTO.setStoreSn(storeSn);

        return termInfoDTO;

    }


    /**
     * 参数组装
     *
     * @param terminalInfo
     * @param subMerchant
     * @return
     */
    public AddTermInfoDTO assembleBoundTerminal(String merchantSn, ProviderTerminal terminalInfo, String subMerchant) {
        AddTermInfoDTO termInfoDTO = new AddTermInfoDTO();
        termInfoDTO.setSubMchId(subMerchant);
        termInfoDTO.setMerchantSn(merchantSn);
        termInfoDTO.setDeviceId(terminalInfo.getProvider_terminal_id());

        //没有门店SN 默认取第一家门店
        String storeSn = terminalInfo.getStore_sn();
        if (StringUtils.isBlank(storeSn)) {
            storeSn = storeBiz.getFirstStoreSnByMerchantSn(merchantSn);
        }

        termInfoDTO.setStoreSn(storeSn);

        return termInfoDTO;

    }


    /**
     * 参数组装
     *
     * @param terminalTask
     * @param terminalContext
     * @param subMerchant
     * @return
     */
    public LogOutTermInfoDTO buildDTO(ProviderTerminalTask terminalTask, ProviderTerminalContext terminalContext, String subMerchant) {
        LogOutTermInfoDTO termInfoDTO = new LogOutTermInfoDTO();
        termInfoDTO.setSubMchId(subMerchant);
        termInfoDTO.setMerchantSn(terminalTask.getMerchant_sn());
        termInfoDTO.setDeviceId(terminalContext.getProviderTerminalId());

        //没有门店SN 默认取第一家门店
        String storeSn = terminalContext.getSqbStoreSn();
        if (StringUtils.isBlank(storeSn)) {
            storeSn = storeBiz.getFirstStoreSnByMerchantSn(terminalTask.getMerchant_sn());
        }

        termInfoDTO.setStoreSn(storeSn);

        return termInfoDTO;

    }

    /**
     * 收钱吧终端与当前子商户是否已经绑定,已绑定返回空集合,未绑定则返回需要绑定的交易参数的集合
     *
     * @param providerTerminal 当前终端记录
     * @param params           商户在某个通道下的支付宝和微信交易参数
     * @return
     */
    public List<MerchantProviderParams> getNotSyncMerchantProviderParams(ProviderTerminal providerTerminal, List<MerchantProviderParams> params) {
        // 组装查询条件
        //子商户集合
        final Set<String> subMerchantNoSet = params.parallelStream().map(param -> param.getPay_merchant_id())
                .filter(Objects::nonNull).collect(Collectors.toSet());
        //是否已经报过
        final String boundSubMchIds = providerTerminal.getBound_sub_mch_ids();
        //子商户号没有绑定成功
        if (StringUtils.isEmpty(boundSubMchIds)) {
            return params;
        }
        final List<String> list = Splitter.on(",").omitEmptyStrings().splitToList(boundSubMchIds);
        final Sets.SetView<String> difference = Sets.difference(subMerchantNoSet, Sets.newHashSet(list));
        final ImmutableSet<String> diff = difference.immutableCopy();
        if (CollectionUtils.isEmpty(diff)) {
            return null;
        }
        //payMerchantId为key
        final Map<String, MerchantProviderParams> paramsMap = params.parallelStream().collect(Collectors.toMap(param -> param.getPay_merchant_id(), param -> param, (val1, val2) -> val1));
        final List<MerchantProviderParams> providerParams = diff.stream().flatMap(x -> Stream.of(x)).map(payMerchantId ->
                paramsMap.get(String.valueOf(payMerchantId))
        ).filter(Objects::nonNull).collect(Collectors.toList());
        return providerParams;
    }


    /**
     * 收钱吧终端与当前子商户是否已经解绑,已解绑返回空集合,未解绑则返回需要解绑的交易参数的集合
     *
     * @param providerTerminal 当前终端记录
     * @param params           商户在某个通道下的支付宝和微信交易参数
     * @return
     */
    public List<MerchantProviderParams> getNeedDeleteMerchantProviderParams(ProviderTerminal providerTerminal, List<MerchantProviderParams> params) {
        //是否已经报过
        final String boundSubMchIds = providerTerminal.getBound_sub_mch_ids();
        //子商户号没有不需要解绑
        if (StringUtils.isEmpty(boundSubMchIds)) {
            return params;
        }
        //已经绑定的子商户号
        final List<String> list = Splitter.on(",").omitEmptyStrings().splitToList(boundSubMchIds);
        final Map<String, MerchantProviderParams> paramsMap = params.parallelStream().collect(Collectors.toMap(param -> param.getPay_merchant_id(), param -> param, (val1, val2) -> val1));

        final List<MerchantProviderParams> providerParams = list.stream().map(payMerchantId ->
                paramsMap.get(payMerchantId)
        ).filter(Objects::nonNull).collect(Collectors.toList());
        return providerParams;
    }

    /**
     * provider_terminal 是否存在收钱吧终端对应记录
     *
     * @param provider
     * @param storeSn
     * @param merchantSn
     * @param terminalSn
     * @return
     */
    public ProviderTerminal existProviderTerminal(Integer provider, String storeSn, String merchantSn, String terminalSn, String acquirerMerchantId) {
        final ProviderTerminal providerTerminal = new ProviderTerminal();
        providerTerminal.setMerchant_sn(merchantSn);
        providerTerminal.setProvider(Integer.valueOf(provider));
        providerTerminal.setStore_sn(storeSn);
        providerTerminal.setTerminal_sn(terminalSn);
        providerTerminal.setAcquirer_merchant_id(acquirerMerchantId);
        //该通道下终端关联的记录
        final List<ProviderTerminal> terminalList = providerTerminalMapper.selectByCondition(providerTerminal);
        if (CollectionUtils.isEmpty(terminalList) || Objects.isNull(terminalList.get(0))) {
            return null;
        }
        return terminalList.get(0);
    }

    /**
     * provider_terminal 是否存在收钱吧门店对应记录
     *
     * @param provider
     * @param storeSn
     * @param merchantSn
     * @return
     */
    public ProviderTerminal existStoreProviderTerminal(Integer provider, String storeSn, String merchantSn, String acquirerMerchantId) {
        final ProviderTerminal providerTerminal = new ProviderTerminal();
        providerTerminal.setMerchant_sn(merchantSn);
        providerTerminal.setProvider(Integer.valueOf(provider));
        providerTerminal.setStore_sn(storeSn);
        providerTerminal.setAcquirer_merchant_id(acquirerMerchantId);
        //该通道下终端关联的记录
        final List<ProviderTerminal> terminalList = providerTerminalMapper.selectByCondition(providerTerminal);
        final List<ProviderTerminal> collect = terminalList.parallelStream().filter(ter -> StringUtils.isEmpty(ter.getTerminal_sn())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect) || Objects.isNull(collect.get(0))) {
            return null;
        }
        return collect.get(0);
    }

    public ProviderTerminal getStoreProviderTerminal(Integer provider, String storeSn, String merchantSn, String acquirerMerchantId) {
        final ProviderTerminal providerTerminal = new ProviderTerminal();
        providerTerminal.setMerchant_sn(merchantSn);
        providerTerminal.setProvider(Integer.valueOf(provider));
        providerTerminal.setStore_sn(storeSn);
        providerTerminal.setAcquirer_merchant_id(acquirerMerchantId);
        //该通道下终端关联的记录
        final List<ProviderTerminal> terminalList = providerTerminalMapper.selectByCondition(providerTerminal);
        final List<ProviderTerminal> collect = terminalList.parallelStream().filter(ter -> StringUtils.isEmpty(ter.getTerminal_sn())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect) || Objects.isNull(collect.get(0))) {
            return null;
        }
        return collect.get(0);
    }

    /**
     * provider_terminal 是否存在收钱吧大商户级别的对应记录
     *
     * @param provider
     * @param merchantSn
     * @return
     */
    public ProviderTerminal existMerchantProviderTerminal(Integer provider, String merchantSn, String acquirerMerchantId) {
        final ProviderTerminal providerTerminal = new ProviderTerminal();
        providerTerminal.setMerchant_sn(merchantSn);
        providerTerminal.setProvider(Integer.valueOf(provider));
        providerTerminal.setAcquirer_merchant_id(acquirerMerchantId);
        //该通道下终端关联的记录
        final List<ProviderTerminal> terminalList = providerTerminalMapper.selectByCondition(providerTerminal);
        //筛选
        final List<ProviderTerminal> collect = terminalList.parallelStream().filter(ter -> StringUtils.isEmpty(ter.getTerminal_sn()) && StringUtils.isEmpty(ter.getStore_sn())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect) || Objects.isNull(collect.get(0))) {
            return null;
        }
        return collect.get(0);
    }


    /**
     * 获取商户报过所有成功的provider
     *
     * @param merchantSn
     * @return
     */
    public List<Integer> getProviderList(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andProviderNotIn(Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()))
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);
        //获取当前所有provider集合
        return records.parallelStream()
                .collect(Collectors.groupingBy(record -> record.getProvider()))
                .keySet()
                .stream()
                .map(provider -> {
                    if (Objects.equals(provider, ProviderEnum.PROVIDER_LAKALA.getValue())) {
                        return ProviderEnum.PROVIDER_LAKALA_V3.getValue();
                    }
                    return provider;

                }).collect(Collectors.toList());
    }


    /**
     * 银行多业务商户 关联收单机构终端ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void bankMultiTradeMerchantTerminal(String bankMerchantId, String providerTerminalId,
                                               int provider) {
        //查询是否存在
        TradeExtConfigQueryRequest queryRequest = new TradeExtConfigQueryRequest();
        queryRequest.setProvider(provider);
        queryRequest.setSn(bankMerchantId);
        queryRequest.setSnType(SN_TYPE_BANK_MERCHANT);
        TradeExtConfigQueryResponse queryResponse = tradeConfigService.queryTradeExtConfig(queryRequest);
        TradeExtConfigContentModel content = new TradeExtConfigContentModel();
        content.setTermId(providerTerminalId);
        if (Objects.isNull(queryResponse)) {
            //调用交易新增接口
            TradeExtConfigCreateRequest request = new TradeExtConfigCreateRequest();
            request.setSn(bankMerchantId);
            //0：商户、1：门店、2：终端, 3:使用银行多业务时终端绑定时,对应的银行终端类型
            request.setSnType(SN_TYPE_BANK_MERCHANT);
            request.setProvider(provider);
            request.setContent(content);
            tradeConfigService.createTradeExtConfig(request);
        } else {
            //调用交易修改接口
            TradeExtConfigUpdateRequest tradeExtConfigUpdateRequest = new TradeExtConfigUpdateRequest();
            tradeExtConfigUpdateRequest.setProvider(provider);
            tradeExtConfigUpdateRequest.setSn(bankMerchantId);
            tradeExtConfigUpdateRequest.setSnType(SN_TYPE_BANK_MERCHANT);
            tradeExtConfigUpdateRequest.setContent(content);
            tradeConfigService.updateTradeExtConfig(tradeExtConfigUpdateRequest);
        }
        //清除交易缓存
        supportService.removeCachedParams(bankMerchantId);
    }

    /**
     * 由于微信交易占比比较多,所以默认微信子商户号绑定成功以后再将终端信息写入trade_extra_config
     *
     * @param task
     * @param terminalInfo
     * @param payWay
     */
    private void modifyTradeExtra(ProviderTerminalTask task, ProviderTerminal terminalInfo, Integer payWay) {
        log.info("modifyTradeExtra taskId:{} task:{} terminal:{}", task.getId(), JSONObject.toJSONString(task), JSONObject.toJSONString(terminalInfo));
        final String terminalSn = terminalInfo.getTerminal_sn();
        final String storeSn = terminalInfo.getStore_sn();
        //终端级别绑定写入交易
        if (task.getType() == ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType() && StringUtils.isNotBlank(terminalSn)) {
            if (!Objects.equals(payWay, PaywayEnum.WEIXIN.getValue())) {
                return;
            }
            log.info("终端级别绑定写入交易,merchantSn:{},storeSn:{},termId:{},provider:{},taskId:{}",
                    terminalInfo.getMerchant_sn(), terminalInfo.getStore_sn(), terminalInfo.getProvider_terminal_id(),
                    terminalInfo.getProvider(), task.getId());
            createOrUpdateTerminalExtra(terminalInfo.getMerchant_sn(),
                    terminalInfo.getProvider_terminal_id(),
                    terminalInfo.getProvider(),
                    terminalInfo.getTerminal_appid(),
                    terminalSn);
        }
        //门店级别绑定写入交易
        if (task.getType() == ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType() && StringUtils.isNotBlank(storeSn)) {
            log.info("门店级别绑定写入交易,merchantSn:{},storeSn:{},termId:{},provider:{},taskId:{}",
                    terminalInfo.getMerchant_sn(), terminalInfo.getStore_sn(), terminalInfo.getProvider_terminal_id(),
                    terminalInfo.getProvider(), task.getId());
            createOrUpdateStoreExtra(terminalInfo,
                    task.getContextInfo(),
                    storeSn,
                    payWay);
        }
    }

    /**
     * 调用拉卡拉终端操作接口
     *
     * @param providerTerminal
     */
    public void addOrUpdateTerm(ProviderTerminal providerTerminal) {
        String subMchIds = providerTerminal.getBound_sub_mch_ids();
        if (StringUtil.empty(subMchIds)) {
            return;
        }
        List<String> subList = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(subMchIds);
        subList.stream().forEach(subMchId -> {
            MerchantProviderParams params = merchantProviderParamsMapper.getByPayMerchantId(subMchId);
            if (params == null || !Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()).contains(params.getPayway())) {
                return;
            }
            BasicProvider handleProvider = providerFactory.getProvider(String.valueOf(params.getProvider()));
            AddTermInfoDTO addTermInfoDTO = new AddTermInfoDTO();
            addTermInfoDTO.setDeviceId(providerTerminal.getProvider_terminal_id());
            addTermInfoDTO.setMerchantSn(params.getMerchant_sn());
            addTermInfoDTO.setStoreSn(providerTerminal.getStore_sn());
            addTermInfoDTO.setSubMchId(subMchId);
            //接口底层如果返回"该商户已绑定该终端，请进行状态变更(UP18A7101)会再次调用终端更新接口
            ContractResponse boundResp = handleProvider.boundTerminal(addTermInfoDTO, params.getPayway(), providerTerminal.getTerminal_sn());
            Optional.ofNullable(boundResp)
                    .filter(Objects::nonNull)
                    .filter(resp -> resp.isSuccess())
                    .orElseThrow(() -> new CommonPubBizException(boundResp.getMessage()));
        });
    }

    /**
     * 调用拉卡拉终端操作接口
     *
     * @param providerTerminal
     */
    public void restoreT9TerminalInfo(ProviderTerminal providerTerminal) {
        String subMchIds = providerTerminal.getBound_sub_mch_ids();
        if (StringUtil.empty(subMchIds)) {
            return;
        }
        List<String> subList = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(subMchIds);
        subList.stream().forEach(subMchId -> {
            MerchantProviderParams params = merchantProviderParamsMapper.getByPayMerchantId(subMchId);
            if (params == null || !Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()).contains(params.getPayway())) {
                return;
            }
            BasicProvider handleProvider = providerFactory.getProvider(String.valueOf(params.getProvider()));
            UpdateTermInfoDTO updateTermInfoDTO = new UpdateTermInfoDTO();
            updateTermInfoDTO.setDeviceId(providerTerminal.getProvider_terminal_id());
            updateTermInfoDTO.setMerchantSn(params.getMerchant_sn());
            updateTermInfoDTO.setStoreSn(providerTerminal.getStore_sn());
            updateTermInfoDTO.setSubMchId(subMchId);
            //设置微信,支付宝自定义参数
            AliTermInfoRequest aliTermInfoRequest = new AliTermInfoRequest();
            aliTermInfoRequest.setTerminalType("11");
            WxTermInfoRequest wxTermInfoRequest = new WxTermInfoRequest();
            wxTermInfoRequest.setDeviceType("11");
            ContractResponse response = handleProvider.updateTerminalWithCustom(updateTermInfoDTO, params.getPayway(),
                    providerTerminal.getTerminal_sn(),
                    aliTermInfoRequest,
                    wxTermInfoRequest);
            Optional.ofNullable(response)
                    .filter(Objects::nonNull)
                    .filter(resp -> resp.isSuccess())
                    .orElseThrow(() -> new CommonPubBizException(response.getMessage()));
        });
    }

    public void deleteProviderTerminal(String merchantSn, String storeSn, String terminalSn, Integer provider) {
        if (com.wosai.mpay.util.StringUtils.isEmpty(merchantSn)) {
            throw new CommonPubBizException("商户号不为空");
        }
        providerTerminalMapper.deleteProviderTerminal(merchantSn, storeSn, terminalSn, provider);
    }


    /**
     * 插入任务查询pos刷卡终端状态
     *
     * @param merchantSn
     * @param storeSn
     * @param terminalSn
     * @param termNo
     * @param subMerchant
     */
    public void insertPosBankQuery(String merchantSn, String storeSn, String terminalSn, String termNo, String subMerchant) {
        PendingTasks pendingTasks = new PendingTasks()
                .setSn(merchantSn)
                .setEvent_type(PendingTasks.TYPE_BANK_POS_QUERY)
                .setEvent_msg(JSON.toJSONString(CollectionUtil.hashMap("storeSn", storeSn,
                        "terminalSn", terminalSn,
                        "termNo", termNo,
                        "subMerchant", subMerchant)))
                .setStatus(PendingTasks.STATUS_PENDING)
                .setVersion(1L);
        pendingTasksMapper.insertSelective(pendingTasks);
    }

    /**
     * 处理pos银行卡刷卡
     *
     * @param pendingTask
     */
    public void handleBankPosQueryTask(PendingTasks pendingTask) {
        final String sn = pendingTask.getSn();
        MerchantProviderParams params = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(sn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        //本地没有记录
        if (Objects.isNull(params)) {
            throw new CommonPubBizException("还没有入网成功");
        }
        BasicProvider provider = providerFactory.getProviderByRule(params.getContract_rule());
        final Map bankPosMap = JSONObject.parseObject(pendingTask.getEvent_msg(), Map.class);
        final String termNo = BeanUtil.getPropString(bankPosMap, "termNo");
        ContractResponse response = provider.queryTermContractResult(params.getProvider_merchant_id(), termNo);
        //拉卡拉还没有绑定成功
        if (response.isBusinessFail() && StrUtil.contains(BeanUtil.getPropString(response.getResponseParam(), "retMsg"), "银联终端报备记录为空")) {
            pendingTask.setVersion(pendingTask.getVersion() + 1);
            delayPendingTask(pendingTask, 5);
            return;
        }
        //其他业务异常
        if (response.isBusinessFail()) {
            final ProviderTerminal terminal = new ProviderTerminal().setProvider_terminal_id(termNo);
            saveTerminalConfigAfterBind(terminal, params.getProvider_merchant_id(), PaywayEnum.BANK_CARD.getValue(), response);
            throw new CommonPubBizException(response.getMessage());
        }
        if (response.isSuccess()) {
            pendingTask.setStatus(PendingTasks.STATUS_SUCCESS);
            pendingTasksMapper.updateByPrimaryKeySelective(pendingTask);
            final ProviderTerminal terminal = new ProviderTerminal().setProvider_terminal_id(termNo);
            saveTerminalConfigAfterBind(terminal, params.getProvider_merchant_id(), PaywayEnum.BANK_CARD.getValue(), response);
        }

    }

    /**
     * 推迟任务
     *
     * @param pendingTask
     * @param minutes
     */
    public void delayPendingTask(PendingTasks pendingTask, int minutes) {
        pendingTask.setUpdate_at(DateUtils.addMinutes(pendingTask.getUpdate_at(), minutes));
        pendingTasksMapper.updateByPrimaryKeySelective(pendingTask);
    }



    /**
     * 重复导入的时候删除已经存在的终端数据
     *
     * @param merchantSn
     */
    public void deleteExistTerminal(String merchantSn,Integer provider) {
        //由于存在重复导入所以删除原有记录避免
        providerTerminalDAO.deleteByMerchantSnAndProviders(merchantSn,Lists.newArrayList(String.valueOf(provider)));
    }
}
