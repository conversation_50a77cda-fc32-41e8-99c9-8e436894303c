package com.wosai.upay.job.biz.keepalive.validation.rule.impl;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.trade.service.activity.response.ApplyConditionQueryResponse;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveCheckRuleTypeEnum;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.AbstractKeepAliveCheckRule;
import com.wosai.upay.job.externalservice.trademanage.TradeManageClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 费率活动规则
 * 检查商户是否参与了费率活动且在活动中
 */
@Component
public class FeeActivityRule extends AbstractKeepAliveCheckRule {

    @Autowired
    private TradeManageClient tradeManageClient;

    @Override
    protected KeepAliveCheckRuleResult doExecute(KeepAliveValidateContext context, KeepAliveCheckRuleConfig ruleConfig) {
        try {
            String merchantSn = context.getMerchantSn();
            // 检查商户是否参与了指定的支付源行业活动
            Map<String, Object> params = ruleConfig.getParams();
            List<Integer> activityTypes = (List<Integer>) WosaiMapUtils.getObject(params, "activityTypes");
            List<ApplyConditionQueryResponse> response = tradeManageClient.getEffectApplyByMerchantSn(merchantSn);
            for (ApplyConditionQueryResponse applyConditionQueryResponse : response) {
                if (activityTypes.contains(applyConditionQueryResponse.getActivityType())) {
                    return createFailureResult(
                            String.format("商户参与了费率活动 %s 且在活动中，不允许执行", applyConditionQueryResponse.getActivityName()),
                            "FEE_ACTIVITY_BLOCKED");
                }
            }
            return createSuccessResult("商户未参与费率活动，通过检查");

        } catch (Exception e) {
            logger.error("执行费率活动检查时发生异常，商户SN: {}", context.getMerchantSn(), e);
            return createFailureResult("规则执行异常: " + e.getMessage(), "EXECUTION_ERROR");
        }
    }

    @Override
    public KeepAliveCheckRuleTypeEnum getRuleType() {
        return KeepAliveCheckRuleTypeEnum.FEE_ACTIVITY;
    }
}