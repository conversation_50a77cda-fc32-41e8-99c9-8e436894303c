package com.wosai.upay.job.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.service.StoreService;
import com.wosai.sales.merchant.business.bean.StoreInfoRequest;
import com.wosai.sales.merchant.business.model.AppInfoModel;
import com.wosai.sales.merchant.business.service.common.CommonMerchantInfoService;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.AopBiz;
import com.wosai.upay.job.biz.LklV3ShopTermBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.acquirer.IAcquirerBiz;
import com.wosai.upay.job.mapper.ForeignCardMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ForeignCard;
import com.wosai.upay.job.model.LklV3Term;
import com.wosai.upay.job.model.subBizParams.SubBizParams;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 手机pos实现
 * <AUTHOR>
 * @Date 2024/6/7 11:13
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class MobilePosServiceImpl implements MobilePosService {

    public static final String MERCHANT_ID = "merchant_id";

    @Autowired
    private ContractStatusService contractStatusService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;

    @Resource(name = "jsonRedisTemplate")
    private RedisTemplate jsonRedisTemplate;

    @Autowired
    private ForeignCardMapper foreignCardMapper;

    @Autowired
    private SubBizParamsBiz subBizParamsBiz;


    @Value("${lklMobilePos.devCode}")
    private String lklMobilePosDevCode;

    @Value("${lklMobilePos.appId}")
    private String lklMobilePosAppId;

    @Autowired
    private CommonMerchantInfoService commonMerchantInfoService;

    @Autowired
    private AopBiz aopBiz;

    @Autowired
    private LklV3ShopTermBiz lklV3ShopTermBiz;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    public static final long EXPIRATION_DURATION = 5L;

    public static final long OPEN_EXPIRATION_DURATION = 1L;

    public static final TimeUnit EXPIRATION_TIME_UNIT = TimeUnit.MINUTES;

    public static final String CACHE_KEY_NOT_FOUND = "cacheNotFound";

    public static final String LKL_MOBILE_POS_TERMINAL_ID = "lkl_mobile_pos";
    public static final String LKL_B2B_CASHIER_DESK_CB = "B2B_CASHIER_DESK_CB";

    public static final String PRODUCT_CODE = "MOBILE_POS";
    public static final String BUSI_TYPE_CODE = "WILD_CARD";

    /**
     * 类型  1三方机构  2银行机构  3支付源直连机构
     */
    public static final Integer MC_ACQUIRER_TYPE_BANK = 2;


    @Override
    public Boolean isPaymentValid(Map paymentParams) {
        //默认不显示该按钮
        final Boolean open = applicationApolloConfig.getMobilePosOpenFlag();
        if (!open) {
            return Boolean.FALSE;
        }
        final String merchantId = MapUtils.getString(paymentParams, MERCHANT_ID);
        try {
            Assert.isTrue(StrUtil.isNotBlank(merchantId), "商户Id不为空");
            String key = String.format("mobilePos:%s", merchantId);
            final Object value = jsonRedisTemplate.opsForValue().get(key);

            // 检查是否为已知的未找到标记
            if (CACHE_KEY_NOT_FOUND.equals(value)) {
                // 直接返回false，避免查询数据库
                return Boolean.FALSE;
            }

            if (Objects.nonNull(value) && value instanceof Boolean) {
                return (Boolean) value;
            }

            final Map<String, Object> merchantInfo = merchantService.getMerchantByMerchantId(merchantId);
            final String merchantSn = MapUtils.getString(merchantInfo, Merchant.SN);
            final ContractStatus contractStatus = Optional.ofNullable(contractStatusService.selectByMerchantSn(merchantSn))
                    .orElseGet(ContractStatus::new);
            final String acquirer = contractStatus.getAcquirer();

            // 报备成功且正在使用拉卡拉
            final boolean display = Objects.equals(contractStatus.getStatus(), ContractStatus.STATUS_SUCCESS)
                    && StrUtil.contains(acquirer, AcquirerTypeEnum.LKL.getValue());

            if (display) {
                setCacheValue(key, Boolean.TRUE, EXPIRATION_DURATION);
                return Boolean.TRUE;
            } else {
                // 当前收单机构类型
                final McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
                final Integer type = mcAcquirerDO.getType();

                if (!Objects.equals(type, MC_ACQUIRER_TYPE_BANK)) {
                    // 数据库中也没有成功状态的记录，缓存"未找到"标记
                    setCacheValue(key, CACHE_KEY_NOT_FOUND, EXPIRATION_DURATION);
                    return Boolean.FALSE;
                }
                //多个间连收单机构判断
                subBizParamsBiz.checkOnlyOneIndirect(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());

                final IAcquirerBiz targetAcquirerBiz = composeAcquirerBiz.getAcquirerBiz(AcquirerTypeEnum.LKL_V3.getValue());
                // 收钱吧银行卡
                Map<String, Object> merchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(merchantId);
                // 当前正在使用银行通道且在拉卡拉商户状态正常且结算账户和当前一致
                final Boolean consistentAndStatusOpen = targetAcquirerBiz.bankAccountConsistentAndStatusOpen(merchantSn, merchantBankAccount);

                if (consistentAndStatusOpen) {
                    setCacheValue(key, Boolean.TRUE, EXPIRATION_DURATION);
                    return Boolean.TRUE;
                } else {
                    // 数据库中也没有成功状态的记录，缓存"未找到"标记
                    setCacheValue(key, CACHE_KEY_NOT_FOUND, EXPIRATION_DURATION);
                    return Boolean.FALSE;
                }
            }
        } catch (Exception e) {
            log.error("isPaymentValid merchantId:{}", merchantId, e);
            return Boolean.FALSE;
        }
    }


    @Override
    public Boolean openMobilePos(Map paymentParams) {

        final String merchantId = MapUtils.getString(paymentParams, MERCHANT_ID);
        //校验
        final Boolean valid = isPaymentValid(paymentParams);
        //检验返回false,说明没有权限开通,直接返回
        if (!valid) {
            log.warn("商户Id:{},校验失败不允许开通手机外卡", merchantId);
            return Boolean.FALSE;
        }

        Assert.isTrue(StrUtil.isNotBlank(merchantId), "商户Id不为空");

        final Map<String, Object> merchantInfo = merchantService.getMerchantByMerchantId(merchantId);
        final String merchantSn = MapUtils.getString(merchantInfo, Merchant.SN);

        ForeignCard foreignCard = Optional.ofNullable(foreignCardMapper.selectByMerchantSnAndCode(merchantSn, lklMobilePosDevCode))
                .orElseGet(ForeignCard::new);

        if (Objects.equals(foreignCard.getStatus(), ForeignCard.STATUS_PROCESS)) {
            throw new CommonPubBizException("开通中,请稍后");
        }
        //多个间连收单机构判断
        try {
            subBizParamsBiz.checkOnlyOneIndirect(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        } catch (Exception e) {
            log.error("openMobilePos merchantId:{}", merchantId, e);
            return Boolean.FALSE;
        }

        //已经开通成功的
        if (Objects.equals(foreignCard.getStatus(), ForeignCard.STATUS_SUCCESS)) {
            handleSuccessfulActivation(merchantSn, merchantId);
            return Boolean.TRUE;
        }
        //首次开通
        handleNewOrFailedActivation(merchantSn, paymentParams, merchantId, merchantInfo);
        return Boolean.FALSE;
    }


    @Override
    public void closeMobilePos(String merchantSn) {
        lklV3ShopTermBiz.closeMobilePos(merchantSn);
    }

    @Override
    public Map crmCloseMobilePos(Map crmMap) {
        final Map<String, Object> crmResultMap = Maps.newHashMap();
        crmResultMap.put("success", Boolean.TRUE);
        final String merchantId = MapUtils.getString(crmMap, MERCHANT_ID);
        if (!Objects.equals(MapUtils.getString(crmMap, "dev_code"), lklMobilePosDevCode)) {
            return crmResultMap;
        }
        try {
            Assert.isTrue(StrUtil.isNotBlank(merchantId), "商户Id不为空");
            final Map<String, Object> merchantInfo = merchantService.getMerchantByMerchantId(merchantId);
            final String merchantSn = MapUtils.getString(merchantInfo, Merchant.SN);
            lklV3ShopTermBiz.closeMobilePos(merchantSn);
        } catch (Exception e) {
            log.error("crmCloseMobilePos error merchantId:{}", merchantId);
            crmResultMap.put("success", Boolean.FALSE);
            final String message = e.getMessage();
            crmResultMap.put("fail_msg", StringUtils.isEmpty(message) ? "失败了" : message);
        }
        return crmResultMap;
    }

    /**
     * 判断是否需要重新设置收钱吧参数
     *
     * @param merchantSn
     * @param merchantId
     */
    private void handleSuccessfulActivation(String merchantSn, String merchantId) {
        List<SubBizParams> bizParams = subBizParamsBiz.getSubBizParams(merchantSn, subBizParamsBiz.getMobilePosAppId(), ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        //这种场景出现在被crm注销后又主动使用手机POS
        if (CollectionUtils.isEmpty(bizParams)) {
            CompletableFuture.runAsync(() -> reSetMobilePosParams(merchantId));
        }
    }

    /**
     * 重新设置收钱吧参数
     *
     * @param merchantId
     */
    private void reSetMobilePosParams(String merchantId) {
        Map<String, Object> storeMap = getFirstStore(merchantId);
        String storeSn = BeanUtil.getPropString(storeMap, Store.SN);
        MutablePair<String, List<LklV3Term>> shopTerm = lklV3ShopTermBiz.getShopTerm(storeSn);
        if (Objects.isNull(shopTerm)) {
            return;
        }
        String shopId = String.valueOf(shopTerm.getLeft());
        List<Map> termList = shopTerm.getRight().parallelStream()
                .map(term -> new MyObjectMapper().convertValue(term, Map.class))
                .collect(Collectors.toList());
        lklV3ShopTermBiz.reSetMobilePosParams(shopId, termList);
    }

    /**
     * 首次开通
     *
     * @param merchantSn
     * @param paymentParams
     * @param merchantId
     * @param merchantInfo
     */
    private void handleNewOrFailedActivation(String merchantSn, Map<String, Object> paymentParams, String merchantId, Map<String, Object> merchantInfo) {
        insertNewForeignCardRecord(merchantSn, paymentParams);
        CompletableFuture.runAsync(() -> createOrUpdateBizOpenInfo(merchantSn, AppInfoModel.STATUS_PENDING, null));
        CompletableFuture.runAsync(() -> createMobilePosTask(merchantId, merchantInfo));
    }

    /**
     * 插入foreignCard表
     *
     * @param merchantSn
     * @param paymentParams
     */
    private void insertNewForeignCardRecord(String merchantSn, Map<String, Object> paymentParams) {
        ForeignCard card = new ForeignCard();
        card.setMerchant_sn(merchantSn);
        card.setStatus(ForeignCard.STATUS_PROCESS);
        card.setForm_body(JSONObject.toJSONString(paymentParams));
        card.setDev_code(lklMobilePosDevCode);
        foreignCardMapper.insert(card);
    }

    /**
     * 创建增网任务
     *
     * @param merchantId
     * @param merchantInfo
     */
    private void createMobilePosTask(String merchantId, Map<String, Object> merchantInfo) {
        Map<String, Object> storeMap = getFirstStore(merchantId);
        String storeId = BeanUtil.getPropString(storeMap, DaoConstants.ID);
        Map<String, Object> terminal = CollectionUtil.hashMap(
                Terminal.MERCHANT_ID, merchantId,
                DaoConstants.ID, String.format(LKL_MOBILE_POS_TERMINAL_ID + ":%s", BeanUtil.getPropString(merchantInfo, Merchant.SN)),
                Terminal.STORE_ID, storeId,
                Terminal.VENDOR_APP_APPID, LKL_MOBILE_POS_TERMINAL_ID
        );
        lklV3ShopTermBiz.bindLklTerminal(terminal, new MyObjectMapper().convertValue(merchantInfo, Map.class));
    }


    /**
     * 获取当前商户第一家门店
     *
     * @param merchantId
     * @return
     */
    @NotNull
    private Map getFirstStore(String merchantId) {
        return storeService.getFirstStore(CollectionUtil.hashMap("merchant_id", merchantId));
    }

    /**
     * 插入crm开通中纪录
     *
     * @param merchantSn
     * @param appStatus
     */
    public void createOrUpdateBizOpenInfo(String merchantSn, Integer appStatus, String maintainUserId) {
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        if (StringUtils.isBlank(maintainUserId)) {
            try {
                maintainUserId = aopBiz.getMaintainUserId(merchantId);
            } catch (Exception exception) {
                log.info("createBizOpenInfo商户号:{},找不到商户维护人", merchantSn);
                return;
            }
        }
        Map storeMap = getFirstStore(merchantId);
        if (MapUtils.isEmpty(storeMap)) {
            log.info("createBizOpenInfo商户号:{},找不到门店", merchantSn);
            return;
        }
        final String storeId = BeanUtil.getPropString(storeMap, DaoConstants.ID);
        StoreInfoRequest storeInfoRequest = new StoreInfoRequest();
        storeInfoRequest.setStoreId(storeId);
        storeInfoRequest.setMerchantId(merchantId);
        storeInfoRequest.setPlatform("contract");
        storeInfoRequest.setPlatformVersion("contract");
        storeInfoRequest.setPlatformDevice("contract");
        storeInfoRequest.setUserId(maintainUserId);
        storeInfoRequest.setKeeperId(maintainUserId);
        storeInfoRequest.setAppId(lklMobilePosAppId);
        storeInfoRequest.setAppStatus(appStatus);
        storeInfoRequest.setNeedAuditMerchant(0);
        storeInfoRequest.setNeedAuditStore(0);
        storeInfoRequest.setIsNew(2);
        storeInfoRequest.setIsFirstStore(Boolean.TRUE);
        if (Objects.equals(appStatus, AppInfoModel.STATUS_PENDING)) {
            storeInfoRequest.setAppCtime(new Date());
            storeInfoRequest.setAppSubmitTime(new Date());
        } else if (Lists.newArrayList(AppInfoModel.STATUS_FAIL, AppInfoModel.STATUS_SUCCESS).contains(appStatus)) {
            storeInfoRequest.setAppConfirmTime(new Date());
        }
        commonMerchantInfoService.createBizOpenInfo(storeInfoRequest);
    }

    /**
     * 设置缓存
     *
     * @param key
     * @param value
     * @param expirationDuration
     */
    private void setCacheValue(String key, Object value, long expirationDuration) {
        jsonRedisTemplate.opsForValue().set(key, value, expirationDuration, EXPIRATION_TIME_UNIT);
    }

    @Override
    public void openApplyPay(String merchantSn, Long auditId) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map firstStore = getFirstStore(merchantId);
        String storeId = BeanUtil.getPropString(firstStore, DaoConstants.ID);
        Map<String, Object> terminal = CollectionUtil.hashMap(
                Terminal.MERCHANT_ID, merchantId,
                DaoConstants.ID, String.format(LKL_B2B_CASHIER_DESK_CB + ":%s", merchantSn),
                Terminal.STORE_ID, storeId,
                Terminal.VENDOR_APP_APPID, LKL_B2B_CASHIER_DESK_CB,
                "auditId", auditId
        );
        lklV3ShopTermBiz.bindLklTerminal(terminal, merchant);
    }

}
