package com.wosai.upay.job.biz.keepalive;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.job.externalservice.aop.AopClient;
import com.wosai.upay.job.model.keepalive.KeepAliveTaskInfo;
import com.wosai.upay.job.model.keepalive.KeepAliveTaskStatusUpdateRequest;
import com.wosai.upay.job.model.keepalive.KeepAliveTasksQueryRequest;
import com.wosai.upay.job.refactor.dao.ProviderParamsKeepaliveTaskDAO;
import com.wosai.upay.job.refactor.model.bo.KeepAliveTaskResultBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.ProviderParamsKeepaliveTaskDO;
import com.wosai.upay.job.refactor.model.enums.ProviderParamsKeepaliveTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.ProviderParamsKeepaliveTaskTypeEnum;
import com.wosai.upay.job.util.SnowFlakeIdGenerator;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.web.api.ListResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 保活任务业务逻辑处理类
 * 负责创建、取消和管理商户的保活任务
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
@Slf4j
@Component
public class KeepAliveTaskBiz {

    @Autowired
    private ProviderParamsKeepaliveTaskDAO providerParamsKeepaliveTaskDAO;

    @Autowired
    private KeepAliveProviderParamsAndDateCalculator calculator;

    @Autowired
    private KeepAliveMessageBiz keepAliveMessageBiz;
    @Autowired
    private SubMchIdLastTradeTimeBiz subMchIdLastTradeTimeBiz;
    @Autowired
    private AopClient aopClient;

    public ListResult<KeepAliveTaskInfo> queryKeepAliveTasks(KeepAliveTasksQueryRequest request) {
        // 使用MyBatis-Plus分页功能
        Page<ProviderParamsKeepaliveTaskDO> providerParamsKeepaliveTaskDOS = providerParamsKeepaliveTaskDAO.queryKeepAliveTasks(request.getMerchantSn(), request.getSubMchId(), request.getPageNum(), request.getPageSize());
        List<ProviderParamsKeepaliveTaskDO> results = providerParamsKeepaliveTaskDOS.getResult();
        long total = providerParamsKeepaliveTaskDOS.getTotal();

        List<KeepAliveTaskInfo> taskInfoList = new ArrayList<>();
        for (ProviderParamsKeepaliveTaskDO task : results) {
            KeepAliveTaskInfo taskInfo = new KeepAliveTaskInfo();
            taskInfo.setMerchantSn(task.getMerchantSn());
            taskInfo.setProviderMerchantId(task.getProviderMchId());
            taskInfo.setPayway(task.getPayway());
            taskInfo.setSubMchId(task.getSubMchId());
            taskInfo.setStatus(task.getStatus());
            ProviderParamsKeepaliveTaskStatusEnum anEnum = EnumUtils.getEnum(ProviderParamsKeepaliveTaskStatusEnum.class, task.getStatus());
            taskInfo.setStatusDesc(anEnum.getText());
            taskInfo.setType(task.getType());
            ProviderParamsKeepaliveTaskTypeEnum typeEnum = EnumUtils.getEnum(ProviderParamsKeepaliveTaskTypeEnum.class, task.getType());
            taskInfo.setTypeDesc(typeEnum.getText());
            taskInfo.setResult(task.getResult());
            taskInfo.setPayTime(task.getPayTime());
            taskInfoList.add(taskInfo);
        }

        ListResult<KeepAliveTaskInfo> result = new ListResult<>();
        result.setRecords(taskInfoList);
        result.setTotal(total);
        return result;
    }

    /**
     * 创建商户的保活任务 加入保活/收单机构切换/报备新通道调用该方法进行保活信息的更新
     * <p>
     * 业务流程：
     * 1. 计算商户需要保活的参数和日期
     * 2. 查询现有的未完成保活任务
     * 3. 取消不再需要的保活任务
     * 4. 筛选出需要新增的任务参数
     * 5. 调整日期冲突，确保不同 provider 不在同一天执行
     * 6. 创建新的保活任务
     *
     * @param merchantSn 商户号
     * @param resultBO   任务结果BO
     */
    @Transactional(rollbackFor = Exception.class)
    public void createKeepAliveTasks(String merchantSn, KeepAliveTaskResultBO resultBO) {
        try {
            log.info("开始为商户 {} 创建保活任务", merchantSn);

            // 1. 计算商户需要保活的参数和日期（包含最大保活日期）
            KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult keepAliveResult =
                    calculator.calculateKeepAliveParamsAndDateWithMaxLimit(merchantSn);
            Map<MerchantProviderParamsDO, LocalDate> requiredKeepAliveParams = keepAliveResult.getParamsAndDates();

            if (CollectionUtils.isEmpty(requiredKeepAliveParams)) {
                log.info("商户 {} 无需要保活的参数，跳过任务创建", merchantSn);
                return;
            }

            // 2. 查询现有的未完成保活任务
            List<ProviderParamsKeepaliveTaskDO> existingTasks =
                    providerParamsKeepaliveTaskDAO.selectNotCompletedTasksByMerchantSn(merchantSn);

            // 3. 处理任务取消和筛选
            TaskProcessingResult processingResult = processExistingTasks(requiredKeepAliveParams, existingTasks);

            // 4. 调整日期冲突并创建新任务
            filterValidParamsWithDateAdjustment(merchantSn, processingResult, keepAliveResult, resultBO);
            // 5.取消不再需要的任务
            KeepAliveTaskResultBO cancelledResult = KeepAliveTaskResultBO.createTaskCancelledResult(resultBO.getSource(), resultBO.getOperationType(), resultBO.getOperator(), resultBO.getRemark());
            cancelTasks(processingResult.getTasksToCancel(), cancelledResult);
            // 6.创建任务
            createNewTasks(merchantSn, processingResult.getValidParamsToCreate(), resultBO);
            log.info("商户 {} 保活任务创建完成", merchantSn);

        } catch (Exception e) {
            log.error("为商户 {} 创建保活任务失败", merchantSn, e);
            throw new ContractBizException("创建保活任务失败: " + e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void closeKeepAliveTaskFromAPI(KeepAliveTaskStatusUpdateRequest request) {
        // 查询任务
        Optional<ProviderParamsKeepaliveTaskDO> providerParamsKeepaliveTaskDO = providerParamsKeepaliveTaskDAO.selectByTaskId(Long.valueOf(request.getTaskId()));
        if (!providerParamsKeepaliveTaskDO.isPresent()) {
            throw new ContractBizException("未找到对应的任务");
        }
        ProviderParamsKeepaliveTaskDO taskDO = providerParamsKeepaliveTaskDO.get();
        // 设置任务状态
        ProviderParamsKeepaliveTaskStatusEnum targetStatus =
                request.getStatus() == 1 ? ProviderParamsKeepaliveTaskStatusEnum.SUCCESS : ProviderParamsKeepaliveTaskStatusEnum.FAILED;

        // 更新子商户号交易时间
        if (ProviderParamsKeepaliveTaskStatusEnum.SUCCESS.equals(targetStatus)) {
            subMchIdLastTradeTimeBiz.insertOrUpdateSubMchIdLastTradeTime(taskDO, request.getPayTime());
        }

        if (!taskDO.getStatus().equals(ProviderParamsKeepaliveTaskStatusEnum.ACTIVE.getValue())) {
            throw new ContractBizException("该任务未在保活中");
        }
        if (targetStatus == ProviderParamsKeepaliveTaskStatusEnum.SUCCESS) {
            reCreateKeepAliveTasksWhenSuccess(taskDO, request.getOrderNo(), request.getAmount(), request.getPayTime(), KeepAliveTaskResultBO.createExecuteSuccessResultFromAPI(request.getSource(), request.getSource()));
        } else {
            reCreateKeepAliveTasksWhenFail(taskDO, KeepAliveTaskResultBO.createExecuteFailedFromAPI(request.getSource(), request.getSource(), request.getReason()));
        }
        // 发送通知
        if (targetStatus == ProviderParamsKeepaliveTaskStatusEnum.SUCCESS) {
            aopClient.sendKeepAliveTaskSuccessNotice(taskDO.getMerchantSn());
        }
    }

    /**
     * 保活任务取消时重新创建保活任务
     * <p>
     * 业务流程：
     * 1. 更新任务的状态和结果信息
     * 2. 全部重新计算需要保活的信息 & 插入数据库
     *
     * @param taskDO   失败的保活任务
     * @param resultBO 任务结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void reCreateKeepAliveTasksWhenCancel(ProviderParamsKeepaliveTaskDO taskDO, KeepAliveTaskResultBO resultBO) {
        try {
            log.info("开始处理保活任务取消重建，任务ID: {}, 商户号: {}", taskDO.getTaskId(), taskDO.getMerchantSn());

            // 1. 更新取消任务状态
            providerParamsKeepaliveTaskDAO.cancelKeepAliveTask(taskDO, resultBO);
            // 2.发送 kafka消息
            keepAliveMessageBiz.sendKeepAliveMessage(taskDO);

            // 3. 重新计算需要保活的信息 & 插入数据库
            createKeepAliveTasks(taskDO.getMerchantSn(), resultBO);
        } catch (Exception e) {
            log.error("处理保活任务结束重建异常，任务ID: {}", taskDO.getTaskId(), e);
            throw new ContractBizException("保活任务结束重建处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保活任务失败时重新创建保活任务
     * <p>
     * 业务流程：
     * 1. 更新任务的状态和结果信息
     * 2. 全部重新计算需要保活的信息 & 插入数据库
     *
     * @param taskDO   失败的保活任务
     * @param resultBO 任务结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void reCreateKeepAliveTasksWhenFail(ProviderParamsKeepaliveTaskDO taskDO, KeepAliveTaskResultBO resultBO) {
        try {
            log.info("开始处理保活任务失败重建，任务ID: {}, 商户号: {}", taskDO.getTaskId(), taskDO.getMerchantSn());

            // 1. 更新失败任务状态
            providerParamsKeepaliveTaskDAO.failKeepAliveTask(taskDO, resultBO);
            // 2.发送 kafka消息
            keepAliveMessageBiz.sendKeepAliveMessage(taskDO);

            // 3. 重新计算需要保活的信息 & 插入数据库
            createKeepAliveTasks(taskDO.getMerchantSn(), resultBO);
        } catch (Exception e) {
            log.error("处理保活任务结束重建异常，任务ID: {}", taskDO.getTaskId(), e);
            throw new ContractBizException("保活任务结束重建处理失败: " + e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void reCreateKeepAliveTasksWhenSuccess(ProviderParamsKeepaliveTaskDO taskDO, String orderNumber, long payAmount, long payTime , KeepAliveTaskResultBO resultBO) {
        try {
            log.info("开始处理保活任务成功重建，任务ID: {}, 商户号: {}", taskDO.getTaskId(), taskDO.getMerchantSn());
            // 1. 更新任务状态
            providerParamsKeepaliveTaskDAO.completeKeepAliveTask(taskDO, orderNumber, payAmount, payTime, resultBO);
            // 2.发送 kafka消息
            keepAliveMessageBiz.sendKeepAliveMessage(taskDO);

            // 3. 重新计算需要保活的信息 & 插入数据库
            createKeepAliveTasks(taskDO.getMerchantSn(), resultBO);
        } catch (Exception e) {
            log.error("处理保活任务结束重建异常，任务ID: {}", taskDO.getTaskId(), e);
            throw new ContractBizException("保活任务结束重建处理失败: " + e.getMessage(), e);
        }
    }


    /**
     * 关闭商户配置关闭时的保活任务
     *
     * @param merchantSn 商户序列号
     * @param resultBO   任务结果BO，用于记录任务取消的原因和相关信息
     */
    public void closeKeepAliveTasksWhenCloseConfig(String merchantSn, KeepAliveTaskResultBO resultBO) {
        List<ProviderParamsKeepaliveTaskDO> providerParamsKeepaliveTaskDOS = Collections.emptyList();
        try {
            // 查询指定商户下所有未完成的保活任务
            providerParamsKeepaliveTaskDOS = providerParamsKeepaliveTaskDAO.selectNotCompletedTasksByMerchantSn(merchantSn);
            if (WosaiCollectionUtils.isEmpty(providerParamsKeepaliveTaskDOS)) {
                return;
            }
            // 构造需要更新的任务列表，将状态设置为已取消
            providerParamsKeepaliveTaskDAO.batchCancelKeepAliveTasks(providerParamsKeepaliveTaskDOS, resultBO);
            keepAliveMessageBiz.batchSendKeepAliveMessage(providerParamsKeepaliveTaskDOS);
        } catch (Exception e) {
            log.error("关闭保活任务异常，任务: {}", JSON.toJSONString(providerParamsKeepaliveTaskDOS), e);
            throw new ContractBizException("关闭保活任务异常: " + e.getMessage(), e);
        }
    }


    /**
     * 处理现有任务：取消不需要的任务，筛选需要创建的任务
     *
     * @param requiredKeepAliveParams 需要保活的参数和日期
     * @param existingTasks           现有的未完成任务
     * @return 任务处理结果
     */
    private TaskProcessingResult processExistingTasks(
            Map<MerchantProviderParamsDO, LocalDate> requiredKeepAliveParams,
            List<ProviderParamsKeepaliveTaskDO> existingTasks) {

        // 获取需要保活的子商户号集合
        Set<String> requiredSubMchIds = requiredKeepAliveParams.keySet().stream()
                .map(MerchantProviderParamsDO::getPayMerchantId)
                .collect(Collectors.toSet());

        // 分离需要取消的任务和保留的任务
        List<ProviderParamsKeepaliveTaskDO> tasksToCancel = existingTasks.stream()
                .filter(task -> !requiredSubMchIds.contains(task.getSubMchId()))
                .collect(Collectors.toList());

        List<ProviderParamsKeepaliveTaskDO> tasksToKeep = existingTasks.stream()
                .filter(task -> requiredSubMchIds.contains(task.getSubMchId()))
                .collect(Collectors.toList());

        // 筛选出需要新增的任务参数
        Map<MerchantProviderParamsDO, LocalDate> paramsToCreate = filterParamsToCreate(
                requiredKeepAliveParams, tasksToKeep);

        return new TaskProcessingResult(tasksToKeep, paramsToCreate, tasksToCancel);
    }

    /**
     * 筛选出需要创建的参数（排除已存在的任务）
     */
    private Map<MerchantProviderParamsDO, LocalDate> filterParamsToCreate(
            Map<MerchantProviderParamsDO, LocalDate> requiredParams,
            List<ProviderParamsKeepaliveTaskDO> existingTasks) {

        Set<String> existingSubMchIds = existingTasks.stream()
                .map(ProviderParamsKeepaliveTaskDO::getSubMchId)
                .collect(Collectors.toSet());

        Map<MerchantProviderParamsDO, LocalDate> paramsToCreate = new LinkedHashMap<>();
        requiredParams.forEach((param, date) -> {
            if (!existingSubMchIds.contains(param.getPayMerchantId())) {
                paramsToCreate.put(param, date);
            }
        });

        return paramsToCreate;
    }

    /**
     * 调整日期冲突并过滤任务
     */
    private void filterValidParamsWithDateAdjustment(String merchantSn, TaskProcessingResult processingResult,
                                                     KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult keepAliveResult,
                                                     KeepAliveTaskResultBO resultBO) {
        if (processingResult.getParamsToCreate().isEmpty()) {
            log.info("商户 {} 无需要创建的新任务", merchantSn);
            return;
        }

        // 构建现有任务的日期-Provider映射，用于日期冲突检测
        Map<MerchantProviderParamsDO, LocalDate> existingTasksDateMap = buildExistingTasksDateMap(
                processingResult.getExistingTasks());

        // 合并需要创建的任务和现有任务，进行统一的日期调整
        Map<MerchantProviderParamsDO, LocalDate> allTasksDateMap = new LinkedHashMap<>();
        allTasksDateMap.putAll(existingTasksDateMap);
        allTasksDateMap.putAll(processingResult.getParamsToCreate());

        // 使用 KeepAliveDateAdjuster 进行日期调整
        Map<MerchantProviderParamsDO, LocalDate> adjustedDateMap =
                KeepAliveDateAdjuster.adjustDuplicateDates(allTasksDateMap);

        // 提取出需要创建的任务的最终日期
        Map<MerchantProviderParamsDO, LocalDate> finalParamsToCreate = new LinkedHashMap<>();
        processingResult.getParamsToCreate().keySet().forEach(param -> {
            finalParamsToCreate.put(param, adjustedDateMap.get(param));
        });

        // 验证任务时间并过滤无效任务
        Map<MerchantProviderParamsDO, LocalDate> validParamsToCreate =
                validateAndFilterTaskDates(merchantSn, finalParamsToCreate, keepAliveResult);
        processingResult.setValidParamsToCreate(validParamsToCreate);
        // 创建新任务
        if (validParamsToCreate.isEmpty()) {
            log.info("商户 {} 所有任务都因时间限制被过滤，无任务创建", merchantSn);
        }
    }

    /**
     * 验证任务时间并过滤无效任务
     * <p>
     * 过滤规则：
     * 1. 如果保活时间 <= 今天，不创建任务（已过期）
     * 2. 如果保活时间 > 最大保活时间，不创建任务（超过最大期限）
     *
     * @param merchantSn      商户号
     * @param paramsToCreate  待创建的任务参数和日期
     * @param keepAliveResult 包含最大保活日期的结果
     * @return 过滤后的有效任务参数和日期
     */
    private Map<MerchantProviderParamsDO, LocalDate> validateAndFilterTaskDates(
            String merchantSn, Map<MerchantProviderParamsDO, LocalDate> paramsToCreate,
            KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult keepAliveResult) {

        Map<MerchantProviderParamsDO, LocalDate> validParams = new LinkedHashMap<>();
        LocalDate today = LocalDate.now();

        for (Map.Entry<MerchantProviderParamsDO, LocalDate> entry : paramsToCreate.entrySet()) {
            MerchantProviderParamsDO param = entry.getKey();
            LocalDate keepAliveDate = entry.getValue();

            // 验证1：检查是否已过期（保活时间 <= 今天）
            if (keepAliveDate.isBefore(today) || keepAliveDate.equals(today)) {
                log.warn("商户 {} 的保活任务已过期，不创建任务。Provider: {}, Payway: {}, SubMchId: {}, 保活日期: {}, 今天: {}",
                        merchantSn, param.getProvider(), param.getPayway(), param.getPayMerchantId(), keepAliveDate, today);
                continue;
            }

            // 验证2：检查是否超过最大保活期限（使用预计算的最大保活日期）
            LocalDate maxKeepAliveDate = keepAliveResult.getMaxKeepAliveDate(param);
            if (keepAliveDate.isAfter(maxKeepAliveDate)) {
                log.warn("商户 {} 的保活任务超过最大保活期限，不创建任务。Provider: {}, Payway: {}, SubMchId: {}, 保活日期: {}, 最大保活期限: {}",
                        merchantSn, param.getProvider(), param.getPayway(), param.getPayMerchantId(), keepAliveDate, maxKeepAliveDate);
                continue;
            }

            // 通过验证的任务
            validParams.put(param, keepAliveDate);
        }

        log.info("商户 {} 任务时间验证完成，原始任务数: {}, 有效任务数: {}, 过滤任务数: {}",
                merchantSn, paramsToCreate.size(), validParams.size(), paramsToCreate.size() - validParams.size());

        return validParams;
    }

    /**
     * 构建现有任务的日期映射
     */
    private Map<MerchantProviderParamsDO, LocalDate> buildExistingTasksDateMap(
            List<ProviderParamsKeepaliveTaskDO> existingTasks) {

        Map<MerchantProviderParamsDO, LocalDate> dateMap = new LinkedHashMap<>();

        for (ProviderParamsKeepaliveTaskDO task : existingTasks) {
            // 为现有任务创建一个虚拟的 MerchantProviderParamsDO 对象用于日期调整
            MerchantProviderParamsDO virtualParam = new MerchantProviderParamsDO();
            virtualParam.setProvider(task.getProvider());
            virtualParam.setPayway(task.getPayway());
            virtualParam.setPayMerchantId(task.getSubMchId());
            virtualParam.setProviderMerchantId(task.getProviderMchId());

            dateMap.put(virtualParam, task.getStartDate());
        }

        return dateMap;
    }

    private void cancelTasks(List<ProviderParamsKeepaliveTaskDO> tasksToCancel, KeepAliveTaskResultBO resultBO) {
        if (tasksToCancel.isEmpty()) {
            return;
        }
        providerParamsKeepaliveTaskDAO.batchCancelKeepAliveTasks(tasksToCancel, resultBO);
        keepAliveMessageBiz.batchSendKeepAliveMessage(tasksToCancel);
    }

    private void createNewTasks(String merchantSn, Map<MerchantProviderParamsDO, LocalDate> paramsToCreate, KeepAliveTaskResultBO resultBO) {
        if (paramsToCreate.isEmpty()) {
            return;
        }

        List<ProviderParamsKeepaliveTaskDO> insertList = paramsToCreate.entrySet().stream()
                .map(entry -> {
                    MerchantProviderParamsDO param = entry.getKey();
                    LocalDate startDate = entry.getValue();

                    ProviderParamsKeepaliveTaskDO task = new ProviderParamsKeepaliveTaskDO();
                    task.setTaskId(SnowFlakeIdGenerator.getInstance().nextId());
                    task.setType(ProviderParamsKeepaliveTaskTypeEnum.AUTO.getValue());
                    task.setMerchantSn(merchantSn);
                    task.setProvider(param.getProvider());
                    task.setPayway(param.getPayway());
                    task.setProviderMchId(param.getProviderMerchantId());
                    task.setSubMchId(param.getPayMerchantId());
                    task.setStartDate(startDate);
                    task.setEndDate(startDate.plusDays(1));
                    task.setStatus(ProviderParamsKeepaliveTaskStatusEnum.PENDING.getValue());
                    task.updateResultWithProcess(resultBO);
                    return task;
                })
                .collect(Collectors.toList());

        providerParamsKeepaliveTaskDAO.batchInsert(insertList);
        keepAliveMessageBiz.batchSendKeepAliveMessage(insertList);
    }

    /**
     * 任务处理结果内部类
     * 用于封装任务处理过程中的中间结果
     */
    @Data
    private static class TaskProcessingResult {
        private List<ProviderParamsKeepaliveTaskDO> existingTasks;
        private Map<MerchantProviderParamsDO, LocalDate> paramsToCreate;
        private List<ProviderParamsKeepaliveTaskDO> tasksToCancel;
        private Map<MerchantProviderParamsDO, LocalDate> validParamsToCreate;

        TaskProcessingResult(List<ProviderParamsKeepaliveTaskDO> existingTasks,
                             Map<MerchantProviderParamsDO, LocalDate> paramsToCreate,
                             List<ProviderParamsKeepaliveTaskDO> tasksToCancel) {
            this.existingTasks = existingTasks;
            this.paramsToCreate = paramsToCreate;
            this.tasksToCancel = tasksToCancel;
        }
    }
}

