package com.wosai.upay.job.model.DO;

import lombok.Data;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * @Description: 实名升级任务与申请单关联关系
 * <AUTHOR>
 * @Date 2021/1/25 6:36 下午
 **/

@Data
@Accessors(chain = true)
public class TaskMch {

    private Long id;

    /**
     *
     * contract_task id
     */
    private Long task_id;

    /**
     * contract_sub_task id
     */
    private Long sub_task_id;

    /**
     * mch_auth_apply id
     */
    private Long auth_apply_id;

    /**
     * 微信子商户号
     */
    private String pay_merchant_id;

    private String merchant_sn;

    private Date time;

    private Date priority;
}