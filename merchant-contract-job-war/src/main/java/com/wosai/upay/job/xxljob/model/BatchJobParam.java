package com.wosai.upay.job.xxljob.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class BatchJobParam extends JobParam {
    /**
     * 执行模式
     * SYNC_SERIAL：同步串行执行
     * SYNC_PARALLEL：同步并行执行
     * ASYNC_SERIAL：异步串行执行
     * ASYNC_PARALLEL：异步并行执行
     */
    private BatchExecTypeEnum execType;

}