package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.McRuleGroupDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.McRuleGroupDO;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * 报备规则组表数据库访问层 {@link McRuleGroupDO}
 * 对McRuleGroupMapper层做出简单封装 {@link McRuleGroupDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class McRuleGroupDAO extends AbstractBaseDAO<McRuleGroupDO, McRuleGroupDynamicMapper> {

    @Autowired
    public McRuleGroupDAO(SqlSessionFactory sqlSessionFactory, McRuleGroupDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据收单机构和状态查询报备规则组
     *
     * @param acquirer 收单机构
     * @param status   状态
     * @return 报备规则组列表
     */
    public List<McRuleGroupDO> listByAcquirerAndStatus(String acquirer, Integer status) {
        if (StringUtils.isBlank(acquirer) || Objects.isNull(status)) {
            return Collections.emptyList();
        }
        return entityMapper.selectList(new LambdaQueryWrapper<McRuleGroupDO>()
                .eq(McRuleGroupDO::getAcquirer, acquirer).eq(McRuleGroupDO::getStatus, status));
    }

    /**
     * 根据收单机构,业务方,启用状态,默认状态查询报备规则组
     *
     * @param acquirer      收单机构
     * @param vendorApp     业务方
     * @param validStatus   启用状态
     * @param defaultStatus 默认状态
     * @return 报备规则组列表
     */
    public List<McRuleGroupDO> listRuleGroup(String acquirer, String vendorApp, Integer validStatus, Integer defaultStatus) {
        LambdaQueryWrapper<McRuleGroupDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(McRuleGroupDO::getAcquirer, acquirer)
                .eq(McRuleGroupDO::getVendorApp, vendorApp)
                .eq(McRuleGroupDO::getStatus, validStatus)
                .eq(McRuleGroupDO::getDefaultStatus, defaultStatus);
        return entityMapper.selectList(wrapper);
    }

    public String getDefaultRuleGroup(String acquirer) {
        if (StringUtils.isEmpty(acquirer)) {
            return "";
        }
        LambdaQueryWrapper<McRuleGroupDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(McRuleGroupDO::getAcquirer, acquirer);
        wrapper.eq(McRuleGroupDO::getDefaultStatus, 1);
        McRuleGroupDO mcRuleGroupDO = entityMapper.selectOne(wrapper);
        if (Objects.isNull(mcRuleGroupDO)) {
            return "";
        }
        return mcRuleGroupDO.getGroupId();
    }
}
