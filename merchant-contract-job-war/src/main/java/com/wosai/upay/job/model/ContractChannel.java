package com.wosai.upay.job.model;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.refactor.model.entity.McChannelDO;
import com.wosai.upay.merchant.contract.model.weixin.AuthV3Param;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 报备渠道
 *
 * <AUTHOR>
 * @date 2019-07-05
 */
@Data
public class ContractChannel {

    private String channel;

    private String name;

    private String subject;

    private Integer payway;

    private String payway_channel_no;

    private String channel_no;

    private String private_key;

    private String provider;

    /**
     * 结算通道参数
     */
    private Map<String, Object> provider_metadata;

    private String acquirer;

    /**
     * 收单机构参数
     */
    private Map<String, Object> acquirer_metadata;

    /**
     * provider_metadata + acquirer_metadata
     */
    private Map<String, Object> channelParam = new HashMap<>();

    public ContractChannel() {
    }

    public ContractChannel(McChannelDO mcChannel) {
        BeanUtils.copyProperties(mcChannel, this);
        payway_channel_no = mcChannel.getPaywayChannelNo();
        channel_no = mcChannel.getChannelNo();
        private_key = mcChannel.getPrivateKey();

        provider_metadata = JSON.parseObject(mcChannel.getProviderMetadata(), Map.class);
        if (provider_metadata == null) {
            provider_metadata = new HashMap<>();
        }
        acquirer_metadata = JSON.parseObject(mcChannel.getAcquirerMetadata(), Map.class);
        if (acquirer_metadata == null) {
            acquirer_metadata = new HashMap<>();
        }

        channelParam.putAll(acquirer_metadata);
        channelParam.putAll(provider_metadata);
    }

    /**
     * 存储于providerMetaData 中的key
     **/
    private static final String KEY_AUTH_PARAM = "authV3Param";

    public AuthV3Param buildAuthV3Param() {
        try {
            Map authV3 = (Map) channelParam.get(ContractChannel.KEY_AUTH_PARAM);
            AuthV3Param authV3Param = new AuthV3Param();
            org.apache.commons.beanutils.BeanUtils.populate(authV3Param, authV3);
            return authV3Param;
        } catch (Exception e) {
            throw new CommonPubBizException("缺少authV3Param", e);
        }
    }
}
