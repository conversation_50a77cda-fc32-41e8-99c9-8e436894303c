package com.wosai.upay.job.biz.bankDirect;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.ViewProcess;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.paramContext.ParamContextBiz.DEDICATED_PARAMS;
import static com.wosai.upay.job.providers.PsbcProvider.replaceHttp;

@Component
@Slf4j
public class UmbDirectBiz extends AbstractBankDirectApplyBiz {

    @Value("${umb_dev_code}")
    public String umbDevCode;

    @Value("${umb_platmer_id_1}")
    public String umbPlatMerId1;

    @Value("${umb_platmer_id_2}")
    public String umbPlatMerId2;

    @Autowired
    private ContractStatusMapper contractStatusMapper;


    @Override
    public void preCheck(BankDirectApply bankDirectApply, String merchantSn) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (Objects.isNull(contractStatus)) {
            ContractStatus insert = new ContractStatus()
                    .setStatus(ContractStatus.STATUS_PROCESS)
                    .setMerchant_sn(merchantSn)
                    .setAcquirer(AcquirerTypeEnum.UMB.getValue());
            contractStatusMapper.insertSelective(insert);
        }
        checkBankDirectApplyStatus(bankDirectApply);
    }

    protected String chooseGroupByContextAndDevCode(Map<String, Object> context, String devCode) {
        Map<String, Object> merchant = (Map<String, Object>) context.get("merchant");
        String industry = (String) merchant.get("industry");

        Map<String, Object> dedicatedMap = (Map<String, Object>) context.get(DEDICATED_PARAMS);
        String comeFrom = MapUtil.getString(dedicatedMap, "come_from", "sqb");
        String groupKey = industry;
        String groupId;
        if (StrUtil.isEmpty(comeFrom) || "sqb".equals(comeFrom)) {
            groupKey += "-sqb";
            groupId = umbPlatMerId2;
        } else {
            groupKey += "-umb";
            groupId = umbPlatMerId1;
        }
        final Map<String, String> umbIndustryToGroupMap = applicationApolloConfig.getUmbIndustryToGroupMap();
        String groupIdByIndustry = umbIndustryToGroupMap.get(groupKey);
        return StrUtil.isEmpty(groupIdByIndustry) ? groupId : groupIdByIndustry;
    }

    @Override
    protected Map<String, Object> completeContext(Map<String, Object> paramContext, BankDirectReq bankDirectReq) {
        final Map formBody = JSONObject.parseObject(bankDirectReq.getForm_body(), Map.class);
        final List config = JSONObject.parseObject(BeanUtil.getPropString(formBody, "merchant_config"), List.class);
        paramContext.put("trade_combo_id", BeanUtil.getPropString(formBody, "trade_combo_id"));
        paramContext.put("merchant_config", BeanUtil.getPropString(formBody, "merchant_config"));
        //放入业务标识
        paramContext.put("dev_code", bankDirectReq.getDev_code());
        //放入spa可以识别的费率信息
        final List<Map> list = ((List<Map>) config).stream().map(x -> {
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.WEIXIN.getValue()) {
                String rateText = BeanUtil.getPropString(x, "rate");
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.WECHAT_PAY_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, rateText
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.ALIPAY.getValue()) {
                String rateText = BeanUtil.getPropString(x, "rate");
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.ALIPAY_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, rateText
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.UNIONPAY.getValue()) {
                String rateText = BeanUtil.getPropString(x, "rate");
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_DEBIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, rateText
                );
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        paramContext.putIfAbsent(ParamContextBiz.MERCHANT_FEE_RATES, list);
        return paramContext;
    }

    @Override
    public List<ViewProcess> initViewProcess(String merchantSn) {
        final List<ViewProcess> viewProcesses = preViewProcess(this.getAcquire());
        if (CollectionUtils.isEmpty(viewProcesses)) {
            return Lists.newArrayList();
        }
        //设置微信图片地址链接
        viewProcesses.forEach(x -> {
            if (Objects.equals(x.getExtra(), Boolean.TRUE)) {
                final String imageUrl = getWXImageUrl(getAcquire(), getProvider());
                x.setExtraMessage(imageUrl);
                x.setAliMessage(replaceHttp("https://images.wosaimg.com/43/94c324ceebb13328dd8d980818e6d3f4f57756.png"));
            }
        });
        return viewProcesses;
    }

    @Override
    protected Integer getBankRef(String dev_code) {
        return BankDirectApplyRefEnum.UMB.getValue();
    }

    @Override
    protected String getAcquire() {
        return AcquirerTypeEnum.UMB.getValue();
    }

    @Override
    protected Integer getProvider() {
        return ProviderEnum.PROVIDER_UMB.getValue();
    }

    @Override
    public String getDevCode() {
        return umbDevCode;
    }
}
