package com.wosai.upay.job.util;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.core.crypto.exception.BizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.constant.PayConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
public class CommonUtil {

    public static Map<String, Map> convert(List<Map> data, String key) {
        Map<String, Map> result = new HashMap<>();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }

        for (Map map : data) {
            result.put(BeanUtil.getPropString(map, key), map);
        }
        return result;
    }

    public static List<Map> convertToListMap(Map<String, Map> object) {
        if (CollectionUtils.isEmpty(object)) {
            return Collections.EMPTY_LIST;
        }
        List<Map> result = new ArrayList<>();
        for (String key : object.keySet()) {
            result.add(object.get(key));
        }
        return result;
    }

    public static List getValues(List<Map> data, String key) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.EMPTY_LIST;
        }
        List result = new ArrayList();
        for (Map map : data) {
            result.add(MapUtils.getObject(map, key));
        }
        return result;
    }


    /**
     * 提供精确的小数位四舍五入处理。
     *
     * @param v     需要四舍五入的数字
     * @param scale 小数点后保留几位
     * @return 四舍五入后的结果
     */
    public static double round(double v, int scale) {

        if (scale < 0) {
            return 0;
        }
        BigDecimal b = new BigDecimal(Double.toString(v));
        BigDecimal one = new BigDecimal("1");
        return b.divide(one, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }


    public static String substring(String str, int maxLength) {
        if (str != null && str.length() > maxLength) {
            return str.substring(0, maxLength - 1);
        }
        return str;
    }

    public static Map bytes2Map(byte[] bytes) {
        try {
            if (bytes == null) {
                return new HashMap();
            } else {
                return JSON.parseObject(bytes, Map.class);
            }
        } catch (Exception e) {
            log.warn("解析数据错误: " + new String(bytes));
            return new HashMap();
        }
    }

    public static byte[] map2Bytes(Map map) {
        if (map == null) {
            return null;
        } else {
            return JSON.toJSONBytes(map);
        }
    }

    public static Map string2Map(String s) {
        try {
            if (WosaiStringUtils.isEmpty(s)) {
                return new HashMap();
            } else {
                return JSON.parseObject(s, Map.class);
            }
        } catch (Exception e) {
            log.warn("解析数据错误: " + s);
            return new HashMap();
        }
    }

    public static String map2String(Map map) {
        if (map == null) {
            return null;
        } else {
            return JSON.toJSONString(map);
        }
    }

    public static Map<String, String> buildApplyFeeRateMap(List<Map<String, Object>> configs) {
        Map<String, String> applyFeeRateMap = new HashMap<>();
        for (Map config : configs) {
            if (WosaiCollectionUtils.isNotEmpty((Collection) config.get("ladder_fee_rates"))) {
                List<Map> waitLadder = new ArrayList<>();
                List<Map> ladderReq = (List<Map>) config.get("ladder_fee_rates");
                for (Map map : ladderReq) {
                    Integer min = MapUtils.getInteger(map, "min");
                    Integer max = MapUtils.getInteger(map, "max");
                    // 银行通道切换到间连再切回, configs里会没有rate信息,取bscFeeRate字段
                    String rate = MapUtils.getString(map, "rate", MapUtils.getString(map, "bscFeeRate"));
                    waitLadder.add(CollectionUtil.hashMap("min", min == null ? 0 : min, "max", max == null ? Integer.MAX_VALUE : max, "fee_rate", rate));
                }
                applyFeeRateMap.put(BeanUtil.getPropString(config, "payway"), JSON.toJSONString(CollectionUtil.hashMap("fee_rate_type", "ladder", "value", waitLadder)));
            } else if (BeanUtil.getPropString(config, "rate", "").contains("以上")) {
                applyFeeRateMap.put(BeanUtil.getPropString(config, "payway"), JSON.toJSONString(CollectionUtil.hashMap("fee_rate_type", "ladder", "value", getLadderFeeRateFromText(BeanUtil.getPropString(config, "rate")))));
            } else if (WosaiCollectionUtils.isNotEmpty((Collection) config.get("channel_ladder"))) {
                //渠道阶梯费率
                List<Map> ladderReq = (List<Map>) config.get("channel_ladder");
                List<Map> value = new ArrayList<>();
                for (Map map : ladderReq) {
                    //类型  credit   or  debit
                    String type = MapUtils.getString(map, "type");
                    List<Map> waitLadderInner = new ArrayList<>();

                    List<Map> LadderFeeRates = (List<Map>) map.get("ladder_fee_rates");

                    for (Map ladderFeeRate : LadderFeeRates) {
                        Integer min = MapUtils.getInteger(ladderFeeRate, "min");
                        Integer max = MapUtils.getInteger(ladderFeeRate, "max");
                        //费率
                        String rate = MapUtils.getString(ladderFeeRate, "rate");
                        waitLadderInner.add(CollectionUtil.hashMap("min", min == null ? 0 : min, "max", max == null ? Integer.MAX_VALUE : max, "fee_rate", rate));
                    }
                    Map valueInner = CollectionUtil.hashMap("type", type, "ladder_fee_rates", waitLadderInner);
                    value.add(valueInner);
                }

                applyFeeRateMap.put(BeanUtil.getPropString(config, "payway"), JSON.toJSONString(CollectionUtil.hashMap("fee_rate_type", "channel_ladder", "value", value)));
            } else if (WosaiCollectionUtils.isNotEmpty((Collection) config.get("channel"))) {
                //渠道费率
                List<Map> ladderReq = (List<Map>) config.get("channel");
                List<Map> value = new ArrayList<>();
                for (Map map : ladderReq) {
                    String type = MapUtils.getString(map, "type");
                    String min = MapUtils.getString(map, "min");
                    String max = MapUtils.getString(map, "max");
                    String rate = MapUtils.getString(map, "rate");
                    value.add(CollectionUtil.hashMap("type", type, "fee_rate", rate));
                }
                applyFeeRateMap.put(BeanUtil.getPropString(config, "payway"), JSON.toJSONString(CollectionUtil.hashMap("fee_rate_type", "channel", "value", value)));
            } else {
                applyFeeRateMap.put(BeanUtil.getPropString(config, "payway"), BeanUtil.getPropString(config, "rate"));
            }
        }
        return applyFeeRateMap;
    }

    /**
     * 有些阶梯费率格式是 300以下0.25, 300以上0.38
     *
     * @param rate
     * @return
     */
    private static List<Map> getLadderFeeRateFromText(String rate) {
        List<Map> waitLadder = new ArrayList<>();
        String[] rates = rate.split("\\,");
        for (String s : rates) {
            if (s.contains("以下")) {
                String[] mins = s.split("以下");
                waitLadder.add(CollectionUtil.hashMap("min", 0, "max", Integer.valueOf(mins[0].trim()), "fee_rate", mins[1].trim()));
            } else {
                String[] maxs = s.split("以上");
                waitLadder.add(CollectionUtil.hashMap("min", Integer.valueOf(maxs[0].trim()), "max", Integer.MAX_VALUE, "fee_rate", maxs[1].trim()));
            }
        }
        return waitLadder;
    }

    public static Date getLastMonth() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -1);
        return cal.getTime();
    }

}
