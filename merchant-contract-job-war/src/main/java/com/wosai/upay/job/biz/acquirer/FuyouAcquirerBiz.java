package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.dao.*;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.FuyouParam;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import com.wosai.upay.merchant.contract.model.weixin.SubdevConfigResp;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-10-07
 */
@Component("fuyou-biz")
@Slf4j
public class FuyouAcquirerBiz implements IAcquirerBiz {

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private FuyouService fuyouService;

    @Autowired
    MerchantBusinessLicenseService mcMerchantBusinessLicenseService;

    @Autowired
    private MerchantBankService merchantBankService;


    @Autowired
    private IndustryV2Service industryService;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private McChannelDAO mcChannelDAO;

    @Value("${fy_pso_dev_code}")
    public String fyPsoDevCode;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private MerchantProviderParamsExtDAO merchantProviderParamsExtDAO;

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        return BeanUtil.getPropString(merchantConfig, "params.fy_trade_params.fy_mch_id");
    }


    /**
     * 获取微信普通渠道规则
     *
     * @return
     */
    @Override
    public String getNormalWxRule() {
        return ContractRuleConstants.FUYOU_NORMAL_WEIXIN_RULE;
    }


    @Override
    public WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        ContractResponse response = null;
        WxMchInfo wxMchInfo = new WxMchInfo();
        MchInfo mchInfo = null;
        SubdevConfigResp resp;
        try {
            response = fuyouService.queryMerchant(providerParams.getMerchant_sn(), fuyouParam);
            if (!response.isSuccess()) {
                return new WxMchInfo();
            }
            Map param = response.getResponseParam();
            mchInfo = new MchInfo();
            mchInfo.setMch_id(providerParams.getPay_merchant_id());
            mchInfo.setMerchant_name(BeanUtil.getPropString(param, "mchntName"));
            mchInfo.setBusiness(BeanUtil.getPropString(param, "business"));
            wxMchInfo.setMchInfo(mchInfo);
            resp = fuyouService.queryWeixinSubdevConfig(providerParams.getMerchant_sn(), fuyouParam);
            wxMchInfo.setSubdevConfig(resp);
        } catch (Exception e) {
            log.error("查询微信子商户号信息失败: {}", providerParams.getPay_merchant_id(), e);
        }
        return wxMchInfo;
    }


    @Override
    public AlipayMchInfo getAlipayMchInfo(MerchantProviderParams providerParams) {
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        ContractResponse response = fuyouService.queryMerchant(providerParams.getMerchant_sn(), fuyouParam);
        Map param = response.getResponseParam();
        AlipayMchInfo mchInfo = new AlipayMchInfo();
        mchInfo.setSub_merchant_id(providerParams.getPay_merchant_id());
        mchInfo.setName(BeanUtil.getPropString(param, "mchntName"));
        mchInfo.setAlias_name(BeanUtil.getPropString(param, "mchntShortname"));
        if (!response.isSuccess()) {
            throw new ContractBizException(response.getMessage());
        }
        return mchInfo;
    }

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_FUYOU;
    }


    /**
     * 查询富友实时费率
     *
     * @param merchantSn
     * @return
     */
    public List<Map> queryFeeRate(String merchantSn) {
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        ContractResponse response = fuyouService.queryMerchant(merchantSn, fuyouParam);
        if (!response.isSuccess()) {
            return new ArrayList<>();
        }
        Map param = response.getResponseParam();
        List<Map> mchntSetCdList = (List<Map>) param.get("mchntSetCdList");
        Map fuyouFeeRateMapping = applicationApolloConfig.getFuyouRate();
        return parsingSetCdList(merchantSn, mchntSetCdList, fuyouFeeRateMapping, param);
    }

    private List<Map> parsingSetCdList(String merchantSn, List<Map> mchntSetCdList, Map fuyouFeeRateMapping, Map param) {
        List<Map> result = new ArrayList<>();
        for (Map setCdMap : mchntSetCdList) {
            if ("微信扫码支付".equals(BeanUtil.getPropString(setCdMap, "busiCd"))) {
                String setCd = BeanUtil.getPropString(setCdMap, "setCd");
                Map feeRateMap = setCd2FeeRate(setCd, fuyouFeeRateMapping);
                if (WosaiMapUtils.isNotEmpty(feeRateMap)) {
                    feeRateMap.put("merchant_name", BeanUtil.getPropString(param, "realName"));
                    feeRateMap.put("payway", PaywayEnum.WEIXIN.getValue());
                    feeRateMap.put("merchantSn", merchantSn);
                    feeRateMap.put("provider", ProviderEnum.PROVIDER_FUYOU.getValue());
                    result.add(feeRateMap);
                }
            } else if ("支付宝扫码支付".equals(BeanUtil.getPropString(setCdMap, "busiCd"))) {
                String setCd = BeanUtil.getPropString(setCdMap, "setCd");
                Map feeRateMap = setCd2FeeRate(setCd, fuyouFeeRateMapping);
                if (WosaiMapUtils.isNotEmpty(feeRateMap)) {
                    feeRateMap.put("merchant_name", BeanUtil.getPropString(param, "realName"));
                    feeRateMap.put("payway", PaywayEnum.ALIPAY.getValue());
                    feeRateMap.put("merchantSn", merchantSn);
                    feeRateMap.put("provider", ProviderEnum.PROVIDER_FUYOU.getValue());
                    result.add(feeRateMap);
                }
            } else if ("银联二维码支付".equals(BeanUtil.getPropString(setCdMap, "busiCd")) && "00".equals(BeanUtil.getPropString(setCdMap, "cardType"))) {
                String setCd = BeanUtil.getPropString(setCdMap, "setCd");
                String fee = BeanUtil.getPropString(setCd2FeeRate(setCd, fuyouFeeRateMapping), "c2b_fee_rate");
                String spcSetCd = BeanUtil.getPropString(setCdMap, "spcSetCd");
                String spcFee = BeanUtil.getPropString(setCd2FeeRate(spcSetCd, fuyouFeeRateMapping), "c2b_fee_rate");
                Map feeRateMap = buildUnionLadderFeeRate(spcFee, fee);
                if (WosaiMapUtils.isNotEmpty(feeRateMap)) {
                    feeRateMap.put("merchant_name", BeanUtil.getPropString(param, "realName"));
                    feeRateMap.put("payway", PaywayEnum.UNIONPAY.getValue());
                    feeRateMap.put("merchantSn", merchantSn);
                    feeRateMap.put("provider", ProviderEnum.PROVIDER_FUYOU.getValue());
                    result.add(feeRateMap);
                }
            }
        }
        return result;
    }

    private Map setCd2FeeRate(String setCd, Map feeRateMapping) {
        try {
            String feeRate = fuyouService.getFeeRateByTemplate(setCd);
            if (WosaiStringUtils.isNotEmpty(feeRate)) {
                Map result = JSON.parseObject(feeRate, Map.class);
                result.remove("payway");
                return result;
            }
        } catch (Exception e) {
            log.warn("解析富友费率失败 {}", setCd, e);
        }

        log.info("从Apollo解析费率 {}", setCd);

        for (Object entryObject : feeRateMapping.entrySet()) {
            Map.Entry entry = (Map.Entry) entryObject;
            if (setCd.equals(entry.getValue())) {
                String feeRateTag = entry.getKey().toString();
                boolean fixed = true;
                if (feeRateTag.contains("_")) {
                    String[] ladder = feeRateTag.split(",");
                    if (ladder.length > 1) {
                        fixed = false;
                    } else {
                        feeRateTag = feeRateTag.split("_")[1];
                    }
                }
                return fixed ? buildFixedFeeRate(feeRateTag) : buildLadderFeeRate(feeRateTag);
            }
        }
        log.warn("费率配置不存在 {}", setCd);
        return null;
    }


    private static Map buildLadderFeeRate(String feeRateTag) {
        Map feeMap = new HashMap();
        feeMap.put("fee_rate_type", "ladder");
        List<Map> labberFeeList = new ArrayList<>();
        String[] ladder = feeRateTag.split(",");
        for (String ladderTag : ladder) {
            String[] tagSplit = ladderTag.split("_");
            String fee = tagSplit[1];
            String[] scope = tagSplit[0].split("-");
            String min = scope[0];
            String max = scope.length > 1 ? scope[1] : null;
            labberFeeList.add(buildLadderFeeRateItem(min, max, fee));
        }
        feeMap.put("ladder_fee_rates", labberFeeList);
        return feeMap;
    }

    /**
     * 构建云闪付阶梯费率
     *
     * @param spcFee 0-1000 费率
     * @param fee    1000以上费率
     * @return
     */
    private static Map buildUnionLadderFeeRate(String spcFee, String fee) {
        Map feeMap = new HashMap();
        feeMap.put("fee_rate_type", "ladder");
        List<Map> labberFeeList = new ArrayList<>();
        labberFeeList.add(buildLadderFeeRateItem("0", "1000", spcFee));
        labberFeeList.add(buildLadderFeeRateItem("1000", null, fee));
        feeMap.put("ladder_fee_rates", labberFeeList);

        return feeMap;
    }

    private static Map buildLadderFeeRateItem(String min, String max, String fee) {
        Map<String, Object> feeMap = new HashMap<>();
        feeMap.put("min", min);
        if (WosaiStringUtils.isNotEmpty(max)) {
            feeMap.put("max", max);
        }
        feeMap.put("b2c_fee_rate", fee);
        feeMap.put("c2b_fee_rate", fee);
        feeMap.put("wap_fee_rate", fee);
        feeMap.put("mini_fee_rate", fee);
        return feeMap;
    }


    private static Map buildFixedFeeRate(String feeRateTag) {
        Map feeMap = new HashMap();
        feeMap.put("fee_rate_type", "fixed");
        feeMap.put("b2c_fee_rate", feeRateTag);
        feeMap.put("c2b_fee_rate", feeRateTag);
        feeMap.put("wap_fee_rate", feeRateTag);
        feeMap.put("mini_fee_rate", feeRateTag);
        return feeMap;
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_FUYOU.getValue());
        if (Objects.isNull(acquirerParams)) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .retry(false)
                    .build();
        }
        BasicProvider provider = providerFactory.getProvider(String.valueOf(acquirerParams.getProvider()));
        com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = provider.queryMerchantContractResult(acquirerParams.getProvider_merchant_id());
        Optional<MerchantProviderParamsDO> unionParam = getUnionPayParams(merchantSn, AcquirerTypeEnum.FU_YOU.getValue());
        if (contractResponse.isSystemFail()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .message(contractResponse.getMessage())
                    .retry(false)
                    .build();
        } else if (contractResponse.isBusinessFail()) {
            saveOrUpdateParamsExt(unionParam, MerchantProviderParamsExtDO.UNION_PAY_FAIL, contractResponse.getMessage());
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                    .message(contractResponse.getMessage())
                    .retry(true)
                    .build();
        } else {
            String status = WosaiMapUtils.getString(contractResponse.getTradeParam(), "status");
            if (MerchantProviderParamsExtDO.UNION_PAY_FAIL.equals(status)) {
                saveOrUpdateParamsExt(unionParam, status, contractResponse.getMessage());
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message(contractResponse.getMessage())
                        .retry(true)
                        .build();
            } else if (MerchantProviderParamsExtDO.UNION_PAY_SUCCESS.equals(status)) {
                // 如果参数不存在要存一下
                if (!unionParam.isPresent()) {
                    saveFuyouUnionPayParams(acquirerParams, contractResponse);
                }
                unionParam = getUnionPayParams(merchantSn, AcquirerTypeEnum.FU_YOU.getValue());
                saveOrUpdateParamsExt(unionParam, status, contractResponse.getMessage());
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                        .retry(true)
                        .build();
            } else {
                saveOrUpdateParamsExt(unionParam, status, "已注销");
                return UnionPayOpenStatusQueryResp.builder()
                        .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                        .message("已注销")
                        .retry(true)
                        .build();
            }
        }
    }

    private void saveFuyouUnionPayParams(MerchantProviderParams acquirerParams, ContractResponse contractResponse) {
        String payMerchantId = WosaiMapUtils.getString(contractResponse.getResponseParam(), "sub_mch_id");
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams();
        merchantProviderParams.setId(UUID.randomUUID().toString());
        merchantProviderParams.setMerchant_sn(acquirerParams.getMerchant_sn());
        merchantProviderParams.setOut_merchant_sn(acquirerParams.getOut_merchant_sn());
        merchantProviderParams.setMerchant_name(getFuYouUnionPayMerchantName(acquirerParams));
        merchantProviderParams.setCtime(System.currentTimeMillis());
        merchantProviderParams.setMtime(System.currentTimeMillis());
        merchantProviderParams.setDeleted(false);
        merchantProviderParams.setPayway(PaywayEnum.UNIONPAY.getValue());
        merchantProviderParams.setStatus(UseStatusEnum.NO_USE.getValue());
        merchantProviderParams.setChannel_no("fuyou");
        merchantProviderParams.setParent_merchant_id(acquirerParams.getParent_merchant_id());
        merchantProviderParams.setProvider_merchant_id(acquirerParams.getProvider_merchant_id());
        merchantProviderParams.setProvider(ProviderEnum.PROVIDER_FUYOU.getValue());
        merchantProviderParams.setParams_config_status(2);
        merchantProviderParams.setPay_merchant_id(payMerchantId);
        merchantProviderParams.setStatus(UseStatusEnum.NO_USE.getValue());
        merchantProviderParams.setContract_rule(ContractRuleConstants.FUYOU_NORMA_UNION_PAY_RULE);
        merchantProviderParams.setRule_group_id(McConstant.RULE_GROUP_FUYOU);
        Optional<MerchantProviderParamsDO> unionParam = getUnionPayParams(acquirerParams.getMerchant_sn(), AcquirerTypeEnum.FU_YOU.getValue());
        if (unionParam.isPresent()) {
            return;
        }
        merchantProviderParamsMapper.insertSelective(merchantProviderParams);
    }

    private String getFuYouUnionPayMerchantName(MerchantProviderParams acquirerParams) {
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        ContractResponse contractResponse = fuyouService.queryMerchant(acquirerParams.getMerchant_sn(), fuyouParam);
        if (contractResponse == null || !contractResponse.isSuccess()) {
            throw new ContractBizException("查询富友商户失败");
        }
        String merchantName = MapUtils.getString(contractResponse.getResponseParam(), "realName");
        if (StringUtils.isBlank(merchantName)) {
            throw new ContractBizException("富友商户m名称为空");
        }
        return merchantName;
    }

    private void saveOrUpdateParamsExt(Optional<MerchantProviderParamsDO> unionParam, String status, String message) {
        if (!unionParam.isPresent()) {
            return;
        }
        Optional<MerchantProviderParamsExtDO> unionPayStatus = merchantProviderParamsExtDAO.getUnionPayStatus(unionParam.get().getId());
        // 如果状态和文案没有发生变化就不需要去做更新
        if (unionPayStatus.isPresent() && Objects.equals(unionPayStatus.get().getExtField1(), status) && Objects.equals(unionPayStatus.get().getFailMessage(), message)) {
            return;
        }
        MerchantProviderParamsExtDO extDO = new MerchantProviderParamsExtDO();
        extDO.setParamId(unionParam.get().getId());
        extDO.setExtField2(unionParam.get().getPayMerchantId());
        extDO.setType(MerchantProviderParamsExtDO.UNION_PAY);
        extDO.setExtField1(status);
        extDO.setExtra(JSON.toJSONString(CollectionUtil.hashMap("message", message)));
        if (unionPayStatus.isPresent()) {
            extDO.setId(unionPayStatus.get().getId());
            merchantProviderParamsExtDAO.updateMerchantParams(extDO);
        } else {
            merchantProviderParamsExtDAO.saveMerchantParametersExt(extDO);
        }
    }


    @Override
    public Boolean bankAccountConsistent(String merchantSn, Map bankAccount) {
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        final ContractResponse contractResponse = fuyouService.queryMerchant(merchantSn, fuyouParam);
        if (!contractResponse.isSuccess()) {
            throw new CommonPubBizException(contractResponse.getMessage());
        }
        final Map<String, Object> responseParam = contractResponse.getResponseParam();
        List<Map> accountList = (List<Map>) responseParam.get("mchntAcntInfList");
        List<String> bankNoList = new ArrayList<>();
        for (Map<String, Object> account : accountList) {
            String acntNo = BeanUtil.getPropString(account, "acntNo");
            bankNoList.add(acntNo);
        }
        //收钱吧银行卡
        final String number = BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.NUMBER);
        final int bankAccountType = BeanUtil.getPropInt(bankAccount, MerchantBankAccountPre.TYPE, BankAccountTypeEnum.PERSONAL.getValue());
        log.info("富友通道下商户号merchantSn:{},富友银行账户:{},收钱吧银行账户:{}", merchantSn, JSONObject.toJSONString(bankNoList), number);

        // 收钱吧对公，富友双账户，银行卡不一致
        if (BankAccountTypeEnum.isPublic(bankAccountType) && bankNoList.size() > 1) {
            return false;
        }

        return bankNoList.contains(number);
    }


    private Optional<MerchantProviderParamsDO> getUnionPayParams(String merchantSn, String acquirer) {
        List<McChannelDO> mcChannels = mcChannelDAO.getMcChannelByAcquirer(acquirer, PaywayEnum.UNIONPAY.getValue());
        for (McChannelDO mcChannel : mcChannels) {
            Optional<MerchantProviderParamsDO> merChantProviderParams = merchantProviderParamsDAO.getMerChantProviderParams(merchantSn, mcChannel.getChannelNo(), PaywayEnum.UNIONPAY.getValue());
            if (merChantProviderParams.isPresent()) {
                return merChantProviderParams;
            }
        }
        return Optional.empty();

    }
}
