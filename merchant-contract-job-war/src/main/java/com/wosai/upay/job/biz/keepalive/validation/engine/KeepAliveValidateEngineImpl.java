package com.wosai.upay.job.biz.keepalive.validation.engine;


import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.job.adapter.apollo.KeepAliveApolloConfig;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveCheckRuleConfig;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveValidationScenarioEnum;
import com.wosai.upay.job.biz.keepalive.validation.factory.RuleFactory;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveCheckRuleResult;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidateContext;
import com.wosai.upay.job.biz.keepalive.validation.model.KeepAliveValidationResult;
import com.wosai.upay.job.biz.keepalive.validation.rule.KeepAliveCheckRule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 规则引擎实现类
 */
@Component
public class KeepAliveValidateEngineImpl implements KeepAliveValidateEngine {

    private static final Logger logger = LoggerFactory.getLogger(KeepAliveValidateEngineImpl.class);

    @Autowired
    private KeepAliveApolloConfig keepAliveApolloConfig;
    @Autowired
    private RuleFactory ruleFactory;

    @Override
    public KeepAliveValidationResult validate(KeepAliveValidateContext context, KeepAliveValidationScenarioEnum scenario) {
        long startTime = System.currentTimeMillis();
        logger.info("开始执行规则校验，商户SN: {}, 场景: {}", context.getMerchantSn(), scenario);

        KeepAliveValidationResult result = new KeepAliveValidationResult(true);

        try {
            // 1. 参数校验
            KeepAliveValidationResult checkContextResult = checkContextParams(context);
            if (!checkContextResult.isSuccess()) {
                logger.warn("参数校验未通过，商户SN: {}, 原因: {}", context.getMerchantSn(), checkContextResult.getFailureReason());
                return checkContextResult;
            }
            // 获取指定场景的规则配置
            List<KeepAliveCheckRuleConfig> keepAliveCheckRuleConfigs = keepAliveApolloConfig.getCachedSortedRules().get(scenario.getCode());
            if (WosaiCollectionUtils.isEmpty(keepAliveCheckRuleConfigs)) {
                logger.warn("场景 {} 没有配置规则", scenario);
                return result;
            }

            logger.debug("准备执行 {} 个规则，商户SN: {}", keepAliveCheckRuleConfigs.size(), context.getMerchantSn());

            // 3. 执行规则（快速失败）
            for (KeepAliveCheckRuleConfig ruleConfig : keepAliveCheckRuleConfigs) {
                KeepAliveCheckRuleResult ruleResult = executeRuleWithErrorHandling(ruleConfig, context);
                result.addRuleResult(ruleResult);

                // 快速失败：一旦有规则失败就停止执行
                if (!ruleResult.isPassed()) {
                    logger.warn("规则校验失败，停止后续规则执行。规则: {}, 商户SN: {}, 原因: {}",
                            ruleConfig.getRuleType(), context.getMerchantSn(), ruleResult.getMessage());
                    break;
                }
            }

        } catch (Exception e) {
            logger.error("规则引擎执行异常，商户SN: {}, 场景: {}", context.getMerchantSn(), scenario, e);
            result.setSuccess(false);
            result.setFailureReason("规则引擎执行异常: " + e.getMessage());
        }

        long duration = System.currentTimeMillis() - startTime;
        logger.info("规则校验完成，商户SN: {}, 场景: {}, 结果: {}, 耗时: {}ms",
                context.getMerchantSn(), scenario, result.isSuccess(), duration);

        return result;
    }

    private KeepAliveValidationResult checkContextParams(KeepAliveValidateContext context) {
        if (context == null) {
            return KeepAliveValidationResult.failure("上下文为空", "CONTEXT_IS_NULL");
        }
        if (Objects.isNull(context.getKeepAliveValidationScenarioEnum())) {
            return KeepAliveValidationResult.failure("场景为空", "SCENARIO_IS_NULL");
        }
        if (WosaiMapUtils.isEmpty(context.getMerchant())) {
            return KeepAliveValidationResult.failure("商户信息为空", "MERCHANT_IS_NULL");
        }
        if (!KeepAliveValidationScenarioEnum.OPEN_CIRCLE.equals(context.getKeepAliveValidationScenarioEnum()) && Objects.isNull(context.getTaskDO())) {
            return KeepAliveValidationResult.failure("保活任务为空", "TASK_IS_NULL");
        }
        return KeepAliveValidationResult.success();
    }

    /**
     * 执行单个规则并处理异常
     */
    private KeepAliveCheckRuleResult executeRuleWithErrorHandling(KeepAliveCheckRuleConfig ruleConfig, KeepAliveValidateContext context) {
        try {
            // 获取规则实现
            KeepAliveCheckRule rule = ruleFactory.getRule(ruleConfig);
            if (rule == null) {
                logger.error("无法获取规则实例，规则类型: {}", ruleConfig.getRuleType());
                return KeepAliveCheckRuleResult.failure(ruleConfig.getRuleType(), "无法获取规则实例", "RULE_NOT_FOUND");
            }

            return rule.execute(context, ruleConfig);

        } catch (Exception e) {
            logger.error("规则执行异常，类型: {}, 商户: {}", ruleConfig.getRuleType(), context.getMerchantSn(), e);
            return KeepAliveCheckRuleResult.failure(
                    ruleConfig.getRuleType(),
                    "规则执行异常: " + e.getMessage(),
                    "RULE_EXECUTION_ERROR"
            );
        }
    }
}