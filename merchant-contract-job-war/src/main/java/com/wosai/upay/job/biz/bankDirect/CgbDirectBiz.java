package com.wosai.upay.job.biz.bankDirect;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.paramContext.FeeRateUtil;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.model.psbc.SelfAuditRejectRequest;
import com.wosai.upay.job.service.callback.GuangfaCallBackService;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.model.guangfa.GFRecord;
import com.wosai.upay.merchant.contract.model.provider.GuangFaParam;
import com.wosai.upay.merchant.contract.service.GuangFaService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021-06-16
 */
@Component
public class CgbDirectBiz extends AbstractBankDirectApplyBiz {

    @Value("${cgb_dev_code}")
    public String cgbDevCode;
    @Autowired
    private GuangFaService guangFaService;
    @Autowired
    private GuangfaCallBackService guangfaCallBackService;

    @Override
    protected String chooseGroupByContextAndDevCode(Map<String, Object> context, String devCode) {
        return McConstant.RULE_GROUP_CGB;
    }

    @Override
    protected Map<String, Object> completeContext(Map<String, Object> paramContext, BankDirectReq bankDirectReq) {
        Map formBody = JSONObject.parseObject(bankDirectReq.getForm_body(), Map.class);
        List configs = JSONObject.parseObject(BeanUtil.getPropString(formBody, "merchant_config"), List.class);
        paramContext.put("cgb_feeRate", configs);
        paramContext.put("trade_combo_id", BeanUtil.getPropString(formBody, "trade_combo_id"));
//        //放入业务标识
        paramContext.put("dev_code", bankDirectReq.getDev_code());
        //放入spa可以识别的费率信息
        final List<Map> list = ((List<Map>) configs).stream().map(x -> {
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.WEIXIN.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.WECHAT_PAY_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.ALIPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.ALIPAY_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.UNIONPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_DEBIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            return null;
        }).filter(x -> Objects.nonNull(x)).collect(Collectors.toList());

        paramContext.putIfAbsent(ParamContextBiz.MERCHANT_FEE_RATES, list);

        //业务合作申请书
        String biz_apply = BeanUtil.getPropString(formBody, "biz_apply");
        Map merchant = (Map) paramContext.get("merchant");
        if (StringUtils.isNotBlank(biz_apply)) {
            merchant.put("biz_apply", JSONObject.parseObject(biz_apply, Map.class));
        }
        //法定代表人联系方式
        String artif_telNo = BeanUtil.getPropString(formBody, "phone_number");
        if (StringUtils.isNotBlank(artif_telNo)) {
            merchant.put("artif_telNo", artif_telNo);
        }
        return paramContext;
    }

    @Override
    protected Integer getBankRef(String dev_code) {
        return BankDirectApplyRefEnum.CGB.getValue();
    }

    @Override
    protected String getAcquire() {
        return AcquirerTypeEnum.CGB.getValue();
    }

    @Override
    protected Integer getProvider() {
        return ProviderEnum.PROVIDER_CGB.getValue();
    }

    @Override
    public String getDevCode() {
        return cgbDevCode;
    }

    /**
     * 撤回处理
     *
     * @param merchantSn
     * @param request
     * @return
     */
    @Override
    public com.wosai.upay.merchant.contract.model.ContractResponse doReject(String merchantSn, SelfAuditRejectRequest request, ContractSubTask contractSubTask) {

        GuangFaParam guangFaParam = guangfaCallBackService.getDefaultParam();
        GFRecord record = new GFRecord();
        record.setMerchantSn(merchantSn);
        record.setContractId(contractSubTask.getContract_id());
        return guangFaService.cancelContract(record, guangFaParam);

    }
}
