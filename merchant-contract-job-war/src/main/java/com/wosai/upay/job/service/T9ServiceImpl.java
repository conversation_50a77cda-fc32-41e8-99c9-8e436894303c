package com.wosai.upay.job.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.device.service.SimpleSmartPosInfoService;
import com.wosai.mc.constants.BankAccountEnum;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreExtService;
import com.wosai.operator.dto.request.CallBackRequest;
import com.wosai.operator.service.BusinessOpenService;
import com.wosai.sales.merchant.business.bean.CloseAppReq;
import com.wosai.sales.merchant.business.model.AppInfoModel;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.upay.bank.info.api.model.Industry;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.*;
import com.wosai.upay.job.Constants.ForeignCardConstant;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.Constants.PreAuthApplyConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.AgreementBiz;
import com.wosai.upay.job.biz.IndustryMappingCommonBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.acquirePos.T9HandleFactory;
import com.wosai.upay.job.biz.acquirePos.T9HandleService;
import com.wosai.upay.job.biz.acquirePos.TlT9HandleService;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.biz.store.StoreBiz;
import com.wosai.upay.job.mapper.DirectStatusMapper;
import com.wosai.upay.job.mapper.ForeignCardMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.PreAuthApplyMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.acquirePos.*;
import com.wosai.upay.job.model.fyPos.OpenForeignCardRequest;
import com.wosai.upay.job.model.fyPos.OpenPreAuthRequest;
import com.wosai.upay.job.model.lklV3Pos.ApplyPosRequest;
import com.wosai.upay.job.model.sybPos.OpenSybForeignCardRequest;
import com.wosai.upay.job.model.sybPos.OpenSybPreAuthRequest;
import com.wosai.upay.job.refactor.dao.DirectStatusDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.SubBizParamsDAO;
import com.wosai.upay.job.refactor.model.entity.DirectStatusDO;
import com.wosai.upay.merchant.contract.model.LimitResp;
import com.wosai.upay.merchant.contract.model.fuyou.response.ElecContractSignV2StatusQueryResponse;
import com.wosai.upay.merchant.contract.model.fuyou.response.ModifyCancelResponse;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.wosai.upay.job.biz.acquirePos.TlT9HandleService.TONGLIAN_INTERNL_CARD_OPEN;
import static com.wosai.upay.job.service.LKlV3PosServiceImpl.convertCentsToYuanFormatted;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2024/2/26 19:10
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class T9ServiceImpl implements T9Service {
    @Autowired
    private T9HandleFactory factory;
    @Autowired
    private TerminalService terminalService;

    @Autowired
    private ForeignCardMapper foreignCardMapper;

    @Autowired
    protected DirectStatusMapper directStatusMapper;

    @Value("${fy_pso_dev_code}")
    public String fyPsoDevCode;

    @Value("${lkl_pso_dev_code}")
    public String lklPsoDevCode;

    @Value("${fyForeignCard.devCode}")
    private String fyForeignCardDevCode;

    @Value("${fy.preAuth.appId}")
    private String fyPreAuthAppId;


    @Value("${tl_pos_devCode}")
    public String tlPosDevCode;

    @Value("${tl_foreignCard_devCode}")
    private String tlForeignCardDevCode;


    @Value("${tl_preAuth_devCode}")
    private String tlPreAuthDevCode;

    @Value("${tl.preAuth.appId}")
    private String tlPreAuthAppId;

    @Resource(name = "jsonRedisTemplate")
    private RedisTemplate jsonRedisTemplate;

    @Resource
    private SubBizParamsBiz subBizParamsBiz;

    @Autowired
    private MerchantService merchantService;


    @Resource
    private DirectStatusDAO directStatusDAO;

    @Resource
    private SubBizParamsDAO subBizParamsDAO;

    @Resource
    private McAcquirerDAO mcAcquirerDAO;

    @Autowired
    private CommonAppInfoService commonAppInfoService;

    @Resource
    private StoreBiz storeBiz;

    @Autowired
    DirectStatusBiz directStatusBiz;

    @Autowired
    private FeeRateService feeRateService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private FuyouService fuyouService;

    @Autowired
    protected AgreementBiz agreementBiz;

    @Autowired
    protected SimpleSmartPosInfoService simpleSmartPosInfoService;

    @Autowired
    private PreAuthApplyMapper preAuthApplyMapper;


    @Autowired
    private ImportCrmInfoService importCrmInfoService;

    @Autowired
    private TlT9HandleService tlV2T9HandleService;

    @Autowired
    @Lazy
    protected T9HandleFactory t9HandleFactory;


    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;

    @Autowired
    private MerchantBankService merchantBankService;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private StoreExtService mcStoreExtService;

    @Autowired
    private StoreService storeService;
    @Autowired
    private IndustryV2Service industryV2Service;

    @Resource(name = "iotBusinessOpenService")
    private BusinessOpenService iotBusinessOpenService;

    public static final long EXPIRATION_DURATION = 7 * 24L;

    public static final TimeUnit EXPIRATION_TIME_UNIT = TimeUnit.HOURS;

    private static final String CACHE_NOT_FOUND_KEY = "cacheNotFound";

    public static final String FOREIGN_CARD_OPEN_FLAG = "foreignCardStatus:%s";


    public static final String ERROR_MESSAGE_PRE_AUTH_ALREADY_ENABLED = "预授权功能已开通";

    public static final String ERROR_MESSAGE_T9_NOT_ENABLED = "请先开通成功通联一体化刷卡业务后再开通预授权！";

    public static final String ERROR_MESSAGE_MERCHANT_NOT_FOUND = "商户不存在";

    @Override
    public void bindCheck(InnerBindCheckDTO dto) {
        factory.getAcquirePosServiceByVenderAppAppId(dto.getVendorAppAppid())
                .innerBindCheck(dto);
    }

    @Override
    public void crmOpenCheck(CrmOpenCheckDTO dto) {
        factory.getAcquirePosServiceByAcquire(dto.getAcquire())
                .crmOpenCheck(dto);
    }


    @Override
    public ContractResponse applyPos(ApplyPosRequest request) {
        return factory.getAcquirePosServiceByAcquire(request.getAcquire())
                .openPos(request);
    }

    @Override
    public PosActiveInfo getPosActiveInfo(PosActiveInfoDTO dto) {
        String terminalSn = dto.getTerminalSn();
        Map terminal = terminalService.getTerminalBySn(terminalSn);
        String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        return factory.getAcquirePosServiceByVenderAppAppId(vendorAppAppid).getPosActiveInfo(dto);
    }

    @Override
    public void fyTermCancel(UnbindDTO dto) {
        factory.getAcquirePosServiceByAcquire(McConstant.ACQUIRER_FUYOU).fyTermCancel(dto);
    }

    @Override
    public void fyTermAdd(String terminalSn) {
        t9TermAdd(terminalSn);
    }

    @Override
    public Boolean getForeignCardStatus(String merchantSn) {
        try {
            String key = String.format(FOREIGN_CARD_OPEN_FLAG, merchantSn);
            final Object value = jsonRedisTemplate.opsForValue().get(key);

            // 检查是否为已知的未找到标记
            if (CACHE_NOT_FOUND_KEY.equals(value)) {
                // 直接返回false，避免查询数据库
                return false;
            }
            if (Objects.nonNull(value) && value instanceof Boolean) {
                return (Boolean) value;
            }
            //不管是手机POS外卡还是外卡pos在收钱吧APP端都认为是外卡,通联外卡不需要T+1结算所以不需要判断通联外卡
            final boolean anyMatch = Optional.ofNullable(foreignCardMapper.selectListByMerchantSn(merchantSn, ForeignCard.STATUS_SUCCESS))
                    .orElseGet(ArrayList::new)
                    .stream()
                    .filter(Objects::nonNull)
                    .anyMatch(card -> !Objects.equals(card.getDev_code(), tlForeignCardDevCode));
            // 根据查询结果设置缓存值
            if (anyMatch) {
                jsonRedisTemplate.opsForValue().set(key, true, EXPIRATION_DURATION, EXPIRATION_TIME_UNIT);
            } else {
                // 数据库中也没有成功状态的记录，缓存"未找到"标记
                jsonRedisTemplate.opsForValue().set(key, CACHE_NOT_FOUND_KEY, EXPIRATION_DURATION, EXPIRATION_TIME_UNIT);
            }
            return anyMatch;
        } catch (Exception e) {
            log.error("merchantSn:{},Exception:{}", merchantSn, e);
            return false;
        }
    }

    @Override
    public Boolean getT9PosStatus(String merchantSn) {
        final List<DirectStatus> statusList = directStatusMapper.getDirectByMerchantSn(merchantSn);
        final boolean match = statusList.stream().anyMatch(direct -> Objects.equals(direct.getStatus(), DirectStatus.STATUS_SUCCESS)
                && t9HandleFactory.getAcquirerDevCodeMap().values().contains(direct.getDev_code()));
        return match;
    }

    /**
     * 刷卡开通状态
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     * @return Boolean-开通,false-未开通
     */
    @Override
    public Boolean isT9PosOpen(String merchantSn, String acquirer) {
        // 使用映射获取 devCode
        String posDevCode = t9HandleFactory.getAcquirerDevCodeMap().get(acquirer);
        if (StrUtil.isBlank(posDevCode)) {
            log.warn("未找到收单机构对应的 devCode, acquirer: {}", acquirer);
            return false;
        }
        final List<DirectStatus> statusList = directStatusMapper.getDirectByMerchantSn(merchantSn);
        return statusList.stream().anyMatch(direct -> Objects.equals(direct.getStatus(), DirectStatus.STATUS_SUCCESS)
                && StringUtils.equals(posDevCode, direct.getDev_code()));
    }

    @Override
    public ModifyCancelResponse fyModifyCancel(String merchantSn, String modifyNo) {
        return factory.getAcquirePosServiceByAcquire(McConstant.ACQUIRER_FUYOU).fyModifyCancel(merchantSn, modifyNo);
    }

    @Override
    public List<LimitResp> queryLimit(String merchantSn) {
        List<DirectStatus> directStatusList = directStatusMapper.getDirectByMerchantSn(merchantSn);
        if (CollectionUtils.isEmpty(directStatusList)) {
            return Lists.newArrayList();
        }
        for (DirectStatus directStatus : directStatusList) {
            if (Objects.equals(DirectStatus.STATUS_SUCCESS, directStatus.getStatus())) {
                T9HandleService handleService = factory.getAcquirePosServiceByDevCode(directStatus.getDev_code());
                if (Objects.nonNull(handleService)) {
                    return handleService.queryLimit(merchantSn);
                }
            }
        }
        return Lists.newArrayList();
    }



    /**
     * 判断商户是否开通T9刷卡功能并绑定一体化终端
     *
     * @param merchantSn 商户号
     * @return 是否开通成功并绑定终端 true-成功
     */
    @Override
    public Boolean isT9OpenSuccessAndBindTerminal(String merchantSn) {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        if (Objects.isNull(merchant)) {
            log.error("商户不存在, merchantSn: {}", merchantSn);
            return false;
        }
        String merchantId = merchant.getId();
        Boolean openSuccess = getT9PosStatus(merchantSn);
        if (!openSuccess) {
            return false;
        }
        List<String> t9VendorAppAppIds = listT9VendorAppAppId();
        for (String t9VendorAppAppId : t9VendorAppAppIds) {
            Map<?, ?> filter = CollectionUtil.hashMap(Terminal.VENDOR_APP_APPID, t9VendorAppAppId, Terminal.STATUS, Terminal.STATUS_ACTIVATED);
            ListResult terminals = terminalService.getTerminals(merchantId, null, null, filter);
            if (Objects.nonNull(terminals) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(terminals.getRecords())) {
                return true;
            }
        }
        return false;
    }

    private List<String> listT9VendorAppAppId() {
        List<String> t9PosVendorAppAppIds = Lists.newArrayList();
        // 目前只有拉卡拉和富友,通联支持T9刷卡功能
        t9PosVendorAppAppIds.addAll(t9HandleFactory.getVerderAcquireMap().keySet());
        return t9PosVendorAppAppIds;
    }

    /**
     * 注销对应收单机构一体化刷卡记录
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     */
    @Override
    public Boolean deactivateIntegratedCardBusiness(String merchantSn, String acquirer) {
        if (StringUtils.isBlank(merchantSn) || StringUtils.isBlank(acquirer)) {
            return false;
        }
        final Map<String, String> acquirerDevCodeMap = t9HandleFactory.getAcquirerDevCodeMap();
        if (!acquirerDevCodeMap.keySet().contains(acquirer)) {
            log.warn("商户号:{},收单机构:{},不支持注销CRM一体化刷卡记录", merchantSn, acquirer);
            return false;
        }
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        if (Objects.isNull(merchant)) {
            log.error("商户不存在, merchantSn: {}", merchantSn);
            return false;
        }
        final String devCode = acquirerDevCodeMap.get(acquirer);
        directStatusDAO.deleteByMerchantSnAndDevCode(merchantSn, devCode);
        subBizParamsDAO.deleteByMerchantSnAndProviderAndAppId(merchantSn, mcAcquirerDAO.getByAcquirer(acquirer).getProvider(),
                getIntegratedCardAppId(acquirer));
        if(Objects.equals(acquirer, AcquirerTypeEnum.TONG_LIAN_V2.getValue())) {
            final CallBackRequest callBackRequest = new CallBackRequest();
            callBackRequest.setBizType(TONGLIAN_INTERNL_CARD_OPEN);
            callBackRequest.setMerchantId(merchant.getId());
            callBackRequest.setAuditResult(2);
            callBackRequest.setErrMessage("注销内卡");
            try {
                iotBusinessOpenService.resultCallBack(callBackRequest);
            } catch (Exception e) {
                log.warn("deactivateIntegratedCardBusiness resultCallBack merchantSn :{},error",merchantSn,e);
            }
        }
        return true;
    }


    private String getIntegratedCardAppId(String acquirer) {
        if(Lists.newArrayList(AcquirerTypeEnum.LKL_V3.getValue(),AcquirerTypeEnum.FU_YOU.getValue(),AcquirerTypeEnum.TONG_LIAN_V2.getValue()).contains(acquirer)) {
            return subBizParamsBiz.getT9TradeAppId();
        }
        throw new CommonPubBizException("不支持的收单机构");
    }

    public void deactivateCrmAndCuaIntegratedCardSwiping(String merchantSn, String acquirer) {
        String devCode = t9HandleFactory.getAcquirerDevCodeMap().get(acquirer);
        closeCrmApp(merchantSn, acquirer, devCode, "小微升级-商户注销一体化刷卡");
    }

    public void deactivateCrmAndCuaForeignCard(String merchantSn, String acquirer) {
        String devCode = t9HandleFactory.getAcquirerForeignCardDevCodeMap().get(acquirer);
        closeCrmApp(merchantSn, acquirer, devCode, "小微升级-商户注销外卡");
    }

    private void closeCrmApp(String merchantSn, String acquirer, String devCode, String remark) {
        if(StringUtils.isBlank(devCode)) {
            log.warn("未找到收单机构对应的devCode merchantSn:{},acquirer:{}", merchantSn, acquirer);
            return;
        }
        String storeId = storeBiz.getFirstStoreIdByMerchantSn(merchantSn);
        CloseAppReq closeAppReq = new CloseAppReq();
        closeAppReq.setStoreId(storeId);
        closeAppReq.setDevCode(devCode);
        closeAppReq.setOperatorId("sys");
        closeAppReq.setOperatorName("sys");
        closeAppReq.setPlatformCode("SPA");
        closeAppReq.setPlatform("SPA");
        closeAppReq.setBusinessLogCode("1EPEKI7GRH4U");
        closeAppReq.setRemark(remark);
        commonAppInfoService.closeApp(closeAppReq);
    }

    @Override
    public void openFyForeignCard(OpenForeignCardRequest request) {
        // 获取商户信息，如果商户不存在则抛出异常
        MerchantInfo merchantInfo = Optional.ofNullable(merchantService.getMerchantById(request.getMerchantId(), null))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));
        String merchantSn = merchantInfo.getSn();

        // 创建新的外卡记录
        ForeignCard card = new ForeignCard();
        card.setMerchant_sn(merchantSn);
        card.setStatus(ForeignCardConstant.Status.APPLYING);
        card.setForm_body(JSONObject.toJSONString(request));
        card.setEcApplyId(request.getModifyNo());
        card.setDev_code(fyForeignCardDevCode);
        foreignCardMapper.insert(card);

        // 通知CRM状态变为开通中
        directStatusBiz.createOrUpdateDirectStatus(merchantSn, fyForeignCardDevCode, DirectStatus.STATUS_PROCESS, null);
    }


    @Override
    public void openFyPreAuth(OpenPreAuthRequest request) {
        // 获取商户信息，如果商户不存在则抛出异常
        MerchantInfo merchantInfo = Optional.ofNullable(merchantService.getMerchantById(request.getMerchantId(), null))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));
        String merchantSn = merchantInfo.getSn();
        final String devCode = request.getDevCode();
        //签约协议申请单号
        final String contractNo = request.getContractNo();

        final com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = fuyouService.elecContractSignV2StatusQuery(merchantSn, contractNo);
        ElecContractSignV2StatusQueryResponse elecQuery = JSONObject.parseObject(
                JSONObject.toJSONString(Optional.ofNullable(contractResponse.getResponseParam()).orElseGet(HashMap::new)),
                ElecContractSignV2StatusQueryResponse.class);
        String signPdfUrl = elecQuery.getSignPdfUrl();

        // 创建新的外卡记录
        PreAuthApply preAuthApply = new PreAuthApply();
        //如果PDF链接不为空,则保存在协议管理平台
        if (!StringUtils.isEmpty(signPdfUrl)) {
            agreementBiz.recordAgreementForT9(merchantInfo.getId(), signPdfUrl);
        }
        preAuthApply.setContractUrl(signPdfUrl);
        preAuthApply.setMerchant_sn(merchantSn);
        preAuthApply.setStatus(PreAuthApplyConstant.Status.SUCCESS);
        preAuthApply.setForm_body(JSONObject.toJSONString(request));
        preAuthApply.setEcApplyId(contractNo);
        preAuthApply.setDev_code(devCode);
        preAuthApplyMapper.insert(preAuthApply);
        //记录在direct_status
        directStatusBiz.getPreStatus(merchantSn,devCode, DirectStatus.STATUS_SUCCESS);
        //开通成功导入到crm中
        importCrmInfoService.createOrUpdateBizOpenInfo(merchantSn, AppInfoModel.STATUS_SUCCESS,fyPreAuthAppId,null);
    }


    @Override
    public void changeFyForeignCardStatus(ChangeAuditPushRequest auditPushRequest) {
        log.info("changeFyForeignCardStatus auditPushRequest:{}", JSONObject.toJSONString(auditPushRequest));
        String modifyNo = auditPushRequest.getModifyNo();
        ForeignCard foreignCard = foreignCardMapper.selectByEcApplyId(modifyNo);

        // 如果外卡记录不存在或已成功开通，则直接返回
        if (foreignCard == null || ForeignCardConstant.Status.SUCCESS.equals(foreignCard.getStatus())) {
            return;
        }

        String auditResult = auditPushRequest.getAuditResult();
        String merchantSn = foreignCard.getMerchant_sn();
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);
        // 审核成功
        if ("1".equals(auditResult)) {
            // 更新外卡状态为成功
            updateForeignCardStatus(foreignCard, ForeignCardConstant.Status.SUCCESS);
            // 通知CRM状态变为成功
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, fyForeignCardDevCode, DirectStatus.STATUS_SUCCESS, null);
            // 更新外卡开通标志
            updateForeignCardOpenFlag(merchantSn);
            // 异步保存外卡协议
            saveForeignCardAgreementAsync(foreignCard);
            // 异步设置外卡费率
            applyForeignCardFeeRateOneAsync(merchantInfo.getId(), merchantSn, foreignCard);
            // 通知IoT组
            notifyIoTGroup(merchantInfo, 1);
        } else if ("0".equals(auditResult)) {
            // 审核失败
            // 更新外卡状态为失败
            updateForeignCardStatus(foreignCard, ForeignCardConstant.Status.FAIL);
            // 通知CRM状态变为失败
            String resultDesc = auditPushRequest.getResultDesc();
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, fyForeignCardDevCode, DirectStatus.STATUS_BIZ_FAIL, StringUtils.isNotBlank(resultDesc) ? resultDesc : "富友审核失败");
            // 通知IoT组
            notifyIoTGroup(merchantInfo, 0);
        }
    }

    /**
     * 更新外卡记录的状态
     * @param foreignCard 外卡记录
     * @param status 新状态
     */
    private void updateForeignCardStatus(ForeignCard foreignCard, Integer status) {
        ForeignCard update = new ForeignCard();
        update.setId(foreignCard.getId());
        update.setStatus(status);
        foreignCardMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 更新外卡开通标志
     * @param merchantSn 商户编号
     */
    private void updateForeignCardOpenFlag(String merchantSn) {
        String key = String.format(T9ServiceImpl.FOREIGN_CARD_OPEN_FLAG, merchantSn);
        jsonRedisTemplate.opsForValue().set(key, Boolean.TRUE, T9ServiceImpl.EXPIRATION_DURATION, T9ServiceImpl.EXPIRATION_TIME_UNIT);
    }

    /**
     * 异步保存外卡协议
     * @param foreignCard 外卡记录
     */
    private void saveForeignCardAgreementAsync(ForeignCard foreignCard) {
        CompletableFuture.runAsync(() -> {
            final Map formBody = foreignCard.getFormBody();
            String contractNo = BeanUtil.getPropString(formBody, "contractNo");
            saveForeignCardAgreement(foreignCard, contractNo);
        });
    }

    /**
     * 异步设置外卡费率
     * @param merchantId 商户ID
     * @param merchantSn 商户编号
     * @param foreignCard 外卡记录
     */
    private void applyForeignCardFeeRateOneAsync(String merchantId, String merchantSn, ForeignCard foreignCard) {
        CompletableFuture.runAsync(() -> applyForeignCardFeeRateOne(merchantId, merchantSn, foreignCard));
    }

    /**
     * 通知IoT组
     * @param merchantInfo 商户信息
     * @param modifyResult 修改结果（1: 成功，0: 失败）
     */
    private void notifyIoTGroup(MerchantInfo merchantInfo, int modifyResult) {
        final Map map = CollectionUtil.hashMap("merchant_id", merchantInfo.getId(),
                "modify_result", modifyResult
                , "biz_type", 2);
        simpleSmartPosInfoService.openStatusCallBack(map);
    }

    /**
     * 保存外卡协议
     * @param foreignCard 外卡记录
     * @param contractNo 合同编号
     */
    public void saveForeignCardAgreement(ForeignCard foreignCard, String contractNo) {
        try {
            String merchantSn = foreignCard.getMerchant_sn();
            final com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = fuyouService.elecContractSignV2StatusQuery(merchantSn, contractNo);
            ElecContractSignV2StatusQueryResponse elecQuery = JSONObject.parseObject(
                    JSONObject.toJSONString(Optional.ofNullable(contractResponse.getResponseParam()).orElseGet(HashMap::new)),
                    ElecContractSignV2StatusQueryResponse.class);
            String signPdfUrl = elecQuery.getSignPdfUrl();

            // 如果PDF链接为空，则直接返回
            if (StringUtils.isEmpty(signPdfUrl)) {
                return;
            }

            MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
            agreementBiz.recordAgreementForT9(merchant.getId(), signPdfUrl);

            // 更新外卡记录的合同链接
            ForeignCard updateUrl = new ForeignCard();
            updateUrl.setId(foreignCard.getId());
            updateUrl.setContractUrl(signPdfUrl);
            foreignCardMapper.updateByPrimaryKeySelective(updateUrl);
        } catch (Exception e) {
            log.error("saveForeignCardAgreement error:", e);
        }
    }

    /**
     * 设置外卡费率
     * @param merchantId 商户ID
     * @param merchantSn 商户编号
     * @param foreignCard 外卡记录
     */
    public void applyForeignCardFeeRateOne(String merchantId, String merchantSn, ForeignCard foreignCard) {
        try {
            Map formBody = foreignCard.getFormBody();
            Long tradeComboId = BeanUtil.getPropLong(formBody, ForeignCardConstant.FormBody.TRADE_COMBO_ID);
            Map feeMap = MapUtils.getMap(formBody, ForeignCardConstant.FormBody.FEE_MAP);
            Map<String, Object> t9FeeMap = getT9FeeMap(merchantId);
            feeMap.putAll(t9FeeMap);

            List<Map<String, String>> feeList = new ArrayList<>();
            feeMap.forEach((k, v) -> {
                Map<String, String> detailMap = new HashMap<>();
                detailMap.put("type", String.valueOf(k).toLowerCase());
                detailMap.put("fee", BeanUtil.getPropString(v, "fee"));
                String maxFee = BeanUtil.getPropString(v, "max");
                if (StringUtils.isNotBlank(maxFee)) {
                    detailMap.put("max", maxFee);
                }
                feeList.add(detailMap);
            });


            Map<String, Object> bankPosMap = CollectionUtil.hashMap("fee_rate_type", "channel", "value", feeList);
            Map<String, String> applyFeeRateMap = CollectionUtil.hashMap(
                    String.valueOf(PaywayEnum.BANK_CARD.getValue()), JSONObject.toJSONString(bankPosMap));

            ApplyFeeRateRequest request = new ApplyFeeRateRequest();
            request.setMerchantSn(merchantSn);
            request.setAuditSn("开通外卡设置费率");
            request.setTradeComboId(tradeComboId);
            request.setApplyFeeRateMap(applyFeeRateMap);
            feeRateService.applyFeeRateOne(request);
            supportService.removeCachedParams(merchantSn);
        } catch (Exception e) {
            log.error("applyForeignCardFeeRateOne error 商户号:{}", merchantSn, e);
        }
    }

    /**
     * 获取T9费率信息
     * @param merchantId 商户ID
     * @return 费率信息映射
     */
    public Map<String, Object> getT9FeeMap(String merchantId) {
        Map<String, Object> feeMap = new HashMap<>();

        Optional<Map> bankcardFee = Optional.ofNullable(tradeConfigService.getAnalyzedMerchantConfigsByPayWayList(merchantId, new int[]{PaywayEnum.BANK_CARD.getValue()}))
                .filter(list -> !list.isEmpty())
                .map(list -> (Map<String, String>) list.get(0))
                .map(bankMap -> MapUtils.getMap(bankMap, "bankcard_fee"));

        if (bankcardFee.isPresent()) {
            Map<String, Object> map = bankcardFee.get();
            feeMap.put("credit", MapUtils.getMap(map, "credit"));

            Map<String, String> debitMap = MapUtils.getMap(map, "debit");
            feeMap.put("debit", debitMap);
            if(debitMap.containsKey("max") && Objects.nonNull(debitMap.get("max"))) {
                final String max = MapUtils.getString(debitMap, "max");
                final String yuan = convertCentsToYuanFormatted(max);
                debitMap.put("max",yuan);
            }
            feeMap.put("debit",debitMap);
        }
        return feeMap;
    }

    @Override
    public void closeForeignCard(String merchantSn, String acquirer) {
        factory.getAcquirePosServiceByAcquire(acquirer).closeForeignCard(merchantSn);
    }

    @Override
    public void closePreAuth(String merchantSn, String acquirer) {
        factory.getAcquirePosServiceByAcquire(acquirer).closePreAuth(merchantSn);
    }


    @Override
    public void applyBankCardProduct(String merchantId, Map<String, Object> feeMap)throws CommonPubBizException {
        tlV2T9HandleService.applyBankCardProduct(merchantId,feeMap);
    }

    @Override
    public void applyForeignCardProduct(String merchantId, Map<String, Object> feeMap) throws CommonPubBizException {
        tlV2T9HandleService.applyForeignCardProduct(merchantId,feeMap);
    }

    @Override
    public void applyPreAuthProduct(String merchantId) throws CommonPubBizException {
        tlV2T9HandleService.applyPreAuthProduct(merchantId);
    }



    @Override
    public void openSybForeignCard(OpenSybForeignCardRequest request) {
        // 获取商户信息，如果商户不存在则抛出异常
        MerchantInfo merchantInfo = Optional.ofNullable(merchantService.getMerchantById(request.getMerchantId(), null))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));
        String merchantSn = merchantInfo.getSn();

        // 创建新的外卡记录
        ForeignCard card = new ForeignCard();
        card.setMerchant_sn(merchantSn);
        card.setStatus(ForeignCardConstant.Status.SUCCESS);
        card.setForm_body(JSONObject.toJSONString(request));
        card.setEcApplyId(null);
        card.setDev_code(tlForeignCardDevCode);
        foreignCardMapper.insert(card);
        if (!StringUtils.isEmpty(request.getUrl())) {
            agreementBiz.recordAgreementForT9(merchantInfo.getId(), request.getUrl());
        }
        // 通知CRM状态变为开通中
        directStatusBiz.createOrUpdateDirectStatus(merchantSn, tlForeignCardDevCode, DirectStatus.STATUS_SUCCESS, null);
        applyForeignCardFeeRateOneAsync(merchantInfo.getId(),merchantInfo.getSn(),card);
    }


    @Override
    public void openSybPreAuthCheck(String merchantId) throws CommonPubBizException {
        final MerchantInfo merchantInfo = merchantService.getMerchantById(merchantId, null);
        //判断是否成功开过预授权
        final Optional<DirectStatusDO> directStatusDO = directStatusDAO.getByMerchantSnAndDevCode(merchantInfo.getSn(), tlPreAuthDevCode);
        if (directStatusDO.isPresent() && Objects.equals(directStatusDO.get().getStatus(), DirectStatus.STATUS_SUCCESS)) {
            throw new CommonPubBizException(ERROR_MESSAGE_PRE_AUTH_ALREADY_ENABLED);
        }

        //判断是否成功开过T9,开通外卡必须先开通T9
        final Optional<DirectStatusDO> postStatusDO = directStatusDAO.getByMerchantSnAndDevCode(merchantInfo.getSn(),tlPosDevCode);
        if (postStatusDO.isPresent() && !Objects.equals(postStatusDO.get().getStatus(), DirectStatus.STATUS_SUCCESS)) {
            throw new CommonPubBizException(ERROR_MESSAGE_T9_NOT_ENABLED);
        }

        //行业校验
        MerchantInfo merchant = Optional.ofNullable(merchantInfo)
                .orElseThrow(() -> new CommonPubBizException(ERROR_MESSAGE_MERCHANT_NOT_FOUND));
        checkIndustry(merchant.getIndustry(),merchant.getId());
    }

    @Override
    public void openSybPreAuth(@Valid OpenSybPreAuthRequest request) {
        // 获取商户信息，如果商户不存在则抛出异常
        MerchantInfo merchantInfo = Optional.ofNullable(merchantService.getMerchantById(request.getMerchantId(), null))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));
        String merchantSn = merchantInfo.getSn();
        final String devCode = request.getDevCode();


        final PreAuthApply selectPreAuthApply = preAuthApplyMapper.selectPreAuthApply(merchantSn, devCode);
        //存在删除
        Optional.ofNullable(selectPreAuthApply)
                .filter(apply -> Objects.equals(apply.getStatus(), PreAuthApplyConstant.Status.SUCCESS))
                .ifPresent(apply -> preAuthApplyMapper.deleteByPrimaryKey(selectPreAuthApply.getId()));
        // 创建新的预授权记录
        PreAuthApply preAuthApply = new PreAuthApply();
        //如果PDF链接不为空,则保存在协议管理平台
        final String requestUrl = request.getUrl();
        if (!StringUtils.isEmpty(requestUrl)) {
            agreementBiz.recordAgreementForT9(merchantInfo.getId(), requestUrl);
        }
        preAuthApply.setContractUrl(requestUrl);
        preAuthApply.setMerchant_sn(merchantSn);
        preAuthApply.setStatus(PreAuthApplyConstant.Status.SUCCESS);
        preAuthApply.setForm_body(JSONObject.toJSONString(request));
        preAuthApply.setEcApplyId(null);
        preAuthApply.setDev_code(devCode);
        preAuthApplyMapper.insert(preAuthApply);
        //记录在direct_status
        directStatusBiz.getPreStatus(merchantSn,devCode, DirectStatus.STATUS_SUCCESS);
        //开通成功导入到crm中
        importCrmInfoService.createOrUpdateBizOpenInfo(merchantSn, AppInfoModel.STATUS_SUCCESS,tlPreAuthAppId,null);
    }

    @Override
    public void t9TermAdd(String terminalSn) throws CommonPubBizException {
        Map terminal = terminalService.getTerminalBySn(terminalSn);
        String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        factory.getAcquirePosServiceByVenderAppAppId(vendorAppAppid).t9TermAdd(terminalSn);
    }

    @Override
    public void t9TermUnbind(String terminalSn) {
        Map terminal = terminalService.getTerminalBySn(terminalSn);
        String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        factory.getAcquirePosServiceByVenderAppAppId(vendorAppAppid).t9TermUnbind(terminalSn);
    }


    /**
     * 商户行业支持预授权
     *
     * @param industry
     * @param merchantId
     * @return
     */
    public void checkIndustry(String industry, String merchantId) {
        //行业校验
        String tlCode = industryMappingCommonBiz.getTongLianV2Code(industry);
        //结算账户类型
        final Map bankAccount = getBankAccount(merchantId);
        final Integer bankType = BeanUtil.getPropInt(bankAccount, MerchantBankAccountPre.TYPE);
        final String tip = Objects.equals(bankType, BankAccountEnum.BUSINESS.getCode()) ? "对公结算" : "对私结算";
        List<String> allow = applicationApolloConfig.getSybPreAuth();
        if (!allow.contains(tlCode)) {
            throw new CommonPubBizException(String.format("商户MCC:%s 结算类型:%s",tlCode,tip));
        }
    }

    @Override
    public MerchantQuotaDetailDTO getMerchantQuotaInfo(String merchantId) {
        final MerchantQuotaDetailDTO detailResponse = new MerchantQuotaDetailDTO();
        final MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        final String merchantSn = merchant.getSn();
        MerchantProviderParams params = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
        if (Objects.isNull(params)) {
            throw new CommonPubBizException("商户未入网通联");
        }
        final String payMerchantId = params.getPay_merchant_id();
        detailResponse.setTlMerchantNo(payMerchantId);
        final MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, null);
        //非小微
        if(!BusinessLicenseTypeEnum.isMicro(license.getType())) {
            detailResponse.setBusinessLicenseImage(license.getPhoto());
        }
        detailResponse.setLegalPersonIdCardFrontPhoto(license.getLegal_person_id_card_front_photo());
        detailResponse.setLegalPersonIdCardBackPhoto(license.getLegal_person_id_card_back_photo());
        Map bankAccount = getBankAccount(merchantId);
        //小微个体
        if(Lists.newArrayList(BusinessLicenseTypeEnum.MICRO.getValue(),BusinessLicenseTypeEnum.INDIVIDUAL.getValue()).contains(license.getType())) {
            detailResponse.setBankCardFrontImage(BeanUtil.getPropString(bankAccount,MerchantBankAccountPre.BANK_CARD_IMAGE));
            detailResponse.setBranchName(BeanUtil.getPropString(bankAccount,MerchantBankAccountPre.BRANCH_NAME));
        }else {
            detailResponse.setAccountOpeningLicense(BeanUtil.getPropString(bankAccount,MerchantBankAccountPre.BANK_CARD_IMAGE));
        }
        final StotreExtInfoAndPictures photosInfo = getPhotosInfo(merchantId, null);
        detailResponse.setIndoorImage(Optional.ofNullable(photosInfo.getIndoorMaterialPhoto())
                .map(photo -> photo.getUrl())
                .orElse(null));
        detailResponse.setOutdoorImage(Optional.ofNullable(photosInfo.getOutdoorMaterialPhoto())
                .map(photo -> photo.getUrl())
                .orElse(null));
        detailResponse.setBrandImage(Optional.ofNullable(photosInfo.getBrandPhoto())
                .map(photo -> photo.getUrl())
                .orElse(null));
        detailResponse.setPriceImage(Optional.ofNullable(photosInfo.getProductPrice())
                .map(photo -> photo.getUrl())
                .orElse(null));
        detailResponse.setLegalPersonPhone(Optional.ofNullable(license.getLegal_person_contact_phone())
                .orElseGet(() -> merchant.getContact_cellphone()));
        detailResponse.setBusinessAddress(String.format("%s%s%s%s",merchant.getProvince(),merchant.getCity(),merchant.getDistrict(),merchant.getStreet_address()));
        detailResponse.setName(license.getName());
        final Map<String, Object> industryMap = industryV2Service.getIndustry(merchant.getIndustry());
        detailResponse.setBusinessScope(Joiner.on("/").skipNulls()
                .join(BeanUtil.getPropString(industryMap, Industry.LEVEL1)
                        ,BeanUtil.getPropString(industryMap, Industry.LEVEL2),
                        BeanUtil.getPropString(industryMap, Industry.LEVEL3),
                        BeanUtil.getPropString(industryMap, Industry.LEVEL4))
                .trim());
        return detailResponse;
    }

    @NotNull
    private Map getBankAccount(String merchantId) {
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", merchantId, "default_status", 1));
        Map bankAccount = null;
        if (listResult != null && listResult.getTotal() > 0) {
            bankAccount = listResult.getRecords().get(0);
        }
        if (MapUtils.isEmpty(bankAccount)) {
            throw new CommonPubBizException("银行账户信息不存在");
        }
        return bankAccount;
    }


    /**
     * 获取该商户对应的第一家门店的门头照、内景照、外景照信息
     *
     * @param merchantId 商户id
     * @return brand、outdoor、indoor
     */
    public StotreExtInfoAndPictures getPhotosInfo(String merchantId, String devCode) {
        String storeId = getLastedStoreIdByMerchantId(merchantId);
        StotreExtInfoAndPictures pictures = mcStoreExtService.findStoreExtAndPicturesByStoreId(storeId, devCode);
        if(Objects.isNull(pictures)) {
            throw new CommonPubBizException("该商户对应的第一家门店的门头照、内景照、外景照为空");
        }
        return pictures;
    }

    /**
     * 获取该商户id对应的第一家门店id
     *
     * @param merchantId 商户id
     * @return 门店id
     */
    public String getLastedStoreIdByMerchantId(String merchantId) {
        ListResult stores = storeService.findStores(new PageInfo(1, 1, null, null, Arrays.asList(new OrderBy("ctime", OrderBy.OrderType.ASC))), CollectionUtil.hashMap(
                Store.MERCHANT_ID, merchantId,
                DaoConstants.DELETED, 0
        ));
        //测试删数据报异常，判断一下
        List<Map> list = stores.getRecords();
        if (WosaiCollectionUtils.isEmpty(list)) {
            throw new CommonPubBizException("门店信息为空");
        }
        return (String) list.get(0).get(DaoConstants.ID);
    }


    @Override
    public MerchantTypeDTO getMerchantType(String merchantId) {
        final MerchantTypeDTO merchantTypeDTO = new MerchantTypeDTO();

        final MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, null);
        final Integer licenseType = license.getType();
        merchantTypeDTO.setBusinessLicenseType(BusinessLicenseTypeEnum.valueOf(licenseType));

        Map bankAccount = getBankAccount(merchantId);
        final Integer bankType = BeanUtil.getPropInt(bankAccount, MerchantBankAccountPre.TYPE);
        merchantTypeDTO.setAccountType(BankAccountTypeEnum.of(bankType));
        return merchantTypeDTO;
    }
}
