package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface McBatchTaskMapper {
    int countByExample(McBatchTaskExample example);

    int deleteByExample(McBatchTaskExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(McBatchTask record);

    int insertSelective(McBatchTask record);

    List<McBatchTask> selectByExampleWithBLOBs(McBatchTaskExample example);

    List<McBatchTask> selectByExample(McBatchTaskExample example);

    McBatchTask selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") McBatchTask record, @Param("example") McBatchTaskExample example);

    int updateByExampleWithBLOBs(@Param("record") McBatchTask record, @Param("example") McBatchTaskExample example);

    int updateByExample(@Param("record") McBatchTask record, @Param("example") McBatchTaskExample example);

    int updateByPrimaryKeySelective(McBatchTask record);


}