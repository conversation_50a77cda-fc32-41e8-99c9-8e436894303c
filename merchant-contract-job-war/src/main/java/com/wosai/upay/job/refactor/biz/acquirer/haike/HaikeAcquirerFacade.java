package com.wosai.upay.job.refactor.biz.acquirer.haike;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.dto.NewMerchantContractResultRspDTO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 海科收单处理策略
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
@Slf4j
public class HaikeAcquirerFacade extends AbstractAcquirerHandler {

    @Resource
    private HaikeMerchantInfoProcessor haikeMerchantInfoProcessor;

    @Resource
    private HaikeMerchantContractProcessor haikeMerchantContractProcessor;

    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.HAI_KE;
    }

    @Override
    public Optional<String> getDefaultContractRuleGroupId() {
        return Optional.of(McConstant.RULE_GROUP_HAIKE);
    }

    @Override
    public String getMicroUpdageDefaultContractRuleGroupId() {
        return McConstant.RULE_GROUP_MICROUPGRADE_HAIKE;
    }
    /**
     * 判断商户所在收单机构的银行卡是否和收钱吧银行卡一致
     *
     * @param merchantSn 商户号
     * @return true-一致 false-不一致
     */
    @Override
    public boolean isBankCardConsistentWithSqb(String merchantSn) {
        return acquirerCommonTemplate.checkBankCardConsistentWithSqb(
                getTypeValue(), merchantSn,
                t -> haikeMerchantInfoProcessor.getBankAccountNo(t).orElse(null),
                null
        );
    }

    /**
     * 商户向收单机构报备
     * 仅仅向收单机构做报备，不涉及其他业务逻辑校验。返回报文也不会落库。调用方需要自行处理
     *
     * @param merchantSn 商户号
     * @return 报备结果
     */
    @Override
    public NewMerchantContractResultRspDTO contractToAcquirer(String merchantSn) {
        return haikeMerchantContractProcessor.contractToAcquirer(merchantSn);
    }

    /**
     * 商户向收单机构报备
     *
     * @param merchantSn   商户号
     * @param contextParam 上下文参数
     * @return 报备结果
     */
    @Override
    public NewMerchantContractResultRspDTO contractToAcquirer(String merchantSn, Map<String, Object> contextParam) {
        return haikeMerchantContractProcessor.contractToAcquirer(merchantSn, contextParam);
    }

    /**
     * 获取银联商户号
     *
     * @param unionPayParamsDO 云闪付交易参数 payWay=17
     * @param acquirerParamsDO 收单机构交易参数 payWay=0
     * @return 银联商户号
     */
    @Override
    public String getUnionMerchantId(MerchantProviderParamsDO unionPayParamsDO, MerchantProviderParamsDO acquirerParamsDO) {
        if (Objects.nonNull(unionPayParamsDO) && Objects.equals(unionPayParamsDO.getPayway(), com.shouqianba.cua.enums.core.PaywayEnum.UNIONPAY.getValue())) {
            return unionPayParamsDO.getPayMerchantId();
        }
        if (Objects.nonNull(acquirerParamsDO) && Objects.equals(acquirerParamsDO.getPayway(), com.shouqianba.cua.enums.core.PaywayEnum.ACQUIRER.getValue())) {
            String extra = acquirerParamsDO.getExtra();
            if (StringUtils.isNotBlank(extra)) {
                JSONObject jsonObject = JSON.parseObject(extra);
                return MapUtils.getString(jsonObject, "bankMerchNo");
            }
        }
        return "";
    }


    @Override
    public MerchantAcquireInfoBO getAcquireInfoFromContractSubTask(Long pTaskId) {
        final List<ContractSubTaskDO> subTaskDOS = contractSubTaskDAO.listByPTaskId(pTaskId);
        // 查找海科入网任务
        ContractSubTaskDO haikeSubTask = findSubTask(subTaskDOS, PaywayEnum.ACQUIRER.getValue(), "海科入网任务", pTaskId);
        String responseBody = haikeSubTask.getResponseBody();
        //TODO 要改造符和海科
        if (Objects.isNull(responseBody)) {
            throw new ContractBizException("海科机构进件子任务返回结果responseBody为空, subTask id = " + haikeSubTask.getId());
        }

        final MerchantAcquireInfoBO merchantAcquireInfoBO = new MerchantAcquireInfoBO();

        // 设置海科相关信息
        merchantAcquireInfoBO.setAcquireMerchantId(getNestedProperty(JSONObject.parseObject(responseBody,Map.class), "responseParam.merch_no"));
        // 查找云闪付入网任务
        ContractSubTaskDO unionSubTask = findSubTask(subTaskDOS, PaywayEnum.UNIONPAY.getValue(), "云闪付报备任务", pTaskId);
        if(Objects.nonNull(unionSubTask.getResponseBody())) {
            //银联商户号
            final Map map = JSONObject.parseObject(unionSubTask.getResponseBody(), Map.class);
            merchantAcquireInfoBO.setUnionNo(getNestedProperty(map, "responseParam.bank_biz_info.bank_merch_no"));
            merchantAcquireInfoBO.setHaikeTermId(getNestedProperty(JSONObject.parseObject(responseBody,Map.class), "responseParam.terminal_id"));

            //银联商户号对应的名称
            merchantAcquireInfoBO.setHaikeUnionName(getNestedProperty(map, "responseParam.merch_short_name"));
        }

        // 查找微信入网任务
        ContractSubTaskDO wxSubTask = findSubTask(subTaskDOS, PaywayEnum.WEIXIN.getValue(), "微信报备任务", pTaskId);
        merchantAcquireInfoBO.setWxNo(getNestedProperty(JSONObject.parseObject(wxSubTask.getResponseBody(),Map.class), "responseParam.sub_mch_id"));
        merchantAcquireInfoBO.setWxContractRule(wxSubTask.getContractRule());
        // 查找支付宝入网任务
        ContractSubTaskDO aliSubTask = findSubTask(subTaskDOS, PaywayEnum.ALIPAY.getValue(), "支付宝报备任务", pTaskId);
        merchantAcquireInfoBO.setAliNo(getNestedProperty(JSONObject.parseObject(aliSubTask.getResponseBody(),Map.class), "responseParam.sub_merchant_id"));

        return merchantAcquireInfoBO;
    }

   public List<String> contractRuleList = Lists.newArrayList("haike-1037-3-176174614","haike-1037-2-2088011691288213","haike-union");

    /**
     * 查找指定支付方式的成功子任务
     */
    protected ContractSubTaskDO findSubTask(List<ContractSubTaskDO> subTaskDOS, Integer payway, String taskName, Long pTaskId) throws ContractBizException {
        Optional<ContractSubTaskDO> optionalSubTask = subTaskDOS.stream()
                .filter(r -> {
                    if(Objects.equals(PaywayEnum.ACQUIRER.getValue(), payway)) {
                        return  Objects.equals(r.getPayway(), payway)
                                && Objects.equals(r.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())
                                && Objects.equals(r.getTaskType(), ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
                                && Objects.equals(r.getContractRule(), "haike")
                                && StrUtil.contains(r.getRuleGroupId(), ContractRuleConstants.RULE_GROUP_MICROUPGRADE);
                    }else {
                        return  Objects.equals(r.getPayway(), payway)
                                && Objects.equals(r.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())
                                && Objects.equals(r.getTaskType(), ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
                                && contractRuleList.contains(r.getContractRule())
                                && StrUtil.contains(r.getRuleGroupId(), ContractRuleConstants.RULE_GROUP_MICROUPGRADE);
                    }


                })
                .findFirst();
        if(!optionalSubTask.isPresent() && Objects.equals(payway,PaywayEnum.UNIONPAY.getValue()) ) {
            return new ContractSubTaskDO();
        }

        if (!optionalSubTask.isPresent()) {
            log.warn("pTaskId{} 没有找到对应的{}", pTaskId, taskName);
            throw new ContractBizException("没有找到对应的" + taskName);
        }

        return optionalSubTask.get();
    }

}
