package com.wosai.upay.job.util.luzhou;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Body签名块对象<br/>
 * <li>对于使用HTTP的Body来传递参数的情况(POST方法)，待加密数据=uri&Body&商户号(Mer-Id)&时间戳(Time-Stamp)</li>
 */
@Data
@AllArgsConstructor
public class BodySignBlock implements ISignBlock {
    /** 接口uri */
    private String uri;
    /** 商户编号[对应http header:Mer-Id] */
    private String merId;
    /** 时间戳[对应http header:Time-Stamp] */
    private String timeStamp;
    /** http body数据 */
    private String bodyString;


    public String getSignBlock() {
        return uri + "&" +
                bodyString + "&" +
                merId + "&" +
                timeStamp;
    }
}
