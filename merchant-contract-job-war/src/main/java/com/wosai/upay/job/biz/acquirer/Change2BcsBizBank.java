package com.wosai.upay.job.biz.acquirer;

import com.wosai.upay.job.constant.ContractRuleConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.shouqianba.cua.enums.contract.ProviderEnum;

@Component("bcs-AcquirerChangeBiz")
public class Change2BcsBizBank extends AbstractBankDirectAcquirerChangeBiz {

    @Value("${bcs_dev_code}")
    private String bcsDevCode = "";

    @Value("${bcs_trade_combo_id}")
    private long bcsTradeComboId = 0;

    @Override
    protected String getDevCode(String acquirer) {
        return bcsDevCode;
    }

    @Override
    protected long getDefaultComboId(String acquirer) {
        return bcsTradeComboId;
    }

    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_BCS_RULE_GROUP;
    }

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_BCS.getValue();
    }
}
