package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.DirectStatusDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.DirectStatusDO;

import java.util.Optional;


/**
 * 直连申请状态表表数据库访问层 {@link DirectStatusDO}
 * 对DirectStatusMapper层做出简单封装 {@link DirectStatusDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class DirectStatusDAO extends AbstractBaseDAO<DirectStatusDO, DirectStatusDynamicMapper> {

    public DirectStatusDAO(SqlSessionFactory sqlSessionFactory, DirectStatusDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据商户号和devCode删除直连申请状态
     *
     * @param merchantSn 商户号
     * @param devCode    devCod
     */
    public void deleteByMerchantSnAndDevCode(String merchantSn, String devCode) {
        if (StringUtils.isBlank(merchantSn) || StringUtils.isBlank(devCode)) {
            return;
        }
        entityMapper.delete(new LambdaQueryWrapper<DirectStatusDO>()
                .eq(DirectStatusDO::getMerchantSn, merchantSn)
                .eq(DirectStatusDO::getDevCode, devCode));
    }


    public Optional<DirectStatusDO> getByMerchantSnAndDevCode(String merchantSn, String devCode) {
        LambdaQueryWrapper<DirectStatusDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(DirectStatusDO::getMerchantSn, merchantSn)
                .eq(DirectStatusDO::getDevCode, devCode);
        return selectOne(lambdaQueryWrapper);
    }
}
