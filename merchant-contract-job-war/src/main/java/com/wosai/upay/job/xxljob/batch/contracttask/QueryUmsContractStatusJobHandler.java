package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.UmsCallBackBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.ChinaUmsParam;
import com.wosai.upay.merchant.contract.service.ChinaUmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * xxl_job_desc: 进件任务-银商进件结果查询
 * <AUTHOR>
 * @date 2025/4/18
 */
@Slf4j
@Component("QueryUmsContractStatusJobHandler")
public class QueryUmsContractStatusJobHandler extends AbstractBatchJobHandler<ContractSubTask> {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ChinaUmsService umsService;
    @Autowired
    private ContractParamsBiz contractParamsBiz;
    @Autowired
    private UmsCallBackBiz umsCallBackBiz;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public List<ContractSubTask> queryTaskItems(BatchJobParam param) {
        return contractSubTaskMapper.selectUmsContractQueryTask(param.getBatchSize(), StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()));
    }

    @Override
    public String getLockKey(ContractSubTask contractSubTask) {
        return "QueryUmsContractStatusJobHandler:" + contractSubTask.getId();
    }

    @Override
    public void doHandleSingleData(ContractSubTask contractSubTask) {
        try {
            //1，再次查询
            ContractSubTask contractSubTaskLast = contractSubTaskMapper.selectByContractId(contractSubTask.getContract_id());
            //2，判断状态
            if (TaskStatus.isFinish(contractSubTaskLast.getStatus())) {
                return;
            }
            //3，调用contract --返回结果
            ChinaUmsParam umsParam = contractParamsBiz.buildContractParamsByContractSubTask(contractSubTaskLast, ChinaUmsParam.class);
            ContractResponse contractResponse = umsService.queryContractStatusByContractId(contractSubTaskLast, umsParam);
            //4，结果处理
            umsCallBackBiz.handUmsMerchantContractResult(contractResponse, contractSubTaskLast);
        } catch (Exception e) {
            log.error("QueryUmsContractStatusJobHandler error：", e);
            chatBotUtil.sendMessageToContractWarnChatBot("QueryUmsContractStatusJobHandler error" + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
