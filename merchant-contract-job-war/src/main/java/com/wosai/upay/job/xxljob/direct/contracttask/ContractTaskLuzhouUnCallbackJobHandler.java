package com.wosai.upay.job.xxljob.direct.contracttask;

import cn.hutool.core.util.StrUtil;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.service.LuzhouCallBackService;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static javax.management.timer.Timer.ONE_DAY;

/**
 * xxl_job_desc: ContractTask-泸州银行未回调任务
 * <AUTHOR>
 * @date 2025/4/27
 */
@Slf4j
@Component("ContractTaskLuzhouUnCallbackJobHandler")
public class ContractTaskLuzhouUnCallbackJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private LuzhouCallBackService callBackService;

    @Override
    public String getLockKey() {
        return "ContractTaskLuzhouUnCallbackJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        List<ContractSubTask> subTasks = contractSubTaskMapper.getLuzhouUnCallbackSubTask(
                param.getBatchSize(),
                StringUtil.formatDate(System.currentTimeMillis() - param.getStartTime()),
                StringUtil.formatDate(System.currentTimeMillis() - param.getEndTime())
        );
        for (ContractSubTask subTask : subTasks) {
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                String contractId = subTask.getContract_id();
                if (StrUtil.isBlank(contractId)) {
                    return;
                }
                callBackService.doHandle(contractId,
                        ContractResponse.builder().message("泸州银行进件任务等待回调超过三十天,任务失败").code(460).build());
            } catch (Exception e) {
                log.error("处理泸州银行未回调任务失败，商户号{},业务类型{}", subTask.getMerchant_sn(), subTask.getTask_type(), e);
            }
        }
    }


}
