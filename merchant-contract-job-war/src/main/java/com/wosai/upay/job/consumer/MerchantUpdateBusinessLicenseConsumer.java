package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense;
import com.wosai.upay.job.Constants.ConstantsEvent;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.AgreementBiz;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationTask;
import com.wosai.upay.job.service.SelfHelpNetInEventService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;


/**
 * @Description: 商户营业执照变更 消费者
 * <AUTHOR>
 * @Date: 2021/9/6 2:33 下午
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class MerchantUpdateBusinessLicenseConsumer {
    public static final String LICENSE_UPDATE_TOPIC = "events.core-business.businessLicense.update";
    @Autowired
    private SelfHelpNetInEventService selfHelpNetInEventService;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private AgreementBiz agreementBiz;

    @Resource
    private BusinessLicenceCertificationTask businessLicenceCertificationTask;


    @KafkaListener(topics = {LICENSE_UPDATE_TOPIC}, containerFactory = "avroListenerContainerFactory")
    @Transactional(rollbackFor = Exception.class)
    public void handle(MerchantUpdateBusinessLicense updateBusinessLicense) {
        log.info("更新营业执照{}", updateBusinessLicense.toString());
        //是否处理更新营业执照消息 true处理 false不处理
        boolean isHandle = applicationApolloConfig.getHandleBizLicenseUpdate();
        if (!isHandle) {
            log.info("更新营业执照暂不处理");
            return;
        }
        //处理
        Map msg = CollectionUtil.hashMap(ConstantsEvent.EVENT_TYPE_TABLE_NAME, "merchant_business_license");
        Map map = CollectionUtil.hashMap("source", "core-b商户更新营业执照");

        String merchantSn = updateBusinessLicense.getMerchantSn().toString();
        String bizType = updateBusinessLicense.getMerchantBusinessLicenseType().toString();
        if (String.valueOf(BusinessLicenseTypeEnum.MICRO.getValue()).equals(bizType)) {
            log.info("小微商户不支持更新营业执照：{}", merchantSn);
            return;
        }
        // 这段逻辑应该在营业执照认证v2上线之前就上线
        if (isLicenseVerifyV2(updateBusinessLicense.getMerchantId().toString())) {
            log.info("营业执照认证V2，商户更新营业执照不处理：merchantSn:{}", merchantSn);
            return;
        }
        if (businessLicenceCertificationTask.removeSuccessCertificateMicroRecordFromRedis(merchantSn)) {
            log.info("小微升级成功商户不需要向收单机构更新营业执照：{}", merchantSn);
            return;
        }
        List<String> list = Arrays.asList("商户更新营业执照");
        try {
            ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
            if (contractStatus == null) {
                log.info("无商户入网记录更新营业执照不处理：{}", merchantSn);
                return;
            }
            // 更新入网协议
            agreementBiz.updateAgreement(merchantSn);

            //没有成功入网不更新
            if (ContractStatus.STATUS_SUCCESS != contractStatus.getStatus()) {
                log.info("商户入网不成功更新营业执照不处理：{}", merchantSn);
                return;
            }
            selfHelpNetInEventService.saveSelfHelpNetInEvent(merchantSn, ContractEvent.OPT_TYPE_UPDATE_BUSINESS_LICENSE, msg, list, map);
        } catch (ContractBizException e) {
            log.error("商户{}更新营业执照业务异常", merchantSn, e);
        } catch (Exception e) {
            log.error("商户{}更新营业执照系统异常", merchantSn, e);
            throw e;
        }
    }

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private com.wosai.upay.core.service.MerchantBusinessLicenseService coreBMerchantBusinessLicenseService;

    /**
     * 等到营业执照v2完全放开后，该消费可以移除
     */
    private boolean isLicenseVerifyV2(String merchantId) {
        try {
            MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, null);
            if (Objects.isNull(licenseInfo)) {
                return false;
            }
            Map extra = licenseInfo.getExtra();
            if (MapUtils.isEmpty(extra)) {
                return false;
            }
            boolean existMark = extra.containsKey("business_license_verify_v2");
            if (existMark) {
                extra.remove("business_license_verify_v2");
                HashMap<String, Object> updateMap = Maps.newHashMap();
                updateMap.put("id", licenseInfo.getId());
                updateMap.put("merchant_id", licenseInfo.getMerchant_id());
                updateMap.put("extra", extra);
                coreBMerchantBusinessLicenseService.updateMerchantBusinessLicense(updateMap);
            }
            return existMark;
        } catch (Exception e) {
            return false;
        }
    }
}