package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.wosai.upay.job.Constants.ApproveConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.changeAcquirerApprove.AcquirerApprove;
import com.wosai.upay.job.model.changeAcquirerApprove.AcquirerApproveExcel;
import com.wosai.upay.job.model.changeAcquirerApprove.ChangeAcquirerApproveDTO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * xxl_job_desc: BatchTask-批量仅入网任务
 * 单个仅入网
 *
 * <AUTHOR>
 * @date 2025/4/17
 */
@Slf4j
@Component("BatchOnlyContractJobHandler")
public class BatchOnlyContractJobHandler extends AbstractMcBatchTaskJobHandler {
    @Override
    public String getLockKey() {
        return "BatchOnlyContractJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        McBatchTaskExample mcBatchTaskExample = new McBatchTaskExample();
        mcBatchTaskExample.or()
                .andStatusEqualTo(0)
                .andEffect_timeLessThanOrEqualTo(new Date())
                .andTypeEqualTo(13);
        List<McBatchTask> list = mcBatchTaskMapper.selectByExampleWithBLOBs(mcBatchTaskExample);
        list.forEach(mcBatchTask -> {
            Map extra = Maps.newHashMap();
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                final String payload = mcBatchTask.getPayload();
                extra = CommonUtil.string2Map(payload);
                final ChangeAcquirerApproveDTO approveDTO = objectMapper.convertValue(MapUtils.getMap(extra, ApproveConstant.APPROVE_INFO), ChangeAcquirerApproveDTO.class);
                final String attachmentUrl = approveDTO.getAttachmentUrls().get(0);
                final List<AcquirerApproveExcel> acquirerApproveExcels = excelUtil.getExcelInfoList(attachmentUrl, new AcquirerApproveExcel());
                final List<AcquirerApprove> approveList = acquirerApproveExcels.stream()
                        .map(acquirerApproveExcel -> createContractEvent(approveDTO.getTarget(), acquirerApproveExcel))
                        .collect(Collectors.toList());
                //将approveList集合变成文件上传到oss中
                final String url = excelUtil.uploadToOss(approveList, BASE_DIR);
                //初步处理的文件
                extra.put(ApproveConstant.LAST_ATTACHMENT_URL, url);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask()
                        .setStatus(1).setId(mcBatchTask.getId())
                        .setPayload(JSONObject.toJSONString(extra)).setResult("处理中"));
            } catch (Exception e) {
                log.error("handleBatchOnlyContract error mc_batch_task {}  exception", mcBatchTask.getId(), e);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(3).setId(mcBatchTask.getId()).setResult(ExceptionUtil.getThrowableMsg(e)));
                callBack(extra, ExceptionUtil.getThrowableMsg(e), AUDIT_EXECUTE_FAIL);
            }
        });
    }

    private AcquirerApprove createContractEvent(String target, AcquirerApproveExcel acquirerApproveExcel) {
        //将acquirerApproveExcel实例转为acquirerApprove实例
        final AcquirerApprove acquirerApprove = new AcquirerApprove();
        final String merchantSn = acquirerApproveExcel.getMerchantSn();
        acquirerApprove.setMerchantSn(merchantSn);
        acquirerApprove.setRemark(acquirerApproveExcel.getRemark());
        //插入切换收单机构任务并返回对应的任务Id
        Long eventId = null;
        try {
            //入网事件Id
            eventId = doApplyOnlyContract(merchantSn, target);
        } catch (Exception exception) {
            //部分商户或出现校验失败需要先记录原因
            log.error("仅入网商户异常:{}", merchantSn, exception);
            String result = exception.getLocalizedMessage();
            acquirerApprove.setResult(StringUtils.isEmpty(result) ? "申请失败" : result);
        }
        //批量提交入网间隔5秒
        ThreadUtil.safeSleep(5000L);
        if (Objects.nonNull(eventId)) {
            acquirerApprove.setApplyId(String.valueOf(eventId));
        }
        return acquirerApprove;
    }
}
