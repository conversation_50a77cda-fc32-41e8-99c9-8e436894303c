package com.wosai.upay.job.service;

import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;

/**
 * @Author: jerry
 * @date: 2019/8/30 11:54
 * @Description:修改任务状态
 */
public interface TaskResultService {


    /**
     * 由于不知道changeStatusAndResult接口本身是否被外部调用
     * @param taskId
     * @param subTaskId
     * @param resultStatus
     * @param result
     * @param authPass
     */
    void changeStatusAndResultV2(Long taskId, Long subTaskId, int resultStatus, String result, boolean authPass);

    /**
     * 成功设置默认通道 发消息 提交实名 改银行卡状态 切通道
     * 失败 发消息 改银行卡状态
     * @param contractTask 进件任务
     * @param result       开通结果
     * @param resultStatus 当前task的状态
     */
    void setDefaultProvider(ContractTask contractTask, String result, int resultStatus, MultiProviderContractEvent contractEvent);


    }
