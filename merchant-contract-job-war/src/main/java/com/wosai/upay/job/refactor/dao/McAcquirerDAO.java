package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.shouqianba.cua.enums.core.ClearTypeEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.model.dto.AcquirerDto;
import com.wosai.upay.job.refactor.mapper.McAcquirerMapper;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/19
 */
@Repository
@Slf4j
public class McAcquirerDAO {

    private static final Cache<String, McAcquirerDO> CACHE = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    @Autowired
    private McAcquirerMapper mcAcquirerMapper;


    public McAcquirerDO getByAcquirer(String acquirer) {
        if (WosaiStringUtils.isEmpty(acquirer)) {
            throw new ContractBizException("acquirer is null");
        }
        try {
            McAcquirerDO mcAcquirerDO = CACHE.get(acquirer, () -> {
                List<McAcquirerDO> mcAcquirers = mcAcquirerMapper.selectList(
                        new LambdaQueryWrapper<McAcquirerDO>()
                                .eq(McAcquirerDO::getAcquirer, acquirer)
                );

                return WosaiCollectionUtils.isEmpty(mcAcquirers) ? null : mcAcquirers.get(0);
            });
            if (Objects.isNull(mcAcquirerDO)) {
                throw new ContractBizException("收单机构不存在 " + acquirer);
            }
            return mcAcquirerDO;

        } catch (Exception e) {
            log.error("getByAcquirer error {}", acquirer, e);
            throw new ContractBizException("收单机构不存在 " + acquirer);
        }
    }


    public McAcquirerDO getByProvider(String provider) {
        if (WosaiStringUtils.isEmpty(provider)) {
            throw new ContractBizException("provider is null");
        }
        try {
            McAcquirerDO mcAcquirerDO = CACHE.get(provider, () -> {
                List<McAcquirerDO> mcAcquirers = mcAcquirerMapper.selectList(
                        new LambdaQueryWrapper<McAcquirerDO>()
                                .eq(McAcquirerDO::getProvider, provider)
                );

                return WosaiCollectionUtils.isEmpty(mcAcquirers) ? null : mcAcquirers.get(0);
            });
            if (Objects.isNull(mcAcquirerDO)) {
                throw new ContractBizException("收单机构本身对应的provider: " + provider);
            }
            return mcAcquirerDO;

        } catch (Exception e) {
            log.error("getByProvider error {}", provider, e);
            throw new ContractBizException("收单机构本身对应的provider " + provider);
        }
    }

    public List<String> getIndirectAcquirerList() {
        List<McAcquirerDO> mcAcquirerList = mcAcquirerMapper.selectList(
                new LambdaQueryWrapper<McAcquirerDO>()
                        .eq(McAcquirerDO::getClearType, ClearTypeEnum.INDIRECT.getValue())
        );
        return mcAcquirerList.stream()
                .map(mcAcquire -> mcAcquire.getAcquirer())
                .collect(Collectors.toList());
    }

    public String getAcquirerName(String acquirer) {
        try {
            return getByAcquirer(acquirer).getName();
        } catch (Exception e) {
            log.error("getAcquirerName error {}", acquirer, e);
            return acquirer;
        }
    }

    public Page<McAcquirerDO> pageAcquirerList(PageInfo pageInfo, AcquirerDto acquirerDto) {
        QueryWrapper queryWrapper = new QueryWrapper<McAcquirerDO>()
                .eq(acquirerDto.getId() != null, "id", acquirerDto.getId())
                .eq(WosaiStringUtils.isNotBlank(acquirerDto.getAcquirer()), "acquirer", acquirerDto.getAcquirer())
                .eq(acquirerDto.getClear_type() != null, "clear_type", acquirerDto.getClear_type())
                .like(WosaiStringUtils.isNotBlank(acquirerDto.getName()), "name", acquirerDto.getName());

        List<OrderBy> orderBys = pageInfo.getOrderBy();
        if (WosaiCollectionUtils.isEmpty(orderBys)) {
            queryWrapper.orderByDesc("create_at");
        } else {
            for (OrderBy orderBy : orderBys) {
                if (Objects.equals(OrderBy.OrderType.ASC, orderBy.getOrder())) {
                    queryWrapper.orderByAsc(orderBy.getField());
                } else {
                    queryWrapper.orderByDesc(orderBy.getField());
                }
            }
        }

        return PageHelper.startPage(pageInfo.getPage(), pageInfo.getPageSize()).doSelectPage(() -> mcAcquirerMapper.selectList(queryWrapper));
    }

    /**
     * 获取所有的第三方收单机构
     *
     * @return 第三方收单机构列表
     */
    public List<String> listThirdAcquirers() {
        return mcAcquirerMapper.selectList(
                new LambdaQueryWrapper<McAcquirerDO>().eq(McAcquirerDO::getType, AcquirerOrgTypeEnum.THIRD_PARTY.getValue()))
                .stream()
                .map(McAcquirerDO::getAcquirer)
                .collect(Collectors.toList());
    }



    public int insert(McAcquirerDO acquirerDO) {
        final int row = mcAcquirerMapper.insert(acquirerDO);
        CACHE.invalidateAll();
        return row;
    }
}
