package com.wosai.upay.job.biz.acquirer;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.constant.ContractRuleConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component("lzb-AcquirerChangeBiz")
public class ChangeToLzbBizBank extends AbstractBankDirectAcquirerChangeBiz {
    @Value("${lzb_dev_code}")
    public String lzbDevCode;

    @Value("${lzb_trade_combo_id}")
    private long lzbTradeComboId;

    @Override
    protected String getDevCode(String acquirer) {
        return lzbDevCode;
    }

    @Override
    protected long getDefaultComboId(String acquirer) {
        return lzbTradeComboId;
    }

    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_LZB_RULE_GROUP;
    }

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_LZB.getValue();
    }
}
