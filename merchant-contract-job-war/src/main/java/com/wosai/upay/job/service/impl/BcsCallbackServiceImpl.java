package com.wosai.upay.job.service.impl;

import java.util.Date;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.bankDirect.BcsDirectBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.BankDirectApplyViewStatusEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.callback.req.BcsCallbackReq;
import com.wosai.upay.job.model.callback.res.BcsCallBackResponse;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.providers.BcsProvider;
import com.wosai.upay.job.service.BcsCallbackService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BcsCallbackServiceImpl implements BcsCallbackService {

    private static final String AUDIT_STATUS_SUCCESS = "PASS";

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private BcsProvider bcsProvider;

    @Autowired
    private QueryContractStatusHandler queryContractStatusHandler;

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    private BcsDirectBiz bcsDirectBiz;

    @Autowired
    private MerchantProviderParamsBiz merchantProviderParamsBiz;

    @Override
    public BcsCallBackResponse contractCallback(BcsCallbackReq bcsCallbackReq) {
        String mchtNo = bcsCallbackReq.getMchtNo();
        if (StringUtils.isBlank(mchtNo)) {
            log.error("BcsCallbackServiceImpl.contractCallback: mchtNo is blank");
            return BcsCallBackResponse.fail(400, "Contract ID is required");
        }
        String status = bcsCallbackReq.getStatus();
        ContractSubTask contractSubTask =
            contractSubTaskMapper.selectByContractIdAndContractRule(mchtNo, ContractRuleConstants.BCS_CONTRACT);
        if (Objects.isNull(contractSubTask)) {
            log.error("BcsCallbackServiceImpl.contractCallback: contractSubTask is null, mchtNo={}", mchtNo);
            return BcsCallBackResponse.fail(400, "Contract ID is required");
        }
        if (AUDIT_STATUS_SUCCESS.equals(status)) {
            // 进件成功
            ContractSubTask subTask = new ContractSubTask().setId(contractSubTask.getId())
                .setStatus(TaskStatus.SUCCESS.getVal()).setResponse_body(JSONObject.toJSONString(bcsCallbackReq));
            contractSubTaskMapper.updateByPrimaryKey(subTask);
            // 写入payWay是0的交易参数
            merchantProviderParamsBiz.saveAcquirerParams(contractSubTask.getMerchant_sn(), McConstant.RULE_GROUP_BCS,
                ProviderEnum.PROVIDER_BCS.getValue(), mchtNo, mchtNo, ContractRuleConstants.BCS_CONTRACT,
                McConstant.RULE_GROUP_BCS);
            handleContractSuccess(mchtNo, bcsCallbackReq.getReturnMsg());
        } else {
            // 进件失败
            ContractSubTask subTask = new ContractSubTask().setId(contractSubTask.getId())
                .setResponse_body(JSONObject.toJSONString(bcsCallbackReq)).setStatus(TaskStatus.FAIL.getVal())
                .setResult("长沙银行审核不通过");
            contractSubTaskMapper.updateByPrimaryKey(subTask);
            handleContractFail(mchtNo, bcsCallbackReq.getReturnMsg());
        }
        return BcsCallBackResponse.success();
    }

    private void handleContractSuccess(String contractId, String auditMsg) {
        ContractSubTask contractSubTask =
            contractSubTaskMapper.selectByContractIdAndContractRule(contractId, ContractRuleConstants.BCS_CONTRACT);
        BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(contractSubTask.getP_task_id());
        if (Objects.isNull(bankDirectApply)) {
            log.error("BcsCallbackServiceImpl.handleContractSuccess: bankDirectApply is null, contractId={}",
                contractId);
            return;
        }
        // 更新流程展示
        bcsDirectBiz.recordViewProcess(bankDirectApply, BankDirectApplyViewStatusEnum.AUTHING.getValue(), new Date());
        HandleQueryStatusResp handleQueryStatusResp = new HandleQueryStatusResp().setSuccess(true).setMessage(auditMsg);
        // 更新下游子任务或主任务状态
        queryContractStatusHandler.handTaskAndSubTask(contractSubTask, handleQueryStatusResp);

    }

    private void handleContractFail(String contractId, String message) {
        ContractSubTask contractSubTask =
            contractSubTaskMapper.selectByContractIdAndContractRule(contractId, ContractRuleConstants.BCS_CONTRACT);
        HandleQueryStatusResp handleQueryStatusResp = new HandleQueryStatusResp().setFail(true).setMessage(message);
        // 更新下游子任务或主任务状态
        queryContractStatusHandler.handTaskAndSubTask(contractSubTask, handleQueryStatusResp);
    }
}
