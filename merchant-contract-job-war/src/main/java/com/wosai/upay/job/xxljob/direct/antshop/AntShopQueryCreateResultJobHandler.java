package com.wosai.upay.job.xxljob.direct.antshop;

import com.wosai.upay.job.Constants.AntShopTaskConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.AntShopBiz;
import com.wosai.upay.job.model.DO.AntShopTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * xxl_job_desc: 蚂蚁门店-查询创建结果
 * <AUTHOR>
 * @date 2025/4/11
 */
@Slf4j
@Component("AntShopQueryCreateResultJobHandler")
public class AntShopQueryCreateResultJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private AntShopBiz antShopBiz;
    @Autowired
    private ChatBotUtil chatBotUtil;


    @Override
    public String getLockKey() {
        return "AntShopQueryCreateResultJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        List<AntShopTask> antShopTasks = antShopBiz.getAntShopTasks(
                Collections.singletonList(AntShopTaskConstant.TaskStatus.SHOP_CREATED_APPLY), Arrays.asList(AntShopTaskConstant.BusinessType.SCAN_CODE, AntShopTaskConstant.BusinessType.OTHER), param.getBatchSize(), param.getQueryTime()
        );
        antShopTasks.forEach(antShopTask -> {
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                antShopBiz.handleAntMerchantExpandShopQuery(antShopTask);
            } catch (Exception e) {
                log.error("商户号:{},查询蚂蚁店铺异常", antShopTask.getMerchant_sn(), e);
                antShopTask.setStatus(AntShopTaskConstant.TaskStatus.FAIL).setDescription("查询蚂蚁店铺异常");
                antShopBiz.updateAntShopTask(antShopTask);
                chatBotUtil.sendMessageToContractWarnChatBot(String.format("业务:%s,查询蚂蚁店铺异常,商户号:%s,异常信息:%s", antShopBiz.getBusiness(antShopTask.getBusiness_type()), antShopTask.getMerchant_sn(), "查询蚂蚁店铺异常"));
            }
        });
    }
}
