package com.wosai.upay.job.xxljob.batch;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ConfigStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.acquirer.AbstractAcquirerChangeBiz;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.fsm.Machine;
import com.wosai.upay.job.fsm.MachineBuilder;
import com.wosai.upay.job.fsm.State;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.service.ContractTaskResultServiceImpl;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.xxljob.context.McAcquirerChangeContext;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * xxl_job_desc: 切换收单机构
 *
 * <AUTHOR>
 * @date 2020-04-23
 */
@Slf4j
@Component("AcquirerChangeJobHandler")
public class AcquirerChangeJobHandler extends AbstractBatchJobHandler<McAcquirerChangeContext> {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private AcquirerChangeDao acquirerChangeDao;

    @Autowired
    private ContractEventMapper contractEventMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private AcquirerChangeDao changeDao;

    private Machine<McAcquirerChange> machine;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private ContractTaskResultServiceImpl contractTaskResultService;
    @Autowired
    @Qualifier("acquirerChangeThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor acquirerChangeThreadPoolTaskExecutor;

    /**
     * 等待调度，
     */
    public static final int WAIT_FOR_SCHEDULE = 99;
    @Resource
    private MerchantProviderParamsService merchantProviderParamsService;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    SubBizParamsBiz subBizParamsBiz;

    @PostConstruct
    public void init() {
        MachineBuilder<McAcquirerChange> builder = new MachineBuilder<>();
        machine = builder
                .on(AcquirerChangeStatus.PENDING).invoke(change -> contract((McAcquirerChange) change))
                .on(AcquirerChangeStatus.CONTRACTING).invoke(change -> checkContract((McAcquirerChange) change))
                .on(AcquirerChangeStatus.CONTRACTED).invoke(change -> syncFee((McAcquirerChange) change))
                .on(AcquirerChangeStatus.SYNC_FEE).invoke(change -> checkSyncFee((McAcquirerChange) change))
                .on(AcquirerChangeStatus.SYNC_FEE_SUCCESS).invoke(change -> syncMchStatusToAcquirer((McAcquirerChange) change))
                .on(AcquirerChangeStatus.CHANGE_ACQUIRER_STATUS_SUCCESS).invoke(change -> changeAcquirer((McAcquirerChange) change))
                .on(AcquirerChangeStatus.SUCCESS).end()
                .on(AcquirerChangeStatus.FAIL).end()
                .on(WAIT_FOR_SCHEDULE).end()
                .build();
    }

    @Override
    public List<McAcquirerChangeContext> queryTaskItems(BatchJobParam param) {
        // 初始化信号量
        Semaphore semaphore = new Semaphore(applicationApolloConfig.getAcquirerChangeScheduleSemaphore());

        // 根据时间段获取不同的任务

        if (isNight()) {
            return acquirerChangeDao.getNightChangeApplies(param.getBatchSize(), param.getQueryTime()).stream().map(r -> new McAcquirerChangeContext(r, semaphore)).collect(Collectors.toList());
        } else {
            return acquirerChangeDao.getDayChangeApplies(param.getBatchSize(), param.getQueryTime()).stream().map(r -> new McAcquirerChangeContext(r, semaphore)).collect(Collectors.toList());
        }
    }

    @Override
    public String getLockKey(McAcquirerChangeContext change) {
        return "AcquirerChangeJobHandler:" + change.getMcAcquirerChange().getApply_id();
    }

    @Override
    protected void doHandle(McAcquirerChangeContext change) {
        // 重写父类方法，添加信号量控制
        if (ShutdownSignal.isShuttingDown()) {
            return;
        }
        try {
            // 尝试获取信号量
            change.getSemaphore().acquire();
        } catch (InterruptedException e) {
            log.error("semaphore acquire error", e);
        }
        // 提交到线程池执行
        acquirerChangeThreadPoolTaskExecutor.submit(RunnableWrapper.of(() -> {
            try {
                super.doHandle(change); // 调用父类方法，会执行加锁和处理逻辑
            } finally {
                change.getSemaphore().release(); // 释放信号量
            }
        }));
    }

    @Override
    public void doHandleSingleData(McAcquirerChangeContext mcAcquirerChangeContext) {
        try {
            // 重新获取最新数据
            McAcquirerChange change = mcAcquirerChangeContext.getMcAcquirerChange();
            change = acquirerChangeDao.getAcquirerChangeByApplyId(change.getApply_id());
            State<McAcquirerChange> state = machine.getState(change.getStatus());
            while (state != null && !state.isEnd()) {
                change = acquirerChangeDao.getAcquirerChangeByApplyId(change.getApply_id());
                Integer nextStatus = state.getAction().invoke(change);
                state = machine.getState(nextStatus);
            }
        } catch (Exception e) {
            log.error("Process acquirer change error, apply_id: {}", mcAcquirerChangeContext.getMcAcquirerChange().getApply_id(), e);
        }
    }

    private boolean isNight() {
        int hour = LocalTime.now().getHour();
        return hour >= 3 && hour <= 5;
    }


    public Integer contract(McAcquirerChange change) {
        AbstractAcquirerChangeBiz changeBiz = getChangeBiz(change);
        try {
            log.info("contract start merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id());
            Map extra = CommonUtil.string2Map(change.getExtra());
            long eventId = BeanUtil.getPropLong(extra, "event_id", -1);
            if (eventId < 0) {
                changeBiz.contractToTargetAcquirer(change);
            } else {
                log.info("merchant_sn {} apply_id {} 进件事件已生成 {}", change.getMerchant_sn(), change.getApply_id(), eventId);
            }
        } catch (Exception e) {
            log.error("contract error merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id(), e);
            changeBiz.changeFail(change, e);
        }
        return acquirerChangeDao.getAcquirerChangeByApplyId(change.getApply_id()).getStatus();
    }

    public Integer checkContract(McAcquirerChange change) {
        AbstractAcquirerChangeBiz changeBiz = getChangeBiz(change);
        try {
            log.info("checkContract start merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id());
            Map extra = CommonUtil.string2Map(change.getExtra());
            long eventId = BeanUtil.getPropLong(extra, "event_id");
            if (!Objects.equals(eventId, 0L)) {
                ContractEvent contractEvent = contractEventMapper.selectByPrimaryKey(eventId);
                if (contractEvent.getStatus() == ContractEvent.STATUS_SUCCESS) {
                    ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractEvent.getTask_id());
                    if (contractTask.getStatus().equals(TaskStatus.SUCCESS.getVal())) {
                        changeDao.updateStatus(change, AcquirerChangeStatus.CONTRACTED, "报备成功");
                    } else if (contractTask.getStatus().equals(TaskStatus.FAIL.getVal())) {
                        throw new CommonPubBizException("报备失败 " + contractTaskResultService.getEscapedContent(contractTask));
                    }
                } else if (contractEvent.getStatus() == ContractEvent.STATUS_BIZ_FAIL) {
                    throw new CommonPubBizException("报备失败 " + contractEvent.getResult());
                }
            }
            long taskId = BeanUtil.getPropLong(extra, "task_id");
            if (!Objects.equals(taskId, 0L)) {
                ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
                if (contractTask.getStatus().equals(TaskStatus.SUCCESS.getVal())) {
                    changeDao.updateStatus(change, AcquirerChangeStatus.CONTRACTED, "银行卡更新成功");
                } else if (contractTask.getStatus().equals(TaskStatus.FAIL.getVal())) {
                    throw new CommonPubBizException("银行卡更新失败 " + contractTask.getResult());
                }
            }
        } catch (Exception e) {
            // 修复数据
            changeBiz.postContractFail(change);
            log.error("checkContract error merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id(), e);
            changeBiz.changeFail(change, e);
        }
        Integer status = acquirerChangeDao.getAcquirerChangeByApplyId(change.getApply_id()).getStatus();
        return status == AcquirerChangeStatus.CONTRACTING ? WAIT_FOR_SCHEDULE : status;
    }

    public Integer syncFee(McAcquirerChange change) {
        AbstractAcquirerChangeBiz changeBiz = getChangeBiz(change);
        try {
            log.info("syncFee start merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id());
            Map extra = CommonUtil.string2Map(change.getExtra());
            long eventId = BeanUtil.getPropLong(extra, "fee_event_id", -1);
            if (eventId < 0) {
                changeBiz.syncFeeToTargetAcquirer(change);
            } else {
                log.info("merchant_sn {} apply_id {} 同步费率事件已生成 {}", change.getMerchant_sn(), change.getApply_id(), eventId);
            }
        } catch (Exception e) {
            log.error("syncFee error merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id(), e);
            changeBiz.changeFail(change, e);
        }
        return acquirerChangeDao.getAcquirerChangeByApplyId(change.getApply_id()).getStatus();
    }

    public Integer checkSyncFee(McAcquirerChange change) {
        AbstractAcquirerChangeBiz changeBiz = getChangeBiz(change);
        try {
            log.info("checkSyncFee start merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id());
            Map extra = CommonUtil.string2Map(change.getExtra());
            long eventId = BeanUtil.getPropLong(extra, "fee_event_id");
            if (!Objects.equals(eventId, 0L)) {
                ContractEvent contractEvent = contractEventMapper.selectByPrimaryKey(eventId);
                if (contractEvent.getStatus() == ContractEvent.STATUS_SUCCESS) {
                    ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractEvent.getTask_id());
                    if (contractTask.getStatus().equals(TaskStatus.SUCCESS.getVal())) {
                        changeDao.updateStatus(change, AcquirerChangeStatus.SYNC_FEE_SUCCESS, "同步费率成功");
                        changeBiz.sendNotice(change.getMerchant_id());
                    } else if (contractTask.getStatus().equals(TaskStatus.FAIL.getVal())) {
                        throw new CommonPubBizException("同步费率失败 " + contractTaskResultService.getEscapedContent(contractTask));
                    }
                } else if (contractEvent.getStatus() == ContractEvent.STATUS_BIZ_FAIL) {
                    throw new CommonPubBizException("同步费率失败 " + contractEvent.getResult());
                }
            }
            long taskId = BeanUtil.getPropLong(extra, "fee_task_id");
            if (!Objects.equals(taskId, 0L)) {
                ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
                if (contractTask.getStatus().equals(TaskStatus.SUCCESS.getVal())) {
                    changeDao.updateStatus(change, AcquirerChangeStatus.SYNC_FEE_SUCCESS, "同步费率成功");
                    changeBiz.sendNotice(change.getMerchant_id());
                } else if (contractTask.getStatus().equals(TaskStatus.FAIL.getVal())) {
                    throw new CommonPubBizException("同步费率失败 " + contractTask.getResult());
                }
            }
        } catch (Exception e) {
            log.error("checkSyncFee error merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id(), e);
            changeBiz.changeFail(change, e);
        }
        Integer status = acquirerChangeDao.getAcquirerChangeByApplyId(change.getApply_id()).getStatus();
        return status == AcquirerChangeStatus.SYNC_FEE ? WAIT_FOR_SCHEDULE : status;
    }

    public Integer syncMchStatusToAcquirer(McAcquirerChange change) {
        Boolean immediately = change.getImmediately() || isNight();
        if (!immediately) {
            return WAIT_FOR_SCHEDULE;
        }
        AbstractAcquirerChangeBiz changeBiz = getChangeBiz(change);
        try {
            log.info("syncMchStatusToAcquirer start merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id());
            changeBiz.syncMchStatusToAcquirer(change);
        } catch (Exception e) {
            log.error("syncMchStatusToAcquirer error merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id(), e);
            changeBiz.changeFail(change, e);
        }
        return acquirerChangeDao.getAcquirerChangeByApplyId(change.getApply_id()).getStatus();
    }

    @Timed(value = "切收单机构")
    public Integer changeAcquirer(McAcquirerChange change) {
        AbstractAcquirerChangeBiz changeBiz = getChangeBiz(change);
        try {
            doCheckBeforeChange(change);
            doChangeAcquirer(change, changeBiz);
            changeBiz.configDigitalCurrency(change.getMerchant_sn());
        } catch (Exception e) {
            log.error("changeAcquirer error merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id(), e);
            changeBiz.changeFail(change, e);
        }
        return acquirerChangeDao.getAcquirerChangeByApplyId(change.getApply_id()).getStatus();
    }

    /**
     * 在切换之前再做一次校验，防止出现多个间连通道的情况
     * <a href="https://furcas.shouqianba.com/workdetail?id=2024092318377715">2024092318377715</a>
     *
     * @param change 切换任务
     */
    private void doCheckBeforeChange(McAcquirerChange change) {
        Map extra = CommonUtil.string2Map(change.getExtra());
        String tradeAppId = BeanUtil.getPropString(extra, "tradeAppId");
        subBizParamsBiz.multiCheckWhenChange(change.getMerchant_sn(), change.getTarget_acquirer(), tradeAppId);
    }

    private void doChangeAcquirer(McAcquirerChange change, AbstractAcquirerChangeBiz changeBiz) {
        log.info("doChangeAcquirer start merchant_sn {} apply_id {}", change.getMerchant_sn(), change.getApply_id());
        // 加这个锁是为了防止切机构的过程中有其他业务切参数
        RLock lock = redissonClient.getLock("changeAcquirer:" + change.getMerchant_sn());
        try {
            if (lock.tryLock()) {
                changeBiz.doChangeAcquirer(change);
            }
        } catch (Exception e) {
            log.error("doChangeAcquirer error, apply_id {}, rollback to source acquirer trade params", change.getApply_id());
            rollbackMerchantConfigWhenChangeFailed(change);
            throw new ContractBizException("切交易参数异常: " + e.getMessage(), e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 切换收单机构失败后,回滚已经写入交易参的配置参数
     * 切收单机构参数失败时候,交易侧可能已经写入部分目标收单机构的配置参数,cua侧已经回滚至原收单机构的配置参数
     *
     * @param change 切收单机构申请
     */
    private void rollbackMerchantConfigWhenChangeFailed(McAcquirerChange change) {
        String tradeAppId = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(change.getExtra())) {
            tradeAppId = BeanUtil.getPropString(CommonUtil.string2Map(change.getExtra()), "tradeAppId");
        }
        if (StringUtils.isBlank(tradeAppId)) {
            tradeAppId = subBizParamsBiz.getPayTradeAppId();
        }
        String finalTradeAppId = tradeAppId;
        merchantProviderParamsDAO
                .listByMerchantSnAndStatus(change.getMerchant_sn(), ConfigStatusEnum.CONFIG_SUCCESS.getValue())
                .stream()
                .filter(t -> Objects.nonNull(t.getPayway()) && !Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                .forEach(paramsDO -> {
                    try {
                        merchantProviderParamsService.setDefaultMerchantProviderParamsByTradeApp(paramsDO.getId(), null, "change failed rollback", finalTradeAppId);
                    } catch (Exception e) {
                        log.error("回滚交易参数失败, paramsId = {}", paramsDO.getId(), e);
                    }
                });
    }

    private AbstractAcquirerChangeBiz getChangeBiz(McAcquirerChange change) {
        try {
            return applicationContext.getBean(change.getTarget_acquirer() + "-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
        } catch (Exception e) {
            // 找不到对应的处理类，尝试返回一个 commonBank 的类
            return applicationContext.getBean("commonBank-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
        }
    }
}

