package com.wosai.upay.job.xxljob.direct.contracttask;

import com.alibaba.fastjson.JSON;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.enume.LklPicType;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.PendingTasks;
import com.wosai.upay.merchant.contract.service.UploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * xxl_job_desc: 进件任务-拉卡拉图片上传
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("ContractTaskLklUploadPicJobHandler")
public class ContractTaskLklUploadPicJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private UploadService uploadService;

    @Override
    public String getLockKey() {
        return "ContractTaskLklUploadPicJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            List<ContractSubTask> subTaskList = contractSubTaskMapper.selectByUpload(StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
            for (ContractSubTask contractSubTask : subTaskList) {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                ContractResponse contractResponse = uploadPic(contractSubTask);
                if (contractResponse == null) {
                    continue;
                }
                if (200 == contractResponse.getCode()) {
                    contractSubTask.setStatus(5).setResponse_body(JSON.toJSONString(contractResponse));
                    contractSubTaskMapper.updateByPrimaryKey(contractSubTask);
                } else if (400 == contractResponse.getCode()) {
                    contractSubTask.setStatus(6).setResponse_body(JSON.toJSONString(contractResponse));
                    contractSubTaskMapper.updateByPrimaryKey(contractSubTask);
                } else {
                    //do nothing;
                }
            }
        } catch (Exception e) {
            log.error(" uploadPic error", e);
            chatBotUtil.sendMessageToContractWarnChatBot("uploadPic error" + ExceptionUtil.getThrowableMsg(e));
        }
    }

    private ContractResponse uploadPic(ContractSubTask contractSubTask) {
        Map eventMsg = JSON.parseObject(contractSubTask.getRequest_body(), Map.class);
        log.info("uploadPic begin: merchantSn {} {}", contractSubTask.getMerchant_sn(), eventMsg);
        String merchantSn = contractSubTask.getMerchant_sn();
        String picUlr = BeanUtil.getPropString(eventMsg, PendingTasks.MSG_PIC_URL);
        String picType = BeanUtil.getPropString(eventMsg, PendingTasks.MSG_PIC_TYPE);
        String lklMerId = BeanUtil.getPropString(eventMsg, PendingTasks.MSG_LKL_MER_ID);
        String contractId = BeanUtil.getPropString(eventMsg, PendingTasks.CONTRACT_ID);
        try {
            return uploadService.uploadPicNew(merchantSn, contractId, lklMerId, LklPicType.toPic(picType), picUlr);
        } catch (Exception e) {
            return null;
        }
    }
}
