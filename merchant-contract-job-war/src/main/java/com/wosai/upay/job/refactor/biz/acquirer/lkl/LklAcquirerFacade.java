package com.wosai.upay.job.refactor.biz.acquirer.lkl;

import com.shouqianba.cua.enums.contract.AcquirerMerchantStatusEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.biz.acquirer.LklAcquirerBiz;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * lkl收单处理策略
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
@Slf4j
public class LklAcquirerFacade extends AbstractAcquirerHandler {

    @Resource
    private LklMerchantInfoProcessor lklMerchantInfoProcessor;

    @Resource(name = "lkl-biz")
    private LklAcquirerBiz lklAcquirerBiz;

    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.LKL;
    }

    @Override
    public boolean isBankCardConsistentWithSqb(String merchantSn) {
        return acquirerCommonTemplate.checkBankCardConsistentWithSqb(
                getTypeValue(), merchantSn,
                t -> lklMerchantInfoProcessor.getMerchantBankAccountNo(merchantSn).orElse(null),
                null
        );
    }

    /**
     * 获取商户所在收单机构的商户状态
     * todo 待重构 本次来不及
     *
     * @param merchantSn 商户号
     * @return 商户状态
     */
    @Override
    public AcquirerMerchantStatusEnum getAcquirerMerchantStatus(String merchantSn) {
        try {
            return lklAcquirerBiz.getAcquirerMchStatus(merchantSn) ? AcquirerMerchantStatusEnum.NORMAL : AcquirerMerchantStatusEnum.CLOSE;
        } catch (Exception e) {
            log.error("获取拉卡拉商户状态异常", e);
            return AcquirerMerchantStatusEnum.CLOSE;
        }
    }
}
