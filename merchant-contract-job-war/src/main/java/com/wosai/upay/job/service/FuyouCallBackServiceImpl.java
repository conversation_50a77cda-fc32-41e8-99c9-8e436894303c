package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.AddAffectStatusSuccessTaskCountBiz;
import com.wosai.upay.job.biz.IndustryMappingCommonBiz;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.helper.MerchantNameHelper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.ProviderTerminalTaskMapper;
import com.wosai.upay.job.model.ChangeAuditPushRequest;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import com.wosai.upay.job.model.dto.ProviderTerminalContext;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * @Author: haochen
 * @date: 2023/10/09 18:09
 * @Description:富友回调服务实现(merchant_contract调用)
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class FuyouCallBackServiceImpl implements FuyouCallBackService{

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private WechatAuthBiz wechatAuthBiz;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private AddAffectStatusSuccessTaskCountBiz addAffectStatusSuccessTaskCountBiz;

    @Autowired
    private TaskResultService taskResultService;

    @Autowired
    private ProviderTerminalTaskMapper terminalTaskMapper;

    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;

    private static String alipayChannelNo = "2088011691288213";

    private static String wechatChannelNo = "178700778";

    @Autowired
    private T9Service t9Service;

    @Autowired
    private MerchantNameHelper merchantNameHelper;


    @Override
    public int querySubTaskByContractId(Map<String, Object> callbackMsg){
        Map param = MapUtils.getMap(callbackMsg, "result");
        ContractSubTask subTask;
        String contractId = "";
        if("alipay".equals(BeanUtil.getPropString(param, "callbackTp"))) {
            contractId = BeanUtil.getPropString(param, "mchntCd" + "_2");
        }else if("wechat".equals(BeanUtil.getPropString(param, "callbackTp"))){
            contractId = BeanUtil.getPropString(param, "mchntCd" + "_3");
        }
        if(StringUtil.empty(contractId)){
            log.error("fuyou回调未找到对应的子任务:{}", JSONObject.toJSONString(callbackMsg));
            return 0;
        }
        subTask = contractSubTaskMapper.selectByContractId(contractId);
        if(subTask == null){
            log.error("fuyou回调未找到对应的子任务:{}", JSONObject.toJSONString(callbackMsg));
            return 0;
        }
        ContractTask task = contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id());
        String result = BeanUtil.getPropString(param, "reportSt");
        if ("9".equals(result)) {
            Map resp = JSON.parseObject(subTask.getResponse_body(), Map.class);
            callbackMsg.put("callback_time", System.currentTimeMillis());
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask().setId(subTask.getId())
                            .setStatus(TaskStatus.FAIL.getVal())
                            .setResponse_body(JSON.toJSONString(resp))
                            .setResult("拉卡拉回调失败")
            );
            Long taskId = subTask.getP_task_id();
            Map reMsg = CollectionUtil.hashMap("channel", ProviderUtil.LKL_CALLBACK_CHANNEL, "message", BeanUtil.getPropString(callbackMsg,"msgs"), "result", callbackMsg);
            taskResultService.changeStatusAndResultV2(taskId, subTask.getId(), 6, JSON.toJSONString(reMsg), false);
            return 1;
        }

        String merchantName = merchantNameHelper.getMerchantName(task.getMerchant_sn());
        String payMerchantId = BeanUtil.getPropString(param, "subMchId");
        if(!"1".equals(result)){
            return 0;
        }

        if (subTask == null) {
            return 0;
        }
        MerchantProviderParams tarparams = merchantProviderParamsMapper.getParamsByProviderMerchantIdAndProviderAndPayWay(contractId, String.valueOf(ProviderEnum.PROVIDER_FUYOU.getValue()), String.valueOf(PaywayEnum.ACQUIRER.getValue()));
        //lkl可能出现推送重复消息 因此只记录状态为[等待回调]的subtask
        if (!SubTaskStatus.isWaiting(subTask.getStatus())) {
            return 1;
        }
        MerchantProviderParams params = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
        if(params != null){
            return 1;
        }

        Map merchant = merchantService.getMerchantBySn(tarparams.getMerchant_sn());
        MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(tarparams.getPay_merchant_id(), null);
        if("alipay".equals(BeanUtil.getPropString(param, "callbackTp"))){
            String unionMcc = industryMappingCommonBiz.getAliIndirectMcc(BeanUtil.getPropString(merchant, Merchant.INDUSTRY));
            params = new MerchantProviderParams().setId(UUID.randomUUID().toString()).setMerchant_name(merchantName)
                    .setChannel_no(alipayChannelNo).setProvider(ProviderEnum.PROVIDER_FUYOU.getValue()).setProvider_merchant_id(contractId).setPay_merchant_id(payMerchantId).setMerchant_sn(tarparams.getMerchant_sn())
                    .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL).setContract_rule("fuyou").setRule_group_id("fuyou").setPayway(PaywayEnum.ALIPAY.getValue())
                    .setAli_mcc(unionMcc).setCtime(System.currentTimeMillis()).setMtime(System.currentTimeMillis());
            merchantProviderParamsMapper.insertSelective(params);
            ProviderTerminalContext context = ProviderTerminalContext.builder()
                    .subMerchant(payMerchantId)
                    .provider(ProviderEnum.PROVIDER_FUYOU.getValue())
                    .payWay(PaywayEnum.ALIPAY.getValue())
                    .build();
            //不存在则插入
            ProviderTerminalTask providerTerminalTask = new ProviderTerminalTask();
            providerTerminalTask.setMerchant_sn(params.getMerchant_sn());
            providerTerminalTask.setType(ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType());
            providerTerminalTask.setContext(JSONObject.toJSONString(context));
            terminalTaskMapper.insertSelective(providerTerminalTask);
        }else if("wechat".equals(BeanUtil.getPropString(param, "callbackTp"))){
            String settlementId = wechatAuthBiz.getSettlementId(BeanUtil.getPropString(merchant, Merchant.INDUSTRY), license.getType(), merchantName);
            params = new MerchantProviderParams().setId(UUID.randomUUID().toString()).setMerchant_name(merchantName)
                    .setChannel_no(wechatChannelNo).setProvider(ProviderEnum.PROVIDER_FUYOU.getValue()).setProvider_merchant_id(contractId).setPay_merchant_id(payMerchantId).setMerchant_sn(tarparams.getMerchant_sn())
                    .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE).setContract_rule("fuyou").setRule_group_id("fuyou").setPayway(PaywayEnum.WEIXIN.getValue())
                    .setWx_settlement_id(settlementId).setCtime(System.currentTimeMillis()).setMtime(System.currentTimeMillis());
            merchantProviderParamsMapper.insertSelective(params);
            ProviderTerminalContext context = ProviderTerminalContext.builder()
                    .subMerchant(payMerchantId)
                    .provider(ProviderEnum.PROVIDER_FUYOU.getValue())
                    .payWay(PaywayEnum.WEIXIN.getValue())
                    .build();
            //不存在则插入
            ProviderTerminalTask providerTerminalTask = new ProviderTerminalTask();
            providerTerminalTask.setMerchant_sn(params.getMerchant_sn());
            providerTerminalTask.setType(ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType());
            providerTerminalTask.setContext(JSONObject.toJSONString(context));
            terminalTaskMapper.insertSelective(providerTerminalTask);
        }
        if (subTask.getStatus_influ_p_task() == 1) {
            Long taskId = subTask.getP_task_id();
                addAffectStatusSuccessTaskCountBiz.addAffectStatusSuccessTaskCount(taskId);
                contractSubTaskMapper.setEnableScheduleByDepId(subTask.getId());
                if (!task.getStatus().equals(TaskStatus.PROGRESSING.getVal())) {
                    taskResultService.changeStatusAndResultV2(taskId, subTask.getId(), task.getStatus(), null, false);
                }

        } else {
            contractSubTaskMapper.setEnableScheduleByDepId(subTask.getId());
        }
        return 1;
    }

    @Override
    public int updateSubTaskByContractId(Map<String, Object> callbackMsg){
        Map param = MapUtils.getMap(callbackMsg, "result");
        ContractSubTask subTask;
        String contractId = "";
        if("alipay".equals(BeanUtil.getPropString(param, "auditResult"))) {
            contractId = BeanUtil.getPropString(param, "mchntCd" + "_update");
        }else if("wechat".equals(BeanUtil.getPropString(param, "callbackTp"))){
            contractId = BeanUtil.getPropString(param, "mchntCd" + "_update");
        }
        if(StringUtil.empty(contractId)){
            log.error("fuyou回调未找到对应的子任务:{}", JSONObject.toJSONString(callbackMsg));
            return 0;
        }
        subTask = contractSubTaskMapper.selectByContractId(contractId);
        if(subTask == null){
            log.error("fuyou回调未找到对应的子任务:{}", JSONObject.toJSONString(callbackMsg));
            return 0;
        }
        ContractTask task = contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id());
        String result = BeanUtil.getPropString(param, "reportSt");
        if ("0".equals(result)) {
            Map resp = JSON.parseObject(subTask.getResponse_body(), Map.class);
            callbackMsg.put("callback_time", System.currentTimeMillis());
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask().setId(subTask.getId())
                            .setStatus(TaskStatus.FAIL.getVal())
                            .setResponse_body(JSON.toJSONString(resp))
                            .setResult("富友回调失败")
            );
            Long taskId = subTask.getP_task_id();
            Map reMsg = CollectionUtil.hashMap("channel", ProviderUtil.FUYOU_CALLBACK_CHANNEL, "message", BeanUtil.getPropString(callbackMsg, "msgs"), "result", callbackMsg);
            taskResultService.changeStatusAndResultV2(taskId, subTask.getId(), TaskStatus.FAIL.getVal(), JSON.toJSONString(reMsg), false);
            return 1;
        }else if ("1".equals(result)){
            Map resp = JSON.parseObject(subTask.getResponse_body(), Map.class);
            callbackMsg.put("callback_time", System.currentTimeMillis());
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask().setId(subTask.getId())
                            .setStatus(TaskStatus.SUCCESS.getVal())
                            .setResponse_body(JSON.toJSONString(resp))
                            .setResult("富友回调成功")
            );
            if (subTask.getStatus_influ_p_task() == 1) {
                Long taskId = subTask.getP_task_id();
                addAffectStatusSuccessTaskCountBiz.addAffectStatusSuccessTaskCount(taskId);
                contractSubTaskMapper.setEnableScheduleByDepId(subTask.getId());
                if (!task.getStatus().equals(TaskStatus.PROGRESSING.getVal())) {
                    taskResultService.changeStatusAndResultV2(taskId, subTask.getId(), task.getStatus(), null, false);
                }

            } else {
                contractSubTaskMapper.setEnableScheduleByDepId(subTask.getId());
            }
            return 1;
        }
        return 0;
    }

    @Override
    public int contractSubTaskByContractId(Map<String, Object> callbackMsg){
        Map param = MapUtils.getMap(callbackMsg, "result");
        ContractSubTask subTask;
        String contractId = "";
        if("alipay".equals(BeanUtil.getPropString(param, "auditResult"))) {
            contractId = BeanUtil.getPropString(param, "mchntCd" + "_contract");
        }else if("wechat".equals(BeanUtil.getPropString(param, "callbackTp"))){
            contractId = BeanUtil.getPropString(param, "mchntCd" + "_contract");
        }
        if(StringUtil.empty(contractId)){
            log.error("fuyou回调未找到对应的子任务:{}", JSONObject.toJSONString(callbackMsg));
            return 0;
        }
        subTask = contractSubTaskMapper.selectByContractId(contractId);
        if(subTask == null){
            log.error("fuyou回调未找到对应的子任务:{}", JSONObject.toJSONString(callbackMsg));
            return 0;
        }
        ContractTask task = contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id());
        String result = BeanUtil.getPropString(param, "reportSt");
        if ("0".equals(result)) {
            Map resp = JSON.parseObject(subTask.getResponse_body(), Map.class);
            callbackMsg.put("callback_time", System.currentTimeMillis());
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask().setId(subTask.getId())
                            .setStatus(TaskStatus.FAIL.getVal())
                            .setResponse_body(JSON.toJSONString(resp))
                            .setResult("富友回调失败")
            );
            Long taskId = subTask.getP_task_id();
            Map reMsg = CollectionUtil.hashMap("channel", ProviderUtil.FUYOU_CALLBACK_CHANNEL, "message", BeanUtil.getPropString(callbackMsg, "msgs"), "result", callbackMsg);
            taskResultService.changeStatusAndResultV2(taskId, subTask.getId(), TaskStatus.FAIL.getVal(), JSON.toJSONString(reMsg), false);
            return 1;
        }else if ("1".equals(result)){
            Map resp = JSON.parseObject(subTask.getResponse_body(), Map.class);
            callbackMsg.put("callback_time", System.currentTimeMillis());
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask().setId(subTask.getId())
                            .setStatus(TaskStatus.SUCCESS.getVal())
                            .setResponse_body(JSON.toJSONString(resp))
                            .setResult("富友回调成功")
            );
            if (subTask.getStatus_influ_p_task() == 1) {
                Long taskId = subTask.getP_task_id();
                addAffectStatusSuccessTaskCountBiz.addAffectStatusSuccessTaskCount(taskId);
                contractSubTaskMapper.setEnableScheduleByDepId(subTask.getId());
                if (!task.getStatus().equals(TaskStatus.PROGRESSING.getVal())) {
                    taskResultService.changeStatusAndResultV2(taskId, subTask.getId(), task.getStatus(), null, false);
                }

            } else {
                contractSubTaskMapper.setEnableScheduleByDepId(subTask.getId());
            }
            return 1;
        }
        return 0;

    }

    /**
     * 当前这个方法只是实现了modifyType:WK 外卡变更
     * @param callbackMsg 富友回调原始信息
     * @return
     */
    @Override
    public int changeAuditPush(Map<String, Object> callbackMsg) {
        String modifyNo = null;
        try {
            final ChangeAuditPushRequest auditPushRequest = JSONObject.parseObject(JSONObject.toJSONString(callbackMsg), ChangeAuditPushRequest.class);
            modifyNo = auditPushRequest.getModifyNo();
            final String modifyType = auditPushRequest.getModifyType();
            //如果不是WK 外卡变更,直接忽略
            if(!Objects.equals(modifyType,"WK")) {
                return 1;
            }
            //处理外卡相关逻辑
            t9Service.changeFyForeignCardStatus(auditPushRequest);
        } catch (Exception e) {
           log.error("changeAuditPush error modifyNo:{}",modifyNo,e);
        }
        return 1;
    }
}
