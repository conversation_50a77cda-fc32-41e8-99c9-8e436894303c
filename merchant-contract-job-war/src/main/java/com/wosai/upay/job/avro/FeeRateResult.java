/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class FeeRateResult extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -2986135422189929451L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"FeeRateResult\",\"namespace\":\"com.wosai.upay.job.avro\",\"fields\":[{\"name\":\"provider\",\"type\":\"string\",\"meta\":\"收单机构号\"},{\"name\":\"result\",\"type\":\"boolean\",\"meta\":\"处理结果\"},{\"name\":\"merchantSn\",\"type\":\"string\",\"meta\":\"商户号\"},{\"name\":\"feeRate\",\"type\":\"string\",\"meta\":\"费率详细信息\"},{\"name\":\"taskId\",\"type\":[\"null\",\"long\"],\"default\":null,\"meta\":\"进件任务Id\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<FeeRateResult> ENCODER =
      new BinaryMessageEncoder<FeeRateResult>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<FeeRateResult> DECODER =
      new BinaryMessageDecoder<FeeRateResult>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<FeeRateResult> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<FeeRateResult> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<FeeRateResult>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this FeeRateResult to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a FeeRateResult from a ByteBuffer. */
  public static FeeRateResult fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence provider;
  @Deprecated public boolean result;
  @Deprecated public java.lang.CharSequence merchantSn;
  @Deprecated public java.lang.CharSequence feeRate;
  @Deprecated public java.lang.Long taskId;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public FeeRateResult() {}

  /**
   * All-args constructor.
   * @param provider The new value for provider
   * @param result The new value for result
   * @param merchantSn The new value for merchantSn
   * @param feeRate The new value for feeRate
   * @param taskId The new value for taskId
   */
  public FeeRateResult(java.lang.CharSequence provider, java.lang.Boolean result, java.lang.CharSequence merchantSn, java.lang.CharSequence feeRate, java.lang.Long taskId) {
    this.provider = provider;
    this.result = result;
    this.merchantSn = merchantSn;
    this.feeRate = feeRate;
    this.taskId = taskId;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return provider;
    case 1: return result;
    case 2: return merchantSn;
    case 3: return feeRate;
    case 4: return taskId;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: provider = (java.lang.CharSequence)value$; break;
    case 1: result = (java.lang.Boolean)value$; break;
    case 2: merchantSn = (java.lang.CharSequence)value$; break;
    case 3: feeRate = (java.lang.CharSequence)value$; break;
    case 4: taskId = (java.lang.Long)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'provider' field.
   * @return The value of the 'provider' field.
   */
  public java.lang.CharSequence getProvider() {
    return provider;
  }

  /**
   * Sets the value of the 'provider' field.
   * @param value the value to set.
   */
  public void setProvider(java.lang.CharSequence value) {
    this.provider = value;
  }

  /**
   * Gets the value of the 'result' field.
   * @return The value of the 'result' field.
   */
  public java.lang.Boolean getResult() {
    return result;
  }

  /**
   * Sets the value of the 'result' field.
   * @param value the value to set.
   */
  public void setResult(java.lang.Boolean value) {
    this.result = value;
  }

  /**
   * Gets the value of the 'merchantSn' field.
   * @return The value of the 'merchantSn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchantSn;
  }

  /**
   * Sets the value of the 'merchantSn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchantSn = value;
  }

  /**
   * Gets the value of the 'feeRate' field.
   * @return The value of the 'feeRate' field.
   */
  public java.lang.CharSequence getFeeRate() {
    return feeRate;
  }

  /**
   * Sets the value of the 'feeRate' field.
   * @param value the value to set.
   */
  public void setFeeRate(java.lang.CharSequence value) {
    this.feeRate = value;
  }

  /**
   * Gets the value of the 'taskId' field.
   * @return The value of the 'taskId' field.
   */
  public java.lang.Long getTaskId() {
    return taskId;
  }

  /**
   * Sets the value of the 'taskId' field.
   * @param value the value to set.
   */
  public void setTaskId(java.lang.Long value) {
    this.taskId = value;
  }

  /**
   * Creates a new FeeRateResult RecordBuilder.
   * @return A new FeeRateResult RecordBuilder
   */
  public static com.wosai.upay.job.avro.FeeRateResult.Builder newBuilder() {
    return new com.wosai.upay.job.avro.FeeRateResult.Builder();
  }

  /**
   * Creates a new FeeRateResult RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new FeeRateResult RecordBuilder
   */
  public static com.wosai.upay.job.avro.FeeRateResult.Builder newBuilder(com.wosai.upay.job.avro.FeeRateResult.Builder other) {
    return new com.wosai.upay.job.avro.FeeRateResult.Builder(other);
  }

  /**
   * Creates a new FeeRateResult RecordBuilder by copying an existing FeeRateResult instance.
   * @param other The existing instance to copy.
   * @return A new FeeRateResult RecordBuilder
   */
  public static com.wosai.upay.job.avro.FeeRateResult.Builder newBuilder(com.wosai.upay.job.avro.FeeRateResult other) {
    return new com.wosai.upay.job.avro.FeeRateResult.Builder(other);
  }

  /**
   * RecordBuilder for FeeRateResult instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<FeeRateResult>
    implements org.apache.avro.data.RecordBuilder<FeeRateResult> {

    private java.lang.CharSequence provider;
    private boolean result;
    private java.lang.CharSequence merchantSn;
    private java.lang.CharSequence feeRate;
    private java.lang.Long taskId;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.job.avro.FeeRateResult.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.provider)) {
        this.provider = data().deepCopy(fields()[0].schema(), other.provider);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.result)) {
        this.result = data().deepCopy(fields()[1].schema(), other.result);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.merchantSn)) {
        this.merchantSn = data().deepCopy(fields()[2].schema(), other.merchantSn);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.feeRate)) {
        this.feeRate = data().deepCopy(fields()[3].schema(), other.feeRate);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.taskId)) {
        this.taskId = data().deepCopy(fields()[4].schema(), other.taskId);
        fieldSetFlags()[4] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing FeeRateResult instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.job.avro.FeeRateResult other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.provider)) {
        this.provider = data().deepCopy(fields()[0].schema(), other.provider);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.result)) {
        this.result = data().deepCopy(fields()[1].schema(), other.result);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.merchantSn)) {
        this.merchantSn = data().deepCopy(fields()[2].schema(), other.merchantSn);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.feeRate)) {
        this.feeRate = data().deepCopy(fields()[3].schema(), other.feeRate);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.taskId)) {
        this.taskId = data().deepCopy(fields()[4].schema(), other.taskId);
        fieldSetFlags()[4] = true;
      }
    }

    /**
      * Gets the value of the 'provider' field.
      * @return The value.
      */
    public java.lang.CharSequence getProvider() {
      return provider;
    }

    /**
      * Sets the value of the 'provider' field.
      * @param value The value of 'provider'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.FeeRateResult.Builder setProvider(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.provider = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'provider' field has been set.
      * @return True if the 'provider' field has been set, false otherwise.
      */
    public boolean hasProvider() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'provider' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.FeeRateResult.Builder clearProvider() {
      provider = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'result' field.
      * @return The value.
      */
    public java.lang.Boolean getResult() {
      return result;
    }

    /**
      * Sets the value of the 'result' field.
      * @param value The value of 'result'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.FeeRateResult.Builder setResult(boolean value) {
      validate(fields()[1], value);
      this.result = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'result' field has been set.
      * @return True if the 'result' field has been set, false otherwise.
      */
    public boolean hasResult() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'result' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.FeeRateResult.Builder clearResult() {
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchantSn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchantSn;
    }

    /**
      * Sets the value of the 'merchantSn' field.
      * @param value The value of 'merchantSn'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.FeeRateResult.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.merchantSn = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'merchantSn' field has been set.
      * @return True if the 'merchantSn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'merchantSn' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.FeeRateResult.Builder clearMerchantSn() {
      merchantSn = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'feeRate' field.
      * @return The value.
      */
    public java.lang.CharSequence getFeeRate() {
      return feeRate;
    }

    /**
      * Sets the value of the 'feeRate' field.
      * @param value The value of 'feeRate'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.FeeRateResult.Builder setFeeRate(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.feeRate = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'feeRate' field has been set.
      * @return True if the 'feeRate' field has been set, false otherwise.
      */
    public boolean hasFeeRate() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'feeRate' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.FeeRateResult.Builder clearFeeRate() {
      feeRate = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'taskId' field.
      * @return The value.
      */
    public java.lang.Long getTaskId() {
      return taskId;
    }

    /**
      * Sets the value of the 'taskId' field.
      * @param value The value of 'taskId'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.FeeRateResult.Builder setTaskId(java.lang.Long value) {
      validate(fields()[4], value);
      this.taskId = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'taskId' field has been set.
      * @return True if the 'taskId' field has been set, false otherwise.
      */
    public boolean hasTaskId() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'taskId' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.FeeRateResult.Builder clearTaskId() {
      taskId = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public FeeRateResult build() {
      try {
        FeeRateResult record = new FeeRateResult();
        record.provider = fieldSetFlags()[0] ? this.provider : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.result = fieldSetFlags()[1] ? this.result : (java.lang.Boolean) defaultValue(fields()[1]);
        record.merchantSn = fieldSetFlags()[2] ? this.merchantSn : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.feeRate = fieldSetFlags()[3] ? this.feeRate : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.taskId = fieldSetFlags()[4] ? this.taskId : (java.lang.Long) defaultValue(fields()[4]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<FeeRateResult>
    WRITER$ = (org.apache.avro.io.DatumWriter<FeeRateResult>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<FeeRateResult>
    READER$ = (org.apache.avro.io.DatumReader<FeeRateResult>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
