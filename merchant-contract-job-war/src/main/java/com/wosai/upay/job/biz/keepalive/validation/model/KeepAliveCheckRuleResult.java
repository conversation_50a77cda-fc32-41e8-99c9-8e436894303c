package com.wosai.upay.job.biz.keepalive.validation.model;

import lombok.Getter;

import java.util.Map;

/**
 * 规则执行结果
 */
@Getter
public class KeepAliveCheckRuleResult {
    
    /**
     * 是否通过
     */
    private boolean passed;
    
    /**
     * 规则类型
     */
    private String ruleType;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 详细信息
     */
    private Map<String, Object> details;
    
    public KeepAliveCheckRuleResult() {}
    
    public KeepAliveCheckRuleResult(boolean passed, String ruleType, String message) {
        this.passed = passed;
        this.ruleType = ruleType;
        this.message = message;
    }
    
    public KeepAliveCheckRuleResult(boolean passed, String ruleType, String message, String errorCode) {
        this.passed = passed;
        this.ruleType = ruleType;
        this.message = message;
        this.errorCode = errorCode;
    }
    
    public static KeepAliveCheckRuleResult success(String ruleType, String message) {
        return new KeepAliveCheckRuleResult(true, ruleType, message);
    }
    
    public static KeepAliveCheckRuleResult failure(String ruleType, String message, String errorCode) {
        return new KeepAliveCheckRuleResult(false, ruleType, message, errorCode);
    }

    public void setPassed(boolean passed) {
        this.passed = passed;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public void setDetails(Map<String, Object> details) {
        this.details = details;
    }
    
    @Override
    public String toString() {
        return "RuleResult{" +
                "passed=" + passed +
                ", ruleType='" + ruleType + '\'' +
                ", message='" + message + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", details=" + details +
                '}';
    }
}