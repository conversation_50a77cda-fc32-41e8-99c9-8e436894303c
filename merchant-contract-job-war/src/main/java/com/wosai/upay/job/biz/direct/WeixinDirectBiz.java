package com.wosai.upay.job.biz.direct;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.enums.TradeComboStatusEnum;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.enume.WeixinDirectApplyStatus;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.WeixinDirectApplyMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.WeixinDirectApply;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.model.direct.*;
import com.wosai.upay.job.model.directparams.WeixinDirectParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.weixin.DirectApplymentStatusResp;
import com.wosai.upay.merchant.contract.service.WeiXinDirectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.wosai.upay.core.model.MerchantConfig.*;

/**
 * <AUTHOR>
 * @date 2020/12/18
 */
@Slf4j
@Component
public class WeixinDirectBiz {

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    @Lazy
    private MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private TradeComboDetailService comboDetailService;

    @Autowired
    private FeeRateService feeRateService;

    @Autowired
    private WeiXinDirectService weiXinDirectService;

    @Autowired
    private ParamContextBiz paramContextBiz;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private WeixinDirectApplyMapper weixinDirectApplyMapper;

    @Autowired
    private DirectStatusBiz directStatusBiz;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private ErrorCodeManageBiz errorCodeManageBiz;

    @Value("${weixin.direct.online}")
    private String weixinDirectOnline;

    @Value("${weixin.direct.offline}")
    private String weixinDirectOffline;

    /**
     * APPLYMENT_STATE_EDITTING 编辑中：提交申请发生错误导致，请尝试重新提交
     * <p>
     * APPLYMENT_STATE_AUDITING 审核中：申请单正在审核中，超级管理员用微信打开“签约链接”，完成绑定微信号后，申请单进度将通过微信公众号通知超级管理员，引导完成后续步骤。
     * <p>
     * APPLYMENT_STATE_REJECTED 已驳回：请按照驳回原因修改申请资料，超级管理员用微信打开“签约链接”，完成绑定微信号，后续申请单进度将通过微信公众号通知超级管理员。
     * <p>
     * APPLYMENT_STATE_TO_BE_CONFIRMED 待账户验证：请超级管理员使用微信打开返回的“签约链接”，根据页面指引完成账户验证。
     * <p>
     * APPLYMENT_STATE_TO_BE_SIGNED 待签约：请超级管理员使用微信打开返回的“签约链接”，根据页面指引完成签约。
     * <p>
     * APPLYMENT_STATE_SIGNING 开通权限中：系统开通相关权限中，请耐心等待。
     * <p>
     * APPLYMENT_STATE_FINISHED 已完成：商户入驻申请已完成。
     * <p>
     * APPLYMENT_STATE_CANCELED 已作废：申请单已被撤销。
     */
    public static final Map<String, WeixinDirectApplyStatus> WEIXIN_STATUS_MAPPING = CollectionUtil.hashMap(
            "APPLYMENT_STATE_EDITTING", WeixinDirectApplyStatus.APPLY_REJECTED,
            "APPLYMENT_STATE_AUDITING", WeixinDirectApplyStatus.IN_WEIXIN_AUDITING,
            "APPLYMENT_STATE_REJECTED", WeixinDirectApplyStatus.APPLY_REJECTED,
            "APPLYMENT_STATE_TO_BE_CONFIRMED", WeixinDirectApplyStatus.WAIT_FOR_VERIFY,
            "APPLYMENT_STATE_TO_BE_SIGNED", WeixinDirectApplyStatus.WAIT_FOR_SIGN,
            "APPLYMENT_STATE_SIGNING", WeixinDirectApplyStatus.IN_OPENING_PERMISSION,
            "APPLYMENT_STATE_FINISHED", WeixinDirectApplyStatus.APPLY_ACCEPTED,
            "APPLYMENT_STATE_CANCELED", WeixinDirectApplyStatus.APPLY_REJECTED
    );
    /**
     * crm提交
     */
    public static final Integer CRM_APPLY = 1;
    /**
     * 人工
     */
    public static final Integer MANUAL_APPLY = 2;

    /**
     * 提交申请之前进行校验
     * TODO 这里和支付宝的有问题，如果已经有申请单，可以报错但是不生成失败的task，直接返回审核中
     *
     * @param merchantSn 商户号
     * @param devCode    应用标识
     */
    public void preCheck(String merchantSn, String devCode) {
        //1 获取对应的contract_task类型
        String contractTaskType = getTaskTypeByDevCode(devCode);
        if (WosaiStringUtils.isEmpty(contractTaskType)) {
            throw new CommonInvalidParameterException("该应用不可以开通微信直连");
        }
        //2 是否存在审核中或审核成功的contract_task
        ContractTask contractTask = contractTaskMapper.selectProcessTaskByMerchantSn(merchantSn, contractTaskType);
        if (contractTask != null) {
            throw new CommonInvalidParameterException("存在未结束的直连申请任务");
        }
        //3 是否存在审核中的申请单
        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectProcessApplyByMerchantSn(merchantSn, devCode);
        if (weixinDirectApply != null) {
            throw new CommonInvalidParameterException("存在未完成的申请单");
        }
        //4 是否以已经申请成功
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode(merchantSn, devCode);
        if (directStatus != null && directStatus.getStatus() == DirectStatus.STATUS_SUCCESS) {
            throw new CommonInvalidParameterException("该商户已开通成功");
        }

    }

    /**
     * 构建上下文信息
     *
     * @param weixinDirectReq 参数
     * @return 上下文信息
     * @throws ContextParamException 构建异常
     */
    public Map<String, Object> buildWeixinParamContext(WeixinDirectReq weixinDirectReq) throws ContextParamException {
        // 微信直连线上不需要门店照片
        if (weixinDirectOnline.equals(weixinDirectReq.getDev_code())) {
            return paramContextBiz.buildWeixinParamContext(weixinDirectReq, false);
        }
        return paramContextBiz.buildWeixinParamContext(weixinDirectReq, true);
    }

    /**
     * 创建微信直连报备任务、微信直连申请单、微信报备状态
     *
     * @param weixinDirectReq 请求参数
     * @param paramContext    上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void createTaskAndApplyAndStatus(WeixinDirectReq weixinDirectReq, Map paramContext) {
        ContractTask contractTask = new ContractTask().setMerchant_sn(weixinDirectReq.getMerchant_sn()).setMerchant_name(BeanUtil.getPropString(paramContext.get(ParamContextBiz.KEY_MERCHANT), Merchant.NAME))
                .setAffect_sub_task_count(0).setAffect_status_success_task_count(0).setEvent_context(JSON.toJSONString(paramContext)).setType(getTaskTypeByDevCode(weixinDirectReq.getDev_code()));
        //可以发送神策消息
        contractTaskBiz.insert(contractTask);
        WeixinDirectApply weixinDirectApply = new WeixinDirectApply().setMerchant_sn(weixinDirectReq.getMerchant_sn()).setDev_code(weixinDirectReq.getDev_code())
                .setTask_id(contractTask.getId()).setForm_body(JSON.toJSONString(weixinDirectReq)).setSubmit_type(CRM_APPLY);
        weixinDirectApplyMapper.insertSelective(weixinDirectApply);
        directStatusBiz.createOrUpdateDirectStatus(weixinDirectReq.getMerchant_sn(), weixinDirectReq.getDev_code(), DirectStatus.STATUS_PENDING, null);
    }

    /**
     * 根据dev_code获取报备任务类型
     *
     * @param devCode 应用类型
     * @return 报备任务类型
     */
    private String getTaskTypeByDevCode(String devCode) {
        String contractTaskType = "";
        if (weixinDirectOnline.equals(devCode)) {
            contractTaskType = ProviderUtil.WEIXIN_DIRECT_ONLINE;
        }
        if (weixinDirectOffline.equals(devCode)) {
            contractTaskType = ProviderUtil.WEIXIN_DIRECT_OFFLINE;
        }
        return contractTaskType;
    }

    /**
     * 根据最新的申请单获取最新的状态
     *
     * @param merchantSn
     * @param devCode
     * @return
     */
    public ApplyStatusResp getLatestStatusFromWeixin(String merchantSn, String devCode, String platform) {
        //1 获取最新的申请单
        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn(merchantSn, devCode);
        //TODO 查询微信对应申请单的状态
        DirectApplymentStatusResp statusResp = weiXinDirectService.queryApplyStatus(this.getBusinessCode(weixinDirectApply));
        String weixinStatus = statusResp.getApplyment_state();
        WeixinDirectApplyStatus applyStatus = WEIXIN_STATUS_MAPPING.get(weixinStatus);
        if (applyStatus == null) {
            throw new CommonInvalidParameterException("查询微信返回未知状态");
        }
        //状态未更改
        if (applyStatus.getVal().equals(weixinDirectApply.getStatus())) {
            return getWeixinDirectContractMemo(contractTaskMapper.selectByPrimaryKey(weixinDirectApply.getTask_id()), platform);
        }
        //状态发生了变化 更改状态 返回新的状态和文案
        doAfterStatusChanged(statusResp, applyStatus, weixinDirectApply);
        return getWeixinDirectContractMemo(contractTaskMapper.selectByPrimaryKey(weixinDirectApply.getTask_id()), platform);
    }

    public String getBusinessCode(WeixinDirectApply apply) {
        Map requestBody = JSON.parseObject(apply.getRequest_body(), Map.class);
        return BeanUtil.getPropString(requestBody, "business_code");
    }


    /**
     * 查询后申请单的状态和之前发生了变化
     *
     * @param statusResp        查询微信状态返回值
     * @param applyStatus       内部状态
     * @param weixinDirectApply 申请单
     */
    @Transactional(rollbackFor = Exception.class)
    public void doAfterStatusChanged(DirectApplymentStatusResp statusResp, WeixinDirectApplyStatus applyStatus, WeixinDirectApply weixinDirectApply) {
        WeixinDirectApply updateApply = new WeixinDirectApply().setId(weixinDirectApply.getId());
        ContractTask contractTask = new ContractTask().setId(weixinDirectApply.getTask_id());
        //失败
        if (applyStatus.getVal().equals(WeixinDirectApplyStatus.APPLY_REJECTED.getVal())) {
            String rejectReason = this.getRejectReason(statusResp);
            updateApply.setStatus(applyStatus.getVal()).setResult(rejectReason).setResponse_body(JSON.toJSONString(statusResp));
            contractTask.setStatus(TaskStatus.FAIL.getVal()).setResult(JSON.toJSONString(CollectionUtil.hashMap("channel", ProviderUtil.WEIXIN_DIRECT, "message", rejectReason)));
            weixinDirectApplyMapper.updateByPrimaryKeySelective(updateApply);
            contractTaskBiz.update(contractTask);
            directStatusBiz.createOrUpdateDirectStatus(weixinDirectApply.getMerchant_sn(), weixinDirectApply.getDev_code(), DirectStatus.STATUS_BIZ_FAIL, rejectReason);
        }
        //成功
        if (applyStatus.getVal().equals(WeixinDirectApplyStatus.APPLY_ACCEPTED.getVal())) {
            updateApply.setStatus(applyStatus.getVal()).setResponse_body(JSON.toJSONString(statusResp));
            contractTask.setStatus(TaskStatus.SUCCESS.getVal());
            WeixinDirectReq weixinDirectReq = JSON.parseObject(weixinDirectApply.getForm_body(), WeixinDirectReq.class);
            merchantProviderParamsService.addWeixinDirectParams(this.addWeixinDirectParams(weixinDirectApply, weixinDirectReq, statusResp.getSub_mchid()));
            weixinDirectApplyMapper.updateByPrimaryKeySelective(updateApply);
            contractTaskBiz.update(contractTask);
            directStatusBiz.createOrUpdateDirectStatus(weixinDirectApply.getMerchant_sn(), weixinDirectApply.getDev_code(), DirectStatus.STATUS_SUCCESS, null);
            applyFeeRate(weixinDirectReq);
        }
        //账户验证中
        if (applyStatus.getVal().equals(WeixinDirectApplyStatus.WAIT_FOR_VERIFY.getVal())) {
            updateApply.setStatus(applyStatus.getVal()).setSign_url(statusResp.getSign_url()).setQrcode_data(statusResp.getSign_url());
            weixinDirectApplyMapper.updateByPrimaryKeySelective(updateApply);
            directStatusBiz.sendStatusChangeMessage(weixinDirectApply.getMerchant_sn(), weixinDirectApply.getDev_code(), DirectStatus.STATUS_WAIT_FOR_VERIFY, null);
        }
        //签约中
        if (applyStatus.getVal().equals(WeixinDirectApplyStatus.WAIT_FOR_SIGN.getVal())) {
            updateApply.setStatus(applyStatus.getVal()).setSign_url(statusResp.getSign_url()).setQrcode_data(statusResp.getSign_url());
            weixinDirectApplyMapper.updateByPrimaryKeySelective(updateApply);
            directStatusBiz.sendStatusChangeMessage(weixinDirectApply.getMerchant_sn(), weixinDirectApply.getDev_code(), DirectStatus.STATUS_WAIT_FOR_SIGN, null);
        }
        //开通权限中
        if (applyStatus.getVal().equals(WeixinDirectApplyStatus.IN_OPENING_PERMISSION.getVal())) {
            updateApply.setStatus(applyStatus.getVal());
            weixinDirectApplyMapper.updateByPrimaryKeySelective(updateApply);
            directStatusBiz.sendStatusChangeMessage(weixinDirectApply.getMerchant_sn(), weixinDirectApply.getDev_code(), DirectStatus.STATUS_IN_OPENING_PERMISSION, null);
        }
    }

    private void applyFeeRate(WeixinDirectReq weixinDirectReq) {
        String merchantSn = weixinDirectReq.getMerchant_sn();
        long comboId = weixinDirectReq.getApp_info().getTrade_combo_id();
        ApplyFeeRateRequest feeRate = new ApplyFeeRateRequest()
                .setMerchantSn(merchantSn)
                .setAuditSn(merchantSn)
                .setTradeComboId(comboId);

        Map<String, String> applyFeeRateMap = new HashMap<>(1);
        //优先判断借贷分离情况
        if (WosaiStringUtils.isNotEmpty(weixinDirectReq.getApp_info().getCredit_activities_rate())) {
            Map weiXinConfig = weixinDirectReq.getApp_info().getMerchant_config().get(0);
            applyFeeRateMap = handleChannelFeeRate(weiXinConfig);
        } else {
            applyFeeRateMap.put(String.valueOf(PaywayEnum.WEIXIN.getValue()), weixinDirectReq.getApp_info().getFee_rate());
        }

        feeRate.setApplyFeeRateMap(applyFeeRateMap);
        try {
            feeRateService.applyFeeRateOne(feeRate);
        } catch (Exception e) {
            log.error("开通微信直连成功,设置套餐失败,商户号:{}", merchantSn, e);
        }
    }

    private Map handleChannelFeeRate(Map weiXinConfig) {
        Map applyFeeRateMap = new HashMap<>();

        //渠道费率
        List<Map> ladderReq = (List<Map>) weiXinConfig.get("channel");
        List<Map> value = new ArrayList<>();
        for (Map map : ladderReq) {
            String type = MapUtils.getString(map, "type");
            String rate = MapUtils.getString(map, "rate");
            value.add(CollectionUtil.hashMap("type", type, "fee_rate", rate));
        }
        applyFeeRateMap.put(BeanUtil.getPropString(weiXinConfig, "payway"), JSON.toJSONString(CollectionUtil.hashMap("fee_rate_type", "channel", "value", value)));

        return applyFeeRateMap;
    }

    /**
     * 获取失败的原因
     *
     * @param statusResp 查询微信的返回值
     * @return 失败原因
     */
    private String getRejectReason(DirectApplymentStatusResp statusResp) {
        if ("APPLYMENT_STATE_EDITTING".equals(statusResp.getApplyment_state())) {
            return "提交申请发生错误导致，请尝试重新提交";
        }
        if ("APPLYMENT_STATE_CANCELED".equals(statusResp.getApplyment_state())) {
            return "申请单已撤销";
        }
        List<DirectApplymentStatusResp.RejectedReason> reasons = statusResp.getAudit_detail();
        StringBuilder stringBuilder = new StringBuilder();
        for (DirectApplymentStatusResp.RejectedReason reason : reasons) {
            stringBuilder.append(reason.getField()).append(reason.getReject_reason());
        }
        return stringBuilder.toString();
    }

    public ApplyStatusResp getWeixinDirectContractMemo(String merchantSn, String devCode, String platform) {
        ContractTask contractTask = contractTaskMapper.getBySnAndType(merchantSn, getTaskTypeByDevCode(devCode));
        return this.getWeixinDirectContractMemo(contractTask, platform);
    }

    /**
     * 获取审核中申请单的文案
     *
     * @param contractTask 进件任务
     * @param platform     查询平台
     * @return 开通文案
     */
    public ApplyStatusResp getWeixinDirectContractMemo(ContractTask contractTask, String platform) {
        if (contractTask == null) {
            return new ApplyStatusResp()
                    .setContract_code(WeixinDirectStatusCode.NO_TASK.getCode())
                    .setContract_memo(WeixinDirectStatusCode.NO_TASK.getMsg())
                    .setStatus(DirectStatus.STATUS_PROCESS);
        }
        if (contractTask.getStatus().equals(TaskStatus.PENDING.getVal())) {
            return new ApplyStatusResp()
                    .setContract_code(WeixinDirectStatusCode.PENDING_TASK.getCode())
                    .setContract_memo(WeixinDirectStatusCode.PENDING_TASK.getMsg())
                    .setStatus(DirectStatus.STATUS_PROCESS);
        }
        //审核中
        if (contractTask.getStatus().equals(TaskStatus.PROGRESSING.getVal()) || contractTask.getStatus().equals(TaskStatus.PAY_FOR_WAIT.getVal())) {
            ApplyStatusResp resp = getProcessMemoByApply(contractTask, platform);
            resp.setStatus(DirectStatus.STATUS_PROCESS);
            return resp;
        } else if (TaskStatus.FAIL.getVal().equals(contractTask.getStatus())) {
            ApplyStatusResp resp = getFailMemo(contractTask, platform);
            resp.setStatus(DirectStatus.STATUS_BIZ_FAIL);
            return resp;
        } else {
            return new ApplyStatusResp()
                    .setContract_code(WeixinDirectStatusCode.WEIXIN_TASK_SUCCESS.getCode())
                    .setContract_memo(WeixinDirectStatusCode.WEIXIN_TASK_SUCCESS.getMsg())
                    .setStatus(DirectStatus.STATUS_SUCCESS);
        }
    }

    private ApplyStatusResp getFailMemo(ContractTask contractTask, String platform) {
        Map result = JSON.parseObject(contractTask.getResult(), Map.class);
        ApplyStatusResp applyStatusResp = new ApplyStatusResp();
        if (CollectionUtils.isEmpty(result)) {
            applyStatusResp.setContract_code(WeixinDirectStatusCode.UNKNOWN_CODE.getCode());
            applyStatusResp.setContract_memo(WeixinDirectStatusCode.UNKNOWN_CODE.getMsg());
            return applyStatusResp;
        }
        String channel = BeanUtil.getPropString(result, "channel");
        String message = BeanUtil.getPropString(result, "message");
        message = WosaiStringUtils.isNotEmpty(message) ? message : "未知失败原因";
        if (ProviderUtil.SHOUQIANBA_CHANNEL.equals(channel)) {
            applyStatusResp.setContract_code(WeixinDirectStatusCode.UNKNOWN_CODE.getCode());
            applyStatusResp.setContract_memo(message);
            return applyStatusResp;
        }
        String type = platform + "_msg";
        final ErrorInfo errorInfo = errorCodeManageBiz.getPromptMessageFromErrorCodeManager(type, message, ErrorCodeManageBiz.PLATFORM_WEIXIN_DIRECT);
        applyStatusResp.setContract_code(errorInfo.getCode());
        applyStatusResp.setContract_memo(errorInfo.getMsg());
        return applyStatusResp;
    }

    private ApplyStatusResp getProcessMemoByApply(ContractTask contractTask, String platform) {
        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectApplyByTaskId(contractTask.getId());
        ApplyStatusResp applyStatusResp = new ApplyStatusResp();
        //审核中的需要转译
        WeixinDirectApplyStatus applyStatus = WeixinDirectApplyStatus.fromValuetoStatus(weixinDirectApply.getStatus());
        //给个默认的，防止后续没有转译文案
        applyStatusResp.setContract_code(WeixinDirectStatusCode.WEIXIN_AUDITING.getCode()).setContract_memo(WeixinDirectStatusCode.WEIXIN_AUDITING.getMsg());
        if (applyStatus.getVal().equals(WeixinDirectApplyStatus.WAIT_FOR_SIGN.getVal()) || applyStatus.getVal().equals(WeixinDirectApplyStatus.WAIT_FOR_VERIFY.getVal())) {
            applyStatusResp.setQrcode(weixinDirectApply.getQrcode_data());
        }
        Map processingContractMap = applicationApolloConfig.getProcessingDirectMessage();
        List<Map> weixinMessages = (List<Map>) processingContractMap.get(ProviderUtil.WEIXIN_DIRECT);
        if (WosaiCollectionUtils.isEmpty(weixinMessages)) {
            return applyStatusResp;
        }
        for (Map config : weixinMessages) {
            String errorMsg = BeanUtil.getPropString(config, "error_msg");
            if (errorMsg.contains(applyStatus.getContext())) {
                applyStatusResp.setContract_code(BeanUtil.getPropString(config, "error_code")).setContract_memo(BeanUtil.getPropString(config, platform + "_msg"));
            }
        }
        return applyStatusResp;

    }

    /**
     * 申请转人工 失败次数三次以上
     * 创建对应的task、apply和更新direct_status
     *
     * @param weixinDirectReq 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void transferToManual(WeixinDirectReq weixinDirectReq) {
        ContractResponse contractResponse = this.allowApplyToManual(weixinDirectReq.getMerchant_sn(), weixinDirectReq.getDev_code());
        if (!contractResponse.isSuccess()) {
            throw new CommonPubBizException(contractResponse.getMsg());
        }
        // 创建在运营审核中的人工申请单 TODO 需要task吗 先创建一个 需要发消息到钉钉
        Map<String, Object> paramContext = this.buildWeixinParamContext(weixinDirectReq);
        ContractTask contractTask = new ContractTask().setMerchant_sn(weixinDirectReq.getMerchant_sn()).setMerchant_name(BeanUtil.getPropString(paramContext.get(ParamContextBiz.KEY_MERCHANT), Merchant.NAME))
                .setStatus(TaskStatus.PROGRESSING.getVal()).setAffect_sub_task_count(0).setAffect_status_success_task_count(0).setEvent_context(JSON.toJSONString(paramContext)).setType(getTaskTypeByDevCode(weixinDirectReq.getDev_code()));
        contractTaskBiz.insert(contractTask);
        WeixinDirectApply weixinDirectApply = new WeixinDirectApply().setMerchant_sn(weixinDirectReq.getMerchant_sn()).setDev_code(weixinDirectReq.getDev_code()).setSubmit_type(MANUAL_APPLY)
                .setStatus(WeixinDirectApplyStatus.IN_SP_AUDITING.getVal()).setTask_id(contractTask.getId()).setForm_body(JSON.toJSONString(weixinDirectReq));
        weixinDirectApplyMapper.insertSelective(weixinDirectApply);
        directStatusBiz.createOrUpdateDirectStatus(weixinDirectReq.getMerchant_sn(), weixinDirectReq.getDev_code(), DirectStatus.STATUS_PROCESS, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeWeixinDirectApplyStatus(ChangeApplyStatusReq req) {
        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn(req.getMerchantSn(), req.getDevCode());
        if (weixinDirectApply == null || !weixinDirectApply.getSubmit_type().equals(MANUAL_APPLY)) {
            throw new CommonPubBizException("申请单不存在或该申请单不处于人工审核中");
        }
        if (req.getStatus().equals(WeixinDirectApplyStatus.APPLY_ACCEPTED.getVal())) {
            //人工审核变为成功
            if (weixinDirectApply.getSubmit_type().equals(MANUAL_APPLY)) {
                if (WosaiStringUtils.isEmpty(req.getSubMerchantSn())) {
                    throw new CommonPubBizException("微信子商户号不能为空");
                }
                WeixinDirectReq weixinDirectReq = JSON.parseObject(weixinDirectApply.getForm_body(), WeixinDirectReq.class);

                WeixinDirectParams weixinDirectParams = this.addWeixinDirectParams(weixinDirectApply, weixinDirectReq, req.getSubMerchantSn());
                merchantProviderParamsService.addWeixinDirectParams(weixinDirectParams);
                weixinDirectApplyMapper.updateStatus(weixinDirectApply.getId(), req.getStatus(), null);
                contractTaskBiz.update(new ContractTask().setId(weixinDirectApply.getTask_id()).setStatus(TaskStatus.SUCCESS.getVal()));
                directStatusBiz.createOrUpdateDirectStatus(req.getMerchantSn(), req.getDevCode(), DirectStatus.STATUS_SUCCESS, null);
                applyFeeRate(weixinDirectReq);
            }
        } else if (req.getStatus().equals(WeixinDirectApplyStatus.APPLY_REJECTED.getVal())) {
            if (WosaiStringUtils.isEmpty(req.getMessage())) {
                throw new CommonPubBizException("驳回原因不能为空");
            }
            weixinDirectApplyMapper.updateStatus(weixinDirectApply.getId(), WeixinDirectApplyStatus.APPLY_REJECTED.getVal(), req.getMessage());
            contractTaskBiz.update(new ContractTask().setId(weixinDirectApply.getTask_id()).setStatus(TaskStatus.FAIL.getVal())
                    .setResult(JSON.toJSONString(CollectionUtil.hashMap("channel", ProviderUtil.SHOUQIANBA_CHANNEL, "message", req.getMessage()))));
            directStatusBiz.createOrUpdateDirectStatus(req.getMerchantSn(), req.getDevCode(), DirectStatus.STATUS_BIZ_FAIL, null);
        } else {
            weixinDirectApplyMapper.updateStatus(weixinDirectApply.getId(), req.getStatus(), null);
            // 这里还是分下状态吧
            directStatusBiz.createOrUpdateDirectStatus(req.getMerchantSn(), req.getDevCode(), getStatus(req.getStatus()), null);
        }
    }

    private int getStatus(Integer status) {
        if (WeixinDirectApplyStatus.WAIT_FOR_VERIFY.getVal().equals(status)) {
            return DirectStatus.STATUS_WAIT_FOR_VERIFY;
        }
        if (WeixinDirectApplyStatus.WAIT_FOR_SIGN.getVal().equals(status)) {
            return DirectStatus.STATUS_WAIT_FOR_SIGN;
        }
        if (WeixinDirectApplyStatus.IN_OPENING_PERMISSION.getVal().equals(status)) {
            return DirectStatus.STATUS_IN_OPENING_PERMISSION;
        }
        return DirectStatus.STATUS_PROCESS;
    }

    public WeixinDirectParams addWeixinDirectParams(WeixinDirectApply weixinDirectApply, WeixinDirectReq weixinDirectReq, String subMerchantSn) {
        String appId = weixinDirectReq.getApp_info().getApp_id();
        String feeRate = weixinDirectReq.getApp_info().getFee_rate();
        //获取套餐对应的支付参数status信息
        String merchantId = merchantService.getMerchantBySn(weixinDirectApply.getMerchant_sn(), null).getId();
        // 获取套餐信息
        List<TradeComboDetailResult> comboDetail = comboDetailService.listByComboId(weixinDirectReq.getApp_info().getTrade_combo_id());
        Optional<TradeComboDetailResult> detailResult = comboDetail.stream().filter(r -> PaywayEnum.WEIXIN.getValue().equals(r.getPayway())).findFirst();
        if (!detailResult.isPresent()) {
            log.error("商户号:{} 该套餐不存在微信费率:{}", weixinDirectApply.getMerchant_sn(), weixinDirectReq.getApp_info().getTrade_combo_id());
            throw new CommonPubBizException(weixinDirectReq.getApp_info().getTrade_combo_id() + "套餐不存在微信费率");
        }
        TradeComboDetailResult tradeComboDetailResult = detailResult.get();

        WeixinDirectParams weixinDirectParams = new WeixinDirectParams();

        //WAP 支付参数
        if (tradeComboDetailResult.getWapStatus() == TradeComboStatusEnum.ENABLE.getCode()) {
            WeixinDirectParams.WeixinWapTradeParams weixinWapTradeParams = new WeixinDirectParams.WeixinWapTradeParams();
            weixinWapTradeParams.setWeixin_sub_mch_id(subMerchantSn);
            weixinWapTradeParams.setWeixin_sub_appid(appId);
            weixinWapTradeParams.setFee_rate(feeRate);
            weixinDirectParams.setWeixin_wap_trade_params(weixinWapTradeParams);
        }

        //BSC CSB 支付参数
        if (tradeComboDetailResult.getB2cStatus() == TradeComboStatusEnum.ENABLE.getCode()) {
            WeixinDirectParams.WeixinTradeParams weixinTradeParams = new WeixinDirectParams.WeixinTradeParams();
            weixinTradeParams.setWeixin_sub_mch_id(subMerchantSn);
            weixinTradeParams.setWeixin_sub_appid(appId);
            weixinTradeParams.setFee_rate(feeRate);
            weixinDirectParams.setWeixin_trade_params(weixinTradeParams);
        }

        //小程序 支付参数
        if (tradeComboDetailResult.getMiniStatus() == TradeComboStatusEnum.ENABLE.getCode()) {
            WeixinDirectParams.WeixinTradeParams weixinMiniTradeParams = new WeixinDirectParams.WeixinTradeParams();
            weixinMiniTradeParams.setWeixin_sub_mch_id(subMerchantSn);
            weixinMiniTradeParams.setWeixin_sub_appid(appId);
            weixinMiniTradeParams.setFee_rate(feeRate);
            weixinDirectParams.setWeixin_mini_trade_params(weixinMiniTradeParams);
        }

        //h5支付参数
        if (tradeComboDetailResult.getH5Status() == TradeComboStatusEnum.ENABLE.getCode()) {
            WeixinDirectParams.WeixinWapTradeParams weixinH5TradeParams = new WeixinDirectParams.WeixinWapTradeParams();
            weixinH5TradeParams.setWeixin_sub_mch_id(subMerchantSn);
            weixinH5TradeParams.setWeixin_sub_appid(appId);
            weixinH5TradeParams.setFee_rate(feeRate);
            weixinDirectParams.setWeixin_h5_trade_params(weixinH5TradeParams);
        }

        //app支付参数
        if (tradeComboDetailResult.getAppStatus() == TradeComboStatusEnum.ENABLE.getCode()) {
            WeixinDirectParams.WeixinWapTradeParams weixinAPPTradeParams = new WeixinDirectParams.WeixinWapTradeParams();
            weixinAPPTradeParams.setWeixin_sub_mch_id(subMerchantSn);
            weixinAPPTradeParams.setWeixin_sub_appid(appId);
            weixinAPPTradeParams.setFee_rate(feeRate);
            weixinDirectParams.setWeixin_app_trade_params(weixinAPPTradeParams);
        }

        weixinDirectParams.setMerchant_id(merchantId);
        weixinDirectParams.setMerchant_sn(weixinDirectApply.getMerchant_sn());
        return weixinDirectParams;
    }

    /**
     * 判断主体是否一致
     *
     * @param bindMerchantReq 请求参数
     * @return 上线文参数
     */
    public Map checkIsSameSubject(BindMerchantReq bindMerchantReq) {
        //判断主体是否一致 1获取商户id
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(bindMerchantReq.getMerchant_sn(), bindMerchantReq.getDev_code());
        MerchantInfo bindMerchantInfo = merchantService.getMerchantBySn(bindMerchantReq.getBind_merchant_sn(), null);
        if (merchantInfo == null || bindMerchantInfo == null) {
            throw new CommonInvalidParameterException("商户号:" + bindMerchantReq.getMerchant_sn() + "或" + bindMerchantReq.getBind_merchant_sn() + "对应的商户不存在");
        }
        MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantInfo.getId(), bindMerchantReq.getDev_code());
        MerchantBusinessLicenseInfo bindLicenseInfo = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(bindMerchantInfo.getId(), null);
        if (licenseInfo == null || bindLicenseInfo == null) {
            throw new CommonInvalidParameterException("商户号:" + bindMerchantReq.getMerchant_sn() + "或" + bindMerchantReq.getBind_merchant_sn() + "对应的商户营业执照不存在");
        }
        if (!Objects.equals(licenseInfo.getNumber(), bindLicenseInfo.getNumber())) {
            throw new CommonInvalidParameterException("商户号:" + bindMerchantReq.getMerchant_sn() + "和" + bindMerchantReq.getBind_merchant_sn() + "对应的主体不一致");
        }
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode(bindMerchantReq.getBind_merchant_sn(), bindMerchantReq.getDev_code());
        if (directStatus == null || directStatus.getStatus() != DirectStatus.STATUS_SUCCESS) {
            throw new CommonInvalidParameterException("商户号:" + bindMerchantReq.getBind_merchant_sn() + "未开通" + getTaskTypeByDevCode(bindMerchantReq.getDev_code()));
        }
        Map<String, Object> context = new HashMap<String, Object>(3);
        context.put(ParamContextBiz.KEY_MERCHANT, merchantInfo);
        context.put(ParamContextBiz.KEY_BUSINESS_LICENCE, licenseInfo);
        context.put(ParamContextBiz.KEY_APP_INFO, bindMerchantReq);
        return context;
    }

    /**
     * 构建直连交易参数
     *
     * @param merchantSn     商户号
     * @param bindMerchantSn 被绑定的商户的sn
     * @param providerParams 被绑定的商户的交易参数
     * @return
     */
    public WeixinDirectParams buildWeixinDirectParams(String merchantSn, String bindMerchantSn, MerchantProviderParamsDto providerParams) {
        //获取对应商户的交易参数
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(bindMerchantSn, null);
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantInfo.getId(), PaywayEnum.WEIXIN.getValue());
        Map<String, Object> extra = providerParams.getExtra();
        WeixinDirectParams weixinDirectParams = new WeixinDirectParams();

        //WAP 支付参数
        if (BeanUtil.getPropBoolean(merchantConfig, WAP_STATUS) && BeanUtil.getPropBoolean(merchantConfig, WAP_FORMAL)) {
            WeixinDirectParams.WeixinWapTradeParams weixinWapTradeParams = new WeixinDirectParams.WeixinWapTradeParams();
            weixinWapTradeParams.setWeixin_sub_mch_id((String) MapUtils.getMap(extra, TransactionParam.WEIXIN_WAP_TRADE_PARAMS).get(TransactionParam.WEIXIN_SUB_MCH_ID));
            weixinWapTradeParams.setWeixin_sub_appid((String) MapUtils.getMap(extra, TransactionParam.WEIXIN_WAP_TRADE_PARAMS).get(TransactionParam.WEIXIN_SUB_APP_ID));
            weixinWapTradeParams.setFee_rate(BeanUtil.getPropString(merchantConfig, WAP_FEE_RATE));
            weixinDirectParams.setWeixin_wap_trade_params(weixinWapTradeParams);
        }

        //BSC CSB 支付参数
        if (BeanUtil.getPropBoolean(merchantConfig, B2C_STATUS) && BeanUtil.getPropBoolean(merchantConfig, B2C_FORMAL)) {
            WeixinDirectParams.WeixinTradeParams weixinTradeParams = new WeixinDirectParams.WeixinTradeParams();
            weixinTradeParams.setWeixin_sub_mch_id((String) MapUtils.getMap(extra, TransactionParam.WEIXIN_TRADE_PARAMS).get(TransactionParam.WEIXIN_SUB_MCH_ID));
            weixinTradeParams.setWeixin_sub_appid((String) MapUtils.getMap(extra, TransactionParam.WEIXIN_TRADE_PARAMS).get(TransactionParam.WEIXIN_SUB_APP_ID));
            weixinTradeParams.setFee_rate(BeanUtil.getPropString(merchantConfig, B2C_FEE_RATE));
            weixinDirectParams.setWeixin_trade_params(weixinTradeParams);
        }

        //小程序 支付参数
        if (BeanUtil.getPropBoolean(merchantConfig, MINI_STATUS) && BeanUtil.getPropBoolean(merchantConfig, MINI_FORMAL)) {
            WeixinDirectParams.WeixinTradeParams weixinMiniTradeParams = new WeixinDirectParams.WeixinTradeParams();
            weixinMiniTradeParams.setWeixin_sub_mch_id((String) MapUtils.getMap(extra, TransactionParam.WEIXIN_MINI_TRADE_PARAMS).get(TransactionParam.WEIXIN_SUB_MCH_ID));
            weixinMiniTradeParams.setWeixin_sub_appid((String) MapUtils.getMap(extra, TransactionParam.WEIXIN_MINI_TRADE_PARAMS).get(TransactionParam.WEIXIN_SUB_APP_ID));
            weixinMiniTradeParams.setFee_rate(BeanUtil.getPropString(merchantConfig, MINI_FEE_RATE));
            weixinDirectParams.setWeixin_mini_trade_params(weixinMiniTradeParams);
        }

        //h5支付参数
        if (BeanUtil.getPropBoolean(merchantConfig, H5_STATUS) && BeanUtil.getPropBoolean(merchantConfig, H5_FORMAL)) {
            WeixinDirectParams.WeixinWapTradeParams weixinH5TradeParams = new WeixinDirectParams.WeixinWapTradeParams();
            weixinH5TradeParams.setWeixin_sub_mch_id((String) MapUtils.getMap(extra, TransactionParam.WEIXIN_H5_TRADE_PARAMS).get(TransactionParam.WEIXIN_SUB_MCH_ID));
            weixinH5TradeParams.setWeixin_sub_appid((String) MapUtils.getMap(extra, TransactionParam.WEIXIN_H5_TRADE_PARAMS).get(TransactionParam.WEIXIN_SUB_APP_ID));
            weixinH5TradeParams.setFee_rate(BeanUtil.getPropString(merchantConfig, H5_FEE_RATE));
            weixinDirectParams.setWeixin_h5_trade_params(weixinH5TradeParams);
        }

        //app支付参数
        if (BeanUtil.getPropBoolean(merchantConfig, APP_STATUS) && BeanUtil.getPropBoolean(merchantConfig, APP_FORMAL)) {
            WeixinDirectParams.WeixinWapTradeParams weixinAPPTradeParams = new WeixinDirectParams.WeixinWapTradeParams();
            weixinAPPTradeParams.setWeixin_sub_mch_id((String) MapUtils.getMap(extra, TransactionParam.WEIXIN_APP_TRADE_PARAMS).get(TransactionParam.WEIXIN_SUB_MCH_ID));
            weixinAPPTradeParams.setWeixin_sub_appid((String) MapUtils.getMap(extra, TransactionParam.WEIXIN_APP_TRADE_PARAMS).get(TransactionParam.WEIXIN_SUB_APP_ID));
            weixinAPPTradeParams.setFee_rate(BeanUtil.getPropString(merchantConfig, APP_FEE_RATE));
            weixinDirectParams.setWeixin_app_trade_params(weixinAPPTradeParams);
        }

        weixinDirectParams.setMerchant_id(merchantService.getMerchantBySn(merchantSn, null).getId());
        weixinDirectParams.setMerchant_sn(merchantSn);
        return weixinDirectParams;
    }

    /**
     * 创建失败的进件任务
     *
     * @param merchantSn 请求参数
     * @param devCode    应用标识
     * @param message    错误信息
     */
    public void createFailTask(String merchantSn, String devCode, String message) {
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, devCode);
        ContractTask contractTask = new ContractTask().setMerchant_sn(merchantSn).setMerchant_name(BeanUtil.getPropString(merchantInfo, Merchant.NAME))
                .setAffect_sub_task_count(0).setAffect_status_success_task_count(0).setType(getTaskTypeByDevCode(devCode)).setStatus(TaskStatus.FAIL.getVal())
                .setResult(JSON.toJSONString(CollectionUtil.hashMap("channel", ProviderUtil.SHOUQIANBA_CHANNEL, "message", message)));
        //可以发送神策消息
        contractTaskBiz.insert(contractTask);
        directStatusBiz.createOrUpdateDirectStatus(merchantSn, devCode, DirectStatus.STATUS_BIZ_FAIL, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void bindMerchantWeixinDirectPay(BindMerchantReq bindMerchantReq, MerchantProviderParamsDto providerParams, Map context) {
        WeixinDirectParams directParams =
                this.buildWeixinDirectParams(bindMerchantReq.getMerchant_sn(), bindMerchantReq.getBind_merchant_sn(), providerParams);
        merchantProviderParamsService.addWeixinDirectParams(directParams);
        ContractTask contractTask = new ContractTask().setMerchant_sn(bindMerchantReq.getMerchant_sn()).setMerchant_name(((MerchantInfo) context.get(ParamContextBiz.KEY_MERCHANT)).getName()).setEvent_context(JSON.toJSONString(context))
                .setAffect_sub_task_count(0).setAffect_status_success_task_count(0).setType(getTaskTypeByDevCode(bindMerchantReq.getDev_code())).setStatus(TaskStatus.SUCCESS.getVal());
        //可以发送神策消息
        contractTaskBiz.insert(contractTask);
        directStatusBiz.createOrUpdateDirectStatus(bindMerchantReq.getMerchant_sn(), bindMerchantReq.getDev_code(), DirectStatus.STATUS_SUCCESS, null);
    }

    public WeixinDirectIndustry getWeixinDirectIndustry(String industryId) {
        Map result = weiXinDirectService.queryWeixinDirectIndustryInfo(industryId);
        WeixinDirectIndustry weixinDirectIndustry = new WeixinDirectIndustry();
        weixinDirectIndustry.setIndustryId(industryId)
                .setSupportDirect(BeanUtil.getPropBoolean(result, "is_direct"))
                .setNeedLicense(BeanUtil.getPropBoolean(result, "need_licence"))
                .setLicenseName(BeanUtil.getPropString(result, "licence"))
                .setIndividualSettlementId(BeanUtil.getPropString(result, "individual_settlement_id"))
                .setEnterpriseSettlementId(BeanUtil.getPropString(result, "enterprise_settlement_id"))
                .setInstitutionsSettlementId(BeanUtil.getPropString(result, "institutions_settlement_id"))
                .setOthersSettlementId(BeanUtil.getPropString(result, "others_settlement_id"));
        return weixinDirectIndustry;
    }

    public ContractResponse allowApplyToManual(String merchantSn, String devCode) {
        try {
            this.preCheck(merchantSn, devCode);
        } catch (CommonInvalidParameterException e) {
            return new ContractResponse().setSuccess(false).setMsg(e.getMessage());
        }
        List<WeixinDirectApply> weixinDirectApplies = weixinDirectApplyMapper.selectFailedApplyByMerchantSn(merchantSn, devCode);
        //失败次数在三次以下
        if (WosaiCollectionUtils.isEmpty(weixinDirectApplies) || weixinDirectApplies.size() < 3) {
            return new ContractResponse().setSuccess(false).setMsg("失败次数不允许转人工");
        }
        return new ContractResponse().setSuccess(true);
    }
}
