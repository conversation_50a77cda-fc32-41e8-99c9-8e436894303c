package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.BatchTemplateApolloConfig;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.acquirer.SyncMchStatusResp;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.application.RuleContractRequest;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McContractRuleDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import com.wosai.upay.job.service.ContractApplicationService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianParam;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateParam;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateQueryResponse;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateResponse;
import com.wosai.upay.merchant.contract.model.weixin.ModifySpecialFeeRateParam;
import com.wosai.upay.merchant.contract.service.NewUnionService;
import com.wosai.upay.merchant.contract.service.TongLianService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-04-16
 */
@Component
@Slf4j
public class ComposeAcquirerBiz {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    @Lazy
    private ContractApplicationService contractApplicationService;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Lazy
    @Autowired
    CommonEventHandler commonEventHandler;

    @Autowired
    private NewUnionService newUnionService;

    @Autowired
    private TongLianService tonglianService;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Autowired
    private BatchTemplateApolloConfig batchTemplateApolloConfig;

    @Autowired
    private McContractRuleDAO mcContractRuleDAO;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    public String getMerchantAcquirer(String merchantSn) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus == null) {
            throw new CommonPubBizException("商户还未报备");
        }
        return contractStatus.getAcquirer();
    }

    public void updateMerchantAcquirer(String merchantSn, String acquirer) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus != null) {
            ContractStatus updateValue = new ContractStatus()
                    .setId(contractStatus.getId())
                    .setAcquirer(acquirer);
            contractStatusMapper.updateByPrimaryKeySelective(updateValue);
        }
    }

    /**
     * 获取商户默认的规则组
     *
     * @param merchantSn
     * @return
     */
    public String getMerchantDefaultRuleGroup(String merchantSn, String acquirer) {
        return getAcquirerBizByMerchantSn(merchantSn).getMerchantDefaultRuleGroup(merchantSn, acquirer);
    }

    /**
     * 获取收单机构默认规则组
     *
     * @param acquirer
     * @return
     */
    public String getAcquirerDefaultRuleGroup(String acquirer) {
        return getAcquirerBiz(acquirer).getDefaultRuleGroup(acquirer);
    }

    /**
     * 从 merchant_config 中获取收单机构商户号
     *
     * @param merchantSn
     * @return
     */
    public String getAcquirerMchIdFromMerchantConfig(String merchantSn, String acquirer) {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, devCode);
        if (merchant == null) {
            throw new CommonPubBizException("商户不存在");
        }
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchant.getId(), null);
        return getAcquirerBiz(acquirer).getAcquirerMchIdFromMerchantConfig(merchantConfig);
    }

    public SyncMchStatusResp syncMchStatusToAcquirer(String merchantSn, int status) {
        return syncMchStatusToAcquirer(merchantSn, status, null);
    }

    /**
     * 将商户状态同步到收单机构
     *
     * @param merchantSn
     * @param status     0：关闭  1：正常  2：禁用
     * @return
     */
    public SyncMchStatusResp syncMchStatusToAcquirer(String merchantSn, int status, String acquirer) {
        SyncMchStatusResp mchStatusResp = new SyncMchStatusResp().setSuccess(true).setMessage("success");
        String message;
        try {
            IAcquirerBiz acquirerBiz;
            if (StringUtils.isEmpty(acquirer)) {
                //如果未指定收单机构，获取当前再用收单机构
                acquirerBiz = getAcquirerBizByMerchantSn(merchantSn);
            } else {
                //如果指定收单机构，则使用指定收单机构
                acquirerBiz = getAcquirerBiz(acquirer);
            }
            //当前再用收单机构状态
            final Boolean acquirerMchStatus = acquirerBiz.getAcquirerMchStatus(merchantSn);
            //收钱吧打开
            if (Objects.equals(status, ValidStatusEnum.VALID.getValue())) {
                //在收单机构打开
                if (acquirerMchStatus) {
                    mchStatusResp.setSubMchIdSyncResult(acquirerBiz.syncSubMchIdStatus(merchantSn, status));
                    return mchStatusResp;
                }
                //在收单机构关闭则需要重新打开
                mchStatusResp = acquirerBiz.syncMchAndSubMchIdStatus(merchantSn, status);
                handleFail(merchantSn, status, mchStatusResp);
                return mchStatusResp;
            } else {
                //收钱吧关闭或者禁用则需要关闭所有收单机构状态
                //当前再用收单机构状态,这样写是为了可以将当前再用收单机构与未使用收单机构分开操作,但目前其实合并也可以
                if (acquirerMchStatus) {
                    //关闭当前在用收单机构
                    mchStatusResp = acquirerBiz.syncMchAndSubMchIdStatus(merchantSn, status);
                } else {
                    // 在收单机构关闭，只需要关闭子商户号
                    mchStatusResp.setSubMchIdSyncResult(acquirerBiz.syncSubMchIdStatus(merchantSn, status));
                }
                //关闭当前商户未使用的收单机构
                final List<SyncMchStatusResp> respList = handleInvalidAcquire(merchantSn, status, null, Arrays.asList(AcquirerTypeEnum.TONG_LIAN.getValue()));
                log.info("处理未生效收单机构返回结果:{},商户号:{}", JSONObject.toJSONString(respList), merchantSn);
                //是否有处理失败的收单机构
                respList.add(mchStatusResp);
                if (respList.parallelStream().anyMatch(x -> !x.isSuccess())) {
                    mchStatusResp = respList.parallelStream().filter(x -> !x.isSuccess()).limit(1L).collect(Collectors.toList()).get(0);
                }
                handleFail(merchantSn, status, mchStatusResp);
                return mchStatusResp;
            }
        } catch (Exception exception) {
            message = ExceptionUtil.getThrowableMsg(exception);
            if (Objects.equals("商户还未报备", message)) {
                return mchStatusResp;
            }
            log.error("商户号:{},向收单机构同步商户状态:{},失败:{}", merchantSn, Objects.equals(status, ValidStatusEnum.VALID.getValue()) ? "打开" : "关闭", exception);
            chatBotUtil.sendMessageToSyncMchStatusChatBot(String.format("商户号:%s,向收单机构同步商户状态:%s,失败:%s", merchantSn, Objects.equals(status, ValidStatusEnum.VALID.getValue()) ? "打开" : "关闭", exception.getLocalizedMessage()));
        }
        return new SyncMchStatusResp().setSuccess(Boolean.FALSE).setMessage(message);
    }

    /**
     * @param merchantSn
     * @param status
     * @param mchStatusResp
     * <AUTHOR>
     * @Description: 处理失败抛出报错信息
     * @time 09:17
     */
    public void handleFail(String merchantSn, int status, SyncMchStatusResp mchStatusResp) {
        if (!mchStatusResp.isSuccess()) {
            chatBotUtil.sendMessageToSyncMchStatusChatBot(String.format("商户号:%s,向收单机构同步商户状态:%s,失败:%s", merchantSn, Objects.equals(status, ValidStatusEnum.VALID.getValue()) ? "打开" : "关闭", mchStatusResp.getMessage()));
        }
    }

    /**
     * 将商户状态同步到指定的收单机构
     *
     * @param merchantSn 商户号
     * @param status     状态
     * @param acquirer   收单机构名称
     * @return 同步结果
     */
    public SyncMchStatusResp syncMchAndSubMchIdStatus(String merchantSn, int status, String acquirer) {
        SyncMchStatusResp mchStatusResp = new SyncMchStatusResp().setSuccess(true).setMessage("success");
        String message;
        try {
            IAcquirerBiz acquirerBiz = getAcquirerBiz(acquirer);
            //当前再用收单机构状态
            final Boolean acquirerMchStatus = acquirerBiz.getAcquirerMchStatus(merchantSn);
            //收钱吧打开
            if (Objects.equals(status, ValidStatusEnum.VALID.getValue())) {
                // 如果在收单机构是打开的，则只需打开子商户号
                if (acquirerMchStatus) {
                    mchStatusResp.setSubMchIdSyncResult(acquirerBiz.syncSubMchIdStatus(merchantSn, status));
                    return mchStatusResp;
                }
                // 打开收单机构商户和子商户号
                mchStatusResp = acquirerBiz.syncMchAndSubMchIdStatus(merchantSn, status);
                handleFail(merchantSn, status, mchStatusResp);
                return mchStatusResp;
            } else {
                // 如果在收单机构是打开，则需要关闭收单机构商户和子商户号
                if (acquirerMchStatus) {
                    mchStatusResp = acquirerBiz.syncMchAndSubMchIdStatus(merchantSn, status);
                } else {
                    // 在收单机构关闭，只需要关闭子商户号
                    mchStatusResp.setSubMchIdSyncResult(acquirerBiz.syncSubMchIdStatus(merchantSn, status));
                }
                return mchStatusResp;
            }
        } catch (Exception exception) {
            message = ExceptionUtil.getThrowableMsg(exception);
            if (Objects.equals("商户还未报备", message)) {
                return mchStatusResp;
            }
            log.error("商户号:{},向收单机构同步商户状态:{},失败:{}", merchantSn, Objects.equals(status, ValidStatusEnum.VALID.getValue()) ? "打开" : "关闭", exception);
            chatBotUtil.sendMessageToSyncMchStatusChatBot(String.format("商户号:%s,向收单机构同步商户状态:%s,失败:%s", merchantSn, Objects.equals(status, ValidStatusEnum.VALID.getValue()) ? "打开" : "关闭", exception.getLocalizedMessage()));
        }
        return new SyncMchStatusResp().setSuccess(Boolean.FALSE).setMessage(message);
    }

    /**
     * @param
     * @param merchantSn      商户号
     * @param validAcquirer   生效的acquirer
     * @param excludeAcquirer 不关闭的收单机构集合
     * @return status 状态 0：关闭  1：正常  2：禁用
     * <AUTHOR>
     * @Description: 关闭当前商户未使用的收单机构 当validAcquirer不为空代表关闭除了validAcquirer的所有收单机构
     * @time 15:59
     */
    public List<SyncMchStatusResp> handleInvalidAcquire(String merchantSn, int status, String validAcquirer, List<String> excludeAcquirer) {
        //关闭当前没有生效的收单机构
        List<SyncMchStatusResp> statusRespList = Lists.newArrayList();
        //1,当前商户报过的所有收单机构
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);
        final List<MerchantProviderParams> paramsList = commonEventHandler.setRule(records);
        if (CollectionUtils.isEmpty(paramsList)) {
            return statusRespList;
        }
        //当前商户使用那些groupId报过
        final List<String> acquireParams = paramsList.parallelStream().map(MerchantProviderParams::getContract_rule).collect(Collectors.toList());
        //mc_rule_group表group_id与acquire对应关系
        final List<McContractRuleDO> ruleList = mcContractRuleDAO.listAllRule();
        final Map<String, String> ruleAcquireMap = ruleList.parallelStream().collect(Collectors.toMap(x -> x.getRule(), x -> x.getAcquirer(), (val1, val2) -> val1));
        //报过的收单机构
        final List<String> acquireList = acquireParams.stream()
                .map(x -> ruleAcquireMap.get(x))
                .filter(y -> Objects.nonNull(y))
                .collect(Collectors.toList());
        //当前再用的收单机构
        if (StringUtils.isEmpty(validAcquirer)) {
            validAcquirer = getMerchantAcquirer(merchantSn);
        }
        acquireList.remove(validAcquirer);
        // 排除不需要关闭的收单机构集合
        if (WosaiCollectionUtils.isNotEmpty(excludeAcquirer)) {
            acquireList.removeAll(excludeAcquirer);
        }
        if (!CollectionUtils.isEmpty(acquireList)) {
            statusRespList = acquireList.parallelStream().map(x -> CompletableFuture.supplyAsync(() -> getAcquirerBiz(x).getAcquirerMchStatus(merchantSn))
                            .thenApply(y -> {
                                IAcquirerBiz acquirerBiz = getAcquirerBiz(x);
                                //商户在收单机构开
                                if (y) {
                                    return acquirerBiz.syncMchAndSubMchIdStatus(merchantSn, status);
                                }
                                return new SyncMchStatusResp().setSuccess(true).setMessage("success").setSubMchIdSyncResult(acquirerBiz.syncSubMchIdStatus(merchantSn, status));
                            }).exceptionally(e -> new SyncMchStatusResp().setSuccess(Boolean.FALSE).setMessage(e.getCause().getMessage()))).filter(z -> Objects.nonNull(z)).map(CompletableFuture::join)
                    .collect(Collectors.toList());
        }
        return statusRespList;
    }

    /**
     * 重新在普通渠道报备一个微信子商户号并设为默认
     *
     * @param merchantSn
     * @param remark
     * @return
     */
    public CommonResult reContractWx(String merchantSn, String remark, boolean forceMicro) {
        IAcquirerBiz acquirerBiz = getAcquirerBizByMerchantSn(merchantSn);
        return contractApplicationService.contractByRule(
                new RuleContractRequest()
                        .setRule(acquirerBiz.getMerchantNormalWxRule(merchantSn))
                        .setMerchantSn(merchantSn)
                        .setPlat(remark)
                        .setReContract(true)
                        .setForceMicro(forceMicro)
        );
    }

    /**
     * 在指定收单机构重新在普通渠道报备一个微信子商户号
     *
     * @param merchantSn 商户号
     * @param acquire    收单机构
     * @return
     */
    public CommonResult reContractWx(String merchantSn, String acquire) {
        IAcquirerBiz acquirerBiz = getAcquirerBiz(acquire);
        return contractApplicationService.contractByRule(
                new RuleContractRequest()
                        .setRule(acquirerBiz.getMerchantNormalWxRule(merchantSn))
                        .setMerchantSn(merchantSn)
                        .setPlat("changeAcquirer结算Id不匹配")
                        .setReContract(true)
                        .setConfigParam(false)
        );
    }


    /**
     * 重新在普通渠道报备一个微信子商户号并设为默认
     *
     * @param merchantSn
     * @param acquirer
     * @param remark
     * @return
     */
    public CommonResult reContractWx(String merchantSn, String acquirer, String remark, boolean configParam) {
        IAcquirerBiz acquirerBiz = getAcquirerBiz(acquirer);
        return contractApplicationService.contractByRule(
                new RuleContractRequest()
                        .setRule(acquirerBiz.getNormalWxRule())
                        .setMerchantSn(merchantSn)
                        .setPlat(remark)
                        .setReContract(true)
                        .setConfigParam(configParam)
        );
    }

    /**
     * 向微信更新客服电话和经营名称
     *
     * @param paramsDto
     * @param params
     */
    public void updateWeixinParams(MerchantProviderParamsDto paramsDto, Map params) {
        getAcquirerBizByMerchantSn(paramsDto.getMerchant_sn()).updateWeixinParams(paramsDto, params);
    }

    /**
     * 修改费率
     *
     * @param merchantSn
     * @param param
     * @param contractChannel
     * @return
     */
    public ApplySpecialFeeRateResponse modifyFeeRate(String merchantSn, ModifySpecialFeeRateParam param, ContractChannel contractChannel) {
        return getAcquirerBizByMerchantSn(merchantSn).modifyFeeRate(merchantSn, param, contractChannel);

    }

    /**
     * 申请费率
     *
     * @param merchantSn
     * @param param
     * @param contractChannel
     * @return
     */
    public ApplySpecialFeeRateResponse applyFeeRate(String merchantSn, ApplySpecialFeeRateParam param, ContractChannel contractChannel) {
        return getAcquirerBizByMerchantSn(merchantSn).applyFeeRate(merchantSn, param, contractChannel);

    }


    /**
     * 查询申请费率状态
     *
     * @param merchantSn
     * @param applicationId
     * @param contractChannel
     * @return
     * @throws Exception
     */
    public ApplySpecialFeeRateQueryResponse queryRateApplyStatus(String merchantSn, String applicationId, ContractChannel contractChannel) throws Exception {
        return getAcquirerBizByMerchantSn(merchantSn).queryRateApplyStatus(merchantSn, applicationId, contractChannel);
    }

    /**
     * 获取银联参数
     *
     * @param merchantSn
     * @return
     */
    public Map<String, Object> getUnionOpenParam(String merchantSn) {
        return getAcquirerBizByMerchantSn(merchantSn).getUnionOpenParam(merchantSn);
    }

    /**
     * 微信高校食堂 获取商户 渠道号
     *
     * @return
     */
    public String getSchoolCanteenChannelNo(String merchantSn) {
        return getAcquirerBizByMerchantSn(merchantSn).getSchoolCanteenChannelNo(merchantSn);
    }


    /**
     * 获取微信子商户号在微信侧的信息
     *
     * @param providerParams
     * @return
     */
    public WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        ContractChannel contractChannel = ruleContext.getContractChannel(providerParams.getPayway(), providerParams.getProvider() + "", providerParams.getChannel_no());
        return getAcquirerBiz(contractChannel.getAcquirer()).getWxMchInfo(providerParams);
    }

    /**
     * 获取支付宝子商户号在支付宝侧的信息
     *
     * @param providerParams
     * @return
     */
    public Map getAlipayMchInfo(MerchantProviderParams providerParams) {
        if (Objects.equals(ProviderEnum.PROVIDER_TONGLIAN.getValue(), providerParams.getProvider())) {
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_ALI.getValue(), TongLianParam.class);
            return tonglianService.queryAliSubMchWithParams(providerParams.getPay_merchant_id(), tongLianParam);
        } else {
            try {
                return newUnionService.queryAlySubMch(providerParams.getPay_merchant_id());
            } catch (Throwable t) {
                return new HashMap();
            }
        }
    }

    /**
     * 获取支付宝子商户号在支付宝侧的信息
     *
     * @param alipayMchId
     * @return
     */
    public Map getAlipayMchInfo(String alipayMchId) {
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(alipayMchId);
        return getAlipayMchInfo(merchantProviderParams);
    }


    /**
     * 获取支付宝子商户号在支付宝侧的信息
     * 包含银行渠道，目前银行渠道均未从报备的子任务列表中获取
     *
     * @param alipayMchId
     * @return
     */
    public AlipayMchInfo getAlipayMchInfoWithBank(String alipayMchId) {
        MerchantProviderParams providerParams = merchantProviderParamsMapper.getByPayMerchantId(alipayMchId);
        ContractChannel contractChannel = ruleContext.getContractChannel(providerParams.getPayway(), providerParams.getProvider() + "", providerParams.getChannel_no());
        return getAcquirerBiz(contractChannel.getAcquirer()).getAlipayMchInfo(providerParams);
    }

    /**
     * 向支付源同步商户信息
     *
     * @param merchantSn
     * @param payWay     2-支付宝 3-微信
     */
    public void syncMchInfo2PayWay(String merchantSn, int payWay) {
        getAcquirerBizByMerchantSn(merchantSn).syncMchInfo2PayWay(merchantSn, payWay);
    }

    /**
     * 将当前商户号 修改为 可以提交实名认证的条件
     * v1:  修改名称 + 修改微信结算id (由于微信结算id接口不适合对接，因此若微信结算id不符合规则，直接返回false)
     *
     * @param params
     * @param context
     * @return true -> 修改成功;   false -> 修改失败
     */
    public Boolean updateWechatNameAndSettleMentId(MerchantProviderParams params, Map context) {
        return getAcquirerBiz(getMerchantAcquirer(params.getMerchant_sn())).updateWechatNameAndSettleMentId(params, context);
    }

    /**
     * 修改支付宝子商户号对应的商户的信息
     *
     * @param providerParams
     * @return
     */
    public ContractResponse updateAlipayMchInfo(MerchantProviderParams providerParams, AlipayMchInfo alipayMchInfo) {
        ContractChannel contractChannel = ruleContext.getContractChannel(providerParams.getPayway(), providerParams.getProvider() + "", providerParams.getChannel_no());
        return getAcquirerBiz(contractChannel.getAcquirer()).updateAlipayParams(providerParams, alipayMchInfo);
    }

    /**
     * 查询该收单机构下的银联云闪付开通状态
     *
     * @param merchantSn 商户号
     * @param acquirer   当前收单机构
     * @return 开通状态
     */
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn, String acquirer) {
        return getAcquirerBiz(acquirer).queryUnionPayOpenStatus(merchantSn);
    }


    private Map<String, IAcquirerBiz> iAcquirerBizMap = new ConcurrentHashMap<>();

    public IAcquirerBiz getAcquirerBiz(String acquirer) {
        McAcquirerDO byAcquirer = mcAcquirerDAO.getByAcquirer(acquirer);
        Map supportBankConfig = batchTemplateApolloConfig.getSupportBankConfig();
        Map indirectImportConfig = batchTemplateApolloConfig.getSupportIndirectConfig();
        String beanName = null;
        if (byAcquirer != null && WosaiMapUtils.isNotEmpty(supportBankConfig) && supportBankConfig.containsKey(byAcquirer.getProvider())) {
            beanName = "common" + "-biz";
        }else if(byAcquirer != null && WosaiMapUtils.isNotEmpty(indirectImportConfig) && indirectImportConfig.containsKey(byAcquirer.getProvider())) {
            beanName = "indirectImport" + "-biz";
        } else {
            beanName = acquirer + "-biz";

        }
        String finalBeanName = beanName;
        return iAcquirerBizMap.computeIfAbsent(beanName, name -> {
            try {
                return applicationContext.getBean(finalBeanName, IAcquirerBiz.class);
            } catch (BeansException e) {
                log.error("{} IAcquirerBiz 不存在", acquirer, e);
                throw new ContractBizException(acquirer + " IAcquirerBiz 不存在");
            }
        });
    }

    public IAcquirerBiz getAcquirerBizByMerchantSn(String merchantSn) {
        return getAcquirerBiz(getMerchantAcquirer(merchantSn));
    }


}
