package com.wosai.upay.job.util;

import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.IdentificationTypeEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.service.StoreExtService;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.bank.info.api.service.DistrictsService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.MerchantType;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.merchant.audit.api.model.MerchantAudit;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.weixin.ApplymentParam;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class WeixinApplyUtil {

    @Autowired
    DistrictsService districtsService;

    @Autowired
    private StoreExtService storeExtService;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    //默认地区编码 上海市普陀区
    private static String defaultCode = "310107";

    /**
     * 申请单 和参数转换成 微信提交申请单请求
     */
    public ApplymentParam toApplyParam(Map contextParam, Map auditInfo) throws ContextParamException {
        Map license = (Map) contextParam.get(ParamContextBiz.KEY_BUSINESS_LICENCE);
        Map bankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
        Map merchant = (Map) contextParam.get(ParamContextBiz.KEY_MERCHANT);
        Boolean forceMicro = (Boolean) contextParam.getOrDefault(ParamContextBiz.FORCE_MICRO, Boolean.FALSE);
        return new ApplymentParam()
                .setContact_info(getConcatInfo(merchant, bankAccount, license, forceMicro))
                .setSubject_info(getSubjectInfo(merchant, license, auditInfo, forceMicro))
                .setIdentification_info((getIdentificationInfo(bankAccount, license, forceMicro)));
    }

    /**
     * 申请单 和参数转换成 微信提交申请单请求 V2
     */
    public ApplymentParam toApplyParam(Map contextParam) throws ContextParamException {
        Map license = (Map) contextParam.get(ParamContextBiz.KEY_BUSINESS_LICENCE);
        Map bankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
        Map merchant = (Map) contextParam.get(ParamContextBiz.KEY_MERCHANT);
        Boolean forceMicro = (Boolean) contextParam.getOrDefault(ParamContextBiz.FORCE_MICRO, Boolean.FALSE);
        Map auditInfo = MapUtils.getMap(contextParam, ParamContextBiz.KEY_MERCHANT_AUDIT);
        return new ApplymentParam()
                .setContact_info(getConcatInfo(merchant, bankAccount, license, forceMicro))
                .setSubject_info(getSubjectInfo(merchant, license, auditInfo, forceMicro))
                .setIdentification_info((getIdentificationInfo(bankAccount, license, forceMicro)));
    }

    /**
     * 联系人信息
     */
    public ApplymentParam.Contact_info getConcatInfo(Map merchant, Map bankAccount, Map license, Boolean forceMicro) {
        MerchantType merchantType = getMerchantType(license, forceMicro);
        ApplymentParam.Contact_info concatInfo = new ApplymentParam.Contact_info();
        if (merchantType == MerchantType.TYPE_MICRO) {
            concatInfo.setName(BeanUtil.getPropString(bankAccount, MerchantBankAccount.HOLDER)); //结算人姓名
            concatInfo.setMobile(BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE)); //联系人手机号
            concatInfo.setId_card_number(BeanUtil.getPropString(bankAccount, MerchantBankAccount.IDENTITY)); //结算人证件号
        } else if (merchantType == MerchantType.TYPE_INDIVIDUAL) {
            concatInfo.setName(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_NAME)); //经营者姓名
            concatInfo.setMobile(BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE)); //联系人手机号
            concatInfo.setId_card_number(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER)); //经营者证件号
        } else if (merchantType == MerchantType.TYPE_ENTERPRISE || merchantType == MerchantType.TYPE_OTHER) {
            concatInfo.setMobile(BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE)); //联系人手机号
            String concatIdentity = BeanUtil.getPropString(merchant, Merchant.CONCAT_IDENTITY);
            if (!StringUtil.empty(concatIdentity)) {
                concatInfo.setName(BeanUtil.getPropString(merchant, Merchant.CONTACT_NAME));
                concatInfo.setId_card_number(concatIdentity);
            } else {
                concatInfo.setName(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_NAME));
                concatInfo.setId_card_number(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER));
            }
        } else {
            concatInfo.setName(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_NAME));
            concatInfo.setMobile(BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE));
            concatInfo.setId_card_number(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER));
        }
        return concatInfo;
    }


    /**
     * 主体信息
     */
    private ApplymentParam.Subject_info getSubjectInfo(Map merchant, Map license, Map auditInfo, Boolean forceMicro) throws ContextParamException {
        ApplymentParam.Subject_info subjectInfo = new ApplymentParam.Subject_info();
        MerchantType merchantType = getMerchantType(license, forceMicro);
        if (merchantType == MerchantType.TYPE_MICRO) {
            subjectInfo.setSubject_type("SUBJECT_TYPE_MICRO"); //小微经营类型
            ApplymentParam.Assist_prove_info proveInfo = new ApplymentParam.Assist_prove_info();
            proveInfo.setMicro_biz_type("MICRO_TYPE_STORE");
            String businessName = BeanUtil.getPropString(merchant, Merchant.BUSINESS_NAME);
            if (StringUtil.empty(businessName)) {
                businessName = BeanUtil.getPropString(merchant, Merchant.ALIAS);
            }
            proveInfo.setStore_name(businessName); //门店名称
            String citycode = String.format("%s %s %s", BeanUtil.getPropString(merchant, Merchant.PROVINCE), BeanUtil.getPropString(merchant, Merchant.CITY), BeanUtil.getPropString(merchant, Merchant.DISTRICT));
            proveInfo.setStore_address_code(getWechatCityCode(citycode));
            proveInfo.setStore_address(BeanUtil.getPropString(merchant, Merchant.STREET_ADDRESS)); //店铺详细地址

            StotreExtInfoAndPictures storeInfo = storeExtService.findLastStoreExtAndPicturesByMerchantId(BeanUtil.getPropString(merchant, DaoConstants.ID));

            String storeShopPhoto = BeanUtil.getPropString(auditInfo, MerchantAudit.BRAND_PHOTO, ""); //门店门口照片
            // 实名审核没有从商户中心获取
            if(StringUtil.empty(storeShopPhoto) && storeInfo != null && storeInfo.getBrandPhoto() != null) {
                storeShopPhoto = storeInfo.getBrandPhoto().getUrl();
            }
            if (!StringUtil.empty(storeShopPhoto) && storeShopPhoto.contains(",")) {
                storeShopPhoto = storeShopPhoto.split(",")[0];
            }
            String storeHallPhoto = BeanUtil.getPropString(auditInfo, MerchantAudit.INDOOR_MATERIAL_PHOTO, ""); //店内环境照片
            // 实名审核没有从商户中心获取
            if(StringUtil.empty(storeHallPhoto) && storeInfo != null && storeInfo.getIndoorMaterialPhoto() != null) {
                storeHallPhoto = storeInfo.getIndoorMaterialPhoto().getUrl();
            }
            if (!StringUtil.empty(storeHallPhoto) && storeHallPhoto.contains(",")) {
                storeHallPhoto = storeHallPhoto.split(",")[0];
            }

            if (StringUtil.empty(storeShopPhoto) || StringUtil.empty(storeHallPhoto)) {
                throw new ContextParamException("门店照片不存在");
            }
            proveInfo.setStore_header_copy(storeShopPhoto);
            proveInfo.setStore_indoor_copy(storeHallPhoto);
            subjectInfo.setAssist_prove_info(proveInfo);
        } else if (merchantType == MerchantType.TYPE_INDIVIDUAL || merchantType == MerchantType.TYPE_ENTERPRISE) {
            if (merchantType == MerchantType.TYPE_INDIVIDUAL) {
                subjectInfo.setSubject_type("SUBJECT_TYPE_INDIVIDUAL");
            }
            if (merchantType == MerchantType.TYPE_ENTERPRISE) {
                subjectInfo.setSubject_type("SUBJECT_TYPE_ENTERPRISE");
            }

            ApplymentParam.Business_licence_info licenceInfo = new ApplymentParam.Business_licence_info();
            licenceInfo.setLicence_number(BeanUtil.getPropString(license, MerchantBusinessLicence.NUMBER));
            licenceInfo.setLicence_copy(BeanUtil.getPropString(license, MerchantBusinessLicence.PHOTO));
            licenceInfo.setMerchant_name(BeanUtil.getPropString(license, MerchantBusinessLicence.NAME));
            licenceInfo.setLegal_person(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_NAME));
            licenceInfo.setCompany_address(BeanUtil.getPropString(license, MerchantBusinessLicence.ADDRESS));
            licenceInfo.setLicence_valid_date(translateValidity(BeanUtil.getPropString(license, MerchantBusinessLicence.VALIDITY)));
            subjectInfo.setBusiness_licence_info(licenceInfo);
        } else {
            subjectInfo.setSubject_type("SUBJECT_TYPE_OTHERS");
            ApplymentParam.Certificate_info certificateInfo = new ApplymentParam.Certificate_info();
            certificateInfo.setCert_type("");
            certificateInfo.setCert_number(BeanUtil.getPropString(license, MerchantBusinessLicence.NUMBER));
            certificateInfo.setCert_copy(BeanUtil.getPropString(license, MerchantBusinessLicence.PHOTO));
            certificateInfo.setMerchant_name(BeanUtil.getPropString(license, MerchantBusinessLicence.NAME));
            certificateInfo.setLegal_person(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_NAME));
            certificateInfo.setCompany_address(BeanUtil.getPropString(license, MerchantBusinessLicence.ADDRESS));
            certificateInfo.setCert_valid_date(translateValidity(BeanUtil.getPropString(license, MerchantBusinessLicence.VALIDITY)));
            String certType = getCertTypeByLicenceType(BeanUtil.getPropInt(license, MerchantBusinessLicence.TYPE, 0));
            certificateInfo.setCert_type(certType);
            subjectInfo.setCertificate_info(certificateInfo);
        }

        return subjectInfo;
    }

    /**
     * 证书类型  收钱吧 https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********
     * 微信:
     * 1、CERTIFICATE_TYPE_2388：事业单位法人证书
     * 2、CERTIFICATE_TYPE_2389：统一社会信用代码证书
     * 3、CERTIFICATE_TYPE_2390：有偿服务许可证（军队医院适用）
     * 4、CERTIFICATE_TYPE_2391：医疗机构执业许可证（军队医院适用）
     * 5、CERTIFICATE_TYPE_2392：企业营业执照（挂靠企业的党组织适用）
     * 6、CERTIFICATE_TYPE_2393：组织机构代码证（政府机关适用）
     * 7、CERTIFICATE_TYPE_2394：社会团体法人登记证书
     * 8、CERTIFICATE_TYPE_2395：民办非企业单位登记证书
     * 9、CERTIFICATE_TYPE_2396：基金会法人登记证书
     * 10、CERTIFICATE_TYPE_2397：慈善组织公开募捐资格证书
     * 11、CERTIFICATE_TYPE_2398：农民专业合作社法人营业执照
     * 12、CERTIFICATE_TYPE_2399：宗教活动场所登记证
     * 13、CERTIFICATE_TYPE_2400：其他证书/批文/证明
     */
    private String getCertTypeByLicenceType(int licenceType) {
        if (licenceType == 3) {
            return "CERTIFICATE_TYPE_2388"; //事业单位法人证书
        } else if (licenceType == 4) {
            return "CERTIFICATE_TYPE_2395"; //民办非企业单位登记证书
        } else if (licenceType == 5) {
            return "CERTIFICATE_TYPE_2394"; //社会团体法人登记证书
        } else if (licenceType == 6) {
            return "CERTIFICATE_TYPE_2396"; //基金会法人登记证书
        } else if (licenceType == 8) {
            return "CERTIFICATE_TYPE_2399";//宗教活动场所法人登记证书
        } else if (licenceType == 9) {
            return "CERTIFICATE_TYPE_2398";//农民专业合作社
        } else if (licenceType == 11) {
            return "CERTIFICATE_TYPE_2389";//统一社会信用代码证书
        } else {
            return "CERTIFICATE_TYPE_2400"; //其他证书/批文/证明
        }

    }

    /**
     * 法人身份信息
     */
    private ApplymentParam.Identification_info getIdentificationInfo(Map bankAccount, Map license, Boolean forceMicro) throws ContextParamException {
        MerchantType merchantType = getMerchantType(license, forceMicro);
        ApplymentParam.Identification_info info = new ApplymentParam.Identification_info();
        if (merchantType == MerchantType.TYPE_MICRO) {
            int type = BeanUtil.getPropInt(bankAccount, MerchantBankAccount.ID_TYPE);
            String identity = BeanUtil.getPropString(bankAccount, MerchantBankAccount.IDENTITY);
            info.setIdentification_type(getWechatIdType(type, identity));
            info.setIdentification_name(BeanUtil.getPropString(bankAccount, MerchantBankAccount.HOLDER));
            info.setIdentification_number(identity);
            info.setIdentification_valid_date(translateValidity(BeanUtil.getPropString(bankAccount, MerchantBankAccount.ID_VALIDITY)));
            info.setIdentification_front_copy(BeanUtil.getPropString(bankAccount, MerchantBankAccount.HOLDER_ID_FRONT_PHOTO));
            info.setIdentification_back_copy(BeanUtil.getPropString(bankAccount, MerchantBankAccount.HOLDER_ID_BACK_PHOTO));
        } else {

            int type = BeanUtil.getPropInt(license, MerchantBusinessLicence.LEGAL_PERSON_ID_TYPE);
            String identity = BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER);
            info.setIdentification_type(getWechatIdType(type, identity));
            info.setIdentification_name(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_NAME));
            info.setIdentification_number(identity);
            info.setIdentification_valid_date(translateValidity(BeanUtil.getPropString(license, MerchantBusinessLicence.ID_VALIDITY)));
            info.setIdentification_front_copy(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_FRONT_PHOTO));
            info.setIdentification_back_copy(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_BACK_PHOTO));
        }
        return info;
    }


    /**
     * 收钱吧法人证件类型：1 身份证；2 港澳居民来往内地通行证； 3 台湾居民来往大陆通行证； 4 非中华人民共和国护照   5 中华人民共和国护照  6 港澳居民证 7 台湾居民证
     * 微信身份证类型映射 账户持有人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；
     * 1、身份证（限中国大陆居民）：IDENTIFICATION_TYPE_IDCARD
     * 2、护照（限境外人士）：IDENTIFICATION_TYPE_OVERSEA_PASSPORT
     * 3、中国香港居民-来往内地通行证：IDENTIFICATION_TYPE_HONGKONG_PASSPORT
     * 4、中国澳门居民-来往内地通行证: IDENTIFICATION_TYPE_MACAO_PASSPORT
     * 5、中国台湾居民-来往大陆通行证IDENTIFICATION_TYPE_TAIWAN_PASSPORT
     */
    private String getWechatIdType(int idType, String number) {
        if (idType == IdentificationTypeEnum.PRC_ID_CARD.getValue()) {
            return "IDENTIFICATION_TYPE_IDCARD";
        } else if (idType == IdentificationTypeEnum.NON_PRC_PASSPORT.getValue() || idType == IdentificationTypeEnum.PRC_PASSPORT.getValue()) {
            return "IDENTIFICATION_TYPE_OVERSEA_PASSPORT";
        } else if (idType == IdentificationTypeEnum.TAIWAN_PERMIT.getValue()) {
            return "IDENTIFICATION_TYPE_TAIWAN_PASSPORT";
        } else if (idType == IdentificationTypeEnum.HK_MACAO_PERMIT.getValue()) {
            if (!StringUtil.empty(number) && number.startsWith("H")) {
                return "IDENTIFICATION_TYPE_HONGKONG_PASSPORT"; //新版本的证件才能验证的到，需要通过证件号区分，香港的证件号是H开通，澳门的是M开头
            }
            return "IDENTIFICATION_TYPE_MACAO_PASSPORT";
        }
        throw new ContractBizException("微信暂不支持" + IdTypeUtil.getNameByType(idType));

    }

    private String translateValidity(String validity) throws ContextParamException {
        try {
            if (StringUtil.empty(validity) || !validity.contains("-")) {
                return "";
            }
            String[] split = validity.split("-");
            String start = split[0];
            String end = split[1];
            if ("99991231".equals(end)) {
                end = "forever";
            }
            return "[\"" + getIdValid(start) + "\",\"" + ("forever".equals(end) ? end : getIdValid(end)) + "\"]";
        } catch (Throwable t) {
            throw new ContextParamException("有效期数据有误请检查后重试" + t.getMessage());
        }

    }

    private String getIdValid(String source) {
        if (StringUtils.isEmpty(source) || source.length() < 8) {
            return "";
        }
        String year = source.substring(0, 4);
        String month = source.substring(4, 6);
        String day = source.substring(6, 8);
        return year + "-" + month + "-" + day;
    }


    /**
     * 获取上传参数需要的 商户类型
     */
    private MerchantType getMerchantType(Map license, Boolean forceMicro) {
        if (Boolean.TRUE.equals(forceMicro)) {
            return MerchantType.TYPE_MICRO;
        }
        int type = BeanUtil.getPropInt(license, MerchantBusinessLicence.TYPE, BusinessLicenseTypeEnum.MICRO.getValue());
        if (type == BusinessLicenseTypeEnum.MICRO.getValue()) {
            return MerchantType.TYPE_MICRO;
        } else if (type == BusinessLicenseTypeEnum.INDIVIDUAL.getValue()) {
            return MerchantType.TYPE_INDIVIDUAL;
        } else if (type == BusinessLicenseTypeEnum.ENTERPRISE.getValue()) {
            return MerchantType.TYPE_ENTERPRISE;
        } else {
            return MerchantType.TYPE_OTHER;
        }

    }

    /**
     * 获取微信地区编码
     * 若找不到地区编码，默认获取上海市普陀区
     *
     * @param district 北京市 市辖区 东城区
     * @return
     */
    private String getWechatCityCode(String district) {
        String code = districtsService.getDistrictCodeByCompleteName(district);
        if (StringUtils.isEmpty(code)) {
            return defaultCode;
        }
        if (applicationApolloConfig.getSqbDistrictCode().contains(code)) {
            return MapUtils.getString(applicationApolloConfig.getReflectCodeMap(), code);
        } else {
            return code;
        }
    }
}