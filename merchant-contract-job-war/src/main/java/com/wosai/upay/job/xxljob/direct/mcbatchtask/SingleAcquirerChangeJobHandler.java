package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.ApproveConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.McBatchTaskMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.acquirer.ChangeAcquirerRequest;
import com.wosai.upay.job.model.changeAcquirerApprove.ChangeAcquirerApproveDTO;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.wosai.upay.job.service.WeixinFeeRateActivityServiceImpl.AUDIT_EXECUTE_FAIL;

/**
 * xxl_job_desc: BatchTask-单个切换收单机构任务
 * <AUTHOR>
 * @date 2025/4/17
 */
@Slf4j
@Component("SingleAcquirerChangeJobHandler")
public class SingleAcquirerChangeJobHandler extends AbstractMcBatchTaskJobHandler {

    @Autowired
    private AcquirerService acquirerService;
    @Autowired
    private AcquirerChangeDao acquirerChangeDao;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private BusinessLogBiz businessLogBiz;


    @Override
    public String getLockKey() {
        return "SingleAcquirerChangeJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        McBatchTaskExample mcBatchTaskExample = new McBatchTaskExample();
        mcBatchTaskExample.or().andStatusEqualTo(0).andEffect_timeLessThanOrEqualTo(new Date()).andTypeEqualTo(4);
        List<McBatchTask> list = mcBatchTaskMapper.selectByExampleWithBLOBs(mcBatchTaskExample);
        list.forEach(mcBatchTask -> {
            Map extra = Maps.newHashMap();
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                final String payload = mcBatchTask.getPayload();
                extra = CommonUtil.string2Map(payload);
                ChangeAcquirerApproveDTO approveDTO = objectMapper.convertValue(MapUtils.getMap(extra, ApproveConstant.APPROVE_INFO), ChangeAcquirerApproveDTO.class);
                final String merchantSn = approveDTO.getMerchantSn();
                //收单机构
                final String target = approveDTO.getTarget();

                //插入切换收单机构任务并返回对应的任务Id
                ChangeAcquirerRequest request = new ChangeAcquirerRequest();
                request.setMerchantSn(merchantSn);
                request.setAcquirer(target);
                request.setImmediately(approveDTO.getImmediate());
                request.setTradeAppId(approveDTO.getTradeAppId());
                request.setCancellable(Boolean.TRUE);
                acquirerService.applyChangeAcquirer(request);
                final McAcquirerChange change = acquirerChangeDao.getLatestUnFinishedApply(request.getMerchantSn());
                final Integer applyId = Objects.isNull(change) ? null : change.getId();
                if (Objects.nonNull(applyId)) {
                    //记录商户日志
                    recordMerchantLog(approveDTO, merchantSn, approveDTO.getReason());
                    //将applyId记录下来
                    extra.put(ApproveConstant.APPLYID, applyId);
                }
                mcBatchTaskMapper.updateByPrimaryKeySelective(
                        new McBatchTask()
                                .setStatus(1)
                                .setId(mcBatchTask.getId())
                                .setPayload(JSONObject.toJSONString(extra))
                                .setResult("处理中")
                );

            } catch (Exception e) {
                log.error("handleAcquirerApproveChange error mc_batch_task {}  exception", mcBatchTask.getId(), e);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(3).setId(mcBatchTask.getId()).setResult(ExceptionUtil.getThrowableMsg(e)));
                callBack(extra, ExceptionUtil.getThrowableMsg(e), AUDIT_EXECUTE_FAIL);
            }
        });
    }

    private void recordMerchantLog(ChangeAcquirerApproveDTO dto, String merchantSn, String remark) {
        final String target = dto.getTarget();
        final String operator = dto.getOperator();
        final String operatorName = dto.getOperatorName();
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        final ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        final String sourceAcquirer = contractStatus.getAcquirer();
        final String auditSn = dto.getAuditSn();
        businessLogBiz.sendChangeAcquirerNewLog(merchantId, sourceAcquirer, target, operator, operatorName, "审批编号:" + auditSn + " 申请原因:" + remark);
    }
}
