package com.wosai.upay.job.xxljob.batch.providerterminal;

import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.mapper.ProviderTerminalTaskMapper;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import com.wosai.upay.job.repository.ProviderTerminalTaskRepository;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.wosai.upay.job.constant.ProviderTerminalConstants.FAIL;
import static com.wosai.upay.job.constant.ProviderTerminalConstants.SUCCESS;

/**
 * xxl_job_desc: 终端-解绑终端
 * <AUTHOR>
 * @date 2025/4/18
 */
@Slf4j
@Component("UnbindSqbTerminalJobHandler")
public class UnbindSqbTerminalJobHandler extends AbstractBatchJobHandler<ProviderTerminalTask> {

    @Autowired
    private ProviderTerminalTaskMapper taskMapper;
    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;
    @Autowired
    private ProviderTerminalTaskRepository taskRepository;


    @Override
    public List<ProviderTerminalTask> queryTaskItems(BatchJobParam param) {
        long current = System.currentTimeMillis();
        String date = StringUtil.formatDate(current - param.getQueryTime());
        String nowDate = StringUtil.formatDate(current);
        return taskMapper.selectMerchantSnByPriorityAndType(date, nowDate, ProviderTerminalTaskTypeEnum.UNBIND_TERMINAL_ALL_SUB_MCH.getType(), param.getBatchSize());
    }

    @Override
    public String getLockKey(ProviderTerminalTask providerTerminalTask) {
        return "UnbindSqbTerminalJobHandler:" + providerTerminalTask.getId();
    }

    @Override
    public void doHandleSingleData(ProviderTerminalTask providerTerminalTask) {
        try {
            //1，再次查询
            ProviderTerminalTask task = taskMapper.selectByPrimaryKey(providerTerminalTask.getId());
            //2，判断状态
            if (task.getStatus() == SUCCESS || task.getStatus() == FAIL) {
                return;
            }
            //处理task
            providerTerminalBiz.unbindTerminal(task);
        } catch (ContractBizException e) {
            //业务异常
            log.error("解绑终端task业务异常 taskId:{} 商户号:{}", providerTerminalTask.getId(), providerTerminalTask.getMerchant_sn(), e);
            taskRepository.updateTaskStatusById(providerTerminalTask.getId(), FAIL, e.getMessage());
        } catch (Exception e) {
            log.error("解绑终端task业务异常 taskId:{} 商户号:{}", providerTerminalTask.getId(), providerTerminalTask.getMerchant_sn(), e);
        }
    }
}
