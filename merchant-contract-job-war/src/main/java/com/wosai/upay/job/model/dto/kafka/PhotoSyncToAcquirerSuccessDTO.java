/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.model.dto.kafka;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class PhotoSyncToAcquirerSuccessDTO extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 2366884846831678676L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"PhotoSyncToAcquirerSuccessDTO\",\"namespace\":\"com.wosai.upay.job.model.dto.kafka\",\"fields\":[{\"name\":\"merchantSn\",\"type\":[\"null\",\"string\"],\"meta\":\"商户号\"},{\"name\":\"acquirer\",\"type\":[\"string\",\"null\"],\"meta\":\"收单机构\"},{\"name\":\"type\",\"type\":[\"null\",\"int\"],\"meta\":\"类型\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<PhotoSyncToAcquirerSuccessDTO> ENCODER =
      new BinaryMessageEncoder<PhotoSyncToAcquirerSuccessDTO>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<PhotoSyncToAcquirerSuccessDTO> DECODER =
      new BinaryMessageDecoder<PhotoSyncToAcquirerSuccessDTO>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<PhotoSyncToAcquirerSuccessDTO> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<PhotoSyncToAcquirerSuccessDTO> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<PhotoSyncToAcquirerSuccessDTO>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this PhotoSyncToAcquirerSuccessDTO to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a PhotoSyncToAcquirerSuccessDTO from a ByteBuffer. */
  public static PhotoSyncToAcquirerSuccessDTO fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchantSn;
  @Deprecated public java.lang.CharSequence acquirer;
  @Deprecated public java.lang.Integer type;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public PhotoSyncToAcquirerSuccessDTO() {}

  /**
   * All-args constructor.
   * @param merchantSn The new value for merchantSn
   * @param acquirer The new value for acquirer
   * @param type The new value for type
   */
  public PhotoSyncToAcquirerSuccessDTO(java.lang.CharSequence merchantSn, java.lang.CharSequence acquirer, java.lang.Integer type) {
    this.merchantSn = merchantSn;
    this.acquirer = acquirer;
    this.type = type;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchantSn;
    case 1: return acquirer;
    case 2: return type;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchantSn = (java.lang.CharSequence)value$; break;
    case 1: acquirer = (java.lang.CharSequence)value$; break;
    case 2: type = (java.lang.Integer)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchantSn' field.
   * @return The value of the 'merchantSn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchantSn;
  }

  /**
   * Sets the value of the 'merchantSn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchantSn = value;
  }

  /**
   * Gets the value of the 'acquirer' field.
   * @return The value of the 'acquirer' field.
   */
  public java.lang.CharSequence getAcquirer() {
    return acquirer;
  }

  /**
   * Sets the value of the 'acquirer' field.
   * @param value the value to set.
   */
  public void setAcquirer(java.lang.CharSequence value) {
    this.acquirer = value;
  }

  /**
   * Gets the value of the 'type' field.
   * @return The value of the 'type' field.
   */
  public java.lang.Integer getType() {
    return type;
  }

  /**
   * Sets the value of the 'type' field.
   * @param value the value to set.
   */
  public void setType(java.lang.Integer value) {
    this.type = value;
  }

  /**
   * Creates a new PhotoSyncToAcquirerSuccessDTO RecordBuilder.
   * @return A new PhotoSyncToAcquirerSuccessDTO RecordBuilder
   */
  public static com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder newBuilder() {
    return new com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder();
  }

  /**
   * Creates a new PhotoSyncToAcquirerSuccessDTO RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new PhotoSyncToAcquirerSuccessDTO RecordBuilder
   */
  public static com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder newBuilder(com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder other) {
    return new com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder(other);
  }

  /**
   * Creates a new PhotoSyncToAcquirerSuccessDTO RecordBuilder by copying an existing PhotoSyncToAcquirerSuccessDTO instance.
   * @param other The existing instance to copy.
   * @return A new PhotoSyncToAcquirerSuccessDTO RecordBuilder
   */
  public static com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder newBuilder(com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO other) {
    return new com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder(other);
  }

  /**
   * RecordBuilder for PhotoSyncToAcquirerSuccessDTO instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<PhotoSyncToAcquirerSuccessDTO>
    implements org.apache.avro.data.RecordBuilder<PhotoSyncToAcquirerSuccessDTO> {

    private java.lang.CharSequence merchantSn;
    private java.lang.CharSequence acquirer;
    private java.lang.Integer type;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchantSn)) {
        this.merchantSn = data().deepCopy(fields()[0].schema(), other.merchantSn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.acquirer)) {
        this.acquirer = data().deepCopy(fields()[1].schema(), other.acquirer);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.type)) {
        this.type = data().deepCopy(fields()[2].schema(), other.type);
        fieldSetFlags()[2] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing PhotoSyncToAcquirerSuccessDTO instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchantSn)) {
        this.merchantSn = data().deepCopy(fields()[0].schema(), other.merchantSn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.acquirer)) {
        this.acquirer = data().deepCopy(fields()[1].schema(), other.acquirer);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.type)) {
        this.type = data().deepCopy(fields()[2].schema(), other.type);
        fieldSetFlags()[2] = true;
      }
    }

    /**
      * Gets the value of the 'merchantSn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchantSn;
    }

    /**
      * Sets the value of the 'merchantSn' field.
      * @param value The value of 'merchantSn'.
      * @return This builder.
      */
    public com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchantSn = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchantSn' field has been set.
      * @return True if the 'merchantSn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchantSn' field.
      * @return This builder.
      */
    public com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder clearMerchantSn() {
      merchantSn = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'acquirer' field.
      * @return The value.
      */
    public java.lang.CharSequence getAcquirer() {
      return acquirer;
    }

    /**
      * Sets the value of the 'acquirer' field.
      * @param value The value of 'acquirer'.
      * @return This builder.
      */
    public com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder setAcquirer(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.acquirer = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'acquirer' field has been set.
      * @return True if the 'acquirer' field has been set, false otherwise.
      */
    public boolean hasAcquirer() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'acquirer' field.
      * @return This builder.
      */
    public com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder clearAcquirer() {
      acquirer = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'type' field.
      * @return The value.
      */
    public java.lang.Integer getType() {
      return type;
    }

    /**
      * Sets the value of the 'type' field.
      * @param value The value of 'type'.
      * @return This builder.
      */
    public com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder setType(java.lang.Integer value) {
      validate(fields()[2], value);
      this.type = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'type' field has been set.
      * @return True if the 'type' field has been set, false otherwise.
      */
    public boolean hasType() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'type' field.
      * @return This builder.
      */
    public com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO.Builder clearType() {
      type = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public PhotoSyncToAcquirerSuccessDTO build() {
      try {
        PhotoSyncToAcquirerSuccessDTO record = new PhotoSyncToAcquirerSuccessDTO();
        record.merchantSn = fieldSetFlags()[0] ? this.merchantSn : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.acquirer = fieldSetFlags()[1] ? this.acquirer : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.type = fieldSetFlags()[2] ? this.type : (java.lang.Integer) defaultValue(fields()[2]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<PhotoSyncToAcquirerSuccessDTO>
    WRITER$ = (org.apache.avro.io.DatumWriter<PhotoSyncToAcquirerSuccessDTO>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<PhotoSyncToAcquirerSuccessDTO>
    READER$ = (org.apache.avro.io.DatumReader<PhotoSyncToAcquirerSuccessDTO>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
