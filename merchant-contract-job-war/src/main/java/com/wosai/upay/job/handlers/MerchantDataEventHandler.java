package com.wosai.upay.job.handlers;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.MerchantChangeDataConstant;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.acquirer.IAcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.config.ApolloConfig;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.FuYouConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.paramContext.ParamContextBiz.*;
import static com.wosai.upay.job.model.ContractEvent.*;

/**
 * 进件通过,商户认证驳回时,可能生成该事件,
 * 1.
 */
@Component
@Slf4j
@Order(98)
public class MerchantDataEventHandler extends AbstractEventHandler<Void> {
    @Autowired
    private ParamContextBiz paramContextBiz;
    @Autowired
    private ContractEventMapper contractEventMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private CommonEventHandler commonEventHandler;
    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;
    @Autowired
    private ContractTaskBiz contractTaskBiz;
    @Autowired
    private MerchantChangeDataBiz merchantChangeDataBiz;
    @Autowired
    private MerchantBusinessLicenseService businessLicenseService;

    @Autowired
    private EventTaskConflictBiz conflictBiz;
    @Autowired
    private AuthAndComboTaskBiz authAndComboTaskBiz;

    private boolean isProd;

    @Autowired
    private Environment environment;

    @PostConstruct
    public void init() {
        isProd = Arrays.asList(environment.getActiveProfiles()).contains("prod");
    }

    //营业执照变更字段
    private static final List<String> updateFields = Arrays.asList("type", "photo", "number", "name", "business_scope",
            "validity", "address", "registered_legal_person_name", "letter_of_authorization",
            "trade_license", "legal_person_id_type", "legal_person_id_card_front_photo",
            "legal_person_id_card_back_photo", "legal_person_name", "legal_person_id_number",
            "id_validity");

    @Override
    protected void handleError(ContractEvent event, Exception e) throws Exception {
        commonEventHandler.handleError(event, e);
    }

    @Transactional
    @Override
    public Void doHandle(ContractEvent event) throws Exception {

        if (!preCheck(event)) {
            return null;
        }
        Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(event.getMerchant_sn(), event);

        handleUpdate(event, paramContext);
        return null;
    }

    @Override
    public boolean supports(ContractEvent contractEvent) {
        return contractEvent.getEvent_type() == ContractEvent.OPT_TYPE_UPDATE_MERCHANT_DATA;
    }

    private boolean preCheck(ContractEvent event) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(event.getMerchant_sn());
        if (contractStatus == null || contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS) {
            return false;
        }
        MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
        dto.createCriteria().andMerchant_snEqualTo(event.getMerchant_sn()).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue());
        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExample(dto);
        if (!CollectionUtils.isEmpty(merchantProviderParams)) {
            // 富友23点50分到0点20分不允许提交
            LocalTime currentTime = LocalTime.now();

            // 设置检查的开始时间和结束时间
            LocalTime startTime = LocalTime.of(23, 50);
            LocalTime endTime = LocalTime.of(0, 20);

            // 比较当前时间是否在指定范围内
            if (currentTime.isAfter(startTime) || currentTime.isBefore(endTime)) {
                return false;
            }
        }
        return !conflictBiz.conflictWithPendingTasks(event);
    }

    private void handleUpdate(ContractEvent event, Map<String, Object> paramContext) {
        String merchantSn = event.getMerchant_sn();
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andDeletedEqualTo(false);
        List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);
        records = commonEventHandler.setRule(records);

        if (CollectionUtils.isEmpty(records)) {
            event.setStatus(6).setResult("该商户无报备记录，不生成任何更新任务");
            contractEventMapper.updateByPrimaryKeySelective(event);
            log.info("该商户 {} 无报备记录，不生成任何更新任务", event.getMerchant_sn());
            return;
        }

        records = commonEventHandler.distinctByRule(records);
        List<ContractSubTask> contractSubTaskList = new ArrayList<>();

        Map bankAccountNew = MapUtils.getMap(paramContext, KEY_BANK_ACCOUNT);
        //先检查下此银行卡是否为默认银行卡,不是就换成这个新卡.
        Map bankAccount = merchantService.getMerchantBankAccountByMerchantId((String) BeanUtil.getNestedProperty(paramContext, "merchant.id"));
        boolean changeCard = false;
        boolean updateLicense = false;
        if (!WosaiStringUtils.equals(MapUtils.getString(bankAccount, MerchantBankAccount.NUMBER), MapUtils.getString(bankAccountNew, MerchantBankAccount.NUMBER))) {
            //当前默认卡号和新卡号不同,触发换卡
            ContractEvent eventChangeCard = new ContractEvent();
            BeanUtils.copyProperties(event, eventChangeCard);
            eventChangeCard.setEvent_type(OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS);
            List<ContractSubTask> subTasks = createSubTask(records, eventChangeCard, paramContext);
            if (WosaiCollectionUtils.isNotEmpty(subTasks)) {
                contractSubTaskList.addAll(subTasks);
                changeCard = true;
            }
        }

        if (needContractBusinessLicense(paramContext)) {
            ContractEvent eventUpdateLicense = new ContractEvent();
            BeanUtils.copyProperties(event, eventUpdateLicense);
            eventUpdateLicense.setEvent_type(OPT_TYPE_UPDATE_BUSINESS_LICENSE);
            List<ContractSubTask> subTasks = createSubTask(records, eventUpdateLicense, paramContext);
            if (WosaiCollectionUtils.isNotEmpty(subTasks)) {
                contractSubTaskList.addAll(subTasks);
                updateLicense = true;
            }

        }

        //卡号一致,也无营业执照更新,只进行商户基本信息变更
        if (!changeCard && !updateLicense) {
            ContractEvent eventChangeMerchant = new ContractEvent();
            BeanUtils.copyProperties(event, eventChangeMerchant);
            eventChangeMerchant.setEvent_type(OPT_TYPE_MERCHANT_BASIC_INFORMATION);
            List<ContractSubTask> subTasks = createSubTask(records, eventChangeMerchant, paramContext);
            if (WosaiCollectionUtils.isNotEmpty(subTasks)) {
                contractSubTaskList.addAll(subTasks);
            }
        }

        //只一步是为了去重
        Set<ContractSubTask> set = new HashSet<>(contractSubTaskList);
        contractSubTaskList = new ArrayList<>(set);

        int affectCount = (int) contractSubTaskList.stream().filter(subTask -> subTask.getStatus_influ_p_task() == 1).count();
        Map eventMsg = JSON.parseObject(event.getEvent_msg(), Map.class);
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(event.getMerchant_sn());
        ContractSubTask weixinSubTask = null;
        ContractSubTask syncSubTask = null;
        Date priority = null;
        if (MapUtils.getBooleanValue(eventMsg, MerchantChangeDataConstant.RE_CONTRACT, false)) {
            //需重新报备微信子商户号
            //reContractService.reContractInNormalChannel(context.getMerchantNew().getSn(), "结算id变更,重新报备");
            IAcquirerBiz acquirerBiz = composeAcquirerBiz.getAcquirerBiz(contractStatus.getAcquirer());
            String rule = acquirerBiz.getMerchantNormalWxRule(event.getMerchant_sn());
            if (AcquirerTypeEnum.FU_YOU.getValue().equals(contractStatus.getAcquirer()) && ApolloConfig.getFuYouSpecialIndustryApplySwitch()) {
                // 修改费率
                Map merchantMap = WosaiMapUtils.getMap(paramContext, "merchant");
                authAndComboTaskBiz.applyFuYouNewCombo(buildAuthAndComboBean(merchantMap, WosaiMapUtils.getString(eventMsg, ContractEvent.FORM_BODY)));
                // 根据行业选择一个报备规则
                Map mapping = ApolloConfig.getNetFuYouSpecChNlTypeAndBusinessCodeAndChannelNoMapping(WosaiMapUtils.getString(merchantMap, "industry"));
                String channelNo = WosaiMapUtils.getString(mapping, FuYouConstant.CHANNEL_NO);
                if (StringUtils.isNotEmpty(channelNo)) {
                    // 获取d+1凌晨1点时间
                    rule = FuYouConstant.WECHAT_CHANNEL_PREFIX + channelNo;
                } else {
                    rule = FuYouConstant.WECHAT_CHANNEL_NORMAL;
                }
                priority = get1AmOfNextDay();
            }
            ContractRule contractRule = ruleContext.getContractRule(rule);
            String ruleGroupId = ProviderUtil.switchAcquirerToRuleGroupId(contractRule.getAcquirer());

            weixinSubTask = new ContractSubTask().setChange_config(0).setDefault_channel(0)
                    .setPayway(contractRule.getPayway())
                    .setContract_rule(contractRule.getRule()).setStatus_influ_p_task(1)
                    .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT)
                    .setChannel(providerFactory.convertToBeanName(contractRule.getProvider()))
                    .setMerchant_sn(merchantSn)
                    .setRule_group_id(ruleGroupId);
            //不在列表的,才对 Contract_id赋值
            if (!ProviderUtil.CHANNELS.contains(weixinSubTask.getChannel())) {
                weixinSubTask.setContract_id(contractRule.getChannelNo());
            }
            if (AcquirerTypeEnum.HAI_KE.getValue().equals(contractStatus.getAcquirer())) {
                syncSubTask = new ContractSubTask().setChange_config(0).setDefault_channel(0)
                        .setPayway(contractRule.getPayway()).setContract_id(contractRule.getChannelNo())
                        .setContract_rule(contractRule.getRule() + "-sync").setStatus_influ_p_task(1)
                        .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT)
                        .setChannel(providerFactory.convertToBeanName(contractRule.getProvider()))
                        .setMerchant_sn(merchantSn)
                        .setRule_group_id(ruleGroupId);
            }
        }

        if (contractSubTaskList.isEmpty() && weixinSubTask == null) {
            //无任何子任务生成
            event.setStatus(ContractEvent.STATUS_SUCCESS).setResult("商户信息变更事件无子任务生成");
            contractEventMapper.updateByPrimaryKeySelective(event);
            merchantChangeDataBiz.sendMerchantChangeDataStatusChange(merchantSn, MapUtils.getString(MapUtils.getMap(paramContext, KEY_MERCHANT), "id"),
                    MerchantChangeDataConstant.COMMON_STATUS_PROCESS,
                    MerchantChangeDataConstant.COMMON_STATUS_SUCCESS, "无子任务生成",
                    MapUtils.getString(paramContext, MerchantChangeDataConstant.APPLY_SOURCE, ""), MapUtils.getBooleanValue(eventMsg, MerchantChangeDataConstant.RE_CONTRACT, false));

            return;
        }

        if (weixinSubTask != null) {
            affectCount++;
        }
        if (syncSubTask != null) {
            affectCount++;
        }

        String merchantName = BeanUtil.getPropString(paramContext.get("merchant"), Merchant.NAME);
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(event.getMerchant_sn())
                .setMerchant_name(merchantName)
                .setType(commonEventHandler.getContractTaskType(event))
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(affectCount)
                .setAffect_status_success_task_count(0)
                .setRule_group_id(event.getRule_group_id())
                .setPriority(priority)
                //如果影响任务状态的子任务数量为0,则直接设为成功
                .setStatus(Objects.equals(affectCount, 0) ? TaskStatus.SUCCESS.getVal() : 0);

        contractTaskBiz.insert(contractTask);
        Long pTaskId = contractTask.getId();

        for (ContractSubTask sub : contractSubTaskList) {
            sub.setP_task_id(pTaskId);
            contractSubTaskMapper.insert(sub);
        }


        if (weixinSubTask != null) {
            List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectByPTaskId(pTaskId);
            List<ContractSubTask> subTaskList = contractSubTasks.stream().filter(sub -> sub.getStatus_influ_p_task() == 1).collect(Collectors.toList());
            weixinSubTask.setP_task_id(pTaskId);
            if (WosaiCollectionUtils.isNotEmpty(subTaskList)) {
                weixinSubTask.setSchedule_dep_task_id(subTaskList.get(0).getId());
            } else {
                weixinSubTask.setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue());
            }
            contractSubTaskMapper.insert(weixinSubTask);
            if (syncSubTask != null) {
                syncSubTask.setP_task_id(pTaskId);
                syncSubTask.setSchedule_dep_task_id(weixinSubTask.getId());
                contractSubTaskMapper.insert(syncSubTask);
            }
        }

        event.setTask_id(contractTask.getId());
        event.setStatus(ContractEvent.STATUS_SUCCESS);
        contractEventMapper.updateByPrimaryKeySelective(event);
        merchantChangeDataBiz.sendMerchantChangeDataStatusChange(merchantSn, MapUtils.getString(MapUtils.getMap(paramContext, KEY_MERCHANT), "id"),
                MerchantChangeDataConstant.COMMON_STATUS_PROCESS,
                contractTask.getStatus().equals(TaskStatus.SUCCESS.getVal()) ? MerchantChangeDataConstant.COMMON_STATUS_SUCCESS : MerchantChangeDataConstant.COMMON_STATUS_PROCESS, null,
                MapUtils.getString(paramContext, MerchantChangeDataConstant.APPLY_SOURCE, ""), MapUtils.getBooleanValue(eventMsg, MerchantChangeDataConstant.RE_CONTRACT, false));
    }

    private Date get1AmOfNextDay() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextDayOneAM = now;
        // 获取第二天的时间
        if (isProd) {// 生产
            LocalDateTime nextDay = now.plusDays(1);

            // 设置时间为凌晨1点
            nextDayOneAM = nextDay.with(LocalTime.of(1, 0));
        }

        // 选择一个时区，例如系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();

        // 将 LocalDateTime 转换为 ZonedDateTime，这样它就包含了时区信息
        ZonedDateTime zonedDateTime = nextDayOneAM.atZone(zoneId);

        // 最后，将 ZonedDateTime 转换为 Date
        return Date.from(zonedDateTime.toInstant());
    }

    private AuthAndComboTask buildAuthAndComboBean(Map merchantMap, String formBody) {
        AuthAndComboTask authAndComboTask = new AuthAndComboTask();
        authAndComboTask.setMerchant_id(WosaiMapUtils.getString(merchantMap, "id"));
        authAndComboTask.setMerchant_sn(WosaiMapUtils.getString(merchantMap, "sn"));
        authAndComboTask.setForm_body(formBody);
        return authAndComboTask;
    }


    private List<ContractSubTask> createSubTask(List<MerchantProviderParams> records, ContractEvent event, Map<String, Object> paramContext) {

        List<ContractSubTask> contractSubTaskList = new ArrayList<>(records.size());
        Integer affectCount = 0;
        for (MerchantProviderParams record : records) {
            String rule = record.getContract_rule();
            String groupId = record.getRule_group_id();
            ContractRule contractRule;
            //获取默认规则
            if (WosaiStringUtils.isEmpty(rule)) {
                contractRule = ruleContext.getDefaultRule(String.valueOf(record.getProvider()), record.getPayway(), record.getChannel_no());
                if (contractRule == null) {
                    log.error("存量商户无法加载默认规则 {}", record);
                    continue;
                }
            } else {
                try {
                    //获取具体规则信息
                    contractRule = ruleContext.getContractRule(rule);
                } catch (CommonPubBizException e) {
                    log.info("更新处理事件规则被禁用或规则不存在无需更新,{}", record);
                    continue;
                }
            }
            //生成任务
            BasicProvider provider = providerFactory.getProviderByContractRule(contractRule);
            if (Objects.isNull(provider)) {
                log.error("无相关provider, 商户号 : {} ,  ContractRule : {}", event.getMerchant_sn(), JSON.toJSONString(contractRule));
                continue;
            }

            ContractSubTask contractSubTask = provider.produceTaskByRule(event.getMerchant_sn(), event, paramContext, contractRule);
            if (contractSubTask != null) {
                if (contractSubTask.getStatus_influ_p_task() == 1) {
                    affectCount++;
                }
                contractSubTask.setRule_group_id(groupId);
                contractSubTaskList.add(contractSubTask);
            }
        }

        return contractSubTaskList;
    }

    private boolean needContractBusinessLicense(Map<String, Object> paramContext) {
        Map merchant = (Map) paramContext.get(KEY_MERCHANT);
        //原表营业执照
        Map<String, Object> businessLicense = businessLicenseService.getBusinessLicenseByMerchantId(MapUtils.getString(merchant, DaoConstants.ID));

        //中间表营业执照
        Map mcBusinessLicense = (Map) paramContext.get(KEY_BUSINESS_LICENCE);
        return checkDiffer(mcBusinessLicense, businessLicense);

    }

    private boolean checkDiffer(Map mcLicense, Map license) {
        //以下字段更新 认为需要提交营业执照更新
        for (String field : updateFields) {
            String update = WosaiMapUtils.getString(mcLicense, field);
            if (StringUtils.isNotBlank(update)) {
                String original = WosaiMapUtils.getString(license, field);
                if (StringUtils.isBlank(original) || !update.equals(original)) {
                    return true;
                }
            }
        }
        return false;
    }
}
