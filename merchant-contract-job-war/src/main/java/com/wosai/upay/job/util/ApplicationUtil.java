package com.wosai.upay.job.util;

import avro.shaded.com.google.common.collect.Lists;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.application.ZhimaAppCreateReq;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.alipay.ZhimaMCreditCreate;

import java.util.List;
import java.util.UUID;

/**
 * Created by hzq on 19/11/27.
 */
public class ApplicationUtil {

    public static ZhimaMCreditCreate toZhimaCreate(ZhimaAppCreateReq req, String smid) {
        ZhimaMCreditCreate create = new ZhimaMCreditCreate();
        create.setSolution_id("solution_004");
        create.setSmid(smid);
        create.setBiz_no(UUID.randomUUID().toString());
        create.setCreate_type("indirect");
        create.setBase_info_config(new ZhimaMCreditCreate.BaseInfoConfig()
                .setService_name(req.getService_name())
                .setLogo(req.getLogo())
                .setContact_email(req.getContact_email())
                .setContact_phone(req.getContact_phone()));
        List<ZhimaMCreditCreate.CategoryRiskInfo> categoryRiskInfos = Lists.newArrayList(new ZhimaMCreditCreate.CategoryRiskInfo()
                .setCategory_code("ZMSC_1_1_8")
                .setCategory_name("充电宝")
                .setScore(req.getScore())
                .setRisk_policy(req.getRisk_policy())
                .setGlobal_orders_limit_number(req.getGlobal_orders_limit_number())
                .setGlobal_quota_switch(req.getGlobal_quota_switch()));
        create.setRisk_config(new ZhimaMCreditCreate.RiskConfig()
                .setCategory_risks(categoryRiskInfos)
                //quota_gradient_rule 接口文档是错误的 这个字段必须填 额度最大值是200
                .setQuota_gradient_rule(new ZhimaMCreditCreate.QuotaGradientRule()
                        .setScore_400(200)
                        .setScore_450(200)
                        .setScore_500(200)
                        .setScore_550(200)
                        .setScore_600(200)
                        .setScore_650(200)
                        .setScore_700(200)
                        .setScore_750(200)
                        .setScore_800(200)
                        .setScore_850(200)
                        .setScore_900(200)
                        .setScore_950(200)));
        create.setPromise_config(new ZhimaMCreditCreate.PromiseConfig()
                .setMerchant_service_phone(req.getMerchant_service_phone())
                .setMoney_operation_type(req.getMoney_operation_type())
                .setMoney_operation_value(req.getMoney_operation_value())
                .setProduct_operation_type(req.getProduct_operation_type())
                .setProduct_operation_value(req.getProduct_operation_value())
                .setAction_operation_type(req.getAction_operation_type())
                .setAction_operation_value(req.getAction_operation_value())
                .setMerchant_service_url(req.getMerchant_service_url())
                .setButton_end(req.getButton_end())
                .setButton_postpone(req.getButton_postpone())
                .setButton_replace(req.getButton_replace())
                .setButton_buy(req.getButton_buy())
                .setButton_repair(req.getButton_repair())
                .setButton_installment(req.getButton_installment())
                .setFulfillment_days(req.getFulfillment_days()));
        return create;
    }


    public static CommonResult toCommonRs(ContractResponse resp) {
        return new CommonResult(resp.getCode(), resp.getMessage(), resp.getTradeParam());
    }

    public static CommonResult toCommonRs(String res) {
        if (StringUtils.isEmpty(res)) {
            return new CommonResult(CommonResult.ERROR, "响应为空", null);
        }
        if (res.contains(CommonResult.ERROR + "")) {
            return new CommonResult(CommonResult.ERROR, res, res);
        }
        return new CommonResult(CommonResult.BIZ_FAIL, res, res);
    }
}
