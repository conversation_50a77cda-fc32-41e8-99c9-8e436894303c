package com.wosai.upay.job.xxljob.batch.contracttask;

import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * xxl_job_desc: 进件任务-处理富友待回调子任务
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@Slf4j
@Component("ContractTaskFuyouWaitForCallbackJobHandler")
public class ContractTaskFuyouWaitForCallbackJobHandler extends AbstractBatchJobHandler<ContractSubTask> {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private SubTaskHandlerContext subTaskHandlerContext;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    public List<ContractSubTask> queryTaskItems(BatchJobParam param) {
        return contractSubTaskMapper.getNoInfluenceSubTaskWaitCallBack(param.getBatchSize(), StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()));
    }

    @Override
    public String getLockKey(ContractSubTask subTask) {
        return "ContractTaskFuyouWaitForCallbackJobHandler:" + subTask.getId();
    }

    @Override
    public void doHandleSingleData(ContractSubTask subTask) {
        try {
            //1，再次查询
            ContractSubTask subTaskLast = contractSubTaskMapper.selectByPrimaryKey(subTask.getId());
            //2，判断状态
            if (subTaskLast.getStatus().equals(TaskStatus.SUCCESS.getVal()) || subTaskLast.getStatus().equals(TaskStatus.FAIL.getVal())) {
                return;
            }
            if (ProviderUtil.FUYOU_CHANNEL.equals(subTaskLast.getChannel()) && Objects.equals(subTaskLast.getPayway(), PaywayEnum.UNIONPAY.getValue())) {
                ContractTask task = contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id());
                subTaskHandlerContext.handle(task, subTaskLast);
            }

        } catch (Exception e) {
            log.error("查询不影响主任务的子任务失败，商户号{},业务类型{}", subTask.getMerchant_sn(), subTask.getTask_type(), e);
            chatBotUtil.sendMessageToContractWarnChatBot("查询不影响主任务的子任务失败,商户号:" + subTask.getMerchant_sn() +
                    ",业务类型:" + subTask.getTask_type() + "错误信息:" + ExceptionUtil.getThrowableMsg(e));
        }
    }
}
