package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.helper.RecordViewProcessHelper;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.callback.umb.res.UMBAuditCallBackRes;
import com.wosai.upay.job.model.callback.umb.res.UMBSignCallBackRes;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.providers.UmbProvider;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.enume.umb.UMBContractStatus;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.UMBParam;
import com.wosai.upay.merchant.contract.model.umb.UMBReq;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Component
@Slf4j
@RequiredArgsConstructor
public class UMBCallBackService {
    private final ProviderTradeParamsService providerTradeParamsService;
    private final ContractSubTaskMapper contractSubTaskMapper;
    private final QueryContractStatusHandler queryContractStatusHandler;
    private final UmbProvider umbProvider;
    private final RecordViewProcessHelper recordViewProcessHelper;
    private final BankDirectApplyMapper bankDirectApplyMapper;
    private final ContractTaskMapper contractTaskMapper;

    //签约回调结果处理
    public void handleSignCallBack(UMBReq.BodyAndHead<UMBSignCallBackRes> callBack, UMBParam umbParam) {
        //处理中直接返回,等待处理通过或者失败的回调.
        if (callBack.getHead().processing()) {
            return;
        }
        UMBContractStatus contractStatus = UMBContractStatus.CALLBACK;
        boolean isSuccess = callBack.getHead().success();
        String contractId = callBack.getBody().getMerid();
        log.info("中投科信回调处理，商户门店号:{}，回调参数{}", contractId, JSONObject.toJSONString(callBack));
        ContractSubTask subTask = contractSubTaskMapper.selectByContractId(contractId);
        if (subTask == null) {
            log.error("中投科信回调未找到对应的子任务,回调参数:{}", JSONObject.toJSONString(callBack));
            return;
        }
        if (TaskStatus.isFinish(subTask.getStatus())) {
            return;
        }
        Integer taskType = subTask.getTask_type();
        if (taskType == 3) {
            HandleQueryStatusResp statusResp = new HandleQueryStatusResp();
            if (isSuccess) {
                statusResp.setSuccess(true);
                statusResp.setMessage("费率修改成功");
            } else {
                statusResp.setFail(true);
                statusResp.setMessage(callBack.getHead().getRespmsg());
            }
            queryContractStatusHandler.handTaskAndSubTask(subTask, statusResp);
        } else {
            ContractResponse contractResponse = new ContractResponse();
            if (isSuccess) {
                contractSubTaskMapper.updateByPrimaryKey(
                        new ContractSubTask().setId(subTask.getId())
                                .setResponse_body(JSON.toJSONString(callBack))
                                .setResult(contractStatus.getSuccessDesc())
                );
                contractResponse.setCode(Constant.RESULT_CODE_SUCCESSS);
                final Long pTaskId = subTask.getP_task_id();
                final BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(pTaskId);
                recordViewProcessHelper.recordViewProcess(apply, 11, new Date());
            } else {
                contractSubTaskMapper.updateByPrimaryKey(
                        new ContractSubTask().setId(subTask.getId())
                                .setStatus(TaskStatus.FAIL.getVal())
                                .setResponse_body(JSON.toJSONString(callBack))
                                .setResult(contractStatus.getFailDesc())
                );
                contractResponse.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION);
            }
            contractResponse.setMessage(callBack.getHead().getRespmsg());
            contractResponse.setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(callBack), Map.class));
            this.signCallbackHandle(subTask, contractResponse, contractStatus, umbParam, contractId);
        }
    }

    //进件审核回调处理
    public void handleAuditPassCallBack(UMBReq.BodyAndHead<UMBAuditCallBackRes> callBack) {
        UMBContractStatus contractStatus = UMBContractStatus.CONTRACT;
        boolean isSuccess = callBack.getBody().isAuditPass();
        String contractId = callBack.getBody().getMerid();
        log.info("中投科信回调处理，商户门店号:{}，回调参数{}", contractId, JSONObject.toJSONString(callBack));
        ContractSubTask subTask = contractSubTaskMapper.selectByContractId(contractId);
        if (subTask == null) {
            log.error("中投科信回调未找到对应的子任务,回调参数:{}", JSONObject.toJSONString(callBack));
            return;
        }
        if (TaskStatus.isFinish(subTask.getStatus())) {
            return;
        }
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id());
        ContractResponse contractResponse = new ContractResponse();
        if (isSuccess) {
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask().setId(subTask.getId())
                            .setResponse_body(JSON.toJSONString(callBack))
                            .setResult(contractStatus.getSuccessDesc())
            );
            umbProvider.updateStatusToContractTask(contractTask, contractStatus, contractId, contractResponse);
        } else {
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask().setId(subTask.getId())
                            .setStatus(TaskStatus.FAIL.getVal())
                            .setResponse_body(JSON.toJSONString(callBack))
                            .setResult(contractStatus.getSuccessDesc())
            );
            contractResponse.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION);
        }
        contractResponse.setMessage(callBack.getBody().getRemark());
        contractResponse.setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(callBack), Map.class));
        this.auditCallbackHandle(subTask, contractResponse, contractStatus);
    }

    //进件审核回调处理
    public void handleModifyAuditPassCallBack(UMBReq.BodyAndHead<UMBAuditCallBackRes> callBack) {
        boolean isSuccess = callBack.getBody().isAuditPass();
        String contractId = callBack.getBody().getMerid();
        log.info("中投科信商户信息修改回调处理，商户门店号:{}，回调参数{}", contractId, JSONObject.toJSONString(callBack));
        ContractSubTask subTask = contractSubTaskMapper.selectByContractId(contractId);
        if (subTask == null) {
            log.error("中投科信商户信息修改回调未找到对应的子任务,回调参数:{}", JSONObject.toJSONString(callBack));
            return;
        }
        if (TaskStatus.isFinish(subTask.getStatus())) {
            return;
        }
        ContractResponse contractResponse = new ContractResponse();
        if (isSuccess) {
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask().setId(subTask.getId())
                            .setResponse_body(JSON.toJSONString(callBack))
                            .setResult("更新商户信息成功")
            );
            contractResponse.setCode(Constant.RESULT_CODE_SUCCESSS);
        } else {
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask().setId(subTask.getId())
                            .setStatus(TaskStatus.FAIL.getVal())
                            .setResponse_body(JSON.toJSONString(callBack))
                            .setResult(callBack.getBody().getRemark())
            );
            contractResponse.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION);
        }
        contractResponse.setMessage(callBack.getBody().getRemark());
        contractResponse.setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(callBack), Map.class));
        this.auditCallbackHandle(subTask, contractResponse, UMBContractStatus.SIGN);
    }

    public void auditCallbackHandle(ContractSubTask subTask, ContractResponse contractResponse, UMBContractStatus status) {
        HandleQueryStatusResp statusResp = umbProvider.doHandleContractStatus(subTask, contractResponse, status);
        queryContractStatusHandler.handTaskAndSubTask(subTask, statusResp);
    }

    public void signCallbackHandle(ContractSubTask subTask, ContractResponse contractResponse, UMBContractStatus status, UMBParam umbParam, String contractId) {
        HandleQueryStatusResp statusResp = umbProvider.doHandleContractStatus(subTask, contractResponse, status);
        queryContractStatusHandler.handTaskAndSubTask(subTask, statusResp);
    }
}
