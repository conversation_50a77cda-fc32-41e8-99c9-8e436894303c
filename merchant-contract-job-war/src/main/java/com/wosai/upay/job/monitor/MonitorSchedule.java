package com.wosai.upay.job.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019-12-16
 * 监控线程心跳监控
 */
@Component
@Slf4j
public class MonitorSchedule {

    @Autowired
    private MonitorWarn monitorWarn;


    @Scheduled(cron = "0/30 * * * * ?")
    public void processMonitorWarnThreads() {
        monitorWarn.schedule();
    }


}
