package com.wosai.upay.job.xxljob.direct.multievent;

import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.MultiEventBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MultiProviderContractEventMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.service.TaskResultServiceImpl;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.wosai.upay.job.util.StringUtil.formatDate;

/**
 * xxl_job_desc: MultiEvent-设置次通道为默认
 * 设置次通道未默认通道
 *
 * <AUTHOR>
 * @date 2025/4/27
 */
@Slf4j
@Component("MultiEventSetSecondDefaultJobHandler")
public class MultiEventSetSecondDefaultJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private MultiProviderContractEventMapper multiEventMapper;
    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private MultiEventBiz multiEventBiz;
    @Autowired
    private TaskResultServiceImpl taskResultService;

    @Override
    public String getLockKey() {
        return "MultiEventSetSecondDefaultJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            final List<MultiProviderContractEvent> events = multiEventMapper.selectProcessingNetInMultiEvents(formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
            for (MultiProviderContractEvent event : events) {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                try {
                    setSecondaryProviderToDefault(event);
                } catch (Exception e) {
                    log.error("setSecondaryProviderDefaultChannel process error", e);
                    chatBotUtil.sendMessageToContractWarnChatBot(event.getMerchant_sn() + "设置次通道为默认通道事件处理异常 " + ExceptionUtil.getThrowableMsg(e));
                }
            }
        } catch (Exception e) {
            log.error("setSecondaryProviderDefaultChannel process error", e);
            chatBotUtil.sendMessageToContractWarnChatBot("设置次通道为默认通道事件处理异常 " + ExceptionUtil.getThrowableMsg(e));
        }
    }

    private void setSecondaryProviderToDefault(MultiProviderContractEvent event) {
        // 没有次任务就不用往下走了
        if (WosaiStringUtils.isEmpty(event.getSecondary_group_id()) || event.getSecondary_task_id() == null) {
            return;
        }
        // 如果contract_status已经是成功的了就不用去设置了
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(event.getMerchant_sn());
        if (contractStatus != null && ContractStatus.STATUS_SUCCESS == contractStatus.getStatus()) {
            return;
        }
        // 判断是否次任务已经成功并且已经过了该渠道配置的时间
        ContractTask secondaryTask = contractTaskMapper.selectByPrimaryKey(event.getSecondary_task_id());
        long timeAfterEventCreated = System.currentTimeMillis() - event.getCreate_at().getTime();
        if (secondaryTask != null && TaskStatus.SUCCESS.getVal().equals(secondaryTask.getStatus()) && timeAfterEventCreated > multiEventBiz.getPrimaryContractTimeOutTime(event)) {
            taskResultService.setDefaultProvider(secondaryTask, secondaryTask.getResult(), TaskStatus.SUCCESS.getVal(), event);
        }
    }
}
