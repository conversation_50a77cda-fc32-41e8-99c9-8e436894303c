package com.wosai.upay.job.xxljob.direct.alidirect;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.alipay.api.request.AlipayOpenAgentCreateRequest;
import com.alipay.api.response.AlipayOpenAgentCreateResponse;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.direct.AliDirectParamBuilder;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.enume.AliDirectApplyStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.AliDirectApplyMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.AliDirectApply;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.monitor.MonitorObject;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.service.AliPayDirectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.List;

/**
 * xxl_job_desc: 支付宝直连-创建事务
 * <AUTHOR>
 * @date 2025/4/10
 */
@Slf4j
@Component("AliDirectCreateBatchNoJobHandler")
public class AliDirectCreateBatchNoJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private AliPayDirectService aliPayDirectService;

    @Autowired
    private AliDirectParamBuilder aliParamBuilder;

    @Autowired
    private AliDirectApplyMapper applyMapper;

    @Autowired
    private DirectStatusBiz directStatusBiz;

    @Autowired
    private ContractTaskBiz taskBiz;

    @Autowired
    private MonitorLog monitorLog;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Value("${ali.direct}")
    private String aliDirectDevCode;

    @Override
    public String getLockKey() {
        return "AliDirectCreateBatchNoJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        Long queryTime = param.getQueryTime();
        Integer queryLimit = param.getBatchSize();
        long currentTime = System.currentTimeMillis();
        List<AliDirectApply> applyList = applyMapper.getAppliesByPrioirtyAndStatus(
                DateFormatUtils.format(currentTime - queryTime, "yyyy-MM-dd HH:mm:ss"),
                DateFormatUtils.format(currentTime, "yyyy-MM-dd HH:mm:ss"),
                Lists.newArrayList(AliDirectApplyStatus.UN_SUBMIT.getVal()),
                queryLimit);
        applyList.forEach(apply -> {
            try {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                doCreateBatchNo(apply);
            } catch (Throwable e) {
                log.error("create batch error {} ", apply.getMerchant_sn(), e);
                monitorLog.recordMonitor("支付宝直连", apply.getMerchant_sn() + "支付宝直连创建事务" + apply.getId() + "异常" + ExceptionUtil.getThrowableMsg(e));
            }
        });
    }

    private void doCreateBatchNo(AliDirectApply apply) {
        String merchantSn = apply.getMerchant_sn();
        long start = System.currentTimeMillis();
        int code = 200;
        String message = "";
        try {
            //1. 提交到支付宝获取batch_no
            AliCommResponse<AlipayOpenAgentCreateRequest, AlipayOpenAgentCreateResponse> response = aliPayDirectService.alipayOpenAgentCreate(aliParamBuilder.buildCreateParam(apply));
            code = response.getCode();
            message = response.getMessage();
            //3. 判断是否成功
            if (response.isSuccess()) {
                //3.1  成功 更新申请单为待提交  报备任务为审核中 报备状态为审核中
                transactionTemplate.executeWithoutResult(tx -> {
                    applyMapper.updateByPrimaryKeySelective(new AliDirectApply().setId(apply.getId()).setRequest_body(JSON.toJSONString(response.getReq()))
                            .setResponse_body(JSON.toJSONString(response.getResp())).setStatus(AliDirectApplyStatus.WAIT_FOR_SUBMITTING.getVal()).setBatch_no(response.getResp().getBatchNo()));
                    taskBiz.update(new ContractTask().setId(apply.getTask_id()).setStatus(TaskStatus.PROGRESSING.getVal()));
                    directStatusBiz.createOrUpdateDirectStatus(merchantSn, aliDirectDevCode, DirectStatus.STATUS_PROCESS, null);
                });
            } else {
                if (response.isSystemFail()) {
                    //3.2.2 500
                    monitorLog.recordMonitor("支付宝直连", apply.getMerchant_sn() + "提交支付宝直连创建事务返回500" + message);
                    applyMapper.updateByPrimaryKeySelective(new AliDirectApply().setId(apply.getId()).setPriority(new Date(System.currentTimeMillis() + ScheduleUtil.DEFAULT_FIVE_MINUTES_MILLIS_QUERY)));
                } else {
                    //3.2.1 非500，更新申请单为失败 报备任务失败 间连状态为失败
                    String finalMessage = message;
                    transactionTemplate.executeWithoutResult(tx -> {
                        AliDirectApply update = new AliDirectApply().setId(apply.getId()).setRequest_body(JSON.toJSONString(response.getReq()))
                                .setResponse_body(JSON.toJSONString(response.getResp())).setStatus(AliDirectApplyStatus.APPLY_REJECTED.getVal()).setResult(finalMessage);
                        applyMapper.updateByPrimaryKeySelective(update);
                        taskBiz.update(new ContractTask().setId(apply.getTask_id()).setStatus(TaskStatus.FAIL.getVal())
                                .setResult(JSON.toJSONString(CollectionUtil.hashMap("channel", ProviderUtil.ALI_DIRECT, "message", finalMessage))));
                        directStatusBiz.createOrUpdateDirectStatus(merchantSn, aliDirectDevCode, DirectStatus.STATUS_BIZ_FAIL, finalMessage);
                    });
                }
            }
        } finally {
            monitorLog.recordObject(new MonitorObject()
                    .setSn(merchantSn)
                    .setEvent(MonitorObject.ALI_DIRECT_APPLY)
                    .setCost(System.currentTimeMillis() - start)
                    .setStatus(code)
                    .setMessage(message));
        }
    }
}
