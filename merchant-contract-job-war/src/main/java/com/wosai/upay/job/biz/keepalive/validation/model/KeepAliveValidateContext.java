package com.wosai.upay.job.biz.keepalive.validation.model;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.job.biz.keepalive.validation.enums.KeepAliveValidationScenarioEnum;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.ProviderParamsKeepaliveTaskDO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 保活验证上下文信息
 */
@Getter
public class KeepAliveValidateContext {

    private final String merchantId;
    private final String merchantSn;
    private final Map merchant;
    private final KeepAliveValidationScenarioEnum keepAliveValidationScenarioEnum;
    /**
     * 在执行预圈选/执行的之后这个字段不能为空
     */
    private final ProviderParamsKeepaliveTaskDO taskDO;
    /**
     * 这个是校验过程中出现的信息
     */
    @Setter
    private List<MerchantProviderParamsDO> keepAliveParams;

    private KeepAliveValidateContext(Map merchant, KeepAliveValidationScenarioEnum keepAliveValidationScenarioEnum, ProviderParamsKeepaliveTaskDO taskDO) {
        this.merchant = merchant;
        this.merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);
        this.merchantSn = WosaiMapUtils.getString(merchant, Merchant.SN);
        this.keepAliveValidationScenarioEnum = keepAliveValidationScenarioEnum;
        this.taskDO = taskDO;
    }

    public static KeepAliveValidateContext createForPreCircleOrExecute(Map merchant,KeepAliveValidationScenarioEnum validationScenarioEnum, ProviderParamsKeepaliveTaskDO taskDO) {
        return new KeepAliveValidateContext(merchant, validationScenarioEnum, taskDO);
    }

    public static KeepAliveValidateContext createForOpenCircle(Map merchant) {
        return new KeepAliveValidateContext(merchant, KeepAliveValidationScenarioEnum.OPEN_CIRCLE, null);
    }

}