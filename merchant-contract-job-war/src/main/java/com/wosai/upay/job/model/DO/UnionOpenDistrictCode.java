package com.wosai.upay.job.model.DO;

import com.wosai.upay.common.validation.NotEmpty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description: 银联地区编码
 * <AUTHOR>
 * @Date 2020/9/25 5:06 PM
 **/

@Data
@Accessors(chain = true)
public class UnionOpenDistrictCode {

    private String id;

    @NotEmpty(message = "province 不能为null")
    private String province;

    private String province_code;

    @NotEmpty(message = "city 不能为null")
    private String city;

    private String city_code;

    @NotEmpty(message = "county 不能为null")
    private String county;

    private String county_code;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;

    private Long version;

}

