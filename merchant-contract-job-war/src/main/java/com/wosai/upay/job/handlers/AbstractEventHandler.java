package com.wosai.upay.job.handlers;

import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.upay.job.model.ContractEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * @date 2021-04-08
 */
@Slf4j
public abstract class AbstractEventHandler<R> implements Handler<ContractEvent, R> {

    @Override
    @Timed(value = "EventHandle")
    public R handle(ContractEvent event) throws Exception {
        try {
            AbstractEventHandler<R> handler = (AbstractEventHandler) AopContext.currentProxy();
            return handler.doHandle(event);
        } catch (Exception e) {
            handleError(event, e);
        }
        return null;
    }

    /**
     * 处理异常
     *
     * @param event
     * @param e
     * @throws Exception
     */
    protected abstract void handleError(ContractEvent event, Exception e) throws Exception;


    /**
     * 具体处理逻辑
     * <p>
     * 子类如果需要事务控制，需要加上  @Transactional
     *
     * @param event
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public abstract R doHandle(ContractEvent event) throws Exception;
}
