package com.wosai.upay.job.xxljob.direct.pendingtask;

import com.alibaba.fastjson.JSONObject;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.databus.event.merchant.config.FeeRateEvent;
import com.wosai.databus.event.merchant.config.Field;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.consumer.DataSyncHandler;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.PendingTasksMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.PendingTasks;
import com.wosai.upay.job.model.DTSBean;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.service.SelfHelpNetInEventService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.wosai.upay.job.util.StringUtil.formatDate;

/**
 * xxl_job_desc: PendingTask-银行卡POS绑定
 * 每5分钟扫描一次拉取pendingTask的银行卡POS绑定任务
 * <AUTHOR>
 * @date 2025/4/25
 */
@Slf4j
@Component("PendingTaskPosBankJobHandler")
public class PendingTaskPosBankJobHandler extends AbstractDirectJobHandler {

    @Autowired
    private PendingTasksMapper pendingTasksMapper;
    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;
    @Autowired
    private ChatBotUtil chatBotUtil;


    @Override
    public String getLockKey() {
        return "PendingTaskPosBankJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            List<PendingTasks> pendingTasks = pendingTasksMapper.selectPendingTasksByUpdateAtAndType(formatDate(System.currentTimeMillis() - param.getQueryTime()), PendingTasks.TYPE_BANK_POS_QUERY, param.getBatchSize());
            for (PendingTasks pendingTask : pendingTasks) {
                try {
                    if (ShutdownSignal.isShuttingDown()) {
                        return;
                    }
                    if (pendingTask.getVersion() > 10) {
                        throw new CommonPubBizException("查询超过10次");
                    }
                    providerTerminalBiz.handleBankPosQueryTask(pendingTask);
                } catch (Exception e) {
                    log.error("handleBankPosQueryTask error, id:{} ", pendingTask.getId(), e);
                    pendingTask.setStatus(PendingTasks.STATUS_FAIL);
                    pendingTask.setResult(e.getMessage());
                    updateResult(pendingTask);
                }
            }
        } catch (Exception e) {
            log.error("execute handleBankPosQueryTask error ", e);
            chatBotUtil.sendMessageToContractWarnChatBot("银行卡刷卡PendingTask处理异常 " + ExceptionUtil.getThrowableMsg(e));
        }
    }

    private void updateResult(PendingTasks pendingTasks) {
        PendingTasks updateValue = new PendingTasks()
                .setId(pendingTasks.getId())
                .setStatus(pendingTasks.getStatus())
                .setResult(pendingTasks.getResult());
        pendingTasksMapper.updateByPrimaryKeySelective(updateValue);
    }
}
