package com.wosai.upay.job.biz.acquirer;

import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.application.RuleContractRequest;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 切换到拉卡拉V3
 *
 * <AUTHOR>
 * @date 2021/9/8
 */
@Component("lklV3-AcquirerChangeBiz")
public class ChangeToLklV3Biz extends ChangeToLklBiz {

    /**
     * 切换到lklV3，但是没有报备过，直接返回新的切换收单机构的规则组
     *
     * @return
     */
    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_LKLORG_RULE_GROUP;
    }

    public static final List<String> LKLORG_CHANNEL_NO_LIST = Arrays.asList("32631798", "217935905", "270860769");

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_LAKALA_V3.getValue();
    }

    @Override
    protected List<MerchantProviderParams> getDefaultChangeParams(McAcquirerChange change) {
        // 查找云闪付、翼支付交易参数京东钱包参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderIn(Arrays.asList(ProviderEnum.PROVIDER_UION_OPEN.getValue(),
                        ProviderEnum.PROVIDER_NUCC.getValue(), ProviderEnum.PROVIDER_LKL_OPEN.getValue()))
                .andPaywayNotIn(Arrays.asList(PaywayEnum.ACQUIRER.getValue(), PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()))
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        Set<Integer> uniPayProviderSets = params.stream().filter(t -> Objects.equals(t.getPayway(), PaywayEnum.UNIONPAY.getValue()))
                .map(MerchantProviderParams::getProvider).collect(Collectors.toSet());
        // 云闪付,如果有1034,过滤掉1017
        if (uniPayProviderSets.contains(ProviderEnum.PROVIDER_LKL_OPEN.getValue())) {
            params = params.stream().filter(t -> !(Objects.equals(t.getPayway(), PaywayEnum.UNIONPAY.getValue())
                    && Objects.equals(t.getProvider(), ProviderEnum.PROVIDER_UION_OPEN.getValue()))).collect(Collectors.toList());
        }
        // 查找支付宝交易参数
        MerchantProviderParams aliParams = getAliParams(change);
        params.add(aliParams);
        // 微信交易参数
        MerchantProviderParams wxParams = getWxParams(change);
        params.add(wxParams);
        return params;

    }

    private MerchantProviderParams getWxParams(McAcquirerChange change) {
        List<MerchantProviderParams> wxParams = Lists.newArrayList();
        // 先获取新渠道的微信参数
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        wxExample.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_LKLORG.getValue())
                .andChannel_noIn(LKLORG_CHANNEL_NO_LIST)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        wxExample.setOrderByClause("ctime desc");
        wxParams.addAll(paramsMapper.selectByExampleWithBLOBs(wxExample));
        // 获取旧的
        MerchantProviderParamsExample oldWxExample = new MerchantProviderParamsExample();
        oldWxExample.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue())
                .andChannel_noIn(Arrays.asList("36TB4213315", "36TB4213341", "36002013293", "36T01224211", "36TB4213553", "36TB4213567"))
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        oldWxExample.setOrderByClause("ctime desc");
        wxParams.addAll(paramsMapper.selectByExampleWithBLOBs(oldWxExample));

        wxParams = wxParams.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        // 如果旧的也是空的，那就在新渠道重新报备一个(直接报新渠道也没毛病)
        if (WosaiCollectionUtils.isEmpty(wxParams)) {
            CommonResult result = contractApplicationService.contractByRule(
                    new RuleContractRequest()
                            .setRule(ContractRuleConstants.LKL_ORG_NORMAL_WEIXIN_RULE)
                            .setMerchantSn(change.getMerchant_sn())
                            .setPlat("changeAcquirer")
                            .setReContract(true)
                            .setConfigParam(false)
            );
            if (!result.isSuccess()) {
                throw new ContractBizException("缺少可用微信子商户号");
            }
            wxParams = paramsMapper.selectByExampleWithBLOBs(wxExample);
            if (WosaiCollectionUtils.isEmpty(wxParams)) {
                throw new ContractBizException("缺少可用微信子商户号");
            }
            return wxParams.get(0);
        }

        // 有多个微信交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams wxParam : wxParams) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(wxParam.getAuth_status())) {
                return wxParam;
            }
        }
        return wxParams.get(0);
    }

    private MerchantProviderParams getAliParams(McAcquirerChange change) {
        // 先取新渠道的参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_LKLORG.getValue())
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        params = params.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            // 有多个支付宝交易参数优先获取实名过的子商户号，都未实名则获取最新的一个
            MerchantProviderParams providerParams = params.parallelStream()
                    .filter(param -> PayMchAuthStatusEnum.YES.getValue().equals(param.getAuth_status()))
                    .findFirst().orElse(null);
            return Objects.isNull(providerParams) ? params.get(0) : providerParams;

        }
        // 没有新渠道再去取旧渠道参数
        MerchantProviderParamsExample example2 = new MerchantProviderParamsExample();
        example2.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue())
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andDeletedEqualTo(false);
        example2.setOrderByClause("ctime desc");
        params = paramsMapper.selectByExampleWithBLOBs(example2);
        params = params.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(params)) {
            throw new ContractBizException("缺少可用支付宝子商户号");
        }
        // 有多个支付宝交易参数优先获取实名过的子商户号，都未实名则获取最新的一个
        MerchantProviderParams providerParams = params.parallelStream()
                .filter(param -> PayMchAuthStatusEnum.YES.getValue().equals(param.getAuth_status()))
                .findFirst().orElse(null);
        return Objects.isNull(providerParams) ? params.get(0) : providerParams;
    }


    @Override
     List<MerchantProviderParams> getDefaultChangeParamsWhenChangeAcquire(McAcquirerChange change) {
        // 查找云闪付、翼支付交易参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderIn(Arrays.asList(ProviderEnum.PROVIDER_UION_OPEN.getValue(),
                        ProviderEnum.PROVIDER_NUCC.getValue(), ProviderEnum.PROVIDER_LKL_OPEN.getValue()))
                .andPaywayNotIn(Arrays.asList(PaywayEnum.ACQUIRER.getValue(), PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()))
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        Set<Integer> uniPayProviderSets = params.stream().filter(t -> Objects.equals(t.getPayway(), PaywayEnum.UNIONPAY.getValue()))
                .map(MerchantProviderParams::getProvider).collect(Collectors.toSet());
        // 云闪付,如果有1034,过滤掉1017
        if (uniPayProviderSets.contains(ProviderEnum.PROVIDER_LKL_OPEN.getValue())) {
            params = params.stream().filter(t -> !(Objects.equals(t.getPayway(), PaywayEnum.UNIONPAY.getValue())
                    && Objects.equals(t.getProvider(), ProviderEnum.PROVIDER_UION_OPEN.getValue()))).collect(Collectors.toList());
        }
        // 查找支付宝交易参数
        MerchantProviderParams aliParams = getAliParamsWhenChangeAcquire(change);
        params.add(aliParams);
        // 微信交易参数
        MerchantProviderParams wxParams = getWxParamsWhenChangeAcquire(change);
        params.add(wxParams);
        return params;
    }

    /**
     * 主动切换收单机构时获取微信交易参数
     * @param change
     * @return
     */
    private MerchantProviderParams getWxParamsWhenChangeAcquire(McAcquirerChange change) {
        List<MerchantProviderParams> wxParams = Lists.newArrayList();
        // 先获取新渠道的微信参数
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        wxExample.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_LKLORG.getValue())
                .andChannel_noIn(LKLORG_CHANNEL_NO_LIST)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        wxExample.setOrderByClause("ctime desc");
        wxParams.addAll(paramsMapper.selectByExampleWithBLOBs(wxExample));
        wxParams = wxParams.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        //1016不允许使用https://jira.wosai-inc.com/browse/CUA-6544
        // 如果旧的也是空的，那就在新渠道重新报备一个(直接报新渠道也没毛病)
        if (WosaiCollectionUtils.isEmpty(wxParams)) {
            CommonResult result = contractApplicationService.contractByRule(
                    new RuleContractRequest()
                            .setRule(ContractRuleConstants.LKL_ORG_NORMAL_WEIXIN_RULE)
                            .setMerchantSn(change.getMerchant_sn())
                            .setPlat("changeAcquirer")
                            .setReContract(true)
                            .setConfigParam(false)
            );
            if (!result.isSuccess()) {
                throw new ContractBizException("缺少可用微信子商户号");
            }
            wxParams = paramsMapper.selectByExampleWithBLOBs(wxExample);
            if (WosaiCollectionUtils.isEmpty(wxParams)) {
                throw new ContractBizException("缺少可用微信子商户号");
            }
            return wxParams.get(0);
        }

        // 有多个微信交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams wxParam : wxParams) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(wxParam.getAuth_status())) {
                return wxParam;
            }
        }
        return wxParams.get(0);
    }


    /**
     * 主动切换收单机构时获取支付宝交易参数
     * @param change
     * @return
     */
    private MerchantProviderParams getAliParamsWhenChangeAcquire(McAcquirerChange change) {
        List<MerchantProviderParams> aliParams;
        // 先取新渠道的参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_LKLORG.getValue())
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        aliParams = paramsMapper.selectByExampleWithBLOBs(example);
        aliParams = aliParams.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(aliParams)) {
            CommonResult result = contractApplicationService.contractByRule(
                    new RuleContractRequest()
                            .setRule(ContractRuleConstants.LKL_ORG_NORMAL_ALI_RULE)
                            .setMerchantSn(change.getMerchant_sn())
                            .setPlat("changeAcquirer")
                            .setReContract(true)
                            .setConfigParam(false)
            );
            if (!result.isSuccess()) {
                throw new ContractBizException("缺少可用支付宝子商户号");
            }
            aliParams = paramsMapper.selectByExampleWithBLOBs(example);
            if (WosaiCollectionUtils.isEmpty(aliParams)) {
                throw new ContractBizException("缺少可用支付宝子商户号");
            }
            return aliParams.get(0);
        }

        // 有多个支付宝交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        MerchantProviderParams providerParams = aliParams.parallelStream()
                .filter(param ->  PayMchAuthStatusEnum.YES.getValue().equals(param.getAuth_status()))
                .findFirst().orElse(null);
        return Objects.isNull(providerParams) ? aliParams.get(0) : providerParams;
    }



    @Override
    protected void sourceAcquirerPostBiz(McAcquirerChange change) {
        super.sourceAcquirerPostBiz(change);
        super.invalidJdPay(change.getMerchant_id(), change.getTarget_acquirer(), change.getMerchant_sn());
    }


}