package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 拉卡拉银行卡Pos终端信息
 * <AUTHOR>
 * @Date 2022/07/23 1:36 下午
 **/

@Data
@Accessors(chain = true)
public class LklV3BankTerm {

    /**
     * lkl 终端号
     */
    private String termNo;

    /**
     * lkl 终端id， 与终端号 1：1 对应
     */
    private String termId;

    /**
     * lkl 业务名称
     */
    private String busiTypeName;

    /**
     * lkl 产品代码
     */
    private String productCode;

    /**
     * lkl产品名称
     */
    private String productName;


    /**
     * 激活码
     */
    private String activeNo;

    /**
     * 拉卡拉返回业务类型
     */
    private String busiTypeCode;

}