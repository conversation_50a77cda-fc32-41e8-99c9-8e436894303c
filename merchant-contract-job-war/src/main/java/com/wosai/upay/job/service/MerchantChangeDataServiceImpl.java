package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonDataObjectNotExistsException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.model.AppInfoResponse;
import com.wosai.model.wechat.AppAuthInfo;
import com.wosai.service.SystemService;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.job.Constants.AuthAndComboTaskConstant;
import com.wosai.upay.job.Constants.MerchantChangeDataConstant;
import com.wosai.upay.job.biz.AuthAndComboTaskBiz;
import com.wosai.upay.job.biz.PendingTasksBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.enume.AuthAndComboTaskStatusCode;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.AuthAndComboTaskMapper;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.PendingTasks;
import com.wosai.upay.job.model.changeMerchantData.ApplyChangeMerchantDataReq;
import com.wosai.upay.job.model.changeMerchantData.CommonResp;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.refactor.model.enums.UseStatusEnum;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class MerchantChangeDataServiceImpl implements MerchantChangeDataService {
    @Autowired
    ComposeAcquirerBiz composeAcquirerBiz;
    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private ContractEventMapper eventMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private SystemService systemService;
    @Autowired
    private AuthAndComboTaskMapper authAndComboTaskMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    @Lazy
    private MerchantProviderParamsService providerParamsService;
    @Autowired
    private JobWeixinService jobWeixinService;
    @Autowired
    private WechatAuthBiz wechatAuthBiz;
    @Autowired
    private AuthAndComboTaskBiz authAndComboTaskBiz;
    @Autowired
    private ComposeAcquirerBiz acquirerBiz;


    @Override
    public CommonResp applyChangeMerchantData(ApplyChangeMerchantDataReq params) {

        try {
            ContractTask task = contractTaskMapper.getBySnAndTypeByCreateDesc(params.getMerchantSn(), ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE);
            if (task != null && (!task.getStatus().equals(TaskStatus.SUCCESS.getVal()) && !task.getStatus().equals(TaskStatus.FAIL.getVal()))) {
                return new CommonResp().setSuccess(false).setMsg("商户有未完成的商户信息变更任务");

            }
            String acquirer = acquirerBiz.getMerchantAcquirer(params.getMerchantSn());
            final String ruleGroup = composeAcquirerBiz.getAcquirerDefaultRuleGroup(acquirer);
            final RuleGroup group = ruleContext.getRuleGroup(ruleGroup);

            ContractEvent event = new ContractEvent().setMerchant_sn(params.getMerchantSn())
                    .setEvent_type(ContractEvent.OPT_TYPE_UPDATE_MERCHANT_DATA)
                    .setEvent_msg(JSON.toJSONString(CollectionUtil.hashMap(
                            MerchantChangeDataConstant.BANK_PRE_ID, params.getBankPreId(),
                            MerchantChangeDataConstant.RE_CONTRACT, params.isReContract(),
                            MerchantChangeDataConstant.APPLY_SOURCE, params.getApplySource(),
                            ContractEvent.FORCE_UPDATE, true,
                            ContractEvent.FORM_BODY, params.getFormBody()
                    )))
                    .setRule_group_id(group.getGroup_id());

            eventMapper.insertSelective(event);
            return new CommonResp().setSuccess(true);
        } catch (Exception e) {
            log.error("提交商户信息变更异常,params : {}", JSON.toJSONString(params), e);
            return new CommonResp().setSuccess(false).setMsg(e.getMessage());
        }

    }

    @Override
    public Integer queryMerchantChangeDataTaskStatus(String merchantSn) {
        List<ContractEvent> contractEvents = eventMapper.selectEventTodoByMerchantSnAndType(merchantSn, ContractEvent.OPT_TYPE_UPDATE_MERCHANT_DATA);
        if (WosaiCollectionUtils.isNotEmpty(contractEvents)) {
            return MerchantChangeDataConstant.COMMON_STATUS_PROCESS;
        }

        ContractTask task = contractTaskMapper.getBySnAndTypeByCreateDesc(merchantSn, ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE);
        if (task == null) {
            return null;
        }
        return task.getStatus().equals(TaskStatus.SUCCESS.getVal()) ? MerchantChangeDataConstant.COMMON_STATUS_SUCCESS : task.getStatus().equals(TaskStatus.FAIL.getVal()) ? MerchantChangeDataConstant.COMMON_STATUS_FAIL : MerchantChangeDataConstant.COMMON_STATUS_PROCESS;
    }

    @Override
    public CommonResp submitAuthAndComboTask(String merchantSn, Map bizParams) {
        //优先查询中间表
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, "MerchantChangeDataServiceImpl");
        //当前在用直连微信参数,变行业不影响.不用往下走流程
        MerchantParamReq req = new MerchantParamReq()
                .setMerchant_sn(merchantSn)
                .setPayway(PaywayEnum.WEIXIN.getValue())
                .setStatus(UseStatusEnum.IN_USE.getValue())
                .setProvider(ProviderEnum.WEI_XIN.getValue());
        List<MerchantProviderParamsDto> inUseWeiXinParams = providerParamsService.getMerchantProviderParams(req);
        if (WosaiCollectionUtils.isNotEmpty(inUseWeiXinParams)) {
            return new CommonResp().setSuccess(true).setMsg("微信参数为直连").setAsync(false);
        }

        //复查确定一下当前要报的微信子商户号,理论上来说,现在参数表肯定有一个符合要求的子商户号
        List<MerchantProviderParamsDto> indirectWeixinParams = providerParamsService.queryCurrentAcquirerWeixinParams(merchantSn);
        if (WosaiCollectionUtils.isEmpty(indirectWeixinParams)) {
            return new CommonResp().setMsg("间连参数为空");
        }

        SettlementIdConfig settlementConfig = jobWeixinService.getSettlementConfig(merchant.getIndustry());
        if (settlementConfig == null) {
            return new CommonResp().setMsg("根据商户行业未找到结算id配置");
        }

        Map configMap = JSON.parseObject(JSON.toJSONString(settlementConfig), Map.class);

        List<MerchantProviderParamsDto> targetParam = indirectWeixinParams.stream().filter(param -> configMap.containsValue(param.getWx_settlement_id())).collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(targetParam)) {
            log.info("商户sn : {}  需要的结算id为 {}", merchantSn, JSON.toJSONString(configMap));
            return new CommonResp().setMsg("未找到符合条件的交易参数");
        }

        List<MerchantProviderParamsDto> targetAlreadyUsed = targetParam.stream().filter(param -> !param.getDeleted() && param.getStatus() == UseStatusEnum.IN_USE.getValue()).collect(Collectors.toList());
        //优先选一个目标参数已经在使用中的,后续在定时任务中触发直接成功,并发送消息
        AuthAndComboTask task = new AuthAndComboTask().setMerchant_id(merchant.getId()).setMerchant_sn(merchantSn).setForm_body(JSON.toJSONString(bizParams));
        if (WosaiCollectionUtils.isNotEmpty(targetAlreadyUsed)) {
            task.setSub_mch_id(targetAlreadyUsed.get(0).getPay_merchant_id());
        } else {
            task.setSub_mch_id(targetParam.get(0).getPay_merchant_id());
        }
        boolean preHandleSuccess = authAndComboTaskBiz.preHandle(task);
        if (preHandleSuccess) {
            return new CommonResp().setSuccess(true).setMsg("success").setAsync(false);
        } else {
            authAndComboTaskMapper.insertSelective(task);
            return new CommonResp().setSuccess(true).setMsg("success").setAsync(true);
        }
    }

    @Override
    public Integer queryAuthAndComboTaskStatus(String merchantSn) {
        AuthAndComboTask task = authAndComboTaskMapper.getLatestTask(merchantSn);
        if (task == null) {
            return null;
        }

        return task.getStatus() == 5 ? MerchantChangeDataConstant.COMMON_STATUS_SUCCESS : task.getStatus() == 6 ? MerchantChangeDataConstant.COMMON_STATUS_FAIL : MerchantChangeDataConstant.COMMON_STATUS_PROCESS;
    }

    private static final String WAIT_FOR_AUTH_MSG = "请{name}使用本人微信扫码核实收款商户号：{subMchId}，授权成功后微信将切至新的收款商户号，微信费率变更为{rate}，为避免影响交易，请在{date}前完成，到期未授权将自动切参数及费率。";
    private static final String WAIT_FOR_AUTH_NO_INFO_MSG = "请在商户详情重新按个体/组织提交微信商家认证,授权子商户号%s";

    @Override
    public Map<String, Object> queryAuthAndComboTaskMsgAndCode(String merchantSn, String type) {
        AuthAndComboTask task = authAndComboTaskMapper.getLatestTask(merchantSn);
        Map<String, Object> resp = new HashMap<>(4);
        if (task == null) {
            resp.put("contract_code", AuthAndComboTaskStatusCode.NO_TASK.getCode());
            resp.put("contract_memo", AuthAndComboTaskStatusCode.NO_TASK.getMsg());
        } else if (task.getStatus().equals(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_WAIT_PROCESS)) {
            resp.put("contract_code", AuthAndComboTaskStatusCode.PENDING_TASK.getCode());
            resp.put("contract_memo", AuthAndComboTaskStatusCode.PENDING_TASK.getMsg());
        } else if (task.getStatus().equals(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_WAIT_AUTH)) {
            resp.put("contract_code", AuthAndComboTaskStatusCode.WAIT_FOR_AUTH.getCode());
            AppInfoResponse appInfoByInfo = systemService.getAppInfoByInfo(CollectionUtil.hashMap("subMchId", task.getSub_mch_id(), "reason", "industry"));
            if (appInfoByInfo.getResult()) {
                resp.put("contract_memo", transferMemo(task, appInfoByInfo.getAppAuthInfo()));
                try {
                    if (appInfoByInfo.getAppAuthInfo().getCode() != null) {
                        resp.put("url", URLDecoder.decode(appInfoByInfo.getAppAuthInfo().getCode(), "UTF-8"));
                    }
                } catch (UnsupportedEncodingException ignored) {
                }
            } else {
                resp.put("contract_memo", String.format(WAIT_FOR_AUTH_NO_INFO_MSG, task.getSub_mch_id()));
            }
        } else if (task.getStatus().equals(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_SUCCEED)) {
            resp.put("contract_code", AuthAndComboTaskStatusCode.TASK_SUCCESS.getCode());
            resp.put("contract_memo", AuthAndComboTaskStatusCode.TASK_SUCCESS.getMsg());
        } else {
            resp.put("contract_code", AuthAndComboTaskStatusCode.TASK_FAIL.getCode());
            resp.put("contract_memo", AuthAndComboTaskStatusCode.TASK_FAIL.getMsg());
        }
        return resp;
    }

    private String transferMemo(AuthAndComboTask task, AppAuthInfo appAuthInfo) {
        Map formBody = JSON.parseObject(task.getForm_body(), Map.class);
        List<Map<String, Object>> config = (List<Map<String, Object>>) formBody.get(AuthAndComboTaskConstant.MERCHANT_CONFIG);
        Map<String, Object> weixinFeeRate = config.stream().filter(r -> BeanUtil.getPropInt(r, MerchantConfig.PAYWAY) == PaywayEnum.WEIXIN.getValue()).findFirst().orElse(new HashMap<>());
        String contractName = appAuthInfo.getContact_name();
        String subMchId = task.getSub_mch_id();
        String date = DateFormatUtils.format(DateUtils.addDays(task.getCreate_at(), 7), "yyyy-MM-dd");
        String wxFeeRateMsg = getFeeRateMsg(weixinFeeRate);
        return WAIT_FOR_AUTH_MSG.replaceAll("\\{name}", contractName)
                .replaceAll("\\{subMchId}", subMchId)
                .replaceAll("\\{rate}", wxFeeRateMsg)
                .replaceAll("\\{date}", date);
    }

    @Override
    public Boolean queryWeixinAuthStatus(String merchantSn) {
        AuthAndComboTask authAndComboTask = authAndComboTaskMapper.getLatestTask(merchantSn);
        if (authAndComboTask == null) {
            return null;
        }

        MerchantProviderParams setDefault = merchantProviderParamsMapper.selectByPayMerchantId(authAndComboTask.getSub_mch_id());
        if (setDefault == null || setDefault.getDeleted()) {
            throw new CommonDataObjectNotExistsException("交易参数不存在");
        }
        Boolean authStatus = wechatAuthBiz.getAuthStatus(setDefault, setDefault.getProvider());
        if (authStatus != null && authStatus) {
            try {
                authAndComboTaskBiz.changeTradeParamsAndApplyCombo(authAndComboTask, setDefault);
                authAndComboTaskBiz.changeStatusAndSendMessage(authAndComboTask, MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_SUCCEED, null);
            } catch (Exception e) {
                log.error("授权和切换套餐任务, 子商户号授权通过设为默认异常, sn: {}", merchantSn, e);
            }
        }
        return authStatus;
    }

    private String getFeeRateMsg(Map<String, Object> weixinFeeRate) {
        if (WosaiCollectionUtils.isNotEmpty((Collection) weixinFeeRate.get("ladder_fee_rates"))) {
            String minMsg = "";
            String maxMsg = "";
            List<Map> ladderReq = (List<Map>) weixinFeeRate.get("ladder_fee_rates");
            for (Map map : ladderReq) {
                Integer min = MapUtils.getInteger(map, "min");
                Integer max = MapUtils.getInteger(map, "max");
                // 银行通道切换到间连再切回, configs里会没有rate信息,取bscFeeRate字段
                String rate = MapUtils.getString(map, "rate", MapUtils.getString(map, "bscFeeRate"));
                if (min == 0) minMsg = max + "元以下," + rate + "%";
                if (max == null) maxMsg = min + "元以上," + rate + "%";
            }
            return minMsg + ";" + maxMsg;
        } else {
            return BeanUtil.getPropString(weixinFeeRate, "rate") + "%";
        }
    }
}
