package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.AliDirectApply;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

public interface AliDirectApplyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AliDirectApply record);

    int insertSelective(AliDirectApply record);

    AliDirectApply selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AliDirectApply record);

    int updateByPrimaryKeyWithBLOBs(AliDirectApply record);

    int updateByPrimaryKey(AliDirectApply record);

    AliDirectApply selectProcessApplyByMerchantSn(@Param("merchantSn") String merchantSn);

    AliDirectApply selectLatestApplyByMerchantSn(@Param("merchantSn") String merchantSn);

    List<AliDirectApply> getAppliesByPrioirtyAndStatus(@Param("startTime") String startTime,@Param("endTime") String endTime, @Param("statuses") ArrayList<Integer> statuses, @Param("limit") Integer limit);

    AliDirectApply selectProcessApplyByBatchNo(@Param("batchNo") String batchNo);

    AliDirectApply selectApplyByTaskId(long taskId);
}