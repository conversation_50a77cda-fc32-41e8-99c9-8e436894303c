package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.controller.TongLianV2Controller;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * xxl_job_desc: 处理收银宝未处理回调任务
 * <AUTHOR>
 * @date 2025/4/18
 */
@Slf4j
@Component("TonglianV2UnCallbackTaskJobHandler")
public class TonglianV2UnCallbackTaskJobHandler extends AbstractBatchJobHandler<ContractSubTask> {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private TongLianV2Controller tongLianV2Controller;

    @Autowired
    private TongLianV2Service tongLianV2Service;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Override
    public List<ContractSubTask> queryTaskItems(BatchJobParam param) {
        long current = System.currentTimeMillis();
        return contractSubTaskMapper.getTongLianV2UnCallbackSubTask(
                param.getBatchSize(),
                StringUtil.formatDate(current - param.getStartTime()),
                StringUtil.formatDate(current - param.getEndTime())
        );
    }

    @Override
    public String getLockKey(ContractSubTask contractSubTask) {
        return "TonglianV2UnCallbackTaskJobHandler:" + contractSubTask.getId();
    }

    @Override
    public void doHandleSingleData(ContractSubTask contractSubTask) {
        try {
            //1，再次查询
            ContractSubTask subTaskLast = contractSubTaskMapper.selectByPrimaryKey(contractSubTask.getId());
            //2，判断状态
            if (TaskStatus.isFinish(subTaskLast.getStatus())) {
                return;
            }

            TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByContractSubTask(contractSubTask, TongLianV2Param.class);
            ContractResponse contractResponse = tongLianV2Service.queryMerchantInfo(subTaskLast.getMerchant_sn(), tongLianV2Param);
            if (contractResponse.isSuccess()) {
                Map<String, Object> tLMchInfo = contractResponse.getResponseParam();
                TongLianV2Controller.AuditNotifyRequest auditNotifyRequest = new TongLianV2Controller.AuditNotifyRequest();
                if (isMatch(subTaskLast.getRequestBody(), tLMchInfo)) {
                    auditNotifyRequest.setErrmsg("修改成功，未回调，主动对比一致");
                    tongLianV2Controller.updateStatus(true,
                            subTaskLast,
                            auditNotifyRequest,
                            null);
                } else {
                    auditNotifyRequest.setErrmsg("审核不通过，原因是:未回调，主动对比不一致");
                    tongLianV2Controller.updateStatus(false,
                            subTaskLast,
                            auditNotifyRequest,
                            "审核不通过");
                }
            }
        } catch (Exception e) {
            log.error("处理收银宝未回调任务失败，商户号{},业务类型{}", contractSubTask.getMerchant_sn(), contractSubTask.getTask_type(), e);
        }
    }

    // 对比关键字段
    private static final List<String> KEYS = Arrays.asList(
            "corpbusname",
            "creditcode",
            "legal",
            "legalidtype",
            "legalidno",
//            "accttp",
            "acctname",
            "acctid",
            "accttype"
    );

    private boolean isMatch(Map<String, Object> request, Map<String, Object> tLMchInfo) {
        for (String key : KEYS) {
            String prop = BeanUtil.getPropString(request, key);
            // 修改过，并且不相等
            if (WosaiStringUtils.isNotEmpty(prop) && !prop.equals(BeanUtil.getPropString(tLMchInfo, key))) {
                return false;
            }
        }

        return true;
    }
}
