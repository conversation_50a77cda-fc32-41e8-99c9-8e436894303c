package com.wosai.upay.job.handlers;

import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021/11/25
 */
@Slf4j
public abstract class AbstractMultiProviderEventHandler<R> implements Handler<MultiProviderContractEvent, R> {

    @Override
    @Timed(value = "EventHandle")
    public R handle(MultiProviderContractEvent event) throws Exception {
        try {
            AbstractMultiProviderEventHandler<R> handler = (AbstractMultiProviderEventHandler<R>) AopContext.currentProxy();
            return handler.doHandle(event);
        } catch (Exception e) {
            handleError(event, e);
        }
        return null;
    }

    /**
     * 处理异常
     *
     * @param event
     * @param e
     * @throws Exception
     */
    protected abstract void handleError(MultiProviderContractEvent event, Exception e) throws Exception;


    /**
     * 具体处理逻辑
     * <p>
     * 子类如果需要事务控制，需要加上  @Transactional
     *
     * @param event
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public abstract R doHandle(MultiProviderContractEvent event) throws Exception;
}
