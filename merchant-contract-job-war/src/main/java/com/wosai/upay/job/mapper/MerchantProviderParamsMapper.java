package com.wosai.upay.job.mapper;

import com.wosai.upay.job.interceptor.DaoLog;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsCustom;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface MerchantProviderParamsMapper {
    int countByExample(MerchantProviderParamsExample example);

    int deleteByExample(MerchantProviderParamsExample example);

    int deleteByPrimaryKey(String id);

    int insert(MerchantProviderParams record);

    int insertSelective(MerchantProviderParams record);

    List<MerchantProviderParams> selectByExampleWithBLOBs(MerchantProviderParamsExample example);

    List<MerchantProviderParams> selectByExample(MerchantProviderParamsExample example);

    List<MerchantProviderParamsCustom> selectByExampleCustom(@Param("params") MerchantProviderParamsCustom params);

    MerchantProviderParams selectByPrimaryKey(String id);

    MerchantProviderParamsCustom selectByPrimaryKeyCustom(@Param("id") String id);

    int updateByExampleSelective(@Param("record") MerchantProviderParams record, @Param("example") MerchantProviderParamsExample example);

    int updateByPrimaryKeySelective(MerchantProviderParams record);

    int updateStatusByKey(@Param("status") Integer status, @Param("mtime") long mtime, @Param("id") String id);


    MerchantProviderParams getUseWeiXinParam(@Param("merchantSn") String merchantSn);

    @Select("select * from merchant_provider_params where merchant_sn=#{merchantSn} and payway = 2 and status = 1 and deleted = 0 limit 1")
    MerchantProviderParams getUseAlipayParam(@Param("merchantSn") String merchantSn);

    @Select("select * from merchant_provider_params where merchant_sn = #{merchantSn} and payway = #{payway} and provider = #{payway} and status = 1 and deleted = 0 limit 1")
    MerchantProviderParams getDirectParam(@Param("merchantSn") String merchantSn, @Param("payway") int payway);

    MerchantProviderParams getWeixinParamByChannelNo(@Param("merchantSn") String merchantSn, @Param("channelNO") String channelNo);

    MerchantProviderParams getByPayMerchantId(@Param("payMerchantID") String payMerchantID);

    @Select("select * from merchant_provider_params where merchant_sn=#{merchantSn} and channel_no=#{channelNo} and payway=#{payWay}  and deleted=0 order by ctime desc limit 1")
    MerchantProviderParams getPayMchIdByChannelAndPayWay(@Param("merchantSn") String merchantSn, @Param("channelNo") String channelNo, @Param("payWay") String payWay);

    @Select("select * from merchant_provider_params where provider_merchant_id=#{providerMerchantId} and provider=#{provider} and payway=#{payWay}  and deleted=0 order by ctime desc limit 1")
    MerchantProviderParams getParamsByProviderMerchantIdAndProviderAndPayWay(@Param("providerMerchantId") String providerMerchantId, @Param("provider") String provider, @Param("payWay") String payWay);

    @Select("select * from merchant_provider_params where merchant_sn=#{merchantSn} and channel_no=#{channelNo} and payway=#{payWay}  and deleted=0")
    List<MerchantProviderParams> getParamsByChannelAndPayWay(@Param("merchantSn") String merchantSn, @Param("channelNo") String channelNo, @Param("payWay") String payWay);

    @Update("update merchant_provider_params set gold_status=#{goldStatus} where id=#{paramId}")
    int updateGoldStatusById(@Param("goldStatus") int goldStatus, @Param("paramId") String paramId);

    @Select("select * from merchant_provider_params where merchant_sn =#{merchant_sn} and payway=17 and contract_rule='tonglian-1020-17' and params_config_status=1 limit 1")
    MerchantProviderParams getUnionOpenParam(@Param("merchant_sn") String merchantSn);

    @Update("update merchant_provider_params set deleted = 1 where merchant_sn= #{merchantSn} and provider = #{provider}")
    int delParamsByProviderAndMerchantSn(@Param("merchantSn") String merchantSn, @Param("provider") Integer provider);

    /**
     * 获取银联云闪付交易参数
     *
     * @param merchantSn
     * @return
     */
    @Select("select * from merchant_provider_params where merchant_sn =#{merchant_sn} and payway=17 and contract_rule='tonglian-1020-17' limit 1")
    MerchantProviderParams getUnionOpenParamCheck(@Param("merchant_sn") String merchantSn);

    /**
     * @Author: zhmh
     * @Description: 根据主键修改微信子商户号用途
     * @time: 09:37 2021/2/3
     */
    Integer updateWxUseTypeById(@Param("id") String id, @Param("wxUseType") Integer wxUseType);

    @Select("select * from merchant_provider_params where merchant_sn = #{merchant_sn} and provider = #{provider} and payway = 0 and deleted=0 limit 1")
    MerchantProviderParams getAcquirerParamsByMerchantSnAndProvider(@Param("merchant_sn") String merchantSn, @Param("provider") Integer provider);

    @Select("select * from merchant_provider_params where channel_no =#{channel} and pay_merchant_id = #{pay_merchant_id} limit 1")
    MerchantProviderParams getChannelParams(@Param("channel") String channel, @Param("pay_merchant_id") String payMerchantId);

    int updateParamsConfigStatusById(@Param("status") Integer status, @Param("mtime") long mtime, @Param("params_config_status") Integer paramConfigStatus, @Param("id") String id);

    /**
     * 查询lkl收单机构下的支付宝和微信交易参数信息
     * @param merchantSn 商户号
     * @return 交易参数
     */
    @Select("select * from merchant_provider_params where merchant_sn =#{merchant_sn} and payway in (2,3) and provider in (1016,1033) and deleted = 0")
    List<MerchantProviderParams> selectLklWxAndAliPayParamsByMerchantSn(@Param("merchant_sn") String merchantSn);

    /**
     * 查询通联收单机构下的支付宝和微信交易参数信息
     * @param merchantSn 商户号
     * @return 交易参数
     */
    @Select("select * from merchant_provider_params where merchant_sn =#{merchant_sn} and payway in (2,3) and provider = 1020 and deleted = 0")
    List<MerchantProviderParams> selectTlWxAndAlipayParamsByMerchantSn(@Param("merchant_sn") String merchantSn);

    /**
     * 根据id修改商户名信息
     * @param id 记录id
     * @param merchantName 商户名
     * @return 交易参数
     */
    @Update("update merchant_provider_params set merchant_name=#{merchantName} where id=#{id}")
    int updateMerchantNameById(@Param("id") String id, @Param("merchantName") String merchantName);

    /**
     * 根据id修改商户名和微信结算id信息
     * @param id 记录id
     * @param merchantName 商户名
     * @param wxSettlementId 微信结算id
     * @return 交易参数
     */
    @Update("update merchant_provider_params set merchant_name=#{merchantName},wx_settlement_id=#{wxSettlementId} where id=#{id}")
    int updateMerchantNameAndSettlementIdById(@Param("id") String id, @Param("merchantName") String merchantName,String wxSettlementId);

    @Select("select * from merchant_provider_params where pay_merchant_id = #{pay_merchant_id} and deleted = 0 limit 1")
    MerchantProviderParams selectByPayMerchantId(@Param("pay_merchant_id") String payMerchantId);

    @Select("select * from merchant_provider_params where pay_merchant_id = #{payMerchantId} and merchant_sn = #{merchantSn} and deleted = 0 limit 1")
    MerchantProviderParams selectByPayMerchantIdAndMerchantSn(@Param("payMerchantId") String payMerchantId, @Param("merchantSn") String merchantSn);

    /**
     * 逻辑删除商户不是某一渠道下微信子商号
     * @param merchantSn
     * @param provider
     * @param payWay
     * @return
     */
    @Update("update merchant_provider_params set deleted = 1,status = 0 where merchant_sn = #{merchantSn} and provider = #{provider} and payway = #{payWay} and channel_no != #{channelNo}")
    int delParamsByMerchantSnAndProviderAndPayWay(@Param("merchantSn") String merchantSn, @Param("provider") Integer provider, @Param("payWay") Integer payWay, @Param("channelNo") String channelNo);

    @Update("update merchant_provider_params set deleted = 1,status = 0 where merchant_sn = #{merchantSn} and provider = #{provider} and payway = #{payWay} and pay_merchant_id != #{payMerchantId} and deleted = 0")
    int delWeiXinParamsExcludeSpecificPayMerchantId(@Param("merchantSn") String merchantSn, @Param("provider") Integer provider, @Param("payWay") Integer payWay, @Param("payMerchantId") String payMerchantId);
}