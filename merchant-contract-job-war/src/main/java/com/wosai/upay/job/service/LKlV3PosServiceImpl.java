package com.wosai.upay.job.service;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSSClient;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.sales.merchant.business.model.AppInfoModel;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.ForeignCardConstant;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.Constants.PreAuthApplyConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirePos.T9HandleFactory;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.config.OssAK;
import com.wosai.upay.job.consumer.StoreCreateConsumer;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ForeignCardMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.PreAuthApplyMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.model.ForeignCard;
import com.wosai.upay.job.model.PreAuthApply;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.dto.acquirePos.LklEcApplyDTO;
import com.wosai.upay.job.model.lklV3Pos.ApplyForeignCardRequest;
import com.wosai.upay.job.model.lklV3Pos.ApplyPosRequest;
import com.wosai.upay.job.model.lklV3Pos.LKlEcApplyStatusResp;
import com.wosai.upay.job.model.lklV3Pos.LklV3MerchantResponse;
import com.wosai.upay.job.refactor.model.entity.LklEcApplyDO;
import com.wosai.upay.job.refactor.model.enums.LklEcApplyStatusRespEnum;
import com.wosai.upay.merchant.contract.model.LimitResp;
import com.wosai.upay.merchant.contract.model.lklV3.EcApplyData;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import com.wosai.upay.model.direct.GetDevParamReq;
import com.wosai.upay.service.CrmEdgeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import vo.ApiRequestParam;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.wosai.upay.job.util.BatchChangeAcquireUtil.ENDPOINT_URL;
import static com.wosai.upay.job.util.BatchChangeAcquireUtil.STATICS_BUCKET_NAME;


/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2022/7/26 10:01
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class LKlV3PosServiceImpl implements LKlV3PosService {
    @Autowired
    LklV3Service lklV3Service;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    DirectStatusBiz directStatusBiz;

    @Autowired
    MerchantService merchantService;

    @Autowired
    TradeConfigService tradeConfigService;

    @Autowired
    SupportService supportService;
    @Autowired
    ParamContextBiz paramContextBiz;
    @Autowired
    ContractParamsBiz contractParamsBiz;
    @Autowired
    LklV3ShopTermBiz lklV3ShopTermBiz;
    @Autowired
    private FeeRateService feeRateService;
    @Autowired(required = false)
    StoreCreateConsumer storeCreateConsumer;
    @Autowired
    SubBizParamsBiz subBizParamsBiz;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    ForeignCardMapper foreignCardMapper;

    @Autowired
    PreAuthApplyMapper preAuthApplyMapper;

    @Autowired
    TerminalService terminalService;
    @Autowired
    StoreService storeService;
    @Autowired
    CrmEdgeService crmEdgeService;
    @Autowired
    private AgreementBiz agreementBiz;
    @Autowired
    private LklEcApplyService lklEcApplyService;

    @Autowired
    private ImportCrmInfoService importCrmInfoService;

    @Lazy
    @Autowired
    private T9HandleFactory factory;

    @Resource(name ="jsonRedisTemplate")
    private RedisTemplate jsonRedisTemplate;

    @Value("${lklForeignCard.devCode}")
    private String lklForeignCardDevCode;

    @Value("${lkl.preAuth.devCode}")
    private String lklPreAuthDevCode;

    public static final String FOREIGN_CARD_BASE_DIR = "lkl/about/T9/foreignCard/";

    public static final String PRE_AUTH_BASE_DIR = "lkl/about/T9/preAuth/";



    @Override
    public ContractResponse applyPos(ApplyPosRequest request) {
        final ContractResponse response = new ContractResponse();
        final String merchantSn = request.getMerchantSn();
        final String devCode = request.getDevCode();
        try {
            //先插入状态信息crm可以立刻查询到
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, devCode, DirectStatus.STATUS_PROCESS, null);
            MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
            final String formBody = request.getForm_body();
            final Map map = JSONObject.parseObject(formBody, Map.class);
            final String payMerchantId = acquirerParams.getPay_merchant_id();
            final com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = lklV3Service.uploadFileSupply(payMerchantId, BeanUtil.getPropLong(map, "ecApplyId"));
            if (!contractResponse.isSuccess()) {
                directStatusBiz.createOrUpdateDirectStatus(merchantSn, devCode, DirectStatus.STATUS_BIZ_FAIL, contractResponse.getMessage());
                return response.setMsg(contractResponse.getMessage());
            }
            //推送消息
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, devCode, DirectStatus.STATUS_SUCCESS, null);
            //调用交易接口初始化信息
            updateLklOpenMerchantConfig(request, map, acquirerParams.getProvider_merchant_id());
            response.setSuccess(Boolean.TRUE);
            //同步拉卡拉门店
            CompletableFuture.runAsync(() -> storeCreateConsumer.syncLklStore(merchantSn));
            //取消待办
            lklEcApplyService.cancelByMerchantSn(merchantSn,devCode);
        } catch (Exception exception) {
            log.error("applyPos error request:{},exception:{}", JSONObject.toJSONString(request), exception);
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, devCode, DirectStatus.STATUS_BIZ_FAIL, exception.getMessage());
            response.setMsg(exception.getMessage());
        }
        return response;
    }

    @Override
    public LklV3MerchantResponse getLklMerchant(String merchantSn) {
        LklV3MerchantResponse lKlV3PosResponse = new LklV3MerchantResponse();
        //lklV3商户号
        final String payMerchantId = Optional.ofNullable(merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue())).map(param -> param.getPay_merchant_id())
                .orElse(null);
        //还没有进件成功
        if (StringUtils.isEmpty(payMerchantId)) {
            return lKlV3PosResponse;
        }
        final LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
        final Map lklV3MerchantResponse = lklV3Service.queryMerchant(payMerchantId, null, lklV3Param);

        lKlV3PosResponse = Optional.ofNullable(lklV3MerchantResponse)
                .map(response -> MapUtils.getMap(lklV3MerchantResponse, "respData"))
                .map(respData -> JSON.parseObject(JSON.toJSONString(respData), LklV3MerchantResponse.class)).orElseGet(LklV3MerchantResponse::new);
        return lKlV3PosResponse;
    }

    @Override
    public List<LimitResp> queryLimit(String merchantSn) {
        return factory.getAcquirePosServiceByAcquire(McConstant.ACQUIRER_LKLV3).queryLimit(merchantSn);
    }


    /**
     * 交易参数
     *
     * @param request
     * @param map
     * @param providerMerchantId 银联商户号
     */
    public void updateLklOpenMerchantConfig(ApplyPosRequest request, Map map, String providerMerchantId) {
        final Map<String, Map> bankcardFee = MapUtils.getMap(map, "bankcard_fee");
        final long tradeComboId = BeanUtil.getPropLong(map, "tradeComboId");
        final MerchantInfo merchant = merchantService.getMerchantBySn(request.getMerchantSn(), null);
        final Map merchantConfigParams = CollectionUtil.hashMap("merchant_id", merchant.getId(), "payway", PaywayEnum.BANK_CARD.getValue(), "b2c_status", 0, "c2b_status", 1, "c2b_fee_rate", "0.55", "c2b_agent_name", "1034_21_false_true_0001"
                , "wap_status", 0, "mini_status", 0, "app_status", 0, "h5_status", 0, "provider", ProviderEnum.PROVIDER_LKL_OPEN.getValue(), "params",
                CollectionUtil.hashMap("lakala_open_trade_params", CollectionUtil.hashMap("merc_id", providerMerchantId, "term_id", "", "term_no", "")));
        //交易参数管理
        String merchantSn = merchant.getSn();
        tradeConfigService.updateLklOpenMerchantConfig(merchantConfigParams);
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest();
        applyFeeRateRequest.setMerchantSn(merchantSn);
        applyFeeRateRequest.setAuditSn("T9业务开通");
        applyFeeRateRequest.setTradeComboId(tradeComboId);
        applyFeeRateRequest.setApplyPartialPayway(Boolean.FALSE);
        final ArrayList<Map> list = Lists.newArrayList();
        bankcardFee.forEach((k, v) -> {
            final Map detailMap = CollectionUtil.hashMap("type", k, "fee", BeanUtil.getPropString(v, "fee"));
            final String maxFee = BeanUtil.getPropString(v, "max");
            if (StringUtils.isNotBlank(maxFee)) {
                detailMap.put("max", maxFee);
            }
            list.add(detailMap);
        });
        Map bankPosMap = CollectionUtil.hashMap("fee_type", "channel", "value", list);

        final Map applyFeeRateMap = CollectionUtil.hashMap(String.valueOf(PaywayEnum.ALIPAY.getValue()), "0.6", String.valueOf(PaywayEnum.WEIXIN.getValue()), "0.6", String.valueOf(PaywayEnum.UNIONPAY.getValue()), "0.6", String.valueOf(PaywayEnum.BESTPAY.getValue()), "0.6",
                String.valueOf(PaywayEnum.BANK_CARD.getValue()), JSONObject.toJSONString(bankPosMap));
        applyFeeRateRequest.setApplyFeeRateMap(applyFeeRateMap);
        applyFeeRateRequest.setCheck(Boolean.TRUE);
        feeRateService.applyFeeRateOne(applyFeeRateRequest);
        supportService.removeCachedParams(merchantSn);
        //记录在sub_biz_param表中
        String t9TradeAppId = subBizParamsBiz.getT9TradeAppId();
        subBizParamsBiz.updateSubBizParams(merchantSn, t9TradeAppId, ProviderEnum.PROVIDER_LAKALA_V3.getValue(), new MerchantProviderParams());
    }

    @Override
    public ForeignCard getForeignCard(String merchantSn) {
        ForeignCard foreignCard = foreignCardMapper.selectByMerchantSnAndCode(merchantSn,lklForeignCardDevCode);
        return foreignCard;
    }


    @Override
    public PreAuthApply getPreAuthApply(String merchantSn) {
        PreAuthApply preAuthApply = preAuthApplyMapper.selectLKlByMerchantSn(merchantSn);
        return preAuthApply;
    }

    @Override
    public String applyForeignCard(ApplyForeignCardRequest request) {
        String merchantId = request.getMerchantId();
        MerchantInfo merchantInfo = Optional.ofNullable(merchantService.getMerchantById(merchantId, null))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));
        String merchantSn = merchantInfo.getSn();
        if (!subBizParamsBiz.checkBankCardConsistence(merchantSn, AcquirerTypeEnum.LKL_V3.getValue())) {
            throw new CommonPubBizException(SubBizParamsBiz.BANK_CARD_NOT_CONSISTENCE_ERROR_MSG);
        }
        ForeignCard foreignCard = Optional.ofNullable(foreignCardMapper.selectByMerchantSnAndCode(merchantSn,lklForeignCardDevCode)).orElseGet(ForeignCard::new);
        //开通成功
        if(Objects.equals(foreignCard.getStatus(),ForeignCardConstant.Status.SUCCESS)) {
            throw new CommonPubBizException("已经开通成功");
        }
        //费率是否为空
        Map feeMap = request.getFeeMap();
        final Map<String,Map<String,Object>> map = feeMap;
        map.forEach((k,v) -> {
            final String fee = BeanUtil.getPropString(v, "fee");
            if(org.springframework.util.StringUtils.isEmpty(fee)) {
                throw new CommonPubBizException("费率不为空");
            }
        });
        Map<String, Object> t9FeeMap = getT9FeeMap(merchantId);
        //先插入状态信息crm可以立刻查询到
        directStatusBiz.createOrUpdateDirectStatus(merchantSn, lklForeignCardDevCode, DirectStatus.STATUS_PROCESS, null);
        final com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = lklV3Service.ecApplyWithForeignBankCard(merchantSn,
                feeMap,
                t9FeeMap,
                request.getSignMobile());
        final Map<String, Object> responseParam = contractResponse.getResponseParam();
        final EcApplyData ecApplyData = new MyObjectMapper().convertValue(responseParam.computeIfAbsent("respData", t -> responseParam.get("data")), EcApplyData.class);
        if(!contractResponse.isSuccess() &&
                (StrUtil.contains(contractResponse.getMessage(),"三四要素认证不通过") || responseParam.containsValue("087900"))) {
            final Long ecApplyId = ecApplyData.getEcApplyId();
            //当三四要素认证不通过时则需要记录
            final LklEcApplyDTO dto = new LklEcApplyDTO();
            dto.setDevCode(lklForeignCardDevCode);
            dto.setMerchantSn(merchantSn);
            dto.setEcApplyId(String.valueOf(ecApplyId));
            dto.setFeeMap(feeMap);
            lklEcApplyService.saveManualAuditingEcApply(dto);
            //申请人工复核
            final com.wosai.upay.merchant.contract.model.ContractResponse ecApplyManualResp = lklV3Service.ecApplyManual(merchantSn, ecApplyId);
            final Map<String, Object> manualRespResponseParam = ecApplyManualResp.getResponseParam();
            if(!ecApplyManualResp.isSuccess() || !manualRespResponseParam.containsValue("000000")) {
                throw new CommonPubBizException(StringUtils.isEmpty(ecApplyManualResp.getMessage()) ? "人工复核申请失败" : ecApplyManualResp.getMessage());
            }
            //保存在本地外卡记录表
            createOrUpdateForeignCard(merchantSn, ecApplyId,request);
            //返回三四要素认证不通过
            throw new CommonPubBizException("三四要素认证不通过");
        }

        //  4. 发起电子合同申请，若报错【code:"087901"， message:"手机号实名认证不通过"】时，需映射报错 前端展示【签约手机号实名认证不通过，请确认为经营者/法人本人在运营商实名登记的手机号！】
        if(!contractResponse.isSuccess()
                && (responseParam.containsValue("手机号实名认证不通过") || responseParam.containsValue("087901"))) {

            throw new CommonPubBizException("手机号实名认证不通过");
        }
        if(!contractResponse.isSuccess()) {
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, lklForeignCardDevCode, DirectStatus.STATUS_BIZ_FAIL, null);
            String message = contractResponse.getMessage();
            throw new CommonPubBizException(StringUtils.isAllEmpty(message) ? "申请外卡电子合同失败" : message);
        }
        Long ecApplyId = ecApplyData.getEcApplyId();
        //没有开通或进行中的
        createOrUpdateForeignCard(merchantSn,ecApplyId,request);
        return ecApplyData.getResultUrl();
    }



    public void createOrUpdateForeignCard(String merchantSn, Long ecApplyId,ApplyForeignCardRequest request) {
        ForeignCard foreignCard = Optional.ofNullable(foreignCardMapper.selectByMerchantSnAndCode(merchantSn,lklForeignCardDevCode))
                .orElseGet(ForeignCard::new);
        //开通成功
        if(Objects.equals(foreignCard.getStatus(),ForeignCardConstant.Status.SUCCESS)) {
           return;
        }
        if(Objects.isNull(foreignCard.getStatus()) || Objects.equals(foreignCard.getStatus(),ForeignCardConstant.Status.FAIL)){
            //插入新纪录
            ForeignCard card = new ForeignCard();
            card.setMerchant_sn(merchantSn);
            card.setStatus(ForeignCardConstant.Status.APPLYING);
            card.setForm_body(JSONObject.toJSONString(request));
            card.setEcApplyId(String.valueOf(ecApplyId));
            card.setDev_code(lklForeignCardDevCode);
            foreignCardMapper.insert(card);
        }else {
            //进行中则更新信息
            ForeignCard update = new ForeignCard();
            update.setId(foreignCard.getId());
            update.setStatus(ForeignCardConstant.Status.APPLYING);
            update.setForm_body(JSONObject.toJSONString(request));
            update.setEcApplyId(String.valueOf(ecApplyId));
            foreignCardMapper.updateByPrimaryKeySelective(update);
        }
    }



    @Override
    public String applyPreAuth(String merchantId) {
        MerchantInfo merchantInfo = Optional.ofNullable(merchantService.getMerchantById(merchantId, null))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));

        String merchantSn = merchantInfo.getSn();
        PreAuthApply preAuth = Optional.ofNullable(preAuthApplyMapper.selectLKlByMerchantSn(merchantSn)).orElseGet(PreAuthApply::new);
        //开通成功
        if(Objects.equals(preAuth.getStatus(),ForeignCardConstant.Status.SUCCESS)) {
            throw new CommonPubBizException("已经开通成功");
        }
        Map<String, Object> t9FeeMap = getT9FeeMap(merchantId);
        final com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = lklV3Service.ecApplyWithPreAuthSpecial(merchantSn, t9FeeMap);
        if(!contractResponse.isSuccess()) {
            String message = contractResponse.getMessage();
            throw new CommonPubBizException(StringUtils.isAllEmpty(message) ? "申请预授权电子合同失败" : message);
        }
        final Map<String, Object> responseParam = contractResponse.getResponseParam();
        final EcApplyData ecApplyData = new MyObjectMapper().convertValue(responseParam.computeIfAbsent("respData", t -> responseParam.get("data")), EcApplyData.class);
        Long ecApplyId = ecApplyData.getEcApplyId();
        //没有开通过或者失败
        if(Objects.isNull(preAuth.getStatus()) || Objects.equals(preAuth.getStatus(),ForeignCardConstant.Status.FAIL)){
            //插入新纪录
            PreAuthApply preAuthApply = new PreAuthApply();
            preAuthApply.setMerchant_sn(merchantSn);
            preAuthApply.setStatus(PreAuthApplyConstant.Status.APPLYING);
            preAuthApply.setForm_body(JSONObject.toJSONString(CollectionUtil.hashMap("merchantId",merchantId)));
            preAuthApply.setEcApplyId(String.valueOf(ecApplyId));
            preAuthApply.setDev_code(lklPreAuthDevCode);
            preAuthApplyMapper.insert(preAuthApply);
            return ecApplyData.getResultUrl();
        }
        //进行中则更新信息
        PreAuthApply update = new PreAuthApply();
        update.setId(preAuth.getId());
        update.setStatus(PreAuthApplyConstant.Status.APPLYING);
        update.setForm_body(JSONObject.toJSONString(CollectionUtil.hashMap("merchantId",merchantId)));
        update.setEcApplyId(String.valueOf(ecApplyId));
        preAuthApplyMapper.updateByPrimaryKeySelective(update);
        return ecApplyData.getResultUrl();
    }


    @Override
    public CommonResult queryForeignCardContract(String merchantId) {
        CommonResult result = new CommonResult();
        //给定初始值
        result.setCode(CommonResult.ERROR);
        MerchantInfo merchantInfo = Optional.ofNullable(merchantService.getMerchantById(merchantId, null))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));
        String merchantSn = merchantInfo.getSn();
        ForeignCard foreignCard = foreignCardMapper.selectByMerchantSnAndCode(merchantSn,lklForeignCardDevCode);
        if(Objects.isNull(foreignCard)) {
            throw new CommonPubBizException("请退出重试");
        }
        String ecApplyId = foreignCard.getEcApplyId();
        com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = lklV3Service.ecqStatus(Long.valueOf(ecApplyId));
        if(contractResponse.isBusinessFail()) {
            //开通记录变为失败
            ForeignCard update = new ForeignCard();
            update.setId(foreignCard.getId());
            update.setStatus(ForeignCardConstant.Status.FAIL);
            foreignCardMapper.updateByPrimaryKeySelective(update);
            //开通记录也变为失败
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, lklForeignCardDevCode, DirectStatus.STATUS_BIZ_FAIL, null);
            lklEcApplyService.cancelByMerchantSn(merchantSn,lklForeignCardDevCode);
            return result.setCode(CommonResult.BIZ_FAIL).setMsg(contractResponse.getMessage());
        }
        if(contractResponse.isSystemFail()){
            return result.setCode(CommonResult.ERROR).setMsg(contractResponse.getMessage());
        }
        final Map<String, Object> responseParam = contractResponse.getResponseParam();
        //返回信息
        Map data = Optional.ofNullable(responseParam).map(response -> (Map) response.computeIfAbsent("respData", t -> response.get("data"))).orElseGet(HashMap::new);
        //合同状态
        //UNDONE 未完成 COMPLETED 已完成
        String ecStatus = MapUtils.getString(data, "ecStatus");
        //电子合同号
        String ecNo = MapUtils.getString(data, "ecNo");
        boolean completed = Objects.equals(ecStatus, "COMPLETED") && StringUtils.isNotBlank(ecNo);
        if(!completed) {
            return result;
        }
        //签约成功
        result.setCode(CommonResult.SUCCESS).setMsg("合同签署成功");
        lklEcApplyService.updateEcApplyStatusByApplyId(ecApplyId, LklEcApplyDO.Status.SUCCESS, LklEcApplyStatusRespEnum.SIGN_SUCCESS.getValue(), LklEcApplyStatusRespEnum.SIGN_SUCCESS.getText());
        return result;
    }

    @Override
    public void openForeignCard(String merchantId) {
        MerchantInfo merchantInfo = Optional.ofNullable(merchantService.getMerchantById(merchantId, null))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));
        String merchantSn = merchantInfo.getSn();
        ForeignCard foreignCard = foreignCardMapper.selectByMerchantSnAndCode(merchantSn,lklForeignCardDevCode);
        if(Objects.isNull(foreignCard)) {
            throw new CommonPubBizException("请退出重试");
        }
        String ecApplyId = foreignCard.getEcApplyId();
        //开通记录变为成功
        directStatusBiz.createOrUpdateDirectStatus(merchantSn, lklForeignCardDevCode, DirectStatus.STATUS_SUCCESS, null);
        //开通记录变为成功
        ForeignCard update = new ForeignCard();
        update.setId(foreignCard.getId());
        update.setStatus(ForeignCardConstant.Status.SUCCESS);
        //开通成功更新外卡redis状态
        String key = String.format(T9ServiceImpl.FOREIGN_CARD_OPEN_FLAG, merchantSn);
        jsonRedisTemplate.opsForValue().set(key,Boolean.TRUE, T9ServiceImpl.EXPIRATION_DURATION, T9ServiceImpl.EXPIRATION_TIME_UNIT);
        foreignCardMapper.updateByPrimaryKeySelective(update);
        //保存协议
        CompletableFuture.runAsync(() -> saveForeignCardAgreement(foreignCard, ecApplyId));
        //设置套餐
        CompletableFuture.runAsync(() -> applyForeignCardFeeRateOne(merchantInfo.getId(),merchantSn, foreignCard));
        //取消待办
        lklEcApplyService.cancelByMerchantSn(merchantSn,lklForeignCardDevCode);
    }

    /**
     * 保存外卡协议
     * @param foreignCard  协议
     * @param ecApplyId
     */
    public void saveForeignCardAgreement(ForeignCard foreignCard, String ecApplyId) {
        try {
            com.wosai.upay.merchant.contract.model.ContractResponse ecDownload = lklV3Service.ecDownload(Long.valueOf(ecApplyId));
            final Map<String, Object> ecDownloadResponseParam = ecDownload.getResponseParam();
            final Map map = (Map) ecDownloadResponseParam.computeIfAbsent("respData", t -> ecDownloadResponseParam.get("data"));
            byte[] ecFiles = Base64Utils.decodeFromUrlSafeString(MapUtils.getString(map, "ecFile"));
            // TODO 上传文件
            FileInputStream stream = convertBase64ToFileInputStream(ecFiles);
            if(Objects.nonNull(stream)) {
                String url = uploadToOss(stream, FOREIGN_CARD_BASE_DIR);
                //开通记录变为成功
                ForeignCard updateUrl = new ForeignCard();
                updateUrl.setId(foreignCard.getId());
                updateUrl.setContractUrl(url);
                foreignCardMapper.updateByPrimaryKeySelective(updateUrl);
                MerchantInfo merchant = merchantService.getMerchantBySn(foreignCard.getMerchant_sn(), null);
                // TODO 保存在协议管理平台
                agreementBiz.recordAgreementForT9(merchant.getId(),url);
            }
        } catch (Exception exception) {
            log.error("saveForeignCardAgreement error:{}",exception);
        }
    }

    /**
     * 设置费率
     * @param merchantSn
     * @param foreignCard
     */
    public void applyForeignCardFeeRateOne(String merchantId,String merchantSn, ForeignCard foreignCard) {
        try {
            Map formBody = foreignCard.getFormBody();
            Long tradeComboId = BeanUtil.getPropLong(formBody, ForeignCardConstant.FormBody.TRADE_COMBO_ID);
            //外卡费率
            Map feeMap = MapUtils.getMap(formBody, ForeignCardConstant.FormBody.FEE_MAP);
            //之前的T9开通时借记卡和贷记卡费率
            Map<String, Object> t9FeeMap = getT9FeeMap(merchantId);
            //所有费率信息放在一起
            feeMap.putAll(t9FeeMap);
            final ArrayList<Map> list = Lists.newArrayList();
            feeMap.forEach((k, v) -> {
                final Map detailMap = CollectionUtil.hashMap("type", String.valueOf(k).toLowerCase(), "fee", BeanUtil.getPropString(v, "fee"));
                final String maxFee = BeanUtil.getPropString(v, "max");
                if (StringUtils.isNotBlank(maxFee)) {
                    detailMap.put("max", maxFee);
                }
                list.add(detailMap);
            });
            Map bankPosMap = CollectionUtil.hashMap("fee_rate_type", "channel", "value", list);

            final Map applyFeeRateMap = CollectionUtil.hashMap(
                    String.valueOf(PaywayEnum.BANK_CARD.getValue()), JSONObject.toJSONString(bankPosMap));
            ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest();
            applyFeeRateRequest.setMerchantSn(merchantSn);
            applyFeeRateRequest.setAuditSn("开通外卡设置费率");
            applyFeeRateRequest.setTradeComboId(tradeComboId);
            applyFeeRateRequest.setApplyFeeRateMap(applyFeeRateMap);
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
            supportService.removeCachedParams(merchantSn);
        } catch (Exception exception) {
           log.error("applyForeignCardFeeRateOne error 商户号:{}",merchantSn,exception);
        }
    }


    @Override
    public CommonResult queryPreAuthContract(String merchantId) {
        CommonResult result = new CommonResult();
        //给定初始值
        result.setCode(CommonResult.ERROR);
        MerchantInfo merchantInfo = Optional.ofNullable(merchantService.getMerchantById(merchantId, null))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));
        String merchantSn = merchantInfo.getSn();
        PreAuthApply preAuth = preAuthApplyMapper.selectLKlByMerchantSn(merchantSn);
        if(Objects.isNull(preAuth)) {
            throw new CommonPubBizException("请退出重试");
        }
        String ecApplyId = preAuth.getEcApplyId();
        com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = lklV3Service.ecqStatus(Long.valueOf(ecApplyId));
        if(contractResponse.isBusinessFail()) {
            //开通记录变为失败
            PreAuthApply update = new PreAuthApply();
            update.setId(preAuth.getId());
            update.setStatus(ForeignCardConstant.Status.FAIL);
            preAuthApplyMapper.updateByPrimaryKeySelective(update);
            return result.setCode(CommonResult.BIZ_FAIL).setMsg(contractResponse.getMessage());
        }
        if(contractResponse.isSystemFail()){
            return result.setCode(CommonResult.ERROR).setMsg(contractResponse.getMessage());
        }
        final Map<String, Object> responseParam = contractResponse.getResponseParam();
        //返回信息
        Map data = Optional.ofNullable(responseParam).map(response -> (Map) response.computeIfAbsent("respData", t -> response.get("data"))).orElseGet(HashMap::new);
        //合同状态
        //UNDONE 未完成 COMPLETED 已完成
        String ecStatus = MapUtils.getString(data, "ecStatus");
        //电子合同号
        String ecNo = MapUtils.getString(data, "ecNo");
        //签约成功
        if(Objects.equals(ecStatus,"COMPLETED") && StringUtils.isNotBlank(ecNo)) {
            result.setCode(CommonResult.SUCCESS).setMsg("合同签署成功");
            //开通记录变为成功
            PreAuthApply update = new PreAuthApply();
            update.setId(preAuth.getId());
            update.setStatus(PreAuthApplyConstant.Status.SUCCESS);
            preAuthApplyMapper.updateByPrimaryKeySelective(update);
            //cop预授权申请
            CompletableFuture.runAsync(()->createFlowWithPreAuthSpecial(merchantId, merchantSn));
            //异步下载协议并将文件保存到协议管理平台
            CompletableFuture.runAsync(() -> saveAuthAgreement(preAuth, ecApplyId));
        }
        return result;
    }

    /**
     * 保存预授权协议
     * @param preAuth
     * @param ecApplyId
     */
    public void saveAuthAgreement(PreAuthApply preAuth, String ecApplyId) {
        try {
            com.wosai.upay.merchant.contract.model.ContractResponse ecDownload = lklV3Service.ecDownload(Long.valueOf(ecApplyId));
            final Map<String, Object> ecDownloadResponseParam = ecDownload.getResponseParam();
            final Map map = (Map) ecDownloadResponseParam.computeIfAbsent("respData", t -> ecDownloadResponseParam.get("data"));
            byte[] ecFiles = Base64Utils.decodeFromUrlSafeString(MapUtils.getString(map, "ecFile"));
            // TODO 上传文件
            FileInputStream stream = convertBase64ToFileInputStream(ecFiles);
            if(Objects.nonNull(stream)) {
                String url = uploadToOss(stream, PRE_AUTH_BASE_DIR);
                //开通记录变为成功
                PreAuthApply updateUrl = new PreAuthApply();
                updateUrl.setId(preAuth.getId());
                updateUrl.setContractUrl(url);
                preAuthApplyMapper.updateByPrimaryKeySelective(updateUrl);
                // TODO 保存在协议管理平台
                MerchantInfo merchant = merchantService.getMerchantBySn(preAuth.getMerchant_sn(), null);
                agreementBiz.recordAgreementForT9(merchant.getId(),url);
            }
        } catch (Exception exception) {
            log.error("saveAuthAgreement error:{}",exception);
        }
    }

    /**
     * cop预授权申请
     * @param merchantId
     * @param merchantSn
     */
    public void createFlowWithPreAuthSpecial(String merchantId, String merchantSn) {
        //TODO 已有银行卡终端发起cop预授权申请
        List<String> simpleSuperPosList = Lists.newArrayList(applicationApolloConfig.getSimpleSuperPos());
        try {
            simpleSuperPosList.stream().forEach(vendor -> {
                Map filter = CollectionUtil.hashMap(Terminal.VENDOR_APP_APPID, vendor, Terminal.STATUS, Terminal.STATUS_ACTIVATED);
                ListResult terminals = terminalService.getTerminals(merchantId, null, null, filter);
                List<Map> records = terminals.getRecords();
                if(CollectionUtils.isEmpty(records)) {
                    return;
                }
                records.stream().forEach(ter -> {
                    //收钱吧终端Id
                    String termId = BeanUtil.getPropString(ter, DaoConstants.ID);
                    //收钱吧终端号
                    String termSn = BeanUtil.getPropString(ter, Terminal.SN);
                    //收钱吧设备号
                    String deviceFingerprint = BeanUtil.getPropString(ter, Terminal.DEVICE_FINGERPRINT);
                    String store_id = BeanUtil.getPropString(ter, Terminal.STORE_ID);
                    StoreInfo store = storeService.getStoreById(store_id, null);
                    String termNo = lklV3ShopTermBiz.getTermNo(store.getSn(), termId);
                    if(StringUtils.isNotBlank(termNo)) {
                        try {
                            //预授权cop
                            com.wosai.upay.merchant.contract.model.ContractResponse response = lklV3Service.createFlowWithPreAuthSpecial(merchantSn, termNo, deviceFingerprint);
                            //钉钉推送
                            Map<String, Object> copResponse = response.getResponseParam();
                            Map copData = Optional.ofNullable(copResponse).map(res -> (Map) res.computeIfAbsent("respData", t -> copResponse.get("data"))).orElseGet(HashMap::new);
                            String workFlowNo = BeanUtil.getPropString(copData, "workFlowNo");
                            String remark = StringUtils.isAllBlank(workFlowNo) ? response.getMessage() : workFlowNo;
                            MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
                        } catch (Exception exception) {
                            log.error("createFlowWithPreAuthSpecial error",exception);
                        }
                    }
                });
            });
        } catch (Exception exception) {
            log.error("createFlowWithPreAuthSpecial error:{}",exception);
        }
    }


    /**
     * 上传文件到阿里云
     * @param inputStream
     * @param baseKey
     * @return
     */
    public String uploadToOss(FileInputStream inputStream,String baseKey) {
        OSSClient client = OssAK.buildOSSClient(ENDPOINT_URL);
        String key = baseKey + UUID.randomUUID()+".pdf";
        client.putObject(STATICS_BUCKET_NAME, key,inputStream);
        return ENDPOINT_URL+STATICS_BUCKET_NAME+"/"+key;
    }


    public static FileInputStream convertBase64ToFileInputStream( byte[] decodedBytes) {
        File tempFile = null;
        try {
            // 创建临时文件
            tempFile = File.createTempFile("temp", ".pdf");
            // 将字节数组写入临时文件
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(decodedBytes);
            }
            // 创建并返回FileInputStream
            return new FileInputStream(tempFile);
        } catch (IOException e) {
            log.error("convertBase64ToFileInputStream error",e);
        } finally {
            FileUtils.deleteQuietly(tempFile);
        }
        return null;
    }



    public Map<String,Object> getT9FeeMap(String merchantId) {
        Map feeMap = new HashMap();
        //先查询交易是否存在刷卡费率
        final Optional<Map> bankcardFee = Optional.ofNullable(tradeConfigService.getAnalyzedMerchantConfigsByPayWayList(merchantId, new int[]{PaywayEnum.BANK_CARD.getValue()}))
                .filter(list -> !list.isEmpty())
                .map(list -> (Map<String, String>) list.get(0))  // 类型转换
                .map(bankMap -> MapUtils.getMap(bankMap, "bankcard_fee"));
        if(bankcardFee.isPresent()) {
            //交易那边存储的所有刷卡相关费率edc,dcc,debit,credit
            final Map map = bankcardFee.get();
            //信用卡
            feeMap.put("credit",MapUtils.getMap(map,"credit"));
            //借记卡,如果有封顶值的话交易这边的是以分为单位,但是三方以元为单位,所以要转换一下
            final Map debitMap = MapUtils.getMap(map, "debit");
            if(debitMap.containsKey("max") && Objects.nonNull(debitMap.get("max"))) {
                final String max = MapUtils.getString(debitMap, "max");
                final String yuan = convertCentsToYuanFormatted(max);
                debitMap.put("max",yuan);
            }
            feeMap.put("debit",debitMap);
        }else {
            //产品说绑定一定在银行卡业务开通以后,所以可以拿到绑定银行卡pos的费率
            final ApiRequestParam param = new ApiRequestParam();
            final GetDevParamReq devParamReq = new GetDevParamReq();
            devParamReq.setDevCode("GF7GNKL9O48N");
            devParamReq.setMerchantId(merchantId);
            param.setBodyParams(devParamReq);
            final Map devParam = crmEdgeService.getDevParam(param);
            feeMap = (Map) devParam.get("feeMap");
            if(CollectionUtils.isEmpty(feeMap)) {
                throw new CommonPubBizException("未开通T9银行卡");
            }
        }
        return feeMap;
    }


    /**
     * 将分转换为元，并格式化为两位小数
     * @param amountInCentsStr 分
     * @return 格式化后的元
     */
    public static String convertCentsToYuanFormatted(String amountInCentsStr) {
        BigDecimal amountInCents = new BigDecimal(amountInCentsStr);
        BigDecimal amountInYuan = amountInCents.divide(new BigDecimal(100));
        return String.format("%.2f", amountInYuan);
    }

    @Override
    public void initCrmRecord() {
        final List<ForeignCard> cardList = foreignCardMapper.selectLklForeign();
        if(CollectionUtils.isEmpty(cardList)) {
            return;
        }
       CompletableFuture.runAsync(() -> {
           cardList.stream().forEach(card -> {
               ThreadUtil.sleep(500L);
               try {
                   importCrmInfoService.createOrUpdateBizOpenInfo(card.getMerchant_sn(), AppInfoModel.STATUS_SUCCESS,"7f5967e3-0900-4e91-aa0f-8adb77411cea",null);
               } catch (Exception e) {
                   log.error("initCrmRecord error",e);
               }
           });
       });
    }
}
