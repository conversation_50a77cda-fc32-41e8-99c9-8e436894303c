package com.wosai.upay.job.enume;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
public enum BankDirectApplyRefEnum implements ITextValueEnum<Integer> {

    /**
     * 邮储银行
     */
    PSBC(1, "邮储银行"),

    /**
     * 广发银行
     */
    CGB(2, "广发银行"),

    /**
     * 建设银行
     */
    CCB(3, "建设银行"),

    /**
     * 华夏银行
     */
    HXB(4, "华夏银行"),
    /**
     * 工商银行
     */
    ICBC(5, "工商银行"),
    /**
     * 平安银行
     */
    PAB(6, "平安银行"),
    /**
     * 福建农信社
     */
    FJNX(7, "福建农信社"),
    /**
     * 江苏银行
     */
    JSB(8, "江苏银行"),
    /**
     * 中国民生银行
     */
    CMBC(9, "中国民生银行"),
    LZB(10, "泸州银行"),
    // 复用民生银行?
    UMB(11, "中投科信"),

    PSBC_SHANXING(12, "邮储银行山西分行"),

    BCS(13,"长沙银行");

    BankDirectApplyRefEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }
    private final int value;
    private final String text;

    @Override
    public Integer getValue() {
        return this.value;
    }

    @Override
    public String getText() {
        return this.text;
    }
}
