package com.wosai.upay.job.service;


import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.alipay.AlipayMchResponse;
import com.wosai.upay.job.model.alipay.UpdateAlipayMchInfoRequest;
import com.wosai.upay.job.model.alipay.UpdateAlipayMchInfoResponse;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.exception.ContractException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * @Description: 支付宝的通用service
 * <AUTHOR>
 * @Date 2022/08/03
 **/
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class JobAlipayServiceImpl implements JobAlipayService {


    @Autowired
    ComposeAcquirerBiz composeAcquirerBiz;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;


    @Override
    public AlipayMchResponse getAlipayMchInfoBySubMchId(String subMchId) {
        AlipayMchResponse response = new AlipayMchResponse();
        try {
            response.setAlipayMchInfo(composeAcquirerBiz.getAlipayMchInfoWithBank(subMchId));
        } catch (ContractException e) {
            log.info("根据子商户号查询商户信息异常: {}", e.getMessage());
            response.setMsg(e.getMessage());
            response.setSuccess(false);
            return response;
        } catch (Exception e) {
            log.error("根据子商户号查询商户信息异常:", e);
            response.setMsg(e.getMessage());
            response.setSuccess(false);
            return response;
        }
        response.setSuccess(true);
        response.setMsg("SUCCESS");
        return response;
    }

    @Override
    public AlipayMchResponse getAlipayMerchantNameBySubMchId(String subMchId) {
        AlipayMchResponse response = new AlipayMchResponse();
        try {
            MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(subMchId);
            if(StringUtils.isNotBlank(merchantProviderParams.getMerchant_name())){
                response.setAlipayMchInfo(new AlipayMchInfo().setName(merchantProviderParams.getMerchant_name()).setSub_merchant_id(subMchId));
            }else {
                response.setAlipayMchInfo(composeAcquirerBiz.getAlipayMchInfoWithBank(subMchId));
                if(StringUtils.isNotBlank(response.getAlipayMchInfo().getName()) && !response.getAlipayMchInfo().getName().equals(merchantProviderParams.getMerchant_name())) {
                    merchantProviderParamsMapper.updateMerchantNameById(merchantProviderParams.getId(), response.getAlipayMchInfo().getName());
                }
            }
        } catch (ContractException e) {
            log.info("根据子商户号查询商户信息异常: {}", e.getMessage());
            response.setMsg(e.getMessage());
            response.setSuccess(false);
            return response;
        } catch (Exception e) {
            log.error("根据子商户号查询商户信息异常:", e);
            response.setMsg(e.getMessage());
            response.setSuccess(false);
            return response;
        }
        response.setSuccess(true);
        response.setMsg("SUCCESS");
        return response;
    }


    @Override
    public UpdateAlipayMchInfoResponse updateAlipayMchInfoBySubMchId(UpdateAlipayMchInfoRequest request) {
        UpdateAlipayMchInfoResponse response = new UpdateAlipayMchInfoResponse();
        try {
            ContractResponse contractResponse = composeAcquirerBiz.updateAlipayMchInfo(merchantProviderParamsMapper.getByPayMerchantId(request.getSubMchId()), request.getAlipayMchInfo());
            response.setCode(contractResponse.getCode()).setMessage(contractResponse.getMessage())
                    .setRequestParam(contractResponse.getRequestParam())
                    .setResponseParam(contractResponse.getResponseParam())
                    .setTradeParam(contractResponse.getTradeParam())
                    .setMerchantProviderParamsId(contractResponse.getMerchantProviderParamsId());
        } catch (Exception e) {
            log.error("根据子商户号修改商户信息异常:", e);
            response.setMessage(e.getMessage());
            response.setCode(400);
            return response;
        }
        return response;
    }
}
