<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.McAcquirerChangeMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.McAcquirerChange" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="apply_id" property="apply_id" jdbcType="VARCHAR" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="merchant_id" property="merchant_id" jdbcType="VARCHAR" />
    <result column="source_acquirer" property="source_acquirer" jdbcType="VARCHAR" />
    <result column="target_acquirer" property="target_acquirer" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="memo" property="memo" jdbcType="VARCHAR" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="immediately" property="immediately" jdbcType="BIT" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.McAcquirerChange" extends="BaseResultMap" >
    <result column="process" property="process" jdbcType="LONGVARCHAR" />
    <result column="extra" property="extra" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, apply_id, merchant_sn, merchant_id, source_acquirer, target_acquirer, status, 
    memo, create_at, update_at,immediately
  </sql>
  <sql id="Blob_Column_List" >
    process, extra
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.wosai.upay.job.model.DO.McAcquirerChangeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mc_acquirer_change
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.wosai.upay.job.model.DO.McAcquirerChangeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mc_acquirer_change
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mc_acquirer_change
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from mc_acquirer_change
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.wosai.upay.job.model.DO.McAcquirerChangeExample" >
    delete from mc_acquirer_change
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.DO.McAcquirerChange" >
    insert into mc_acquirer_change (id, apply_id, merchant_sn, 
      merchant_id, source_acquirer, target_acquirer, 
      status, memo, create_at, 
      update_at, process, extra,immediately
      )
    values (#{id,jdbcType=INTEGER}, #{apply_id,jdbcType=VARCHAR}, #{merchant_sn,jdbcType=VARCHAR}, 
      #{merchant_id,jdbcType=VARCHAR}, #{source_acquirer,jdbcType=VARCHAR}, #{target_acquirer,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{memo,jdbcType=VARCHAR}, #{create_at,jdbcType=TIMESTAMP}, 
      #{update_at,jdbcType=TIMESTAMP}, #{process,jdbcType=LONGVARCHAR}, #{extra,jdbcType=LONGVARCHAR},#{immediately}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.McAcquirerChange" >
    insert into mc_acquirer_change
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="apply_id != null" >
        apply_id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="merchant_id != null" >
        merchant_id,
      </if>
      <if test="source_acquirer != null" >
        source_acquirer,
      </if>
      <if test="target_acquirer != null" >
        target_acquirer,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="memo != null" >
        memo,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="process != null" >
        process,
      </if>
      <if test="extra != null" >
        extra,
      </if>
      <if test="immediately != null" >
        immediately,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="apply_id != null" >
        #{apply_id,jdbcType=VARCHAR},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="merchant_id != null" >
        #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="source_acquirer != null" >
        #{source_acquirer,jdbcType=VARCHAR},
      </if>
      <if test="target_acquirer != null" >
        #{target_acquirer,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="memo != null" >
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="process != null" >
        #{process,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        #{extra,jdbcType=LONGVARCHAR},
      </if>
      <if test="immediately != null" >
        #{immediately},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.job.model.DO.McAcquirerChangeExample" resultType="java.lang.Integer" >
    select count(*) from mc_acquirer_change
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update mc_acquirer_change
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.apply_id != null" >
        apply_id = #{record.apply_id,jdbcType=VARCHAR},
      </if>
      <if test="record.merchant_sn != null" >
        merchant_sn = #{record.merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="record.merchant_id != null" >
        merchant_id = #{record.merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="record.source_acquirer != null" >
        source_acquirer = #{record.source_acquirer,jdbcType=VARCHAR},
      </if>
      <if test="record.target_acquirer != null" >
        target_acquirer = #{record.target_acquirer,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.memo != null" >
        memo = #{record.memo,jdbcType=VARCHAR},
      </if>
      <if test="record.create_at != null" >
        create_at = #{record.create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="record.update_at != null" >
        update_at = #{record.update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="record.process != null" >
        process = #{record.process,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.extra != null" >
        extra = #{record.extra,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.immediately != null" >
        immediately = #{record.immediately},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update mc_acquirer_change
    set id = #{record.id,jdbcType=INTEGER},
      apply_id = #{record.apply_id,jdbcType=VARCHAR},
      merchant_sn = #{record.merchant_sn,jdbcType=VARCHAR},
      merchant_id = #{record.merchant_id,jdbcType=VARCHAR},
      source_acquirer = #{record.source_acquirer,jdbcType=VARCHAR},
      target_acquirer = #{record.target_acquirer,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      memo = #{record.memo,jdbcType=VARCHAR},
      create_at = #{record.create_at,jdbcType=TIMESTAMP},
      update_at = #{record.update_at,jdbcType=TIMESTAMP},
      process = #{record.process,jdbcType=LONGVARCHAR},
      extra = #{record.extra,jdbcType=LONGVARCHAR},
    immediately=#{record.immediately}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update mc_acquirer_change
    set id = #{record.id,jdbcType=INTEGER},
      apply_id = #{record.apply_id,jdbcType=VARCHAR},
      merchant_sn = #{record.merchant_sn,jdbcType=VARCHAR},
      merchant_id = #{record.merchant_id,jdbcType=VARCHAR},
      source_acquirer = #{record.source_acquirer,jdbcType=VARCHAR},
      target_acquirer = #{record.target_acquirer,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      memo = #{record.memo,jdbcType=VARCHAR},
      create_at = #{record.create_at,jdbcType=TIMESTAMP},
      update_at = #{record.update_at,jdbcType=TIMESTAMP},
    immediately = #{record.immediately}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.McAcquirerChange" >
    update mc_acquirer_change
    <set >
      <if test="apply_id != null" >
        apply_id = #{apply_id,jdbcType=VARCHAR},
      </if>
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="merchant_id != null" >
        merchant_id = #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="source_acquirer != null" >
        source_acquirer = #{source_acquirer,jdbcType=VARCHAR},
      </if>
      <if test="target_acquirer != null" >
        target_acquirer = #{target_acquirer,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="memo != null" >
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="process != null" >
        process = #{process,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
      <if test="immediately != null" >
        immediately = #{immediately},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.DO.McAcquirerChange" >
    update mc_acquirer_change
    set apply_id = #{apply_id,jdbcType=VARCHAR},
      merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      merchant_id = #{merchant_id,jdbcType=VARCHAR},
      source_acquirer = #{source_acquirer,jdbcType=VARCHAR},
      target_acquirer = #{target_acquirer,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      memo = #{memo,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      process = #{process,jdbcType=LONGVARCHAR},
      extra = #{extra,jdbcType=LONGVARCHAR},
      immediately = #{immediately}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.McAcquirerChange" >
    update mc_acquirer_change
    set apply_id = #{apply_id,jdbcType=VARCHAR},
      merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      merchant_id = #{merchant_id,jdbcType=VARCHAR},
      source_acquirer = #{source_acquirer,jdbcType=VARCHAR},
      target_acquirer = #{target_acquirer,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      memo = #{memo,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      immediately = #{immediately}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>