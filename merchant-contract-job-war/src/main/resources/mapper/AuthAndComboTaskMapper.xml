<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.AuthAndComboTaskMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.AuthAndComboTask" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_id" property="merchant_id" jdbcType="VARCHAR" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="sub_mch_id" property="sub_mch_id" jdbcType="VARCHAR" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.AuthAndComboTask" extends="BaseResultMap" >
    <result column="form_body" property="form_body" jdbcType="LONGVARCHAR" />
    <result column="result" property="result" jdbcType="LONGVARCHAR" />
    <result column="extra" property="extra" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, merchant_id, merchant_sn, status, sub_mch_id, create_at, update_at
  </sql>
  <sql id="Blob_Column_List" >
    form_body, result, extra
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from auth_and_combo_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from auth_and_combo_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.AuthAndComboTask" >
    insert into auth_and_combo_task (id, merchant_id, merchant_sn, 
      status, sub_mch_id, create_at, 
      update_at, form_body, result, 
      extra)
    values (#{id,jdbcType=BIGINT}, #{merchant_id,jdbcType=VARCHAR}, #{merchant_sn,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{sub_mch_id,jdbcType=VARCHAR}, #{create_at,jdbcType=TIMESTAMP}, 
      #{update_at,jdbcType=TIMESTAMP}, #{form_body,jdbcType=LONGVARCHAR}, #{result,jdbcType=LONGVARCHAR}, 
      #{extra,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.AuthAndComboTask" >
    insert into auth_and_combo_task
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="merchant_id != null" >
        merchant_id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="sub_mch_id != null" >
        sub_mch_id,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="form_body != null" >
        form_body,
      </if>
      <if test="result != null" >
        result,
      </if>
      <if test="extra != null" >
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_id != null" >
        #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="sub_mch_id != null" >
        #{sub_mch_id,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="form_body != null" >
        #{form_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null" >
        #{result,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.AuthAndComboTask" >
    update auth_and_combo_task
    <set >
      <if test="merchant_id != null" >
        merchant_id = #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="sub_mch_id != null" >
        sub_mch_id = #{sub_mch_id,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="form_body != null" >
        form_body = #{form_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.AuthAndComboTask" >
    update auth_and_combo_task
    set merchant_id = #{merchant_id,jdbcType=VARCHAR},
      merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      sub_mch_id = #{sub_mch_id,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      form_body = #{form_body,jdbcType=LONGVARCHAR},
      result = #{result,jdbcType=LONGVARCHAR},
      extra = #{extra,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.AuthAndComboTask" >
    update auth_and_combo_task
    set merchant_id = #{merchant_id,jdbcType=VARCHAR},
      merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      sub_mch_id = #{sub_mch_id,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>