<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.ContractSubTaskMapper">
    <select id="selectByPrimaryKey" resultType="com.wosai.upay.job.model.ContractSubTask"
            parameterType="java.lang.Long">
        select *
        from contract_sub_task
        where id = #{id,jdbcType=BIGINT}
    </select>


    <insert id="insert" parameterType="com.wosai.upay.job.model.ContractSubTask" useGeneratedKeys="true"
            keyProperty="id">
        insert into contract_sub_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="p_task_id != null">
                p_task_id,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="default_channel != null">
                default_channel,
            </if>
            <if test="change_config != null">
                change_config,
            </if>
            <if test="change_body != null">
                change_body,
            </if>
            <if test="task_type != null">
                task_type,
            </if>
            <if test="contract_id != null">
                contract_id,
            </if>
            <if test="payway != null">
                payway,
            </if>
            <if test="schedule_status != null">
                schedule_status,
            </if>
            <if test="schedule_dep_task_id != null">
                schedule_dep_task_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="status_influ_p_task != null">
                status_influ_p_task,
            </if>
            <if test="priority != null">
                priority,
            </if>
            <if test="create_at != null">
                create_at,
            </if>
            <if test="update_at != null">
                update_at,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="request_body != null">
                request_body,
            </if>
            <if test="response_body != null">
                response_body,
            </if>
            <if test="result != null">
                result,
            </if>
            <if test="contract_rule != null">
                contract_rule,
            </if>
            <if test="rule_group_id != null">
                rule_group_id,
            </if>
            <if test="retry != null">
                retry,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="p_task_id != null">
                #{p_task_id,jdbcType=BIGINT},
            </if>
            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                #{channel,jdbcType=VARCHAR},
            </if>
            <if test="default_channel != null">
                #{default_channel,jdbcType=INTEGER},
            </if>
            <if test="change_config != null">
                #{change_config,jdbcType=INTEGER},
            </if>
            <if test="change_body != null">
                #{change_body,jdbcType=INTEGER},
            </if>
            <if test="task_type != null">
                #{task_type,jdbcType=INTEGER},
            </if>
            <if test="contract_id != null">
                #{contract_id,jdbcType=VARCHAR},
            </if>
            <if test="payway != null">
                #{payway,jdbcType=INTEGER},
            </if>
            <if test="schedule_status != null">
                #{schedule_status,jdbcType=INTEGER},
            </if>
            <if test="schedule_dep_task_id != null">
                #{schedule_dep_task_id,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="status_influ_p_task != null">
                #{status_influ_p_task,jdbcType=INTEGER},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=TIMESTAMP},
            </if>
            <if test="create_at != null">
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="request_body != null">
                #{request_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="response_body != null">
                #{response_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                #{result,jdbcType=LONGVARCHAR},
            </if>
            <if test="contract_rule != null">
                #{contract_rule,jdbcType=VARCHAR},
            </if>
            <if test="rule_group_id != null">
                #{rule_group_id,jdbcType=VARCHAR},
            </if>
            <if test="retry != null">
                #{retry,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.ContractSubTask">
        update contract_sub_task
        <set>
            <if test="p_task_id != null">
                p_task_id = #{p_task_id,jdbcType=BIGINT},
            </if>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                channel = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="default_channel != null">
                default_channel = #{default_channel,jdbcType=INTEGER},
            </if>
            <if test="change_config != null">
                change_config = #{change_config,jdbcType=INTEGER},
            </if>
            <if test="change_body != null">
                change_body = #{change_body,jdbcType=INTEGER},
            </if>
            <if test="task_type != null">
                task_type = #{task_type,jdbcType=INTEGER},
            </if>
            <if test="contract_id != null">
                contract_id = #{contract_id,jdbcType=VARCHAR},
            </if>
            <if test="payway != null">
                payway = #{payway,jdbcType=INTEGER},
            </if>
            <if test="schedule_status != null">
                schedule_status = #{schedule_status,jdbcType=INTEGER},
            </if>
            <if test="schedule_dep_task_id != null">
                schedule_dep_task_id = #{schedule_dep_task_id,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="status_influ_p_task != null">
                status_influ_p_task = #{status_influ_p_task,jdbcType=INTEGER},
            </if>
            <!--<if test="priority != null">-->
            <!--priority = #{priority,jdbcType=TIMESTAMP},-->
            <!--</if>-->
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <!--<if test="update_at != null">-->
            <!--update_at = #{update_at,jdbcType=TIMESTAMP},-->
            <!--</if>-->
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="request_body != null">
                request_body = #{request_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="response_body != null">
                response_body = #{response_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                result = #{result,jdbcType=LONGVARCHAR},
            </if>
            <if test="retry != null">
                retry = #{retry,jdbcType=INTEGER},
            </if>
            <if test="priority != null">
                priority = #{priority,jdbcType=TIMESTAMP}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="setSubTaskResult">
        update contract_sub_task
        set status       = #{result_status},
            response_body= #{response_body},
            result       = #{result}
        where id = #{id}
          and status = #{pre_status}
    </update>

    <update id="setSubTaskStatus">
        update contract_sub_task
        set status = #{result_status}
        where id = #{id}
          and status = #{pre_status}
    </update>


    <update id="setEnableScheduleByDepId">
        update contract_sub_task
        set schedule_status = 1
        where schedule_dep_task_id = #{schedule_dep_task_id}
    </update>

    <update id="restartHxSubTask">
        update contract_sub_task
        set status = 1, priority = now()
        where p_task_id = #{p_task_id} and status = 6
    </update>


    <select id="selectByMerchatSn" resultType="com.wosai.upay.job.model.ContractSubTask"
            parameterType="java.lang.String">
        select *
        from contract_sub_task
        where status = 1
          and schedule_status = 1
          and status_influ_p_task = 1
          and merchant_sn = #{merchant_sn,jdbcType=VARCHAR}
    </select>

    <select id="selectByMerchantSnAndParentIdAndStatusAndScheduleStatus"
            resultType="com.wosai.upay.job.model.ContractSubTask">
        select * from contract_sub_task
        <where>
            <if test="merchant_sn!=null and merchant_sn!=''">
                merchant_sn=#{merchant_sn}
            </if>
            <if test="id!=null and id!=''">
                AND p_task_id = #{id,jdbcType=BIGINT}
            </if>
            <if test="status!=null">
                AND status=#{status,jdbcType=INTEGER}
            </if>
            <if test="schedule_status!=null and schedule_status!=-1">
                AND schedule_status=#{schedule_status,jdbcType=INTEGER}
            </if>
        </where>
    </select>
    <select id="getSubtasksByPtaskIdAndChannel" resultType="com.wosai.upay.job.model.ContractSubTask">
        select * from contract_sub_task
        <where>
            <if test="p_task_id!=null">
                p_task_id=#{p_task_id}
            </if>
            <if test="channel!=null and channel!=''">
                AND channel =#{channel}
            </if>
        </where>
    </select>
    <select id="getSubTasksByMerchantSnAndChannel" resultType="com.wosai.upay.job.model.ContractSubTask">
        select * from contract_sub_task
        <where>
            <if test="merchant_sn!=null">
                merchant_sn=#{merchant_sn}
            </if>
            <if test="channel!=null and channel!=''">
                AND channel =#{channel}
            </if>
        </where>
    </select>
    <select id="getSubtasksByMerchantAndPayway" resultType="com.wosai.upay.job.model.ContractSubTask">
        select * from contract_sub_task
        <where>
            <if test="merchant_sn!=null and merchant_sn!=''">
                merchant_sn=#{merchant_sn}
            </if>
            <if test="p_task_id!=null and p_task_id!=''">
                AND p_task_id=#{p_task_id}
            </if>
            AND payway in (2,3,17,18,0)
        </where>
    </select>

    <select id="selectByContractId" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where contract_id = #{param1} order by create_at desc limit 1
    </select>

    <select id="selectByContractIdAndContractRule" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where contract_id = #{contract_id}
          and contract_rule = #{contract_rule}
    </select>


    <select id="selectByPTaskIdAndStatus" resultType="com.wosai.upay.job.model.ContractSubTask">
        select * from contract_sub_task
        <where>
            <if test="p_task_id!=null and p_task_id!=''">
                p_task_id=#{p_task_id}
            </if>
            <if test="status!=null and status!=''">
                AND status=#{status}
            </if>
        </where>
    </select>

    <select id="selectByPTaskId" resultType="com.wosai.upay.job.model.ContractSubTask">
        select * from contract_sub_task where p_task_id = #{p_task_id}
    </select>


    <select id="selectByDependTaskId" resultType="com.wosai.upay.job.model.ContractSubTask"
            parameterType="java.lang.Long">
        select *
        from contract_sub_task
        where schedule_dep_task_id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByATDependTaskId" resultType="com.wosai.upay.job.model.ContractSubTask"
            parameterType="java.lang.Long">
        select *
        from contract_sub_task
        where schedule_dep_task_id = #{id,jdbcType=BIGINT}
        and payway in (2,3);
    </select>

    <select id="selectShopAndTermSubTaskByDependTaskId" resultType="com.wosai.upay.job.model.ContractSubTask"
            parameterType="java.lang.Long">
        select *
        from contract_sub_task
        where schedule_dep_task_id = #{id,jdbcType=BIGINT}
          and type in ('10', '11');
    </select>
    <update id="updateShopTermSubTask">
        update contract_sub_task
        set schedule_status      = 1,
            schedule_dep_task_id = #{scheduleDependId},
            where id = #{id}
    </update>

    <select id="selectByDepenTaskIdAndScheduleStatus" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where schedule_dep_task_id = #{param1}
          and schedule_status = #{param2}
    </select>

    <select id="selectByChannel" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel = #{param2}
          and status = 1
          and contract_id is not null
    </select>

    <select id="selectLKLTaskByPTaskId" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel = 'lkl'
          and p_task_id = #{param1}
    </select>

    <select id="selectLKLV3TaskByPTaskId" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel = 'lklV3'
          and p_task_id = #{param1}
    </select>

    <select id="selectLKLContractQueryTask" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel = 'lkl'
          and status = 1
          and contract_id <![CDATA[<>]]> ''
          and create_at > #{create,jdbcType=TIMESTAMP} LIMIT ${limit}
    </select>
    <select id="selectLKLV3ContractQueryTask" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel = 'lklV3'
          and status in (10, 11)
          and contract_id is not null
          and priority > #{start,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> #{end,jdbcType=TIMESTAMP} LIMIT ${limit}
    </select>
    <select id="selectGuotongContractQueryTask" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel = 'guotong'
          and status = 10
          and contract_id is not null
          and priority > #{start,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> #{end,jdbcType=TIMESTAMP} LIMIT ${limit}
    </select>

    <select id="selectByMerchantSnAndPTaskIdAndScheduleStatus" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where merchant_sn = #{param1}
          and p_task_id = #{param2}
          and schedule_status = #{param3}
          and status in (0, 1)
          and status_influ_p_task = 1
    </select>
    <select id="selectByUpload" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where status = 3
          and create_at > #{param1,jdbcType=TIMESTAMP}
          and p_task_id is not null
--         and rule_group_id = 'default'
        order by create_at desc
            LIMIT ${param2}
    </select>

    <select id="selectByTemp" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where status in (0, 1)
          and status_influ_p_task = 0
          and schedule_status = 1
          and p_task_id = #{param1}
          and task_type!=99
    </select>
    <select id="queryUnionOpenTasks" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where status = 5
          and channel = 'union_open'
          and contract_id = ''
          and create_at > #{param1,jdbcType=TIMESTAMP} LIMIT ${param2}
    </select>
    <select id="getWeixinSub" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where p_task_id = #{taskId}
          and payway = 3
          and status = 5 limit 1
    </select>
    <select id="getNearWeixinSub" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where merchant_sn = #{merchantSn}
          and payway = 3
          and status = 5
        order by create_at desc limit 1
    </select>

    <select id="findTasksByMerchantAndPayway" resultType="com.wosai.upay.job.model.ContractSubTask">
        select * from contract_sub_task
        <where>
            merchant_sn = #{req.merchantSn} and status = 5
            <if test="req.payway != null">
                and payway = #{req.payway}
            </if>
            <if test="req.taskType != null">
                and task_type = #{req.taskType}
            </if>
        </where>
    </select>

    <select id="findTaskByRecord" resultType="com.wosai.upay.job.model.ContractSubTask">
        select * from contract_sub_task
        <where>
            merchant_sn = #{req.merchantSn}
            <if test="req.payway != null">
                and payway = #{}
            </if>
            <if test="req.taskType != null">
                and task_type = #{req.taskType}
            </if>
            <if test="req.status != null">
                and status = #{req.status}
            </if>
            <if test="req.channel != null">
                and channel = #{req.channel}
            </if>
            <if test="req.contractRule != null">
                and contract_rule = #{req.contractRule}
            </if>
            <if test="req.ruleGroupId != null">
                and rule_group_id = #{req.ruleGroupId}
            </if>

        </where>
    </select>

    <select id="selectUmsContractQueryTask" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel = 'ums'
          and status = 1
          and contract_id <![CDATA[<>]]> ''
          and priority > #{create,jdbcType=TIMESTAMP} LIMIT ${limit}
    </select>

    <select id="selectPsbcContractQueryTask" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel in ('psbc', 'cgb','hxb','cmbc')
          and status = 1
          and contract_id <![CDATA[<>]]> ''
          and priority > #{start,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> #{end,jdbcType=TIMESTAMP} LIMIT ${limit}
    </select>

    <select id="selectPabContractQueryTask" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel ='pab'
          and status = 1
          and contract_id <![CDATA[<>]]> ''
          and priority > #{start,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> #{end,jdbcType=TIMESTAMP} LIMIT ${limit}
    </select>

    <select id="selectLzbContractQueryTask" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel ='lzb'
          and status = 1
          and contract_id <![CDATA[<>]]> ''
          and priority > #{start,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> #{end,jdbcType=TIMESTAMP} LIMIT ${limit}
    </select>

    <select id="selectCcbContractQueryTask" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel = 'ccb'
          and status = 1
          and contract_id <![CDATA[<>]]> ''
          and priority > #{start,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> #{end,jdbcType=TIMESTAMP} LIMIT ${limit}
    </select>

    <select id="selectFuyouUpdateQueryTask" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel = 'fuyou'
          and status in (1,10)
          and task_type in (2,4)
          and priority > #{start,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> #{end,jdbcType=TIMESTAMP}
          and contract_id is not null
    </select>

    <select id="selectNetInSubTasksByMerchantSnAndRuleGroup" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where merchant_sn = #{param1}
          and rule_group_id = #{param2}
          and task_type = 5
          and status = 5
    </select>
    <select id="selectNetInNoInfluenceSubTasksByPTaskId" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where p_task_id = #{p_task_id}
          and task_type = 5
          and status = 5
          and status_influ_p_task = 0
    </select>
    <select id="selectUnionMerchantContractResultQueryTask"
            resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel = 'lklV3'
            and task_type = 14
            and status in (0,1)
            and contract_id is null
            and priority > #{start,jdbcType=TIMESTAMP}
            and priority <![CDATA[<]]> #{end,jdbcType=TIMESTAMP} LIMIT ${limit}
    </select>

    <delete id="deleteByPtaskId" parameterType="java.lang.Long" >
        delete from contract_sub_task
        where p_task_id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectFuYouContractQueryTask" resultType="com.wosai.upay.job.model.ContractSubTask">
        select *
        from contract_sub_task
        where channel = 'fuyou'
          and status = 1
          and contract_id is not null
          and priority > #{start,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> #{end,jdbcType=TIMESTAMP} LIMIT ${limit}
    </select>

</mapper>