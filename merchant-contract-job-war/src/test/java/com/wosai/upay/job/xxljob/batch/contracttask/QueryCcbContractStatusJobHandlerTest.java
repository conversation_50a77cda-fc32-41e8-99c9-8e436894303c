package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryCcbContractStatusJobHandlerTest {

    @InjectMocks
    private QueryCcbContractStatusJobHandler queryCcbContractStatusJobHandler;

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private QueryContractStatusHandler queryContractStatusHandler;

    @Mock
    private ChatBotUtil chatBotUtil;

    private ContractSubTask contractSubTask;
    private BatchJobParam batchJobParam;
    private ContractTask contractTask;

    @Before
    public void setUp() {
        contractSubTask = new ContractSubTask();
        batchJobParam = new BatchJobParam();
        batchJobParam.setBatchSize(10);
        batchJobParam.setQueryTime(3600000L); // 1小时

        contractSubTask.setId(1L);
        contractSubTask.setP_task_id(2L);

        contractTask = new ContractTask();
        contractTask.setId(2L);
    }

    @Test
    public void getLockKey_ValidId_ReturnsCorrectLockKey() {
        contractSubTask.setId(123L);
        String expectedLockKey = "QueryCcbContractStatusJobHandler:123";
        String actualLockKey = queryCcbContractStatusJobHandler.getLockKey(contractSubTask);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void getLockKey_NullId_ReturnsLockKeyWithNull() {
        contractSubTask.setId(null);
        String expectedLockKey = "QueryCcbContractStatusJobHandler:null";
        String actualLockKey = queryCcbContractStatusJobHandler.getLockKey(contractSubTask);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsContractSubTaskList() {
        // 准备
        ContractSubTask task1 = new ContractSubTask();
        task1.setId(1L);
        task1.setMerchant_sn("merchant1");
        ContractSubTask task2 = new ContractSubTask();
        task2.setId(2L);
        task2.setMerchant_sn("merchant2");
        List<ContractSubTask> expectedTasks = Arrays.asList(task1, task2);

        when(contractSubTaskMapper.selectCcbContractQueryTask(
                Mockito.anyInt(),
                Mockito.anyString(),
                Mockito.anyString()
        )).thenReturn(expectedTasks);

        // 执行
        List<ContractSubTask> actualTasks = queryCcbContractStatusJobHandler.queryTaskItems(batchJobParam);

        // 验证
        assertEquals(expectedTasks, actualTasks);
    }

    @Test
    public void doHandleSingleData_SuccessStatus_Returns() {
        contractSubTask.setStatus(TaskStatus.SUCCESS.getVal());
        when(contractSubTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractSubTask);

        queryCcbContractStatusJobHandler.doHandleSingleData(contractSubTask);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(contractTaskMapper, never()).selectByPrimaryKey(anyLong());
        verify(queryContractStatusHandler, never()).doHandle(any(ContractTask.class), any(ContractSubTask.class));
    }

    @Test
    public void doHandleSingleData_FailStatus_Returns() {
        contractSubTask.setStatus(TaskStatus.FAIL.getVal());
        when(contractSubTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractSubTask);

        queryCcbContractStatusJobHandler.doHandleSingleData(contractSubTask);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(contractTaskMapper, never()).selectByPrimaryKey(anyLong());
        verify(queryContractStatusHandler, never()).doHandle(any(ContractTask.class), any(ContractSubTask.class));
    }

    @Test
    public void doHandleSingleData_ProcessingStatus_QueriesAndHandles() {
        contractSubTask.setStatus(TaskStatus.PROGRESSING.getVal());
        when(contractSubTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractSubTask);
        when(contractTaskMapper.selectByPrimaryKey(2L)).thenReturn(contractTask);

        queryCcbContractStatusJobHandler.doHandleSingleData(contractSubTask);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(contractTaskMapper, times(1)).selectByPrimaryKey(2L);
        verify(queryContractStatusHandler, times(1)).doHandle(contractTask, contractSubTask);
    }

    @Test
    public void doHandleSingleData_ExceptionOccurs_SendsWarningMessage() {
        contractSubTask.setStatus(TaskStatus.PROGRESSING.getVal());
        when(contractSubTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractSubTask);
        when(contractTaskMapper.selectByPrimaryKey(2L)).thenReturn(contractTask);
        doThrow(new RuntimeException("Test Exception")).when(queryContractStatusHandler).doHandle(any(ContractTask.class), any(ContractSubTask.class));

        queryCcbContractStatusJobHandler.doHandleSingleData(contractSubTask);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(contractTaskMapper, times(1)).selectByPrimaryKey(2L);
        verify(queryContractStatusHandler, times(1)).doHandle(contractTask, contractSubTask);
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(any());
    }
}
