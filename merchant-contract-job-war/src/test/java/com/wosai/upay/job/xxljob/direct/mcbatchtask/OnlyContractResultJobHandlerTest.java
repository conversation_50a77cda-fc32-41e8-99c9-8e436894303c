package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.google.common.cache.Cache;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.McBatchTaskMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.model.changeAcquirerApprove.AcquirerApprove;
import com.wosai.upay.job.model.changeAcquirerApprove.AcquirerApproveExcel;
import com.wosai.upay.job.service.ContractTaskResultServiceImpl;
import com.wosai.upay.job.util.ExcelUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.ExecutionException;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OnlyContractResultJobHandlerTest {

    @InjectMocks
    private OnlyContractResultJobHandler onlyContractResultJobHandler;

    @Mock
    private ContractEventMapper contractEventMapper;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private ContractTaskResultServiceImpl contractTaskResultService;

    @Mock
    private McBatchTaskMapper mcBatchTaskMapper;

    @Mock
    private ErrorCodeManageBiz errorCodeManageBiz;

    @Mock
    private CallBackService callBackService;

    @Mock
    private Cache<String, List<AcquirerApprove>> onlyContractCache;
    @Mock
    private ExcelUtil excelUtil;

    private AcquirerApprove acquirerApprove;

    @Before
    public void setUp() {
        // 如果需要，可以在这里进行任何设置
        acquirerApprove = new AcquirerApprove();
        acquirerApprove.setApplyId("123");
        acquirerApprove.setMerchantSn("merchant123");
        acquirerApprove.setRemark("reason");
        Mockito.doReturn(new ErrorInfo()).when(errorCodeManageBiz).getPromptMessageFromErrorCodeManager(anyString(), anyString(), anyString());
    }

    @Test
    public void getLockKey_ReturnsCorrectLockKey() {
        // 调用被测试的方法
        String lockKey = onlyContractResultJobHandler.getLockKey();

        // 验证返回的锁键是否正确
        assertEquals("OnlyContractResultJobHandler", lockKey);
    }

    @Test
    public void getContractEventResult_AlreadyProcessed_ReturnsDirectly() {
        acquirerApprove.setResult("Processed");
        AcquirerApproveExcel result = onlyContractResultJobHandler.getContractEventResult(acquirerApprove);
        assertEquals("Processed", result.getResult());
    }

    @Test
    public void getContractEventResult_ValidationFailed_ReturnsFailure() {
        acquirerApprove.setApplyId(null);
        AcquirerApproveExcel result = onlyContractResultJobHandler.getContractEventResult(acquirerApprove);
        assertEquals("商户仅入网失败", result.getResult());
    }

    @Test
    public void getContractEventResult_ValidationFailedWithResult_ReturnsDirectly() {
        acquirerApprove.setApplyId(null);
        acquirerApprove.setResult("AlreadyFailed");
        AcquirerApproveExcel result = onlyContractResultJobHandler.getContractEventResult(acquirerApprove);
        assertEquals("AlreadyFailed", result.getResult());
    }

    @Test
    public void getContractEventResult_SuccessStatus_ReturnsSuccess() {
        ContractEvent contractEvent = new ContractEvent();
        contractEvent.setTask_id(1L);
        when(contractEventMapper.selectByPrimaryKey(123L)).thenReturn(contractEvent);

        ContractTask contractTask = new ContractTask();
        contractTask.setStatus(TaskStatus.SUCCESS.getVal());
        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractTask);

        AcquirerApproveExcel result = onlyContractResultJobHandler.getContractEventResult(acquirerApprove);
        assertEquals("入网成功", result.getResult());
    }

    @Test
    public void getContractEventResult_FailStatus_ReturnsEscapedContent() {
        ContractEvent contractEvent = new ContractEvent();
        contractEvent.setTask_id(1L);
        when(contractEventMapper.selectByPrimaryKey(123L)).thenReturn(contractEvent);

        ContractTask contractTask = new ContractTask();
        contractTask.setStatus(TaskStatus.FAIL.getVal());
        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractTask);

        when(contractTaskResultService.getEscapedContent(contractTask)).thenReturn("EscapedContent");

        AcquirerApproveExcel result = onlyContractResultJobHandler.getContractEventResult(acquirerApprove);
        assertEquals("EscapedContent", result.getResult());
    }

    @Test
    public void getContractEventResult_InProgressStatus_ReturnsDirectly() {
        ContractEvent contractEvent = new ContractEvent();
        contractEvent.setTask_id(1L);
        when(contractEventMapper.selectByPrimaryKey(123L)).thenReturn(contractEvent);

        ContractTask contractTask = new ContractTask();
        contractTask.setStatus(TaskStatus.PROGRESSING.getVal());
        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractTask);

        AcquirerApproveExcel result = onlyContractResultJobHandler.getContractEventResult(acquirerApprove);
        assertEquals(null, result.getResult());
    }

    @Test
    public void execute_BatchType13_AllProcessed_UploadsResult() throws ExecutionException {
        DirectJobParam param = new DirectJobParam();
        McBatchTask mcBatchTask = new McBatchTask();
        mcBatchTask.setId(1);
        mcBatchTask.setPayload("{\"batch_size\":7,\"approveInfo\":{\"applyType\":\"batch\",\"auditSn\":\"SP365220250212000033\",\"immediate\":false,\"callBackBean\":{\"auditId\":544886,\"message\":\"\",\"templateId\":357305,\"resultType\":1}},\"lastAttachmentUrl\":\"xxx\"}");
        mcBatchTask.setType(13);
        mcBatchTask.setStatus(1);
        mcBatchTask.setEffect_time(new Date());

        List<McBatchTask> mcBatchTasks = new ArrayList<>();
        mcBatchTasks.add(mcBatchTask);
        AcquirerApprove acquirerApprove = new AcquirerApprove();
        acquirerApprove.setResult("处理成功");
        when(mcBatchTaskMapper.selectByExampleWithBLOBs(any(McBatchTaskExample.class))).thenReturn(mcBatchTasks);
        when(onlyContractCache.get(anyString(), any())).thenReturn(Collections.singletonList(acquirerApprove));

        onlyContractResultJobHandler.execute(param);

        verify(mcBatchTaskMapper).updateByPrimaryKeySelective(any(McBatchTask.class));
    }

    @Test
    public void execute_SingleType12_SuccessfulTask_CallsBackWithSuccess() {
        DirectJobParam param = new DirectJobParam();
        McBatchTask mcBatchTask = new McBatchTask();
        mcBatchTask.setId(1);
        mcBatchTask.setPayload("{\"approveInfo\":{\"auditSn\":\"SP365220250415000005\",\"callBackBean\":{\"auditId\":585431,\"resultType\":1,\"templateId\":365009}},\"eventId\":1}");
        mcBatchTask.setType(12);
        mcBatchTask.setStatus(1);
        mcBatchTask.setEffect_time(new Date());

        List<McBatchTask> mcBatchTasks = new ArrayList<>();
        mcBatchTasks.add(mcBatchTask);

        ContractEvent contractEvent = new ContractEvent();
        contractEvent.setTask_id(1L);

        ContractTask contractTask = new ContractTask();
        contractTask.setStatus(TaskStatus.SUCCESS.getVal());

        when(mcBatchTaskMapper.selectByExampleWithBLOBs(any(McBatchTaskExample.class))).thenReturn(mcBatchTasks);
        when(contractEventMapper.selectByPrimaryKey(1L)).thenReturn(contractEvent);
        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractTask);

        onlyContractResultJobHandler.execute(param);

        verify(callBackService).addComment(any(CallBackBean.class));
    }

    @Test
    public void execute_ExceptionOccurs_UpdatesTaskStatusToFailed() {
        DirectJobParam param = new DirectJobParam();
        McBatchTask mcBatchTask = new McBatchTask();
        mcBatchTask.setId(1);
        mcBatchTask.setPayload("{\"approveInfo\":{\"auditSn\":\"SP365220250415000005\",\"callBackBean\":{\"auditId\":585431,\"resultType\":1,\"templateId\":365009}},\"eventId\":1}");
        mcBatchTask.setType(12);
        mcBatchTask.setStatus(1);
        mcBatchTask.setEffect_time(new Date());

        List<McBatchTask> mcBatchTasks = new ArrayList<>();
        mcBatchTasks.add(mcBatchTask);

        when(mcBatchTaskMapper.selectByExampleWithBLOBs(any(McBatchTaskExample.class))).thenReturn(mcBatchTasks);
        doThrow(new RuntimeException("Test Exception")).when(contractEventMapper).selectByPrimaryKey(anyLong());

        onlyContractResultJobHandler.execute(param);

        verify(mcBatchTaskMapper).updateByPrimaryKeySelective(any(McBatchTask.class));
    }
}
