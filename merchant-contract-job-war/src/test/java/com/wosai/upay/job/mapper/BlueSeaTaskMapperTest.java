package com.wosai.upay.job.mapper;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.util.StringUtil;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

public class BlueSeaTaskMapperTest extends BaseTest {

    @Autowired
    private BlueSeaTaskMapper mapper;


    @Test
    public void insertSelective() {
        BlueSeaTask task = new BlueSeaTask();
        task.setAudit_id(123L);
        task.setAli_mch_id("aliMchId");
        task.setMerchant_sn("merchantSn");
        task.setMerchant_id("merchantId");
        task.setStatus(BlueSeaConstant.PENDING);
        task.setForm_body("{}");
        task.setMch_snapshot("{}");
        mapper.insertSelective(task);


        long current = System.currentTimeMillis();
        String startTime = StringUtil.formatDate(current - 3 * 3600 * 1000L);
        String endTime = StringUtil.formatDate(current);
        List<BlueSeaTask> tasks = mapper.selectByStatus(Arrays.asList(BlueSeaConstant.PENDING, BlueSeaConstant.M3), Arrays.asList(BlueSeaConstant.blueSeaType, BlueSeaConstant.kx), 10, startTime, endTime);
        Assert.assertEquals(1, tasks.size());
        tasks = mapper.selectByStatus(Arrays.asList(BlueSeaConstant.M4, BlueSeaConstant.M3),Arrays.asList(BlueSeaConstant.blueSeaType, BlueSeaConstant.kx), 10, startTime, endTime);
        Assert.assertEquals(0, tasks.size());
    }
}