package com.wosai.upay.job.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.data.util.CollectionUtil;
import com.wosai.operation.activity.service.WechantOasisService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.ChangeTradeParamsBiz;
import com.wosai.upay.job.biz.MerchantConfigParamsBiz;
import com.wosai.upay.job.dao.WeixinParamsUpdateApplyDao;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2020/7/21
 */
public class ContractWeixinServiceImplTest extends BaseTest {

    @InjectMocks
    private ContractWeixinServiceImpl contractWeixinService;

    @Mock
    private TradeConfigService tradeConfigService;

    @Mock
    private MerchantService merchantService;

    @Mock
    private ConfigSupportService configSupportService;

    @Mock
    private MerchantConfigParamsBiz merchantConfigParamsBiz;

    @Mock
    private SupportService supportService;

    @Mock
    private WechantOasisService wechantOasisService;

    @Mock
    private MerchantProviderParamsService merchantProviderParamsService;

    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Mock
    private ChangeTradeParamsBiz tradeParamsBiz;

    @Mock
    private ProviderTradeParamsService merchantProviderTradeParamsService;

    @Mock
    private WeixinParamsUpdateApplyDao weixinParamsUpdateApplyDao;

    @Mock
    private AcquirerService acquirerService;

    @Mock(name = "merchantContractJdbcTemplate")
    private JdbcTemplate merchantContractJdbcTemplate;

    public ContractWeixinServiceImplTest() {
    }

    /**
     * conditions太多了，只覆盖了主要分支
     */
    @Test
    public void contractWechantAddDevConfig() {

    }

    @Test
    public void addSubDevConfigAndUpdateAgentHandle() throws Exception {
    }

    @Test
    public void updateAgentWeixinCustom() {

    }

    @Test
    public void getMerchantChannelMessage() {
        Mockito.doReturn(CollectionUtil.hashMap("sn", "sn")).when(merchantService).getMerchant("merchant_id");
        Mockito.doReturn(CollectionUtil.hashMap("up_trade_params", CollectionUtil.hashMap("channel_id", "channel_id", "weixin_mch_id", "weixin_mch_id", "weixin_sub_mch_id", "weixin_sub_mch_id")))
                .when(tradeConfigService).getTradeParams(anyInt(), anyInt(), any());
        MerchantProviderParamsDto dto = new MerchantProviderParamsDto().setChannel_no("channel_no").setWeixin_sub_appid("weixin_sub_id");
        Mockito.doReturn(Arrays.asList(dto)).when(merchantProviderParamsService).getMerchantProviderParams(any());
        Map resp = CollectionUtil.hashMap("channel_name", "channel_name");
        Mockito.doReturn(resp).when(merchantContractJdbcTemplate).queryForMap("SELECT * FROM channel_appid_map WHERE channel_no = ? AND appid = ? AND type = 0 LIMIT 1", "channel_no", "weixin_sub_id");
        Map request = CollectionUtil.hashMap("merchant_id", "merchant_id");
        //成功
        Map result = contractWeixinService.getMerchantChannelMessage(request);
        assertEquals(2, result.get("result_code"));
        //空
        resp.remove("channel_name");
        result = contractWeixinService.getMerchantChannelMessage(request);
        assertEquals(1, result.get("result_code"));
        //contractMessage为空
        Mockito.doReturn(Maps.newHashMap()).when(tradeConfigService).getTradeParams(anyInt(), anyInt(), any());
        result = contractWeixinService.getMerchantChannelMessage(request);
        assertEquals(0, result.get("result_code"));
    }

    @Test
    public void rollbackMerchantWeixinConfig() {
        Mockito.doReturn(CollectionUtil.hashMap("up_direct_trade_params", CollectionUtil.hashMap("rece_org_no", "rece_org_no", "weixin_mch_id", "weixin_mch_id", "weixin_sub_mch_id", "weixin_sub_mch_id")))
                .when(tradeConfigService).getTradeParams(anyInt(), anyInt(), any());
        Mockito.doReturn(CollectionUtil.hashMap("sn", "sn")).when(merchantService).getMerchant("merchant_id");
        Map resp = CollectionUtil.hashMap("status", "3", "id", "id", "old_pay_appid", "old_pay_appid", "channel_no", "channel_no");
        Mockito.doReturn(resp).when(merchantContractJdbcTemplate).queryForMap(anyString(), anyString(), anyInt());
        Map beforeAgentName = CollectionUtil.hashMap("wap_agent_name", "wap_agent_name", "params", "params");
        Mockito.doReturn(beforeAgentName).when(tradeConfigService).getMerchantConfigByMerchantIdAndPayway("merchant_id", 3);
        List<MerchantProviderParams> list = Lists.newArrayList();
        MerchantProviderParams params = new MerchantProviderParams();
        list.add(params);
        Mockito.doReturn(list).when(merchantProviderParamsMapper).selectByExample(any());
        Mockito.doReturn(true).when(tradeParamsBiz).changeTradeParams(params, "", false,any());
        Map request = CollectionUtil.hashMap("merchant_id", "merchant_id");
        //成功
        Map result = contractWeixinService.rollbackMerchantWeixinConfig(request);
        assertEquals(1, result.get("result_code"));
        //异常
        Mockito.doThrow(Exception.class).when(tradeParamsBiz).changeTradeParams(params, "", false,any());
        contractWeixinService.rollbackMerchantWeixinConfig(request);
        //false
        Mockito.doReturn(false).when(tradeParamsBiz).changeTradeParams(params, "", false,any());
        contractWeixinService.rollbackMerchantWeixinConfig(request);
        //报备结果为空
        list.remove(params);
        contractWeixinService.rollbackMerchantWeixinConfig(request);
        //配置失败
        beforeAgentName.remove("wap_agent_name");
        contractWeixinService.rollbackMerchantWeixinConfig(request);
        //status!=3
        resp.put("status", 1);
        contractWeixinService.rollbackMerchantWeixinConfig(request);
        //商户不存在
        Mockito.doReturn(null).when(merchantService).getMerchant("merchant_id");
        contractWeixinService.rollbackMerchantWeixinConfig(request);
    }

    @Test
    public void getCustomAppidApplyByParams() {
        Mockito.doReturn(CollectionUtil.hashMap("up_tl_trade_params", CollectionUtil.hashMap("weixin_sub_appid", "weixin_sub_appid", "weixin_sub_mch_id", "weixin_sub_mch_id", "channel_no", "channel_no")))
                .when(tradeConfigService).getTradeParams(anyInt(), anyInt(), any());
        contractWeixinService.getCustomAppidApplyByParams(CollectionUtil.hashMap("merchant_id", "merchant_id"));
        Mockito.doReturn(CollectionUtil.hashMap("not_exist", "not_exist")).when(tradeConfigService).getTradeParams(anyInt(), anyInt(), any());
        Map result = contractWeixinService.getCustomAppidApplyByParams(CollectionUtil.hashMap("merchant_id", "merchant_id"));
        assertEquals(true, result.isEmpty());
    }

    @Test
    public void updateWeixinParams() {
        Map merchant = CollectionUtil.hashMap("sn", "merchant_sn", "id", "merchant_id");
        Mockito.doReturn(merchant).when(merchantService).getMerchant(any());
        Mockito.doReturn(merchant).when(merchantService).getMerchantBySn(any());
        Map params = CollectionUtil.hashMap("merchant_sn", "merchant_sn", "service_phone", "service_phone", "merchant_shortname", "merchant_shortname");
        Map weixinPayParams = CollectionUtil.hashMap("provider", 1);
        MerchantProviderParamsDto dto = new MerchantProviderParamsDto().setChannel_no("channel_no").setMerchant_sn("merchant_sn");
        Mockito.doReturn(weixinPayParams).when(merchantProviderTradeParamsService).getPayTradeParams("merchant_sn", 3);
        Mockito.doReturn(Arrays.asList(dto)).when(merchantProviderParamsService).getMerchantProviderParams(any());
        Mockito.doReturn("lkl").when(acquirerService).getMerchantAcquirer("merchant_sn");
        //lkl
        contractWeixinService.updateWeixinParams(params);
        //tonglian
        Mockito.doReturn("tl").when(acquirerService).getMerchantAcquirer("merchant_sn");
        contractWeixinService.updateWeixinParams(params);
        //获取参数失败
        Mockito.doReturn(Lists.newArrayList()).when(merchantProviderParamsService).getMerchantProviderParams(any());
        contractWeixinService.updateWeixinParams(params);
        //通道为直连通道
        weixinPayParams.put("provider", 0);
        contractWeixinService.updateWeixinParams(params);
        //修改参数为空
        params.remove("service_phone");
        params.remove("merchant_shortname");
        params.put("merchant_id", "merchant_id");
        contractWeixinService.updateWeixinParams(params);
    }

    @Test
    public void getWeixinUpdateTime() {
        Map merchant = CollectionUtil.hashMap("sn", "merchant_sn", "id", "merchant_id");
        Mockito.doReturn(merchant).when(merchantService).getMerchant(any());
        Mockito.doReturn(merchant).when(merchantService).getMerchantBySn(any());
        Map weixinPayParams = CollectionUtil.hashMap("provider", 1, "weixin_merchant_id", "weixin_merchant_id");
        Mockito.doReturn(weixinPayParams).when(merchantProviderTradeParamsService).getPayTradeParams("merchant_sn", 3);
        Map params = CollectionUtil.hashMap("merchant_sn", "merchant_sn");
        Map weixinParamsUpdateApply = CollectionUtil.hashMap("mtime", 0L);
        Mockito.doReturn(weixinParamsUpdateApply).when(weixinParamsUpdateApplyDao).getWeixinParamsUpdateApply("weixin_merchant_id");
        //pass
        contractWeixinService.getWeixinUpdateTime(params);
        //nopass
        params.put("merchant_id", "merchant_id");
        weixinParamsUpdateApply.put("mtime", System.currentTimeMillis());
        contractWeixinService.getWeixinUpdateTime(params);
        //通道暂时不允许查询
        weixinPayParams.put("provider", 0);
        contractWeixinService.getWeixinUpdateTime(params);
        //merchant不存在
        merchant.remove("sn");
        merchant.remove("id");
        contractWeixinService.getWeixinUpdateTime(params);
    }
}