package com.wosai.upay.job.biz.directparams;

import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.BaseTest;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;


public class DirectParamsBizFactoryTest extends BaseTest {


    @Test(expected = CommonPubBizException.class)
    public void getDirectParamsBiz() {
        DirectParamsBizFactory directParamsBizFactory = new DirectParamsBizFactory(null, null, null, null, null);
        List<Integer> subPayways = Arrays.asList(2, 3, 18, 19, 20);
        subPayways.forEach(sub -> {
            DirectParamsBizFactory.getDirectParamsBiz(sub);
        });
        DirectParamsBizFactory.getDirectParamsBiz(0);
    }
}
