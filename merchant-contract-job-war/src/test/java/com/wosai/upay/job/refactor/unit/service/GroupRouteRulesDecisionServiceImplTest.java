package com.wosai.upay.job.refactor.unit.service;

import com.wosai.upay.job.model.dto.response.GroupRouteRuleDetailRspDTO;
import com.wosai.upay.job.refactor.dao.GroupRouteRuleDetailDAO;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRuleDetailDO;
import com.wosai.upay.job.refactor.service.impl.GroupRouteRulesDecisionServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

/**
 * 规则决策detail测试
 *
 * <AUTHOR>
 * @date 2024/3/8 10:44
 */
class GroupRouteRulesDecisionServiceImplTest {

    @InjectMocks
    private GroupRouteRulesDecisionServiceImpl groupRouteRulesDecisionService;

    @Mock
    private GroupRouteRuleDetailDAO groupRouteRuleDetailDAO;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testGetGroupRouteRuleDetailById() {
        Long id = 1L;
        GroupRouteRuleDetailDO groupRouteRuleDetailDO = new GroupRouteRuleDetailDO();
        groupRouteRuleDetailDO.setId(id);
        groupRouteRuleDetailDO.setRuleDecisionId(id);
        when(groupRouteRuleDetailDAO.getByPrimaryKey(id)).thenReturn(Optional.of(groupRouteRuleDetailDO));
        GroupRouteRuleDetailRspDTO result = groupRouteRulesDecisionService.getGroupRouteRuleDetailById(id);
        assertEquals(id, result.getId());
    }
}
