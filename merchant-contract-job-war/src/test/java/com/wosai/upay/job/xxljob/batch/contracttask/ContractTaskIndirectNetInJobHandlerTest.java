package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.xxljob.context.ContractTaskContext;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.side.service.GeneralRuleService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ContractTaskIndirectNetInJobHandlerTest {

    @InjectMocks
    private ContractTaskIndirectNetInJobHandler handler;

    @Mock
    private GeneralRuleService generalRuleService;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @Mock
    private SubTaskHandlerContext subTaskHandlerContext;

    @Mock
    private ChatBotUtil chatBotUtil;

    @Mock
    private ApplicationApolloConfig  applicationApolloConfig;
    @Test
    public void queryTaskItems_WithTasks_ReturnsContractTaskContexts() {
        // 准备
        BatchJobParam param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        ContractTask task = new ContractTask();
        task.setId(1L);
        task.setMerchant_sn("testMerchant");
        when(generalRuleService.getFirstWeekDayAfterDate(anyString())).thenReturn("2023-10-01");
        when(contractTaskMapper.selectIndirectContractTaskTodo(anyString(), anyInt(),anyList()))
                .thenReturn(Arrays.asList(task));

        List<ContractTaskContext> result = handler.queryTaskItems(param);

        assertEquals(1, result.size());
        assertEquals("2023-10-01", result.get(0).getReviewComplete());
        assertEquals(task, result.get(0).getTask());
    }

    @Test
    public void queryTaskItems_NoTasks_ReturnsEmptyList() {
        // 准备
        BatchJobParam param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        when(generalRuleService.getFirstWeekDayAfterDate(anyString())).thenReturn("2023-10-01");
        when(contractTaskMapper.selectIndirectContractTaskTodo(anyString(), anyInt(),anyList()))
                .thenReturn(Arrays.asList());

        List<ContractTaskContext> result = handler.queryTaskItems(param);

        assertEquals(0, result.size());
    }

    @Test(expected = RuntimeException.class)
    public void queryTaskItems_WithException_ThrowsRuntimeException() {
        // 准备
        BatchJobParam param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        when(generalRuleService.getFirstWeekDayAfterDate(anyString())).thenThrow(new RuntimeException("Test Exception"));

        handler.queryTaskItems(param);
    }

    @Test
    public void doHandleSingleData_SuccessStatus_Returns() {
        ContractTask taskA = new ContractTask().setId(1L).setStatus(TaskStatus.SUCCESS.getVal()).setMerchant_sn("merchantSn");
        ContractTaskContext contextA = new ContractTaskContext(taskA, "2023-01-01");
        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contextA.getTask());

        handler.doHandleSingleData(contextA);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verifyNoInteractions(contractSubTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_FailStatus_Returns() {
        ContractTask taskA = new ContractTask().setId(1L).setStatus(TaskStatus.SUCCESS.getVal()).setMerchant_sn("merchantSn");
        ContractTaskContext contextA = new ContractTaskContext(taskA, "2023-01-01");
        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contextA.getTask());

        handler.doHandleSingleData(contextA);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verifyNoInteractions(contractSubTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_PendingStatus_SetsCompleteAt() {
        ContractTask taskA = new ContractTask().setId(1L).setStatus(TaskStatus.SUCCESS.getVal()).setMerchant_sn("merchantSn");
        ContractTaskContext contextA = new ContractTaskContext(taskA, "2023-01-01");
        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contextA.getTask());

        handler.doHandleSingleData(contextA);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verifyNoInteractions(contractSubTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_NoSubTasks_LogsInfo() {
        ContractTask taskA = new ContractTask().setId(1L).setStatus(TaskStatus.PENDING.getVal()).setMerchant_sn("merchantSn");
        ContractTaskContext contextA = new ContractTaskContext(taskA, "2023-01-01");

        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contextA.getTask());
        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus(contextA.getTask().getMerchant_sn(), 1, 1)).thenReturn(Collections.emptyList());

        handler.doHandleSingleData(contextA);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(contractSubTaskMapper, times(1)).selectByMerchantSnAndPTaskIdAndScheduleStatus(contextA.getTask().getMerchant_sn(), 1, 1);
        verifyNoInteractions(subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_SubTaskWithChannelUpdatesPriority() {
        ContractTask taskA = new ContractTask().setId(1L).setStatus(TaskStatus.PENDING.getVal()).setMerchant_sn("merchantSn");
        ContractTaskContext contextA = new ContractTaskContext(taskA, "2023-01-01");
        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contextA.getTask());
        ContractSubTask contractSubTask = new ContractSubTask().setChannel(ProviderUtil.CHANNELS.get(0)).setContract_id("123").setCreate_at(new Date());
        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus(contextA.getTask().getMerchant_sn(), 1, 1)).thenReturn(Arrays.asList(contractSubTask));

        handler.doHandleSingleData(contextA);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(contractSubTaskMapper, times(1)).selectByMerchantSnAndPTaskIdAndScheduleStatus(contextA.getTask().getMerchant_sn(), 1, 1);
        verify(contractTaskMapper, times(1)).updatePriority(any(), eq(1L));
        verifyNoInteractions(subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_ExceptionCaught_LogsErrorAndSendsWarning() {
        ContractTask taskA = new ContractTask().setId(1L).setStatus(TaskStatus.SUCCESS.getVal()).setMerchant_sn("merchantSn");
        ContractTaskContext contextA = new ContractTaskContext(taskA, "2023-01-01");
        when(contractTaskMapper.selectByPrimaryKey(1L)).thenThrow(new RuntimeException("Test exception"));

        handler.doHandleSingleData(contextA);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }

    @Test
    public void getLockKey_ValidContext_ReturnsCorrectLockKey() {
        ContractTask taskB = new ContractTask();
        taskB.setId(12345L);
        taskB.setMerchant_sn("merchantSn");
        ContractTaskContext contextB = new ContractTaskContext(taskB, "reviewComplete");
        String expectedLockKey = "ContractTaskIndirectNetInJobHandler:merchantSn";
        String actualLockKey = handler.getLockKey(contextB);
        assertEquals(expectedLockKey, actualLockKey);
    }
}
