package com.wosai.upay.job.biz.keepalive;

import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.AuthStatusEnum;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * KeepAliveParamsSelector 测试类
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
class KeepAliveParamsSelectorTest {

    @Test
    void testSelectKeepAliveParams_EmptyInput() {
        List<MerchantProviderParamsDO> result = KeepAliveParamsSelector.selectKeepAliveParams(
                Arrays.asList(), Arrays.asList(1, 2), Arrays.asList(2, 3), 2);
        assertTrue(result.isEmpty());
    }

    @Test
    void testSelectKeepAliveParams_MaxTwoProviders() {
        // 创建测试数据：3个 Provider，每个有2种 Payway
        List<MerchantProviderParamsDO> allParams = createTestParams();
        List<Integer> keepAliveProviders = Arrays.asList(1001, 1002, 1003); // 3个 Provider
        List<Integer> paywayList = Arrays.asList(2, 3); // 2种 Payway

        List<MerchantProviderParamsDO> result = KeepAliveParamsSelector.selectKeepAliveParams(
                allParams, keepAliveProviders, paywayList, 2);

        // 验证最多只选择2个 Provider 的参数
        long providerCount = result.stream()
                .mapToInt(MerchantProviderParamsDO::getProvider)
                .distinct()
                .count();
        assertTrue(providerCount <= 2);

        // 验证选择的是前2个 Provider（1001, 1002）
        assertTrue(result.stream().anyMatch(p -> p.getProvider() == 1001));
        assertTrue(result.stream().anyMatch(p -> p.getProvider() == 1002));
        assertFalse(result.stream().anyMatch(p -> p.getProvider() == 1003));
    }

    @Test
    void testSelectKeepAliveParams_PreferAuthorizedParams() {
        List<MerchantProviderParamsDO> allParams = createTestParamsWithAuth();
        List<Integer> keepAliveProviders = Arrays.asList(1001);
        List<Integer> paywayList = Arrays.asList(2);

        List<MerchantProviderParamsDO> result = KeepAliveParamsSelector.selectKeepAliveParams(
                allParams, keepAliveProviders, paywayList, 2);

        // 验证选择了已授权的参数
        assertEquals(1, result.size());
        assertEquals(AuthStatusEnum.YES.getValue(), result.get(0).getAuthStatus());
        assertEquals("auth_param", result.get(0).getId());
    }

    private List<MerchantProviderParamsDO> createTestParams() {
        return Arrays.asList(
                createParam("1", 1001, 2, 1000L, AuthStatusEnum.NOT.getValue()),
                createParam("2", 1001, 3, 999L, AuthStatusEnum.NOT.getValue()),
                createParam("3", 1002, 2, 998L, AuthStatusEnum.NOT.getValue()),
                createParam("4", 1002, 3, 997L, AuthStatusEnum.NOT.getValue()),
                createParam("5", 1003, 2, 996L, AuthStatusEnum.NOT.getValue()),
                createParam("6", 1003, 3, 995L, AuthStatusEnum.NOT.getValue())
        );
    }

    private List<MerchantProviderParamsDO> createTestParamsWithAuth() {
        return Arrays.asList(
                createParam("not_auth_param", 1001, 2, 1000L, AuthStatusEnum.NOT.getValue()),
                createParam("auth_param", 1001, 2, 999L, AuthStatusEnum.YES.getValue())
        );
    }

    private MerchantProviderParamsDO createParam(String id, Integer provider, Integer payway, Long ctime, Integer authStatus) {
        MerchantProviderParamsDO param = new MerchantProviderParamsDO();
        param.setId(id);
        param.setProvider(provider);
        param.setPayway(payway);
        param.setCtime(ctime);
        param.setAuthStatus(authStatus);
        param.setMerchantSn("test_merchant");
        param.setDeleted(0);
        return param;
    }
}
