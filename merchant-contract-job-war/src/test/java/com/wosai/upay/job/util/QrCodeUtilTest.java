package com.wosai.upay.job.util;

import com.wosai.upay.job.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @Auther: hrx
 * @Date: 2019-08-26
 * @Description: com.wosai.upay.job.util
 * @version: 1.0
 */
@Slf4j
public class QrCodeUtilTest extends BaseTest {

    @Autowired
    private WechatQrCodeUtils wechatQrCodeUtils;

    @Test
    public void authorizationCodeUrlTest() throws IOException {
        Map<String, Object> info = new HashMap<>();
        info.put("merchantName", "哈哈哈哈哈哈");
        info.put("concatName", "哼哼哼");
        info.put("merchantBusinessName", "哈哈哈");
        info.put("servicePhone", "1101110101010");
        info.put("weixinMerchantId", "sdjfhaskdhjflasf");
        String url = wechatQrCodeUtils.authorizationCodeUrl(info, "lkl-1016-3-32631798");
        System.out.println(url);
    }


}
