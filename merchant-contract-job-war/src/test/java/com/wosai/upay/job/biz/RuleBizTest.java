package com.wosai.upay.job.biz;


import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.service.ConfigSupportService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;


public class RuleBizTest extends BaseTest {

    @SpyBean
    RuleBiz ruleBiz;

    @MockBean
    TradeConfigService tradeConfigService;

    @Autowired
    RuleContext ruleContext;
    @MockBean
    MerchantProviderParamsMapper merchantProviderParamsMapper;
    @MockBean
    ConfigSupportService configSupportService;
    @MockBean
    AgentAppidBiz agentAppidBiz;

    @Before
    public void reflect() {
        ReflectionTestUtils.setField(ruleBiz, "tradeConfigService", tradeConfigService);
        ReflectionTestUtils.setField(ruleBiz, "merchantProviderParamsMapper", merchantProviderParamsMapper);
        ReflectionTestUtils.setField(ruleBiz, "configSupportService", configSupportService);
    }

    @Test
    public void contractByRule() {
        ContractRule contractRule = ruleContext.getContractRule("lkl-1016-3-32631798");
        ruleBiz.contractByRule("21690002971171", contractRule, new HashMap(), false);
    }

    @Test
    public void changeDbbConfigParam() {
        MerchantProviderParams params = new MerchantProviderParams().setPayway(2).setMerchant_sn("test").setProvider(1016).setChannel_no("213455");
        Mockito.doReturn(params).when(merchantProviderParamsMapper).selectByPrimaryKey(Mockito.anyObject());
        Mockito.doReturn("上海").when(configSupportService).getMerchantCity(Mockito.anyObject());
        Mockito.doReturn("agent").when(agentAppidBiz).getAgentName(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString(), Mockito.anyString());
        ruleBiz.changeDbbConfigParam("0570bab6-d832-414e-86d3-d80eb0ba2d4c", "0.38", new ArrayList<>());

    }

    @Test
    public void changeTerminalConfigParam() {
        Mockito.doReturn(null).when(tradeConfigService).getTerminalConfigByTerminalIdAndPayway(Mockito.any(), Mockito.any());
        ruleBiz.changeTerminalConfigParam(new HashMap(), new HashMap(), new ArrayList<>(), 3);
        Mockito.doReturn(new HashMap<>()).when(tradeConfigService).getTerminalConfigByTerminalIdAndPayway(Mockito.any(), Mockito.any());
        Mockito.doReturn(new HashMap<>()).when(tradeConfigService).updateTerminalConfig(Mockito.any());
        ruleBiz.changeTerminalConfigParam(new HashMap(), new HashMap(), new ArrayList<>(), 3);
    }

    @Test
    public void testChangeTradeParams() {
    }
}
