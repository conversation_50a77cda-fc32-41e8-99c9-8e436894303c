package com.wosai.upay.job.biz;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.merchant.contract.model.provider.TongLianParam;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class ContractParamsBizTest extends BaseTest {

    @Autowired
    private ContractParamsBiz biz;

    @Test
    public void buildTongLianParam() {
        TongLianParam tongLianParam = biz.buildContractParams("tonglian-1020-2", TongLianParam.class);
        assertNotNull(tongLianParam);
        assertTrue(tongLianParam == biz.buildContractParams("tonglian-1020-2", TongLianParam.class));
    }

    @Test
    public void buildContractParamsByPayMchId() {
        assertNotNull(biz.buildContractParamsByPayMchId("1584644616665", TongLianParam.class));
    }
}