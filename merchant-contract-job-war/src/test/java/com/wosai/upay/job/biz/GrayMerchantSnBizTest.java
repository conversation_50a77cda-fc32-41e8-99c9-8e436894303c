package com.wosai.upay.job.biz;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

class GrayMerchantSnBizTest extends BaseTest {

    @Autowired
    private GrayMerchantSnBiz grayMerchantSnBiz;

    @SpyBean
    ApplicationApolloConfig applicationApolloConfig;


    @Test
    public void newLklInterface() {

        Mockito.doReturn(false).when(applicationApolloConfig).getAllMerchantLklOld();
        Mockito.doReturn(false).when(applicationApolloConfig).getAllMerchantLklNew();
        //7
        boolean newLklInterface = grayMerchantSnBiz.newLklInterface("1680005261979");
        assertTrue(newLklInterface);
    }

    @Test
    public void newLklInterface001() {
        Mockito.doReturn(false).when(applicationApolloConfig).getAllMerchantLklOld();
        Mockito.doReturn(false).when(applicationApolloConfig).getAllMerchantLklNew();
        //52
        boolean newLklInterface = grayMerchantSnBiz.newLklInterface("1680005260830");
        assertFalse(newLklInterface);
    }

    @Test
    public void newLklInterface002() {
        Mockito.doReturn(true).when(applicationApolloConfig).getAllMerchantLklOld();
        Mockito.doReturn(false).when(applicationApolloConfig).getAllMerchantLklNew();
        //7
        boolean newLklInterface = grayMerchantSnBiz.newLklInterface("1680005261979");
        assertFalse(newLklInterface);
    }

    @Test
    public void newLklInterface003() {
        Mockito.doReturn(false).when(applicationApolloConfig).getAllMerchantLklOld();
        Mockito.doReturn(true).when(applicationApolloConfig).getAllMerchantLklNew();
        //52
        boolean newLklInterface = grayMerchantSnBiz.newLklInterface("1680005260830");
        assertTrue(newLklInterface);
    }

    @Test
    void getHash() {
        System.out.println(GrayMerchantSnBiz.getHash("1680005260830"));
        System.out.println(GrayMerchantSnBiz.getHash("1680005260828"));
        System.out.println(GrayMerchantSnBiz.getHash("1680005260827"));
        System.out.println(GrayMerchantSnBiz.getHash("1680005260822"));
        System.out.println(GrayMerchantSnBiz.getHash("1680005260820"));
        System.out.println(GrayMerchantSnBiz.getHash("1680005261979"));
    }
}