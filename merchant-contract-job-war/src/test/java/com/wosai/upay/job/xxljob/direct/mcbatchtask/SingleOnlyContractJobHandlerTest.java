package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.upay.job.biz.acquirer.AbstractAcquirerChangeBiz;
import com.wosai.upay.job.biz.acquirer.ChangeToHaikeBiz;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.McBatchTaskMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.service.McRulesDecisionService;
import com.wosai.upay.job.service.ContractEventService;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SingleOnlyContractJobHandlerTest {

    @Mock
    private McBatchTaskMapper mcBatchTaskMapper;
    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private ContractEventMapper contractEventMapper;
    @Mock
    private ContractTaskMapper contractTaskMapper;
    @Mock
    private ContractEventService contractEventService;
    @Mock
    private ErrorCodeManageBiz errorCodeManageBiz;
    @Mock
    private McRulesDecisionService mcRulesDecisionService;
    @Mock
    private ContractStatusService contractStatusService;
    @Mock
    private CallBackService callBackService;

    @InjectMocks
    private SingleOnlyContractJobHandler singleOnlyContractJobHandler;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(singleOnlyContractJobHandler, "objectMapper", new ObjectMapper());
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        // 调用被测试的方法
        String lockKey = singleOnlyContractJobHandler.getLockKey();

        // 验证返回的锁键是否正确
        assertEquals("SingleOnlyContractJobHandler", lockKey);
    }

    @Test
    public void execute_NoTasks_NoProcessing() {
        singleOnlyContractJobHandler.execute(new DirectJobParam());

        verify(mcBatchTaskMapper, times(1)).selectByExampleWithBLOBs(any(McBatchTaskExample.class));
    }

    @Test
    public void execute_TaskProcessing_Success() {
        McBatchTask mcBatchTask = new McBatchTask();
        mcBatchTask.setId(1);
        mcBatchTask.setPayload("{\"eventId\":1111111112126374,\"approveInfo\":{\"applyType\":\"single\",\"auditSn\":\"SP365220250212000049\",\"immediate\":false,\"merchantSn\":\"21690003980679\",\"callBackBean\":{\"auditId\":544911,\"message\":\"\",\"templateId\":357305,\"resultType\":1},\"tradeAppId\":\"1\",\"operatorName\":\"李芸测试\",\"operator\":\"c0a885db-81d1-1f61-8181-d2f7b93c0000\",\"target\":\"haike\"}}");

        List<McBatchTask> tasks = new ArrayList<>();
        tasks.add(mcBatchTask);

        when(mcBatchTaskMapper.selectByExampleWithBLOBs(any())).thenReturn(tasks);
        when(applicationContext.getBean("haike-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class)).thenReturn(Mockito.mock(ChangeToHaikeBiz.class));
        when(contractTaskMapper.getContractsBySnAndType(anyString(), anyString())).thenReturn(new ArrayList<>());
        when(contractStatusService.getContractStatus(anyString())).thenReturn(2);
        when(mcRulesDecisionService.checkMerchantEligibilityToAcquirer(anyString(), anyString(), null)).thenReturn(new ContractGroupRuleVerifyResultBO(true));
        when(contractEventService.saveContractEvent(any(), any(), any())).thenReturn(new ContractEvent().setId(1L));
        singleOnlyContractJobHandler.execute(new DirectJobParam());

        verify(mcBatchTaskMapper, times(1)).updateByPrimaryKeySelective(any(McBatchTask.class));
    }

    @Test
    public void execute_TaskProcessing_Exception() {
        McBatchTask mcBatchTask = new McBatchTask();
        mcBatchTask.setId(1);
        mcBatchTask.setPayload("{\"eventId\":1111111112126374,\"approveInfo\":{\"applyType\":\"single\",\"auditSn\":\"SP365220250212000049\",\"immediate\":false,\"merchantSn\":\"21690003980679\",\"callBackBean\":{\"auditId\":544911,\"message\":\"\",\"templateId\":357305,\"resultType\":1},\"tradeAppId\":\"1\",\"operatorName\":\"李芸测试\",\"operator\":\"c0a885db-81d1-1f61-8181-d2f7b93c0000\",\"target\":\"haike\"}}");

        List<McBatchTask> tasks = new ArrayList<>();
        tasks.add(mcBatchTask);

        when(mcBatchTaskMapper.selectByExampleWithBLOBs(any())).thenReturn(tasks);
        when(applicationContext.getBean("haike-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class)).thenReturn(Mockito.mock(ChangeToHaikeBiz.class));
        when(contractTaskMapper.getContractsBySnAndType(anyString(), anyString())).thenReturn(new ArrayList<>());
        when(contractStatusService.getContractStatus(anyString())).thenReturn(2);
        when(mcRulesDecisionService.checkMerchantEligibilityToAcquirer(anyString(), anyString(), null)).thenReturn(new ContractGroupRuleVerifyResultBO(false));
        when(errorCodeManageBiz.getPromptMessageFromErrorCodeManager(anyString(), anyString(), anyString())).thenReturn(new ErrorInfo());

        singleOnlyContractJobHandler.execute(new DirectJobParam());

        verify(mcBatchTaskMapper, times(1)).updateByPrimaryKeySelective(any(McBatchTask.class));
    }
}
