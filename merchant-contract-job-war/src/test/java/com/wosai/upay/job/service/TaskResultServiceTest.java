package com.wosai.upay.job.service;


import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.util.ProviderUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;


public class TaskResultServiceTest extends H2DbBaseTest {

    @Autowired
    TaskResultService taskResultService;

    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    private MultiProviderContractEventMapper multiEventMapper;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private PayWayConfigChangeMapper payWayConfigChangeMapper;

    @Autowired
    DataBusBiz dataBusBiz;

    @MockBean
    BankCardServiceImpl bankCardService;

    @MockBean
    WeixinAuthApplyBiz weixinAuthApplyBiz;

    @MockBean
    private RuleContext ruleContext;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @MockBean
    private ContractSubTaskBiz contractSubTaskBiz;

    @MockBean
    PendingTasksBiz pendingTasksBiz;

    @MockBean
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Mock
    private SensorSendBiz sensorSendBiz;

    @Mock
    private MerchantService merchantService;

    @Mock
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Mock
    private TradeManageBiz tradeManageBiz;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(contractTaskBiz, "sensorSendBiz", sensorSendBiz);

        ReflectionTestUtils.setField(dataBusBiz, "sensorSendBiz", sensorSendBiz);
        ReflectionTestUtils.setField(dataBusBiz, "merchantService", merchantService);
        ReflectionTestUtils.setField(dataBusBiz, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(dataBusBiz, "tradeManageBiz", tradeManageBiz);

        Mockito.doReturn(new HashMap()).when(merchantService).getMerchantBySn(any());
    }


    /**
     * 微信商家认证
     * 要改成待授权的状态
     */
    @Test
    public void changeStatusAndResultV201() {
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(44312106L);
        taskResultService.changeStatusAndResultV2(contractTask.getId(), null, TaskStatus.SUCCESS.getVal(), null, false);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(contractTask.getId());
        Assert.assertEquals(TaskStatus.WAIT_FOR_AUTH.getVal(), result.getStatus());
    }

    /**
     * 新增商户入网 成功
     * 没有multi_event
     */
    @Test
    public void changeStatusAndResultV202() {
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn("changeStatusAndResultV202").setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal());
        contractTaskBiz.insert(contractTask);
        taskResultService.changeStatusAndResultV2(contractTask.getId(), null, TaskStatus.SUCCESS.getVal(), null, false);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(contractTask.getId());
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn("changeStatusAndResultV202");
        Assert.assertEquals(TaskStatus.SUCCESS.getVal(), result.getStatus());
        Assert.assertEquals(ContractStatus.STATUS_SUCCESS, contractStatus.getStatus().intValue());
    }

    /**
     * 新增商户入网 失败
     * 没有multi_event
     */
    @Test
    public void changeStatusAndResultV203() {
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn("changeStatusAndResultV203").setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal());
        contractTaskBiz.insert(contractTask);
        taskResultService.changeStatusAndResultV2(contractTask.getId(), null, TaskStatus.FAIL.getVal(), "{\"result\":\"单元测试失败\"}", false);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(contractTask.getId());
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn("changeStatusAndResultV203");
        Assert.assertEquals(TaskStatus.FAIL.getVal(), result.getStatus());
        Assert.assertEquals(ContractStatus.STATUS_BIZ_FAIL, contractStatus.getStatus().intValue());

    }

    /**
     * 新增商户入网 成功
     * 有 multi_event
     * 主通道成功，contract_status不是成功 --> 将主通道设为默认通道
     */
    @Test
    public void changeStatusAndResultV204() {
        String merchantSn = "changeStatusAndResultV204";
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal()).setRule_group_id("tonglian");
        contractTaskBiz.insert(contractTask);

        ContractSubTask contractSubTask = new ContractSubTask().setP_task_id(contractTask.getId()).setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT)
                .setResponse_body("{\"id\":\"id\"}").setPayway(2).setChannel("channel").setChange_config(1);
        contractSubTaskMapper.insert(contractSubTask);

        ContractStatus contractStatus = new ContractStatus().setMerchant_sn(merchantSn).setStatus(ContractStatus.STATUS_PROCESS);
        contractStatusMapper.insertSelective(contractStatus);

        MultiProviderContractEvent multiEvent = new MultiProviderContractEvent().setMerchant_sn(merchantSn)
                .setStatus(MultiProviderContractEvent.STATUS_PROCESS).setPrimary_group_id("tonglian")
                .setPrimary_task_id(contractTask.getId()).setSecondary_group_id(null);
        multiEventMapper.insertSelective(multiEvent);

        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setAcquirer("tonglian");
        Mockito.doReturn(ruleGroup).when(ruleContext).getRuleGroup(multiEvent.getPrimary_group_id());

        taskResultService.changeStatusAndResultV2(contractTask.getId(), null, TaskStatus.SUCCESS.getVal(), null, false);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(contractTask.getId());
        ContractStatus resultStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        PayWayConfigChange payWayConfigChange = payWayConfigChangeMapper.selectConfigChange(merchantSn, "channel", 2);
        MultiProviderContractEvent resultEvent = multiEventMapper.selectByPrimaryKey(multiEvent.getId());

        Assert.assertNotNull(payWayConfigChange);
        Assert.assertEquals(MultiProviderContractEvent.STATUS_SUCCESS, resultEvent.getStatus().intValue());
        Assert.assertEquals(TaskStatus.SUCCESS.getVal(), result.getStatus());
        Assert.assertEquals(ContractStatus.STATUS_SUCCESS, resultStatus.getStatus().intValue());
        Assert.assertEquals("tonglian", resultStatus.getAcquirer());
    }

    /**
     * 新增商户入网 成功
     * 有 multi_event
     * 主通道成功，contract_status 成功 --> 什么都不做
     */
    @Test
    public void changeStatusAndResultV205() {
        String merchantSn = "changeStatusAndResultV205";
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal()).setRule_group_id("tonglian");
        contractTaskBiz.insert(contractTask);

        ContractStatus contractStatus = new ContractStatus().setMerchant_sn(merchantSn).setStatus(ContractStatus.STATUS_SUCCESS).setAcquirer("lklV3");
        contractStatusMapper.insertSelective(contractStatus);

        MultiProviderContractEvent multiEvent = new MultiProviderContractEvent().setMerchant_sn(merchantSn)
                .setStatus(MultiProviderContractEvent.STATUS_SUCCESS).setPrimary_group_id("tonglian")
                .setPrimary_task_id(contractTask.getId()).setSecondary_group_id(null);
        multiEventMapper.insertSelective(multiEvent);


        taskResultService.changeStatusAndResultV2(contractTask.getId(), null, TaskStatus.SUCCESS.getVal(), null, false);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(contractTask.getId());
        ContractStatus resultStatus = contractStatusMapper.selectByMerchantSn(merchantSn);

        Assert.assertEquals(TaskStatus.SUCCESS.getVal(), result.getStatus());
        Assert.assertEquals(ContractStatus.STATUS_SUCCESS, resultStatus.getStatus().intValue());
        Assert.assertEquals("lklV3", resultStatus.getAcquirer());
    }

    /**
     * 新增商户入网 成功
     * 有 multi_event
     * 次通道成功，contract_status不是成功，而且过了五分钟了 --> 次通道设为默认通道
     */
    @Test
    public void changeStatusAndResultV206() {
        String merchantSn = "changeStatusAndResultV206";
        ContractTask primaryTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal()).setRule_group_id("tonglian");
        ContractTask secondaryTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal()).setRule_group_id("lklV3");
        contractTaskBiz.insert(primaryTask);
        contractTaskBiz.insert(secondaryTask);

        ContractSubTask contractSubTask = new ContractSubTask().setP_task_id(secondaryTask.getId()).setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT)
                .setResponse_body("{\"id\":\"id\"}").setPayway(2).setChannel("channel").setChange_config(1);
        contractSubTaskMapper.insert(contractSubTask);

        ContractStatus contractStatus = new ContractStatus().setMerchant_sn(merchantSn).setStatus(ContractStatus.STATUS_PROCESS).setAcquirer("tonglian");
        contractStatusMapper.insertSelective(contractStatus);

        MultiProviderContractEvent multiEvent = new MultiProviderContractEvent().setMerchant_sn(merchantSn)
                .setStatus(MultiProviderContractEvent.STATUS_PROCESS).setPrimary_group_id("tonglian")
                .setPrimary_task_id(primaryTask.getId()).setSecondary_task_id(secondaryTask.getId()).setSecondary_group_id("lklV3")
                        .setCreate_at(new Date(System.currentTimeMillis() - 500000L));
        multiEventMapper.insertSelective(multiEvent);

        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setAcquirer("lklV3");
        Mockito.doReturn(ruleGroup).when(ruleContext).getRuleGroup(multiEvent.getSecondary_group_id());


        taskResultService.changeStatusAndResultV2(secondaryTask.getId(), null, TaskStatus.SUCCESS.getVal(), null, false);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(secondaryTask.getId());
        ContractStatus resultStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        PayWayConfigChange payWayConfigChange = payWayConfigChangeMapper.selectConfigChange(merchantSn, "channel", 2);
        MultiProviderContractEvent resultEvent = multiEventMapper.selectByPrimaryKey(multiEvent.getId());

        Assert.assertNotNull(payWayConfigChange);
        Assert.assertEquals(MultiProviderContractEvent.STATUS_SUCCESS, resultEvent.getStatus().intValue());
        Assert.assertEquals(TaskStatus.SUCCESS.getVal(), result.getStatus());
        Assert.assertEquals(ContractStatus.STATUS_SUCCESS, resultStatus.getStatus().intValue());
        Assert.assertEquals("lklV3", resultStatus.getAcquirer());
    }

    /**
     * 新增商户入网 成功
     * 有 multi_event
     * 次通道成功，contract_status是成功 --> 啥也不做
     */
    @Test
    public void changeStatusAndResultV207() {
        String merchantSn = "changeStatusAndResultV207";
        ContractTask primaryTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal()).setRule_group_id("tonglian");
        ContractTask secondaryTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal()).setRule_group_id("lklV3");
        contractTaskBiz.insert(primaryTask);
        contractTaskBiz.insert(secondaryTask);

        ContractStatus contractStatus = new ContractStatus().setMerchant_sn(merchantSn).setStatus(ContractStatus.STATUS_SUCCESS).setAcquirer("tonglian");
        contractStatusMapper.insertSelective(contractStatus);

        MultiProviderContractEvent multiEvent = new MultiProviderContractEvent().setMerchant_sn(merchantSn)
                .setStatus(MultiProviderContractEvent.STATUS_SUCCESS).setPrimary_group_id("tonglian")
                .setPrimary_task_id(primaryTask.getId()).setSecondary_task_id(secondaryTask.getId()).setSecondary_group_id("lklV3")
                .setCreate_at(new Date(System.currentTimeMillis() - 500000L));
        multiEventMapper.insertSelective(multiEvent);

        taskResultService.changeStatusAndResultV2(secondaryTask.getId(), null, TaskStatus.SUCCESS.getVal(), null, false);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(secondaryTask.getId());
        ContractStatus resultStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        MultiProviderContractEvent resultEvent = multiEventMapper.selectByPrimaryKey(multiEvent.getId());

        Assert.assertEquals(MultiProviderContractEvent.STATUS_SUCCESS, resultEvent.getStatus().intValue());
        Assert.assertEquals(TaskStatus.SUCCESS.getVal(), result.getStatus());
        Assert.assertEquals(ContractStatus.STATUS_SUCCESS, resultStatus.getStatus().intValue());
        Assert.assertEquals("tonglian", resultStatus.getAcquirer());
    }

    /**
     * 新增商户入网 失败
     * 有 multi_event
     * 主通道失败，次通道也失败
     */
    @Test
    public void changeStatusAndResultV208() {
        String merchantSn = "changeStatusAndResultV208";
        ContractTask primaryTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal()).setRule_group_id("tonglian");
        ContractTask secondaryTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.FAIL.getVal()).setRule_group_id("lklV3");
        contractTaskBiz.insert(primaryTask);
        contractTaskBiz.insert(secondaryTask);

        ContractStatus contractStatus = new ContractStatus().setMerchant_sn(merchantSn).setStatus(ContractStatus.STATUS_PROCESS).setAcquirer("tonglian");
        contractStatusMapper.insertSelective(contractStatus);

        MultiProviderContractEvent multiEvent = new MultiProviderContractEvent().setMerchant_sn(merchantSn)
                .setStatus(MultiProviderContractEvent.STATUS_PROCESS).setPrimary_group_id("tonglian")
                .setPrimary_task_id(primaryTask.getId()).setSecondary_task_id(secondaryTask.getId()).setSecondary_group_id("lklV3")
                .setCreate_at(new Date(System.currentTimeMillis() - 500000L));
        multiEventMapper.insertSelective(multiEvent);

        taskResultService.changeStatusAndResultV2(primaryTask.getId(), null, TaskStatus.FAIL.getVal(), "{\"result\":\"单元测试失败\"}", false);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(primaryTask.getId());
        ContractStatus resultStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        MultiProviderContractEvent resultEvent = multiEventMapper.selectByPrimaryKey(multiEvent.getId());

        Assert.assertEquals(MultiProviderContractEvent.STATUS_BIZ_FAIL, resultEvent.getStatus().intValue());
        Assert.assertEquals(TaskStatus.FAIL.getVal(), result.getStatus());
        Assert.assertEquals(ContractStatus.STATUS_BIZ_FAIL, resultStatus.getStatus().intValue());
        Assert.assertEquals("tonglian", resultStatus.getAcquirer());
    }

    /**
     * 新增商户入网 失败
     * 有 multi_event
     * 主通道失败，次通道成功，并且contract_status不是成功 --> 将次通道设置为默认通道
     */
    @Test
    public void changeStatusAndResultV209() {
        String merchantSn = "changeStatusAndResultV209";
        ContractTask primaryTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal()).setRule_group_id("tonglian");
        ContractTask secondaryTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.SUCCESS.getVal()).setRule_group_id("lklV3");
        contractTaskBiz.insert(primaryTask);
        contractTaskBiz.insert(secondaryTask);

        ContractStatus contractStatus = new ContractStatus().setMerchant_sn(merchantSn).setStatus(ContractStatus.STATUS_PROCESS).setAcquirer("tonglian");
        contractStatusMapper.insertSelective(contractStatus);

        MultiProviderContractEvent multiEvent = new MultiProviderContractEvent().setMerchant_sn(merchantSn)
                .setStatus(MultiProviderContractEvent.STATUS_PROCESS).setPrimary_group_id("tonglian")
                .setPrimary_task_id(primaryTask.getId()).setSecondary_task_id(secondaryTask.getId()).setSecondary_group_id("lklV3")
                .setCreate_at(new Date(System.currentTimeMillis() - 500000L));
        multiEventMapper.insertSelective(multiEvent);

        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setAcquirer("lklV3");
        Mockito.doReturn(ruleGroup).when(ruleContext).getRuleGroup(multiEvent.getSecondary_group_id());

        taskResultService.changeStatusAndResultV2(primaryTask.getId(), null, TaskStatus.FAIL.getVal(), "{\"result\":\"单元测试失败\"}", false);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(primaryTask.getId());
        ContractStatus resultStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        MultiProviderContractEvent resultEvent = multiEventMapper.selectByPrimaryKey(multiEvent.getId());

        Assert.assertEquals(MultiProviderContractEvent.STATUS_SUCCESS, resultEvent.getStatus().intValue());
        Assert.assertEquals(TaskStatus.FAIL.getVal(), result.getStatus());
        Assert.assertEquals(ContractStatus.STATUS_SUCCESS, resultStatus.getStatus().intValue());
        Assert.assertEquals("lklV3", resultStatus.getAcquirer());
    }

    /**
     * 新增商户入网 失败
     * 有 multi_event
     * 主通道失败，次通道审核中  --> 什么都不做
     */
    @Test
    public void changeStatusAndResultV210() {
        String merchantSn = "changeStatusAndResultV210";
        ContractTask primaryTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal()).setRule_group_id("tonglian");
        ContractTask secondaryTask = new ContractTask()
                .setMerchant_sn(merchantSn).setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setStatus(TaskStatus.PROGRESSING.getVal()).setRule_group_id("lklV3");
        contractTaskBiz.insert(primaryTask);
        contractTaskBiz.insert(secondaryTask);

        ContractStatus contractStatus = new ContractStatus().setMerchant_sn(merchantSn).setStatus(ContractStatus.STATUS_PROCESS).setAcquirer("tonglian");
        contractStatusMapper.insertSelective(contractStatus);

        MultiProviderContractEvent multiEvent = new MultiProviderContractEvent().setMerchant_sn(merchantSn)
                .setStatus(MultiProviderContractEvent.STATUS_PROCESS).setPrimary_group_id("tonglian")
                .setPrimary_task_id(primaryTask.getId()).setSecondary_task_id(secondaryTask.getId()).setSecondary_group_id("lklV3")
                .setCreate_at(new Date(System.currentTimeMillis() - 500000L));
        multiEventMapper.insertSelective(multiEvent);

        taskResultService.changeStatusAndResultV2(primaryTask.getId(), null, TaskStatus.FAIL.getVal(), "{\"result\":\"单元测试失败\"}", false);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(primaryTask.getId());
        ContractStatus resultStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        MultiProviderContractEvent resultEvent = multiEventMapper.selectByPrimaryKey(multiEvent.getId());

        Assert.assertEquals(MultiProviderContractEvent.STATUS_PROCESS, resultEvent.getStatus().intValue());
        Assert.assertEquals(TaskStatus.FAIL.getVal(), result.getStatus());
        Assert.assertEquals(ContractStatus.STATUS_PROCESS, resultStatus.getStatus().intValue());
        Assert.assertEquals("tonglian", resultStatus.getAcquirer());
    }
}
