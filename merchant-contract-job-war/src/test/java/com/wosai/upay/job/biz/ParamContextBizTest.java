package com.wosai.upay.job.biz;


import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.info.api.service.BankInfoService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.Constants.ConstantsEvent;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.service.BankCardServiceImpl;
import com.wosai.upay.merchant.audit.api.service.MerchantAuditService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description:
 * @time 2:14 下午 2020/8/4
 **/
public class ParamContextBizTest extends BaseTest {

    @SpyBean
    ParamContextBiz paramContextBiz;
    @MockBean
    MerchantService merchantService;
    @MockBean
    BankInfoService bankInfoService;
    @MockBean
    MerchantBankService merchantBankService;
    @MockBean
    BankCardServiceImpl bankCardService;
    @MockBean
    MerchantBusinessLicenseService merchantBusinessLicenseService;
    @MockBean
    MerchantAuditService merchantAuditService;

    @Before
    public void before() {
        ReflectionTestUtils.setField(paramContextBiz, "merchantService", merchantService);
        ReflectionTestUtils.setField(paramContextBiz, "bankInfoService", bankInfoService);
        ReflectionTestUtils.setField(paramContextBiz, "merchantBankService", merchantBankService);
        ReflectionTestUtils.setField(paramContextBiz, "bankCardService", bankCardService);
        ReflectionTestUtils.setField(paramContextBiz, "merchantBusinessLicenseService", merchantBusinessLicenseService);
        ReflectionTestUtils.setField(paramContextBiz, "auditService", merchantAuditService);
    }

    @Test
    public void getParamContextByMerchantSn() throws Exception {
        Map merchant = CollectionUtil.hashMap("id", "id");
        Mockito.doReturn(merchant).when(merchantService).getMerchantByMerchantSn(Mockito.anyString());
        ListResult preResult = new ListResult();
        preResult.setTotal(1);
        List<Map> mapList = new ArrayList<>();
        Map bank = CollectionUtil.hashMap("id", "bankId");
        mapList.add(bank);
        preResult.setRecords(mapList);
        Mockito.doReturn(preResult).when(merchantBankService).findMerchantBankAccountPres(Mockito.anyObject(), Mockito.anyMap());
        Map bankInfo = CollectionUtil.hashMap("bankinfo", "bankinfo");
        Mockito.doReturn(bankInfo).when(bankInfoService).getBankInfo(Mockito.anyMap());
        Mockito.doReturn(new HashMap<>()).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId(Mockito.anyString());
        paramContextBiz.getParamContextByMerchantSn("sn", new ContractEvent().setEvent_type(4));
        ContractEvent cardChange = new ContractEvent();
        Map eventMsg = new HashMap();
        eventMsg.put(ConstantsEvent.EVENT_TYPE_SOURCE, CollectionUtil.hashMap("card", "card"));
        cardChange.setEvent_msg(JSON.toJSONString(eventMsg))
                .setEvent_type(ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS);
        paramContextBiz.getParamContextByMerchantSn("sn", cardChange);

    }
}
