package com.wosai.upay.job.biz;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.util.CommonUtil;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.ArrayList;
import java.util.Arrays;

public class MerchantProviderParamsBizTest extends BaseTest {


    @Autowired
    MerchantProviderParamsBiz merchantProviderParamsBiz;
    @MockBean
    private MerchantProviderParamsMapper paramsMapper;


    @Test
    public void saveDirectMerchantProviderParams() {
        merchantProviderParamsBiz.saveDirectMerchantProviderParams(new MerchantProviderParamsDto()
                .setProvider(1016).setPayway(3).setMerchant_sn("sn").setExtra(CollectionUtil.hashMap("map", "map")));
        MerchantProviderParams params = new MerchantProviderParams().setProvider(1016).setPayway(3);
        params.setExtra(CommonUtil.map2Bytes(CollectionUtil.hashMap("map", "map")));
        Mockito.doReturn(Arrays.asList(params)).when(paramsMapper).selectByExampleWithBLOBs(Mockito.anyObject());
        merchantProviderParamsBiz.saveDirectMerchantProviderParams(new MerchantProviderParamsDto()
                .setProvider(1016).setPayway(3).setMerchant_sn("sn").setExtra(CollectionUtil.hashMap("map", "map")));
    }

    @Test
    public void getDirectParams() {
        merchantProviderParamsBiz.getDirectParams("sn", 3, 3);
        MerchantProviderParams params = new MerchantProviderParams();
        Mockito.doReturn(Arrays.asList(params)).when(paramsMapper).selectByExampleWithBLOBs(Mockito.anyObject());
        merchantProviderParamsBiz.getDirectParams("sn", 3, 3);
        Mockito.doReturn(Arrays.asList(params, params)).when(paramsMapper).selectByExampleWithBLOBs(Mockito.anyObject());
        try {
            merchantProviderParamsBiz.getDirectParams("sn", 3, 3);
        } catch (Exception e) {

        }
    }

    @Test
    public void getParamsById() {
        merchantProviderParamsBiz.getParamsById("id");
    }

    @Test
    public void updateByPrimaryKeySelective() {
        merchantProviderParamsBiz.updateByPrimaryKeySelective(new MerchantProviderParamsDto());
    }

    @Test
    public void deleteParamsById() {
        merchantProviderParamsBiz.deleteParamsById("id");
    }

    @Test
    public void toDO() {
        merchantProviderParamsBiz.toDO(new MerchantProviderParams());
    }

    @Test
    public void fromDO() {
        merchantProviderParamsBiz.fromDO(new MerchantProviderParamsDto());
    }

    @Test
    public void handleDirectParams() {
        merchantProviderParamsBiz.handleDirectParams(new ArrayList<>());
    }
}
