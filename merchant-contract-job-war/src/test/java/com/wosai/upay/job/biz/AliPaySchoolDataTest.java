package com.wosai.upay.job.biz;


import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.job.model.MchSnapshot;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;

/**
 * @Description: 支付宝高校食堂数据整理
 * <AUTHOR>
 * @Date: 2021/9/22 4:35 下午
 */
public class AliPaySchoolDataTest {


    public static void main(String[] args) {
        test();
    }

    public static void test() {
        String in = "/Users/<USER>/Downloads/gaoxiao.txt";
        String out = "/Users/<USER>/Downloads/school.txt";
        File read = new File(in);
        File write = new File(out);

        try {
            if (!write.isFile()) {
                write.createNewFile();
            }

            FileWriter fileWriter = new FileWriter(write);

            FileReader fileReader = new FileReader(read);
            BufferedReader br = new BufferedReader(fileReader);


            LineIterator iterator = FileUtils.lineIterator(new File(in));

            while (iterator.hasNext()) {
                String lineContent = iterator.nextLine();

                String merchantSn = lineContent.substring(0, 13);
                String mch_snapshot = lineContent.substring(14);

                MchSnapshot snapshot = JSONObject.parseObject(mch_snapshot, MchSnapshot.class);
                String shopId = snapshot.getShopId();
                try {
                    String result = String.format("%s,%s", merchantSn, shopId);

                    fileWriter.write(result + "\n");

                } catch (Exception e) {

                }
            }
            br.close();
            fileReader.close();
            fileWriter.flush();
            fileWriter.close();


        } catch (Exception e) {

        }


    }
}
