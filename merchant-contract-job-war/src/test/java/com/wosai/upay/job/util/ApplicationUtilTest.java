package com.wosai.upay.job.util;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.model.application.ZhimaAppCreateReq;
import com.wosai.upay.merchant.audit.api.model.MerchantAudit;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;


public class ApplicationUtilTest extends ApplicationUtil {

    @Autowired
    WeixinApplyUtil weixinApplyUtil;

    @Test
    public void toZhimaCreate() {
        ApplicationUtil.toCommonRs("成功");
        ApplicationUtil.toCommonRs("500失败");
        ApplicationUtil.toCommonRs("400失败");
    }

    @Test
    public void toCommonRs() {
        com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = new ContractResponse();
        ApplicationUtil.toCommonRs(contractResponse);
    }

    @Test
    public void testToCommonRs() {
        ApplicationUtil.toZhimaCreate(new ZhimaAppCreateReq(), "212121");
        CommonUtil.bytes2Map(null);
        CommonUtil.bytes2Map("2121".getBytes());
        CommonUtil.bytes2Map("{}".getBytes());
    }

    @Test
    public void testCommonUtil() {
        CommonUtil.string2Map("{}");
        CommonUtil.map2Bytes(CollectionUtil.hashMap("key", "val"));
        CommonUtil.map2String(CollectionUtil.hashMap("key", "val"));
        CommonUtil.convert(new ArrayList<>(), "key");
        Map map = CollectionUtil.hashMap("key", "val", "key2", "val2");
        CommonUtil.convert(Arrays.asList(map), "key");
        CommonUtil.convertToListMap(map);
        CommonUtil.getValues(Arrays.asList(map), "key");
        CommonUtil.round(1.2d, 3);
        CommonUtil.substring("sasasazzh", 3);
    }


    @Test
    public void weixinApplyContactInfo() {
        Map licens = CollectionUtil.hashMap("type", 0);
        Map merchant = new HashMap();
        Map bank = new HashMap();
        weixinApplyUtil.getConcatInfo(merchant, bank, licens, false);
        licens.put("type", 1);
        weixinApplyUtil.getConcatInfo(merchant, bank, licens, false);
        licens.put("type", 2);
        weixinApplyUtil.getConcatInfo(merchant, bank, licens, false);
        licens.put("type", 3);
        weixinApplyUtil.getConcatInfo(merchant, bank, licens, false);
    }

    @Test
    public void weixinApply() throws Exception {
        Map audit = CollectionUtil.hashMap(MerchantAudit.OUTDOOR_MATERIAL_PHOTO, "sasasa", MerchantAudit.INDOOR_MATERIAL_PHOTO, "asasada");
        Map licens = CollectionUtil.hashMap("type", 0);
        Map context = new HashMap();
        context.put(ParamContextBiz.KEY_BUSINESS_LICENCE, licens);
        weixinApplyUtil.toApplyParam(context, audit);

        licens.put("type", 1);
        context.put(ParamContextBiz.KEY_BUSINESS_LICENCE, licens);
        weixinApplyUtil.toApplyParam(context, audit);
        licens.put("type", 3);
        context.put(ParamContextBiz.KEY_BUSINESS_LICENCE, licens);
        weixinApplyUtil.toApplyParam(context, audit);
    }

    @Test
    public void sqlUtil() {
        Map params = CollectionUtil.hashMap("key", "val", "key2", "val2", "key3", "val3", "key4", null, "key5", "NULL");
        List list = Arrays.asList("key", "key2", "key3", "key4", "key5");
        SqlUtil.appendCriteriaParams("test", params, list);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setDateStart(System.currentTimeMillis());
        pageInfo.setDateEnd(System.currentTimeMillis());
        SqlUtil.appendCriteriaDateInfo("select *", pageInfo);
        SqlUtil.appendPageInfoCtimeDesc("select *", new PageInfo());
    }

    @Test
    public void pageHelpTest() {
        PageInfoHelper.checkoutPageInfo(null);
        PageInfo pageInfo = new PageInfo(1, 10);
        PageInfoHelper.checkoutPageInfo(pageInfo);
        pageInfo.setPage(-2);
        try {
            PageInfoHelper.checkoutPageInfo(pageInfo);
        } catch (Exception e) {
        }
    }

    @Test
    public void ProviderUtilTest() {
        List<String> sources = Arrays.asList("jerry", "tom");
        List<String> target = Arrays.asList("jerry", "mike");
        ProviderUtil.existsOne(sources, target);
        ProviderUtil.hasStatus(Arrays.asList("status"));
        ProviderUtil.exsistWeixinContractMore(0, Arrays.asList("holder"));
        ProviderUtil.exsistWeixinContractMore(1, Arrays.asList("name"));
        ProviderUtil.getAlyPayUpdate(0, sources);
        ProviderUtil.getPayWayUpdate(2, 0, sources);
        ProviderUtil.getPayWayUpdate(3, 0, sources);
        ProviderUtil.getWeixinPayUpdate(0, sources);
        System.out.println(CrmUtil.MERCHANT_BANK_ACCOUNT);
        System.out.println(CrmUtil.MERCHANTS);
    }


}
