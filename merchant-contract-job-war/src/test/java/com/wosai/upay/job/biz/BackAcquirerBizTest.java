package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.acquirer.ByPassTradeConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class BackAcquirerBizTest extends BaseTest {

    @Autowired
    private BackAcquirerBiz backAcquirerBiz;

    @Test
    void buildByPassTradeConfig() {
        List<ByPassTradeConfig> list = backAcquirerBiz.buildByPassTradeConfig("f121e182-bf5f-4033-8ef4-25834c077584", Arrays.asList(2, 3, 17));
        System.out.println(JSON.toJSONString(list));
    }
}