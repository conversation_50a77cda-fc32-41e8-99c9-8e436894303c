package com.wosai.upay.job.service;


import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.RMQService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.side.service.GeneralRuleService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.text.DateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;


public class BankCardServiceImplTest extends BaseTest {

    @Autowired
    BankCardServiceImpl bankCardService;
    @MockBean
    private KafkaTemplate kafkaTemplate;
    @MockBean
    private MerchantService merchantService;
    @MockBean
    private MerchantBankService merchantBankService;
    @MockBean
    private RMQService rmqService;
    @MockBean
    private BankService bankService;

    @Before
    public void before() {
        ReflectionTestUtils.setField(bankCardService, "merchantService", merchantService);
        ReflectionTestUtils.setField(bankCardService, "merchantBankService", merchantBankService);
        ReflectionTestUtils.setField(bankCardService, "rmqService", rmqService);
        ReflectionTestUtils.setField(bankCardService, "bankService", bankService);

    }

    @Test
    public void updateCardAfterTaskStatus() {
        Map cardRequestParam = CollectionUtil.hashMap("card", "card");
        Map context = CollectionUtil.hashMap("cardRequestParam", cardRequestParam);
        bankCardService.updateCardAfterTaskStatus(context, 3, "test");
        bankCardService.updateCardAfterTaskStatus(new HashMap(), 3, "test");
        Mockito.doReturn(cardRequestParam).when(merchantService).getMerchantBankAccountByMerchantId(Mockito.anyString());
        bankCardService.updateCardAfterTaskStatus(new HashMap(), 3, "test");
        bankCardService.updateCardAfterTaskStatus(new HashMap(), 2, "test");

    }

    @Test
    public void sendMesaageToBank() {
        bankCardService.sendMesaageToBank(new HashMap(), 2, "sn", "memo");
    }

    @Test
    public void resubmitChangePreInprogressVerify() {
        bankCardService.resubmitChangePreInProgressVerify(new HashMap());
    }
}
