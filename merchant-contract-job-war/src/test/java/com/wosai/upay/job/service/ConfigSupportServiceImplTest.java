package com.wosai.upay.job.service;


import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.CommonModel;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

public class ConfigSupportServiceImplTest extends BaseTest {

    @Autowired
    ConfigSupportService configSupportService;
    @MockBean(name = "merchantContractJdbcTemplate")
    JdbcTemplate merchantContractJdbcTemplate;
    @MockBean
    private TradeConfigService tradeConfigService;
    @MockBean
    private MerchantService merchantService;

    @Before
    public void before() {
        ReflectionTestUtils.setField(configSupportService, "tradeConfigService", tradeConfigService);
        ReflectionTestUtils.setField(configSupportService, "merchantService", merchantService);
    }


    @Test
    public void getMerchantModuleWhite() {
        Map map = new HashMap();
        map.put("app_module_white_type", 0);
        configSupportService.getMerchantModuleWhite(map);
        map.put("app_module_white_type", 1);
        configSupportService.getMerchantModuleWhite(map);
        Mockito.doReturn(map).when(merchantService).getMerchant(Mockito.anyString());
        configSupportService.getMerchantModuleWhite(map);
        Mockito.doReturn(map).when(tradeConfigService).getTradeParams(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyMap());
        map.put(CommonModel.TRADE_PARAMS, new HashMap<>());
        configSupportService.getMerchantModuleWhite(map);
    }

    @Test
    public void getMerchantCity() {
        Mockito.doReturn(new HashMap<>()).when(merchantService).getMerchantBySn(Mockito.anyString());
        configSupportService.getMerchantCity("sn");
    }
}
