package com.wosai.upay.job.xxljob.direct.antshop;

import com.wosai.upay.job.Constants.AntShopTaskConstant;
import com.wosai.upay.job.biz.AntShopBiz;
import com.wosai.upay.job.model.DO.AntShopTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AntShopQueryOperationApplyJobHandlerTest {

    @InjectMocks
    private AntShopQueryOperationApplyJobHandler antShopQueryOperationApplyJobHandler;

    @Mock
    private AntShopBiz antShopBiz;

    @Mock
    private ChatBotUtil chatBotUtil;

    private DirectJobParam directJobParam;

    @Before
    public void setUp() {
        directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(10);
        directJobParam.setQueryTime(System.currentTimeMillis());
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        // 调用被测方法
        String lockKey = antShopQueryOperationApplyJobHandler.getLockKey();

        // 验证返回的锁键是否正确
        assertEquals("AntShopQueryOperationApplyJobHandler", lockKey);
    }

    @Test
    public void execute_AllTasksProcessedSuccessfully() {
        List<AntShopTask> antShopTasks = Arrays.asList(
                new AntShopTask().setId(1L).setMerchant_sn("123").setStatus(AntShopTaskConstant.TaskStatus.ALREADY_OPERATION_APPLY),
                new AntShopTask().setId(2L).setMerchant_sn("456").setStatus(AntShopTaskConstant.TaskStatus.ALREADY_OPERATION_APPLY)
        );

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong())).thenReturn(antShopTasks);

        antShopQueryOperationApplyJobHandler.execute(directJobParam);

        verify(antShopBiz, times(2)).queryOperationApplyResult(any(AntShopTask.class));
        verify(antShopBiz, never()).updateAntShopTask(any(AntShopTask.class));
        verify(chatBotUtil, never()).sendMessageToContractWarnChatBot(anyString());
    }

    @Test
    public void execute_ExceptionDuringTaskProcessing() {
        List<AntShopTask> antShopTasks = Arrays.asList(
                new AntShopTask().setId(1L).setMerchant_sn("123").setStatus(AntShopTaskConstant.TaskStatus.ALREADY_OPERATION_APPLY),
                new AntShopTask().setId(2L).setMerchant_sn("456").setStatus(AntShopTaskConstant.TaskStatus.ALREADY_OPERATION_APPLY)
        );

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong())).thenReturn(antShopTasks);
        doThrow(new RuntimeException("Test exception")).when(antShopBiz).queryOperationApplyResult(any(AntShopTask.class));

        antShopQueryOperationApplyJobHandler.execute(directJobParam);

        verify(antShopBiz, times(2)).queryOperationApplyResult(any(AntShopTask.class));
        verify(antShopBiz, times(2)).updateAntShopTask(any(AntShopTask.class));
        verify(chatBotUtil, times(2)).sendMessageToContractWarnChatBot(anyString());
    }
}
