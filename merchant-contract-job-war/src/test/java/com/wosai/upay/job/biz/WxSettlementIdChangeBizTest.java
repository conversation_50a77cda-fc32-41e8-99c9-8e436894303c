package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.externalservice.customer.CustomerRelationClient;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;

public class WxSettlementIdChangeBizTest extends BaseTest {

//    @SpyBean
    @Autowired
    private WxSettlementIdChangeBiz biz;

    @Mock
    private MerchantService merchantService;

    @Mock
    private FeeRateService feeRateService;

    @Mock
    private CustomerRelationClient customerRelationClient;

    @MockBean
    private AopBiz aopBiz;

//    @Before
//    public void before() {
//        ReflectionTestUtils.setField(biz, "merchantService", merchantService);
//        ReflectionTestUtils.setField(biz, "feeRateService", feeRateService);
//    }



    @Test
    public void doHandleSettlementId() throws Exception {
        doReturn("xxx").when(biz).doHandle(any(), any(), any());
        doReturn(new MerchantInfo().setIndustry("xxxx")).when(merchantService).getMerchantBySn(any(), eq(null));

        MerchantProviderParams merchantProviderParams = new MerchantProviderParams()
                .setMerchant_sn("xxx");
        Assert.assertEquals("非特殊行业，不处理", biz.doHandleSettlementId(merchantProviderParams));

        doReturn(new MerchantInfo().setIndustry("e6b8d357-312d-11e6-aebb-ecf4bbdee2f0")).when(merchantService).getMerchantBySn(anyString(), eq(null));
        Assert.assertNotEquals("非特殊行业，不处理", biz.doHandleSettlementId(merchantProviderParams));

    }

    @Test
    public void doHandle() throws Exception {
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams()
                .setMerchant_sn("xxx");
        MerchantInfo merchant = new MerchantInfo()
                .setId("00311d8cf29a-af38-f9c4-a4d0-f8ea92a3")
                .setSn("mch-1680000921414");
        Map config = JSON.parseObject("{\n" +
                "    \"industry_type\":\"电信通讯/营业厅\",\n" +
                "    \"industry\":\"f0709630-1593-4d2e-a25e-2da6c3c6458a\",\n" +
                "    \"activity_type\":-1,\n" +
                "    \"isv_default_combo_id\":171,\n" +
                "    \"default_combo_id\":170,\n" +
                "    \"isv_default_fee\":\"0.78\",\n" +
                "    \"default_fee\":\"0.78\",\n" +
                "    \"notice_remark\":\"\"\n" +
                "}");

        doReturn(null).when(customerRelationClient).getMchIndirectOrg(merchant.getId());
        doReturn(Arrays.asList(new HashMap())).when(biz).getPayCombos(merchant, null,null);
        doReturn(true).when(biz).isISV(null);
        doReturn(new ArrayList<>()).when(feeRateService).listMchFeeRates(merchant.getSn());
        doNothing().when(feeRateService).applyFeeRateOne(any());
        doNothing().when(aopBiz).sendNoticeToCrm(any(), any(), any(), any());
        doNothing().when(aopBiz).sendNoticeToAdmin(any(), any(), any(), any());

        String result = biz.doHandle(merchantProviderParams, merchant, config);
        Assert.assertTrue(result.contains("设置新套餐"));
    }

    @Test
    public void sendNoticeToAdmin() {
//        biz.sendNoticeToAdmin("00311d8cf29a-af38-f9c4-a4d0-f8ea92a3", "KEADQNEXFQ7O", CollectionUtil.hashMap(
//                "indursty_name", "教育培训",
//                "remark", "如需恢复微信支付0.38%费率，业务员通过收钱吧掌柜-审批中心完成微信教育培训活动报名即可。"
//        ));
//        biz.sendNoticeToAdmin("00311d8cf29a-af38-f9c4-a4d0-f8ea92a3", "KEADQNEXFQ7O", CollectionUtil.hashMap(
//                "indursty_name", "电信通讯/营业厅"
//        ));
    }

    @Test
    public void sendNoticeToCrm() {
        Collection<String> ss = biz.getSettlementIds("cbd824f3-c294-435d-b5db-385ce3fa654d");
        System.out.println();
//        biz.sendNoticeToCrm("ae9c4434-ed99-4239-8445-e44ffdec3a82","KIKNVRB6IPBQ");
    }
}