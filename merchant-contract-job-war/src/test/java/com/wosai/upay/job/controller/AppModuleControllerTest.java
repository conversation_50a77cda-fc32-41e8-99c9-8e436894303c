package com.wosai.upay.job.controller;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.SelfOpenCcbDecpMapper;
import com.wosai.upay.job.model.AppModuleResponse;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.SelfOpenCcbDecp;
import com.wosai.upay.merchant.contract.model.ccb.response.CcbMerchantInfoResp;
import com.wosai.upay.merchant.contract.model.ccb.response.CcbPosGrpInfo;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

public class AppModuleControllerTest extends H2DbBaseTest {

    @InjectMocks
    private AppModuleController appModuleController;

    @Mock
    private MerchantService merchantService;

    @Mock
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Mock
    private TradeConfigService tradeConfigService;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private SelfOpenCcbDecpMapper selfOpenCcbDecpMapper;

    @Mock
    private ApplicationApolloConfig applicationApolloConfig;



    @Mock
    private StringRedisTemplate redisTemplate;

    private final List<Map> CCB_DECP_FAIL = JSON.parseArray("[\n" +
            "    {\n" +
            "        \"error_msg\": \"YBLA06412136\",\n" +
            "        \"error_code\": \"YBLA06412136\",\n" +
            "        \"app_msg\": \"暂时无法开通数字人民币业务,请前往Apple Store或安卓主流应用市场搜索'数字人民币'APP下载注册开通建设银行数字人名币业务,开通成功后,请重新在收钱吧app内开通数字人民币收款业务\",\n" +
            "        \"app_code\": \"YBLA06412136\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"error_msg\": \"YBLA06412140\",\n" +
            "        \"error_code\": \"YBLA06412140\",\n" +
            "        \"app_msg\": \"暂时无法开通数字人民币业务,请前往Apple Store或安卓主流应用市场搜索'数字人民币'APP下载注册开通建设银行数字人名币业务,开通成功后,请重新在收钱吧app内开通数字人民币收款业务\",\n" +
            "        \"app_code\": \"YBLA06412140\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"error_msg\": \"兜底文案放到最后\",\n" +
            "        \"error_code\": \"\",\n" +
            "        \"app_msg\": \"暂时无法开通数字人民币业务，请联系建行客服电话95533进行咨询\",\n" +
            "        \"app_code\": \"\"\n" +
            "    }\n" +
            "]", Map.class);

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(appModuleController, "contractTaskMapper", contractTaskMapper);
        ReflectionTestUtils.setField(appModuleController, "contractSubTaskMapper", contractSubTaskMapper);
        ReflectionTestUtils.setField(appModuleController, "selfOpenCcbDecpMapper", selfOpenCcbDecpMapper);
        Mockito.doReturn(new MyValueOperations<>()).when(redisTemplate).opsForValue();
    }

    /**
     * 还没有到判断是否开通融合就返回了false
     */
    @Test
    public void ccbDecp01() {
        String merchantId = "ccbDecpMerchantId01";
        Map<String, Object> request = new HashMap<>(1);
        request.put("merchantId", merchantId);

        // 1. 入参没有商户id 返回false 不可见
        AppModuleResponse appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(null).get("data");
        assertFalse(appModuleResponse.isVisible());

        // 2. 商户不存在 返回false 不可见
        appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertFalse(appModuleResponse.isVisible());
    }

    /**
     * 数币城市配置不存在 返回false 不可见
     */
    @Test
    public void ccbDecp02() {
        String merchantId = "ccbDecp03MerchantId";

        Map<String, Object> request = new HashMap<>(1);
        request.put("merchantId", merchantId);
        AppModuleResponse appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertFalse(appModuleResponse.isVisible());

    }

    /**
     * 不在商户白名单中,不可见
     */
    @Test
    public void ccbDecp03() {
        String merchantId = "ccbDecp02MerchantId";
        Mockito.doReturn(CollectionUtil.hashMap("上海市", "123456", "idList", Arrays.asList("123456"))).when(applicationApolloConfig).getCcbDecpCity();

        Map<String, Object> request = new HashMap<>(1);
        request.put("merchantId", merchantId);
        AppModuleResponse appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertFalse(appModuleResponse.isVisible());

        // 在商户白名单中, 但是商户不存在, 不可见
        request.put("merchantId", "123456");
        appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertFalse(appModuleResponse.isVisible());

    }

    /**
     * 不在城市白名单中  返回false 不可见
     */
    @Test
    public void ccbDecp04() {
        String merchantSn = "ccbDecp02MerchantSn";
        String merchantId = "ccbDecp02MerchantId";
        Mockito.doReturn(CollectionUtil.hashMap("上海市", "123456")).when(applicationApolloConfig).getCcbDecpCity();
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBusinessLicence.TYPE, 0)).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(Merchant.CITY, "成都市", Merchant.SN, merchantSn)).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBankAccount.IDENTITY, "*********")).when(merchantService).getMerchantBankAccountByMerchantId(merchantId);

        Map<String, Object> request = new HashMap<>(1);
        request.put("merchantId", merchantId);
        AppModuleResponse appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertFalse(appModuleResponse.isVisible());

    }

    /**
     * 商户是组织类型的商户  返回false 不可见
     */
    @Test
    public void ccbDecp05() {
        String merchantSn = "ccbDecp02MerchantSn";
        String merchantId = "ccbDecp02MerchantId";
        Mockito.doReturn(CollectionUtil.hashMap("上海市", "123456")).when(applicationApolloConfig).getCcbDecpCity();
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBusinessLicence.TYPE, 2)).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(Merchant.CITY, "上海市", Merchant.SN, merchantSn)).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBankAccount.IDENTITY, "*********")).when(merchantService).getMerchantBankAccountByMerchantId(merchantId);

        Map<String, Object> request = new HashMap<>(1);
        request.put("merchantId", merchantId);
        AppModuleResponse appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertFalse(appModuleResponse.isVisible());

    }

    /**
     * 商户有银行卡数据,但是没有证件号  返回false 不可见
     */
    @Test
    public void ccbDecp06() {
        String merchantSn = "ccbDecp02MerchantSn";
        String merchantId = "ccbDecp02MerchantId";
        Mockito.doReturn(CollectionUtil.hashMap("上海市", "123456")).when(applicationApolloConfig).getCcbDecpCity();
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBusinessLicence.TYPE, 1)).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(Merchant.CITY, "上海市", Merchant.SN, merchantSn)).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBankAccount.NUMBER, "*********")).when(merchantService).getMerchantBankAccountByMerchantId(merchantId);

        Map<String, Object> request = new HashMap<>(1);
        request.put("merchantId", merchantId);
        AppModuleResponse appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertFalse(appModuleResponse.isVisible());

    }

    /**
     * 商户为个体或小微 在配置的城市当中
     * 已经开通成功了融合
     */
    @Test
    public void ccbDecp07() {
        String merchantSn = "ccbDecpMerchantSn02";
        String merchantId = "ccbDecpMerchantId02";

        Mockito.doReturn(CollectionUtil.hashMap("上海市", "123456")).when(applicationApolloConfig).getCcbDecpCity();
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBusinessLicence.TYPE, 0)).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(Merchant.CITY, "上海市", Merchant.SN, merchantSn)).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBankAccount.IDENTITY, "*********")).when(merchantService).getMerchantBankAccountByMerchantId(merchantId);
        Map<String, Object> request = new HashMap<>(1);
        request.put("merchantId", merchantId);

        // 1 开通过融合， 而且Task为开通成功
        ContractTask contractTask = new ContractTask().setMerchant_sn(merchantSn).setStatus(TaskStatus.SUCCESS.getVal()).setRule_group_id("ccb").setType("新增商户入网");
        contractTaskMapper.insert(contractTask);
        ContractSubTask contractSubTask = new ContractSubTask().setMerchant_sn(merchantSn).setP_task_id(contractTask.getId()).setTask_type(5);
        contractSubTaskMapper.insert(contractSubTask);

        // 1.1 开通成功了，但是没有钱包号,也是不可以见
        AppModuleResponse appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertFalse(appModuleResponse.isVisible());

        // 1.2 开通成功了，而且有钱包号
        CcbMerchantInfoResp ccbMerchantInfoResp = new CcbMerchantInfoResp();
        CcbPosGrpInfo posGrpInfo = new CcbPosGrpInfo();
        posGrpInfo.setCstAccNo("123456");
        ccbMerchantInfoResp.setPosGrp(Arrays.asList(posGrpInfo));
        contractSubTask.setId(contractSubTask.getId()).setResponse_body(JSON.toJSONString(ccbMerchantInfoResp));
        contractSubTaskMapper.updateByPrimaryKey(contractSubTask);
        appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertTrue(appModuleResponse.isVisible());
        assertEquals(AppModuleResponse.SUCCESS_OPEN, appModuleResponse.getLetters());

        // 1.3 在开通中，也是不可见
        contractTask.setStatus(TaskStatus.PENDING.getVal());
        contractTaskMapper.updateByPrimaryKey(contractTask);
        appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertFalse(appModuleResponse.isVisible());

    }

    /**
     * 商户为个体或小微 在配置的城市当中
     * 没有开通过融合, 没有批量开通过
     */
    @Test
    public void ccbDecp08() {
        String merchantSn = "ccbDecpMerchantSn03";
        String merchantId = "ccbDecpMerchantId03";

        Mockito.doReturn(CollectionUtil.hashMap("上海市", "123456")).when(applicationApolloConfig).getCcbDecpCity();
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBusinessLicence.TYPE, 0)).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(Merchant.CITY, "上海市", Merchant.SN, merchantSn)).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBankAccount.IDENTITY, "*********")).when(merchantService).getMerchantBankAccountByMerchantId(merchantId);
        Map<String, Object> request = new HashMap<>(1);
        request.put("merchantId", merchantId);

        // 1. 没有批量开通过该商户
        // 1.1 没有开通过其他通道的数字货币 可见,并且是待申请
        AppModuleResponse appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertTrue(appModuleResponse.isVisible());
        assertEquals(AppModuleResponse.WAIT_OPEN, appModuleResponse.getLetters());

        // 1.2 开通过其他通道的数字货币 不可见
        Mockito.doReturn(CollectionUtil.hashMap(MerchantConfig.B2C_AGENT_NAME, "1018_1_1")).when(tradeConfigService).getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.DCEP.getValue());
        appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertFalse(appModuleResponse.isVisible());
    }

    /**
     * 商户为个体或小微 在配置的城市当中
     * 没有开通过融合
     */
    @Test
    public void ccbDecp09() {
        String merchantSn = "ccbDecpMerchantSn03";
        String merchantId = "ccbDecpMerchantId03";

        Mockito.doReturn(CollectionUtil.hashMap("上海市", "123456")).when(applicationApolloConfig).getCcbDecpCity();
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBusinessLicence.TYPE, 0)).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(Merchant.CITY, "上海市", Merchant.SN, merchantSn)).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBankAccount.IDENTITY, "*********")).when(merchantService).getMerchantBankAccountByMerchantId(merchantId);
        Map<String, Object> request = new HashMap<>(1);
        request.put("merchantId", merchantId);

        // 2. 该商户批量开通过
        // 2.1 开通成功
        SelfOpenCcbDecp selfOpenCcbDecp = new SelfOpenCcbDecp();
        selfOpenCcbDecp.setMerchant_sn(merchantSn);
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.SUCCESS_OPEN_STATUS);
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp);
        AppModuleResponse appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertTrue(appModuleResponse.isVisible());
        assertEquals(AppModuleResponse.SUCCESS_OPEN, appModuleResponse.getLetters());

        // 2.2 是待提交
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.WAIT_OPEN_STATUS);
        selfOpenCcbDecpMapper.updateByPrimaryKeySelective(selfOpenCcbDecp);
        appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertTrue(appModuleResponse.isVisible());
        assertEquals(AppModuleResponse.WAIT_OPEN, appModuleResponse.getLetters());

        // 2.3 是失败
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.FAIL_OPEN_STATUS);
        selfOpenCcbDecpMapper.updateByPrimaryKeySelective(selfOpenCcbDecp);
        appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertTrue(appModuleResponse.isVisible());
        assertEquals(AppModuleResponse.FAIL_OPEN, appModuleResponse.getLetters());

        // 2.4 是审核中
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.PROCESS_OPEN_STATUS);
        selfOpenCcbDecpMapper.updateByPrimaryKeySelective(selfOpenCcbDecp);
        appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertTrue(appModuleResponse.isVisible());
        assertEquals(AppModuleResponse.PROCESS_OPEN, appModuleResponse.getLetters());
    }

    /**
     * 商户为个体或小微 在配置的城市当中
     * LgPsRprNm和Acq_Mrch_CtcPsn_Nm一致
     * 没有开通过融合, 批量开通过，
     * 并且提交过，建行返回失败，文案转译
     */
    @Test
    public void ccbDecp10() {
        String merchantSn = "ccbDecpMerchantSn03";
        String merchantId = "ccbDecpMerchantId03";

        Mockito.doReturn(CollectionUtil.hashMap("上海市", "123456")).when(applicationApolloConfig).getCcbDecpCity();
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBusinessLicence.TYPE, 0)).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(Merchant.CITY, "上海市", Merchant.SN, merchantSn)).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBankAccount.IDENTITY, "*********")).when(merchantService).getMerchantBankAccountByMerchantId(merchantId);
        Map<String, Object> request = new HashMap<>(1);
        request.put("merchantId", merchantId);

        // 2. 该商户批量开通过
        // 2.1 开通失败 result为空，返回了兜底文案 返回兜底文案
        SelfOpenCcbDecp selfOpenCcbDecp = new SelfOpenCcbDecp();
        selfOpenCcbDecp.setMerchant_sn(merchantSn);
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.FAIL_OPEN_STATUS);
        selfOpenCcbDecp.setResult("");
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp);
        AppModuleResponse appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertTrue(appModuleResponse.isVisible());
        assertEquals(AppModuleResponse.FAIL_OPEN, appModuleResponse.getLetters());
        assertTrue(appModuleResponse.getSubpage().contains("暂时无法开通数字人民币业务，请联系建行客服电话95533进行咨询"));

        // 2.2 开通失败 result不为空，但是没有匹配的 返回了兜底文案
        selfOpenCcbDecp.setResult("图片上传失败");
        selfOpenCcbDecpMapper.updateByPrimaryKeySelective(selfOpenCcbDecp);
        appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertEquals(AppModuleResponse.FAIL_OPEN, appModuleResponse.getLetters());
        assertTrue(appModuleResponse.getSubpage().contains("暂时无法开通数字人民币业务，请联系建行客服电话95533进行咨询"));

        // 2.2 开通失败 result不为空，但是有匹配的 返回了匹配文案
        selfOpenCcbDecp.setResult("[ERRORCODE=YBLA06412136] [客户手机号已经绑定四类钱包，请先升级钱包为三类以上钱包]");
        selfOpenCcbDecpMapper.updateByPrimaryKeySelective(selfOpenCcbDecp);
        appModuleResponse = (AppModuleResponse) appModuleController.ccbDecp(request).get("data");
        assertEquals(AppModuleResponse.FAIL_OPEN, appModuleResponse.getLetters());
        assertTrue(appModuleResponse.getSubpage().contains("暂时无法开通数字人民币业务,请前往Apple Store或安卓主流应用市场搜索"));
    }
}