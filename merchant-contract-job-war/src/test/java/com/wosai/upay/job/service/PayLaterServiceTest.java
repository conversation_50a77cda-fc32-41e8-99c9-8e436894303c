package com.wosai.upay.job.service;

import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class PayLaterServiceTest {

    @InjectMocks
    private PayLaterServiceImpl payLaterService;
    @Mock
    private MerchantService merchantService;
    @Mock
    private MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Mock
    private ApplicationApolloConfig applicationApolloConfig;

    /**
     * 开通之前的校验
     */
    @Test
    public void check() {
        String merchantId = "merchantId";
        String merchantSn = "merchantSn";
        //1. 商户行业不支持
        Mockito.doReturn(new MerchantInfo().setIndustry("industry").setId(merchantId))
                .when(merchantService).getMerchantBySn(merchantSn, null);
        Mockito.doReturn(new HashMap<>())
                .when(applicationApolloConfig).getPayLaterIndustryFee();
        CommonPubBizException commonPubBizException = Assertions.assertThrows(CommonPubBizException.class, () -> payLaterService.check(merchantSn));
        Assert.assertEquals("当前行业不支持", commonPubBizException.getMessage());

        //2.营业执照不支持
        Mockito.doReturn(CollectionUtil.hashMap("industry", "0.38"))
                .when(applicationApolloConfig).getPayLaterIndustryFee();
        Mockito.doReturn(new MerchantBusinessLicenseInfo().setType(0))
                .when(merchantBusinessLicenseService).getMerchantBusinessLicenseByMerchantId(merchantId, null);
        commonPubBizException = Assertions.assertThrows(CommonPubBizException.class, () -> payLaterService.check(merchantSn));
        Assert.assertEquals("当前商户营业执照类型不支持", commonPubBizException.getMessage());

        //3.经营名称错误
        //3.1 长度超长
        Mockito.doReturn(new MerchantBusinessLicenseInfo().setType(1))
                .when(merchantBusinessLicenseService).getMerchantBusinessLicenseByMerchantId(merchantId, null);
        Mockito.doReturn(new MerchantInfo().setIndustry("industry").setId(merchantId).setBusiness_name("瑜伽上海长宁店AAA"))
                .when(merchantService).getMerchantBySn(merchantSn, null);
        commonPubBizException = Assertions.assertThrows(CommonPubBizException.class, () -> payLaterService.check(merchantSn));
        Assert.assertEquals("商户经营名称不符合申请标准，请联系您的业务员修改后重新提交", commonPubBizException.getMessage());
        //3.2有其他字符
        Mockito.doReturn(new MerchantInfo().setIndustry("industry").setId(merchantId).setBusiness_name("瑜伽上海长宁店-A"))
                .when(merchantService).getMerchantBySn(merchantSn, null);
        commonPubBizException = Assertions.assertThrows(CommonPubBizException.class, () -> payLaterService.check(merchantSn));
        Assert.assertEquals("商户经营名称不符合申请标准，请联系您的业务员修改后重新提交", commonPubBizException.getMessage());
        //3.3禁用词命中
        Mockito.doReturn(new MerchantInfo().setIndustry("industry").setId(merchantId).setBusiness_name("瑜伽上海长宁蚂蚁"))
                .when(merchantService).getMerchantBySn(merchantSn, null);
        Mockito.doReturn(Arrays.asList("蚂蚁", "阿里"))
                .when(applicationApolloConfig).getPayLaterForbidWords();
        commonPubBizException = Assertions.assertThrows(CommonPubBizException.class, () -> payLaterService.check(merchantSn));
        Assert.assertEquals("商户经营名称不符合申请标准，请联系您的业务员修改后重新提交", commonPubBizException.getMessage());

        //4.校验通过
        Mockito.doReturn(new MerchantInfo().setIndustry("industry").setId(merchantId).setBusiness_name("瑜伽上海长宁店12"))
                .when(merchantService).getMerchantBySn(merchantSn, null);
        Mockito.doReturn(Arrays.asList("蚂蚁", "阿里"))
                .when(applicationApolloConfig).getPayLaterForbidWords();
        payLaterService.check(merchantSn);
    }
}