package com.wosai.upay.job.refactor.Integration.task;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.refactor.task.BnsInActiveMerchantCleanTask;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * bns非活跃商户清理任务测试
 *
 * <AUTHOR>
 * @date 2024/11/26 10:08
 */
public class BnsInActiveMerchantCleanTaskTest extends BaseTest {

    @Resource
    private BnsInActiveMerchantCleanTask bnsInActiveMerchantCleanTask;


    @Test
    public void testCreateMerchantCleanTask() {
        bnsInActiveMerchantCleanTask.initCleanTask();
    }

    @Test
    public void testProcessCleanTask() {
        bnsInActiveMerchantCleanTask.batchHandleTasksInSequence(1);
    }

}
