package com.wosai.upay.job.refactor.Integration.task;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.task.HaikeUnionPayContractResultTask;
import com.wosai.upay.job.service.task.BusinessLicenceTaskService;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 轮询任务测试
 *
 * <AUTHOR>
 * @date 2025/04/24 15:08
 */
public class HaikeUnionPayContractResultTaskTest extends BaseTest {

    @Resource
    private HaikeUnionPayContractResultTask haikeUnionPayContractResultTask;


    @Test
    public void testCreateTask() {
        String merchantSn = "21690004127518";
        int delayMinute = 5;
        haikeUnionPayContractResultTask.insertTaskWithDelay(merchantSn, delayMinute);
    }



    @Test
    public void testProcessTask() {
        List<Long> mainTaskIds = Collections.singletonList(20587L);
        haikeUnionPayContractResultTask.batchHandleTasksByMainTaskIds(mainTaskIds);
    }

    @Resource
    private BusinessLicenceTaskService businessLicenceTaskService;


    @Test
    @Rollback(false)
    public void testBatchInsertTasks() {
        List<String> merchantSnList = Arrays.asList("21690004134790", "21690004128160", "21690004127753");
        businessLicenceTaskService.internalBuildHaikeUnionContractResultPollingTask(merchantSnList);
    }


}
