package com.wosai.upay.job.mapper;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.model.DO.BankDirectApply;

public class BankDirectApplyMapperTest extends BaseTest {

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Test
    public void listByProcessStatusAndPriorityTest() {
        ArrayList<Integer> processStatus = new ArrayList<>();
        processStatus.add(BankDirectApplyConstant.ProcessStatus.CONTRACT_SUCCESS);
        List<BankDirectApply> bankDirectApplies =
            bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(processStatus, "2025-07-05 17:43:33", 500);
    }
}