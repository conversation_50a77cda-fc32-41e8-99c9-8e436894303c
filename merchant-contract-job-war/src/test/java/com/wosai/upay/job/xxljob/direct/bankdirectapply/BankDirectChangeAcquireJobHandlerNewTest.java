package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.wosai.upay.job.xxljob.model.DirectExecTypeEnum;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.upay.job.BaseTest;

public class BankDirectChangeAcquireJobHandlerNewTest extends BaseTest {

    @Autowired
    private BankDirectChangeAcquireJobHandler bankDirectChangeAcquireJobHandler;

    @Test
    public void executeTest() {
        DirectJobParam param = new DirectJobParam();
        param.setQueryTime(10800000L);
        param.setBatchSize(500);
        param.setExecType(DirectExecTypeEnum.ASYNC);
        bankDirectChangeAcquireJobHandler.execute(param);
    }
}