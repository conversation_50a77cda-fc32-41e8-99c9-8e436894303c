package com.wosai.upay.job.service;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.BatchResq;
import com.wosai.upay.job.model.IcbcParams;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;


public class BatchServiceTest extends BaseTest {


    @Autowired
    BatchService batchService;

    @Test
    public void batchContract() {
        batchService.batchContract(getRequest());
    }

    @Test
    public void batchChangeByFile() {
        batchService.batchChangeByFile(getRequest());
    }

    @Test
    public void batchChangeByRule() {
        batchService.batchChangeByRule(getRequest());
    }

    private BatchResq getRequest() {
        BatchResq batchResq = new BatchResq();
        batchResq.setRule("lkl-1016-3-32631798");
        batchResq.setEffectTime(System.currentTimeMillis());
        batchResq.setOldRule("lkl-1016-3-32631798");
        batchResq.setUrl("sasasa");
        batchResq.setRemark("备注");
        batchResq.setFeeRate("0.38");
        return batchResq;
    }

    @Test
    @Rollback(false)
    public void commonImportTest() {
        String string = "[\n" +
                "    \"21690004142982\",\n" +
                "    \"1065\",\n" +
                "    \"M21002025060000004624\",\n" +
                "    \"784850140\",\n" +
                "    \"2088670176395387\",\n" +
                "    \"M21002025060000004624\",\n" +
                "    \"0.28\",\n" +
                "    \"518\",\n" +
                "    \"佟玢\",\n" +
                "    \"1237\",\n" +
                "    \"76A***71\",\n" +
                "    \"1231\"\n" +
                "]";
        List<String> row = JSONObject.parseObject(string, new TypeReference<List<String>>() {});
        String operator = "test11";
        batchService.commonImport(row, operator);
    }

    @Test
    public void importIcbcParams_test() {
        String json = "{\n" + "        \"merchant_sn\": \"21690004016892\",\n"
            + "        \"provider_mch_id\": \"21690004016892\",\n" + "        \"weixin_sub_mch_id\": \"784178483\",\n"
            + "        \"alipay_sub_mch_id\": \"2088670015717024\",\n"
            + "        \"union_open_sub_mch_id\": \"190141040087\",\n"
            + "        \"app_id\": \"10000000000003219534\",\n" + "        \"fee_rate\": \"0.38\",\n"
            + "        \"identify_end\": \"410721197907052123\",\n" + "        \"holder_name\": \"正磊\",\n"
            + "        \"private_key\": null,\n" + "        \"public_key\": null,\n"
            + "        \"should_send_sub_app_id\": true,\n" + "        \"change_immediately\": true,\n"
            + "        \"operate\": \"周华宇\"\n" + "    }";
        IcbcParams icbcParams = JSONObject.parseObject(json, IcbcParams.class);
        batchService.importIcbcParams(icbcParams, true);
    }
}
