package com.wosai.upay.job.biz.keepalive;

import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.LinkedHashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * KeepAliveDateAdjuster 测试类
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
class KeepAliveDateAdjusterTest {

    @Test
    void testAdjustDuplicateDates_EmptyInput() {
        Map<MerchantProviderParamsDO, LocalDate> input = new LinkedHashMap<>();
        Map<MerchantProviderParamsDO, LocalDate> result = KeepAliveDateAdjuster.adjustDuplicateDates(input);
        
        assertTrue(result.isEmpty());
    }

    @Test
    void testAdjustDuplicateDates_SameProviderSameDate() {
        // 测试相同 provider 可以在同一天
        Map<MerchantProviderParamsDO, LocalDate> input = new LinkedHashMap<>();
        LocalDate baseDate = LocalDate.of(2025, 8, 15);
        
        MerchantProviderParamsDO param1 = createParam("1", 1001);
        MerchantProviderParamsDO param2 = createParam("2", 1001);
        
        input.put(param1, baseDate);
        input.put(param2, baseDate);
        
        Map<MerchantProviderParamsDO, LocalDate> result = KeepAliveDateAdjuster.adjustDuplicateDates(input);
        
        // 相同 provider 应该可以在同一天
        assertEquals(baseDate, result.get(param1));
        assertEquals(baseDate, result.get(param2));
    }

    @Test
    void testAdjustDuplicateDates_DifferentProviderSameDate() {
        // 测试不同 provider 不能在同一天
        Map<MerchantProviderParamsDO, LocalDate> input = new LinkedHashMap<>();
        LocalDate baseDate = LocalDate.of(2025, 8, 15);
        
        MerchantProviderParamsDO param1 = createParam("1", 1001);
        MerchantProviderParamsDO param2 = createParam("2", 1002);
        
        input.put(param1, baseDate);
        input.put(param2, baseDate);
        
        Map<MerchantProviderParamsDO, LocalDate> result = KeepAliveDateAdjuster.adjustDuplicateDates(input);
        
        // 第一个 provider 保持原日期，第二个 provider 应该推迟一天
        assertEquals(baseDate, result.get(param1));
        assertEquals(baseDate.plusDays(1), result.get(param2));
    }

    @Test
    void testAdjustDuplicateDates_ComplexScenario() {
        // 测试复杂场景：多个 provider，多个日期
        Map<MerchantProviderParamsDO, LocalDate> input = new LinkedHashMap<>();
        LocalDate baseDate = LocalDate.of(2025, 8, 15);
        
        MerchantProviderParamsDO param1 = createParam("1", 1001); // provider 1001, 8/15
        MerchantProviderParamsDO param2 = createParam("2", 1002); // provider 1002, 8/15 -> 应该推迟到 8/16
        MerchantProviderParamsDO param3 = createParam("3", 1001); // provider 1001, 8/15 -> 可以保持 8/15
        MerchantProviderParamsDO param4 = createParam("4", 1003); // provider 1003, 8/16 -> 应该推迟到 8/17
        
        input.put(param1, baseDate);
        input.put(param2, baseDate);
        input.put(param3, baseDate);
        input.put(param4, baseDate.plusDays(1));
        
        Map<MerchantProviderParamsDO, LocalDate> result = KeepAliveDateAdjuster.adjustDuplicateDates(input);
        
        assertEquals(baseDate, result.get(param1));           // 1001: 8/15
        assertEquals(baseDate.plusDays(1), result.get(param2)); // 1002: 8/16
        assertEquals(baseDate, result.get(param3));           // 1001: 8/15 (相同provider可以同一天)
        assertEquals(baseDate.plusDays(2), result.get(param4)); // 1003: 8/17 (8/16被1002占用)
    }

    @Test
    void testAdjustDuplicateDates_PreserveProviderPriority() {
        // 测试保持 provider 优先级：先出现的 provider 优先保持原日期
        Map<MerchantProviderParamsDO, LocalDate> input = new LinkedHashMap<>();
        LocalDate baseDate = LocalDate.of(2025, 8, 15);
        
        MerchantProviderParamsDO param1 = createParam("1", 1001);
        MerchantProviderParamsDO param2 = createParam("2", 1002);
        MerchantProviderParamsDO param3 = createParam("3", 1003);
        
        input.put(param1, baseDate);
        input.put(param2, baseDate);
        input.put(param3, baseDate);
        
        Map<MerchantProviderParamsDO, LocalDate> result = KeepAliveDateAdjuster.adjustDuplicateDates(input);
        
        // 验证顺序：先出现的 provider 优先保持原日期
        assertEquals(baseDate, result.get(param1));           // 1001: 8/15 (第一个，保持原日期)
        assertEquals(baseDate.plusDays(1), result.get(param2)); // 1002: 8/16 (第二个，推迟1天)
        assertEquals(baseDate.plusDays(2), result.get(param3)); // 1003: 8/17 (第三个，推迟2天)
    }

    @Test
    void testAdjustDuplicateDates_MixedScenario() {
        // 测试混合场景：相同和不同 provider 混合
        Map<MerchantProviderParamsDO, LocalDate> input = new LinkedHashMap<>();
        LocalDate baseDate = LocalDate.of(2025, 8, 15);
        
        MerchantProviderParamsDO param1 = createParam("1", 1001); // 1001: 8/15
        MerchantProviderParamsDO param2 = createParam("2", 1001); // 1001: 8/15 (相同provider)
        MerchantProviderParamsDO param3 = createParam("3", 1002); // 1002: 8/15 -> 8/16
        MerchantProviderParamsDO param4 = createParam("4", 1002); // 1002: 8/16 (相同provider)
        MerchantProviderParamsDO param5 = createParam("5", 1001); // 1001: 8/16 -> 8/17 (时间只能后退)
        
        input.put(param1, baseDate);
        input.put(param2, baseDate);
        input.put(param3, baseDate);
        input.put(param4, baseDate.plusDays(1));
        input.put(param5, baseDate.plusDays(1));
        
        Map<MerchantProviderParamsDO, LocalDate> result = KeepAliveDateAdjuster.adjustDuplicateDates(input);
        
        assertEquals(baseDate, result.get(param1));           // 1001: 8/15
        assertEquals(baseDate, result.get(param2));           // 1001: 8/15 (相同provider)
        assertEquals(baseDate.plusDays(1), result.get(param3)); // 1002: 8/16
        assertEquals(baseDate.plusDays(1), result.get(param4)); // 1002: 8/16 (相同provider)
        assertEquals(baseDate.plusDays(2), result.get(param5));           // 1001: 8/17 (相同provider可以在8/15)
    }

    @Test
    void testAdjustDuplicateDatesSimple_SameAsOptimized() {
        // 测试简化版本和优化版本的结果一致性
        Map<MerchantProviderParamsDO, LocalDate> input = new LinkedHashMap<>();
        LocalDate baseDate = LocalDate.of(2025, 8, 15);
        
        MerchantProviderParamsDO param1 = createParam("1", 1001);
        MerchantProviderParamsDO param2 = createParam("2", 1002);
        MerchantProviderParamsDO param3 = createParam("3", 1001);
        
        input.put(param1, baseDate);
        input.put(param2, baseDate);
        input.put(param3, baseDate);
        
        Map<MerchantProviderParamsDO, LocalDate> optimizedResult = 
            KeepAliveDateAdjuster.adjustDuplicateDates(input);
        Map<MerchantProviderParamsDO, LocalDate> simpleResult = 
            KeepAliveDateAdjuster.adjustDuplicateDates(input);
        
        // 两种方法的结果应该一致
        assertEquals(optimizedResult, simpleResult);
    }

    @Test
    void testAdjustDuplicateDates_PreserveLinkedHashMapOrder() {
        // 测试保持 LinkedHashMap 的插入顺序
        Map<MerchantProviderParamsDO, LocalDate> input = new LinkedHashMap<>();
        LocalDate baseDate = LocalDate.of(2025, 8, 15);
        
        MerchantProviderParamsDO[] params = new MerchantProviderParamsDO[5];
        for (int i = 0; i < 5; i++) {
            params[i] = createParam(String.valueOf(i), 1001 + i);
            input.put(params[i], baseDate);
        }
        
        Map<MerchantProviderParamsDO, LocalDate> result = KeepAliveDateAdjuster.adjustDuplicateDates(input);
        
        // 验证结果保持了原有的顺序
        MerchantProviderParamsDO[] resultKeys = result.keySet().toArray(new MerchantProviderParamsDO[0]);
        assertArrayEquals(params, resultKeys);
        
        // 验证日期分配正确
        for (int i = 0; i < 5; i++) {
            assertEquals(baseDate.plusDays(i), result.get(params[i]));
        }
    }

    private MerchantProviderParamsDO createParam(String id, Integer provider) {
        MerchantProviderParamsDO param = new MerchantProviderParamsDO();
        param.setId(id);
        param.setProvider(provider);
        param.setMerchantSn("test_merchant");
        return param;
    }
}
