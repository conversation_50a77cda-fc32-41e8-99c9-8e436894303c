package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.bank.service.AccountVerifyService;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.PayForTask;
import com.wosai.upay.job.providers.PayForProvider;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.service.BankCardServiceImpl;
import com.wosai.upay.job.service.TaskResultService;
import com.wosai.upay.remit.exception.RemitException;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;

/**
 * @Description:代付测试
 * <AUTHOR>
 * Date 2019/12/25 4:22 下午
 **/
public class PayForProviderTest extends H2DbBaseTest {

    @Autowired
    private PayForProvider payForProvider;

    @Autowired
    private TaskResultService taskResultService;

    @Autowired
    private ContractTaskMapper contractTaskMapper;


    @Test
    public void test() {
        AccountVerifyService accountVerifyService = Mockito.mock(AccountVerifyService.class);
        ReflectionTestUtils.setField(payForProvider,"accountVerifyService",accountVerifyService);
        ReflectionTestUtils.setField(taskResultService,"dataBusBiz",Mockito.mock(DataBusBiz.class));
        ReflectionTestUtils.setField(taskResultService,"bankCardService",Mockito.mock(BankCardServiceImpl.class));
        PayForTask payForTask = new PayForTask();
        payForTask.setId(79503L).setContext_param("{\"bankAccount\":{\"city\":\"江苏省 苏州市\",\"type\":2,\"number\":\"6228480405965827372\",\"bank_card_status\":0,\"branch_name\":\"中国农业银行苏州平江新城支行\",\"bank_name\":\"中国农业银行\",\"ctime\":*************,\"id_type\":1,\"verify_status\":0,\"id\":\"33d702e1-fe56-42e0-994f-8a8e107d618f\",\"holder\":\"自助入网营业执照账户\"}}");
        payForTask.setSub_task_id(4510722L);
        Mockito.doThrow(RemitException.class).when(accountVerifyService).apply(any());
        payForProvider.processTask(payForTask);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(2585382L);
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_PAYMENT_FAIL_PUBLIC, JSON.parseObject(result.getResult(), Map.class).get("message"));

    }

}
