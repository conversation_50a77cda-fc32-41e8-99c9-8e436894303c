package com.wosai.upay.job.biz;

import com.google.common.collect.Maps;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.concurrent.SettableListenableFuture;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/11/18 14:06
 */
public class ContractTaskBizTest extends BaseTest {
    @SpyBean
    private ContractTaskBiz contractTaskBiz;

    @MockBean
    private ContractTaskMapper contractTaskMapper;

    @MockBean
    private SensorSendBiz sensorSendBiz;

    @Mock
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Mock
    private MerchantService merchantService;

    /**
     * 外部服务手动注入
     */
    @Before
    public void before() {
        ReflectionTestUtils.setField(sensorSendBiz, "merchantService", merchantService);
        ReflectionTestUtils.setField(sensorSendBiz, "kafkaTemplate", kafkaTemplate);
    }

    @Test
    public void insertTest() {
        final ContractTask contractTask = new ContractTask();
        Mockito.doReturn(1).when(contractTaskMapper).insert(contractTask);
        Mockito.doReturn(new ListResult()).when(merchantService).findMerchants(new PageInfo(1,10), Maps.newHashMap());
        Mockito.doReturn(new SettableListenableFuture()).when(kafkaTemplate).send(new String(),new Object());
        Mockito.doReturn(1).when(contractTaskBiz).insert(contractTask);
    }

    @Test
    public void updateTest() {
        final ContractTask contractTask = new ContractTask();
        Mockito.doReturn(1).when(contractTaskMapper).updateByPrimaryKey(contractTask);
        Mockito.doReturn(new ListResult()).when(merchantService).findMerchants(new PageInfo(1,10), Maps.newHashMap());
        Mockito.doReturn(new SettableListenableFuture()).when(kafkaTemplate).send(new String(),new Object());
        Mockito.doReturn(1).when(contractTaskBiz).insert(contractTask);
    }

}
