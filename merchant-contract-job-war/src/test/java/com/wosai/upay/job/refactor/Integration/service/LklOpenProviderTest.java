package com.wosai.upay.job.refactor.Integration.service;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import com.wosai.upay.job.providers.LklOpenProvider;
import com.wosai.upay.job.providers.LklV3Provider;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;


/**
 * temp test
 *
 * <AUTHOR>
 * @date 2023/11/20 13:36
 */
@Slf4j
public class LklOpenProviderTest extends BaseTest {

    @Resource
    private RuleContext ruleContext;

    @Resource
    private LklOpenProvider lklOpenProvider;

    @Resource
    private ContractTaskMapper contractTaskMapper;

    @Resource
    private ContractSubTaskMapper contractSubTaskMapper;

    @Test
    public void testProcessInsertTask() {
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(43840980L);
        ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(1000194078L);
        ContractChannel contractChannel = ruleContext.getContractRule(contractSubTask.getContract_rule()).getContractChannel();
        lklOpenProvider.processInsertTaskByRule(contractTask, contractChannel, contractSubTask);
    }


    @Resource
    private SubBizParamsBiz subBizParamsBiz;

    @Resource
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Test
    public void testInsertSubBiz() {
        MerchantProviderParams params = merchantProviderParamsMapper.selectByPrimaryKey("0a816641-5a15-4240-8de0-f4268d802f59");
        subBizParamsBiz.updateSubBizParams("21690003693319", "1", 1034, params);

    }

    @Resource
    private LklV3Provider lklV3Provider;

    @Test
    public void testCreateLklOpenUnionTermConfig() {
        lklV3Provider.createProviderTerminal("21690003687924", 1032);
    }

    @Resource
    private ProviderTerminalTaskMapper taskMapper;

    @Resource
    private ProviderTerminalBiz providerTerminalBiz;

    @Test
    public void testUnionOpenTermTask() {
        ProviderTerminalTask task = taskMapper.selectByPrimaryKey(848254L);
        providerTerminalBiz.boundTerminal(task);
    }

}
