package com.wosai.upay.job.service;

import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.ForeignCardConstant;
import com.wosai.upay.job.exception.ApplicationException;
import com.wosai.upay.job.model.ForeignCard;
import com.wosai.upay.job.model.application.CommonResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
@RunWith(MockitoJUnitRunner.class)
public class ContractApplicationServiceMockTest {

    @InjectMocks
    private ContractApplicationServiceImpl contractApplicationService;

    @Mock
    private MerchantService merchantService;

    @Mock
    private LKlV3PosService lKlV3PosService;



    @Test
    public void testGetForeignCardStatusException() {
        Map<String, Object> params = new HashMap<>();
        params.put("merchantId", "12345");
        ApplicationException applicationException =  Assert.assertThrows(ApplicationException.class, () -> contractApplicationService.getForeignCardStatus(params));
        assertEquals(applicationException.getMessage(), "商户Id不为空");
        assertEquals(applicationException.getCode(), CommonResult.BIZ_FAIL);
    }

    @Test
    public void testGetForeignCardStatusSuccess() {
        Map<String, Object> params = new HashMap<>();
        params.put("merchant_id", "12345");
        String merchantSn = "SN12345";
        ForeignCard foreignCard = new ForeignCard();
        foreignCard.setStatus(ForeignCardConstant.Status.SUCCESS);
        CommonResult expected = new CommonResult(CommonResult.SUCCESS, "Success", foreignCard);

        when(merchantService.getMerchantByMerchantId(anyString())).thenReturn(new HashMap<String, Object>() {{
            put(Merchant.SN, merchantSn);
        }});
        when(lKlV3PosService.getForeignCard(anyString())).thenReturn(foreignCard);

        boolean result = contractApplicationService.getForeignCardStatus(params);

        assertTrue(result);
    }

    @Test
    public void testGetForeignCardStatusFail() {
        // Arrange
        Map<String, Object> params = new HashMap<>();
        params.put("merchant_id", "12345");
        String merchantSn = "SN12345";
        ForeignCard foreignCard = new ForeignCard();
        foreignCard.setStatus(ForeignCardConstant.Status.FAIL);
        CommonResult expected = new CommonResult(CommonResult.BIZ_FAIL, "Business Failure");

        when(merchantService.getMerchantByMerchantId(anyString())).thenReturn(new HashMap<String, Object>() {{
            put(Merchant.SN, merchantSn);
        }});
        when(lKlV3PosService.getForeignCard(anyString())).thenReturn(foreignCard);

        boolean result = contractApplicationService.getForeignCardStatus(params);

        assertFalse(result);
    }
}
