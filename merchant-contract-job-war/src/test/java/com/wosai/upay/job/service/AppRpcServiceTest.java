package com.wosai.upay.job.service;


import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.BaseTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;


public class AppRpcServiceTest extends BaseTest {

    @Autowired
    AppRpcService appRpcService;
    @MockBean
    MerchantService merchantService;
    @MockBean
    AcquirerService acquirerService;

    @Before
    public void before() {
        ReflectionTestUtils.setField(appRpcService, "merchantService", merchantService);
        ReflectionTestUtils.setField(appRpcService, "acquirerService", acquirerService);
    }


    @Test
    public void acquirer() {
        Map merchant = CollectionUtil.hashMap("sn", "sn");
        Mockito.doReturn(merchant).when(merchantService).getMerchantByMerchantId(Mockito.anyString());
        appRpcService.acquirer(CollectionUtil.hashMap("merchant_id", "0000a8d2f834-adab-5754-5512-5e1b7c02"));
        Mockito.doThrow(new RuntimeException("ex")).when(merchantService).getMerchantByMerchantId(Mockito.anyString());
        appRpcService.acquirer(CollectionUtil.hashMap("merchant_id", "0000a8d2f834-adab-5754-5512-5e1b7c02"));
    }
}
