package com.wosai.upay.job.biz.keepalive;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * KeepAliveProviderParamsAndDateCalculator 测试类
 * 主要测试 applyKeepAliveDateRules 方法的日期调整逻辑
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
class KeepAliveDateRulesTest {

    private KeepAliveProviderParamsAndDateCalculator calculator;

    @BeforeEach
    void setUp() {
        calculator = new KeepAliveProviderParamsAndDateCalculator();
    }

    @Test
    void testApplyKeepAliveDateRules_MaxDelayConstraint_BeforeToday() throws Exception {
        // 测试：最大延迟约束 - 最大保活日期 < 今天
        LocalDate normalKeepAliveDate = LocalDate.of(2025, 8, 20);  // 基础保活日期
        LocalDate maxKeepAliveDate = LocalDate.of(2025, 8, 15);     // 最大保活日期（过去）
        LocalDate today = LocalDate.of(2025, 8, 18);               // 今天
        LocalDate nextDelayDay = LocalDate.of(2025, 8, 19);        // 从今天起的延迟日期

        LocalDate result = callApplyKeepAliveDateRules(normalKeepAliveDate, maxKeepAliveDate, today, nextDelayDay);
        
        // 期望结果：最大保活日期（最高优先级）
        assertEquals(maxKeepAliveDate, result);
    }

    @Test
    void testApplyKeepAliveDateRules_MaxDelayConstraint_EqualsToday() throws Exception {
        // 测试：最大延迟约束 - 最大保活日期 = 今天
        LocalDate normalKeepAliveDate = LocalDate.of(2025, 8, 20);  // 基础保活日期
        LocalDate maxKeepAliveDate = LocalDate.of(2025, 8, 18);     // 最大保活日期（今天）
        LocalDate today = LocalDate.of(2025, 8, 18);               // 今天
        LocalDate nextDelayDay = LocalDate.of(2025, 8, 22);        // 从今天起的延迟日期

        LocalDate result = callApplyKeepAliveDateRules(normalKeepAliveDate, maxKeepAliveDate, today, nextDelayDay);
        
        // 期望结果：最大保活日期（最高优先级）
        assertEquals(maxKeepAliveDate, result);
    }

    @Test
    void testApplyKeepAliveDateRules_NormalDateEarlier() throws Exception {
        // 测试：最大保活日期 < 今天，且基础保活日期 > 今天，返回基础保活日期
        LocalDate normalKeepAliveDate = LocalDate.of(2025, 8, 20);  // 基础保活日期
        LocalDate maxKeepAliveDate = LocalDate.of(2025, 8, 25);     // 最大保活日期（更晚）
        LocalDate today = LocalDate.of(2025, 8, 18);               // 今天
        LocalDate nextDelayDay = LocalDate.of(2025, 8, 22);        // 从今天起的延迟日期


        LocalDate result = callApplyKeepAliveDateRules(normalKeepAliveDate, maxKeepAliveDate, today, nextDelayDay);

        // 期望结果：基础保活日期（因为它更早）
        assertEquals(normalKeepAliveDate, result);
    }

    @Test
    void testApplyKeepAliveDateRules_MaxDateEarlier() throws Exception {
        // 测试：最大保活日期 > 今天，且基础保活日期 > 今天，返回基础保活日期
        LocalDate normalKeepAliveDate = LocalDate.of(2025, 8, 25);  // 基础保活日期
        LocalDate maxKeepAliveDate = LocalDate.of(2025, 8, 26);     // 最大保活日期（更早）
        LocalDate today = LocalDate.of(2025, 8, 18);               // 今天
        LocalDate nextDelayDay = LocalDate.of(2025, 8, 27);        // 从今天起的延迟日期


        LocalDate result = callApplyKeepAliveDateRules(normalKeepAliveDate, maxKeepAliveDate, today, nextDelayDay);

        // 期望结果：基础保活日期
        assertEquals(normalKeepAliveDate, result);
    }

    @Test
    void testApplyKeepAliveDateRules_SameDate() throws Exception {
        // 测试：基础保活日期 = 最大保活日期
        LocalDate normalKeepAliveDate = LocalDate.of(2025, 8, 20);  // 基础保活日期
        LocalDate maxKeepAliveDate = LocalDate.of(2025, 8, 20);     // 最大保活日期（相同）
        LocalDate today = LocalDate.of(2025, 8, 18);               // 今天
        LocalDate nextDelayDay = LocalDate.of(2025, 8, 27);        // 从今天起的延迟日期

        LocalDate result = callApplyKeepAliveDateRules(normalKeepAliveDate, maxKeepAliveDate, today, nextDelayDay);

        // 期望结果：任一日期（因为相同）
        assertEquals(normalKeepAliveDate, result);
        assertEquals(maxKeepAliveDate, result);
    }

    @Test
    void testApplyKeepAliveDateRules_DefaultRule_FutureDate() throws Exception {
        // 测试：默认规则 - 基础保活日期>明天，直接返回
        LocalDate normalKeepAliveDate = LocalDate.of(2025, 8, 22);  // 基础保活日期（后天之后）
        LocalDate maxKeepAliveDate = LocalDate.of(2025, 8, 25);     // 最大保活日期（未来）
        LocalDate today = LocalDate.of(2025, 8, 18);               // 今天
        LocalDate nextDelayDay = LocalDate.of(2025, 8, 27);        // 从今天起的延迟日期

        LocalDate result = callApplyKeepAliveDateRules(normalKeepAliveDate, maxKeepAliveDate, today, nextDelayDay);
        
        // 期望结果：基础保活日期
        assertEquals(normalKeepAliveDate, result);
    }

    @Test
    void testApplyKeepAliveDateRules_DefaultRule_PastDate() throws Exception {
        // 测试：默认规则 - 基础保活日期<明天，但最大延迟约束不生效
        LocalDate normalKeepAliveDate = LocalDate.of(2025, 8, 16);  // 基础保活日期（过去）
        LocalDate maxKeepAliveDate = LocalDate.of(2025, 8, 25);     // 最大保活日期（未来）
        LocalDate today = LocalDate.of(2025, 8, 18);               // 今天
        LocalDate nextDelayDay = LocalDate.of(2025, 8, 22);        // 从今天起的延迟日期


        LocalDate result = callApplyKeepAliveDateRules(normalKeepAliveDate, maxKeepAliveDate, today, nextDelayDay);
        
        // 期望结果：从今天起的延迟日期
        assertEquals(nextDelayDay, result);
    }

    /**
     * 通过反射调用私有方法 applyKeepAliveDateRules
     */
    private LocalDate callApplyKeepAliveDateRules(LocalDate normalKeepAliveDate, 
                                                 LocalDate maxKeepAliveDate,
                                                 LocalDate today,
                                                  LocalDate nextDelayDay) throws Exception {
        Method method = KeepAliveProviderParamsAndDateCalculator.class.getDeclaredMethod(
                "applyKeepAliveDateRules", 
                LocalDate.class, LocalDate.class, LocalDate.class, LocalDate.class);
        method.setAccessible(true);
        return (LocalDate) method.invoke(calculator, normalKeepAliveDate, maxKeepAliveDate, today, nextDelayDay);
    }
}
