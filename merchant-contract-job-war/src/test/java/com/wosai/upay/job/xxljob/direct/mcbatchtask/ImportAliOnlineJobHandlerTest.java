package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.job.biz.OnlinePaymentBiz;
import com.wosai.upay.job.mapper.McBatchTaskMapper;
import com.wosai.upay.job.model.BatchImportAliOnlineMerchantsExcel;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.util.ExcelUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ImportAliOnlineJobHandlerTest {

    @InjectMocks
    private ImportAliOnlineJobHandler importAliOnlineJobHandler;

    @Mock
    private OnlinePaymentBiz onlinePaymentBiz;

    @Mock
    private McBatchTaskMapper mcBatchTaskMapper;

    @Mock
    private LogService logService;

    @Mock
    private ExcelUtil excelUtil;


    private ThreadPoolTaskExecutor batchScheduleExecutorThreadPoolTaskExecutor = new ThreadPoolTaskExecutor();
    {
        batchScheduleExecutorThreadPoolTaskExecutor.initialize();
    }

    private DirectJobParam param;
    private McBatchTask mcBatchTask;
    private List<BatchImportAliOnlineMerchantsExcel> excelList;

    @Before
    public void setUp() {
        param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());

        // 初始化McBatchTask
        mcBatchTask = new McBatchTask();
        mcBatchTask.setId(1);
        mcBatchTask.setStatus(0);
        mcBatchTask.setType(11);
        mcBatchTask.setEffect_time(new Date());
        mcBatchTask.setTask_apply_log_id("id");

        HashMap<String, String> payload = new HashMap<>();
        payload.put("fileUrl", "http://example.com/test.xlsx");
        mcBatchTask.setPayload(JSON.toJSONString(payload));

        // 初始化Excel数据
        excelList = new ArrayList<>();
        BatchImportAliOnlineMerchantsExcel excel = new BatchImportAliOnlineMerchantsExcel();
        excelList.add(excel);

        ReflectionTestUtils.setField(importAliOnlineJobHandler, "batchScheduleExecutorThreadPoolTaskExecutor", batchScheduleExecutorThreadPoolTaskExecutor);
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String lockKey = importAliOnlineJobHandler.getLockKey();
        assertEquals("ImportAliOnlineJobHandler", lockKey);
    }

    @Test
    public void execute_Success() {
        // Mock数据准备
        List<McBatchTask> taskList = new ArrayList<>();
        taskList.add(mcBatchTask);

        // Mock方法行为
        when(mcBatchTaskMapper.selectByExampleWithBLOBs(any(McBatchTaskExample.class))).thenReturn(taskList);
        when(excelUtil.getExcelInfoList(anyString(), any(BatchImportAliOnlineMerchantsExcel.class))).thenReturn(excelList);
        // 执行测试方法
        importAliOnlineJobHandler.execute(param);

        // 验证调用
        verify(mcBatchTaskMapper).selectByExampleWithBLOBs(any(McBatchTaskExample.class));
        verify(mcBatchTaskMapper).updateByPrimaryKeySelective(any(McBatchTask.class));
        verify(logService).updateTaskApplyLog(any(HashMap.class));
    }

    @Test
    public void execute_WithException() {
        // Mock数据准备
        List<McBatchTask> taskList = new ArrayList<>();
        taskList.add(mcBatchTask);

        // Mock方法抛出异常
        when(mcBatchTaskMapper.selectByExampleWithBLOBs(any(McBatchTaskExample.class))).thenReturn(taskList);
        when(excelUtil.getExcelInfoList(anyString(), any(BatchImportAliOnlineMerchantsExcel.class)))
                .thenThrow(new RuntimeException("Test Exception"));

        // 执行测试方法
        importAliOnlineJobHandler.execute(param);

        // 验证异常处理
        verify(mcBatchTaskMapper).updateByPrimaryKeySelective(argThat(task ->
                task.getStatus() == 3 && task.getId().equals(mcBatchTask.getId())
        ));
        verify(logService).updateTaskApplyLog(argThat(map ->
                map.get(TaskApplyLog.APPLY_STATUS).equals(ImportAliOnlineJobHandler.APPLY_STATUS_EXCUTE_FAILURE)
        ));
    }

    @Test
    public void execute_WithEmptyTaskList() {
        // Mock空任务列表
        when(mcBatchTaskMapper.selectByExampleWithBLOBs(any(McBatchTaskExample.class)))
                .thenReturn(new ArrayList<>());

        // 执行测试方法
        importAliOnlineJobHandler.execute(param);

        // 验证没有进行后续处理
        verify(excelUtil, never()).getExcelInfoList(anyString(), any(BatchImportAliOnlineMerchantsExcel.class));
        verify(mcBatchTaskMapper, never()).updateByPrimaryKeySelective(any(McBatchTask.class));
        verify(logService, never()).updateTaskApplyLog(any(HashMap.class));
    }
}
