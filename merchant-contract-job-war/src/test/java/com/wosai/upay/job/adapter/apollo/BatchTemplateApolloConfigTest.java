package com.wosai.upay.job.adapter.apollo;

import com.wosai.upay.job.BaseTest;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/12
 */
public class BatchTemplateApolloConfigTest extends BaseTest {

    @Autowired
    private BatchTemplateApolloConfig apolloConfig;

    @Test
    public void getBatchTemplates() {
        List<Map> batchTemplateList1 = apolloConfig.getList("batch-template", "[]");
        List<Map> batchTemplateList2 = apolloConfig.getBatchTemplates();
        Assert.assertEquals(batchTemplateList1, batchTemplateList2);
    }
}
