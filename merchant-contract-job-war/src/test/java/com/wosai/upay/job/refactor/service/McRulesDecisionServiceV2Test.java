package com.wosai.upay.job.refactor.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.utils.thread.CollectionWorker;
import com.wosai.assistant.response.UserBean;
import com.wosai.assistant.service.UserRpcService;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.BusinessRuleBiz;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.model.NetInRuleGroups;
import com.wosai.upay.job.refactor.mapper.MerchantProviderParamsDynamicMapper;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.Deleted;
import com.wosai.upay.job.refactor.model.enums.NetInSceneEnum;
import com.wosai.upay.job.refactor.service.impl.McRulesDecisionServiceImpl;
import com.wosai.upay.job.refactor.service.impl.MerchantFeatureExtractor;
import com.wosai.upay.job.xxljob.batch.contractevent.ContractEventCreateNetInTaskJobHandler;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 规则决策测试类V2
 *
 * <AUTHOR>
 * @date 2024/08/21 16:59
 */
@Slf4j
@ActiveProfiles("dev")
public class McRulesDecisionServiceV2Test extends BaseTest {

    @Resource
    private McRulesDecisionServiceImpl mcRulesDecisionService;


    private final MerchantFeatureBO merchantFeatureBO = new MerchantFeatureBO();

    @Before
    public void initMerchant() {
        merchantFeatureBO.setMerchantSn("**************");
        merchantFeatureBO.setMerchantSn("test001");
        merchantFeatureBO.setSettlementAccountType("3");
        MerchantFeatureBO.ExtraFeature extraFeature = new MerchantFeatureBO.ExtraFeature();
        MerchantFeatureBO.ExtraFeature.AccountInfo accountInfo = new MerchantFeatureBO.ExtraFeature.AccountInfo();
        accountInfo.setAccountType(2);
        accountInfo.setIdentityId("540127198108113435");
        accountInfo.setHolderName("齐天大圣");
        MerchantBusinessLicenseInfo merchantBusinessLicenseInfo = new MerchantBusinessLicenseInfo();
        merchantBusinessLicenseInfo.setLegal_person_id_number("540127198108113435");
        merchantBusinessLicenseInfo.setName("");
        merchantBusinessLicenseInfo.setType(2);
        extraFeature.setAccountInfo(accountInfo);
        extraFeature.setMerchantBusinessLicenseInfo(merchantBusinessLicenseInfo);
        merchantFeatureBO.setExtraFeature(extraFeature);
    }


    /**
     * or规则测试
     */
    @Test
    public void testNestRule2() {
        merchantFeatureBO.setName("上海望德文化艺术有限公司");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(1);
        assertThat(detailDOS.iterator().next().getGroupId()).isEqualTo("fuyou");
    }

    /**
     * or规则测试
     */
    @Test
    public void testNestRule3() {
        merchantFeatureBO.setPromotionOrganizationPath("85000,98948,98949,107973,3545555");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(2);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
        assertThat(iterator.next().getGroupId()).isEqualTo("haike");
    }

    /**
     * haike unable
     */
    @Test
    public void testNestRule4() {
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setName("xxx公司");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        if (CollectionUtils.isNotEmpty(detailDOS)) {
            assertThat(detailDOS.stream().map(GroupCombinedStrategyDetailDO::getGroupId).collect(Collectors.toSet())).doesNotContain("haike");
        }
    }

    /**
     * lklorg unable
     */
    @Test
    public void testNestRule5() {
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setName("中国移动有限xxxx");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        if (CollectionUtils.isNotEmpty(detailDOS)) {
            assertThat(detailDOS.stream().map(GroupCombinedStrategyDetailDO::getGroupId).collect(Collectors.toSet())).doesNotContain("lklorg");
        }
    }

    /**
     * only can
     */
    @Test
    public void testNestRule6() {
        merchantFeatureBO.setPersonalCertificateType("1");
        merchantFeatureBO.setName("CS商户FY");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("fuyou");
    }

    /**
     * only can
     */
    @Test
    public void testNestRule7() {
        merchantFeatureBO.setPromotionOrganizationPath("85000,98948,98950,104910.243343");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * can
     */
    @Test
    public void testNestRule8() {
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setIndustry("76d27502-312d-11e6-aebb-ecf4bbdee2f0");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("haike");
    }

    /**
     * can
     */
    @Test
    public void testNestRule9() {
        merchantFeatureBO.setPromotionOrganizationPath("85000,98948,98950,104910.243343");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * can
     */
    @Test
    public void testNestRule10() {
        merchantFeatureBO.setPersonalCertificateType("1");
        merchantFeatureBO.setCityCode("440900");
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setPromotionOrganizationPath("2343243");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(2);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("fuyou");
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * checkMerchantEligibilityToAcquirer
     */
    @Test
    public void testNestRule11() {
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setName("中国移动有限xxxx");
        ContractGroupRuleVerifyResultBO result = mcRulesDecisionService.checkEligibilityByUnableRule(merchantFeatureBO, AcquirerTypeEnum.LKL_V3.getValue());
        assertThat(result.isCheckPass()).isEqualTo(false);
    }

    @Test
    public void testDefault() {
    }


    @Resource
    private MerchantProviderParamsDynamicMapper paramsDynamicMapper;

    @Test
    public void multiTest() {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .select(MerchantProviderParamsDO::getMerchantSn)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .ge(MerchantProviderParamsDO::getMerchantSn, "mch-1680001575858")
                .orderByDesc(MerchantProviderParamsDO::getMerchantSn)
                .last("limit 1000");
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = paramsDynamicMapper.selectList(lambdaQueryWrapper);
        CollectionWorker.of(merchantProviderParamsDOS).forEach(merchantProviderParamsDO -> {
            String merchantSn = merchantProviderParamsDO.getMerchantSn();
            try {
                Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupBySnAndOrg(merchantSn, null, null).get_1();
                log.info("商户:{}, 选择的收单机构: {}", merchantSn, detailDOS);
            } catch (Exception e) {
                log.error("exception: ", e);
            }
        });
    }


    @Resource
    private MerchantFeatureExtractor merchantFeatureExtractor;

    @Resource
    private ContractEventCreateNetInTaskJobHandler contractEventCreateNetInTaskJobHandler;

    @Resource
    private ContractEventMapper contractEventMapper;

    @Test
    public void testMerchantFeatureExtractor() {
        contractEventCreateNetInTaskJobHandler.doHandleSingleData(contractEventMapper.selectByPrimaryKey(1111111112222130L));
    }

    @Test
    public void test1() {
        String req = "{\n" +
                "        \"name\": \"test1111\",\n" +
                "        \"type\": \"1\",\n" +
                "        \"districtCode\": \"320506\",\n" +
                "        \"provinceCode\": \"320000\",\n" +
                "        \"cityCode\": \"320500\",\n" +
                "        \"provinceName\": \"江苏省\",\n" +
                "        \"cityName\": \"苏州市\",\n" +
                "        \"industry\": \"7198ee72-7371-422e-833c-eb6ddd9be5b1\",\n" +
                "        \"promotionOrganizationPath\": \"00003\",\n" +
                "        \"organizationPath\": \"00003\",\n" +
                "        \"bankAccountType\": \"1\",\n" +
                "        \"legalPersonType\": \"1\",\n" +
                "        \"settlementAccountType\": \"1\",\n" +
                "        \"personalCertificateType\": \"1\",\n" +
                "        \"businessName\": \"测试经营部\",\n" +
                "        \"openedBusinessAppIdListJson\": \"[\\\"6a50e156-7222-41a9-99e1-43d87a0dfc9a\\\"]\",\n" +
                "        \"acquirer\": null,\n" +
                "        \"paymentMode\": null,\n" +
                "        \"storeProvinces\": \"320000\",\n" +
                "        \"storeCities\": \"320500\",\n" +
                "        \"netInScene\": \"BUSINESS_OPENING\",\n" +
                "        \"legalPersonCertificateType\": \"1\",\n" +
                "        \"legalIdCardAge\": \"35\",\n" +
                "        \"settlementIdCardAge\": \"28\",\n" +
                "        \"extraFeature\": {\n" +
                "            \"merchantBusinessLicenseInfo\": {\n" +
                "                \"id\": null,\n" +
                "                \"merchant_id\": \"04259f27-178c-4e00-8161-b75959c21705\",\n" +
                "                \"type\": 2,\n" +
                "                \"photo\": \"https://private-images.shouqianba.com/sales-system-gateway/2025-07-24/9662105a0b704bb3b6d265de0eac5883.jpeg?Expires=**********&OSSAccessKeyId=STS.NYQhMu1LffqSxrywi3oxUYsUB&Signature=a9o5TVlPwmOEuFgoTOxKSHayoik%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5rkI%2FfB3JNH0bO4elTIk2lmY%2Fd5trb%2BoDz2IHxNdXVvAO8atfoxlWxZ6fgSlolwVpVIX0rJccZrq49O%2B0a6etOe5pfut%2BVd18DgRjTJWkZVYBRoiL6rIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYE2fVMmP%2FdmQPAa%2FaStyqMooJbIjo6ywAxIeP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6iKFoZGnddForPMkvPiAnq0YzjuduTGsLE7qANOuy%2Bot6qFOhDajhtHpDHPELtvUr2B2c%2FFTYSsXJwGRgwSJGhqAASIT1te2ppE1TQkCEJg2xN%2Fs21on3VMwMi9%2ByBrBHxXfUZqm%2BCeSSxf5tQsGiwRBQ%2F7BYTGaKCo%2Fd2ht6xR8QvGadHJkeTlg%2Fx8Wkd1XN8d9sM7Y91gdi7tK2nHYo0DYRIIfQlPxVPNoZBiEzy5SqNsRvzAKrz0qJyBhLYX8DJo4IAA%3D\",\n" +
                "                \"number\": \"91520103MAEN9M5H35\",\n" +
                "                \"name\": \"贵州青鸾奇点信息科技有限公司\",\n" +
                "                \"business_scope\": \"法律、法规、国务院决定规定禁止的不得经营;法律、法规、国务院决定规定应当许可(审批)的,经审批机关批准后凭许可(审批)文件经营;法律、法规、国务院决定规定无需许可(审批)的,市场主体自主选择经营.许可项目:旅游业务;住宿服务;国际道路旅客运输.(依法须经批准的项目,经相关部门批准后方可开展经营活动,具体经营项目以相关部门批准文件或许可证件为准)一般项目:技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广;信息咨询服务(不含许可类信息咨询服务);企业管理咨询;业务培训(不含教育培训、职业技能培训等需取得许可的培训);企业形象策划;市场营销策划;咨询策划服务;社会经济咨询服务;健康咨询服务(不含诊疗服务);组织文化艺术交流活动;组织体育表演活动;体育竞赛组织;票务代理服务;婚姻介绍服务;婚庆礼仪服务;互联网销售(除销售需要许可的商品);日用百货销售;旅行社服务网点旅游招徕、咨询服务;单用途商业预付卡代理销售;信息技术咨询服务.(除依法须经批准的项目外,凭营业执照依法自主开展经营活动)\",\n" +
                "                \"validity\": \"20250703-99991231\",\n" +
                "                \"address\": \"贵州省贵阳市云岩区杨惠街道金西大道延伸段万科城B区27-28商业-208\",\n" +
                "                \"letter_of_authorization\": null,\n" +
                "                \"legal_person_id_type\": 1,\n" +
                "                \"legal_person_id_card_front_photo\": \"https://private-images.shouqianba.com/sales-system-gateway/2025-07-24/05e74b5e764b441ab5b5387162616420.jpeg?Expires=**********&OSSAccessKeyId=STS.NYQhMu1LffqSxrywi3oxUYsUB&Signature=e0iivT7AmWUgrlC5GZxMUxi0ROc%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5rkI%2FfB3JNH0bO4elTIk2lmY%2Fd5trb%2BoDz2IHxNdXVvAO8atfoxlWxZ6fgSlolwVpVIX0rJccZrq49O%2B0a6etOe5pfut%2BVd18DgRjTJWkZVYBRoiL6rIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYE2fVMmP%2FdmQPAa%2FaStyqMooJbIjo6ywAxIeP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6iKFoZGnddForPMkvPiAnq0YzjuduTGsLE7qANOuy%2Bot6qFOhDajhtHpDHPELtvUr2B2c%2FFTYSsXJwGRgwSJGhqAASIT1te2ppE1TQkCEJg2xN%2Fs21on3VMwMi9%2ByBrBHxXfUZqm%2BCeSSxf5tQsGiwRBQ%2F7BYTGaKCo%2Fd2ht6xR8QvGadHJkeTlg%2Fx8Wkd1XN8d9sM7Y91gdi7tK2nHYo0DYRIIfQlPxVPNoZBiEzy5SqNsRvzAKrz0qJyBhLYX8DJo4IAA%3D\",\n" +
                "                \"legal_person_id_card_back_photo\": \"https://private-images.shouqianba.com/sales-system-gateway/2025-07-24/dfc21cc4d92a442fad28c56919dea12b.jpeg?Expires=**********&OSSAccessKeyId=STS.NYQhMu1LffqSxrywi3oxUYsUB&Signature=ExwCyJb8by%2BXG2yuDz94oiHx6ic%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5rkI%2FfB3JNH0bO4elTIk2lmY%2Fd5trb%2BoDz2IHxNdXVvAO8atfoxlWxZ6fgSlolwVpVIX0rJccZrq49O%2B0a6etOe5pfut%2BVd18DgRjTJWkZVYBRoiL6rIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYE2fVMmP%2FdmQPAa%2FaStyqMooJbIjo6ywAxIeP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6iKFoZGnddForPMkvPiAnq0YzjuduTGsLE7qANOuy%2Bot6qFOhDajhtHpDHPELtvUr2B2c%2FFTYSsXJwGRgwSJGhqAASIT1te2ppE1TQkCEJg2xN%2Fs21on3VMwMi9%2ByBrBHxXfUZqm%2BCeSSxf5tQsGiwRBQ%2F7BYTGaKCo%2Fd2ht6xR8QvGadHJkeTlg%2Fx8Wkd1XN8d9sM7Y91gdi7tK2nHYo0DYRIIfQlPxVPNoZBiEzy5SqNsRvzAKrz0qJyBhLYX8DJo4IAA%3D\",\n" +
                "                \"legal_person_name\": \"李毅\",\n" +
                "                \"legal_person_id_number\": \"522425199403225137\",\n" +
                "                \"id_validity\": \"20231218-20431218\",\n" +
                "                \"legal_person_id_card_address\": \"贵州省织金县大平乡新黔村蓑衣塘组\",\n" +
                "                \"legal_person_id_card_issuing_authority\": \"织金县公安局\",\n" +
                "                \"ctime\": null,\n" +
                "                \"mtime\": null,\n" +
                "                \"deleted\": null,\n" +
                "                \"extra\": null,\n" +
                "                \"verify_status\": null,\n" +
                "                \"trade_license_list\": [\n" +
                "                    {\n" +
                "                        \"id\": null,\n" +
                "                        \"verify_status\": null,\n" +
                "                        \"extra\": null,\n" +
                "                        \"ctime\": null,\n" +
                "                        \"mtime\": null,\n" +
                "                        \"version\": null,\n" +
                "                        \"deleted\": false,\n" +
                "                        \"business_license_id\": null,\n" +
                "                        \"license_name\": null,\n" +
                "                        \"license_number\": null,\n" +
                "                        \"license_validity\": null,\n" +
                "                        \"license_photo\": null,\n" +
                "                        \"license_type\": null\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"trade_license\": \"\"\n" +
                "            },\n" +
                "            \"accountInfo\": {\n" +
                "                \"accountType\": 1,\n" +
                "                \"identityId\": \"522425199403225137\",\n" +
                "                \"holderName\": \"李毅\"\n" +
                "            }\n" +
                "        }\n" +
                "    }";
        MerchantFeatureBO merchantFeatureBO2 = JSON.parseObject(req, MerchantFeatureBO.class);
        Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>> setSetTuple2 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO2);
    }


}
