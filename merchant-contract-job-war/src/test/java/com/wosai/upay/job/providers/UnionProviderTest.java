package com.wosai.upay.job.providers;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.*;
import com.wosai.upay.merchant.contract.service.NewUnionService;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;


public class UnionProviderTest extends BaseTest {

    @Autowired
    UnionProvider unionProvider;
    @MockBean(name = "newUnionService")
    NewUnionService newUnionService;
    private ContractRule rule;
    private Map context = new HashMap();

    @Before
    public void before() {
        ReflectionTestUtils.setField(unionProvider, "newUnionService", newUnionService);
        rule = new ContractRule();
        ContractChannel contractChannel = new ContractChannel();
        contractChannel.setChannel("channel");
        rule.setIs_update_influ_ptask(true);
        rule.setIs_insert_influ_ptask(true);
        rule.setIs_default(true);
        rule.setContractChannel(contractChannel);
        Map merchant = CollectionUtil.hashMap("ctime", System.currentTimeMillis());
        context.put("merchant", merchant);
    }

    @Test
    public void produceInsertTaskByRule() {
        unionProvider.produceInsertTaskByRule("sn", new ContractEvent(), context, rule);
    }

    @Test
    public void produceUpdateTaskByRule() {
        unionProvider.produceUpdateTaskByRule("sn", new ContractEvent(), context, rule);

    }

    @Test
    public void processInsertTaskByRule() {
        unionProvider.processInsertTaskByRule(new ContractTask(), new ContractChannel(), new ContractSubTask());

    }

    @Test
    public void processUpdateTaskByRule() {
        unionProvider.processUpdateTaskByRule(new ContractTask(), new ContractChannel(), new ContractSubTask());
    }
}
