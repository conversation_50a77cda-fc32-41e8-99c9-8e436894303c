package com.wosai.upay.job.biz;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import static org.junit.Assert.*;

public class AopBizTest extends BaseTest {


    @Autowired
    private AopBiz aopBiz;

    @Value("${bank_biz.devCode}")
    private String bankBizDevCode;

    @Value("${bank_biz.first_change_acquirer_crm_template_code}")
    private String firstChangeAcquirerCrmTemplateCode;

    @Value("${bank_biz.change_acquirer_crm_template_code}")
    private String changeAcquirerCrmTemplateCode;

//    @Test
    public void sendNoticeToCrm() {

        aopBiz.sendNoticeToCrm("0f193bbf-799d-4e7f-a871-84e649aaac6b", bankBizDevCode, changeAcquirerCrmTemplateCode, CollectionUtil.hashMap(
                "merchant_name", "业务开通1208hiIe",
                "merchant_sn", "**************",
                "acquirer", "lkl"
        ));

        aopBiz.sendNoticeToCrm("0f193bbf-799d-4e7f-a871-84e649aaac6b", bankBizDevCode, firstChangeAcquirerCrmTemplateCode, CollectionUtil.hashMap(
                "merchant_name", "业务开通1208hiIe",
                "merchant_sn", "**************",
                "acquirer", "lkl"
        ));

    }
}