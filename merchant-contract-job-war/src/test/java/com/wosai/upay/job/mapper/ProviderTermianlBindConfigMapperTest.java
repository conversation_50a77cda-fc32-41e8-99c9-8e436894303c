package com.wosai.upay.job.mapper;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.model.ProviderTerminalBindConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * @Description: ProviderTermianlBindConfigMapperTest
 * <AUTHOR>
 * @Date 2023/4/11 13:39
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ProviderTermianlBindConfigMapperTest {

    @Autowired
    ProviderTerminalBindConfigMapper mapper;

    @Test
    public void selectByPrimaryKey(){
        String id = "12341234";
        ProviderTerminalBindConfig config = mapper.selectByPrimaryKey(id);
    }

    @Test
    public void updateByPrimaryKey(){
        String id = "12341234";
        ProviderTerminalBindConfig config = mapper.selectByPrimaryKey(id);
        config.setMerchant_sn("1236789");
        mapper.updateByPrimaryKey(config);
    }

    @Test
    public void selectByExample(){
        ProviderTerminalBindConfig config = new ProviderTerminalBindConfig();
        config.setMerchant_sn("1236789");
        List<ProviderTerminalBindConfig> providerTerminalBindConfigs = mapper.selectByExample(config);
    }


}