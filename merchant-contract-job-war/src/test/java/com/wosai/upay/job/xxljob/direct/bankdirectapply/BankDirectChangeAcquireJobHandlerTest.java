package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.job.biz.bankDirect.HxbImportBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.biz.CcbConfigBiz;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.ccbConfig.CcbConfig;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.job.xxljob.model.DirectJobParam;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.google.common.collect.Lists.newArrayList;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BankDirectChangeAcquireJobHandlerTest {

    @InjectMocks
    private BankDirectChangeAcquireJobHandler bankDirectChangeAcquireJobHandler;

    @Mock
    private BankDirectApplyMapper bankDirectApplyMapper;
    @Mock
    private ContractStatusMapper contractStatusMapper;

    @Mock
    private AcquirerService acquirerService;

    @Mock
    private ChatBotUtil chatBotUtil;

    @Mock
    private CcbConfigBiz ccbConfigBiz;

    @Mock
    private DirectStatusBiz directStatusBiz;

    @Mock
    private MerchantService merchantService;
    @Mock
    private HxbImportBiz hxbImportBiz;

    @Mock
    private BankService bankService;

    private DirectJobParam param;

    @Before
    public void setUp() {
        param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "BankDirectChangeAcquireJobHandler";
        String actualLockKey = bankDirectChangeAcquireJobHandler.getLockKey();
        assertEquals("The lock key should match the expected value", expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_ShouldProcessApplications() {
        BankDirectApply apply = new BankDirectApply();
        apply.setId(1L);
        apply.setMerchant_sn("testMerchant");
        apply.setDev_code("testDevCode");
        apply.setForm_body("{}");
        apply.setExtra("{\"acquire\":\"hxb\",\"changeImmediately\":true}");
        apply.setPriority(new Date());

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(newArrayList(apply));
        when(acquirerService.applyChangeAcquirer(anyString(), anyString(), anyBoolean())).thenReturn(true);
        when(contractStatusMapper.selectByMerchantSn(apply.getMerchant_sn())).thenReturn(new ContractStatus());
        bankDirectChangeAcquireJobHandler.execute(param);

        verify(acquirerService, times(1)).applyChangeAcquirer(anyString(), anyString(), anyBoolean());
        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any(BankDirectApply.class));
    }

    @Test
    public void execute_ShouldHandleCcbChangeAcquirerNotAutoChange() {
        BankDirectApply apply = new BankDirectApply();
        apply.setId(1L);
        apply.setMerchant_sn("testMerchant");
        apply.setDev_code("testDevCode");
        apply.setForm_body("{\"bank_pre_id\":\"123\"}");
        apply.setExtra("{\"acquire\":\"ccb\",\"changeImmediately\":true}");

        CcbConfig ccbConfig = new CcbConfig();
        ccbConfig.setIs_auto_change(false);
        when(ccbConfigBiz.getCcbConfigByDistrictCode(any())).thenReturn(ccbConfig);
        when(merchantService.getMerchantByMerchantSn(any())).thenReturn(CollectionUtil.hashMap(Merchant.DISTRICT_CODE, "310001"));
        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(newArrayList(apply));

        bankDirectChangeAcquireJobHandler.execute(param);

        verify(directStatusBiz, times(1)).createOrUpdateDirectStatus(anyString(), anyString(), anyInt(), any());
        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any());
        verify(hxbImportBiz, times(1)).createOrUpdateBizOpenInfo(anyString(), anyString(), anyInt());
    }

    @Test
    public void execute_ShouldHandleCcbChangeAcquirerAutoChange() {
        BankDirectApply apply = new BankDirectApply();
        apply.setId(1L);
        apply.setMerchant_sn("testMerchant");
        apply.setDev_code("testDevCode");
        apply.setForm_body("{\"bank_pre_id\":\"123\"}");
        apply.setExtra("{\"acquire\":\"ccb\",\"changeImmediately\":true}");

        CcbConfig ccbConfig = new CcbConfig();
        ccbConfig.setIs_auto_change(true);
        ccbConfig.setDelay_day(2);
        when(ccbConfigBiz.getCcbConfigByDistrictCode(any())).thenReturn(ccbConfig);
        when(merchantService.getMerchantByMerchantSn(any())).thenReturn(CollectionUtil.hashMap(Merchant.DISTRICT_CODE, "310001"));
        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(newArrayList(apply));

        bankDirectChangeAcquireJobHandler.execute(param);

        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void execute_ShouldHandlePreCheckFailure() {
        BankDirectApply apply = new BankDirectApply();
        apply.setId(1L);
        apply.setMerchant_sn("testMerchant");
        apply.setDev_code("testDevCode");
        apply.setForm_body("{\"bank_pre_id\":\"123\"}");
        apply.setExtra("{\"acquire\":\"hxb\",\"changeImmediately\":true}");

        Map<String, Object> preCheckResult = new HashMap<>();
        preCheckResult.put("allow", false);
        preCheckResult.put("message", "银行卡不存在");

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(newArrayList(apply));
        when(bankService.preCheckReplaceBankAccountForBankDirect(anyString())).thenReturn(preCheckResult);

        bankDirectChangeAcquireJobHandler.execute(param);

        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any(BankDirectApply.class));
    }

    @Test
    public void execute_ShouldHandleException() {
        BankDirectApply apply = new BankDirectApply();
        apply.setId(1L);
        apply.setMerchant_sn("testMerchant");
        apply.setDev_code("testDevCode");
        apply.setForm_body("{}");
        apply.setExtra("{\"acquire\":\"hxb\",\"changeImmediately\":true}");

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(newArrayList(apply));
        when(acquirerService.applyChangeAcquirer(anyString(), anyString(), anyBoolean())).thenThrow(new RuntimeException("Test Exception"));
        when(contractStatusMapper.selectByMerchantSn(apply.getMerchant_sn())).thenReturn(new ContractStatus());
        bankDirectChangeAcquireJobHandler.execute(param);

        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
