package com.wosai.upay.job.mapper;

import com.wosai.upay.job.avro.AcquirerChange;
import com.wosai.upay.job.biz.PendingTasksBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.AuthSpTask;
import com.wosai.upay.job.model.DO.PendingTasks;
import com.wosai.upay.job.service.ContractTaskService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/5/31 7:07 下午
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class PendingTasksMapperTest {

    @Autowired
    PendingTasksMapper mapper;

    @Autowired
    PendingTasksBiz pendingTasksBiz;

    @Test
    public void selectKey() {
        PendingTasks pendingTasks = mapper.selectByPrimaryKey(80634L);
        System.out.println(pendingTasks);
    }

    @Autowired
    ContractTaskService contractTaskService;

    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    CommonEventHandler commonEventHandler;

    @Autowired
    ContractEventMapper contractEventMapper;

    @Test
    public void processEvent() throws Exception {
        ContractEvent event = contractEventMapper.selectByPrimaryKey(1111111111322877L);
        commonEventHandler.handle(event);
    }

    @Autowired
    ComposeAcquirerBiz composeAcquirerBiz;

    @Autowired
    AuthSpTaskMapper authSpTaskMapper;

    @Test
    public void insertTask() {
//        AuthSpTask task = AuthSpTask.builder().merchant_sn("sn").level("A").template_id("tem").build();
//        authSpTaskMapper.insertTask(task);
    }

    @Test
    public void selectBySn() {
        AuthSpTask task1 = authSpTaskMapper.selectBySn("sn");
        AuthSpTask task2 = authSpTaskMapper.selectBySn("sn1");
        System.out.println(task1);
        System.out.println(task2);
    }

    @Autowired
    KafkaTemplate kafkaTemplate;

    @Test
    public void m1(){
        AcquirerChange acquirerChange = new AcquirerChange("change.getMerchant_sn()112", "change.getMerchant_id()", "change.getSource_acquirer()", "change.getTarget_acquirer()", "getBankChannelFlag(change.getTarget_acquirer())");
        kafkaTemplate.send("events.merchant-contract-job.acquirer-change", acquirerChange);
    }


}