package com.wosai.upay.job.biz;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.acquirer.IAcquirerBiz;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ComposeAcquirerBizTest extends BaseTest {

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;

    @Test
    public void getAcquirerBizTest() {
        IAcquirerBiz ii = composeAcquirerBiz.getAcquirerBiz("bcs");
        System.out.println("ok");
    }

}
