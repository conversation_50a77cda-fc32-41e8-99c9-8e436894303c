package com.wosai.upay.job.helper;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class MerchantNameHelperTest {
    @Autowired
    private MerchantNameHelper merchantNameHelper;

    @Test
    public void testGetMerchantName_MicroBusiness_ReturnsBankHolder() {
        Assertions.assertEquals("宿城区驰乔百货店（个体工商户）", merchantNameHelper.getMerchantName("**************"));
        Assertions.assertEquals("商户_入网自动化小微", merchantNameHelper.getMerchantName("**************"));
        Assertions.assertEquals("山东优益药业有限公司", merchantNameHelper.getMerchantName("**************"));
    }
}