package com.wosai.upay.job.refactor.Integration.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.*;
import com.shouqianba.cua.utils.thread.CollectionWorker;
import com.shouqianba.service.ContractRelatedMappingConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.acquirer.ChangeToLklV3Biz;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.biz.acquirer.fuyou.FuYouUniqueAbility;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.LklV3AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.model.LklV3ContractResultDTO;
import com.wosai.upay.job.refactor.mapper.MerchantProviderParamsDynamicMapper;
import com.wosai.upay.job.refactor.model.bo.AcquirerInfoBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.service.rpc.risk.RiskMerchantBusinessLicenseAuditService;
import com.wosai.upay.job.refactor.service.rpc.risk.req.RiskEntryResult;
import com.wosai.upay.job.service.T9Service;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 收单机构测试
 *
 * <AUTHOR>
 */
@Slf4j
public class AcquirerTest extends BaseTest {

    @Resource
    private AcquirerFacade acquirerFacade;


    @Resource
    private MerchantProviderParamsDynamicMapper merchantProviderParamsDynamicMapper;

    private Set<String> listMerchantSns(String provider, Integer limit) {
        LambdaQueryWrapper<MerchantProviderParamsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(MerchantProviderParamsDO::getMerchantSn)
                .eq(MerchantProviderParamsDO::getProvider, provider)
                .eq(MerchantProviderParamsDO::getDeleted, DeleteStatusEnum.NO_DELETED.getValue())
                .eq(MerchantProviderParamsDO::getPayway, PaywayEnum.ACQUIRER.getValue())
                .orderByDesc(MerchantProviderParamsDO::getCtime)
                .last("limit " + limit);
        return merchantProviderParamsDynamicMapper.selectList(wrapper).stream().map(MerchantProviderParamsDO::getMerchantSn).collect(Collectors.toSet());
    }

    @Test
    public void testGetSharedAbilityAcquirer() {
        AcquirerSharedAbility strategy = acquirerFacade.getSharedAbilityByAcquirerEnum(AcquirerTypeEnum.FU_YOU);
        assertThat(strategy.getClass().getSimpleName()).isEqualTo("FuYouAcquirerFacade");
        FuYouUniqueAbility uniqueAbilityAcquirer = acquirerFacade.getUniqueAbilityAcquirer(AcquirerTypeEnum.FU_YOU, FuYouUniqueAbility.class);
        assertThat(uniqueAbilityAcquirer).isNotNull();
        assertThat(uniqueAbilityAcquirer.getClass().getSimpleName()).isEqualTo("FuYouAcquirerFacade");

    }

    @Test
    public void batchTestLklV3BankCardConsistentWithSqb() {
        CollectionWorker.of(listMerchantSns("1032", 1000))
                .parallel()
                .forEach(merchantSn -> {
                    AcquirerSharedAbility strategy = acquirerFacade.getSharedAbilityByAcquirerEnum(AcquirerTypeEnum.LKL_V3);
                    boolean consistentWithSqb = strategy.isBankCardConsistentWithSqb(merchantSn);
                    if (!consistentWithSqb) {
                        log.error("商户银行卡不一致:{}", merchantSn);
                    }
                    assertThat(consistentWithSqb).isTrue();
                });
    }

    @Test
    public void batchTestLklBankCardConsistentWithSqb() {
        CollectionWorker.of(listMerchantSns("1002", 10))
                .parallel()
                .forEach(merchantSn -> {
                    AcquirerSharedAbility strategy = acquirerFacade.getSharedAbilityByAcquirerEnum(AcquirerTypeEnum.LKL);
                    boolean consistentWithSqb = strategy.isBankCardConsistentWithSqb(merchantSn);
                });
    }

    @Test
    public void batchTestFuYouBankCardConsistentWithSqb() {
        CollectionWorker.of(listMerchantSns("1038", 1000))
                .parallel()
                .forEach(merchantSn -> {
                    AcquirerSharedAbility strategy = acquirerFacade.getSharedAbilityByAcquirerEnum(AcquirerTypeEnum.FU_YOU);
                    boolean consistentWithSqb = strategy.isBankCardConsistentWithSqb(merchantSn);
                });
    }

    @Test
    public void batchTestHaiKeBankCardConsistentWithSqb() {
        CollectionWorker.of(listMerchantSns("1037", 100))
                .parallel()
                .forEach(merchantSn -> {
                    AcquirerSharedAbility strategy = acquirerFacade.getSharedAbilityByAcquirerEnum(AcquirerTypeEnum.HAI_KE);
                    boolean consistentWithSqb = strategy.isBankCardConsistentWithSqb(merchantSn);
                });
    }

    @Test
    public void batchTestTongLianBankCardConsistentWithSqb() {
        CollectionWorker.of(listMerchantSns("1020", 100))
                .parallel()
                .forEach(merchantSn -> {
                    AcquirerSharedAbility strategy = acquirerFacade.getSharedAbilityByAcquirerEnum(AcquirerTypeEnum.TONG_LIAN);
                    boolean consistentWithSqb = strategy.isBankCardConsistentWithSqb(merchantSn);
                });
    }

    @Test
    public void batchTestTongV2LianBankCardConsistentWithSqb() {
        CollectionWorker.of(listMerchantSns("1035", 100))
                .parallel()
                .forEach(merchantSn -> {
                    AcquirerSharedAbility strategy = acquirerFacade.getSharedAbilityByAcquirerEnum(AcquirerTypeEnum.TONG_LIAN_V2);
                    boolean consistentWithSqb = strategy.isBankCardConsistentWithSqb(merchantSn);
                });
    }

    @Resource
    private SubBizParamsBiz subBizParamsBiz;

    @Resource
    private ContractRelatedMappingConfigService contractRelatedMappingConfigService;

    @Test
    public void testConsistent() {
        ArrayList<String> providers = Lists.newArrayList("1032", "1002", "1038", "1037", "1020", "1035", "1028");
        for (String provider : providers) {
            listMerchantSns(provider, 100).forEach(merchantSn -> {
                        boolean consistence = subBizParamsBiz.checkBankCardConsistence(merchantSn, contractRelatedMappingConfigService.getAcquirerByProvider(provider));
                        log.info("consistence: {}", consistence ? "一致" : "不一致");
                    });
        }
    }


    @Test
    public void testGetAcquirerInfo() {
        AcquirerSharedAbility strategy = acquirerFacade.getSharedAbilityByAcquirerEnum(AcquirerTypeEnum.LKL_V3);
        for (int i = 0; i < 3; i++) {
            AcquirerInfoBO acquirerInfo = strategy.getAcquirerInfo();
            assertThat(acquirerInfo).isNotNull();
            assertThat(acquirerInfo.getName()).isEqualTo("拉卡拉V3");
            assertThat(acquirerInfo.getOrgType()).isEqualTo(AcquirerOrgTypeEnum.THIRD_PARTY);
            assertThat(acquirerInfo.getTradeType()).isEqualTo(AcquirerTradeTypeEnum.INDIRECT);
            assertThat(acquirerInfo.getClearType()).isEqualTo(ClearTypeEnum.INDIRECT);
            assertThat(acquirerInfo.getSupportedProviders()).isNotEmpty();
            for (String supportedProvider : acquirerInfo.getSupportedProviders()) {
                assertThat(supportedProvider).isIn("1016", "1014", "1013", "1017", "1033", "1034");
            }
            assertThat(acquirerInfo.getProvider()).isEqualTo("1032");
        }
    }

    @Test
    public void testGetDefaultRuleGroupId() {
        AcquirerSharedAbility strategy = acquirerFacade.getSharedAbilityByAcquirerEnum(AcquirerTypeEnum.LKL_V3);
        Optional<String> defaultContractRuleGroupId = strategy.getDefaultContractRuleGroupId();
        assertThat(defaultContractRuleGroupId).isNotEmpty();
        assertThat(defaultContractRuleGroupId.get()).isEqualTo("lklorg");
    }

    @Resource
    private ChangeToLklV3Biz changeToLklV3Biz;

    @Test
    public void testGetLastedThirdPartyToBankSuccessAcquirerChange() {
        String merchantSn = "**************";
        McAcquirerChange change = changeToLklV3Biz.getLastedThirdPartyToBankSuccessAcquirerChange(merchantSn);
        assertThat(change).isNotNull();
        assertThat(change.getSource_acquirer()).isEqualTo(642892);
    }

    @Resource
    private T9Service t9Service;

    @Test
    public void testIsT9OpenSuccessAndBindTerminal() {
        String merchantSn = "**************";
        boolean success = t9Service.isT9OpenSuccessAndBindTerminal(merchantSn);
        assertThat(success).isTrue();
    }

    @Resource
    private LklV3AcquirerFacade lklV3AcquirerFacade;

    @Resource
    private RiskMerchantBusinessLicenseAuditService riskMerchantBusinessLicenseAuditService;

    @Test
    public void testQueryContractResult() {
        RiskEntryResult entryResult = new RiskEntryResult();
        entryResult.setMerchantSn("**************");
        entryResult.setSuccess(false);
        entryResult.setReason("进件失败");
        riskMerchantBusinessLicenseAuditService.processEntryResult(entryResult);
        String contractId = "***************";
        LklV3ContractResultDTO lklV3ContractResultDTO = lklV3AcquirerFacade.queryContractResult(contractId);
        assertThat(lklV3ContractResultDTO).isNotNull();
    }

    @Test
    public void testSupportSpecialIndustry() {
        AcquirerSharedAbility strategy = acquirerFacade.getSharedAbilityByAcquirerEnum(AcquirerTypeEnum.UMB);

        AcquirerInfoBO acquirerInfoUmb = strategy.getAcquirerInfo();
        assertThat(acquirerInfoUmb).isNotNull();
        assertThat(acquirerInfoUmb.isSupportSpecialIndustry()).isEqualTo(true);

        AcquirerSharedAbility strategyLzb = acquirerFacade.getSharedAbilityByAcquirerEnum(AcquirerTypeEnum.LZB);

        AcquirerInfoBO acquirerInfoLzb = strategyLzb.getAcquirerInfo();
        assertThat(acquirerInfoLzb).isNotNull();
        assertThat(acquirerInfoLzb.isSupportSpecialIndustry()).isEqualTo(false);
    }

}
