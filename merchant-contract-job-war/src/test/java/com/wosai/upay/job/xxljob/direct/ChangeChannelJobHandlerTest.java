package com.wosai.upay.job.xxljob.direct;

import com.wosai.common.exception.CommonException;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.ChangeTradeParamsBiz;
import com.wosai.upay.job.biz.JdBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.PayWayConfigChangeMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.PayWayConfigChange;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.ShutdownSignal;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ChangeChannelJobHandlerTest {

    @InjectMocks
    private ChangeChannelJobHandler changeChannelJobHandler;

    @Mock
    private PayWayConfigChangeMapper payWayConfigChangeMapper;

    @Mock
    private MerchantService merchantService;

    @Mock
    private ChangeTradeParamsBiz tradeParamsBiz;
    @Mock
    private JdBiz jdBiz;
    @Mock
    private SubBizParamsBiz subBizParamsBiz;
    @Mock
    private ChatBotUtil chatBotUtil;
    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    private DirectJobParam param;

    @Before
    public void setUp() {
        param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "ChangeChannelJobHandler";
        String actualLockKey = changeChannelJobHandler.getLockKey();

        assertEquals("The lock key should match the expected value", expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_NoRecordsFound_LogsInfoAndReturns() {
        when(payWayConfigChangeMapper.selectByUpdateAt(anyString(), anyString(), anyInt())).thenReturn(Arrays.asList());

        changeChannelJobHandler.execute(param);

        verify(payWayConfigChangeMapper, times(1)).selectByUpdateAt(anyString(), anyString(), anyInt());
    }

    @Test
    public void execute_RecordsFound_ProcessesEachRecordNoMerchant() {
        List<PayWayConfigChange> payWayConfigChanges = Arrays.asList(new PayWayConfigChange().setPayway(3));
        when(payWayConfigChangeMapper.selectByUpdateAt(anyString(), anyString(), anyInt())).thenReturn(payWayConfigChanges);

        changeChannelJobHandler.execute(param);

        verify(payWayConfigChangeMapper, times(1)).selectByUpdateAt(anyString(), anyString(), anyInt());
        verify(payWayConfigChangeMapper, times(1)).updateByPrimaryKeySelective(any());
    }
    @Test
    public void execute_RecordsFound_ProcessesEachRecordChangeSuccess() {
        PayWayConfigChange payWayConfigChange = new PayWayConfigChange().setPayway(3).setMerchant_sn("merchantSn").setBody("{\"merchantProviderParamsId\":\"merchantProviderParamsId\"}");
        List<PayWayConfigChange> payWayConfigChanges = Arrays.asList(payWayConfigChange);
        when(payWayConfigChangeMapper.selectByUpdateAt(anyString(), anyString(), anyInt())).thenReturn(payWayConfigChanges);
        when(merchantService.getMerchantBySn(payWayConfigChange.getMerchant_sn())).thenReturn(CollectionUtil.hashMap("id", "merchantId", "sn", "merchantSn"));
        when(tradeParamsBiz.changeTradeParams("merchantProviderParamsId", null, false, null)).thenReturn(true);

        changeChannelJobHandler.execute(param);

        verify(payWayConfigChangeMapper, times(1)).selectByUpdateAt(anyString(), anyString(), anyInt());
        verify(payWayConfigChangeMapper, times(1)).updateByPrimaryKeySelective(any());
        verify(jdBiz, times(1)).setJdComb(anyString());
    }

    @Test
    public void execute_RecordsFound_ProcessesEachRecordNoParamsId() {
        PayWayConfigChange payWayConfigChange = new PayWayConfigChange().setPayway(3).setMerchant_sn("merchantSn").setBody("{\"tradeParam\":{\"weixin_merchant_id\":\"11111\"}}");
        List<PayWayConfigChange> payWayConfigChanges = Arrays.asList(payWayConfigChange);
        when(payWayConfigChangeMapper.selectByUpdateAt(anyString(), anyString(), anyInt())).thenReturn(payWayConfigChanges);
        when(merchantService.getMerchantBySn(payWayConfigChange.getMerchant_sn())).thenReturn(CollectionUtil.hashMap("id", "merchantId", "sn", "merchantSn"));
        when(merchantProviderParamsMapper.getByPayMerchantId("11111")).thenReturn(new MerchantProviderParams());

        changeChannelJobHandler.execute(param);

        verify(payWayConfigChangeMapper, times(1)).selectByUpdateAt(anyString(), anyString(), anyInt());
        verify(payWayConfigChangeMapper, times(1)).updateByPrimaryKeySelective(any());
        verify(chatBotUtil, times(0)).sendMessageToContractWarnChatBot(anyString());
    }

    @Test
    public void execute_RecordsFound_ProcessesEachRecordNoParamsIdAndNoWeixinMerchantId() {
        PayWayConfigChange payWayConfigChange = new PayWayConfigChange().setPayway(3).setMerchant_sn("merchantSn").setBody("{\"tradeParam\":{\"weixin_merchant_id\":\"\"}}");
        List<PayWayConfigChange> payWayConfigChanges = Arrays.asList(payWayConfigChange);
        when(payWayConfigChangeMapper.selectByUpdateAt(anyString(), anyString(), anyInt())).thenReturn(payWayConfigChanges);
        when(merchantService.getMerchantBySn(payWayConfigChange.getMerchant_sn())).thenReturn(CollectionUtil.hashMap("id", "merchantId", "sn", "merchantSn"));

        changeChannelJobHandler.execute(param);

        verify(payWayConfigChangeMapper, times(1)).selectByUpdateAt(anyString(), anyString(), anyInt());
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }

    @Test
    public void execute_RecordsFound_ProcessesEachRecordChangeThrowCommonException() {
        PayWayConfigChange payWayConfigChange = new PayWayConfigChange().setPayway(3).setMerchant_sn("merchantSn").setBody("{\"merchantProviderParamsId\":\"merchantProviderParamsId\"}");
        List<PayWayConfigChange> payWayConfigChanges = Arrays.asList(payWayConfigChange);
        when(payWayConfigChangeMapper.selectByUpdateAt(anyString(), anyString(), anyInt())).thenReturn(payWayConfigChanges);
        when(merchantService.getMerchantBySn(payWayConfigChange.getMerchant_sn())).thenReturn(CollectionUtil.hashMap("id", "merchantId", "sn", "merchantSn"));
        when(tradeParamsBiz.changeTradeParams("merchantProviderParamsId", null, false, null)).thenThrow(new CommonInvalidParameterException("test"));

        changeChannelJobHandler.execute(param);

        verify(payWayConfigChangeMapper, times(1)).selectByUpdateAt(anyString(), anyString(), anyInt());
        verify(payWayConfigChangeMapper, times(1)).updateByPrimaryKeySelective(any());
        verify(jdBiz, times(0)).setJdComb(anyString());
    }

    @Test
    public void execute_RecordsFound_ProcessesEachRecordChangeThrowRunTimeException() {
        PayWayConfigChange payWayConfigChange = new PayWayConfigChange().setPayway(3).setMerchant_sn("merchantSn").setBody("{\"merchantProviderParamsId\":\"merchantProviderParamsId\"}");
        List<PayWayConfigChange> payWayConfigChanges = Arrays.asList(payWayConfigChange);
        when(payWayConfigChangeMapper.selectByUpdateAt(anyString(), anyString(), anyInt())).thenReturn(payWayConfigChanges);
        when(merchantService.getMerchantBySn(payWayConfigChange.getMerchant_sn())).thenReturn(CollectionUtil.hashMap("id", "merchantId", "sn", "merchantSn"));
        when(tradeParamsBiz.changeTradeParams("merchantProviderParamsId", null, false, null)).thenThrow(new RuntimeException("test"));

        changeChannelJobHandler.execute(param);

        verify(payWayConfigChangeMapper, times(1)).selectByUpdateAt(anyString(), anyString(), anyInt());
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
        verify(jdBiz, times(0)).setJdComb(anyString());
    }

}
