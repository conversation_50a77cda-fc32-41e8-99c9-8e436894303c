package com.wosai.upay.job.xxljob.batch.contracttask;

import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ProviderUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ContractTaskNoInfluJobHandlerTest {

    @InjectMocks
    private ContractTaskNoInfluJobHandler contractTaskNoInfluJobHandler;

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private SubTaskHandlerContext subTaskHandlerContext;

    @Mock
    private ChatBotUtil chatBotUtil;

    private BatchJobParam batchJobParamA;
    private ContractSubTask contractSubTaskB;

    @Before
    public void setUp() {
        // Setup for batchJobParam from file A
        batchJobParamA = new BatchJobParam();
        batchJobParamA.setBatchSize(10);
        batchJobParamA.setQueryTime(1000L);

        // Setup for contractSubTask from file B
        contractSubTaskB = new ContractSubTask();
        contractSubTaskB.setId(1L);
        contractSubTaskB.setP_task_id(2L);
        contractSubTaskB.setMerchant_sn("12345");
        contractSubTaskB.setTask_type(5);
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsContractSubTaskList() {
        // 准备
        ContractSubTask task1 = new ContractSubTask();
        task1.setId(1L);
        ContractSubTask task2 = new ContractSubTask();
        task2.setId(2L);
        List<ContractSubTask> expectedTasks = Arrays.asList(task1, task2);

        when(contractSubTaskMapper.getNoInfluenceSubTask(anyInt(), anyString())).thenReturn(expectedTasks);

        // 执行
        List<ContractSubTask> actualTasks = contractTaskNoInfluJobHandler.queryTaskItems(batchJobParamA);

        // 验证
        assertEquals(expectedTasks, actualTasks);
        Mockito.verify(contractSubTaskMapper).getNoInfluenceSubTask(any(), anyString());
    }

    @Test
    public void getLockKey_ContractSubTaskWithZeroId_ReturnsLockKeyWithZeroId() {
        contractSubTaskB.setId(0L).setMerchant_sn("merchantSn").setTask_type(1);
        String expectedLockKey = "ContractTaskNoInfluJobHandler:merchantSn1";
        String actualLockKey = contractTaskNoInfluJobHandler.getLockKey(contractSubTaskB);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void doHandleSingleData_StatusSuccessOrFail_Returns() {
        ContractSubTask subTaskLast = new ContractSubTask();
        subTaskLast.setStatus(TaskStatus.SUCCESS.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(subTaskLast);

        contractTaskNoInfluJobHandler.doHandleSingleData(contractSubTaskB);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getId());
        verifyNoMoreInteractions(contractSubTaskMapper, contractTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_ChannelAndContractId_Returns() {
        ContractSubTask subTaskLast = new ContractSubTask();
        subTaskLast.setChannel(ProviderUtil.LKL_PROVIDER_CHANNEL);
        subTaskLast.setContract_id("123");
        subTaskLast.setStatus(TaskStatus.PENDING.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(subTaskLast);

        contractTaskNoInfluJobHandler.doHandleSingleData(contractSubTaskB);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getId());
        verifyNoMoreInteractions(contractSubTaskMapper, contractTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_FuyouUnionPay_UpdateStatus() {
        ContractSubTask subTaskLast = new ContractSubTask().setStatus(TaskStatus.PENDING.getVal()).setContract_id("contract_id")
                .setTask_type(ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue()).setPayway(PaywayEnum.UNIONPAY.getValue());
        subTaskLast.setChannel(ProviderUtil.FUYOU_CHANNEL);
        subTaskLast.setTask_type(5);

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(subTaskLast);

        contractTaskNoInfluJobHandler.doHandleSingleData(contractSubTaskB);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getId());
        verify(contractSubTaskMapper, times(1)).updateByPrimaryKey(any(ContractSubTask.class));
        verifyNoMoreInteractions(contractSubTaskMapper, contractTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_RetryExceeded_UpdateStatusToFail() {
        ContractSubTask subTaskLast = new ContractSubTask();
        subTaskLast.setRetry(11);
        subTaskLast.setStatus(TaskStatus.PENDING.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(subTaskLast);

        contractTaskNoInfluJobHandler.doHandleSingleData(contractSubTaskB);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getId());
        verify(contractSubTaskMapper, times(1)).updateByPrimaryKey(any(ContractSubTask.class));
        verifyNoMoreInteractions(contractSubTaskMapper, contractTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_Exception_LogsAndSendsWarning() throws Exception {
        ContractSubTask subTaskLast = new ContractSubTask().setStatus(TaskStatus.PROGRESSING.getVal()).setRetry(0);
        ContractTask task = new ContractTask();

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(subTaskLast);
        when(contractTaskMapper.selectByPrimaryKey(contractSubTaskB.getP_task_id())).thenReturn(task);
        doThrow(new RuntimeException("Test exception")).when(subTaskHandlerContext).handle(task, subTaskLast);

        contractTaskNoInfluJobHandler.doHandleSingleData(contractSubTaskB);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getId());
        verify(contractTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getP_task_id());
        verify(subTaskHandlerContext, times(1)).handle(task, subTaskLast);
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
