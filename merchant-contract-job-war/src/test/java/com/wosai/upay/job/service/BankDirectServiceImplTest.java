package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.CcbConstant;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.merchant.contract.exception.ContractException;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class BankDirectServiceImplTest extends H2DbBaseTest {

    @InjectMocks
    private BankDirectServiceImpl bankDirectService;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Mock
    private MerchantService merchantService;

    @Test
    public void getSignParamForCcb() {
        ReflectionTestUtils.setField(bankDirectService, "contractSubTaskMapper", contractSubTaskMapper);
        String merchantId = "id_getSignParamForCcb";
        String merchantSn = "sn_getSignParamForCcb";

        Map request = new HashMap<>(1);
        // 商户id为空
        ContractException contractException = assertThrows(ContractException.class, () -> bankDirectService.getSignParamForCcb(request));
        assertEquals(contractException.getMessage(), "商户id不能为空");

        request.put("merchant_id", merchantId);
        // 商户不存在
        contractException = assertThrows(ContractException.class, () -> bankDirectService.getSignParamForCcb(request));
        assertEquals(contractException.getMessage(), "商户不存在");

        Mockito.doReturn(CollectionUtil.hashMap(Merchant.SN, merchantSn)).when(merchantService).getMerchantByMerchantId(merchantId);
        // 建行进件子任务不存在
        contractException = assertThrows(ContractException.class, () -> bankDirectService.getSignParamForCcb(request));
        assertEquals(contractException.getMessage(), "建行进件子任务不存在");

        // 插入一条进件的子任务
        ContractSubTask contractSubTask = new ContractSubTask().setMerchant_sn(merchantSn)
                .setTask_type(5).setContract_rule("ccb").setStatus(TaskStatus.FAIL.getVal()).setP_task_id(123321L);
        contractSubTaskMapper.insert(contractSubTask);
        // 建行申请已经失败
        contractException = assertThrows(ContractException.class, () -> bankDirectService.getSignParamForCcb(request));
        assertEquals(contractException.getMessage(), "建行申请已失败，请重新提交");

        // 建行申请成功
        contractSubTaskMapper.updateByPrimaryKey(new ContractSubTask().setId(contractSubTask.getId()).setStatus(TaskStatus.SUCCESS.getVal()));
        contractException = assertThrows(ContractException.class, () -> bankDirectService.getSignParamForCcb(request));
        assertEquals(contractException.getMessage(), "建行申请已成功");

        // 有数据
        contractSubTaskMapper.updateByPrimaryKey(new ContractSubTask().setId(contractSubTask.getId()).setStatus(TaskStatus.PROGRESSING.getVal()).setResult(CcbConstant.WAIT_FOR_SIGN)
                .setResponse_body("{\n" +
                        "    \"responseParam\":{\n" +
                        "        \"ERRORMSG\":\"\",\n" +
                        "        \"dataInfo\":{\n" +
                        "            \"param\":{\n" +
                        "                \"SYSID\":\"B0004\",\n" +
                        "                \"ccbParam\":\"3D\"\n" +
                        "            },\n" +
                        "            \"url\":\"https://124.127.94.45:8080/NCCB/MMER00GatePTReqServlet\"\n" +
                        "        },\n" +
                        "        \"ERRORCODE\":\"\",\n" +
                        "        \"TRACEID\":\"1010025181635330311000879\",\n" +
                        "        \"RESULT\":\"Y\",\n" +
                        "        \"TXCODE\":\"5W4007\"\n" +
                        "    },\n" +
                        "    \"tradeParam\":{}\n" +
                        "}"));
        Map<String, String> result = bankDirectService.getSignParamForCcb(request);
        assertTrue(result.containsKey("url"));

    }
}