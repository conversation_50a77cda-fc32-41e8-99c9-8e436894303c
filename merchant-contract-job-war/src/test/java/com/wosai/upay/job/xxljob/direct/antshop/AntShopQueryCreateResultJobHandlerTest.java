package com.wosai.upay.job.xxljob.direct.antshop;

import com.wosai.upay.job.Constants.AntShopTaskConstant;
import com.wosai.upay.job.biz.AntShopBiz;
import com.wosai.upay.job.model.DO.AntShopTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AntShopQueryCreateResultJobHandlerTest {

    @InjectMocks
    private AntShopQueryCreateResultJobHandler antShopQueryCreateResultJobHandler;

    @Mock
    private AntShopBiz antShopBiz;

    @Mock
    private ChatBotUtil chatBotUtil;

    private DirectJobParam param;

    @Before
    public void setUp() {
        param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "AntShopQueryCreateResultJobHandler";
        String actualLockKey = antShopQueryCreateResultJobHandler.getLockKey();
        assertEquals("The lock key should match the expected value", expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_NormalExecution_ShouldHandleTasks() {
        AntShopTask task = new AntShopTask();
        task.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);
        task.setMerchant_sn("12345");
        List<AntShopTask> tasks = Collections.singletonList(task);

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong())).thenReturn(tasks);
        doNothing().when(antShopBiz).handleAntMerchantExpandShopQuery(any(AntShopTask.class));

        antShopQueryCreateResultJobHandler.execute(param);

        verify(antShopBiz, times(1)).handleAntMerchantExpandShopQuery(task);
    }

    @Test
    public void execute_ExceptionHandling_ShouldUpdateTaskAndSendWarning() {
        AntShopTask task = new AntShopTask();
        task.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);
        task.setMerchant_sn("12345");
        List<AntShopTask> tasks = Collections.singletonList(task);

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong())).thenReturn(tasks);
        doThrow(new RuntimeException("Test exception")).when(antShopBiz).handleAntMerchantExpandShopQuery(any(AntShopTask.class));

        antShopQueryCreateResultJobHandler.execute(param);

        verify(antShopBiz, times(1)).updateAntShopTask(task);
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
