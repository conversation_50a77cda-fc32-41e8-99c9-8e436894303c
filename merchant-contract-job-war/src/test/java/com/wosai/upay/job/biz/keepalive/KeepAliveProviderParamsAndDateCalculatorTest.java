package com.wosai.upay.job.biz.keepalive;

import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.KeepAliveApolloConfig;
import com.wosai.upay.job.adapter.apollo.model.KeepAliveConfigModel;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.SubMchIdLastTradeTimeDAO;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.SubMchIdLastTradeTimeDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class KeepAliveProviderParamsAndDateCalculatorTest {

    @Mock
    private ContractStatusDAO contractStatusDAO;

    @Mock
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Mock
    private SubMchIdLastTradeTimeDAO subMchIdLastTradeTimeDAO;

    @Mock
    private McProviderDAO mcProviderDAO;

    @Mock
    private KeepAliveApolloConfig keepAliveApolloConfig;

    @InjectMocks
    private KeepAliveProviderParamsAndDateCalculator calculator;

    private static final String TEST_MERCHANT_SN = "M123456";

    @Test
    public void testCalculateKeepAliveParamsAndDateWithMaxLimit_ContractStatusNotExists() {
        // Given
        when(contractStatusDAO.getByMerchantSn(TEST_MERCHANT_SN)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ContractBizException.class, () -> {
            calculator.calculateKeepAliveParamsAndDateWithMaxLimit(TEST_MERCHANT_SN);
        });
    }

    @Test
    public void testCalculateKeepAliveParamsAndDateWithMaxLimit_NoKeepAliveParams() {
        // Given
        ContractStatusDO contractStatus = new ContractStatusDO();
        contractStatus.setAcquirer("ALIPAY");
        when(contractStatusDAO.getByMerchantSn(TEST_MERCHANT_SN)).thenReturn(Optional.of(contractStatus));

        KeepAliveConfigModel configModel = createDefaultConfigModel();
        when(keepAliveApolloConfig.getKeepAliveConfigModel()).thenReturn(configModel);

        when(merchantProviderParamsDAO.getKeepAliveParamsSorted(anyString(), anyList(), anyList()))
                .thenReturn(Collections.emptyList());

        // When
        KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult result =
                calculator.calculateKeepAliveParamsAndDateWithMaxLimit(TEST_MERCHANT_SN);

        // Then
        assertTrue(result.getParamsAndDates().isEmpty());
        assertTrue(result.getParamsAndMaxDates().isEmpty());
    }

    @Test
    public void testCalculateKeepAliveParamsAndDateWithMaxLimit_NormalScenario_UseLastTradeTime() {
        // Given
        ContractStatusDO contractStatus = new ContractStatusDO();
        contractStatus.setAcquirer("ALIPAY");
        when(contractStatusDAO.getByMerchantSn(TEST_MERCHANT_SN)).thenReturn(Optional.of(contractStatus));

        KeepAliveConfigModel configModel = createDefaultConfigModel();
        when(keepAliveApolloConfig.getKeepAliveConfigModel()).thenReturn(configModel);

        MerchantProviderParamsDO param = createMerchantProviderParams(1L, 1001, 2, System.currentTimeMillis() - 86400000L); // 1天前创建
        List<MerchantProviderParamsDO> params = Arrays.asList(param);
        when(merchantProviderParamsDAO.getKeepAliveParamsSorted(anyString(), anyList(), anyList()))
                .thenReturn(params);

        // 模拟有最后交易时间（比创建时间更近）
        long lastTradeTime = System.currentTimeMillis() - 3600000L; // 1小时前
        SubMchIdLastTradeTimeDO lastTradeTimeDO = new SubMchIdLastTradeTimeDO();
        lastTradeTimeDO.setTradeTime(lastTradeTime);
        when(subMchIdLastTradeTimeDAO.selectByMerchantSnAndSubMchId(param.getMerchantSn(), param.getPayMerchantId())).thenReturn(Optional.of(lastTradeTimeDO));

        // When
        KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult result = calculator.calculateKeepAliveParamsAndDateWithMaxLimit(TEST_MERCHANT_SN);

        // Then
        assertFalse(result.getParamsAndDates().isEmpty());
        assertFalse(result.getParamsAndMaxDates().isEmpty());

        LocalDate baseDate = LocalDateTime.ofInstant(new Date(lastTradeTime).toInstant(), java.time.ZoneId.systemDefault()).toLocalDate();
        assertEquals(baseDate.plusDays(30), result.getParamsAndDates().get(param)); // period delay day
        assertEquals(baseDate.plusDays(90), result.getParamsAndMaxDates().get(param)); // max delay day
    }

    @Test
    public void testCalculateKeepAliveParamsAndDateWithMaxLimit_NormalScenario_UseCreateTime() {
        // Given
        ContractStatusDO contractStatus = new ContractStatusDO();
        contractStatus.setAcquirer("ALIPAY");
        when(contractStatusDAO.getByMerchantSn(TEST_MERCHANT_SN)).thenReturn(Optional.of(contractStatus));

        KeepAliveConfigModel configModel = createDefaultConfigModel();
        when(keepAliveApolloConfig.getKeepAliveConfigModel()).thenReturn(configModel);

        long createTime = System.currentTimeMillis() - 86400000L; // 1天前
        MerchantProviderParamsDO param = createMerchantProviderParams(1L, 1001, 2, createTime);
        List<MerchantProviderParamsDO> params = Arrays.asList(param);
        when(merchantProviderParamsDAO.getKeepAliveParamsSorted(anyString(), anyList(), anyList()))
                .thenReturn(params);

        // 模拟没有最后交易时间，使用创建时间
        when(subMchIdLastTradeTimeDAO.selectByMerchantSnAndSubMchId(param.getMerchantSn(), param.getPayMerchantId())).thenReturn(Optional.empty());

        // When
        KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult result = calculator.calculateKeepAliveParamsAndDateWithMaxLimit(TEST_MERCHANT_SN);
        // Then
        assertFalse(result.getParamsAndDates().isEmpty());
        assertFalse(result.getParamsAndMaxDates().isEmpty());

        LocalDate baseDate = LocalDateTime.ofInstant(new Date(createTime).toInstant(), java.time.ZoneId.systemDefault()).toLocalDate();
        assertEquals(baseDate.plusDays(30), result.getParamsAndDates().get(param)); // period delay day
        assertEquals(baseDate.plusDays(90), result.getParamsAndMaxDates().get(param)); // max delay day
    }

    @Test
    public void testCalculateKeepAliveParamsAndDateWithMaxLimit_MaxDateLimitReached() {
        // Given
        ContractStatusDO contractStatus = new ContractStatusDO();
        contractStatus.setAcquirer("ALIPAY");
        when(contractStatusDAO.getByMerchantSn(TEST_MERCHANT_SN)).thenReturn(Optional.of(contractStatus));

        KeepAliveConfigModel configModel = createDefaultConfigModel();
        when(keepAliveApolloConfig.getKeepAliveConfigModel()).thenReturn(configModel);

        // 创建很久以前的时间，使得maxDate会早于今天
        long oldTime = System.currentTimeMillis() - 100L * 24 * 3600 * 1000; // 100天前
        MerchantProviderParamsDO param = createMerchantProviderParams(1L, 1001, 2, oldTime);
        List<MerchantProviderParamsDO> params = Arrays.asList(param);
        when(merchantProviderParamsDAO.getKeepAliveParamsSorted(anyString(), anyList(), anyList()))
                .thenReturn(params);

        when(subMchIdLastTradeTimeDAO.selectByMerchantSnAndSubMchId(param.getMerchantSn(), param.getPayMerchantId())).thenReturn(Optional.empty());

        // When
        KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult result = calculator.calculateKeepAliveParamsAndDateWithMaxLimit(TEST_MERCHANT_SN);

        // Then
        assertFalse(result.getParamsAndDates().isEmpty());
        assertFalse(result.getParamsAndMaxDates().isEmpty());

        LocalDate baseDate = LocalDateTime.ofInstant(new Date(oldTime).toInstant(), java.time.ZoneId.systemDefault()).toLocalDate();
        LocalDate maxDate = baseDate.plusDays(90);
        // 由于maxDate早于今天，应该返回maxDate作为保活日期
        assertEquals(maxDate, result.getParamsAndDates().get(param));
        assertEquals(maxDate, result.getParamsAndMaxDates().get(param));
    }

    @Test
    public void testCalculateKeepAliveParamsAndDateWithMaxLimit_DefaultConfigUsed() {
        // Given
        ContractStatusDO contractStatus = new ContractStatusDO();
        contractStatus.setAcquirer("ALIPAY");
        when(contractStatusDAO.getByMerchantSn(TEST_MERCHANT_SN)).thenReturn(Optional.of(contractStatus));

        KeepAliveConfigModel configModel = createDefaultConfigModel();
        // 移除特定provider_payway配置，强制使用默认配置
        configModel.getPeriodDelayDay().clear();
        configModel.getMaxDelayDay().clear();
        configModel.getPeriodDelayDay().put("default", 15);
        configModel.getMaxDelayDay().put("default", 45);

        when(keepAliveApolloConfig.getKeepAliveConfigModel()).thenReturn(configModel);

        MerchantProviderParamsDO param = createMerchantProviderParams(1L, 1001, 2, System.currentTimeMillis());
        List<MerchantProviderParamsDO> params = Arrays.asList(param);
        when(merchantProviderParamsDAO.getKeepAliveParamsSorted(anyString(), anyList(), anyList()))
                .thenReturn(params);

        when(subMchIdLastTradeTimeDAO.selectByMerchantSnAndSubMchId(param.getMerchantSn(), param.getPayMerchantId())).thenReturn(Optional.empty());

        // When
        KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult result = calculator.calculateKeepAliveParamsAndDateWithMaxLimit(TEST_MERCHANT_SN);

        // Then
        assertFalse(result.getParamsAndDates().isEmpty());
        assertFalse(result.getParamsAndMaxDates().isEmpty());

        LocalDate baseDate = LocalDateTime.ofInstant(new Date(param.getCtime()).toInstant(), java.time.ZoneId.systemDefault()).toLocalDate();
        assertEquals(baseDate.plusDays(15), result.getParamsAndDates().get(param)); // default period delay day
        assertEquals(baseDate.plusDays(45), result.getParamsAndMaxDates().get(param)); // default max delay day
    }

    private KeepAliveConfigModel createDefaultConfigModel() {
        KeepAliveConfigModel configModel = new KeepAliveConfigModel();

        List<Integer> keepAliveProviders = Arrays.asList(1001, 1002, 1003);
        configModel.setKeepAliveProviders(keepAliveProviders);

        List<Integer> keepAlivePayways = Arrays.asList(2, 3);
        configModel.setKeepAlivePayways(keepAlivePayways);

        Map<String, Integer> periodDelayDay = new HashMap<>();
        periodDelayDay.put("1001_2", 30);
        periodDelayDay.put("default", 10);
        configModel.setPeriodDelayDay(periodDelayDay);

        Map<String, Integer> maxDelayDay = new HashMap<>();
        maxDelayDay.put("1001_2", 90);
        maxDelayDay.put("default", 60);
        configModel.setMaxDelayDay(maxDelayDay);

        configModel.setMaxKeepAliveProvidersSize(5);

        return configModel;
    }

    private MerchantProviderParamsDO createMerchantProviderParams(Long id, Integer provider, Integer payway, long createTime) {
        MerchantProviderParamsDO param = new MerchantProviderParamsDO();
        param.setId(String.valueOf(id));
        param.setProvider(provider);
        param.setPayway(payway);
        param.setPayMerchantId("subMchId" + id);
        param.setCtime(createTime);
        return param;
    }
}
