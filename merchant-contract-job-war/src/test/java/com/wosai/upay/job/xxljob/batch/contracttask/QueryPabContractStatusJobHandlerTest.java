package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryPabContractStatusJobHandlerTest {

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @InjectMocks
    private QueryPabContractStatusJobHandler queryPabContractStatusJobHandler;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private QueryContractStatusHandler queryContractStatusHandler;

    @Mock
    private ChatBotUtil chatBotUtil;

    private ContractSubTask contractSubTask;
    private ContractSubTask contractSubTaskB;
    private ContractTask contractTask;
    private BatchJobParam batchJobParam;

    @Before
    public void setUp() {
        contractSubTask = new ContractSubTask();
        batchJobParam = new BatchJobParam();
        batchJobParam.setBatchSize(10);
        batchJobParam.setQueryTime(1000L);

        contractSubTaskB = new ContractSubTask();
        contractSubTaskB.setId(1L);
        contractSubTaskB.setP_task_id(2L);

        contractTask = new ContractTask();
        contractTask.setId(2L);
    }

    @Test
    public void getLockKey_ValidId_ReturnsCorrectLockKey() {
        contractSubTask.setId(123L);
        String expectedLockKey = "QueryPabContractStatusJobHandler:123";
        String actualLockKey = queryPabContractStatusJobHandler.getLockKey(contractSubTask);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void getLockKey_NullId_ReturnsLockKeyWithNull() {
        contractSubTask.setId(null);
        String expectedLockKey = "QueryPabContractStatusJobHandler:null";
        String actualLockKey = queryPabContractStatusJobHandler.getLockKey(contractSubTask);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsContractSubTaskList() {
        List<ContractSubTask> expectedTasks = Arrays.asList(new ContractSubTask(), new ContractSubTask());
        when(contractSubTaskMapper.selectPabContractQueryTask(
                Mockito.anyInt(),
                Mockito.anyString(),
                Mockito.anyString()
        )).thenReturn(expectedTasks);

        List<ContractSubTask> actualTasks = queryPabContractStatusJobHandler.queryTaskItems(batchJobParam);

        assertEquals(expectedTasks, actualTasks);
    }

    @Test
    public void queryTaskItems_BatchSizeZero_ReturnsEmptyList() {
        batchJobParam.setBatchSize(0);
        when(contractSubTaskMapper.selectPabContractQueryTask(
                Mockito.anyInt(),
                Mockito.anyString(),
                Mockito.anyString()
        )).thenReturn(Arrays.asList());

        List<ContractSubTask> actualTasks = queryPabContractStatusJobHandler.queryTaskItems(batchJobParam);

        assertEquals(0, actualTasks.size());
    }

    @Test
    public void queryTaskItems_NegativeBatchSize_ReturnsEmptyList() {
        batchJobParam.setBatchSize(-1);
        when(contractSubTaskMapper.selectPabContractQueryTask(
                Mockito.anyInt(),
                Mockito.anyString(),
                Mockito.anyString()
        )).thenReturn(Arrays.asList());

        List<ContractSubTask> actualTasks = queryPabContractStatusJobHandler.queryTaskItems(batchJobParam);

        assertEquals(0, actualTasks.size());
    }

    @Test
    public void doHandleSingleData_StatusSuccess_Returns() {
        contractSubTaskB.setStatus(TaskStatus.SUCCESS.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(contractSubTaskB);

        queryPabContractStatusJobHandler.doHandleSingleData(contractSubTaskB);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getId());
        verifyNoInteractions(contractTaskMapper, queryContractStatusHandler, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_StatusFail_Returns() {
        contractSubTaskB.setStatus(TaskStatus.FAIL.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(contractSubTaskB);

        queryPabContractStatusJobHandler.doHandleSingleData(contractSubTaskB);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getId());
        verifyNoInteractions(contractTaskMapper, queryContractStatusHandler, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_ValidExecution_CallsDoHandle() {
        contractSubTaskB.setStatus(TaskStatus.PROGRESSING.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(contractSubTaskB);
        when(contractTaskMapper.selectByPrimaryKey(contractSubTaskB.getP_task_id())).thenReturn(contractTask);

        queryPabContractStatusJobHandler.doHandleSingleData(contractSubTaskB);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getId());
        verify(contractTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getP_task_id());
        verify(queryContractStatusHandler, times(1)).doHandle(contractTask, contractSubTaskB);
        verifyNoInteractions(chatBotUtil);
    }

    @Test
    public void doHandleSingleData_ExceptionOccurs_LogsErrorAndSendsWarning() {
        contractSubTaskB.setStatus(TaskStatus.PROGRESSING.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(contractSubTaskB);
        when(contractTaskMapper.selectByPrimaryKey(contractSubTaskB.getP_task_id())).thenReturn(contractTask);
        doThrow(new RuntimeException("Test exception")).when(queryContractStatusHandler).doHandle(any(ContractTask.class), any(ContractSubTask.class));

        queryPabContractStatusJobHandler.doHandleSingleData(contractSubTaskB);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getId());
        verify(contractTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getP_task_id());
        verify(queryContractStatusHandler, times(1)).doHandle(contractTask, contractSubTaskB);
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
