package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.xxljob.model.DirectExecTypeEnum;
import com.wosai.upay.job.xxljob.model.DirectJobParam;

public class BankDirectAuthStatusJobHandlerNewTest extends BaseTest {

    @Autowired
    private BankDirectAuthStatusJobHandler bankDirectAuthStatusJobHandler;

    @Test
    public void executeTest() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(500);
        param.setQueryTime(2592000000L);
        param.setExecType(DirectExecTypeEnum.ASYNC);
        bankDirectAuthStatusJobHandler.execute(param);
    }

}