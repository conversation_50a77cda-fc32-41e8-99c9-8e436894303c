package com.wosai.upay.job.service;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsCustom;
import com.wosai.upay.job.model.SyncSubMchToLklResp;
import com.wosai.upay.job.model.directparams.*;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.dto.WeixinSubAppidDto;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

@RunWith(MockitoJUnitRunner.class)
public class MerchantProviderParamsServiceImplTest {

    @InjectMocks
    private MerchantProviderParamsServiceImpl service;

    @Mock
    RuleContext ruleContext;
    @Mock
    private McAcquirerDAO mcAcquirerDAO;
    @Mock
    private SubBizParamsBiz subBizParamsBiz;
    @Mock
    private LklV3Service lklV3Service;
    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Test
    public void addAlipayV2DirectParams() {
        AlipayV2DirectParams baseParams = new AlipayV2DirectParams();
        baseParams.setMerchant_id("id");
        baseParams.setMerchant_sn("sn");
        service.addAlipayV2DirectParams(baseParams);
    }

    @Test
    public void addWeixinDirectParams() {
        WeixinDirectParams baseParam = new WeixinDirectParams();
        baseParam.setMerchant_id("id");
        baseParam.setMerchant_sn("sn");
        service.addWeixinDirectParams(baseParam);
    }

    @Test
    public void addAlipayIntlDirectParams() {
        AlipayIntlDirectParams baseParam = new AlipayIntlDirectParams();
        baseParam.setMerchant_id("id");
        baseParam.setMerchant_sn("sn");
        service.addAlipayIntlDirectParams(baseParam);
    }

    @Test
    public void addWeixinHKDirectParams() {
        WeixinHKDirectParams baseParam = new WeixinHKDirectParams();
        baseParam.setMerchant_id("id");
        baseParam.setMerchant_sn("sn");
        service.addWeixinHKDirectParams(baseParam);
    }

    @Test
    public void addBestpayDirectParams() {
        BestpayDirectParams baseParam = new BestpayDirectParams();
        baseParam.setMerchant_id("id");
        baseParam.setMerchant_sn("sn");
        service.addBestpayDirectParams(baseParam);
    }

    @Test
    public void findMerchantProviderParamsList() {
        MerchantProviderParamsCustom providerParamsCustom = new MerchantProviderParamsCustom();
        providerParamsCustom.setPayway(3).setProvider(1016);
        ContractChannel contractChannel = new ContractChannel();
        Mockito.doReturn(contractChannel).when(ruleContext).getContractChannel(Mockito.anyInt(), Mockito.anyString(), Mockito.anyString());
        Mockito.doReturn(new McAcquirerDO()).when(mcAcquirerDAO).getByAcquirer(Mockito.anyString());
    }

    @Test
    public void getMerchantInfoBySns() {
    }

    @Test
    public void findMerchantProviderParamsDetailById() {
    }

    @Test
    public void modifyMerchantProviderParamsById() {
    }

    @Test
    public void setDefaultMerchantProviderParams() {
    }

    @Test
    public void getSetDefaultMerchantProviderParamsMsg() {
    }

    @Test
    public void deleteDirectParams() {
    }

    @Test
    public void deleteDirectParamsByMerchantId() {
    }

    @Test
    public void getAcquirerMerchantInfo() {
    }

    @Test
    public void testAddWeixinSubAppid() {
    }

    @Test
    public void getInUsePayMchId() {
    }

    @Test
    public void syncMerchantConfigToParams() {
    }

    @Test
    public void getMerchantProviderParams() {
    }

    @Test
    public void syncSubMchToLkl() {
        String providerMerchantId = "providerMerchantId";
        // 1.微信成功支付宝失败
        Mockito.doReturn(CollectionUtil.hashMap("wx", new ContractResponse().setCode(200).setMessage("成功")))
                .when(lklV3Service).syncSubMchByMerCupNo(providerMerchantId);
        SyncSubMchToLklResp syncSubMchToLklResp = service.syncSubMchToLkl(providerMerchantId);
        Assert.assertEquals("成功", syncSubMchToLklResp.getWx());
        Assert.assertEquals("支付宝子商户号不存在", syncSubMchToLklResp.getAli());

    }
}