package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.xxljob.batch.contracttask.QueryGuotongContractStatusJobHandler;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryGuotongContractStatusJobHandlerTest {

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @InjectMocks
    private QueryGuotongContractStatusJobHandler queryGuotongContractStatusJobHandler;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private QueryContractStatusHandler queryContractStatusHandler;

    private ContractSubTask contractSubTask;
    private ContractTask contractTask;
    private ContractSubTask contractSubTaskB;
    private BatchJobParam batchJobParam;

    @Before
    public void setUp() {
        contractSubTask = new ContractSubTask();
        contractSubTask.setId(123L);

        contractSubTaskB = new ContractSubTask();
        contractSubTaskB.setId(1L);
        contractSubTaskB.setP_task_id(2L);

        contractTask = new ContractTask();
        contractTask.setId(2L);

        batchJobParam = new BatchJobParam();
        batchJobParam.setBatchSize(10);
        batchJobParam.setQueryTime(1000L);
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "QueryGuotongContractStatusJobHandler:123";
        String actualLockKey = queryGuotongContractStatusJobHandler.getLockKey(contractSubTask);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsNonEmptyList() {
        List<ContractSubTask> expectedTasks = new ArrayList<>();
        expectedTasks.add(new ContractSubTask());

        when(contractSubTaskMapper.selectGuotongContractQueryTask(
                Mockito.anyInt(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(expectedTasks);

        List<ContractSubTask> actualTasks = queryGuotongContractStatusJobHandler.queryTaskItems(batchJobParam);

        assertEquals(expectedTasks, actualTasks);
    }

    @Test
    public void queryTaskItems_EmptyResult_ReturnsEmptyList() {
        when(contractSubTaskMapper.selectGuotongContractQueryTask(
                Mockito.anyInt(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(new ArrayList<>());

        List<ContractSubTask> actualTasks = queryGuotongContractStatusJobHandler.queryTaskItems(batchJobParam);

        assertTrue(actualTasks.isEmpty());
    }

    @Test(expected = RuntimeException.class)
    public void queryTaskItems_MapperThrowsException_ThrowsException() {
        when(contractSubTaskMapper.selectGuotongContractQueryTask(
                Mockito.anyInt(), Mockito.anyString(), Mockito.anyString()))
                .thenThrow(new RuntimeException("Test exception"));

        queryGuotongContractStatusJobHandler.queryTaskItems(batchJobParam);
    }

    @Test
    public void doHandleSingleData_StatusMatches_CallsDoHandle() {
        when(contractSubTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractSubTaskB);
        when(contractTaskMapper.selectByPrimaryKey(2L)).thenReturn(contractTask);
        contractSubTaskB.setStatus(ContractSubTaskProcessStatusEnum.WAIT_CALL_BACK_TEN.getValue());

        queryGuotongContractStatusJobHandler.doHandleSingleData(contractSubTaskB);

        verify(queryContractStatusHandler, times(1)).doHandle(contractTask, contractSubTaskB);
    }

    @Test
    public void doHandleSingleData_StatusDoesNotMatch_DoesNotCallDoHandle() {
        when(contractSubTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractSubTaskB);
        contractSubTaskB.setStatus(ContractSubTaskProcessStatusEnum.WAIT_PROCESS.getValue());

        queryGuotongContractStatusJobHandler.doHandleSingleData(contractSubTaskB);

        verify(queryContractStatusHandler, never()).doHandle(any(ContractTask.class), any(ContractSubTask.class));
    }
}
