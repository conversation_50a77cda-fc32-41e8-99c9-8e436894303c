package com.wosai.upay.job.service;


import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.crow.api.model.query.SingleTagEntityRecord;
import com.wosai.data.crow.api.model.query.SingleVersionTagKV;
import com.wosai.data.crow.api.service.OnlineQueryService;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.ProviderTerminalIdBiz;
import com.wosai.upay.job.biz.acquirer.HaikeAcquirerBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.FailEventConf;
import com.wosai.upay.job.model.HxbParamsV2;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("default")
@Slf4j
public class ContractApplyStatusServiceTest {

    @Autowired
    ContractStatusService contractStatusService;
    @Autowired
    BankCardServiceImpl bankCardServiceImpl;
    @Autowired
    BatchService batchService;

    @Autowired
    ProviderTerminalIdBiz providerTerminalIdBiz;

    @Autowired
    HaikeAcquirerBiz haikeAcquirerBiz;

    @Autowired
    MerchantProviderParamsBiz merchantProviderParamsBiz;
    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Test
    public void selectByUpdateTime() {
        String terminalId = providerTerminalIdBiz.nextProviderTerminalId();
        long hour = 1000 * 60 * 60;
        contractStatusService.selectByUpdateTime(System.currentTimeMillis() - hour);
    }

    @Test
    public void selectByPrimaryKey() {
        contractStatusService.selectByPrimaryKey(1);
    }

    @Test
    public void selectByMerchantSn() {
        contractStatusService.selectByMerchantSn("**************");
    }

    @Test
    public void getMessageByContractTask() {
        contractStatusService.getMessageByContractTask(new ContractTask().setStatus(5));
    }

    @Test
    public void getMessageByTaskId() {
        contractStatusService.getMessageByTaskId(2539169);
    }

    @Test
    public void rejectContractFields() {
        contractStatusService.rejectContractFields("21690003580817", new ArrayList<>());
    }

    @Test
    public void rejectContractFieldsV2() {
        FailEventConf failEventConf = new FailEventConf().setFail(true).setFail_msg("单元测试用");
        contractStatusService.rejectContractFieldsV2("**************", new ArrayList<>(), failEventConf);
    }

    @Test
    public void existMerchant() {
        contractStatusService.existMerchant(0, "**********");
    }

    @Test
    public void getWeixinUpgrade() {
        contractStatusService.getWeixinUpgrade("**************", "crm", ProviderUtil.CONTRACT_TYPE_AUTH);
    }

    @Test
    public void verifyAmount() {
        contractStatusService.verifyAmount("**************", BigDecimal.TEN);
    }

    @Test
    public void queryPayForResult() {
        contractStatusService.queryPayForResult("**************");
    }

    @Test
    public void authChannelCode() {
        contractStatusService.authChannelCode("*************");
    }

    @Test
    public void test1() {
//        String con = "";
//        Map map = JSON.parseObject(con, Map.class);
//        bankCardServiceImpl.updateCardAfterTaskStatus(map,2,"成功");
        HxbParamsV2 hxbParamsV2 = new HxbParamsV2();
        hxbParamsV2.setFee_rate("0.38");
        hxbParamsV2.setHolder_name("甘立");
        hxbParamsV2.setIdentify_end("5411");
        hxbParamsV2.setProvider_mch_id("***************");
        hxbParamsV2.setProvider_term_id("********");
        hxbParamsV2.setWeixin_sub_mch_id("*********");
        hxbParamsV2.setAlipay_sub_mch_id("****************");
        hxbParamsV2.setMerchant_sn("**************");
        hxbParamsV2.setDevelop_app_id("4D544A347474463252425841467877734B47415A6258563262385111");

        batchService.importHxbParamsV2(hxbParamsV2);

    }

    @Autowired
    private OnlineQueryService onlineQueryService;
    @Value("${hxb_check_value_1:华夏额度包}")
    private String hxbValue1;

    @Test
    public void checkTagsForHxb() {

        SingleTagEntityRecord record = onlineQueryService.getSingleTagEntityRecordById("af1de674-2cf9-4491-8c65-7a649d3fada4", "9db021b2-0b8c-4870-ada4-ddb21a86cb9f",
                Arrays.asList("7a8e4f34-b8d8-4d5d-887f-643f4ebb9be4"), false);

        if (record != null && record.getTag() != null) {
            SingleVersionTagKV tag = record.getTag();
            String tagValue = tag.getValue() == null ? null : (String) tag.getValue();
            //有任意一个标签符合对应值,认为被打上了标签. 校验失败
            if (WosaiStringUtils.equals(tagValue, hxbValue1)) {
                throw new CommonInvalidParameterException("该商户已参加华夏绑卡活动，不允许提交银行进件");
            }
        }

    }

    public static String getEncoding(String str) {
        String encode = "GB2312";
        try {
            if (isEncoding(str, encode)) { // 判断是不是GB2312
                return encode;
            }
        } catch (Exception exception) {
        }
        encode = "ISO-8859-1";
        try {
            if (isEncoding(str, encode)) { // 判断是不是ISO-8859-1
                return encode;
            }
        } catch (Exception exception1) {
        }
        encode = "UTF-8";
        try {
            if (isEncoding(str, encode)) { // 判断是不是UTF-8
                return encode;
            }
        } catch (Exception exception2) {
        }
        encode = "GBK";
        try {
            if (isEncoding(str, encode)) { // 判断是不是GBK
                return encode;
            }
        } catch (Exception exception3) {
        }
        return "未知"; // 如果都不是，说明输入的内容不属于常见的编码格式。
    }

    public static boolean isEncoding(String str, String encode) {
        try {
            if ("市场额度包".equals(new String(str.getBytes(), encode))) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
