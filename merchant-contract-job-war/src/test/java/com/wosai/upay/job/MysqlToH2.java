package com.wosai.upay.job;

import com.google.common.io.ByteStreams;

import java.io.InputStream;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020-05-14
 */
public class MysqlToH2 {

    public static void main(String[] args) throws Exception {
        try (InputStream input = MysqlToH2.class.getResourceAsStream("/mysql.sql")) {
            if (input != null) {
                String content = new String(ByteStreams.toByteArray(input));
                content = "SET MODE MYSQL;\n" + content;

                content = content.replaceAll("`", "");
                content = content.replaceAll("IF NOT EXISTS", "");
                content = content.replaceAll("CREATE TABLE ", "CREATE TABLE IF NOT EXISTS ");
                content = content.replaceAll("COLLATE.*(?=D|C|,)", "");
                content = content.replaceAll("COMMENT.*'(?=,)", "");
                content = content.replaceAll("\\).*ENGINE.*(?=;)", ")");
                content = content.replaceAll("DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", " AS CURRENT_TIMESTAMP");

                content = uniqueKey(content);

                System.out.println(content);
            }
        }


    }

    /**
     * h2的索引名必须全局唯一
     *
     * @param content sql建表脚本
     * @return 替换索引名为全局唯一
     */
    private static String uniqueKey(String content) {
        int inc = 0;
        Pattern pattern = Pattern.compile("(?<=KEY )(.*?)(?= \\()");
        Matcher matcher = pattern.matcher(content);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group() + inc++);
        }
        matcher.appendTail(sb);
        content = sb.toString();
        return content;
    }
}
