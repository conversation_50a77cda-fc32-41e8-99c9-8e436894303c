package com.wosai.upay.job.util;

import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.model.acquirer.CheckChangeAcquirerResp;
import com.wosai.upay.job.service.AcquirerService;
import org.junit.*;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * TongLianUtil Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>May 14, 2020</pre>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TongLianUtilTest {

    @Autowired
    ParamContextBiz paramContextBiz;

    @Autowired
    TradeConfigService tradeConfigService;

    @Autowired
    AcquirerService acquirerService;

    @Test
    public void getWechatContext() throws ContextParamException {
        CheckChangeAcquirerResp tonglian = acquirerService.checkChangeAcquirer("21690003072424", "tonglian");
        System.out.println(tonglian);

    }


} 
