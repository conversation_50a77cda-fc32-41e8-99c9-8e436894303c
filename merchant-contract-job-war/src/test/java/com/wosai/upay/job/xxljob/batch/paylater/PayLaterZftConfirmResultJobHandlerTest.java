package com.wosai.upay.job.xxljob.batch.paylater;


import com.wosai.upay.job.biz.PayLaterBiz;
import com.wosai.upay.job.model.payLater.PayLaterApply;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PayLaterZftConfirmResultJobHandlerTest {

    @InjectMocks
    private PayLaterZftConfirmResultJobHandler handler;

    @Mock
    private PayLaterBiz payLaterBiz;

    private BatchJobParam param;

    @Before
    public void setUp() {
        param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());
    }

    @Test
    public void queryTaskItems_ValidParam_ReturnsExpectedList() {
        List<PayLaterApply> expectedList = Arrays.asList(
                new PayLaterApply().setId(1L),
                new PayLaterApply().setId(2L)
        );

        when(payLaterBiz.getPayLaterTasks(Mockito.anyList(), Mockito.eq(10), Mockito.anyLong()))
                .thenReturn(expectedList);

        List<PayLaterApply> result = handler.queryTaskItems(param);

        assertEquals(expectedList, result);
    }

    @Test
    public void queryTaskItems_BatchSizeZero_ReturnsEmptyList() {
        param.setBatchSize(0);

        when(payLaterBiz.getPayLaterTasks(Mockito.anyList(), Mockito.eq(0), Mockito.anyLong()))
                .thenReturn(Arrays.asList());

        List<PayLaterApply> result = handler.queryTaskItems(param);

        assertEquals(Arrays.asList(), result);
    }
}