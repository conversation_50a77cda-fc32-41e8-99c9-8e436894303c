package com.wosai.upay.job.xxljob.batch.paylater;

import com.wosai.upay.job.Constants.PayLaterConstant;
import com.wosai.upay.job.biz.PayLaterBiz;
import com.wosai.upay.job.mapper.PayLaterApplyMapper;
import com.wosai.upay.job.model.payLater.PayLaterApply;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PayLaterCreateZhimaMerchantJobHandlerTest {

    @Mock
    private PayLaterApplyMapper payLaterApplyMapper;

    @Mock
    private PayLaterBiz payLaterBiz;

    @InjectMocks
    private PayLaterCreateZhimaMerchantJobHandler handler;

    private PayLaterApply payLaterApply;

    private BatchJobParam param;

    @Before
    public void setUp() {
        payLaterApply = new PayLaterApply();
        payLaterApply.setId(1L);
        payLaterApply.setMerchant_sn("testMerchant");

        param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());
    }

    @Test
    public void getLockKey_ValidId_ReturnsCorrectLockKey() {
        Long id = 123L;
        payLaterApply.setId(id);

        String expectedLockKey = "PayLaterCreateZhimaMerchantJobHandler:" + id;
        String actualLockKey = handler.getLockKey(payLaterApply);

        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void getLockKey_NullId_ReturnsLockKeyWithNull() {
        payLaterApply.setId(null);

        String expectedLockKey = "PayLaterCreateZhimaMerchantJobHandler:" + null;
        String actualLockKey = handler.getLockKey(payLaterApply);

        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void doHandleSingleData_ProcessStatusNotZFTSuccess_DoesNothing() {
        PayLaterApply apply = new PayLaterApply();
        apply.setProcess_status(PayLaterConstant.ProcessStatus.PENDING);

        when(payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId())).thenReturn(apply);

        handler.doHandleSingleData(payLaterApply);

        verify(payLaterApplyMapper, times(1)).selectByPrimaryKey(payLaterApply.getId());
        verify(payLaterBiz, never()).createZhiMaMerchant(any(PayLaterApply.class));
    }

    @Test
    public void doHandleSingleData_ProcessStatusZFTSuccess_CreatesZhiMaMerchant() {
        PayLaterApply apply = new PayLaterApply();
        apply.setProcess_status(PayLaterConstant.ProcessStatus.ZFT_SUCCESS);

        when(payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId())).thenReturn(apply);

        handler.doHandleSingleData(payLaterApply);

        verify(payLaterApplyMapper, times(1)).selectByPrimaryKey(payLaterApply.getId());
        verify(payLaterBiz, times(1)).createZhiMaMerchant(payLaterApply);
    }

    @Test
    public void doHandleSingleData_ExceptionOccurs_ModifiesApplyStatus() {
        when(payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId())).thenThrow(new RuntimeException("Test Exception"));

        handler.doHandleSingleData(payLaterApply);

        verify(payLaterApplyMapper, times(1)).selectByPrimaryKey(payLaterApply.getId());
        verify(payLaterBiz, times(1)).modifyPayLaterApply(
                eq(payLaterApply),
                eq(PayLaterConstant.Status.ZHIMA_FAIL),
                eq(PayLaterConstant.SubStatus.FAIL),
                eq(PayLaterConstant.ProcessStatus.FAIL),
                eq(PayLaterConstant.Result.ZHIMA_AUDIT_FAIL),
                eq(0)
        );
    }

    @Test
    public void queryTaskItems_WhenTasksExist_ReturnsListOfTasks() {
        PayLaterApply apply = new PayLaterApply();
        List<PayLaterApply> expectedTasks = Collections.singletonList(apply);

        when(payLaterBiz.getPayLaterTasks(Collections.singletonList(PayLaterConstant.ProcessStatus.ZFT_SUCCESS), param.getBatchSize(), param.getQueryTime()))
                .thenReturn(expectedTasks);

        List<PayLaterApply> result = handler.queryTaskItems(param);

        assertEquals(expectedTasks, result);
    }

    @Test
    public void queryTaskItems_WhenNoTasksExist_ReturnsEmptyList() {
        List<PayLaterApply> result = handler.queryTaskItems(param);
        assertTrue(result.isEmpty());
    }
}
