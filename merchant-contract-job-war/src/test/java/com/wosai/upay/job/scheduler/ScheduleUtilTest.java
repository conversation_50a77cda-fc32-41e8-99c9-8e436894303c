package com.wosai.upay.job.scheduler;

import com.wosai.upay.bank.model.enume.AccountApplyStatus;
import com.wosai.upay.job.model.ContractTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @date 2020/7/15
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Transactional
public class ScheduleUtilTest {

    @Autowired
    private ScheduleUtil scheduleUtil;

    @Test
    public void getPayForTip() {
        ContractTask task = new ContractTask();
        task.setResult("{\"message\":\""+ScheduleUtil.TIPS_PAY_FOR_PROCESS_PUBLIC+"\"}");
        Map result = ScheduleUtil.getPayForTip(task, AccountApplyStatus.FAIL.getStatus());
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_VERIFY_FAIL_PUBLIC,result.get("message"));

        task.setResult("{\"message\":\"" + ScheduleUtil.TIPS_PAY_FOR_PROCESS_PUBLIC + "\"}");
        result = ScheduleUtil.getPayForTip(task, AccountApplyStatus.PAYMENT_FAIL.getStatus());
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_PAYMENT_FAIL_PUBLIC, result.get("message"));

        task.setResult("{\"message\":\"" + ScheduleUtil.TIPS_PAY_FOR_PROCESS_PUBLIC + "\"}");
        result = ScheduleUtil.getPayForTip(task, AccountApplyStatus.BANK_CHARGEBACK_FAIL.getStatus());
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_BANK_CHARGEBACK_PUBLIC, result.get("message"));

        task.setResult("{\"message\":\"" + ScheduleUtil.TIPS_PAY_FOR_PROCESS_PRIVATE_FOREIGN + "\"}");
        result = ScheduleUtil.getPayForTip(task, AccountApplyStatus.BANK_CHARGEBACK_FAIL.getStatus());
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_BANK_CHARGEBACK_PRIVATE_FOREIGN, result.get("message"));
    }
}