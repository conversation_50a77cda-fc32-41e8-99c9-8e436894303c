package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.controller.TongLianV2Controller;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TonglianV2UnCallbackTaskJobHandlerTest {

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @InjectMocks
    private TonglianV2UnCallbackTaskJobHandler handler;

    @Mock
    private TongLianV2Controller tongLianV2Controller;

    @Mock
    private TongLianV2Service tongLianV2Service;

    @Mock
    private ContractParamsBiz contractParamsBiz;

    private ContractSubTask contractSubTaskA;
    private BatchJobParam batchJobParam;

    private ContractSubTask contractSubTaskB;

    @Before
    public void setUp() {
        contractSubTaskA = new ContractSubTask();
        contractSubTaskA.setId(123L);

        batchJobParam = new BatchJobParam();
        batchJobParam.setBatchSize(10);
        batchJobParam.setStartTime(1000L);
        batchJobParam.setEndTime(2000L);

        contractSubTaskB = new ContractSubTask();
        contractSubTaskB.setId(1L);
        contractSubTaskB.setMerchant_sn("12345");
        contractSubTaskB.setTask_type(5);
    }

    @Test
    public void getLockKey_ValidContractSubTask_ReturnsCorrectLockKey() {
        String expectedLockKey = "TonglianV2UnCallbackTaskJobHandler:123";
        String actualLockKey = handler.getLockKey(contractSubTaskA);

        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void queryTaskItems_ValidBatchJobParam_ReturnsContractSubTaskList() {
        List<ContractSubTask> expectedTasks = Arrays.asList(new ContractSubTask(), new ContractSubTask());
        when(contractSubTaskMapper.getTongLianV2UnCallbackSubTask(any(), anyString(), anyString())).thenReturn(expectedTasks);

        List<ContractSubTask> actualTasks = handler.queryTaskItems(batchJobParam);

        assertEquals(expectedTasks, actualTasks);
    }

    @Test
    public void doHandleSingleData_StatusSuccessOrFail_Returns() {
        ContractSubTask subTaskLast = new ContractSubTask();
        subTaskLast.setStatus(TaskStatus.SUCCESS.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(subTaskLast);

        handler.doHandleSingleData(contractSubTaskB);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTaskB.getId());
        verifyNoMoreInteractions(contractSubTaskMapper, contractParamsBiz, tongLianV2Service, tongLianV2Controller);
    }

    @Test
    public void doHandleSingleData_QuerySuccessfulAndMatched_UpdatesStatusSuccess() {
        ContractSubTask subTaskLast = new ContractSubTask();
        subTaskLast.setStatus(TaskStatus.PROGRESSING.getVal());
        subTaskLast.setMerchant_sn("merchant_sn");
        subTaskLast.setRequest_body("{\"corpbusname\":\"Test Corp\"}");
        TongLianV2Param tongLianV2Param = new TongLianV2Param();
        ContractResponse contractResponse = new ContractResponse();
        contractResponse.setCode(200);
        contractResponse.setResponseParam(new HashMap<String, Object>() {{
            put("corpbusname", "Test Corp");
        }});

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(subTaskLast);
        when(contractParamsBiz.buildContractParams(anyString(), eq(TongLianV2Param.class))).thenReturn(tongLianV2Param);
        when(tongLianV2Service.queryMerchantInfo(anyString(), eq(tongLianV2Param))).thenReturn(contractResponse);

        handler.doHandleSingleData(contractSubTaskB);

        verify(tongLianV2Controller, times(1)).updateStatus(eq(true), eq(subTaskLast), any(TongLianV2Controller.AuditNotifyRequest.class), isNull());
    }

    @Test
    public void doHandleSingleData_QuerySuccessfulAndNotMatched_UpdatesStatusFail() {
        ContractSubTask subTaskLast = new ContractSubTask();
        subTaskLast.setStatus(TaskStatus.PROGRESSING.getVal());
        subTaskLast.setMerchant_sn("merchant_sn");
        subTaskLast.setRequest_body("{\"corpbusname\":\"Test Corp\"}");
        TongLianV2Param tongLianV2Param = new TongLianV2Param();
        ContractResponse contractResponse = new ContractResponse();
        contractResponse.setCode(200);
        contractResponse.setResponseParam(new HashMap<String, Object>() {{
            put("corpbusname", "Different Corp");
        }});
        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(subTaskLast);
        when(contractParamsBiz.buildContractParamsByContractSubTask(contractSubTaskB, TongLianV2Param.class)).thenReturn(tongLianV2Param);
        when(tongLianV2Service.queryMerchantInfo(anyString(), eq(tongLianV2Param))).thenReturn(contractResponse);

        handler.doHandleSingleData(contractSubTaskB);

        verify(tongLianV2Controller, times(1)).updateStatus(eq(false), eq(subTaskLast), any(TongLianV2Controller.AuditNotifyRequest.class), eq("审核不通过"));
    }

    @Test
    public void doHandleSingleData_QueryFailed_LogsError() {
        ContractSubTask subTaskLast = new ContractSubTask();
        subTaskLast.setStatus(TaskStatus.PROGRESSING.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTaskB.getId())).thenReturn(subTaskLast);
        when(contractParamsBiz.buildContractParams(anyString(), eq(TongLianV2Param.class))).thenThrow(new RuntimeException("Query failed"));

        handler.doHandleSingleData(contractSubTaskB);

        verify(tongLianV2Controller, never()).updateStatus(anyBoolean(), any(ContractSubTask.class), any(TongLianV2Controller.AuditNotifyRequest.class), anyString());
    }
}
