package com.wosai.upay.job.biz.comboparams;


import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;


public class ProviderParamsHandleTest extends BaseTest {

    @Autowired
    List<ProviderParamsHandle> handleList;
    @MockBean
    TradeConfigService tradeConfigService;

    @Before
    public void before() {
        handleList.forEach(h -> {
            ReflectionTestUtils.setField(h, "tradeConfigService", tradeConfigService);
        });
    }


    @Test
    public void accept() {
        handleList.forEach(h -> {
            h.accept(new MerchantConfigParams().setProvider(1016));
        });
    }

    @Test
    public void handle() {
        handleList.get(0).handle(new MerchantConfigParams().setAgentColumns(Arrays.asList("b2c_agent_name")).setPayWay(3));
    }

    @Test
    public void getConfigParams() {
        List<Integer> payWays = Arrays.asList(2, 3, 17, 18);
        handleList.forEach(h -> {
            for (Integer payWay : payWays) {
                MerchantConfigParams configParams = new MerchantConfigParams().setPayWay(payWay).setTongLianTradeParams(CollectionUtil.hashMap("param", "param"));
                h.getConfigParams(configParams);
            }
        });
    }
}
