package com.wosai.upay.job.handlers;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.env.Environment;
import org.springframework.test.context.junit4.SpringRunner;

import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.controller.UMBController;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.merchant.contract.model.ContractResponse;

@SpringBootTest
@RunWith(SpringRunner.class)
public class ContractSubTaskHandlerNewTest {

    @Autowired
    private ContractSubTaskHandler contractSubTaskHandler;

    @Autowired
    private Environment environment;

    @MockBean
    private ContractParamsBiz contractParamsBiz;

    @MockBean
    private RuleContext ruleContext;

    @MockBean
    private UMBController umbController;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Before
    public void setUp() throws Exception {
        ContractRule contractRule = new ContractRule();
        contractRule.setProvider("1032");
        contractRule.setAcquirer("lklV3");
        Mockito.when(ruleContext.getContractRule(Mockito.anyString())).thenReturn(contractRule);
    }

    @Test
    public void test() {
        ContractSubTask contractSubTask = new ContractSubTask();
        ContractResponse contractResponse = new ContractResponse();
        contractSubTask.setMerchant_sn("**************");
        contractSubTask.setChannel("lkl");
        contractSubTask.setContract_rule("");
        contractResponse.setMessage("参数格式错误:联行号未找到");
        environment.getProperty("spring.datasource.url");
        ContractTask contractTask = new ContractTask();
        contractTask.setEvent_context(
            "{\"merchant\":{\"alias\":\"唐阿囍柳州螺蛳粉深圳民乐店\",\"bank_account_verify_status\":-1,\"business\":\"唐阿囍柳州螺蛳粉深圳民乐店\",\"business_name\":\"唐阿囍柳州螺蛳粉深圳民乐店\",\"city\":\"深圳市\",\"contact_cellphone\":\"***********\",\"contact_name\":\"唐凤兰\",\"country\":\"CHN\",\"ctime\":*************,\"currency\":\"CNY\",\"customer_phone\":\"***********\",\"deleted\":false,\"district\":\"龙华区\",\"district_code\":\"440309\",\"extra\":{\"poi_address\":\"广东省深圳市龙华区民乐路东一巷与一区中路交叉口西120米\",\"1\":[\"name\",\"business_name\",\"street_address\",\"industry\",\"contact_name\",\"city\"]},\"id\":\"f6b5aaa7-a7de-497e-b456-b53134a5096e\",\"industry\":\"7198ee72-7371-422e-833c-eb6ddd9be5b1\",\"latitude\":\"22.598808\",\"legal_person_id_card_back_photo\":\"https://private-images.shouqianba.com/sales-system-gateway/2025-06-16/cf1a0be2ffe248b99cdebd957a134deb.jpeg?Expires=**********&OSSAccessKeyId=STS.NVSZ9H7zRLEye2mkSv6XJocCf&Signature=S1W2n8R0r8mtx%2FJRg6T9Bt6EAGk%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5XmEYP82qVz%2B4eSZxTcj1MjOtdmgKbohDz2IHhKfnVhAO8dtvw2mmhT7%2F0eloloU55fSAnfdME154xPtAimedOe5pTtsOFY2MHgRjbIW0ZoZj4H%2FL%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYUxdlMoP%2FdhQ%2FYd8qCnzKYkoJbIjo6y0x9ceP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6uKOKYTM%2FPQLnt5h223NW7DnWz5qwiq%2BRi2JZeZrXo%2B4H1S74SvGs3T0ybahIdG7lRJ8EPtayytueVqKgwSJGhqAAZnA0tWMSKeGTJOQLX4q1mrkd%2BW%2BLBet%2B4acAxh0uh2tHXAV6KYcAwHkhftm7z6zfG5McGQjRl7Lqb%2BbYUp2qsjlUXL5JAHPEY2PThb0OnCHwiDwqWeDLj8mZoiUfeW0tW44doAfyZfHw0pK%2FxpGqesjvplKajN49J9j1ghLONXkIAA%3D\",\"legal_person_id_card_front_photo\":\"https://private-images.shouqianba.com/sales-system-gateway/2025-06-16/6e1d4b0cd1814d36a4dc636f715a75be.jpeg?Expires=**********&OSSAccessKeyId=STS.NVSZ9H7zRLEye2mkSv6XJocCf&Signature=C0caTvQwrMu9dNwAktVWI2Lem7Y%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5XmEYP82qVz%2B4eSZxTcj1MjOtdmgKbohDz2IHhKfnVhAO8dtvw2mmhT7%2F0eloloU55fSAnfdME154xPtAimedOe5pTtsOFY2MHgRjbIW0ZoZj4H%2FL%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYUxdlMoP%2FdhQ%2FYd8qCnzKYkoJbIjo6y0x9ceP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6uKOKYTM%2FPQLnt5h223NW7DnWz5qwiq%2BRi2JZeZrXo%2B4H1S74SvGs3T0ybahIdG7lRJ8EPtayytueVqKgwSJGhqAAZnA0tWMSKeGTJOQLX4q1mrkd%2BW%2BLBet%2B4acAxh0uh2tHXAV6KYcAwHkhftm7z6zfG5McGQjRl7Lqb%2BbYUp2qsjlUXL5JAHPEY2PThb0OnCHwiDwqWeDLj8mZoiUfeW0tW44doAfyZfHw0pK%2FxpGqesjvplKajN49J9j1ghLONXkIAA%3D\",\"legal_person_id_type\":1,\"longitude\":\"114.052243\",\"merchant_scale\":2,\"merchant_type\":0,\"mtime\":*************,\"name\":\"唐阿囍柳州螺蛳粉深圳民乐店\",\"owner_cellphone\":\"***********\",\"owner_name\":\"唐凤兰\",\"platform\":0,\"province\":\"广东省\",\"sn\":\"*************\",\"solicitor_id\":\"c97fcf9b-b300-11e5-9987-6c92bf21bb99\",\"status\":1,\"street_address\":\"民乐路东一巷与一区中路交叉口西120米\",\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"verify_status\":1,\"withdraw_mode\":2},\"contractUseAuth\":true,\"forceMicro\":true,\"bankAccount\":{\"bank_card_image\":\"https://private-images.shouqianba.com/sales-system-gateway/2025-06-16/81795d3e011546ebac22f62eedef9081.jpeg?Expires=**********&OSSAccessKeyId=STS.NX4cpFo4rApLZHN4VGQr4uNZR&Signature=yLw33%2FDkFiHBEiOHApBi8%2F4N8Mw%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5uBKMrygutT9rKnWG7%2F0FYSXf0YmovxsDz2IHhKfnVhAO8dtvw2mmhT7%2F0eloloU55fSAnfdME154xPtAimedOe5pTtsOFd18XgRDHCUEYMCSEH%2FL%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYUxdlMoP%2FdhQ%2FYd8qCnzKYkoJbIjo6y0x9ceP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6oKMPL7M%2FPQLnt5h223NW7DnWz5qwiq%2BRi2JZeZrXo%2B4H1S74SvGs3T0ybahIdG7lRJ8EPtayyv9%2BCIsgwSJGhqAAYffhTK70N9ZU1MlhR7o5ttB%2BRJTAfUEsmT6XmVir6M%2FoKFEz2vBBOOmqIx9M36RCKKiYhjvsD033VPYIuQLhkG6h0EcZt19wvQPN5NBGwK%2BN9%2BDJbaWntvnZ1QOcZHlMTMQB4U5rq2EptVw3rm0OZITY5DzXQgDEtSKxEHMLg3qIAA%3D\",\"bank_card_status\":1,\"bank_name\":\"中国银行\",\"branch_name\":\"中国银行深圳福永支行\",\"card_validity\":\"12/9999\",\"city\":\"广东省 深圳市\",\"clearing_number\":\"************\",\"ctime\":*************,\"deleted\":false,\"extra\":{\"contractMemo\":\"SUCCESS\"},\"hand_letter_of_authorization\":\"\",\"holder\":\"唐凤兰\",\"holder_id_back_ocr_status\":0,\"holder_id_back_photo\":\"https://private-images.shouqianba.com/sales-system-gateway/2025-06-16/cf1a0be2ffe248b99cdebd957a134deb.jpeg?Expires=**********&OSSAccessKeyId=STS.NX4cpFo4rApLZHN4VGQr4uNZR&Signature=mUa6wuV6bFQsGGMWiVrGmpaRq2k%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5uBKMrygutT9rKnWG7%2F0FYSXf0YmovxsDz2IHhKfnVhAO8dtvw2mmhT7%2F0eloloU55fSAnfdME154xPtAimedOe5pTtsOFd18XgRDHCUEYMCSEH%2FL%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYUxdlMoP%2FdhQ%2FYd8qCnzKYkoJbIjo6y0x9ceP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6oKMPL7M%2FPQLnt5h223NW7DnWz5qwiq%2BRi2JZeZrXo%2B4H1S74SvGs3T0ybahIdG7lRJ8EPtayyv9%2BCIsgwSJGhqAAYffhTK70N9ZU1MlhR7o5ttB%2BRJTAfUEsmT6XmVir6M%2FoKFEz2vBBOOmqIx9M36RCKKiYhjvsD033VPYIuQLhkG6h0EcZt19wvQPN5NBGwK%2BN9%2BDJbaWntvnZ1QOcZHlMTMQB4U5rq2EptVw3rm0OZITY5DzXQgDEtSKxEHMLg3qIAA%3D\",\"holder_id_card_address\":\"广西富川瑶族自治县朝东镇戏台脚村219号\",\"holder_id_card_issuing_authority\":\"富川瑶族自治县公安局\",\"holder_id_front_ocr_status\":0,\"holder_id_front_photo\":\"https://private-images.shouqianba.com/sales-system-gateway/2025-06-16/6e1d4b0cd1814d36a4dc636f715a75be.jpeg?Expires=**********&OSSAccessKeyId=STS.NX4cpFo4rApLZHN4VGQr4uNZR&Signature=VQLAFR%2FVospogSNzsKdYN%2BRkNGI%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5uBKMrygutT9rKnWG7%2F0FYSXf0YmovxsDz2IHhKfnVhAO8dtvw2mmhT7%2F0eloloU55fSAnfdME154xPtAimedOe5pTtsOFd18XgRDHCUEYMCSEH%2FL%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYUxdlMoP%2FdhQ%2FYd8qCnzKYkoJbIjo6y0x9ceP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6oKMPL7M%2FPQLnt5h223NW7DnWz5qwiq%2BRi2JZeZrXo%2B4H1S74SvGs3T0ybahIdG7lRJ8EPtayyv9%2BCIsgwSJGhqAAYffhTK70N9ZU1MlhR7o5ttB%2BRJTAfUEsmT6XmVir6M%2FoKFEz2vBBOOmqIx9M36RCKKiYhjvsD033VPYIuQLhkG6h0EcZt19wvQPN5NBGwK%2BN9%2BDJbaWntvnZ1QOcZHlMTMQB4U5rq2EptVw3rm0OZITY5DzXQgDEtSKxEHMLg3qIAA%3D\",\"holder_id_status\":0,\"id\":\"9dfcc8d0-1740-43f3-b398-7637abfd4ad5\",\"id_type\":1,\"id_validity\":\"20240228-20440228\",\"identity\":\"******************\",\"merchant_id\":\"f6b5aaa7-a7de-497e-b456-b53134a5096e\",\"mtime\":1750055889821,\"number\":\"6217582000029728540\",\"opening_number\":\"104584002308\",\"type\":1,\"verify_status\":2,\"version\":4},\"merchantBusinessLicense\":{\"ctime\":1750055861587,\"deleted\":false,\"extra\":{\"1\":[\"legal_person_id_type\",\"legal_person_id_card_front_photo\",\"legal_person_id_card_back_photo\",\"id_validity\"],\"2\":[\"legal_person_name\",\"legal_person_id_number\"]},\"id\":\"7775014\",\"id_validity\":\"20240228-20440228\",\"legal_person_id_card_address\":\"广西富川瑶族自治县朝东镇戏台脚村219号\",\"legal_person_id_card_back_photo\":\"https://private-images.shouqianba.com/sales-system-gateway/2025-06-16/cf1a0be2ffe248b99cdebd957a134deb.jpeg?Expires=**********&OSSAccessKeyId=STS.NVpqKYZeaV17PJ8etZP7TdsP8&Signature=mFPZ29Ewx187%2BtOyx9uapQniJYQ%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5XFOvHtt7pA4fPcUmyJgXQPXLh4i7b72jz2IHhKfnVhAO8dtvw2mmhT7%2F0eloloU55fSAnfdME154xPtAimedOe5pTtsOFe08LgQjjLV0Ypei0H%2FL%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYUxdlMoP%2FdhQ%2FYd8qCnzKYkoJbIjo6y0x9ceP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6sKGLbbM%2FPQLnt5h223NW7DnWz5qwiq%2BRi2JZeZrXo%2B4H1S74SvGs3T0ybahIdG7lRJ8EPtayyvGUql0gwSJGhqAAW2trarOWQQ3K86J9fud%2Br%2BSo8wtcIrUvV1raYLRtxDeFLuzyphnJLZlW0%2FpOQ2YVfrcJghFarbFfrS3lauf9V%2Fv5SW%2F7fNVx%2BZoI9BC94bNsJ3knFjEZKVP6geNAy7xmQU1ZS%2F3DOSA42AhPxj4GTrpqX2MZMVhnwytVcDDyW51IAA%3D\",\"legal_person_id_card_front_photo\":\"https://private-images.shouqianba.com/sales-system-gateway/2025-06-16/6e1d4b0cd1814d36a4dc636f715a75be.jpeg?Expires=**********&OSSAccessKeyId=STS.NVpqKYZeaV17PJ8etZP7TdsP8&Signature=WdWwSLNnaBDbJDDfGrn%2B57ooP4M%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5XFOvHtt7pA4fPcUmyJgXQPXLh4i7b72jz2IHhKfnVhAO8dtvw2mmhT7%2F0eloloU55fSAnfdME154xPtAimedOe5pTtsOFe08LgQjjLV0Ypei0H%2FL%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYUxdlMoP%2FdhQ%2FYd8qCnzKYkoJbIjo6y0x9ceP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6sKGLbbM%2FPQLnt5h223NW7DnWz5qwiq%2BRi2JZeZrXo%2B4H1S74SvGs3T0ybahIdG7lRJ8EPtayyvGUql0gwSJGhqAAW2trarOWQQ3K86J9fud%2Br%2BSo8wtcIrUvV1raYLRtxDeFLuzyphnJLZlW0%2FpOQ2YVfrcJghFarbFfrS3lauf9V%2Fv5SW%2F7fNVx%2BZoI9BC94bNsJ3knFjEZKVP6geNAy7xmQU1ZS%2F3DOSA42AhPxj4GTrpqX2MZMVhnwytVcDDyW51IAA%3D\",\"legal_person_id_card_issuing_authority\":\"富川瑶族自治县公安局\",\"legal_person_id_number\":\"******************\",\"legal_person_id_type\":1,\"legal_person_name\":\"唐凤兰\",\"merchant_id\":\"f6b5aaa7-a7de-497e-b456-b53134a5096e\",\"mtime\":1750055892705,\"tradeLicense\":\"\",\"type\":0,\"verify_status\":1},\"type\":\"1\"}");
        contractSubTaskHandler.settlementCardNonsupportAlarm(contractTask, contractSubTask, contractResponse);
    }

    @Test
    public void doHandleTest() {
        ContractResponse contractResponse = new ContractResponse();
        contractResponse.setCode(500);
        contractResponse.setMessage("aasa");
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(44291787272L);
        List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectByPTaskId(44291787272L);
        Optional<ContractSubTask> contractSubTaskOptional = contractSubTasks.stream()
            .filter(contractSubTask -> StringUtils.equals(contractSubTask.getContract_rule(), "bcs-contract"))
            .findFirst();
        ContractSubTask contractSubTask = contractSubTaskOptional.get();
        // contractSubTaskHandler.failHandler(contractTask, contractSubTask,new HashMap(),contractResponse,true);
    }
}