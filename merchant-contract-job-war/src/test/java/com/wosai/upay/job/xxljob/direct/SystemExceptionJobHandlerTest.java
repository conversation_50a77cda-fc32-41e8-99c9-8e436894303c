package com.wosai.upay.job.xxljob.direct;

import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.constant.CommonConstants;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SystemExceptionJobHandlerTest {

    @InjectMocks
    private SystemExceptionJobHandler systemExceptionJobHandler;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private ContractTaskBiz contractTaskBiz;

    @Mock
    private ChatBotUtil chatBotUtil;

    @Mock
    private Logger logger;

    private DirectJobParam directJobParam;

    @Before
    public void setUp() {
        // 如果需要，可以在这里进行设置，但目前不需要
        directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(10);
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "SystemExceptionJobHandler";
        String actualLockKey = systemExceptionJobHandler.getLockKey();
        assertEquals("The lock key should match the expected value", expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_SuccessfulExecution_UpdatesTaskVersions() {
        ContractTask task1 = new ContractTask().setId(1L).setVersion(1L);
        ContractTask task2 = new ContractTask().setId(2L).setVersion(2L);
        List<ContractTask> tasks = Arrays.asList(task1, task2);

        when(contractTaskMapper.selectSystemExceptionTask(CommonConstants.SYSTEM_ERROR_PRIORITY, directJobParam.getBatchSize()))
                .thenReturn(tasks);

        systemExceptionJobHandler.execute(directJobParam);

        verify(contractTaskBiz, times(1)).update(new ContractTask().setId(task1.getId()).setVersion(task1.getVersion() + 1));
        verify(contractTaskBiz, times(1)).update(new ContractTask().setId(task2.getId()).setVersion(task2.getVersion() + 1));
    }

    @Test
    public void execute_ExceptionDuringExecution_LogsErrorAndSendsNotification() {
        when(contractTaskMapper.selectSystemExceptionTask(CommonConstants.SYSTEM_ERROR_PRIORITY, directJobParam.getBatchSize()))
                .thenThrow(new RuntimeException("Test Exception"));

        systemExceptionJobHandler.execute(directJobParam);

        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
