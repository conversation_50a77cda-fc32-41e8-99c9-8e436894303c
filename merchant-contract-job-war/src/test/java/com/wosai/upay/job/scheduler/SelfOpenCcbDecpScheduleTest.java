package com.wosai.upay.job.scheduler;

import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSidePushService;
import com.wosai.app.dto.MerchantUserSimpleInfo;
import com.wosai.app.service.MerchantUserService;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.mapper.SelfOpenCcbDecpMapper;
import com.wosai.upay.job.model.DO.SelfOpenCcbDecp;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;

public class SelfOpenCcbDecpScheduleTest extends H2DbBaseTest {

    @InjectMocks
    private SelfOpenCcbDecpSchedule schedule = new SelfOpenCcbDecpSchedule();

    @Mock
    private MerchantService merchantService;

    @Mock
    private MerchantUserService merchantUserService;

    @Mock
    private ClientSideNoticeService clientSideNoticeService;

    @Mock
    private ClientSidePushService clientSidePushService;

    @Mock
    private RedisLock redisLock;

    @Autowired
    private SelfOpenCcbDecpMapper selfOpenCcbDecpMapper;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(schedule, "selfOpenCcbDecpMapper", selfOpenCcbDecpMapper);
        Mockito.doReturn(true).when(redisLock).lock(anyString(), anyString(), anyLong());
        Mockito.doReturn(CollectionUtil.hashMap(DaoConstants.ID, "merchantId")).when(merchantService).getMerchantBySn(any());
        Mockito.doReturn(new MerchantUserSimpleInfo()).when(merchantUserService).getSuperAdminSimpleInfoByMerchantId(any());
    }

    /**
     * 1 没有审核中的商户
     */
    @Test
    public void changeProcessToSuccessAndSendNotice01() {
        schedule.changeProcessToSuccessAndSendNotice();
    }

    /**
     * 有2个审核中的商户, 但是在24小时之内
     */
    @Test
    public void changeProcessToSuccessAndSendNotice02() {
        String merchantSn = "changeProcessToSuccessAndSendNotice02";
        SelfOpenCcbDecp selfOpenCcbDecp = new SelfOpenCcbDecp();
        selfOpenCcbDecp.setMerchant_sn(merchantSn);
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.PROCESS_OPEN_STATUS);
        selfOpenCcbDecp.setMtime(System.currentTimeMillis() - 50000L);
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp);

        SelfOpenCcbDecp selfOpenCcbDecp2 = new SelfOpenCcbDecp();
        selfOpenCcbDecp2.setMerchant_sn(merchantSn);
        selfOpenCcbDecp2.setOpen_status(SelfOpenCcbDecp.PROCESS_OPEN_STATUS);
        selfOpenCcbDecp2.setMtime(System.currentTimeMillis() - 60000L);
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp2);

        schedule.changeProcessToSuccessAndSendNotice();

        SelfOpenCcbDecp result = selfOpenCcbDecpMapper.selectByPrimaryKey(selfOpenCcbDecp.getId());
        assertEquals(SelfOpenCcbDecp.PROCESS_OPEN_STATUS, result.getOpen_status().intValue());
        result = selfOpenCcbDecpMapper.selectByPrimaryKey(selfOpenCcbDecp2.getId());
        assertEquals(SelfOpenCcbDecp.PROCESS_OPEN_STATUS, result.getOpen_status().intValue());
    }

    /**
     * 有2个审核中的商户, 1个在24小时之内
     */
    @Test
    public void changeProcessToSuccessAndSendNotice03() {
        String merchantSn = "changeProcessToSuccessAndSendNotice03";
        SelfOpenCcbDecp selfOpenCcbDecp1 = new SelfOpenCcbDecp();
        selfOpenCcbDecp1.setMerchant_sn(merchantSn);
        selfOpenCcbDecp1.setOpen_status(SelfOpenCcbDecp.PROCESS_OPEN_STATUS);
        selfOpenCcbDecp1.setMtime(System.currentTimeMillis() - 90000000L);
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp1);

        SelfOpenCcbDecp selfOpenCcbDecp2 = new SelfOpenCcbDecp();
        selfOpenCcbDecp2.setMerchant_sn(merchantSn);
        selfOpenCcbDecp2.setOpen_status(SelfOpenCcbDecp.PROCESS_OPEN_STATUS);
        selfOpenCcbDecp2.setMtime(System.currentTimeMillis() - 60000L);
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp2);

        schedule.changeProcessToSuccessAndSendNotice();

        SelfOpenCcbDecp result = selfOpenCcbDecpMapper.selectByPrimaryKey(selfOpenCcbDecp1.getId());
        assertEquals(SelfOpenCcbDecp.SUCCESS_OPEN_STATUS, result.getOpen_status().intValue());
        result = selfOpenCcbDecpMapper.selectByPrimaryKey(selfOpenCcbDecp2.getId());
        assertEquals(SelfOpenCcbDecp.PROCESS_OPEN_STATUS, result.getOpen_status().intValue());
    }

    /**
     * 有3个审核中的商户, 1个在24小时之内
     */
    @Test
    public void changeProcessToSuccessAndSendNotice04() {
        String merchantSn = "changeProcessToSuccessAndSendNotice04";
        SelfOpenCcbDecp selfOpenCcbDecp1 = new SelfOpenCcbDecp();
        selfOpenCcbDecp1.setMerchant_sn(merchantSn);
        selfOpenCcbDecp1.setOpen_status(SelfOpenCcbDecp.PROCESS_OPEN_STATUS);
        selfOpenCcbDecp1.setMtime(System.currentTimeMillis() - 90000000L);
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp1);

        SelfOpenCcbDecp selfOpenCcbDecp2 = new SelfOpenCcbDecp();
        selfOpenCcbDecp2.setMerchant_sn(merchantSn);
        selfOpenCcbDecp2.setOpen_status(SelfOpenCcbDecp.PROCESS_OPEN_STATUS);
        selfOpenCcbDecp2.setMtime(System.currentTimeMillis() - 60000L);
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp2);

        SelfOpenCcbDecp selfOpenCcbDecp3 = new SelfOpenCcbDecp();
        selfOpenCcbDecp3.setMerchant_sn(merchantSn);
        selfOpenCcbDecp3.setOpen_status(SelfOpenCcbDecp.PROCESS_OPEN_STATUS);
        selfOpenCcbDecp3.setMtime(System.currentTimeMillis() - 60000L);
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp3);

        schedule.changeProcessToSuccessAndSendNotice();

        SelfOpenCcbDecp result = selfOpenCcbDecpMapper.selectByPrimaryKey(selfOpenCcbDecp1.getId());
        assertEquals(SelfOpenCcbDecp.SUCCESS_OPEN_STATUS, result.getOpen_status().intValue());
        result = selfOpenCcbDecpMapper.selectByPrimaryKey(selfOpenCcbDecp2.getId());
        assertEquals(SelfOpenCcbDecp.PROCESS_OPEN_STATUS, result.getOpen_status().intValue());
        result = selfOpenCcbDecpMapper.selectByPrimaryKey(selfOpenCcbDecp3.getId());
        assertEquals(SelfOpenCcbDecp.PROCESS_OPEN_STATUS, result.getOpen_status().intValue());
    }
}