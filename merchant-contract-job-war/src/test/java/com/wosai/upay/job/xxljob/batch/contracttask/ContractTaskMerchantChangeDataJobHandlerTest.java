package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.xxljob.context.ContractTaskContext;
import com.wosai.upay.side.service.GeneralRuleService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ContractTaskMerchantChangeDataJobHandlerTest {

    @Mock
    private GeneralRuleService generalRuleService;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @Mock
    private SubTaskHandlerContext subTaskHandlerContext;

    @Mock
    private ChatBotUtil chatBotUtil;

    @InjectMocks
    private ContractTaskMerchantChangeDataJobHandler handler;

    private BatchJobParam param;
    private ContractTaskContext context;
    private ContractTask contractTask;
    private ContractSubTask contractSubTask;

    @Before
    public void setUp() {
        param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        ContractTask task = new ContractTask();
        task.setMerchant_sn("12345");
        context = new ContractTaskContext(task, "reviewComplete");

        contractTask = new ContractTask();
        contractSubTask = new ContractSubTask();
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsContractTaskContextList() {
        // 准备
        String reviewComplete = "2023-01-01";
        ContractTask task = new ContractTask();
        task.setId(1L);
        List<ContractTask> tasks = Arrays.asList(task);

        when(generalRuleService.getFirstWeekDayAfterDate(Mockito.anyString())).thenReturn(reviewComplete);
        when(contractTaskMapper.selectMerchantChangeDataTasks(Mockito.anyString(), Mockito.eq(10))).thenReturn(tasks);

        List<ContractTaskContext> result = handler.queryTaskItems(param);

        assertEquals(1, result.size());
        assertEquals(reviewComplete, result.get(0).getReviewComplete());
        assertEquals(task, result.get(0).getTask());
    }

    @Test
    public void queryTaskItems_NoTasks_ReturnsEmptyList() {
        // 准备
        when(generalRuleService.getFirstWeekDayAfterDate(Mockito.anyString())).thenReturn("2023-01-01");
        when(contractTaskMapper.selectMerchantChangeDataTasks(Mockito.anyString(), Mockito.eq(10))).thenReturn(Arrays.asList());

        List<ContractTaskContext> result = handler.queryTaskItems(param);

        assertEquals(0, result.size());
    }

    @Test
    public void getLockKey_ValidMerchantSn_ReturnsCorrectLockKey() {
        String lockKey = handler.getLockKey(context);
        assertEquals("ContractTaskMerchantChangeDataJobHandler:12345", lockKey);
    }

    @Test
    public void getLockKey_EmptyMerchantSn_ReturnsCorrectLockKey() {
        context.getTask().setMerchant_sn("");
        String lockKey = handler.getLockKey(context);
        assertEquals("ContractTaskMerchantChangeDataJobHandler:", lockKey);
    }

    @Test
    public void getLockKey_NullMerchantSn_ReturnsCorrectLockKey() {
        context.getTask().setMerchant_sn(null);
        String lockKey = handler.getLockKey(context);
        assertEquals("ContractTaskMerchantChangeDataJobHandler:null", lockKey);
    }

    @Test
    public void doHandleSingleData_PendingStatus_CompleteAtSet() {
        contractTask.setStatus(TaskStatus.PENDING.getVal());
        contractTask.setMerchant_sn("123");
        contractTask.setId(1L);

        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus("123", 1L, 1))
                .thenReturn(Collections.emptyList());

        handler.doHandleSingleData(context);

        assertEquals(new Date(2023 - 1900, 9, 1), contractTask.getComplete_at());
    }

    @Test
    public void doHandleSingleData_NonPendingStatus_CompleteAtUnchanged() {
        contractTask.setStatus(TaskStatus.SUCCESS.getVal());
        contractTask.setMerchant_sn("123");
        contractTask.setId(1L);

        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus("123", 1L, 1))
                .thenReturn(Collections.emptyList());

        handler.doHandleSingleData(context);

        assertEquals(null, contractTask.getComplete_at());
    }

    @Test
    public void doHandleSingleData_NoSubTasks_LogInfo() {
        contractTask.setStatus(TaskStatus.PENDING.getVal());
        contractTask.setMerchant_sn("123");
        contractTask.setId(1L);

        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus("123", 1L, 1))
                .thenReturn(Collections.emptyList());

        handler.doHandleSingleData(context);

        verify(contractSubTaskMapper, times(1)).selectByMerchantSnAndPTaskIdAndScheduleStatus("123", 1L, 1);
    }

    @Test
    public void doHandleSingleData_SubTasksHandled() {
        contractTask.setStatus(TaskStatus.PENDING.getVal());
        contractTask.setMerchant_sn("123");
        contractTask.setId(1L);

        contractSubTask.setMerchant_sn("123");
        contractSubTask.setChannel("test_channel");
        contractSubTask.setContract_id("12345");

        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus("123", 1L, 1))
                .thenReturn(Arrays.asList(contractSubTask));

        handler.doHandleSingleData(context);

        try {
            verify(subTaskHandlerContext, times(1)).handle(contractTask, contractSubTask);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void doHandleSingleData_ExceptionHandled() {
        contractTask.setStatus(TaskStatus.PENDING.getVal());
        contractTask.setMerchant_sn("123");
        contractTask.setId(1L);

        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus("123", 1L, 1))
                .thenThrow(new RuntimeException("Test Exception"));

        handler.doHandleSingleData(context);

        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(Mockito.contains("Test Exception"));
    }
}
