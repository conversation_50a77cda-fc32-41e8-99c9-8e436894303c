package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.ContractTask;
import org.apache.commons.collections.MapUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;


public class ContractTaskResultServiceTest extends BaseTest {

    @Autowired
    private ContractTaskResultService contractTaskResultService;

    @Test
    public void getContractasksTest() {
        Map queryFilter = CollectionUtil.hashMap("pageNum", 1, "pageSize", 10,"merchant_sn","21690003580830");
        Map<String, Object> contractasks = contractTaskResultService.getContractasks(queryFilter);
        Object data = MapUtils.getObject(contractasks, "data");
        List<ContractTask> data1 = (List<ContractTask>) data;
        System.out.println(JSON.toJSONString(data1));
//        System.out.println("【contracttask】" + MapUtils.getObject(contractasks,"data"));
    }


    @Test
    public void selectByPrimaryKey() {
        contractTaskResultService.selectByPrimaryKey(2539169L);
    }
}
