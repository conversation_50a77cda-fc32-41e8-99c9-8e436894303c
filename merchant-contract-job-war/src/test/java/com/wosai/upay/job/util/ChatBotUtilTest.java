package com.wosai.upay.job.util;

import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.env.Environment;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(SpringRunner.class)
public class ChatBotUtilTest {

    private ChatBotUtil chatBotUtil;

    @MockBean
    private ApplicationApolloConfig applicationApolloConfig;

    @MockBean
    private Environment environment;

    @Before
    public void setUp() {
        chatBotUtil = new ChatBotUtil();
        ReflectionTestUtils.setField(chatBotUtil, "applicationApolloConfig", applicationApolloConfig);
        ReflectionTestUtils.setField(chatBotUtil, "environment", environment);
        Mockito.doReturn(new String[]{"dev"}).when(environment).getActiveProfiles();
    }

    @Test
    public void test() {
        chatBotUtil.sendMessageToPaymentModeChangeChatBot("测试机器人");
    }
}
