package com.wosai.upay.job.refactor.Integration.service;

import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.dto.request.AcquirerSupportSettlementRepDTO;
import com.wosai.upay.job.model.dto.response.AcquirerSupportSettlementRspDTO;
import com.wosai.upay.job.service.AcquirerSupportSettlementService;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 收单机构支持的账户结算类型服务测试
 *
 * <AUTHOR>
 * @date 2024/4/3 10:19
 */
@ActiveProfiles("dev")
public class AcquirerSupportSettlementServiceTest extends BaseTest {

    @Resource
    private AcquirerSupportSettlementService acquirerSupportSettlementService;

    @Test
    public void testListByLicenseType() {
        List<AcquirerSupportSettlementRspDTO> acquirerSupportSettlementRspDTOS = acquirerSupportSettlementService.listByLicenseType(BusinessLicenseTypeEnum.ENTERPRISE.getValue());
        assertThat(acquirerSupportSettlementRspDTOS).isNotNull();
        assertThat(acquirerSupportSettlementRspDTOS).isNotEmpty();
        assertThat(acquirerSupportSettlementRspDTOS).hasSize(5);
    }

    @Test
    public void testUpdateAcquirerSupportSettlement() {
        AcquirerSupportSettlementRepDTO acquirerSupportSettlementRepDTO = new AcquirerSupportSettlementRepDTO();
        acquirerSupportSettlementRepDTO.setId(1);
        acquirerSupportSettlementRepDTO.setAcquirer("lklV3");
        acquirerSupportSettlementRepDTO.setBusinessLicenseType(0);
        acquirerSupportSettlementRepDTO.setLegalPrivateSupportType(0);
        acquirerSupportSettlementRepDTO.setNonLegalPrivateSupportType(0);
        acquirerSupportSettlementRepDTO.setCommonPublicSupportType(0);
        acquirerSupportSettlementRepDTO.setOtherPublicSupportType(0);
        AcquirerSupportSettlementRepDTO acquirerSupportSettlementRepDTO2 = new AcquirerSupportSettlementRepDTO();
        acquirerSupportSettlementRepDTO2.setId(2);
        acquirerSupportSettlementRepDTO2.setAcquirer("lklV3");
        acquirerSupportSettlementRepDTO2.setBusinessLicenseType(0);
        acquirerSupportSettlementRepDTO2.setLegalPrivateSupportType(0);
        acquirerSupportSettlementRepDTO2.setNonLegalPrivateSupportType(0);
        acquirerSupportSettlementRepDTO2.setCommonPublicSupportType(0);
        acquirerSupportSettlementRepDTO2.setOtherPublicSupportType(0);
        Integer updateRows = acquirerSupportSettlementService.updateAcquirerSupportSettlement(Lists.newArrayList(acquirerSupportSettlementRepDTO, acquirerSupportSettlementRepDTO2));
        assertThat(updateRows).isEqualTo(2);

    }

}
