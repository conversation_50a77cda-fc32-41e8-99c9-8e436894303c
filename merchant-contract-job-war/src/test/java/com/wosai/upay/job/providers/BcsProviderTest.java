package com.wosai.upay.job.providers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.wosai.upay.job.model.ContractChannel;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.merchant.contract.model.ContractResponse;

public class BcsProviderTest extends BaseTest {

    @Autowired
    private BcsProvider bcsProvider;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Test
    public void processInsertTaskByRuleTest() {
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(44291794833L);
        ContractSubTask contractSubTask =
            contractSubTaskMapper.selectByContractIdAndContractRule("20250801461430100002QAW11370003", "bcs-contract");
        bcsProvider.processInsertTaskByRule(contractTask, null, contractSubTask);
    }

    @Test
    @Rollback(false)
    public void processBcsMerchantNoTest() {
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(44291790529L);
        ContractResponse contractResponse = new ContractResponse();
        Map<String, Object> tradeParam = new HashMap<>();
        tradeParam.put("mchtNo", "20250730461430100002QAW00000000");
        contractResponse.setTradeParam(tradeParam);
        String eventContext = contractTask.getEvent_context();
        Map<String, Object> objectMap =
            JSONObject.parseObject(eventContext, new TypeReference<Map<String, Object>>() {});
        // bcsProvider.processBcsMerchantNo(contractTask, contractResponse, objectMap);
    }

    @Test
    public void sendSignUrl2AppTest() {
        HashMap<String, Object> merchantMap = new HashMap<>();
        merchantMap.put("sn", "**************");
        merchantMap.put("id", "24d21312-57df-4a01-b30f-2544cc40df39");
        bcsProvider.sendSignUrl2App(
            "http://test.bankofchangsha.com/uaps-apiuat//api-enter/index.html?token=ElectronAPI_668e3c2d-b3b7-4f4f-afeb-b3596d926519&mchtEntryNumber=20250801461430100002QAW11180002&t=*************",
            merchantMap);
    }

    @Test
    public void processSignInfoTest() {
        String content =
            "{\"businessFail\":false,\"code\":200,\"message\":\"签约链接生成成功\",\"requestParam\":{\"legalPersonIdCardFrontPhotoUrl\":\"https://images.wosaimg.com/14/0fb51c9815c5abc0bde9a8f11407e1.jpg\",\"legalPersonIdCardBackPhotoUrl\":\"https://images.wosaimg.com/14/0fb51c9815c5abc0bde9a8f11407e1.jpg\",\"indoorOnlyScenePhotoUrl\":\"https://private-images.shouqianba.com/sales-system-gateway/2025-08-05/b1c60b55d7cc4bc3bdc19c2649e92c1a.jpeg?Expires=**********&OSSAccessKeyId=STS.NZAX6kfiYewkTzGj9g2ZXah7P&Signature=7tGeiSMMP3M7OvvFSFkswAinqGk%3D&security-token=CAIS4AJ1q6Ft5B2yfSjIr5n0E4zfi7Z40rWAVlz2jjkyPtV0jq2csjz2IHlJdXFrB%2B0bvv4xlW9V5%2FYalrZwVpVIX0rJccZrq49O%2B0a6etOe5pDuvOZZ1MXgSTLMU5NVFnY9OLeXdsjUX9vwQXKm%2F3YB7NmXXDGmWEPfQv%2FtoJV7b9MRcxClZD5dfrl%2FLRdjr8loXhm4d4zaUHjQj3HXEVBjtydllGp78t7f%2BMCH7QfEh1CIoY185aaJe8T0MpYzZs8iDIvsgbcqKZCsinAAt0J4k45tl7FB9Dv9udWQPkJc%2BR3uMZCPq4A%2BdFQnO%2FVqQfAV86WtxaViwPfKncH81h8II%2BhIVziaWI273Nsf7UdcileAH1ySHgMusrjnXvGd22uvceCBhJJUcJP3pivFy%2FiAnq0ejgMjbGqLA2HtAuHOq4otmoFO9G6jnpHxLDukaP70v2tmfPF1ASwmXxuAwj6wGsduGoABeyJqJXS4QXEZD9YL9uFM1mYyQMKE2AX%2FVqnBHXQk%2FMEMc5DO1%2FW6nuI%2FyJnhrWUN57C1IVgYXgM4Vz9RG%2BGVydA%2FVHd6%2FNMsAI30WaMS0uZs8CI3928VW1ALBFgiTVMJ8%2Fk2AnX4JXjVsrEcGfKWrFNxld5ZCvXhMFnTkLp8ScAgAA%3D%3D\",\"brandPhotoUrl\":\"https://images.wosaimg.com/14/0fb51c9815c5abc0bde9a8f11407e1.jpg\",\"brandOnlyScenePhotoUrl\":\"https://images.wosaimg.com/14/0fb51c9815c5abc0bde9a8f11407e1.jpg\",\"businessLicensePhotoUrl\":\"https://images.wosaimg.com/14/0fb51c9815c5abc0bde9a8f11407e1.jpg\"},\"responseParam\":{\"legalPersonBackPhotoContentId\":\"a0Vb8L1YBnshlYsz\",\"legalPersonFrontPhotoContentId\":\"u49mSaKNNz3tKoD2\",\"indoorOnlyScenePhotoContentId\":\"CZxwXr4aN58smZhj\",\"contractId\":\"20250805461430100002QAW08060468\",\"brandOnlyScenePhotoContentId\":\"vzYHQSl1FP8m0y24\",\"businessLicensePhotoContentId\":\"efZ4qMQbgztNa3NG\",\"storeFrontPhotoContentId\":\"uqpJsSZ0g2Xqtcrz\"},\"success\":true,\"systemFail\":false,\"tradeParam\":{\"legalPersonBackPhotoContentId\":\"a0Vb8L1YBnshlYsz\",\"legalPersonFrontPhotoContentId\":\"u49mSaKNNz3tKoD2\",\"indoorOnlyScenePhotoContentId\":\"CZxwXr4aN58smZhj\",\"contractId\":\"20250805461430100002QAW08060468\",\"brandOnlyScenePhotoContentId\":\"vzYHQSl1FP8m0y24\",\"businessLicensePhotoContentId\":\"efZ4qMQbgztNa3NG\",\"storeFrontPhotoContentId\":\"uqpJsSZ0g2Xqtcrz\"}}";
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(44291806544L);
        ContractResponse contractResponse = JSONObject.parseObject(content, ContractResponse.class);
        bcsProvider.processSignInfo(contractTask, contractResponse, contractTask.getEventContext());
    }

    @Test
    public void processUpdateTaskByRuleTest() {
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(44291813815L);
        List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectByPTaskId(44291813815L);
        Optional<ContractSubTask> contractSubTaskOptional = contractSubTasks.stream()
            .filter(contractSubTask -> StringUtils.equals(contractSubTask.getChannel(), "bcs")).findFirst();
        bcsProvider.processUpdateTaskByRule(contractTask, new ContractChannel(), contractSubTaskOptional.get());
    }
}