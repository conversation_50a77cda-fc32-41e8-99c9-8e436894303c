package com.wosai.upay.job.biz.directparams;


import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.RuleBiz;
import com.wosai.upay.job.model.directparams.BestpayDirectParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;


public class BestpayDirectParamsBizTest extends BaseTest {


    @Autowired
    BestpayDirectParamsBiz bestpayDirectParamsBiz;
    @MockBean
    MerchantProviderParamsBiz merchantProviderParamsBiz;
    @MockBean
    TradeConfigService tradeConfigService;
    @MockBean
    SupportService supportService;
    @MockBean
    MerchantService merchantService;
    @MockBean
    private RuleBiz ruleBiz;

    @Before
    public void reflect() {
        ReflectionTestUtils.setField(bestpayDirectParamsBiz, "tradeConfigService", tradeConfigService);
        ReflectionTestUtils.setField(bestpayDirectParamsBiz, "supportService", supportService);
        ReflectionTestUtils.setField(bestpayDirectParamsBiz, "merchantService", merchantService);

        Mockito.doReturn(new HashMap<>()).when(tradeConfigService).getMerchantConfigByMerchantIdAndPayway(Mockito.anyString(), Mockito.anyInt());
        Map merchant = CollectionUtil.hashMap("merchant_id", "test_id", "sn", "sn");
        Mockito.doReturn(merchant).when(merchantService).getMerchant(Mockito.anyString());
    }

    @Test
    public void addDirectParams() {
        BestpayDirectParams baseParam=new BestpayDirectParams();
        baseParam.setMerchant_id("test_id");
        baseParam.setMerchant_sn("sn");
        BestpayDirectParams.BestpayParams bestpayParams=new BestpayDirectParams.BestpayParams();
        bestpayParams.setMerchant_id("test_id");
        baseParam.setBestpay_trade_params(bestpayParams);
        bestpayDirectParamsBiz.addDirectParams(baseParam);
    }

    @Test
    public void deleteDirectParams() {
        MerchantProviderParamsDto paramsDto=  new MerchantProviderParamsDto();
        paramsDto.setExtra(new HashMap<>()).setMerchant_sn("sn");
        bestpayDirectParamsBiz.deleteDirectParams(paramsDto,"3","0.38");
    }

    @Test
    public void handleDirectParams() {
        bestpayDirectParamsBiz.handleDirectParams(new MerchantProviderParamsCustomDto());
    }
}
