package com.wosai.upay.job.refactor.Integration.service;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.businesslog.LogPlatformEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.model.dto.request.ChangeAcquirerReqDTO;
import com.wosai.upay.job.model.dto.response.AcquirerChangeTaskRspDTO;
import com.wosai.upay.job.service.AcquirerService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 收单机构测试
 *
 * <AUTHOR>
 */
@Slf4j
public class AcquirerChangeServiceTest extends BaseTest {

    @Resource
    private AcquirerService acquirerService;


    @Test
    public void testGetByTaskId() {
        AcquirerChangeTaskRspDTO rsp = acquirerService.getAcquirerChangeTaskById(650633);
        assertThat(rsp).isNotNull();
        assertThat(rsp.getMerchantSn()).isEqualTo("21690003556117");
    }

    @Test
    public void testJson() {
        LogParamsDto logParamsDto = new LogParamsDto();
        logParamsDto.setLogPlatformEnum(LogPlatformEnum.CRM_APP);
        logParamsDto.setRemark("test");
        logParamsDto.setSceneTemplateCode("11111111");
        LogParamsDto logParamsDto1 = JSON.parseObject(JSON.toJSONString(logParamsDto), LogParamsDto.class);
        assertThat(logParamsDto1.getSceneTemplateCode()).isEqualTo("11111111");
    }

    @Test
    public void testChangeAcquirer() {
        ChangeAcquirerReqDTO req = new ChangeAcquirerReqDTO
                .Builder("21690003912887", AcquirerTypeEnum.HAI_KE).immediately(true).build();
        LogParamsDto logParamsDto = new LogParamsDto();
        logParamsDto.setLogPlatformEnum(LogPlatformEnum.APP);
        logParamsDto.setRemark("test");
        Integer taskId = acquirerService.submitChangeAcquirerTask(req, logParamsDto);
        assertThat(taskId).isGreaterThan(0);
    }

    @Resource
    private BusinessLogBiz businessLogBiz;

    @Test
    public void testSendLog() {
        LogParamsDto logParamsDto = new LogParamsDto();
        logParamsDto.setLogPlatformEnum(LogPlatformEnum.APP);
        logParamsDto.setRemark("test");
        businessLogBiz.sendChangeAcquirerLog("9e4bdefd-8f03-47ab-852b-b2ee11b26eca", "lklV3", "haike", "msh", logParamsDto);
    }

}
