package com.wosai.upay.job.xxljob.batch.providerterminal;

import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.biz.ProviderTerminalBiz;

import static com.wosai.upay.job.constant.ProviderTerminalConstants.SUCCESS;
import static com.wosai.upay.job.constant.ProviderTerminalConstants.FAIL;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.mapper.ProviderTerminalTaskMapper;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import com.wosai.upay.job.repository.ProviderTerminalTaskRepository;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UnbindSqbTerminalJobHandlerTest {

    @Mock
    private ProviderTerminalTaskMapper taskMapper;

    @Mock
    private ProviderTerminalBiz providerTerminalBiz;

    @Mock
    private ProviderTerminalTaskRepository taskRepository;

    @InjectMocks
    private UnbindSqbTerminalJobHandler unbindSqbTerminalJobHandler;

    private ProviderTerminalTask providerTerminalTaskA;

    private ProviderTerminalTask providerTerminalTaskB;

    private BatchJobParam param;

    @Before
    public void setUp() {
        providerTerminalTaskA = new ProviderTerminalTask();
        param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        providerTerminalTaskB = new ProviderTerminalTask();
        providerTerminalTaskB.setId(1L);
        providerTerminalTaskB.setMerchant_sn("testMerchant");
    }

    @Test
    public void getLockKey_WithNonNullId_ReturnsCorrectLockKey() {
        providerTerminalTaskA.setId(123L);
        String expectedLockKey = "UnbindSqbTerminalJobHandler:123";
        String actualLockKey = unbindSqbTerminalJobHandler.getLockKey(providerTerminalTaskA);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void getLockKey_WithNullId_ReturnsLockKeyWithNull() {
        providerTerminalTaskA.setId(null);
        String expectedLockKey = "UnbindSqbTerminalJobHandler:null";
        String actualLockKey = unbindSqbTerminalJobHandler.getLockKey(providerTerminalTaskA);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsTaskList() {
        List<ProviderTerminalTask> expectedTasks = Arrays.asList(
                new ProviderTerminalTask().setId(1L),
                new ProviderTerminalTask().setId(2L)
        );

        when(taskMapper.selectMerchantSnByPriorityAndType(any(String.class), any(String.class), eq(ProviderTerminalTaskTypeEnum.UNBIND_TERMINAL_ALL_SUB_MCH.getType()), eq(10)))
                .thenReturn(expectedTasks);

        List<ProviderTerminalTask> actualTasks = unbindSqbTerminalJobHandler.queryTaskItems(param);

        assertEquals(expectedTasks, actualTasks);
    }

    @Test
    public void doHandleSingleData_StatusSuccess_NoAction() {
        providerTerminalTaskB.setStatus(SUCCESS);
        when(taskMapper.selectByPrimaryKey(providerTerminalTaskB.getId())).thenReturn(providerTerminalTaskB);

        unbindSqbTerminalJobHandler.doHandleSingleData(providerTerminalTaskB);

        verify(taskMapper, times(1)).selectByPrimaryKey(providerTerminalTaskB.getId());
        verify(providerTerminalBiz, never()).unbindTerminal(any(ProviderTerminalTask.class));
        verify(taskRepository, never()).updateTaskStatusById(anyLong(), anyInt(), anyString());
    }

    @Test
    public void doHandleSingleData_StatusFail_NoAction() {
        providerTerminalTaskB.setStatus(FAIL);
        when(taskMapper.selectByPrimaryKey(providerTerminalTaskB.getId())).thenReturn(providerTerminalTaskB);

        unbindSqbTerminalJobHandler.doHandleSingleData(providerTerminalTaskB);

        verify(taskMapper, times(1)).selectByPrimaryKey(providerTerminalTaskB.getId());
        verify(providerTerminalBiz, never()).unbindTerminal(any(ProviderTerminalTask.class));
        verify(taskRepository, never()).updateTaskStatusById(anyLong(), anyInt(), anyString());
    }

    @Test
    public void doHandleSingleData_StatusProcessing_UnbindTerminalCalled() {
        providerTerminalTaskB.setStatus(1);
        when(taskMapper.selectByPrimaryKey(providerTerminalTaskB.getId())).thenReturn(providerTerminalTaskB);

        unbindSqbTerminalJobHandler.doHandleSingleData(providerTerminalTaskB);

        verify(taskMapper, times(1)).selectByPrimaryKey(providerTerminalTaskB.getId());
        verify(providerTerminalBiz, times(1)).unbindTerminal(providerTerminalTaskB);
        verify(taskRepository, never()).updateTaskStatusById(anyLong(), anyInt(), anyString());
    }

    @Test
    public void doHandleSingleData_ContractBizException_TaskStatusUpdated() {
        providerTerminalTaskB.setStatus(1);
        when(taskMapper.selectByPrimaryKey(providerTerminalTaskB.getId())).thenReturn(providerTerminalTaskB);
        doThrow(new ContractBizException("Test Exception")).when(providerTerminalBiz).unbindTerminal(providerTerminalTaskB);

        unbindSqbTerminalJobHandler.doHandleSingleData(providerTerminalTaskB);

        verify(taskMapper, times(1)).selectByPrimaryKey(providerTerminalTaskB.getId());
        verify(providerTerminalBiz, times(1)).unbindTerminal(providerTerminalTaskB);
        verify(taskRepository, times(1)).updateTaskStatusById(providerTerminalTaskB.getId(), FAIL, "Test Exception");
    }

    @Test
    public void doHandleSingleData_OtherException_NoStatusUpdate() {
        providerTerminalTaskB.setStatus(1);
        when(taskMapper.selectByPrimaryKey(providerTerminalTaskB.getId())).thenReturn(providerTerminalTaskB);
        doThrow(new RuntimeException("Test Exception")).when(providerTerminalBiz).unbindTerminal(providerTerminalTaskB);

        unbindSqbTerminalJobHandler.doHandleSingleData(providerTerminalTaskB);

        verify(taskMapper, times(1)).selectByPrimaryKey(providerTerminalTaskB.getId());
        verify(providerTerminalBiz, times(1)).unbindTerminal(providerTerminalTaskB);
        verify(taskRepository, never()).updateTaskStatusById(anyLong(), anyInt(), anyString());
    }
}
