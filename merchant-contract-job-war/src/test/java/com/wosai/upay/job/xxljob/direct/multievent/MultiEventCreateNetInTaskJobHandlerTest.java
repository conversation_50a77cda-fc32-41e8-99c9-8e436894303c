package com.wosai.upay.job.xxljob.direct.multievent;

import com.wosai.upay.job.handlers.MultiProviderEventHandlerContext;
import com.wosai.upay.job.mapper.MultiProviderContractEventMapper;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MultiEventCreateNetInTaskJobHandlerTest {

    @Mock
    private MultiProviderContractEventMapper multiEventMapper;

    @Mock
    private MultiProviderEventHandlerContext multiProviderEventHandlerContext;

    @Mock
    private ChatBotUtil chatBotUtil;

    @InjectMocks
    private MultiEventCreateNetInTaskJobHandler multiEventCreateNetInTaskJobHandler;

    @InjectMocks
    private MultiEventCreateNetInTaskJobHandler handler;

    private DirectJobParam param;

    @Before
    public void setUp() {
        param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "MultiEventCreateNetInTaskJobHandler";
        String actualLockKey = handler.getLockKey();
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_NoEvents_ReturnsImmediately() throws Exception {
        when(multiEventMapper.selectPendingNetInMultiEvents(anyString(), anyInt())).thenReturn(new ArrayList<>());

        multiEventCreateNetInTaskJobHandler.execute(param);

        verify(multiEventMapper, times(1)).selectPendingNetInMultiEvents(anyString(), anyInt());
        verify(multiProviderEventHandlerContext, never()).handle(any(MultiProviderContractEvent.class));
        verify(chatBotUtil, never()).sendMessageToContractWarnChatBot(anyString());
    }

    @Test
    public void execute_EventsProcessedSuccessfully() throws Exception {
        List<MultiProviderContractEvent> events = new ArrayList<>();
        MultiProviderContractEvent event = new MultiProviderContractEvent();
        event.setMerchant_sn("testMerchant");
        events.add(event);

        when(multiEventMapper.selectPendingNetInMultiEvents(anyString(), anyInt())).thenReturn(events);

        multiEventCreateNetInTaskJobHandler.execute(param);

        verify(multiEventMapper, times(1)).selectPendingNetInMultiEvents(anyString(), anyInt());
        verify(multiProviderEventHandlerContext, times(1)).handle(event);
        verify(chatBotUtil, never()).sendMessageToContractWarnChatBot(anyString());
    }

    @Test
    public void execute_EventProcessingFails() throws Exception {
        List<MultiProviderContractEvent> events = new ArrayList<>();
        MultiProviderContractEvent event = new MultiProviderContractEvent();
        event.setMerchant_sn("testMerchant");
        events.add(event);

        when(multiEventMapper.selectPendingNetInMultiEvents(anyString(), anyInt())).thenReturn(events);
        doThrow(new Exception("Processing error")).when(multiProviderEventHandlerContext).handle(event);

        multiEventCreateNetInTaskJobHandler.execute(param);

        verify(multiEventMapper, times(1)).selectPendingNetInMultiEvents(anyString(), anyInt());
        verify(multiProviderEventHandlerContext, times(1)).handle(event);
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot("testMerchant入网事件处理异常 Processing error");
    }

    @Test
    public void execute_ExceptionDuringEventRetrieval() throws Exception {
        when(multiEventMapper.selectPendingNetInMultiEvents(anyString(), anyInt())).thenThrow(new RuntimeException("Retrieval error"));

        multiEventCreateNetInTaskJobHandler.execute(param);

        verify(multiEventMapper, times(1)).selectPendingNetInMultiEvents(anyString(), anyInt());

        verify(multiProviderEventHandlerContext, never()).handle(any(MultiProviderContractEvent.class));
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot("入网事件处理异常 Retrieval error");
    }
}
