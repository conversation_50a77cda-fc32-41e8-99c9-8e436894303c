package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.google.common.collect.Lists;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BankDirectContractStatusJobHandlerTest {

    @InjectMocks
    private BankDirectContractStatusJobHandler bankDirectContractStatusJobHandler;

    @Mock
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private ChatBotUtil chatBotUtil;

    @Before
    public void setUp() {
        // 如果需要，可以在这里进行设置
        // 在测试之前设置任何必要的初始状态
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        // 调用被测试的方法
        String lockKey = bankDirectContractStatusJobHandler.getLockKey();

        // 验证返回的锁键是否正确
        Assert.assertEquals("BankDirectContractStatusJobHandler", lockKey);
    }

    @Test
    public void execute_NormalExecution_Success() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());

        BankDirectApply apply = new BankDirectApply();
        apply.setId(1L);
        apply.setMerchant_sn("merchant1");
        apply.setTask_id(100L);
        apply.setDev_code("dev1");

        ContractTask contractTask = new ContractTask();
        contractTask.setStatus(TaskStatus.SUCCESS.getVal());

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Lists.newArrayList(apply));
        when(contractTaskMapper.selectByPrimaryKey(100L)).thenReturn(contractTask);

        bankDirectContractStatusJobHandler.execute(param);

        verify(bankDirectApplyMapper, times(1)).listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt());
        verify(contractTaskMapper, times(1)).selectByPrimaryKey(100L);
    }

    @Test
    public void execute_ExceptionDuringExecution_SendWarningMessage() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenThrow(new RuntimeException("Test exception"));

        bankDirectContractStatusJobHandler.execute(param);

        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }

    @Test
    public void execute_ExceptionDuringApplyProcessing_SendWarningMessage() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());

        BankDirectApply apply = new BankDirectApply();
        apply.setId(1L);
        apply.setMerchant_sn("merchant1");
        apply.setTask_id(100L);
        apply.setDev_code("dev1");

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Lists.newArrayList(apply));
        when(contractTaskMapper.selectByPrimaryKey(100L)).thenThrow(new RuntimeException("Test exception"));

        bankDirectContractStatusJobHandler.execute(param);

        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
