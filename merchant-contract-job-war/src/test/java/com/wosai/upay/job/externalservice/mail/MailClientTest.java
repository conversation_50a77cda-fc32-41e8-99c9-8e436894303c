package com.wosai.upay.job.externalservice.mail;

import com.github.rholder.retry.RetryException;
import com.wosai.upay.job.externalservice.mail.model.MailSendReq;
import com.wosai.upay.job.externalservice.mail.model.MailSendResp;
import com.wosai.upay.job.util.ChatBotUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MailClient单元测试
 * 涵盖所有分支和异常场景
 */
@RunWith(MockitoJUnitRunner.class)
public class MailClientTest {

    private MailClient mailClient;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private ChatBotUtil chatBotUtil;

    @Before
    public void setUp() {
        mailClient = new MailClient();
        ReflectionTestUtils.setField(mailClient, "restTemplate", restTemplate);
        ReflectionTestUtils.setField(mailClient, "chatBotUtil", chatBotUtil);
        ReflectionTestUtils.setField(mailClient, "mailGateway", "http://test-mail-gateway.com");
    }

    @Test
    public void testSendEmail_Success() {
        // 准备测试数据
        MailSendReq req = new MailSendReq()
                .setId(123L)
                .setTo("<EMAIL>")
                .setContent("测试邮件内容");

        Map<String, Object> successResponse = new HashMap<>();
        successResponse.put("errcode", 0);
        successResponse.put("errmsg", "success");

        // Mock RestTemplate返回成功响应
        when(restTemplate.postForObject(anyString(), any(), eq(Map.class)))
                .thenReturn(successResponse);

        // 执行测试
        MailSendResp result = mailClient.sendEmail(req);

        // 验证结果
        assertTrue(result.getSuccess());
        assertNull(result.getMessage());
        verify(restTemplate).postForObject("http://test-mail-gateway.com", req.genMailSendRequest(), Map.class);
    }

    @Test
    public void testSendEmail_Failure() {
        // 准备测试数据
        MailSendReq req = new MailSendReq()
                .setId(123L)
                .setTo("<EMAIL>")
                .setContent("测试邮件内容");

        Map<String, Object> failureResponse = new HashMap<>();
        failureResponse.put("errcode", 1);
        failureResponse.put("errmsg", "发送失败");

        // Mock RestTemplate返回失败响应
        when(restTemplate.postForObject(anyString(), any(), eq(Map.class)))
                .thenReturn(failureResponse);

        // 执行测试
        MailSendResp result = mailClient.sendEmail(req);

        // 验证结果
        assertFalse(result.getSuccess());
        assertEquals("发送失败", result.getMessage());
    }

    @Test
    public void testSendEmail_Exception() {
        // 准备测试数据
        MailSendReq req = new MailSendReq()
                .setId(123L)
                .setTo("<EMAIL>")
                .setContent("测试邮件内容");

        // Mock RestTemplate抛出异常
        when(restTemplate.postForObject(anyString(), any(), eq(Map.class)))
                .thenThrow(new RuntimeException("网络异常"));

        // 执行测试
        MailSendResp result = mailClient.sendEmail(req);

        // 验证结果
        assertFalse(result.getSuccess());
        assertEquals("发送异常: 网络异常", result.getMessage());
    }

    @Test
    public void testSendEmailWithRetry_SuccessOnFirstAttempt() {
        // 准备测试数据
        MailSendReq req = new MailSendReq()
                .setId(123L)
                .setTo("<EMAIL>")
                .setContent("测试邮件内容");

        Map<String, Object> successResponse = new HashMap<>();
        successResponse.put("errcode", 0);
        successResponse.put("errmsg", "success");

        // Mock RestTemplate返回成功响应
        when(restTemplate.postForObject(anyString(), any(), eq(Map.class)))
                .thenReturn(successResponse);

        // 执行测试
        MailSendResp result = mailClient.sendEmailWithRetry(req);

        // 验证结果
        assertTrue(result.getSuccess());
        assertNull(result.getMessage());
        verify(restTemplate, times(1)).postForObject(anyString(), any(), eq(Map.class));
        verify(chatBotUtil, never()).sendMessageToContractWarnChatBot(anyString());
    }

    @Test
    public void testSendEmailWithRetry_SuccessOnSecondAttempt() {
        // 准备测试数据
        MailSendReq req = new MailSendReq()
                .setId(123L)
                .setTo("<EMAIL>")
                .setContent("测试邮件内容");

        Map<String, Object> failureResponse = new HashMap<>();
        failureResponse.put("errcode", 1);
        failureResponse.put("errmsg", "发送失败");

        Map<String, Object> successResponse = new HashMap<>();
        successResponse.put("errcode", 0);
        successResponse.put("errmsg", "success");

        // Mock RestTemplate第一次失败，第二次成功
        when(restTemplate.postForObject(anyString(), any(), eq(Map.class)))
                .thenReturn(failureResponse)
                .thenReturn(successResponse);

        // 执行测试
        MailSendResp result = mailClient.sendEmailWithRetry(req);

        // 验证结果
        assertTrue(result.getSuccess());
        assertNull(result.getMessage());
        verify(restTemplate, times(2)).postForObject(anyString(), any(), eq(Map.class));
        verify(chatBotUtil, never()).sendMessageToContractWarnChatBot(anyString());
    }

    @Test
    public void testSendEmailWithRetry_AllAttemptsFailed() {
        // 准备测试数据
        MailSendReq req = new MailSendReq()
                .setId(123L)
                .setTo("<EMAIL>")
                .setContent("测试邮件内容");

        Map<String, Object> failureResponse = new HashMap<>();
        failureResponse.put("errcode", 1);
        failureResponse.put("errmsg", "发送失败");

        // Mock RestTemplate始终返回失败
        when(restTemplate.postForObject(anyString(), any(), eq(Map.class)))
                .thenReturn(failureResponse);

        // 执行测试
        MailSendResp result = mailClient.sendEmailWithRetry(req);

        // 验证结果
        assertFalse(result.getSuccess());
        assertEquals("发送失败", result.getMessage());
        verify(restTemplate, times(3)).postForObject(anyString(), any(), eq(Map.class));

        // 验证告警消息
        ArgumentCaptor<String> alertCaptor = ArgumentCaptor.forClass(String.class);
        verify(chatBotUtil).sendMessageToContractWarnChatBot(alertCaptor.capture());
        String alertMessage = alertCaptor.getValue();
        assertTrue(alertMessage.contains("📧 邮件发送失败"));
        assertTrue(alertMessage.contains("邮件ID: 123"));
        assertTrue(alertMessage.contains("错误原因: 发送失败"));
    }

    @Test
    public void testSendEmailWithRetry_MixedFailures() {
        // 准备测试数据
        MailSendReq req = new MailSendReq()
                .setId(123L)
                .setTo("<EMAIL>")
                .setContent("测试邮件内容");

        Map<String, Object> failureResponse1 = new HashMap<>();
        failureResponse1.put("errcode", 1);
        failureResponse1.put("errmsg", "第一次失败");

        Map<String, Object> failureResponse2 = new HashMap<>();
        failureResponse2.put("errcode", 2);
        failureResponse2.put("errmsg", "第二次失败");

        Map<String, Object> failureResponse3 = new HashMap<>();
        failureResponse3.put("errcode", 3);
        failureResponse3.put("errmsg", "第三次失败");

        // Mock RestTemplate返回不同的失败响应
        when(restTemplate.postForObject(anyString(), any(), eq(Map.class)))
                .thenReturn(failureResponse1)
                .thenReturn(failureResponse2)
                .thenReturn(failureResponse3);

        // 执行测试
        MailSendResp result = mailClient.sendEmailWithRetry(req);

        // 验证结果
        assertFalse(result.getSuccess());
        assertEquals("第三次失败", result.getMessage());
        verify(restTemplate, times(3)).postForObject(anyString(), any(), eq(Map.class));

        // 验证告警消息包含最后一次失败的信息
        ArgumentCaptor<String> alertCaptor = ArgumentCaptor.forClass(String.class);
        verify(chatBotUtil).sendMessageToContractWarnChatBot(alertCaptor.capture());
        String alertMessage = alertCaptor.getValue();
        assertTrue(alertMessage.contains("📧 邮件发送失败"));
        assertTrue(alertMessage.contains("邮件ID: 123"));
        assertTrue(alertMessage.contains("错误原因: 第三次失败"));
    }

    @Test
    public void testSendEmailWithRetry_NullResponse() {
        // 准备测试数据
        MailSendReq req = new MailSendReq()
                .setId(123L)
                .setTo("<EMAIL>")
                .setContent("测试邮件内容");

        // Mock RestTemplate返回null
        when(restTemplate.postForObject(anyString(), any(), eq(Map.class)))
                .thenReturn(null);

        // 执行测试
        MailSendResp result = mailClient.sendEmailWithRetry(req);

        // 验证结果
        assertFalse(result.getSuccess());
        assertNotNull(result.getMessage());
        verify(restTemplate, times(3)).postForObject(anyString(), any(), eq(Map.class));

        // 验证告警消息
        ArgumentCaptor<String> alertCaptor = ArgumentCaptor.forClass(String.class);
        verify(chatBotUtil).sendMessageToContractWarnChatBot(alertCaptor.capture());
        String alertMessage = alertCaptor.getValue();
        assertTrue(alertMessage.contains("📧 邮件发送失败"));
        assertTrue(alertMessage.contains("邮件ID: 123"));
    }

    @Test
    public void testSendEmailWithRetry_EmptyResponse() {
        // 准备测试数据
        MailSendReq req = new MailSendReq()
                .setId(123L)
                .setTo("<EMAIL>")
                .setContent("测试邮件内容");

        Map<String, Object> emptyResponse = new HashMap<>();

        // Mock RestTemplate返回空响应
        when(restTemplate.postForObject(anyString(), any(), eq(Map.class)))
                .thenReturn(emptyResponse);

        // 执行测试
        MailSendResp result = mailClient.sendEmailWithRetry(req);

        // 验证结果
        assertFalse(result.getSuccess());
        assertNotNull(result.getMessage());
        verify(restTemplate, times(3)).postForObject(anyString(), any(), eq(Map.class));

        // 验证告警消息
        ArgumentCaptor<String> alertCaptor = ArgumentCaptor.forClass(String.class);
        verify(chatBotUtil).sendMessageToContractWarnChatBot(alertCaptor.capture());
        String alertMessage = alertCaptor.getValue();
        assertTrue(alertMessage.contains("📧 邮件发送失败"));
        assertTrue(alertMessage.contains("邮件ID: 123"));
    }

    @Test
    public void testSendEmailWithRetry_ChatBotException() {
        // 准备测试数据
        MailSendReq req = new MailSendReq()
                .setId(123L)
                .setTo("<EMAIL>")
                .setContent("测试邮件内容");

        Map<String, Object> failureResponse = new HashMap<>();
        failureResponse.put("errcode", 1);
        failureResponse.put("errmsg", "发送失败");

        // Mock RestTemplate返回失败响应
        when(restTemplate.postForObject(anyString(), any(), eq(Map.class)))
                .thenReturn(failureResponse);

        // Mock ChatBotUtil抛出异常
        doThrow(new RuntimeException("告警发送失败"))
                .when(chatBotUtil).sendMessageToContractWarnChatBot(anyString());

        // 执行测试并验证异常
        try {
            mailClient.sendEmailWithRetry(req);
            fail("应该抛出异常");
        } catch (RuntimeException e) {
            assertEquals("告警发送失败", e.getMessage());
        }

        // 验证结果
        verify(restTemplate, times(3)).postForObject(anyString(), any(), eq(Map.class));
        verify(chatBotUtil).sendMessageToContractWarnChatBot(anyString());
    }
} 