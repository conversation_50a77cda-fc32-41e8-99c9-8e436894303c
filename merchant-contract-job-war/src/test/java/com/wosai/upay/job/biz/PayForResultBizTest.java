package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.bank.model.enume.AccountApplyStatus;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.PayForTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.PayForTask;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.service.TaskResultService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2020/7/16
 */
public class PayForResultBizTest extends H2DbBaseTest {

    @Autowired
    private PayForResultBiz payForResultBiz;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    private ContractSubTask contractSubTask;

    @Autowired
    private PayForTaskMapper payForTaskMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Before
    public void setUp() {
        if (contractSubTask == null) {
            contractSubTask = contractSubTaskMapper.selectByPrimaryKey(4510722L);
        }
        ReflectionTestUtils.setField(payForResultBiz, "taskResultService", Mockito.mock(TaskResultService.class));
        ReflectionTestUtils.setField(payForResultBiz,"dataBusBiz",Mockito.mock(DataBusBiz.class));
    }

    @Test
    public void sucessHandle() {
        payForResultBiz.successHandle(contractSubTask, 79503L);
        PayForTask result = payForTaskMapper.selectByPrimaryKey(79503L);
        assertEquals(PayForTask.SUCCESS_STATUS, result.getStatus().intValue());
    }

    @Test
    public void failHandler() {
        payForResultBiz.failHandler(contractSubTask, 79503L, AccountApplyStatus.PAYMENT_FAIL.getStatus());
        PayForTask result = payForTaskMapper.selectByPrimaryKey(79503L);
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id());
        assertEquals(PayForTask.FAIL_STATUS, result.getStatus().intValue());
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_PAYMENT_FAIL_PUBLIC, JSON.parseObject(contractTask.getResult(), Map.class).get("message"));
    }

    @Test
    public void needVeriyHandle() {
        payForResultBiz.needVeriyHandle(contractSubTask, 79503L);
        PayForTask result = payForTaskMapper.selectByPrimaryKey(79503L);
        assertEquals(PayForTask.VERIFY_STATUS, result.getStatus().intValue());
    }
}