package com.wosai.upay.job.xxljob.direct.contracttask;

import com.wosai.upay.job.xxljob.model.DirectExecTypeEnum;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.upay.job.BaseTest;

public class ContractTaskUpdateAccountJobHandlerTest extends BaseTest {

    @Autowired
    private ContractTaskUpdateAccountJobHandler contractTaskUpdateAccountJobHandler;

    @Test
    public void executeTest() {
        DirectJobParam param = new DirectJobParam();
        param.setQueryTime(10800000L);
        param.setBatchSize(200);
        param.setExecType(DirectExecTypeEnum.ASYNC);
        contractTaskUpdateAccountJobHandler.execute(param);
    }
}