package com.wosai.upay.job.biz;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.side.service.GeneralRuleService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;

public class CompleteDateBizTest extends BaseTest {

    @Autowired
    private CompleteDateBiz biz;

    @Mock
    private GeneralRuleService generalRuleService;


    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(biz, "generalRuleService", generalRuleService);
        Mockito.doReturn("2020-02-02").when(generalRuleService).getFirstWeekDayAfterDate(any());
    }

    @Test
    public void getLklDate() {
        biz.getLklDate(new Date(1592901223000L));
        biz.getLklDate(new Date(1592904823000L));
    }

    @Test
    public void getPayCompleteTime() {
        biz.getPayCompleteTime(new Date(1592899200000L), 5);
    }

    @Test
    public void getWeekDay() {
        biz.getWeekDay(new Date(1592899200000L), 5);
    }
}