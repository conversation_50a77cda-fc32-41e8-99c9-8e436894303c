package com.wosai.upay.job.xxljob.direct.weixindirect;

import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.biz.direct.WeixinDirectBiz;
import com.wosai.upay.job.enume.WeixinDirectApplyStatus;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.WeixinDirectApplyMapper;
import com.wosai.upay.job.model.DO.WeixinDirectApply;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import com.wosai.upay.merchant.contract.model.weixin.DirectApplymentStatusResp;
import com.wosai.upay.merchant.contract.service.WeiXinDirectService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Arrays;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class WeixinDirectQueryApplyJobHandlerTest {

    @InjectMocks
    private WeixinDirectQueryApplyJobHandler handler;

    @Mock
    private WeiXinDirectService weiXinDirectService;

    @Mock
    private MonitorLog monitorLog;

    @Mock
    private ChatBotUtil chatBotUtil;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private JobThreadPoolExecutor directJobHandlerThreadPool;

    @Mock
    private WeixinDirectApplyMapper applyMapper;

    @Mock
    private DirectStatusBiz directStatusBiz;

    @Mock
    private WeixinDirectBiz directBiz;

    @Mock
    private ContractTaskBiz taskBiz;

    @Mock
    private ContractTaskMapper taskMapper;

    @Mock
    private TransactionTemplate transactionTemplate;

    @Value("${weixin.direct.online}")
    private String weixinDirectOnline;

    @Test
    public void queryApplySuccess() {
        WeixinDirectApply apply = new WeixinDirectApply();
        apply.setMerchant_sn("merchant_sn").setTask_id(111L).setStatus(WeixinDirectApplyStatus.IN_WEIXIN_AUDITING.getVal()).setDev_code(weixinDirectOnline)
                .setPriority(new Date(System.currentTimeMillis() - 5000));
        DirectApplymentStatusResp statusResp = new DirectApplymentStatusResp();
        statusResp.setCode("200").setApplyment_state("APPLYMENT_STATE_AUDITING");//微信审核中
        Mockito.doReturn(statusResp).when(weiXinDirectService).queryApplyStatus(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPriorityAndStatus(any(), any(), any(), any());
        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);

        handler.execute(directJobParam);

    }

    @Test
    public void queryApplySysException() {
        WeixinDirectApply apply = new WeixinDirectApply();
        apply.setMerchant_sn("merchant_sn").setTask_id(111L).setStatus(WeixinDirectApplyStatus.IN_WEIXIN_AUDITING.getVal()).setDev_code(weixinDirectOnline)
                .setPriority(new Date(System.currentTimeMillis() - 5000));
        DirectApplymentStatusResp statusResp = new DirectApplymentStatusResp();
        statusResp.setCode("500");//异常
        Mockito.doReturn(statusResp).when(weiXinDirectService).queryApplyStatus(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPriorityAndStatus(any(), any(), any(), any());
        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(chatBotUtil, Mockito.times(1)).sendMessageToContractWarnChatBot(any());
    }

    @Test
    public void queryApplyFail() {
        WeixinDirectApply apply = new WeixinDirectApply();
        apply.setMerchant_sn("merchant_sn").setTask_id(111L).setStatus(WeixinDirectApplyStatus.IN_WEIXIN_AUDITING.getVal()).setDev_code(weixinDirectOnline)
                .setPriority(new Date(System.currentTimeMillis() - 5000));
        DirectApplymentStatusResp statusResp = new DirectApplymentStatusResp();
        statusResp.setCode("405");//异常
        Mockito.doReturn(statusResp).when(weiXinDirectService).queryApplyStatus(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPriorityAndStatus(any(), any(), any(), any());
        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(chatBotUtil, Mockito.times(1)).sendMessageToContractWarnChatBot(any());
        Mockito.verify(transactionTemplate, Mockito.times(1)).executeWithoutResult(any());

    }
}
