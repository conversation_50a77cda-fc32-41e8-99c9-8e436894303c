package com.wosai.upay.job.biz.keepalive;

import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.externalservice.aop.AopClient;
import com.wosai.upay.job.model.keepalive.KeepAliveTaskStatusUpdateRequest;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.ProviderParamsKeepaliveTaskDAO;
import com.wosai.upay.job.refactor.dao.SubMchIdLastTradeTimeDAO;
import com.wosai.upay.job.refactor.model.bo.KeepAliveTaskResultBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.ProviderParamsKeepaliveTaskDO;
import com.wosai.upay.job.refactor.model.entity.SubMchIdLastTradeTimeDO;
import com.wosai.upay.job.refactor.model.enums.ProviderParamsKeepaliveTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.ProviderParamsKeepaliveTaskTypeEnum;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * KeepAliveTasksBiz 单元测试类
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
@ExtendWith(MockitoExtension.class)
class KeepAliveTaskBizTest {

    @Mock
    private ProviderParamsKeepaliveTaskDAO providerParamsKeepaliveTaskDAO;

    @Mock
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Mock
    private KeepAliveProviderParamsAndDateCalculator calculator;

    @Mock
    private ApplicationApolloConfig applicationApolloConfig;

    @Mock
    private SubMchIdLastTradeTimeBiz subMchIdLastTradeTimeBiz;

    @Mock
    private KeepAliveMessageBiz keepAliveMessageBiz;

    @Mock
    private AopClient aopClient;

    @InjectMocks
    private KeepAliveTaskBiz keepAliveTaskBiz;

    private String merchantSn;
    private KeepAliveTaskResultBO resultBO;

    @BeforeEach
    void setUp() {
        merchantSn = "test_merchant_001";
        
        resultBO = new KeepAliveTaskResultBO();
        resultBO.setSource("test_user");
        resultBO.setOperator("test_user_name");
        resultBO.setRemark("测试备注");
    }

    @Test
    void testCreateKeepAliveTasks_Success() {
        // 测试成功创建保活任务
        
        // 模拟需要保活的参数
        Map<MerchantProviderParamsDO, LocalDate> requiredParams = new LinkedHashMap<>();
        MerchantProviderParamsDO param1 = createParam("param_001", 1001, 2, "sub_mch_001");
        MerchantProviderParamsDO param2 = createParam("param_002", 1002, 3, "sub_mch_002");
        
        LocalDate today = LocalDate.now();
        requiredParams.put(param1, today.plusDays(5));
        requiredParams.put(param2, today.plusDays(10));
        
        // 模拟最大保活日期
        Map<MerchantProviderParamsDO, LocalDate> maxDates = new LinkedHashMap<>();
        maxDates.put(param1, today.plusDays(30));
        maxDates.put(param2, today.plusDays(30));
        
        KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult result = 
            new KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult(requiredParams, maxDates);
        
        when(calculator.calculateKeepAliveParamsAndDateWithMaxLimit(merchantSn)).thenReturn(result);
        when(providerParamsKeepaliveTaskDAO.selectNotCompletedTasksByMerchantSn(merchantSn))
                .thenReturn(Collections.emptyList());

        // 执行测试
        keepAliveTaskBiz.createKeepAliveTasks(merchantSn, resultBO);

        // 验证任务创建
        ArgumentCaptor<List<ProviderParamsKeepaliveTaskDO>> insertCaptor = ArgumentCaptor.forClass(List.class);
        verify(providerParamsKeepaliveTaskDAO).batchInsert(insertCaptor.capture());
        
        List<ProviderParamsKeepaliveTaskDO> insertedTasks = insertCaptor.getValue();
        assertEquals(2, insertedTasks.size());
        
        // 验证任务属性
        ProviderParamsKeepaliveTaskDO task1 = insertedTasks.get(0);
        assertEquals(merchantSn, task1.getMerchantSn());
        assertEquals(1001, task1.getProvider());
        assertEquals(2, task1.getPayway());
        assertEquals("sub_mch_001", task1.getSubMchId());
        assertEquals(ProviderParamsKeepaliveTaskStatusEnum.PENDING.getValue(), task1.getStatus());
        assertEquals(ProviderParamsKeepaliveTaskTypeEnum.AUTO.getValue(), task1.getType());
    }

    @Test
    void testCreateKeepAliveTasks_NoRequiredParams() {
        // 测试没有需要保活的参数
        
        Map<MerchantProviderParamsDO, LocalDate> requiredParams = new LinkedHashMap<>();
        Map<MerchantProviderParamsDO, LocalDate> maxDates = new LinkedHashMap<>();
        
        KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult result = 
            new KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult(requiredParams, maxDates);
        
        when(calculator.calculateKeepAliveParamsAndDateWithMaxLimit(merchantSn)).thenReturn(result);

        // 执行测试
        keepAliveTaskBiz.createKeepAliveTasks(merchantSn, resultBO);

        // 验证没有创建任务
        verify(providerParamsKeepaliveTaskDAO, never()).batchInsert(any());
    }

    @Test
    void testCreateKeepAliveTasks_WithExistingTasksNeedCancel() {
        // 测试存在现有任务的情况
        
        // 模拟需要保活的参数
        Map<MerchantProviderParamsDO, LocalDate> requiredParams = new LinkedHashMap<>();
        MerchantProviderParamsDO param1 = createParam("param_001", 1001, 2, "sub_mch_001");
        MerchantProviderParamsDO param2 = createParam("param_002", 1002, 3, "sub_mch_002");
        
        LocalDate today = LocalDate.now();
        requiredParams.put(param1, today.plusDays(5));
        requiredParams.put(param2, today.plusDays(10));
        
        Map<MerchantProviderParamsDO, LocalDate> maxDates = new LinkedHashMap<>();
        maxDates.put(param1, today.plusDays(30));
        maxDates.put(param2, today.plusDays(30));
        
        KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult result = 
            new KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult(requiredParams, maxDates);
        
        when(calculator.calculateKeepAliveParamsAndDateWithMaxLimit(merchantSn)).thenReturn(result);
        
        // 模拟现有任务
        ProviderParamsKeepaliveTaskDO existingTask = new ProviderParamsKeepaliveTaskDO();
        existingTask.setProvider(1003);
        existingTask.setPayway(2);
        existingTask.setStartDate(today.plusDays(5)); // 与新任务日期相同
        existingTask.setSubMchId("sub_mch_003");
        existingTask.setStatus(ProviderParamsKeepaliveTaskStatusEnum.PENDING.getValue());
        
        when(providerParamsKeepaliveTaskDAO.selectNotCompletedTasksByMerchantSn(merchantSn))
                .thenReturn(Arrays.asList(existingTask));

        // 执行测试
        keepAliveTaskBiz.createKeepAliveTasks(merchantSn, resultBO);

        // 验证任务取消和创建
        verify(providerParamsKeepaliveTaskDAO, times(1)).batchCancelKeepAliveTasks(any(), any());

        // 验证新任务创建
        ArgumentCaptor<List<ProviderParamsKeepaliveTaskDO>> insertCaptor = ArgumentCaptor.forClass(List.class);
        verify(providerParamsKeepaliveTaskDAO).batchInsert(insertCaptor.capture());
        
        List<ProviderParamsKeepaliveTaskDO> insertedTasks = insertCaptor.getValue();
        assertTrue(insertedTasks.size() > 0); // 应该创建新任务
    }

    @Test
    void testCreateKeepAliveTasks_FilterExpiredTasks() {
        // 测试过滤过期任务
        
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        
        // 模拟需要保活的参数（包含过期任务）
        Map<MerchantProviderParamsDO, LocalDate> requiredParams = new LinkedHashMap<>();
        MerchantProviderParamsDO expiredParam = createParam("param_001", 1001, 2, "sub_mch_001");
        MerchantProviderParamsDO validParam = createParam("param_002", 1002, 3, "sub_mch_002");
        
        requiredParams.put(expiredParam, yesterday); // 过期任务
        requiredParams.put(validParam, today.plusDays(5)); // 有效任务
        
        Map<MerchantProviderParamsDO, LocalDate> maxDates = new LinkedHashMap<>();
        maxDates.put(expiredParam, today.plusDays(30));
        maxDates.put(validParam, today.plusDays(30));
        
        KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult result = 
            new KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult(requiredParams, maxDates);
        
        when(calculator.calculateKeepAliveParamsAndDateWithMaxLimit(merchantSn)).thenReturn(result);
        when(providerParamsKeepaliveTaskDAO.selectNotCompletedTasksByMerchantSn(merchantSn))
                .thenReturn(Collections.emptyList());

        // 执行测试
        keepAliveTaskBiz.createKeepAliveTasks(merchantSn, resultBO);

        // 验证只创建了有效任务
        ArgumentCaptor<List<ProviderParamsKeepaliveTaskDO>> insertCaptor = ArgumentCaptor.forClass(List.class);
        verify(providerParamsKeepaliveTaskDAO).batchInsert(insertCaptor.capture());
        
        List<ProviderParamsKeepaliveTaskDO> insertedTasks = insertCaptor.getValue();
        assertEquals(1, insertedTasks.size()); // 只有一个有效任务
        assertEquals("sub_mch_002", insertedTasks.get(0).getSubMchId()); // 验证是有效任务
    }

    @Test
    void testCreateKeepAliveTasks_AllTasksFiltered() {
        // 测试所有任务都被过滤的情况

        LocalDate today = LocalDate.now();

        // 模拟需要保活的参数（全部过期）
        Map<MerchantProviderParamsDO, LocalDate> requiredParams = new LinkedHashMap<>();
        MerchantProviderParamsDO expiredParam1 = createParam("param_001", 1001, 2, "sub_mch_001");
        MerchantProviderParamsDO expiredParam2 = createParam("param_002", 1002, 3, "sub_mch_002");

        requiredParams.put(expiredParam1, today.minusDays(1)); // 过期任务
        requiredParams.put(expiredParam2, today); // 今天的任务（也被认为是过期）

        Map<MerchantProviderParamsDO, LocalDate> maxDates = new LinkedHashMap<>();
        maxDates.put(expiredParam1, today.plusDays(30));
        maxDates.put(expiredParam2, today.plusDays(30));

        KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult result =
            new KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult(requiredParams, maxDates);

        when(calculator.calculateKeepAliveParamsAndDateWithMaxLimit(merchantSn)).thenReturn(result);
        when(providerParamsKeepaliveTaskDAO.selectNotCompletedTasksByMerchantSn(merchantSn))
                .thenReturn(Collections.emptyList());

        // 执行测试
        keepAliveTaskBiz.createKeepAliveTasks(merchantSn, resultBO);

        // 验证没有创建任何任务
        verify(providerParamsKeepaliveTaskDAO, never()).batchInsert(any());
    }

    @Test
    void testCreateKeepAliveTasks_DateAdjustment() {
        // 测试日期调整逻辑

        LocalDate today = LocalDate.now();

        // 模拟需要保活的参数（日期可能冲突）
        Map<MerchantProviderParamsDO, LocalDate> requiredParams = new LinkedHashMap<>();
        MerchantProviderParamsDO param1 = createParam("param_001", 1001, 2, "sub_mch_001");
        MerchantProviderParamsDO param2 = createParam("param_002", 1002, 3, "sub_mch_002");

        // 设置相同的保活日期，测试日期调整
        LocalDate sameDate = today.plusDays(5);
        requiredParams.put(param1, sameDate);
        requiredParams.put(param2, sameDate);

        Map<MerchantProviderParamsDO, LocalDate> maxDates = new LinkedHashMap<>();
        maxDates.put(param1, today.plusDays(30));
        maxDates.put(param2, today.plusDays(30));

        KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult result =
            new KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult(requiredParams, maxDates);

        when(calculator.calculateKeepAliveParamsAndDateWithMaxLimit(merchantSn)).thenReturn(result);
        when(providerParamsKeepaliveTaskDAO.selectNotCompletedTasksByMerchantSn(merchantSn))
                .thenReturn(Collections.emptyList());

        // 执行测试
        keepAliveTaskBiz.createKeepAliveTasks(merchantSn, resultBO);

        // 验证任务创建
        ArgumentCaptor<List<ProviderParamsKeepaliveTaskDO>> insertCaptor = ArgumentCaptor.forClass(List.class);
        verify(providerParamsKeepaliveTaskDAO).batchInsert(insertCaptor.capture());

        List<ProviderParamsKeepaliveTaskDO> insertedTasks = insertCaptor.getValue();
        assertEquals(2, insertedTasks.size());

        // 验证日期调整（不同的 provider 应该有不同的日期）
        Set<LocalDate> uniqueDates = new HashSet<>();
        for (ProviderParamsKeepaliveTaskDO task : insertedTasks) {
            uniqueDates.add(task.getStartDate());
        }
        // 由于日期调整，应该有不同的日期
        assertTrue(uniqueDates.size() >= 1);
    }

    @Test
    public void testCloseKeepAliveTasksWhenCloseConfig() {
        String merchantSn = "merchantSn";
        ProviderParamsKeepaliveTaskDO taskDO = new ProviderParamsKeepaliveTaskDO();
        taskDO.setTaskId(1234L);
        Mockito.doReturn(Collections.singletonList(taskDO)).when(providerParamsKeepaliveTaskDAO).selectNotCompletedTasksByMerchantSn(merchantSn);
        keepAliveTaskBiz.closeKeepAliveTasksWhenCloseConfig(merchantSn, resultBO);
        verify(providerParamsKeepaliveTaskDAO, times(1)).batchCancelKeepAliveTasks(any(), any());
    }

    @Test
    public void testCloseKeepAliveTaskFromAPI_NotFound() {
        KeepAliveTaskStatusUpdateRequest request = new KeepAliveTaskStatusUpdateRequest();
        request.setTaskId("1234");
        ContractBizException contractBizException = assertThrows(ContractBizException.class, () -> keepAliveTaskBiz.closeKeepAliveTaskFromAPI(request));
        assertEquals("未找到对应的任务", contractBizException.getMessage());
    }

    @Test
    public void testCloseKeepAliveTaskFromAPI_NotActiveTaskAndPayTime() {
        KeepAliveTaskStatusUpdateRequest request = new KeepAliveTaskStatusUpdateRequest();
        request.setTaskId("1234");
        request.setPayTime(1L);
        request.setStatus(1);

        ProviderParamsKeepaliveTaskDO taskDO = new ProviderParamsKeepaliveTaskDO();
        taskDO.setStatus(ProviderParamsKeepaliveTaskStatusEnum.SUCCESS.getValue());
        taskDO.setMerchantSn("merchantSn");
        taskDO.setSubMchId("subMchId");

        SubMchIdLastTradeTimeDO subMchIdLastTradeTimeDO = new SubMchIdLastTradeTimeDO();
        subMchIdLastTradeTimeDO.setTradeTime(1L);
        Mockito.doReturn(Optional.of(taskDO)).when(providerParamsKeepaliveTaskDAO).selectByTaskId(Long.parseLong(request.getTaskId()));

        ContractBizException contractBizException = assertThrows(ContractBizException.class, () -> keepAliveTaskBiz.closeKeepAliveTaskFromAPI(request));
        assertEquals("该任务未在保活中", contractBizException.getMessage());
        verify(subMchIdLastTradeTimeBiz, times(1)).insertOrUpdateSubMchIdLastTradeTime(any(), anyLong());
    }

    @Test
    public void testCloseKeepAliveTaskFromAPI_Success() {
        KeepAliveTaskStatusUpdateRequest request = new KeepAliveTaskStatusUpdateRequest();
        request.setTaskId("1234");
        request.setStatus(1);
        request.setOrderNo("orderNo");
        request.setAmount(1L);
        request.setPayTime(2L);

        ProviderParamsKeepaliveTaskDO taskDO = new ProviderParamsKeepaliveTaskDO();
        taskDO.setStatus(ProviderParamsKeepaliveTaskStatusEnum.ACTIVE.getValue());
        taskDO.setMerchantSn("merchantSn");
        taskDO.setSubMchId("subMchId");

        Mockito.doReturn(Optional.of(taskDO)).when(providerParamsKeepaliveTaskDAO).selectByTaskId(Long.parseLong(request.getTaskId()));
        Mockito.doReturn(new KeepAliveProviderParamsAndDateCalculator.KeepAliveParamsAndDateResult(new HashMap<>(), new HashMap<>())).when(calculator).calculateKeepAliveParamsAndDateWithMaxLimit(taskDO.getMerchantSn());

        keepAliveTaskBiz.closeKeepAliveTaskFromAPI(request);
        verify(subMchIdLastTradeTimeBiz, times(1)).insertOrUpdateSubMchIdLastTradeTime(any(), anyLong());
        verify(providerParamsKeepaliveTaskDAO, times(1)).completeKeepAliveTask(any(), any(), anyLong(), anyLong(), any());
    }

    private MerchantProviderParamsDO createParam(String id, Integer provider, Integer payway, String subMchId) {
        MerchantProviderParamsDO param = new MerchantProviderParamsDO();
        param.setId(id);
        param.setProvider(provider);
        param.setPayway(payway);
        param.setPayMerchantId(subMchId);
        param.setProviderMerchantId("provider_mch_" + provider);
        param.setMerchantSn(merchantSn);
        param.setCtime(System.currentTimeMillis() - 30 * 24 * 60 * 60 * 1000L); // 30天前创建
        return param;
    }
}
