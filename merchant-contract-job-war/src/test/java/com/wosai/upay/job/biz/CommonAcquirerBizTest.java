package com.wosai.upay.job.biz;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.acquirer.CommonAcquirerBiz;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CommonAcquirerBizTest extends BaseTest {

    @Autowired
    private CommonAcquirerBiz commonAcquirerBiz;

    @Test
    public void getDefaultRuleGroupTest() {
        String ss = commonAcquirerBiz.getDefaultRuleGroup("fjnx");
        Assert.assertEquals(ss, "fjnx");
    }
}
