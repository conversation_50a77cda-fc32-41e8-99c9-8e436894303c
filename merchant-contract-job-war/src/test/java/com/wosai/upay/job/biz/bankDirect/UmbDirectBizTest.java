package com.wosai.upay.job.biz.bankDirect;

import cn.hutool.core.map.MapUtil;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static com.wosai.upay.job.biz.paramContext.ParamContextBiz.DEDICATED_PARAMS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

public class UmbDirectBizTest {

    @InjectMocks
    private UmbDirectBiz umbDirectBiz;

    @Mock
    private ApplicationApolloConfig applicationApolloConfig;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        umbDirectBiz.umbPlatMerId2 = "umb-CF3000053379";
        umbDirectBiz.umbPlatMerId1 = "umb-CF3000053368";

    }

    @Test
    public void testChooseGroupByContextAndDevCode_WithIndustryAndComeFromSqb() {
        // Arrange
        Map<String, Object> context = new HashMap<>();
        Map<String, Object> merchant = new HashMap<>();
        merchant.put("industry", "e6b8d357-312d-11e6-aebb-ecf4bbdee2f0");
        context.put("merchant", merchant);

        Map<String, Object> dedicatedMap = new HashMap<>();
        dedicatedMap.put("come_from", "sqb");
        context.put(DEDICATED_PARAMS, dedicatedMap);

        String devCode = "dev123";

        when(applicationApolloConfig.getUmbIndustryToGroupMap()).thenReturn(MapUtil.of("e6b8d357-312d-11e6-aebb-ecf4bbdee2f0-sqb", "umb-CF3000053369"));

        // Act
        String result = umbDirectBiz.chooseGroupByContextAndDevCode(context, devCode);

        // Assert
        assertEquals("umb-CF3000053369", result);
        verify(applicationApolloConfig, times(1)).getUmbIndustryToGroupMap();
    }

    @Test
    public void testChooseGroupByContextAndDevCode_WithIndustryAndComeFromUmb() {
        // Arrange
        Map<String, Object> context = new HashMap<>();
        Map<String, Object> merchant = new HashMap<>();
        merchant.put("industry", "e6b8d357-312d-11e6-aebb");
        context.put("merchant", merchant);

        Map<String, Object> dedicatedMap = new HashMap<>();
        dedicatedMap.put("come_from", "umb");
        context.put(DEDICATED_PARAMS, dedicatedMap);

        String devCode = "dev123";

        when(applicationApolloConfig.getUmbIndustryToGroupMap()).thenReturn(MapUtil.of("e6b8d357-312d-11e6-aebb-ecf4bbdee2f0-umb", "umb-CF3000053378"));

        // Act
        String result = umbDirectBiz.chooseGroupByContextAndDevCode(context, devCode);

        // Assert
        assertEquals("umb-CF3000053368", result);
        verify(applicationApolloConfig, times(1)).getUmbIndustryToGroupMap();
    }

    @Test
    public void testChooseGroupByContextAndDevCode_WithIndustryAndComeFromNotSqb() {
        // Arrange
        Map<String, Object> context = new HashMap<>();
        Map<String, Object> merchant = new HashMap<>();
        merchant.put("industry", "");
        context.put("merchant", merchant);

        Map<String, Object> dedicatedMap = new HashMap<>();
        dedicatedMap.put("come_from", "sqb");
        context.put(DEDICATED_PARAMS, dedicatedMap);

        String devCode = "dev123";

        when(applicationApolloConfig.getUmbIndustryToGroupMap()).thenReturn(MapUtil.of("e6b8d357-312d-11e6-aebb-ecf4bbdee2f0-umb", "umb-CF3000053368"));

        // Act
        String result = umbDirectBiz.chooseGroupByContextAndDevCode(context, devCode);

        // Assert
        assertEquals("umb-CF3000053379", result);
        verify(applicationApolloConfig, times(1)).getUmbIndustryToGroupMap();
    }
}
