package com.wosai.upay.job.refactor.dao;

import com.github.pagehelper.Page;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.dto.AcquirerDto;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.*;

public class McAcquirerDAOTest extends BaseTest {
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Test
    public void getByAcquirer() {
        Page<McAcquirerDO> mcAcquirerDOPage = mcAcquirerDAO.pageAcquirerList(new PageInfo(1, 10), new AcquirerDto());
        Assert.assertTrue(mcAcquirerDOPage.getResult().size() > 0);
        Assert.assertTrue(mcAcquirerDOPage.getTotal() > 0);
        for (McAcquirerDO mcAcquirerDO : mcAcquirerDOPage.getResult()) {
            String acquirerName = mcAcquirerDAO.getAcquirerName(mcAcquirerDO.getAcquirer());
            Assert.assertEquals(mcAcquirerDO.getName(), acquirerName);
        }
    }

    @Test
    public void getIndirectAcquirerList() {
        List<String> indirectAcquirerList = mcAcquirerDAO.getIndirectAcquirerList();
        Assert.assertTrue(indirectAcquirerList.contains("lklV3"));
        Assert.assertFalse(indirectAcquirerList.contains("icbc"));
    }
}