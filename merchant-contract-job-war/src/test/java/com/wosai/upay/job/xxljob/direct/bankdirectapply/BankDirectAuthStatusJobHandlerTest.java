package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.google.common.collect.Lists;
import com.wosai.data.util.CollectionUtil;
import com.wosai.task.service.TaskInstanceService;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.AlipayAuthBiz;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.biz.bankDirect.BankHandleServiceFactory;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectExecTypeEnum;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Arrays;
import java.util.Date;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BankDirectAuthStatusJobHandlerTest {

    @InjectMocks
    private BankDirectAuthStatusJobHandler bankDirectAuthStatusJobHandler;

    @Mock
    private BankDirectApplyMapper bankDirectApplyMapperB;

    @Mock
    private ContractTaskMapper contractTaskMapperB;

    @Mock
    private WechatAuthBiz wechatAuthBizB;

    @Mock
    private McAcquirerDAO mcAcquirerDAOB;

    @Mock
    private StringRedisTemplate redisTemplateB;

    @Mock
    private ChatBotUtil chatBotUtilB;
    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Mock
    private MerchantBankService merchantBankService;
    @Mock
    private DirectStatusBiz directStatusBiz;
    @Mock
    private BankService bankService;
    @Mock
    private TaskInstanceService taskInstanceService;
    @Mock
    private ApplicationApolloConfig applicationApolloConfig;
    @Mock
    private AlipayAuthBiz alipayAuthBiz;
    @Mock
    private BankHandleServiceFactory factory;

    private DirectJobParam paramB;

    private String extra = "{\"view_process\":[{\"finish\":true,\"remark\":\"操作说明: \",\"statusDesc\":\"已提交,待商户签约\",\"time\":\"分配时间: 2025-04-07 16:19:42\",\"viewStatus\":20},{\"finish\":true,\"remark\":\"处理时效: 预计银行在1-2个工作日内完成审核\",\"statusDesc\":\"商户已签约,待银行审核\",\"time\":\"签约时间: 2025-04-07 16:20:03\",\"viewStatus\":30},{\"aliMch\":\"子商户号*************\",\"aliMessage\":\"\",\"aliStatus\":false,\"extraMessage\":\"\",\"finish\":false,\"viewStatus\":40,\"wxMch\":\"子商户号*************\",\"wxStatus\":false},{\"finish\":false,\"remark\":\"处理时效: 目前待完成实名认证后的3-5个工作日完成通道切换\",\"statusDesc\":\"商户微信及支付宝实名认证均已成功，待切换通道\",\"time\":\"认证时间: 2025-04-07 16:20:11\",\"viewStatus\":50},{\"finish\":false,\"statusDesc\":\"通道切换完成\",\"time\":\"操作时间: 2025-04-08 03:14:32\",\"viewStatus\":60}],\"provider\":1026,\"task_id\":***********,\"acquire\":\"ccb\"}";

    @Before
    public void setUp() {
        paramB = new DirectJobParam();
        paramB.setBatchSize(10);
        paramB.setQueryTime(1000L);
    }

    @Test
    public void getLockKey_ReturnsCorrectLockKey() {
        String expectedLockKey = "BankDirectAuthStatusJobHandler";
        String actualLockKey = bankDirectAuthStatusJobHandler.getLockKey();
        Assert.assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_EmptyApplyList_NoAction() {
        when(bankDirectApplyMapperB.listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt())).thenReturn(Lists.newArrayList());

        bankDirectAuthStatusJobHandler.execute(paramB);

        verify(bankDirectApplyMapperB, times(1)).listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt());
        verifyNoMoreInteractions(bankDirectApplyMapperB, contractTaskMapperB, wechatAuthBizB, mcAcquirerDAOB, redisTemplateB, chatBotUtilB);
    }

    @Test
    public void execute_WechatAuthFailedAndOver30Days_StatusUpdatedAndAccountDeleted() {
        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setTask_id(1L);
        apply.setDev_code("devCode");
        apply.setForm_body("{\"bank_pre_id\":\"preId\"}");
        apply.setExtra(extra);

        ContractTask contractTask = new ContractTask();
        contractTask.setUpdate_at(new Date(System.currentTimeMillis() - DateUtils.MILLIS_PER_DAY * 31));

        when(bankDirectApplyMapperB.listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt())).thenReturn(Lists.newArrayList(apply));
        when(contractTaskMapperB.selectByPrimaryKey(anyLong())).thenReturn(contractTask);
        when(wechatAuthBizB.getAuthStatus(any(MerchantProviderParams.class), anyInt())).thenReturn(false);
        when(merchantProviderParamsMapper.selectByExample(any())).thenReturn(Arrays.asList(new MerchantProviderParams().setCtime(System.currentTimeMillis())));
        when(merchantBankService.getMerchantBankAccountPre(any())).thenReturn(CollectionUtil.hashMap("id", "id"));

        bankDirectAuthStatusJobHandler.execute(paramB);

        verify(bankDirectApplyMapperB, times(1)).listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt());
        verify(contractTaskMapperB, times(1)).selectByPrimaryKey(anyLong());
        verify(wechatAuthBizB, times(1)).getAuthStatus(any(MerchantProviderParams.class), anyInt());
        verify(merchantBankService, times(1)).getMerchantBankAccountPre(any());
        verify(bankService, times(1)).deletedMerchantBankAccountPre(any());
    }

    @Test
    public void execute_WechatAuthFailedAndNotOver30Days_TaskDispatched() {
        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setTask_id(1L);
        apply.setDev_code("devCode");
        apply.setExtra(extra);
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams().setCtime(System.currentTimeMillis());
        ContractTask contractTask = new ContractTask();
        contractTask.setUpdate_at(new Date());

        when(bankDirectApplyMapperB.listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt())).thenReturn(Lists.newArrayList(apply));
        when(contractTaskMapperB.selectByPrimaryKey(anyLong())).thenReturn(contractTask);
        when(wechatAuthBizB.getAuthStatus(any(MerchantProviderParams.class), anyInt())).thenReturn(false);
        when(redisTemplateB.hasKey(anyString())).thenReturn(false);
        when(redisTemplateB.opsForValue()).thenReturn(mock(ValueOperations.class));
        when(mcAcquirerDAOB.getAcquirerName(anyString())).thenReturn("acquirerName");

        when(merchantProviderParamsMapper.selectByExample(any())).thenReturn(Arrays.asList(merchantProviderParams));
        when(applicationApolloConfig.getDirectApplyAliCheckSwitch()).thenReturn(true);
        bankDirectAuthStatusJobHandler.execute(paramB);

        verify(bankDirectApplyMapperB, times(1)).listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt());
        verify(contractTaskMapperB, times(1)).selectByPrimaryKey(anyLong());
        verify(wechatAuthBizB, times(1)).getAuthStatus(any(MerchantProviderParams.class), anyInt());
        verify(redisTemplateB, times(1)).hasKey(anyString());
        verify(redisTemplateB, times(3)).opsForValue();
        verify(mcAcquirerDAOB, times(1)).getAcquirerName(anyString());
        verify(taskInstanceService, times(1)).startTaskForRpc(any());
        verify(bankDirectApplyMapperB, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void execute_AlipayAuthFailed_DelayApply() {
        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setTask_id(1L);
        apply.setDev_code("devCode");
        apply.setExtra(extra);
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams().setCtime(System.currentTimeMillis());
        ContractTask contractTask = new ContractTask();
        contractTask.setUpdate_at(new Date());

        when(bankDirectApplyMapperB.listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt())).thenReturn(Lists.newArrayList(apply));
        when(contractTaskMapperB.selectByPrimaryKey(anyLong())).thenReturn(contractTask);
        when(wechatAuthBizB.getAuthStatus(any(MerchantProviderParams.class), anyInt())).thenReturn(true);

        when(merchantProviderParamsMapper.selectByExample(any())).thenReturn(Arrays.asList(merchantProviderParams));
        when(applicationApolloConfig.getDirectApplyAliCheckSwitch()).thenReturn(true);
        when(alipayAuthBiz.queryAuthByMchId(merchantProviderParams)).thenReturn(false);
        bankDirectAuthStatusJobHandler.execute(paramB);

        verify(bankDirectApplyMapperB, times(1)).listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt());
        verify(contractTaskMapperB, times(1)).selectByPrimaryKey(anyLong());
        verify(wechatAuthBizB, times(1)).getAuthStatus(any(MerchantProviderParams.class), anyInt());
        verify(alipayAuthBiz, times(1)).queryAuthByMchId(any(MerchantProviderParams.class));
        verify(bankDirectApplyMapperB, times(2)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void execute_AuthenticationSuccessful_StatusUpdated() {
        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setTask_id(1L);
        apply.setDev_code("devCode");
        apply.setExtra(extra);
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams().setCtime(System.currentTimeMillis());
        ContractTask contractTask = new ContractTask();
        contractTask.setUpdate_at(new Date());

        when(bankDirectApplyMapperB.listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt())).thenReturn(Lists.newArrayList(apply));
        when(contractTaskMapperB.selectByPrimaryKey(anyLong())).thenReturn(contractTask);
        when(wechatAuthBizB.getAuthStatus(any(MerchantProviderParams.class), anyInt())).thenReturn(true);

        when(merchantProviderParamsMapper.selectByExample(any())).thenReturn(Arrays.asList(merchantProviderParams));
        when(applicationApolloConfig.getDirectApplyAliCheckSwitch()).thenReturn(true);
        when(alipayAuthBiz.queryAuthByMchId(merchantProviderParams)).thenReturn(true);
        bankDirectAuthStatusJobHandler.execute(paramB);

        verify(bankDirectApplyMapperB, times(1)).listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt());
        verify(contractTaskMapperB, times(1)).selectByPrimaryKey(anyLong());
        verify(wechatAuthBizB, times(1)).getAuthStatus(any(MerchantProviderParams.class), anyInt());
        verify(alipayAuthBiz, times(1)).queryAuthByMchId(any(MerchantProviderParams.class));
        verify(bankDirectApplyMapperB, times(2)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void execute_ExceptionThrown_LogsAndSendsMessage() {
        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setTask_id(1L);
        apply.setDev_code("devCode");
        apply.setExtra(extra);

        when(bankDirectApplyMapperB.listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt())).thenReturn(Lists.newArrayList(apply));
        when(contractTaskMapperB.selectByPrimaryKey(anyLong())).thenThrow(new RuntimeException("Test exception"));

        bankDirectAuthStatusJobHandler.execute(paramB);

        verify(bankDirectApplyMapperB, times(1)).listByProcessStatusAndPriorityLimit(any(), anyString(), anyInt());
        verify(contractTaskMapperB, times(1)).selectByPrimaryKey(anyLong());
        verify(chatBotUtilB, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
