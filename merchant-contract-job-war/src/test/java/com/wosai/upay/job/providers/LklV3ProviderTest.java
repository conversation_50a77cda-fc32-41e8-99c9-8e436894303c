package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.mc.service.MerchantService;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.upay.job.biz.BusinessRuleBiz;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WeixinAuthApplyBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.consumer.StoreCreateConsumer;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.handlers.EventHandlerContext;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.service.ContractEventService;
import com.wosai.upay.job.service.LakalaCallBackService;
import com.wosai.upay.job.service.SelfHelpNetInEventService;
import com.wosai.upay.merchant.contract.enume.LklPicTypeV3;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import com.wosai.upay.merchant.contract.model.weixin.WeixinParam;
import com.wosai.upay.merchant.contract.service.TongLianService;
import com.wosai.upay.merchant.contract.service.WeixinService;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.Map;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/4/29 3:31 下午
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class LklV3ProviderTest {

    @Autowired
    RuleContext ruleContext;

    @Autowired
    SelfHelpNetInEventService selfHelpNetInEventService;

    @Autowired
    ContractEventService contractEventService;

    /**
     * 插入event
     */
    @Test
    public void insertEvent() {
        Map source = JSONObject.parseObject("{\"operator\":\"BD\",\"platform\":\"CRM\",\"remark\":\"入网新增卡\"}", Map.class);
        NewMchNetInReq req = new NewMchNetInReq();
        req.setMerchantSn("21690003580495");
        req.setOrganizationId("85bdb5889fdba96980ebbf75");
        req.setPlatform("CRM");
        contractEventService.saveContractEventV3(req);
    }

    @Autowired
    EventHandlerContext eventHandlerContext;

    @Autowired
    ContractEventMapper contractEventMapper;

    @Autowired
    ParamContextBiz paramContextBiz;

    @Autowired
    CommonEventHandler commonEventHandler;

    @Autowired
    private BusinessRuleBiz biz;

    @Test
    public void processEvent() throws Exception {

        NetInRuleGroups ruleGroupId = biz.getRuleGroupId("21690003580136", "85bdb5889fdba96980ebbf75");
        ContractEvent event = contractEventMapper.selectByPrimaryKey(1111111111622252L);
        commonEventHandler.handle(event);
    }

    @Autowired
    StoreCreateConsumer storeCreateConsumer;

    @Autowired
    LakalaCallBackService lakalaCallBackService;

    @Test
    public void uploadPic() {
        lakalaCallBackService.savePicTaskByRiskV3("21690003178790", LklPicTypeV3.ID_CARD_FRONT, "https://private-images.shouqianba.com/8f/3--8f4ee47a32c1b957572fcde9e744bc81.jpeg?Expires=1570604996&OSSAccessKeyId=LTAIf4w9oUgn1uKg&Signature=n3J9iTlWjhco83GsmRp3aNB1Nqw%3D");
    }

    @Autowired
    ContractTaskMapper taskMapper;

    @Autowired
    ContractSubTaskMapper subTaskMapper;

    @Autowired
    SubTaskHandlerContext subTaskHandlerContext;

    @Test
    public void processContractTask() throws Exception {
        ContractTask task = taskMapper.selectByPrimaryKey(24827014L);
        ContractSubTask subTask = subTaskMapper.selectByPrimaryKey(568012L);
        subTaskHandlerContext.handle(task, subTask);
    }

    @Test
    public void processShopTask() throws Exception {
        ContractTask task = taskMapper.selectByPrimaryKey(2795904L);
        ContractSubTask subTask = subTaskMapper.selectByPrimaryKey(42011613L);
        subTaskHandlerContext.handle(task, subTask);
    }

    @Test
    public void processInsertTermTask() throws Exception {
        ContractTask task = taskMapper.selectByPrimaryKey(2795908L);
        ContractSubTask subTask = subTaskMapper.selectByPrimaryKey(42011648L);
        subTaskHandlerContext.handle(task, subTask);
    }

//    @Test
//    public void queryContract() {
//        schedule.queryLklV3ContractStatus();
//    }

    @Test
    public void contractCallBack() {
//        String str = "{\n" +
//                "            \"appid\":\"\",\n" +
//                "            \"cmdRetCode\":\"GLOBAL_SUCCESS\",\n" +
//                "            \"reqId\":\"\",\n" +
//                "            \"respData\":{\n" +
//                "                \"merCupNo\":\"cup_no1685435407458\",\n" +
//                "                \"orderNo\":\"orderNo1685435407458\",\n" +
//                "                \"contractStatus\":\"WAIT_FOR_CONTACT\",\n" +
//                "                \"orgCode\":\"228783\",\n" +
//                "                \"termDatas\":null,\n" +
//                "                \"contractId\":\"202408092685509815\",\n" +
//                "                \"merInnerNo\":\"merInnerNo1685435407458\",\n" +
//                "                \"contractMemo\":\"结算卡验证失败\"\n" +
//                "            },\n" +
//                "            \"retCode\":\"000000\",\n" +
//                "            \"retMsg\":\"交易成功\",\n" +
//                "            \"timestamp\":1685435407458,\n" +
//                "            \"ver\":\"1.0.0\"\n" +
//                "        }";

        String callBack = "{\n" +
                "        \"code\": \"000000\",\n" +
                "        \"data\": {\n" +
                "            \"contractId\": \"202408092685509815\",\n" +
                "            \"contractStatus\": \"WAIT_FOR_CONTACT\",\n" +
                "            \"merCupNo\": \"82255107392016P\",\n" +
                "            \"merInnerNo\": \"merInnerNo1685435407458\",\n" +
                "            \"orderNo\": \"2475872024081211024982100000\",\n" +
                "            \"orgCode\": \"247587\",\n" +
                "            \"termDatas\": [\n" +
                "                {\n" +
                "                    \"activeNo\": \"748499128408\",\n" +
                "                    \"busiStatus\": \"VALID\",\n" +
                "                    \"busiTypeCode\": \"QR_CODE_CARD\",\n" +
                "                    \"busiTypeName\": \"扫码\",\n" +
                "                    \"devSerialNo\": \"21590000001192165\",\n" +
                "                    \"productCode\": \"SQB_SCAN_CODE\",\n" +
                "                    \"productId\": 200701,\n" +
                "                    \"productName\": \"收钱吧扫码\",\n" +
                "                    \"shopId\": \"*************\",\n" +
                "                    \"termId\": \"875333505269399552\",\n" +
                "                    \"termNo\": \"J8499128\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"message\": \"成功\"\n" +
                "    }\n" ;
        lakalaCallBackService.contractV3CallBack(JSON.parseObject(callBack, Map.class));
    }

    @Test
    public void contractV3CallBackBankCard(){
        String str = "{\n" +
                "            \"appid\":\"\",\n" +
                "            \"cmdRetCode\":\"GLOBAL_SUCCESS\",\n" +
                "            \"reqId\":\"\",\n" +
                "            \"respData\":{\n" +
                "                \"merCupNo\":\"cup_no*************\",\n" +
                "                \"orderNo\":\"orderNo*************\",\n" +
                "                \"contractStatus\":\"WAIT_FOR_CONTACT\",\n" +
                "                \"orgCode\":\"228783\",\n" +
                "                \"termDatas\":[\n" +
                "                    {\n" +
                "                        \"busiTypeName\":\"银行卡\",\n" +
                "                        \"termId\":\"*************\",\n" +
                "                        \"productCode\":\"SQB_POS\",\n" +
                "                        \"productId\":200704,\n" +
                "                        \"devSerialNo\":\"0c1eef6e-c6aa-4672-b391-ebb0a1515e39\",\n" +
                "                        \"busiStatus\":\"VALID\",\n" +
                "                        \"busiTypeCode\":\"BANK_CARD\",\n" +
                "                        \"shopId\":\"614852429758717953\",\n" +
                "                        \"termNo\":\"F1313783\",\n" +
                "                        \"activeNo\":\"************\",\n" +
                "                        \"productName\":\"收钱吧POS\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"busiTypeName\":\"扫码\",\n" +
                "                        \"termId\":\"*************\",\n" +
                "                        \"productCode\":\"SQB_POS\",\n" +
                "                        \"productId\":200704,\n" +
                "                        \"devSerialNo\":\"0c1eef6e-c6aa-4672-b391-ebb0a1515e39\",\n" +
                "                        \"busiStatus\":\"VALID\",\n" +
                "                        \"busiTypeCode\":\"QR_CODE_CARD\",\n" +
                "                        \"shopId\":\"614852429758717953\",\n" +
                "                        \"termNo\":\"F7654852\",\n" +
                "                        \"activeNo\":\"374803587553\",\n" +
                "                        \"productName\":\"收钱吧POS\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"contractId\":\"1681119783821\",\n" +
                "                \"merInnerNo\":\"merInnerNo*************\",\n" +
                "                \"contractMemo\":\"\"\n" +
                "            },\n" +
                "            \"retCode\":\"000000\",\n" +
                "            \"retMsg\":\"交易成功\",\n" +
                "            \"timestamp\":*************,\n" +
                "            \"ver\":\"1.0.0\"\n" +
                "        }";
        Map req = JSON.parseObject(str, Map.class);
        lakalaCallBackService.contractV3CallBack(req);
    }

    @Autowired
    MerchantService merchantService;

    @Test
    public void processTask() {
        ContractEvent event = contractEventMapper.selectByPrimaryKey(1111111111622252L);
        commonEventHandler.doHandle(event);
    }

    @Test
    public void notTask() throws Exception {
        ContractTask task = taskMapper.selectByPrimaryKey(186L);
        ContractSubTask subTask = subTaskMapper.selectByPrimaryKey(824L);
        subTaskHandlerContext.handle(task, subTask);
    }

    @Autowired
    WeixinAuthApplyBiz weixinAuthApplyBiz;

    @Autowired
    MchAuthApplyMapper mchAuthApplyMapper;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    ContractParamsBiz contractParamsBiz;
    @Autowired
    TongLianService tongLianService;

    @Test
    public void querySub() throws MpayException, MpayApiNetworkError {
        MerchantProviderParams param = merchantProviderParamsMapper.selectByPrimaryKey("04e1a6a7-05f0-4ca8-8b08-e567d7663ec2");
        WeixinParam weixinParam = contractParamsBiz.buildContractParams(String.valueOf(param.getProvider()), param.getPayway(), param.getChannel_no(), WeixinParam.class);
        weixinParam.setSub_mch_id(param.getPay_merchant_id());
        MchInfo mchInfo = tongLianService.queryWeChatMchInfoParams(param.getPay_merchant_id());
        System.out.println(mchInfo);
    }


    @Autowired
    WeixinService weixinService;

    @Autowired
    LklV3Provider provider;

    @Test
    public void updateAddShopAndTermTask() {
        provider.updateAddShopAndTermTask("21690003165644", 123L);
    }

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Test
    public void delayTask(){
//        ContractSubTask update = new ContractSubTask().setId(subTask.getId()).setPriority(DateUtils.addMinutes(new Date(), 5));
//        if (StringUtil.isNotEmpty(message)) {
//            update.setResult(message);
//        }
//        contractSubTaskMapper.updateByPrimaryKey(update);
        ContractSubTask subTask = contractSubTaskMapper.selectByPrimaryKey(42283003L);
        ContractSubTask update = new ContractSubTask().setId(subTask.getId()).setPriority(DateUtils.addMinutes(new Date(), 5));
        contractSubTaskMapper.updateByPrimaryKey(update);

    }

    @Autowired
    PayForProvider payForProvider;

    @Autowired
    PayForTaskMapper payForTaskMapper;

    @Test
    public void payfor(){
        PayForTask payForTask = payForTaskMapper.selectByPrimaryKey(207011L);
        payForProvider.processTask(payForTask);
    }

}