package com.wosai.upay.job.refactor.unit.biz.acquirer;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerCommonTemplate;
import com.wosai.upay.job.refactor.biz.settlement.AcquirerSupportSettlementBiz;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McRuleGroupDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.PaywayEnum;
import com.wosai.upay.job.refactor.service.impl.McRulesDecisionServiceImpl;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.UnionAlipayParam;
import com.wosai.upay.merchant.contract.service.NewUnionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AbstractAcquirerHandler 单元测试
 *
 * <AUTHOR>
 * @date 2023/12/01
 */
@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class AbstractAcquirerHandlerTest {

    @Mock
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Mock
    private NewUnionService newUnionService;

    @Mock
    private ContractParamsBiz contractParamsBiz;

    @Mock
    private AcquirerCommonTemplate acquirerCommonTemplate;

    @Mock
    private ContractSubTaskDAO contractSubTaskDAO;

    @Mock
    private AcquirerSupportSettlementBiz acquirerSupportSettlementBiz;

    @Mock
    private McAcquirerDAO mcAcquirerDAO;

    @Mock
    private McRuleGroupDAO mcRuleGroupDAO;

    @Mock
    private McRulesDecisionServiceImpl mcRulesDecisionService;

    @InjectMocks
    private TestAcquirerHandler testAcquirerHandler;

    private MerchantProviderParamsDO mockParamsDO;
    private final String PARAMS_ID = "test-params-id";
    private final String NEW_UNION_MERCHANT_ID = "new-union-merchant-id";

    @Before
    public void setUp() {
        // 准备测试数据
        mockParamsDO = new MerchantProviderParamsDO();
        mockParamsDO.setMerchantSn("test-merchant-sn");
        mockParamsDO.setPayMerchantId("test-pay-merchant-id");
        mockParamsDO.setPayway(PaywayEnum.ALIPAY.getValue());
        mockParamsDO.setProvider(1);
        mockParamsDO.setChannelNo("test-channel");

        // 使用 ReflectionTestUtils 手动注入依赖
        ReflectionTestUtils.setField(testAcquirerHandler, "merchantProviderParamsDAO", merchantProviderParamsDAO);
        ReflectionTestUtils.setField(testAcquirerHandler, "newUnionService", newUnionService);
        ReflectionTestUtils.setField(testAcquirerHandler, "contractParamsBiz", contractParamsBiz);
        ReflectionTestUtils.setField(testAcquirerHandler, "acquirerCommonTemplate", acquirerCommonTemplate);
        ReflectionTestUtils.setField(testAcquirerHandler, "contractSubTaskDAO", contractSubTaskDAO);
        ReflectionTestUtils.setField(testAcquirerHandler, "acquirerSupportSettlementBiz", acquirerSupportSettlementBiz);
        ReflectionTestUtils.setField(testAcquirerHandler, "mcAcquirerDAO", mcAcquirerDAO);
        ReflectionTestUtils.setField(testAcquirerHandler, "mcRuleGroupDAO", mcRuleGroupDAO);
        ReflectionTestUtils.setField(testAcquirerHandler, "mcRulesDecisionService", mcRulesDecisionService);

        // Mock ContractParamsBiz - 使用正确的参数匹配器
        UnionAlipayParam alipayParam = new UnionAlipayParam();
        when(contractParamsBiz.buildContractParams(anyString(), anyInt(), anyString(), eq(UnionAlipayParam.class)))
                .thenReturn(alipayParam);
    }

    /**
     * 测试正常情况下更新成功
     */
    @Test
    public void testUpdatePayUnionMerchantIdToUnion_Success() {
        // Given
        when(merchantProviderParamsDAO.getByPrimaryKey(PARAMS_ID))
                .thenReturn(Optional.of(mockParamsDO));
        
        ContractResponse successResponse = new ContractResponse();
        successResponse.setCode(200);
        when(newUnionService.updateAlipayUnionMerchantIdWithParams(anyString(), anyString(), any(UnionAlipayParam.class)))
                .thenReturn(successResponse);

        // When
        boolean result = testAcquirerHandler.updatePayUnionMerchantIdToUnion(PARAMS_ID, NEW_UNION_MERCHANT_ID);

        // Then
        assertTrue(result);
        verify(newUnionService, times(1))
                .updateAlipayUnionMerchantIdWithParams(eq(mockParamsDO.getPayMerchantId()), eq(NEW_UNION_MERCHANT_ID), any(UnionAlipayParam.class));
    }

    /**
     * 测试交易参数不存在的情况
     */
    @Test
    public void testUpdatePayUnionMerchantIdToUnion_ParamsNotFound() {
        // Given
        when(merchantProviderParamsDAO.getByPrimaryKey(PARAMS_ID))
                .thenReturn(Optional.empty());

        // When
        boolean result = testAcquirerHandler.updatePayUnionMerchantIdToUnion(PARAMS_ID, NEW_UNION_MERCHANT_ID);

        // Then
        assertFalse(result);
        verify(newUnionService, never())
                .updateAlipayUnionMerchantIdWithParams(anyString(), anyString(), any(UnionAlipayParam.class));
    }

    /**
     * 测试遇到频率限制错误不重试直接失败（非 Spring Retry 环境下）
     */
    @Test
    public void testUpdatePayUnionMerchantIdToUnion_RateLimitWithoutRetry() {
        // Given
        when(merchantProviderParamsDAO.getByPrimaryKey(PARAMS_ID))
                .thenReturn(Optional.of(mockParamsDO));

        ContractResponse rateLimitResponse = new ContractResponse();
        rateLimitResponse.setCode(500);
        rateLimitResponse.setMessage("包含调用频率超限");

        when(newUnionService.updateAlipayUnionMerchantIdWithParams(anyString(), anyString(), any(UnionAlipayParam.class)))
                .thenReturn(rateLimitResponse);

        // When
        boolean result = testAcquirerHandler.updatePayUnionMerchantIdToUnion(PARAMS_ID, NEW_UNION_MERCHANT_ID);

        // Then
        assertFalse(result);
        // 在非 Spring 环境下，重试机制不会生效，只调用1次
        verify(newUnionService, times(1))
                .updateAlipayUnionMerchantIdWithParams(eq(mockParamsDO.getPayMerchantId()), eq(NEW_UNION_MERCHANT_ID), any(UnionAlipayParam.class));
    }

    /**
     * 测试遇到其他类型错误不重试直接失败
     */
    @Test
    public void testUpdatePayUnionMerchantIdToUnion_OtherErrorNoRetry() {
        // Given
        when(merchantProviderParamsDAO.getByPrimaryKey(PARAMS_ID))
                .thenReturn(Optional.of(mockParamsDO));

        ContractResponse errorResponse = new ContractResponse();
        errorResponse.setCode(500);
        errorResponse.setMessage("其他类型错误");

        when(newUnionService.updateAlipayUnionMerchantIdWithParams(anyString(), anyString(), any(UnionAlipayParam.class)))
                .thenReturn(errorResponse);

        // When
        boolean result = testAcquirerHandler.updatePayUnionMerchantIdToUnion(PARAMS_ID, NEW_UNION_MERCHANT_ID);

        // Then
        assertFalse(result);
        // 不应该重试，只调用1次
        verify(newUnionService, times(1))
                .updateAlipayUnionMerchantIdWithParams(eq(mockParamsDO.getPayMerchantId()), eq(NEW_UNION_MERCHANT_ID), any(UnionAlipayParam.class));
    }

    /**
     * 测试抛出异常的情况
     */
    @Test
    public void testUpdatePayUnionMerchantIdToUnion_Exception() {
        // Given
        when(merchantProviderParamsDAO.getByPrimaryKey(PARAMS_ID))
                .thenReturn(Optional.of(mockParamsDO));

        when(newUnionService.updateAlipayUnionMerchantIdWithParams(anyString(), anyString(), any(UnionAlipayParam.class)))
                .thenThrow(new RuntimeException("测试异常"));

        // When
        boolean result = testAcquirerHandler.updatePayUnionMerchantIdToUnion(PARAMS_ID, NEW_UNION_MERCHANT_ID);

        // Then
        assertFalse(result);
    }

    /**
     * 测试 ContractResponse 为 null 的情况
     */
    @Test
    public void testUpdatePayUnionMerchantIdToUnion_NullResponse() {
        // Given
        when(merchantProviderParamsDAO.getByPrimaryKey(PARAMS_ID))
                .thenReturn(Optional.of(mockParamsDO));

        when(newUnionService.updateAlipayUnionMerchantIdWithParams(anyString(), anyString(), any(UnionAlipayParam.class)))
                .thenReturn(null);

        // When
        boolean result = testAcquirerHandler.updatePayUnionMerchantIdToUnion(PARAMS_ID, NEW_UNION_MERCHANT_ID);

        // Then
        assertFalse(result);
        verify(newUnionService, times(1))
                .updateAlipayUnionMerchantIdWithParams(eq(mockParamsDO.getPayMerchantId()), eq(NEW_UNION_MERCHANT_ID), any(UnionAlipayParam.class));
    }

    /**
     * 测试用的具体 AcquirerHandler 实现
     */
    public static class TestAcquirerHandler extends AbstractAcquirerHandler {
        
        @Override
        public AcquirerTypeEnum getType() {
            return AcquirerTypeEnum.LKL_V3;
        }

        @Override
        public String getTypeValue() {
            return AcquirerTypeEnum.LKL_V3.getValue();
        }
    }
} 