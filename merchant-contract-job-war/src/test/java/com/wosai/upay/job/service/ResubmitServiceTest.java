package com.wosai.upay.job.service;


import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Arrays;

import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyLong;

public class ResubmitServiceTest extends BaseTest {


    @Autowired
    ResubmitService resubmitService;

    @MockBean
    private ContractTaskMapper contractTaskMapper;
    @MockBean
    private ContractSubTaskMapper contractSubTaskMapper;



    @Test
    public void reSubmitByTaskId() {
        //任务不存在
        Mockito.doReturn(null).when(contractTaskMapper).selectByPrimaryKey(2539169L);
        resubmitService.reSubmitByTaskId(2539169);
        Mockito.doReturn(new ContractTask().setStatus(5)).when(contractTaskMapper).selectByPrimaryKey(2539169L);
        resubmitService.reSubmitByTaskId(2539169);

        ContractTask task = new ContractTask().setStatus(6).setMerchant_sn("21690002971171").setResult("{}").setId(2539169L);
        ContractSubTask sub = new ContractSubTask().setTask_type(5).setStatus(6).setChannel("lkl");
        Mockito.doReturn(task).when(contractTaskMapper).selectByPrimaryKey(2539169L);
        Mockito.doReturn(task).when(contractTaskMapper).selectLastByMerchantSn("21690002971171");
        Mockito.doReturn(Arrays.asList(sub)).when(contractSubTaskMapper).selectByPTaskIdAndStatus(anyLong(), anyInt());
        resubmitService.reSubmitByTaskId(2539169);
    }

    @Test
    public void editOpnion() {
        Mockito.doReturn(new ContractTask().setStatus(5)).when(contractTaskMapper).selectByPrimaryKey(2539169L);
        resubmitService.editOpnion(2539169, "单元测试");
        ContractTask task = new ContractTask().setStatus(6).setType("新增商户入网").setId(2539169L).setMerchant_sn("21690002971171")
                .setEvent_context("{\"merchant\":{" +
                        "merchant_id:" + "21212121" +
                        "}}");
        Mockito.doReturn(task).when(contractTaskMapper).selectByPrimaryKey(2539169L);
        Mockito.doReturn(task).when(contractTaskMapper).getBySnAndType("21690002971171", "新增商户入网");
        resubmitService.editOpnion(2539169, "单元测试");

    }
}
