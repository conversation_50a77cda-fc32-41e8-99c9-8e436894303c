package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.batch.contracttask.ContractTaskLklFileUploadJobHandler;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ContractTaskLklFileUploadJobHandlerTest {

    @InjectMocks
    private ContractTaskLklFileUploadJobHandler contractTaskLklFileUploadJobHandler;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @Mock
    private SubTaskHandlerContext subTaskHandlerContext;

    @Mock
    private ChatBotUtil chatBotUtil;

    private BatchJobParam batchJobParam;

    private ContractTask contractTask;
    private ContractSubTask contractSubTask;

    @Before
    public void setUp() {
        batchJobParam = new BatchJobParam();
        batchJobParam.setBatchSize(10);
        batchJobParam.setQueryTime(1000L);

        contractTask = new ContractTask();
        contractTask.setId(1L);
        contractTask.setMerchant_sn("testMerchant");

        contractSubTask = new ContractSubTask();
        contractSubTask.setId(1L);
        contractSubTask.setP_task_id(1L);
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsContractTaskList() {
        List<ContractTask> expectedTasks = new ArrayList<>();
        expectedTasks.add(new ContractTask());
        expectedTasks.add(new ContractTask());

        when(contractTaskMapper.selectPicUploadTo(Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(expectedTasks);

        List<ContractTask> actualTasks = contractTaskLklFileUploadJobHandler.queryTaskItems(batchJobParam);

        assertEquals(expectedTasks.size(), actualTasks.size());
    }

    @Test
    public void queryTaskItems_EmptyResult_ReturnsEmptyList() {
        when(contractTaskMapper.selectPicUploadTo(Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(new ArrayList<>());

        List<ContractTask> actualTasks = contractTaskLklFileUploadJobHandler.queryTaskItems(batchJobParam);

        assertTrue(actualTasks.isEmpty());
    }

    @Test
    public void queryTaskItems_InvalidQueryTime_ReturnsEmptyList() {
        batchJobParam.setQueryTime(-1000L);

        when(contractTaskMapper.selectPicUploadTo(Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(new ArrayList<>());

        List<ContractTask> actualTasks = contractTaskLklFileUploadJobHandler.queryTaskItems(batchJobParam);

        assertTrue(actualTasks.isEmpty());
    }

    @Test
    public void queryTaskItems_InvalidBatchSize_ReturnsEmptyList() {
        batchJobParam.setBatchSize(-5);

        when(contractTaskMapper.selectPicUploadTo(Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(new ArrayList<>());

        List<ContractTask> actualTasks = contractTaskLklFileUploadJobHandler.queryTaskItems(batchJobParam);

        assertTrue(actualTasks.isEmpty());
    }

    @Test
    public void getLockKey_WithNonNullId_ReturnsCorrectLockKey() {
        contractTask.setId(123L);
        String expectedLockKey = "ContractTaskLklFileUploadJobHandler:123";
        String actualLockKey = contractTaskLklFileUploadJobHandler.getLockKey(contractTask);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void getLockKey_WithNullId_ReturnsLockKeyWithNull() {
        contractTask.setId(null);
        String expectedLockKey = "ContractTaskLklFileUploadJobHandler:null";
        String actualLockKey = contractTaskLklFileUploadJobHandler.getLockKey(contractTask);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void doHandleSingleData_SuccessfulExecution() throws Exception {
        List<ContractSubTask> subTasks = Arrays.asList(contractSubTask);
        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus(anyString(), anyLong(), anyInt())).thenReturn(subTasks);

        contractTaskLklFileUploadJobHandler.doHandleSingleData(contractTask);

        verify(subTaskHandlerContext, times(1)).handle(eq(contractTask), eq(contractSubTask));
        verify(chatBotUtil, never()).sendMessageToContractWarnChatBot(anyString());
    }

    @Test
    public void doHandleSingleData_ExceptionDuringExecution() throws Exception {
        List<ContractSubTask> subTasks = Arrays.asList(contractSubTask);
        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus(anyString(), anyLong(), anyInt())).thenReturn(subTasks);
        doThrow(new RuntimeException("Test Exception")).when(subTaskHandlerContext).handle(any(ContractTask.class), any(ContractSubTask.class));

        contractTaskLklFileUploadJobHandler.doHandleSingleData(contractTask);

        verify(subTaskHandlerContext, times(1)).handle(eq(contractTask), eq(contractSubTask));
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
