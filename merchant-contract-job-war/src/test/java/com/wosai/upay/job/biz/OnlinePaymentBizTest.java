package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.model.SystemResponse;
import com.wosai.service.SystemService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.upay.job.Constants.OnlinePaymentConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.acquirer.LklV3AcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.OpenOnlinePaymentApplyDAO;
import com.wosai.upay.job.refactor.model.bo.OnlinePaymentApplyAliBO;
import com.wosai.upay.job.refactor.model.bo.OnlinePaymentApplyComboDetailBO;
import com.wosai.upay.job.refactor.model.bo.OnlinePaymentApplyExtraBO;
import com.wosai.upay.job.refactor.model.bo.OnlinePaymentApplyProcessBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.OpenOnlinePaymentApplyDO;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;

@RunWith(MockitoJUnitRunner.class)
public class OnlinePaymentBizTest {

    @InjectMocks
    private OnlinePaymentBiz onlinePaymentBiz;
    @Mock
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Mock
    private OpenOnlinePaymentApplyDAO openOnlinePaymentApplyDAO;
    @Mock
    private WechatAuthBiz wechatAuthBiz;
    @Mock
    private AlipayAuthBiz alipayAuthBiz;
    @Mock
    private SubBizParamsBiz subBizParamsBiz;
    @Mock
    private RuleContext ruleContext;
    @Mock
    private RuleBiz ruleBiz;
    @Mock
    private ParamContextBiz paramContextBiz;
    @Mock
    private ComposeAcquirerBiz composeAcquirerBiz;
    @Mock
    private ApplicationApolloConfig applicationApolloConfig;
    @Mock
    private SystemService systemService;
    @Mock
    private MerchantProviderParamsService merchantProviderParamsService;
    @Mock
    private FeeRateService feeRateService;
    @Mock
    private AcquirerChangeDao acquirerChangeDao;
    @Mock
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Test
    public void reContract01() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.ALIPAY.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.PENDING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.PENDING);

        LklV3AcquirerBiz lklV3AcquirerBiz = Mockito.mock(LklV3AcquirerBiz.class);
        Mockito.doReturn("lkl").when(composeAcquirerBiz).getMerchantAcquirer(openOnlinePaymentApplyDO.getMerchantSn());
        Mockito.doReturn(lklV3AcquirerBiz).when(composeAcquirerBiz).getAcquirerBiz("lkl");
        // 拉卡拉支付宝取报备规则组
        Mockito.doReturn(CollectionUtil.hashMap("merchantBusinessLicense", CollectionUtil.hashMap("type", 0)))
                .when(paramContextBiz).getParamContextByMerchantSn(openOnlinePaymentApplyDO.getMerchantSn(), new ContractEvent().setEvent_type(0));
        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setPayMerchantId("payMerchantId");
        Mockito.doReturn(Optional.of(merchantProviderParamsDO)).when(merchantProviderParamsDAO).getOnlineMerchantProviderParams(openOnlinePaymentApplyDO.getMerchantSn(), ContractRuleConstants.LKL_ORG_NORMAL_ALI_RULE, PaywayEnum.ALIPAY.getValue());
        Mockito.doReturn(new SystemResponse().setResult(true)).when(systemService).createAuthTask(any());

        onlinePaymentBiz.reContractAndSubmitAuth(openOnlinePaymentApplyDO);
        List<OnlinePaymentApplyProcessBO> processList = openOnlinePaymentApplyDO.processList();
        processList.forEach(onlinePaymentApplyProcessBO -> {
            if (OnlinePaymentApplyProcessBO.CONTRACTING.equals(onlinePaymentApplyProcessBO.getStage())) {
                onlinePaymentApplyProcessBO.setFinish(true).setMessage("报备成功，请继续完成认证");
            }
        });
        Mockito.verify(openOnlinePaymentApplyDAO, Mockito.times(1)).updateApply(openOnlinePaymentApplyDO.getId(), OnlinePaymentConstant.ApplyStatus.APPLYING, OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH, null, processList, JSON.parseObject("{\"ali\":{\"payMerchantId\":\"payMerchantId\"}}", OnlinePaymentApplyExtraBO.class));
        Mockito.verify(kafkaTemplate, Mockito.times(1)).send("events_CUA_job_open-online-payment", new OpenOnlinePaymentApplyStatusChange(String.valueOf(openOnlinePaymentApplyDO.getId()), openOnlinePaymentApplyDO.getMerchantSn(), PaywayEnum.ALIPAY.getValue(), openOnlinePaymentApplyDO.getStatus(), OnlinePaymentConstant.ApplyStatus.APPLYING, null));


    }

    /**
     * 需要重新报备 报备成功
     */
    @Test
    public void reContract02() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.WEIXIN.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.PENDING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.PENDING);

        LklV3AcquirerBiz lklV3AcquirerBiz = Mockito.mock(LklV3AcquirerBiz.class);
        Mockito.doReturn("lkl").when(composeAcquirerBiz).getMerchantAcquirer(openOnlinePaymentApplyDO.getMerchantSn());
        Mockito.doReturn(lklV3AcquirerBiz).when(composeAcquirerBiz).getAcquirerBiz("lkl");
        Mockito.doReturn("lklv3-weixin").when(lklV3AcquirerBiz).getNormalWxRule();
        Mockito.doReturn(Optional.empty()).when(merchantProviderParamsDAO).getOnlineMerchantProviderParams(openOnlinePaymentApplyDO.getMerchantSn(), "lklv3-weixin", PaywayEnum.WEIXIN.getValue());
        Mockito.doReturn(CollectionUtil.hashMap("merchant", CollectionUtil.hashMap("name", "单元测试商户"),
                        "merchantBusinessLicense", CollectionUtil.hashMap("type", 1)))
                .when(paramContextBiz).getParamContextByMerchantSn(any(), any());
        Mockito.doReturn("000").when(wechatAuthBiz).getOnlineSettlementId(1, "单元测试商户", false);
        Mockito.doReturn(CollectionUtil.hashMap("param", "paramId")).when(ruleBiz).contractByRule(anyString(), anyObject(), any(), anyBoolean());
        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setPayMerchantId("payMerchantId");
        Mockito.doReturn(Optional.of(merchantProviderParamsDO)).when(merchantProviderParamsDAO).getMerchantProviderParamsById("paramId");
        Mockito.doReturn(new SystemResponse().setResult(true)).when(systemService).createAuthTask(any());

        onlinePaymentBiz.reContractAndSubmitAuth(openOnlinePaymentApplyDO);
        List<OnlinePaymentApplyProcessBO> processList = openOnlinePaymentApplyDO.processList();
        processList.forEach(onlinePaymentApplyProcessBO -> {
            if (OnlinePaymentApplyProcessBO.CONTRACTING.equals(onlinePaymentApplyProcessBO.getStage())) {
                onlinePaymentApplyProcessBO.setFinish(true).setMessage("报备成功，请继续完成认证");
            }
        });
        Mockito.verify(openOnlinePaymentApplyDAO, Mockito.times(1)).updateApply(openOnlinePaymentApplyDO.getId(), OnlinePaymentConstant.ApplyStatus.APPLYING, OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH, null, processList, JSON.parseObject("{\"weixin\":{\"payMerchantId\":\"payMerchantId\"}}", OnlinePaymentApplyExtraBO.class));
        Mockito.verify(kafkaTemplate, Mockito.times(1)).send("events_CUA_job_open-online-payment", new OpenOnlinePaymentApplyStatusChange(String.valueOf(openOnlinePaymentApplyDO.getId()), openOnlinePaymentApplyDO.getMerchantSn(), PaywayEnum.WEIXIN.getValue(), openOnlinePaymentApplyDO.getStatus(), OnlinePaymentConstant.ApplyStatus.APPLYING, null));
    }


    /**
     * 需要重新报备 报备失败
     */
    @Test
    public void reContract03() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.ALIPAY.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.PENDING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.PENDING);

        LklV3AcquirerBiz lklV3AcquirerBiz = Mockito.mock(LklV3AcquirerBiz.class);
        Mockito.doReturn("lkl").when(composeAcquirerBiz).getMerchantAcquirer(openOnlinePaymentApplyDO.getMerchantSn());
        Mockito.doReturn(lklV3AcquirerBiz).when(composeAcquirerBiz).getAcquirerBiz("lkl");
        Mockito.doReturn(Optional.empty()).when(merchantProviderParamsDAO).getOnlineMerchantProviderParams(openOnlinePaymentApplyDO.getMerchantSn(), ContractRuleConstants.LKL_ORG_NORMAL_ALI_RULE, PaywayEnum.ALIPAY.getValue());
        Mockito.doReturn(CollectionUtil.hashMap("merchant", CollectionUtil.hashMap("name", "单元测试商户"),
                        "merchantBusinessLicense", CollectionUtil.hashMap("type", 1)))
                .when(paramContextBiz).getParamContextByMerchantSn(any(), any());
        Mockito.doReturn(CollectionUtil.hashMap("res", "测试报备失败")).when(ruleBiz).contractByRule(anyString(), anyObject(), any(), anyBoolean());

        ContractBizException contractBizException = Assertions.assertThrows(ContractBizException.class, () -> onlinePaymentBiz.reContractAndSubmitAuth(openOnlinePaymentApplyDO));
        assertEquals("测试报备失败", contractBizException.getMessage());
    }

    /**
     * 需要重新报备 强制小微 报备成功
     */
    @Test
    public void reContract04() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.WEIXIN.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.PENDING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.PENDING);

        LklV3AcquirerBiz lklV3AcquirerBiz = Mockito.mock(LklV3AcquirerBiz.class);
        Mockito.doReturn("lkl").when(composeAcquirerBiz).getMerchantAcquirer(openOnlinePaymentApplyDO.getMerchantSn());
        Mockito.doReturn(lklV3AcquirerBiz).when(composeAcquirerBiz).getAcquirerBiz("lkl");
        Mockito.doReturn(CollectionUtil.hashMap("merchant", CollectionUtil.hashMap("name", "单元测试商户"),
                        "merchantBusinessLicense", CollectionUtil.hashMap("type", 1)))
                .when(paramContextBiz).getParamContextByMerchantSn(any(), any());
        // 强制小微
        MerchantProviderParamsDO merchantProviderParamsDO1 = new MerchantProviderParamsDO();
        merchantProviderParamsDO1.setMerchantName("商户_123");
        Mockito.doReturn(Optional.of(merchantProviderParamsDO1)).when(merchantProviderParamsDAO).getInUseProviderParams("merchantSn", 3);
        Mockito.doReturn("000").when(wechatAuthBiz).getOnlineSettlementId(1, "单元测试商户", true);
        Mockito.doReturn(CollectionUtil.hashMap("param", "paramId")).when(ruleBiz).contractByRule(anyString(), anyObject(), any(), anyBoolean());
        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setPayMerchantId("payMerchantId");
        Mockito.doReturn(Optional.of(merchantProviderParamsDO)).when(merchantProviderParamsDAO).getMerchantProviderParamsById("paramId");
        Mockito.doReturn(new SystemResponse().setResult(false).setDesc("提交失败")).when(systemService).createAuthTask(any());

        onlinePaymentBiz.reContractAndSubmitAuth(openOnlinePaymentApplyDO);
        List<OnlinePaymentApplyProcessBO> processList = openOnlinePaymentApplyDO.processList();
        Mockito.verify(openOnlinePaymentApplyDAO, Mockito.times(1)).updateApply(openOnlinePaymentApplyDO.getId(), OnlinePaymentConstant.ApplyStatus.FAIL, OnlinePaymentConstant.ApplyProcessStatus.SUBMIT_AUTH_FAIL, "提交授权申请失败:提交失败", processList, JSON.parseObject("{\"weixin\":{\"payMerchantId\":\"payMerchantId\"}}", OnlinePaymentApplyExtraBO.class));
        Mockito.verify(kafkaTemplate, Mockito.times(1)).send("events_CUA_job_open-online-payment", new OpenOnlinePaymentApplyStatusChange(String.valueOf(openOnlinePaymentApplyDO.getId()), openOnlinePaymentApplyDO.getMerchantSn(), PaywayEnum.WEIXIN.getValue(), openOnlinePaymentApplyDO.getStatus(), OnlinePaymentConstant.ApplyStatus.FAIL, "提交授权申请失败:提交失败"));
    }


    /**
     * 授权通过
     */
    @Test
    public void queryWeixinAuth01() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.WEIXIN.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH);
        openOnlinePaymentApplyDO.setExtra("{\"weixin\":{\"payMerchantId\":\"payMerchantId\"}}");

        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setId("paramId");
        merchantProviderParamsDO.setMerchantSn("merchantSn");
        merchantProviderParamsDO.setParentMerchantId("payMerchantId");
        merchantProviderParamsDO.setProvider(1033);
        merchantProviderParamsDO.setPayway(PaywayEnum.WEIXIN.getValue());
        merchantProviderParamsDO.setContractRule("lkl-1033-3-270860769");
        merchantProviderParamsDO.setDeleted(0);
        Mockito.doReturn(Optional.of(merchantProviderParamsDO)).when(merchantProviderParamsDAO).getMerchantProviderParamsByPayMerchantId("payMerchantId");
        Mockito.doReturn(true).when(wechatAuthBiz).getAuthStatus(merchantProviderParamsDO, merchantProviderParamsDO.getProvider());
        Mockito.doReturn(AcquirerTypeEnum.LKL_V3.getValue()).when(composeAcquirerBiz).getMerchantAcquirer("merchantSn");
        ContractRule contractRule = new ContractRule();
        contractRule.setAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        Mockito.doReturn(contractRule).when(ruleContext).getContractRule("lkl-1033-3-270860769");
        Boolean authStatus = onlinePaymentBiz.queryAuthStatusAndSetDefault(openOnlinePaymentApplyDO);
        assertTrue(authStatus);

        List<OnlinePaymentApplyProcessBO> processList = openOnlinePaymentApplyDO.processList();
        processList.forEach(onlinePaymentApplyProcessBO -> {
            if (OnlinePaymentApplyProcessBO.CONTRACTING.equals(onlinePaymentApplyProcessBO.getStage())) {
                onlinePaymentApplyProcessBO.setFinish(true).setMessage("报备成功，请继续完成认证");
            } else {
                onlinePaymentApplyProcessBO.setFinish(true);
            }
        });
        Mockito.verify(openOnlinePaymentApplyDAO, Mockito.times(1)).updateApply(openOnlinePaymentApplyDO.getId(), OnlinePaymentConstant.ApplyStatus.SUCCESS, OnlinePaymentConstant.ApplyProcessStatus.SUCCESS, "开通成功", processList, null);
        Mockito.verify(kafkaTemplate, Mockito.times(1)).send("events_CUA_job_open-online-payment", new OpenOnlinePaymentApplyStatusChange(String.valueOf(openOnlinePaymentApplyDO.getId()), openOnlinePaymentApplyDO.getMerchantSn(), PaywayEnum.WEIXIN.getValue(), openOnlinePaymentApplyDO.getStatus(), OnlinePaymentConstant.ApplyStatus.SUCCESS, "开通成功"));

    }

    /**
     * 授权未通过 delay
     */
    @Test
    public void queryWeixinAuth02() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.WEIXIN.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH);
        openOnlinePaymentApplyDO.setExtra("{\"weixin\":{\"payMerchantId\":\"payMerchantId\"}}");

        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setId("paramId");
        merchantProviderParamsDO.setMerchantSn("merchantSn");
        merchantProviderParamsDO.setParentMerchantId("payMerchantId");
        merchantProviderParamsDO.setProvider(1033);
        merchantProviderParamsDO.setPayway(PaywayEnum.WEIXIN.getValue());
        merchantProviderParamsDO.setDeleted(0);
        Mockito.doReturn(Optional.of(merchantProviderParamsDO)).when(merchantProviderParamsDAO).getMerchantProviderParamsByPayMerchantId("payMerchantId");
        Mockito.doReturn(false).when(wechatAuthBiz).getAuthStatus(merchantProviderParamsDO, merchantProviderParamsDO.getProvider());

        Boolean authStatus = onlinePaymentBiz.queryAuthStatusAndSetDefault(openOnlinePaymentApplyDO);
        assertFalse(authStatus);
        Mockito.verify(openOnlinePaymentApplyDAO, Mockito.times(1)).delayApply(openOnlinePaymentApplyDO.getId(), 2);
    }

    /**
     * 支付宝授权通过
     */
    @Test
    public void queryWeixinAuth03() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.ALIPAY.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH);
        openOnlinePaymentApplyDO.setExtra("{\"ali\":{\"payMerchantId\":\"payMerchantId\"}}");

        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setId("paramId");
        merchantProviderParamsDO.setMerchantSn("merchantSn");
        merchantProviderParamsDO.setParentMerchantId("payMerchantId");
        merchantProviderParamsDO.setProvider(1033);
        merchantProviderParamsDO.setPayway(PaywayEnum.ALIPAY.getValue());
        merchantProviderParamsDO.setContractRule(ContractRuleConstants.LKL_ORG_NORMAL_ALI_RULE);
        merchantProviderParamsDO.setDeleted(0);
        Mockito.doReturn(Optional.of(merchantProviderParamsDO)).when(merchantProviderParamsDAO).getMerchantProviderParamsByPayMerchantId("payMerchantId");
        Mockito.doReturn(true).when(alipayAuthBiz).queryAuthByMchId(merchantProviderParamsDO);
        ContractRule contractRule = new ContractRule();
        contractRule.setAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        Boolean authStatus = onlinePaymentBiz.queryAuthStatusAndSetDefault(openOnlinePaymentApplyDO);
        assertTrue(authStatus);

        List<OnlinePaymentApplyProcessBO> processList = openOnlinePaymentApplyDO.processList();
        processList.forEach(onlinePaymentApplyProcessBO -> {
            if (OnlinePaymentApplyProcessBO.CONTRACTING.equals(onlinePaymentApplyProcessBO.getStage())) {
                onlinePaymentApplyProcessBO.setFinish(true).setMessage("报备成功，请继续完成认证");
            }
            if (OnlinePaymentApplyProcessBO.AUTHING.equals(onlinePaymentApplyProcessBO.getStage())) {
                onlinePaymentApplyProcessBO.setFinish(true);
            }
        });
        Mockito.verify(openOnlinePaymentApplyDAO, Mockito.times(1)).updateApply(openOnlinePaymentApplyDO.getId(), OnlinePaymentConstant.ApplyStatus.APPLYING, OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUDIT, null, processList, null);
        Mockito.verify(kafkaTemplate, Mockito.times(0)).send("events_CUA_job_open-online-payment", new OpenOnlinePaymentApplyStatusChange(String.valueOf(openOnlinePaymentApplyDO.getId()), openOnlinePaymentApplyDO.getMerchantSn(), PaywayEnum.ALIPAY.getValue(), openOnlinePaymentApplyDO.getStatus(), OnlinePaymentConstant.ApplyStatus.SUCCESS, null));

    }


    /**
     * 授权通过,但是正在切收单机构
     */
    @Test
    public void queryWeixinAuth04() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.WEIXIN.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH);
        openOnlinePaymentApplyDO.setExtra("{\"weixin\":{\"payMerchantId\":\"payMerchantId\"}}");

        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setId("paramId");
        merchantProviderParamsDO.setMerchantSn("merchantSn");
        merchantProviderParamsDO.setParentMerchantId("payMerchantId");
        merchantProviderParamsDO.setProvider(1033);
        merchantProviderParamsDO.setPayway(PaywayEnum.WEIXIN.getValue());
        merchantProviderParamsDO.setContractRule("lkl-1033-3-270860769");
        merchantProviderParamsDO.setDeleted(0);
        Mockito.doReturn(Optional.of(merchantProviderParamsDO)).when(merchantProviderParamsDAO).getMerchantProviderParamsByPayMerchantId("payMerchantId");
        Mockito.doReturn(true).when(wechatAuthBiz).getAuthStatus(merchantProviderParamsDO, merchantProviderParamsDO.getProvider());
        Mockito.doReturn(new McAcquirerChange()).when(acquirerChangeDao).getLatestUnFinishedApply("merchantSn");
        onlinePaymentBiz.queryAuthStatusAndSetDefault(openOnlinePaymentApplyDO);
        Mockito.verify(kafkaTemplate, Mockito.times(1)).send(anyString(), anyObject());
    }

    /**
     * 授权通过,但是收单机构已经切成别的了
     */
    @Test
    public void queryWeixinAuth05() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.WEIXIN.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH);
        openOnlinePaymentApplyDO.setExtra("{\"weixin\":{\"payMerchantId\":\"payMerchantId\"}}");

        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setId("paramId");
        merchantProviderParamsDO.setMerchantSn("merchantSn");
        merchantProviderParamsDO.setParentMerchantId("payMerchantId");
        merchantProviderParamsDO.setProvider(1033);
        merchantProviderParamsDO.setPayway(PaywayEnum.WEIXIN.getValue());
        merchantProviderParamsDO.setContractRule("lkl-1033-3-270860769");
        merchantProviderParamsDO.setDeleted(0);
        Mockito.doReturn(Optional.of(merchantProviderParamsDO)).when(merchantProviderParamsDAO).getMerchantProviderParamsByPayMerchantId("payMerchantId");
        Mockito.doReturn(true).when(wechatAuthBiz).getAuthStatus(merchantProviderParamsDO, merchantProviderParamsDO.getProvider());
        Mockito.doReturn(AcquirerTypeEnum.HAI_KE.getValue()).when(composeAcquirerBiz).getMerchantAcquirer("merchantSn");
        ContractRule contractRule = new ContractRule();
        contractRule.setAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        Mockito.doReturn(contractRule).when(ruleContext).getContractRule("lkl-1033-3-270860769");
        onlinePaymentBiz.queryAuthStatusAndSetDefault(openOnlinePaymentApplyDO);
        Mockito.verify(kafkaTemplate, Mockito.times(1)).send(anyString(), anyObject());
    }

    /**
     * 支付宝审核通过设为默认 支付宝申请单不存在
     */
    @Test
    public void setAliOnlinePayMerchantId01() {
        ContractBizException contractBizException = Assertions.assertThrows(ContractBizException.class, () -> onlinePaymentBiz.setAliOnlinePayMerchantId("merchantSn"));
        Assert.assertEquals("支付宝申请单不存在", contractBizException.getMessage());

        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.ALIPAY.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH);
        openOnlinePaymentApplyDO.setExtra("{\"ali\":{\"payMerchantId\":\"payMerchantId\"}}");
        Mockito.doReturn(Optional.of(openOnlinePaymentApplyDO)).when(openOnlinePaymentApplyDAO).queryLatestOpenOnlinePaymentApply("merchantSn", PaywayEnum.ALIPAY.getValue());
        contractBizException = Assertions.assertThrows(ContractBizException.class, () -> onlinePaymentBiz.setAliOnlinePayMerchantId("merchantSn"));
        Assert.assertEquals("支付宝申请单不存在", contractBizException.getMessage());
    }

    /**
     * 支付宝审核通过设为默认 交易参数不存在
     */
    @Test
    public void setAliOnlinePayMerchantId02() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.ALIPAY.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUDIT);
        openOnlinePaymentApplyDO.setExtra("{\"ali\":{\"payMerchantId\":\"payMerchantId\"}}");
        Mockito.doReturn(Optional.of(openOnlinePaymentApplyDO)).when(openOnlinePaymentApplyDAO).queryLatestOpenOnlinePaymentApply("merchantSn", PaywayEnum.ALIPAY.getValue());

        ContractBizException contractBizException = Assertions.assertThrows(ContractBizException.class, () -> onlinePaymentBiz.setAliOnlinePayMerchantId("merchantSn"));
        Assert.assertEquals("交易参数不存在", contractBizException.getMessage());
    }

    /**
     * 支付宝审核通过设为默认 但是正在切收单机构
     */
    @Test
    public void setAliOnlinePayMerchantId03() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.ALIPAY.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUDIT);
        openOnlinePaymentApplyDO.setExtra("{\"ali\":{\"payMerchantId\":\"payMerchantId\"}}");

        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setId("paramId");
        merchantProviderParamsDO.setMerchantSn("merchantSn");
        merchantProviderParamsDO.setParentMerchantId("payMerchantId");
        merchantProviderParamsDO.setProvider(1033);
        merchantProviderParamsDO.setPayway(PaywayEnum.ALIPAY.getValue());
        merchantProviderParamsDO.setContractRule("lkl-1033-2-2088011691288213");
        merchantProviderParamsDO.setDeleted(0);
        Mockito.doReturn(Optional.of(openOnlinePaymentApplyDO)).when(openOnlinePaymentApplyDAO).queryLatestOpenOnlinePaymentApply("merchantSn", PaywayEnum.ALIPAY.getValue());
        Mockito.doReturn(Optional.of(merchantProviderParamsDO)).when(merchantProviderParamsDAO).getMerchantProviderParamsByPayMerchantId("payMerchantId");

        Mockito.doReturn(new McAcquirerChange()).when(acquirerChangeDao).getLatestUnFinishedApply("merchantSn");
        ContractBizException contractBizException = Assertions.assertThrows(ContractBizException.class, () -> onlinePaymentBiz.setAliOnlinePayMerchantId("merchantSn"));
        Assert.assertEquals("当前商户正在切换收单机构", contractBizException.getMessage());
    }

    /**
     * 支付宝审核通过设为默认 但是正在切收单机构
     */
    @Test
    public void setAliOnlinePayMerchantId04() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.ALIPAY.getValue());
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUDIT);
        openOnlinePaymentApplyDO.setExtra("{\"ali\":{\"payMerchantId\":\"payMerchantId\"}}");

        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setId("paramId");
        merchantProviderParamsDO.setMerchantSn("merchantSn");
        merchantProviderParamsDO.setParentMerchantId("payMerchantId");
        merchantProviderParamsDO.setProvider(1033);
        merchantProviderParamsDO.setPayway(PaywayEnum.ALIPAY.getValue());
        merchantProviderParamsDO.setContractRule("lkl-1033-2-2088011691288213");
        merchantProviderParamsDO.setDeleted(0);
        Mockito.doReturn(Optional.of(openOnlinePaymentApplyDO)).when(openOnlinePaymentApplyDAO).queryLatestOpenOnlinePaymentApply("merchantSn", PaywayEnum.ALIPAY.getValue());
        Mockito.doReturn(Optional.of(merchantProviderParamsDO)).when(merchantProviderParamsDAO).getMerchantProviderParamsByPayMerchantId("payMerchantId");

        Mockito.doReturn(AcquirerTypeEnum.HAI_KE.getValue()).when(composeAcquirerBiz).getMerchantAcquirer("merchantSn");
        ContractRule contractRule = new ContractRule();
        contractRule.setAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        Mockito.doReturn(contractRule).when(ruleContext).getContractRule("lkl-1033-2-2088011691288213");

        ContractBizException contractBizException = Assertions.assertThrows(ContractBizException.class, () -> onlinePaymentBiz.setAliOnlinePayMerchantId("merchantSn"));
        Assert.assertEquals("当前收单机构与线上收款子商户号收单机构不一致", contractBizException.getMessage());
    }

    @Test
    public void resetOnlinePaymentParams01() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.ALIPAY.getValue());
        openOnlinePaymentApplyDO.setExtra("{\"ali\":{\"payMerchantId\":\"aliPayMerchantId\"},\"combo\":{\"crossCity\":{\"tradeComboId\":\"474\",\"feeRate\":\"0.6\"},\"online\":{\"tradeComboId\":\"475\",\"feeRate\":\"0.6\"}}}");
        Mockito.doReturn(JSON.parseObject("{\"onlinePayment\":{\"mappingComboId\":\"475\",\"feeRate\":{\"2\":\"0.78\"}},\"crossCityPayment\":{\"tradeName\":\"跨城收款\"}}")).when(applicationApolloConfig).getAppIdSubBiz();
        Mockito.doReturn(Optional.of(new MerchantProviderParamsDO())).when(merchantProviderParamsDAO).getMerchantProviderParamsByPayMerchantId("aliPayMerchantId");
        onlinePaymentBiz.resetOnlinePaymentParams(openOnlinePaymentApplyDO);
        Mockito.verify(feeRateService, Mockito.times(1)).applyFeeRateOne(any());

    }
}