package com.wosai.upay.job.biz.directparams;


import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.RuleBiz;
import com.wosai.upay.job.model.directparams.BaseParams;
import com.wosai.upay.job.model.directparams.WeixinDirectParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;


public class WeixinDirectParamsBizTest extends BaseTest {

    @Autowired
    WeixinDirectParamsBiz weixinDirectParamsBiz;
    @MockBean
    MerchantProviderParamsBiz merchantProviderParamsBiz;
    @MockBean
    TradeConfigService tradeConfigService;
    @MockBean
    SupportService supportService;
    @MockBean
    MerchantService merchantService;
    @MockBean
    private RuleBiz ruleBiz;


    @Before
    public void reflect() {
        ReflectionTestUtils.setField(weixinDirectParamsBiz, "tradeConfigService", tradeConfigService);
        ReflectionTestUtils.setField(weixinDirectParamsBiz, "supportService", supportService);
        ReflectionTestUtils.setField(weixinDirectParamsBiz, "merchantService", merchantService);

        Mockito.doReturn(new HashMap<>()).when(tradeConfigService).getMerchantConfigByMerchantIdAndPayway(Mockito.anyString(), Mockito.anyInt());
        Map merchant = CollectionUtil.hashMap("merchant_id", "test_id", "sn", "sn");
        Mockito.doReturn(merchant).when(merchantService).getMerchant(Mockito.anyString());
    }

    @Test
    public void addDirectParams() {
        weixinDirectParamsBiz.addDirectParams(getParams());
    }

    private BaseParams getParams() {
        WeixinDirectParams baseParams = new WeixinDirectParams();
        baseParams.setMerchant_sn("sn");
        baseParams.setMerchant_id("test_id");
        WeixinDirectParams.WeixinTradeParams tradeParams = new WeixinDirectParams.WeixinTradeParams();
        tradeParams.setWeixin_sub_appid("sub_app_id");
        WeixinDirectParams.WeixinWapTradeParams wapParams = new WeixinDirectParams.WeixinWapTradeParams();
        wapParams.setWeixin_sub_appsecret("sub_app_secret");

        baseParams.setWeixin_mini_trade_params(tradeParams);
        baseParams.setWeixin_trade_params(tradeParams);
        baseParams.setWeixin_app_trade_params(wapParams);
        baseParams.setWeixin_h5_trade_params(wapParams);
        baseParams.setWeixin_wap_trade_params(wapParams);
        return baseParams;
    }

    @Test
    public void deleteDirectParams() {
        for (String sub : AlipayV2DirectParamsBizTest.subPayWays) {
            weixinDirectParamsBiz.deleteDirectParams(new MerchantProviderParamsDto().setExtra(new HashMap<>()).setMerchant_sn("sn"), sub, "0.38");
        }
    }

    @Test
    public void handleDirectParams() {
        MerchantProviderParamsCustomDto customDto = new MerchantProviderParamsCustomDto();
        customDto.setMerchant_sn("sn");
        customDto.setExtra(JSON.parseObject(JSON.toJSONString(getParams()), Map.class));
        weixinDirectParamsBiz.handleDirectParams(customDto);
    }
}
