package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.model.verify.CheckRemitResp;
import com.wosai.upay.bank.model.verify.VerifyResp;
import com.wosai.upay.bank.service.AccountVerifyService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.MemoApolloConfig;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.enume.AuthApplyStatus;
import com.wosai.upay.job.handlers.ContractSubTaskHandler;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.WechatQrCodeUtils;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import com.wosai.upay.scene.service.activity.response.SceneConfigRecord;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @date 2020/7/16
 */
public class ContractApplyStatusServieImplTest extends H2DbBaseTest {

    @InjectMocks
    private ContractStatusServieImpl contractStatusService;

    @Autowired
    private MemoApolloConfig memoApolloConfig;

    @Autowired
    private PayForResultBiz payForResultBiz;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private PayForTaskMapper payForTaskMapper;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private CompleteDateBiz completeDateBiz;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Mock
    private AccountVerifyService accountVerifyService;

    @Mock
    private ContractEventMapper contractEventMapper;

    @Mock
    private ContractSubTaskHandler contractSubTaskHandler;

    @Mock
    private RuleContext ruleContext;

    @Mock
    private MchAuthApplyMapper mchAuthApplyMapper;

    @Mock
    private ProviderTradeParamsService providerTradeParamsService;

    @Mock
    private RedisService redisService;

    @Mock
    private WechatQrCodeUtils wechatQrCodeUtils;

    @Mock
    private SelfHelpNetInEventService selfHelpNetInEventService;

    @Mock
    private MerchantService merchantService;

    @Mock
    private TradeConfigService tradeConfigService;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ErrorCodeManageBiz errorCodeManageBiz;

    private Map failVerify;

    private Map paymentFail;

    private Map bankChargeBack;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(contractStatusService, "apolloBean", memoApolloConfig);
        ReflectionTestUtils.setField(contractStatusService, "errorCodeManageBiz", errorCodeManageBiz);
        ReflectionTestUtils.setField(contractStatusService, "payForResultBiz", payForResultBiz);
        ReflectionTestUtils.setField(contractStatusService, "contractTaskMapper", contractTaskMapper);
        ReflectionTestUtils.setField(contractStatusService, "contractSubTaskMapper", contractSubTaskMapper);
        ReflectionTestUtils.setField(contractStatusService, "applicationApolloConfig", applicationApolloConfig);
        ReflectionTestUtils.setField(contractStatusService, "redisLock", redisLock);
        ReflectionTestUtils.setField(contractStatusService, "payForTaskMapper", payForTaskMapper);
        ReflectionTestUtils.setField(contractStatusService, "completeDateBiz", completeDateBiz);
        ReflectionTestUtils.setField(contractStatusService, "contractStatusMapper", contractStatusMapper);
        if (MapUtils.isEmpty(failVerify)) {
            final MyObjectMapper mapper = new MyObjectMapper();
            final SceneConfigRecord configRecord = errorCodeManageBiz.getSceneConfigRecord(ScheduleUtil.TIPS_PAY_FOR_VERIFY_FAIL_PUBLIC, errorCodeManageBiz.PLATFORM_LKL);
            failVerify = mapper.convertValue(configRecord,Map.class);
            final SceneConfigRecord configRecordPayment = errorCodeManageBiz.getSceneConfigRecord(ScheduleUtil.TIPS_PAY_FOR_PAYMENT_FAIL_PUBLIC, errorCodeManageBiz.PLATFORM_LKL);
            paymentFail = mapper.convertValue(configRecordPayment,Map.class);
            final SceneConfigRecord configRecordChargeback = errorCodeManageBiz.getSceneConfigRecord(ScheduleUtil.TIPS_PAY_FOR_BANK_CHARGEBACK_PUBLIC, errorCodeManageBiz.PLATFORM_LKL);
            bankChargeBack = mapper.convertValue(configRecordChargeback,Map.class);
        }
    }

    @Test
    public void selectByUpdateTime() {
        List<ContractStatus> result = contractStatusService.selectByUpdateTime(0L);
        assertFalse(result.isEmpty());
    }

    @Test
    public void selectByPrimaryKey() {
        ContractStatus result = contractStatusService.selectByPrimaryKey(2503640L);
        assertEquals(2503640L,result.getId().longValue());
    }

    @Test
    public void selectByMerchantSn() {
        ContractStatus result = contractStatusService.selectByMerchantSn("**************");
        assertEquals("**************",result.getMerchant_sn());
    }

    /**
     * 各种都要测试
     */
    @Test
    public void getMerchantContractStatusByType() {
        ContractTask task = new ContractTask();
        ContractTaskMapper mockMapper = Mockito.mock(ContractTaskMapper.class);
        ContractSubTaskMapper mockSubMapper = Mockito.mock(ContractSubTaskMapper.class);
        ReflectionTestUtils.setField(contractStatusService, "contractTaskMapper", mockMapper);
        ReflectionTestUtils.setField(contractStatusService, "contractSubTaskMapper", mockSubMapper);
        //失败 金额验证失败
        Map failResult = CollectionUtil.hashMap("message", ScheduleUtil.TIPS_PAY_FOR_VERIFY_FAIL_PUBLIC);
        Mockito.doReturn(task.setStatus(6).setResult(JSON.toJSONString(failResult))).when(mockMapper).selectForTipsByMerchantSn(any());
        Map result = contractStatusService.getMerchantContractStatusByType(CollectionUtil.hashMap("type", "crm", "merchant_sn", "fail_verify_merchant_sn"));
        assertEquals(failVerify.get("crm_msg"), result.get("contract_memo"));

        //失败 银行代付失败
        failResult.put("message", ScheduleUtil.TIPS_PAY_FOR_PAYMENT_FAIL_PUBLIC);
        Mockito.doReturn(task.setStatus(6).setResult(JSON.toJSONString(failResult))).when(mockMapper).selectForTipsByMerchantSn(any());
        result = contractStatusService.getMerchantContractStatusByType(CollectionUtil.hashMap("type", "crm", "merchant_sn", "fail_verify_merchant_sn"));
        assertEquals(paymentFail.get("crm_msg"), result.get("contract_memo"));

        //失败 银行退单
        failResult.put("message", ScheduleUtil.TIPS_PAY_FOR_BANK_CHARGEBACK_PUBLIC);
        Mockito.doReturn(task.setStatus(6).setResult(JSON.toJSONString(failResult))).when(mockMapper).selectForTipsByMerchantSn(any());
        result = contractStatusService.getMerchantContractStatusByType(CollectionUtil.hashMap("type", "crm", "merchant_sn", "fail_verify_merchant_sn"));
        assertEquals(bankChargeBack.get("crm_msg"), result.get("contract_memo"));

        //null
        Mockito.doReturn(null).when(mockMapper).selectForTipsByMerchantSn(any());
        result = contractStatusService.getMerchantContractStatusByType(CollectionUtil.hashMap("type", "crm", "merchant_sn", "fail_verify_merchant_sn"));
        assertEquals(ContractStatusCode.NO_TASK.getMsg(), result.get("contract_memo"));

        //提交
        Mockito.doReturn(task.setStatus(0)).when(mockMapper).selectForTipsByMerchantSn(any());
        result = contractStatusService.getMerchantContractStatusByType(CollectionUtil.hashMap("type", "crm", "merchant_sn", "fail_verify_merchant_sn"));
        assertEquals(ContractStatusCode.PENDING_TASK.getMsg(), result.get("contract_memo"));
        //成功
        Mockito.doReturn(task.setStatus(5)).when(mockMapper).selectForTipsByMerchantSn(any());
        result = contractStatusService.getMerchantContractStatusByType(CollectionUtil.hashMap("type", "crm", "merchant_sn", "fail_verify_merchant_sn"));
        assertEquals("进件任务处理成功", result.get("contract_memo"));
        //未知
        Mockito.doReturn(task.setStatus(10)).when(mockMapper).selectForTipsByMerchantSn(any());
        result = contractStatusService.getMerchantContractStatusByType(CollectionUtil.hashMap("type", "crm", "merchant_sn", "fail_verify_merchant_sn"));
        assertEquals(ContractStatusCode.UNKNOWN_STATUS_CODE.getMsg(), result.get("contract_memo"));
        //进行中
        task.setEvent_context(JSON.toJSONString(CollectionUtil.hashMap("bankAccount", CollectionUtil.hashMap("type", 2))));
        Mockito.doReturn(task.setStatus(1)).when(mockMapper).selectForTipsByMerchantSn(any());
        Mockito.doReturn(
                Arrays.asList(new ContractSubTask().setSchedule_status(ScheduleEnum.SCHEDULE_DISABLE.getValue()).setStatus(1).setContract_id("id"))
        ).when(mockSubMapper).getAcquireSubTask(any());
        result = contractStatusService.getMerchantContractStatusByType(CollectionUtil.hashMap("type", "crm", "merchant_sn", "fail_verify_merchant_sn"));
        assertEquals(9999, result.get("contract_code"));
    }

    @Test
    public void getMessageByContractTask() {
        ContractTask task = new ContractTask();
        //提交
        String result = contractStatusService.getMessageByContractTask(task.setStatus(0));
        assertEquals(ContractStatusCode.PENDING_TASK.getMsg(), result);

        //进行中
        ContractSubTaskMapper mockSubMapper = Mockito.mock(ContractSubTaskMapper.class);
        ReflectionTestUtils.setField(contractStatusService, "contractSubTaskMapper", mockSubMapper);
        Mockito.doReturn(null).when(mockSubMapper).getAcquireSubTask(any());
        result = contractStatusService.getMessageByContractTask(task.setStatus(1));
        assertEquals("收单机构进件任务不存在", result);

        //失败
        Map failResult = CollectionUtil.hashMap("message", ScheduleUtil.TIPS_PAY_FOR_VERIFY_FAIL_PUBLIC);
        task.setStatus(6).setResult(JSON.toJSONString(failResult));
        result = contractStatusService.getMessageByContractTask(task);
        assertEquals(failVerify.get("sp_msg"), result);

        //成功
        result = contractStatusService.getMessageByContractTask(task.setStatus(5));
        assertEquals("进件任务处理完成", result);

        //未知
        result = contractStatusService.getMessageByContractTask(task.setStatus(10));
        assertEquals(ContractStatusCode.UNKNOWN_STATUS_CODE.getMsg(), result);
    }

    @Test
    public void getMessageByTaskId() {
        String result = contractStatusService.getMessageByTaskId(2585382L);
        assertNotNull(result);
        thrown.expectMessage("报备任务不存在");
        contractStatusService.getMessageByTaskId(99999999L);
    }

    @Test
    public void rejectContractCheck() {
        ContractStatusMapper mock = Mockito.mock(ContractStatusMapper.class);
        ReflectionTestUtils.setField(contractStatusService,"contractStatusMapper",mock);
        ContractTaskMapper mock2 = Mockito.mock(ContractTaskMapper.class);
        ReflectionTestUtils.setField(contractStatusService,"contractTaskMapper",mock2);
        //非进件驳回状态
        Mockito.doReturn(new ContractStatus().setStatus(2)).when(mock).selectByMerchantSn(any());
        Map result = contractStatusService.rejectContractFieldsV2("merchant_sn", null, new FailEventConf().setFail(true));
        assertEquals(false,result.get("result"));
        //已经有报备事件
        Mockito.doReturn(new ContractStatus().setStatus(3)).when(mock).selectByMerchantSn(any());
        Mockito.doReturn(Arrays.asList(new ContractEvent())).when(contractEventMapper).selectByMerchantSnRejectContract(any());
        result = contractStatusService.rejectContractFieldsV2("merchant_sn", null, new FailEventConf().setFail(true));
        assertEquals(false,result.get("result"));
        //任务正在执行
        Mockito.doReturn(Arrays.asList(new ContractTask())).when(mock2).selectTaskTodoByMerchantSn(any());
        result = contractStatusService.rejectContractFieldsV2("merchant_sn", null, new FailEventConf().setFail(true));
        assertEquals(false,result.get("result"));
    }

    @Test
    public void rejectContractFieldsV2() {
        ContractStatusMapper mock = Mockito.mock(ContractStatusMapper.class);
        ReflectionTestUtils.setField(contractStatusService,"contractStatusMapper",mock);
        ContractTaskMapper mock2 = Mockito.mock(ContractTaskMapper.class);
        ReflectionTestUtils.setField(contractStatusService,"contractTaskMapper",mock2);
        //失败
        Mockito.doReturn(null).when(mock).selectByMerchantSn(any());
        Map result = contractStatusService.rejectContractFieldsV2("merchant_sn", null, null);
        assertEquals(false,result.get("result"));
        //校验成功 1
        Mockito.doReturn(new ContractStatus().setStatus(3)).when(mock).selectByMerchantSn(any());
        Mockito.doReturn(Lists.emptyList()).when(mock2).selectTaskTodoByMerchantSn(any());
        Mockito.doReturn(Lists.emptyList()).when(contractEventMapper).selectByMerchantSnRejectContract(any());
        result = contractStatusService.rejectContractFieldsV2("merchant_sn", null, new FailEventConf().setFail(true));
        assertEquals(true,result.get("result"));
        //校验成功 2
        Mockito.doReturn(CollectionUtil.hashMap("id","id")).when(merchantService).getMerchantBySn(any());
        ContractTask task = new ContractTask();
        task.setResult("{\"channel\":\"wechat_auth\"}");
        Mockito.doReturn(task).when(mock2).selectLastByMerchantSn(any());
        result = contractStatusService.rejectContractFieldsV2("merchant_sn", null, new FailEventConf().setFail(false));
        assertEquals(false,result.get("result"));
    }

    @Test
    public void existMerchant() {
        boolean result = contractStatusService.existMerchant(1, "1");
        assertFalse(result);
    }

    @Test
    public void getWeixinUpgrade() {
        ContractTaskMapper mockMapper = Mockito.mock(ContractTaskMapper.class);
        ReflectionTestUtils.setField(contractStatusService, "contractTaskMapper", mockMapper);
        ContractTask task = new ContractTask();
        //null
        Mockito.doReturn(null).when(mockMapper).getUpgradeTaskByMerchangtSn(any(), ProviderUtil.CONTRACT_TYPE_AUTH);
        Map result = contractStatusService.getWeixinUpgrade("merchant_sn", "crm", ProviderUtil.CONTRACT_TYPE_AUTH);
        assertEquals(ContractStatusCode.NO_TASK.getCode(), result.get("contract_code"));
        //提交
        Mockito.doReturn(task.setStatus(0)).when(mockMapper).getUpgradeTaskByMerchangtSn(any(), ProviderUtil.CONTRACT_TYPE_AUTH);
        result = contractStatusService.getWeixinUpgrade("merchant_sn", "crm", ProviderUtil.CONTRACT_TYPE_AUTH);
        assertEquals(ContractStatusCode.PENDING_TASK.getCode(), result.get("contract_code"));
        //进行中
        task.setStatus(1);
        result = contractStatusService.getWeixinUpgrade("merchant_sn", "crm", ProviderUtil.CONTRACT_TYPE_AUTH);
        assertEquals(ContractStatusCode.UPGRADE_TASK_CONTRACTING.getCode(), result.get("contract_code"));
        //失败
        task.setStatus(6).setEvent_msg("event_msg").setEvent_context("{\"micro_resub\":\"false\"}");
        result = contractStatusService.getWeixinUpgrade("merchant_sn", "crm", ProviderUtil.CONTRACT_TYPE_AUTH);
        assertEquals(ContractStatusCode.UPGRADE_TASK_FAIL.getCode(), result.get("contract_code"));
        //成功
        task.setStatus(5);
        result = contractStatusService.getWeixinUpgrade("merchant_sn", "crm", ProviderUtil.CONTRACT_TYPE_AUTH);
        assertEquals(ContractStatusCode.UPGRADE_TASK_SUCCESS.getCode(), result.get("contract_code"));
        //未知
        task.setStatus(10);
        result = contractStatusService.getWeixinUpgrade("merchant_sn", "crm", ProviderUtil.CONTRACT_TYPE_AUTH);
        assertEquals(ContractStatusCode.UNKNOWN_STATUS_CODE.getCode(), result.get("contract_code"));
    }

    /**
     * 验证失败
     */
    @Test
    public void verifyAmountSuccess() {
        ReflectionTestUtils.setField(payForResultBiz, "taskResultService", Mockito.mock(TaskResultService.class));
        VerifyResp resp = new VerifyResp();
        resp.setMax_retry(3).setRetried(3).setValid(false);
        Mockito.doReturn(resp).when(accountVerifyService).verifyAmount(any());
        contractStatusService.verifyAmount("**************", new BigDecimal(1));
        ContractTask result = contractTaskMapper.selectByPrimaryKey(2585382L);
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_VERIFY_FAIL_PUBLIC, JSON.parseObject(result.getResult(), Map.class).get("message"));
    }

    /**
     * 验证成功
     */
    @Test
    public void verifyAmountFail() {
        ReflectionTestUtils.setField(payForResultBiz, "taskResultService", Mockito.mock(TaskResultService.class));
        VerifyResp resp = new VerifyResp();
        resp.setValid(true);
        Mockito.doReturn(resp).when(accountVerifyService).verifyAmount(any());
        contractStatusService.verifyAmount("**************", new BigDecimal(1));
        ContractTask result = contractTaskMapper.selectByPrimaryKey(2585382L);
        assertEquals("对公代付通过", JSON.parseObject(result.getResult(), Map.class).get("message"));
    }

    @Test
    public void queryPayForResult() {
        ReflectionTestUtils.setField(payForResultBiz, "taskResultService", Mockito.mock(TaskResultService.class));
        Mockito.doReturn(new CheckRemitResp().setStatus(CheckRemitResp.FAIL)).when(accountVerifyService).checkRemitResult(any());
        contractStatusService.queryPayForResult("**************");
        ContractTask result = contractTaskMapper.selectByPrimaryKey(2585382L);
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_BANK_CHARGEBACK_PUBLIC, JSON.parseObject(result.getResult(), Map.class).get("message"));
    }

    @Test
    public void authChannelCode() {
        MerchantProviderParamsMapper mock = Mockito.mock(MerchantProviderParamsMapper.class);
        ReflectionTestUtils.setField(contractStatusService, "merchantProviderParamsMapper", mock);
        MerchantProviderParams params = new MerchantProviderParams();
        Mockito.doReturn(params).when(mock).getByPayMerchantId("sub_merchant_id");
        params.setChannel_no("***********");
        String result = contractStatusService.authChannelCode("sub_merchant_id");
        params.setChannel_no("*********");
        String result2 = contractStatusService.authChannelCode("sub_merchant_id");
        params.setChannel_no("not_exist");
        ContractChannel channel = new ContractChannel();
        channel.setChannel("lkl-1016-3-*********");
        params.setPayway(1).setProvider(1).setChannel_no("1");
        Mockito.doReturn(channel).when(ruleContext).getContractChannel(1, "1", "1");
        String result3 = contractStatusService.authChannelCode("sub_merchant_id");
        assertNotNull(result3);
        assertNotNull(result2);
        assertNotNull(result);
        thrown.expectMessage("子商户号不存在");
        Mockito.doReturn(null).when(mock).getByPayMerchantId("sub_merchant_id");
        contractStatusService.authChannelCode("sub_merchant_id");
    }

    @Test
    public void weixinAuthTip() {
        String eventContext = "{\n" +
                "  \"bankAccount\": {\n" +
                "    \"type\": 1,\n" +
                "    \"holder\": \"自助入网·对私\",\n" +
                "    \"id_type\": 1,\n" +
                "    \"identity\": \"451522199201302371\",\n" +
                "    \"number\": \"6228480405965827372\"\n" +
                "  },\n" +
                "  \"merchantBusinessLicense\": {\n" +
                "    \"type\": 1,\n" +
                "    \"number\": \"abcdefg12345678\",\n" +
                "    \"name\": \"自助入网营业执照\",\n" +
                "    \"legal_person_id_type\": 1,\n" +
                "    \"legal_person_name\": \"营业执照法人abc\",\n" +
                "    \"legal_person_id_number\": \"441522199201302372\",\n" +
                "    \"id_validity\": \"********-********\",\n" +
                "    \"merchant_sn\": \"**************\"\n" +
                "  },\n" +
                "  \"contractUseAuth\": true,\n" +
                "  \"merchant\": {\n" +
                "    \"sn\": \"**************\",\n" +
                "    \"contact_name\": \"自助入网联系人\",\n" +
                "    \"contact_phone\": \"***********\",\n" +
                "    \"contact_cellphone\": \"***********\",\n" +
                "    \"legal_person_name\": \"营业执照法人abc\",\n" +
                "    \"legal_person_id_type\": 1,\n" +
                "    \"legal_person_register_no\": \"abcdefg12345678\",\n" +
                "    \"customer_phone\": \"***********\",\n" +
                "    \"business_name\": \"自助入网自动化abc123\",\n" +
                "    \"concat_identity\": \"441522199201302379\"\n" +
                "  },\n" +
                "  \"taskSource\": \"crm\",\n" +
                "  \"type\": \"1\",\n" +
                "  \"needAuthChannel\": \"1\"\n" +
                "}";
        ContractTask task = new ContractTask();
        task.setId(1L).setEvent_context(eventContext);
        MchAuthApply authApply = new MchAuthApply().setCommit_at(new Date());
        Mockito.doReturn(authApply).when(mchAuthApplyMapper).selectByTaskId(1L);
        //un_submit
        authApply.setStatus(AuthApplyStatus.UN_SUBMIT.getVal());
        Map result = ReflectionTestUtils.invokeMethod(contractStatusService, "weixinAuthTip", task, "crm_msg");
        assertEquals(ContractStatusCode.UPGRADE_TASK_CONTRACTING.getCode(),result.get("contract_code"));
        //MICRO_WATI_SUBMIT
        authApply.setStatus(AuthApplyStatus.MICRO_WATI_SUBMIT.getVal());
        result = ReflectionTestUtils.invokeMethod(contractStatusService, "weixinAuthTip", task, "crm_msg");
        assertEquals("10013", result.get("contract_code"));
        //SUBMIT
        authApply.setStatus(AuthApplyStatus.SUBMIT.getVal());
        result = ReflectionTestUtils.invokeMethod(contractStatusService, "weixinAuthTip", task, "crm_msg");
        assertEquals(ContractStatusCode.UPGRADE_TASK_CONTRACTING.getCode(), result.get("contract_code"));
        //null
        ListResult listResult = ListResult.emptyListResult();
        listResult.setRecords(Arrays.asList(CollectionUtil.hashMap("pay_merchant_id","pay_merchant_id")));
        Mockito.doReturn(listResult).when(providerTradeParamsService).listMerchantProviderParams(any(),any());
        Mockito.doReturn(new ContractChannel()).when(ruleContext).getContractChannel("1");
        Mockito.doReturn(null).when(mchAuthApplyMapper).selectByTaskId(1L);
        result = ReflectionTestUtils.invokeMethod(contractStatusService, "weixinAuthTip", task, "crm_msg");
        assertEquals(ContractStatusCode.UPGRADE_WAIT_FOR_AUTH.getCode(), result.get("contract_code"));
        //WAITTING_FOR_CONFIRM_CONTACT
        authApply.setStatus(AuthApplyStatus.WAITTING_FOR_CONFIRM_CONTACT.getVal());
        Mockito.doReturn(authApply).when(mchAuthApplyMapper).selectByTaskId(1L);
        result = ReflectionTestUtils.invokeMethod(contractStatusService, "weixinAuthTip", task, "crm_msg");
        assertEquals(ContractStatusCode.UPGRADE_MICRO_WAIT_FOR_CONFIRM.getCode(), result.get("contract_code"));
        //PASSED
        authApply.setStatus(AuthApplyStatus.PASSED.getVal());
        result = ReflectionTestUtils.invokeMethod(contractStatusService, "weixinAuthTip", task, "crm_msg");
        assertEquals(ContractStatusCode.UPGRADE_WAIT_FOR_AUTH.getCode(), result.get("contract_code"));
        //PASSED_FREEZE
        authApply.setStatus(AuthApplyStatus.PASSED_FREEZE.getVal());
        Mockito.doReturn("1").when(redisService).getKeyWithoutPrefix(any());
        result = ReflectionTestUtils.invokeMethod(contractStatusService, "weixinAuthTip", task, "crm_msg");
        assertEquals(ContractStatusCode.UPGRADE_APPLY_FREEZE.getCode(), result.get("contract_code"));
    }

    @Test
    public void setAcChannelFromRedis() {
        Map map = CollectionUtil.hashMap("payMchId", "payMchId", "channel", "channel");
        Map request = CollectionUtil.hashMap("extraActivities", Arrays.asList(map));
        ReflectionTestUtils.invokeMethod(contractStatusService,"setAcChannelFromRedis",request,"lzMerchantBusinessName",request);
        assertEquals("lzMerchantBusinessName",((List<Map<String, String>>)request.get("extraActivities")).get(0).get("merchantName"));
    }

    @Test
    public void setAcChannles() {
        Map map = CollectionUtil.hashMap("payMchId", "payMchId", "channel", "channel");
        Map request = CollectionUtil.hashMap("extraActivities", Arrays.asList(map));
        ReflectionTestUtils.invokeMethod(contractStatusService,"setAcChannles",request,"lzMerchantBusinessName",request,request);
        assertEquals("lzMerchantBusinessName",((List<Map<String, String>>)request.get("extraActivities")).get(0).get("merchantName"));
    }

    @Test
    public void getOriginalMemo() {
        ContractTask task = new ContractTask();
        //未知
        Object result = ReflectionTestUtils.invokeMethod(contractStatusService, "getOriginalMemo", task);
        assertEquals("未知失败原因",result);
        //lkl
        task.setResult("{\"message\":\"message\",\"channel\":\"lkl\"}");
        ReflectionTestUtils.invokeMethod(contractStatusService, "getOriginalMemo", task);
        //nucc
        task.setResult("{\"message\":\"message\",\"channel\":\"nucc\"}");
        ReflectionTestUtils.invokeMethod(contractStatusService, "getOriginalMemo", task);
        //union
        task.setResult("{\"message\":\"message\",\"channel\":\"union\"}");
        ReflectionTestUtils.invokeMethod(contractStatusService, "getOriginalMemo", task);
        //union_wm
        task.setResult("{\"message\":\"message\",\"channel\":\"union_wm\"}");
        ReflectionTestUtils.invokeMethod(contractStatusService, "getOriginalMemo", task);
        //pay_for
        task.setResult("{\"message\":\"message\",\"channel\":\"pay_for\"}");
        ReflectionTestUtils.invokeMethod(contractStatusService, "getOriginalMemo", task);
        //lkl_callback
        task.setResult("{\"message\":\"message\",\"channel\":\"lkl_callback\"}");
        ReflectionTestUtils.invokeMethod(contractStatusService, "getOriginalMemo", task);
        //union_open
        task.setResult("{\"message\":\"message\",\"channel\":\"union_open\"}");
        ReflectionTestUtils.invokeMethod(contractStatusService, "getOriginalMemo", task);
        //wechat_auth
        task.setResult("{\"message\":\"message\",\"channel\":\"wechat_auth\"}");
        ReflectionTestUtils.invokeMethod(contractStatusService, "getOriginalMemo", task);
        //tonglian
        task.setResult("{\"message\":\"message\",\"channel\":\"tonglian\"}");
        ReflectionTestUtils.invokeMethod(contractStatusService, "getOriginalMemo", task);
        //未知通道异常
        task.setResult("{\"channel\":\"aaa\"}");
        result = ReflectionTestUtils.invokeMethod(contractStatusService, "getOriginalMemo", task);
        assertEquals("未知通道异常",result);

    }

    @Test
    public void processReviewComplete() {
        ContractTask task = new ContractTask().setUpdate_at(new Date()).setComplete_at(new Date()).setPriority(new Date());
        //null
        Map result = ReflectionTestUtils.invokeMethod(contractStatusService, "processReviewComplete", null, null, "");
        assertNull(result);
        //无contract_code或memo
        result = ReflectionTestUtils.invokeMethod(contractStatusService, "processReviewComplete", CollectionUtil.hashMap("id", "id"), task, "");
        assertNotNull(result);
        //LKL_APPLYING_CODE app
        Map config = CollectionUtil.hashMap("contract_code","10001","contract_memo","单元测试#");
        ReflectionTestUtils.invokeMethod(contractStatusService,"processReviewComplete",config,task,"app_msg");
        //LKL_APPLYING_CODE crm
        ReflectionTestUtils.invokeMethod(contractStatusService,"processReviewComplete",config,task,"crm_msg");
        //LKL_APPLYING_CODE sp
        ReflectionTestUtils.invokeMethod(contractStatusService,"processReviewComplete",config,task,"sp_msg");
        //LKL_REVIEW_CODE
        config.put("contract_code","10004");
        ReflectionTestUtils.invokeMethod(contractStatusService,"processReviewComplete",config,task,"sp_msg");
        //PUBLIC_PAY_FOR_CODE
        config.put("contract_code","10077");
        ReflectionTestUtils.invokeMethod(contractStatusService,"processReviewComplete",config,task,"sp_msg");
        //other
        config.put("contract_code","10078");
        ReflectionTestUtils.invokeMethod(contractStatusService,"processReviewComplete",config,task,"sp_msg");
    }
}