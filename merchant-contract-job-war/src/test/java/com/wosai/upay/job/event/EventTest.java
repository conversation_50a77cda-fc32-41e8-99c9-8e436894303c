package com.wosai.upay.job.event;

import com.wosai.upay.job.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;

/**
 * <AUTHOR>
 * @date 2023/12/19
 */
public class EventTest extends BaseTest {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;


    @Test
    public void testPubishUpdateMerchantBankAccountEvent() throws Exception {
//        applicationEventPublisher.publishEvent(new UpdateMerchantBankAccountEvent(this,
//                1000088017L,
//                PayParamsModel.PROVIDER_LKLORG,
//                ProviderUtil.LKL_ORG_PROVIDER_CHANNEL));
//        TimeUnit.SECONDS.sleep(60);
//        System.out.println("xxx");
    }
}
