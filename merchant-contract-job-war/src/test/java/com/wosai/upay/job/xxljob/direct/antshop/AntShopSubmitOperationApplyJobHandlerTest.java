package com.wosai.upay.job.xxljob.direct.antshop;

import com.wosai.upay.job.Constants.AntShopTaskConstant;
import com.wosai.upay.job.biz.AntShopBiz;
import com.wosai.upay.job.model.DO.AntShopTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AntShopSubmitOperationApplyJobHandlerTest {

    @InjectMocks
    private AntShopSubmitOperationApplyJobHandler handler;

    @Mock
    private AntShopBiz antShopBiz;

    @Mock
    private ChatBotUtil chatBotUtil;

    private DirectJobParam param;

    @Before
    public void setUp() {
        // 如果需要，可以在这里进行任何设置
        param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "AntShopSubmitOperationApplyJobHandler";
        String actualLockKey = handler.getLockKey();
        Assert.assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_ExceptionHandling_TaskStatusUpdatedAndMessageSent() {
        AntShopTask task = new AntShopTask();
        task.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);
        task.setMerchant_sn("12345");
        task.setStatus(AntShopTaskConstant.TaskStatus.WAIT_OPERATION_APPLY);

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong()))
                .thenReturn(Collections.singletonList(task));

        doThrow(new RuntimeException("Test Exception")).when(antShopBiz).handleSubmitOperationApply(task);

        handler.execute(param);

        verify(antShopBiz, times(1)).handleSubmitOperationApply(task);
        verify(antShopBiz, times(1)).updateAntShopTask(task);
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
