package com.wosai.upay.job.util.luzhou;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.wosai.upay.job.BaseTest;

public class SMSaltSignerTest extends BaseTest {

    @Autowired
    private SMSaltSigner signer;

    @Value("${bcs_sm2_public_key}")
    private String sm2PublicKey;

    @Test
    public void signerTest() {
        String signature =
            "3045022032B0D2D57C4AC4B30C34117F569CA16522DB61F44985AC19E71369A012634045022100E2D56AD5D986AB7E4DEB2BFBF87CC70DAD38554D8A80B780A5445B0FE453AA22";
        String signBody = "{\n" + "    \"x-aob-appID\": \"612d11fc-17fc-4c1b-a58f-694ef4aac7a9\",\n"
            + "    \"x-aob-bankID\": \"BCS\",\n"
            + "    \"bizContent\": \"Yts00RusVbkly5KwRjFxBbeNhYl11N7QC1h/cPFA/T9qPXUvx1D4FTzK5i/UwLG5eiUV9ptuHmXcw4S70j24sXzwGMVJRbyjsyue/E30fw0=\"\n"
            + "}";
        signer.verifySignSm2(sm2PublicKey, signBody, signature);
    }
}