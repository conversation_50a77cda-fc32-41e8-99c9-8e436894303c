package com.wosai.upay.job.service;


import com.wosai.upay.job.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.TimeUnit;


public class RedisServiceTest extends BaseTest {

    @Autowired
    RedisService redisService;

    String key = "test";
    String value = "test";
    long time = 60;

    @Test
    public void setNx() {
        redisService.setNx(key, value, time, TimeUnit.SECONDS);
    }

    @Test
    public void getKeyWithoutPrefix() {
        redisService.getKeyWithoutPrefix(key);
    }


    @Test
    public void del() {
        redisService.del(key);
    }

    @Test
    public void cacheValue() {
        redisService.cacheValue(key, value, time);
    }

    @Test
    public void containsValueKey() {
        redisService.containsValueKey(key);
    }

    @Test
    public void containsSetKey() {
        redisService.containsSet<PERSON>ey(key);
    }

    @Test
    public void containsListKey() {
        redisService.containsList<PERSON>ey(key);
    }

    @Test
    public void containsKey() {
        redisService.containsList<PERSON>ey(key);
    }

    @Test
    public void getValue() {
        redisService.getValue(key);
    }

    @Test
    public void removeValue() {
        redisService.removeValue(key);
    }

    @Test
    public void removeSet() {
        redisService.removeSet(key);
    }

    @Test
    public void removeList() {
        redisService.removeList(key);
    }

    @Test
    public void cacheSet() {
        redisService.cacheSet(key, value, time);
    }

    @Test
    public void testCacheSet() {
        redisService.cacheSet(key, value);
    }

    @Test
    public void testCacheSet1() {
        redisService.cacheSet(key, Collections.EMPTY_SET);
    }

    @Test
    public void testCacheSet2() {
        redisService.cacheSet(key, Collections.EMPTY_SET, time);
    }

    @Test
    public void getSet() {
        redisService.getSet(key);
    }

    @Test
    public void cacheList() {
        redisService.cacheList(key, value, time);

    }

    @Test
    public void testCacheList() {
        redisService.cacheList(key, Arrays.asList(value), time);
    }

    @Test
    public void testCacheList1() {
        redisService.cacheList(key, Arrays.asList(value));
    }

    @Test
    public void testCacheList2() {
        redisService.cacheList(key, value);
    }

    @Test
    public void getList() {
        redisService.getList(key, 0, 10);
    }

    @Test
    public void getListSize() {
        redisService.getListSize(key);
    }

    @Test
    public void removeOneOfList() {
        redisService.removeOneOfList(key);
    }

    @Test
    public void incr() {
        redisService.incr(key, time);
    }
}
