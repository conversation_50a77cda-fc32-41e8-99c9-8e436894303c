package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.service.ContractApplicationService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/4/25 5:29 下午
 **/
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class LklShopTermBizTest {

    @Autowired
    LklV3ShopTermBiz biz;
//
//    @Test
//    public void addMer() {
//        biz.addMer("asn", "asn", "acontractId");
//    }
//
//    @Test
//    public void addShop() {
//        Boolean aBoolean = biz.addShop("asn", "bsn", "con123");
//        System.out.println(aBoolean);
//    }
//
//    @Test
//    public void addTerm() {
//        biz.addTerm("bsn", "ssn", "dd123");
//    }
//
//    @Test
//    public void verifyShop() {
//        LklV3Term term = new LklV3Term().setContractId("con").setTermNo("termno").setTermId("termid").setBusiTypeName("typeName").setProductCode("code");
//        biz.verifyShop("bsn", "shopId", term);
//    }
//
//    @Test
//    public void verifyTerm() {
//        LklV3Term lklV3Term = new LklV3Term().setContractId("dd").setBusiTypeName("aaaa").setProductCode("code").setTermNo("no").setTermId("id");
//        biz.verifyTerm("shopId2",lklV3Term,"dd123");
//    }

    @Test
    public void unbind(){
        Boolean aBoolean = biz.unbindTerm("bsn", "tsn");
        System.out.println(aBoolean);
    }

    @Test
    public void getTermNo(){
        String termNo = biz.getTermNo("bsn", "dddsn");
        System.out.println(termNo);
    }

    @Autowired
    ContractSubTaskMapper mapper;

    @Autowired
    ParamContextBiz paramContextBiz;

    @Test
    public void getContext(){
        Map<String, Object> paramContextByMerchantSn =
                paramContextBiz.getParamContextByMerchantSn("21690003160997", new ContractEvent().setEvent_type(ContractEvent.OPT_TYPE_NET_IN));
        System.out.println(JSON.toJSONString(paramContextByMerchantSn));
    }

    @Autowired
    StoreService storeService;

    @Test
    public void store(){
        StoreInfo store = storeService.getStoreBySn("st-180880551001200200174145", "PQPBJGNJC26N");
        System.out.println(store);
    }

    @Autowired
    ContractApplicationService contractApplicationService;

    @Test
    public void reep101v3(){
        contractApplicationService.reContractForEp101V3("21690003040345");
    }


}