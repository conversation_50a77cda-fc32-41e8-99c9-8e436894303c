package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.service.BankDirectService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.lang.reflect.Method;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BankDirectTimeoutJobHandlerTest {

    @InjectMocks
    private BankDirectTimeoutJobHandler bankDirectTimeoutJobHandler;

    @Mock
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private ProviderFactory providerFactory;

    @Mock
    private BankDirectService bankDirectService;

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @Mock
    private ChatBotUtil chatBotUtil;

    @Mock
    private MerchantService merchantService;

    @Mock
    private DirectJobParam param;

    @Before
    public void setUp() {
        when(param.getQueryTime()).thenReturn(1000L);
        when(param.getBatchSize()).thenReturn(10);
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "BankDirectTimeoutJobHandler";
        String actualLockKey = bankDirectTimeoutJobHandler.getLockKey();
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_EmptyApplyList_Returns() {
        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Collections.emptyList());

        bankDirectTimeoutJobHandler.execute(param);

        verify(bankDirectApplyMapper, times(1)).listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt());
    }

    @Test
    public void execute_CheckStatusAndDoTimeOut_Called() {
        BankDirectApply apply = new BankDirectApply();
        apply.setTask_id(111L);
        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Collections.singletonList(apply));
        when(contractTaskMapper.selectByPrimaryKey(apply.getTask_id())).thenReturn(new ContractTask());
        when(contractSubTaskMapper.getAcquireSubTask(apply.getTask_id())).thenReturn(Arrays.asList(new ContractSubTask().setUpdate_at(new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000))));
        BasicProvider basicProvider = mock(BasicProvider.class);
        when(providerFactory.getProviderByName(any())).thenReturn(basicProvider);
        when(basicProvider.checkBankContractToBeSigned(any())).thenReturn(true);

        bankDirectTimeoutJobHandler.execute(param);

        verify(bankDirectService, times(1)).selfAuditReject(any());
    }
}
