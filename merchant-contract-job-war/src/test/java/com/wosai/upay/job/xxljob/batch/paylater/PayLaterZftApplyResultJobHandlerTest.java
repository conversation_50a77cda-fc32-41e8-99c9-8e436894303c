package com.wosai.upay.job.xxljob.batch.paylater;

import com.wosai.upay.job.Constants.PayLaterConstant;
import com.wosai.upay.job.biz.PayLaterBiz;
import com.wosai.upay.job.mapper.PayLaterApplyMapper;
import com.wosai.upay.job.model.payLater.PayLaterApply;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PayLaterZftApplyResultJobHandlerTest {

    @InjectMocks
    private PayLaterZftApplyResultJobHandler payLaterZftApplyResultJobHandler;

    @Mock
    private PayLaterBiz payLaterBiz;

    @Mock
    private PayLaterApplyMapper payLaterApplyMapper;

    private PayLaterApply payLaterApply;

    @Mock
    private BatchJobParam param;

    @Before
    public void setUp() {
        payLaterApply = new PayLaterApply();
        payLaterApply.setId(1L);
        payLaterApply.setMerchant_sn("testMerchant");
        when(param.getBatchSize()).thenReturn(10);
        when(param.getQueryTime()).thenReturn(System.currentTimeMillis());
    }

    @Test
    public void getLockKey_WithNonNullId_ReturnsCorrectLockKey() {
        payLaterApply.setId(123L);
        String expectedLockKey = "PayLaterZftApplyResultJobHandler:123";
        String actualLockKey = payLaterZftApplyResultJobHandler.getLockKey(payLaterApply);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void getLockKey_WithNullId_ReturnsCorrectLockKey() {
        payLaterApply.setId(null);
        String expectedLockKey = "PayLaterZftApplyResultJobHandler:null";
        String actualLockKey = payLaterZftApplyResultJobHandler.getLockKey(payLaterApply);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsExpectedList() {
        // 准备
        List<PayLaterApply> expectedList = Collections.singletonList(new PayLaterApply());
        when(payLaterBiz.getPayLaterTasks(Arrays.asList(PayLaterConstant.ProcessStatus.PENDING, PayLaterConstant.ProcessStatus.ZFT_APPLYING), param.getBatchSize(), param.getQueryTime())).thenReturn(expectedList);

        // 执行
        List<PayLaterApply> result = payLaterZftApplyResultJobHandler.queryTaskItems(param);

        // 验证
        assertEquals(expectedList, result);
        Mockito.verify(payLaterBiz).getPayLaterTasks(anyList(), anyInt(), anyLong());
    }

    @Test
    public void doHandleSingleData_ProcessStatusNotPendingOrZftApplying_DoesNothing() {
        PayLaterApply apply = new PayLaterApply();
        apply.setId(1L);
        apply.setProcess_status(PayLaterConstant.ProcessStatus.ZFT_SUCCESS);

        when(payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId())).thenReturn(apply);

        payLaterZftApplyResultJobHandler.doHandleSingleData(payLaterApply);

        verify(payLaterBiz, never()).handleZftApplyResult(any(PayLaterApply.class));
    }

    @Test
    public void doHandleSingleData_ProcessStatusPendingOrZftApplying_CallsHandleZftApplyResult() {
        PayLaterApply apply = new PayLaterApply();
        apply.setProcess_status(PayLaterConstant.ProcessStatus.PENDING);

        when(payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId())).thenReturn(apply);

        payLaterZftApplyResultJobHandler.doHandleSingleData(payLaterApply);

        verify(payLaterBiz, times(1)).handleZftApplyResult(any(PayLaterApply.class));
    }

    @Test
    public void doHandleSingleData_ExceptionOccurs_CallsModifyPayLaterApply() {
        PayLaterApply apply = new PayLaterApply();
        apply.setProcess_status(PayLaterConstant.ProcessStatus.PENDING);

        when(payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId())).thenReturn(apply);
        doThrow(new RuntimeException("Test Exception")).when(payLaterBiz).handleZftApplyResult(any(PayLaterApply.class));

        payLaterZftApplyResultJobHandler.doHandleSingleData(payLaterApply);

        verify(payLaterBiz, times(1)).modifyPayLaterApply(
                any(PayLaterApply.class),
                eq(PayLaterConstant.Status.ALI_FAIL),
                eq(PayLaterConstant.SubStatus.FAIL),
                eq(PayLaterConstant.ProcessStatus.FAIL),
                eq(PayLaterConstant.Result.QUERY_ZFT_AUDIT_FAIL),
                eq(0)
        );
    }
}
