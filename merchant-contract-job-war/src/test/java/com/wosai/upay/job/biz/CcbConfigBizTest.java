package com.wosai.upay.job.biz;

import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.mapper.CcbConfigMapper;
import com.wosai.upay.job.model.ccbConfig.*;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class CcbConfigBizTest extends H2DbBaseTest {

    @Autowired
    private CcbConfigBiz ccbConfigBiz;

    @Test
    public void testAddCcbConfig() {
        CreateCcbConfigReq req = new CreateCcbConfigReq();
        req.setDistrictCode("310000");
        req.setPrivateMinPrice("0.1");
        req.setPublicMinPrice("0.1");
        req.setAccount("account");
        req.setOperator("SYSTEM");
        ccbConfigBiz.addCcbConfig(req, "上海市", "市辖区");

        QueryCcbConfigReq queryCcbConfigReq = new QueryCcbConfigReq();
        List<CcbConfig> ccbConfig = ccbConfigBiz.getCcbConfig(queryCcbConfigReq);
        assertEquals(1, ccbConfig.size());
        assertEquals(req.getDistrictCode(), ccbConfig.get(0).getDistrict_code());

        QueryCcbChangeHistoryReq queryCcbChangeHistoryReq = new QueryCcbChangeHistoryReq();
        queryCcbChangeHistoryReq.setCcbConfigId(ccbConfig.get(0).getId());
        List<CcbConfigChangeHistory> histories = ccbConfigBiz.getCcbConfigChangeHistoryByCcbConfigId(queryCcbChangeHistoryReq);
        assertEquals(1, histories.size());
        assertEquals(req.getOperator(), histories.get(0).getOperator());
        assertEquals(req.getAccount(), histories.get(0).getAccount());
    }

    @Test
    public void testGetCcbConfig() {
        CreateCcbConfigReq req = new CreateCcbConfigReq();
        req.setDistrictCode("310000");
        req.setPrivateMinPrice("0.1");
        req.setPublicMinPrice("0.1");
        req.setAccount("account");
        req.setOperator("SYSTEM");
        ccbConfigBiz.addCcbConfig(req, "上海市", "市辖区");

        QueryCcbConfigReq queryCcbConfigReq = new QueryCcbConfigReq();
        queryCcbConfigReq.setProvince("上海市");
        queryCcbConfigReq.setCity("市辖区");
        List<CcbConfig> ccbConfig = ccbConfigBiz.getCcbConfig(queryCcbConfigReq);
        assertEquals(1, ccbConfig.size());
        assertEquals("上海市", ccbConfig.get(0).getProvince());
        assertEquals(req.getPrivateMinPrice(), ccbConfig.get(0).getPrivate_min_price());
    }

    @Test
    public void testUpdateCcbConfig() {
        CreateCcbConfigReq req = new CreateCcbConfigReq();
        req.setDistrictCode("310000");
        req.setPrivateMinPrice("0.1");
        req.setPublicMinPrice("0.1");
        req.setAccount("account");
        req.setOperator("SYSTEM");
        ccbConfigBiz.addCcbConfig(req, "上海市", "市辖区");

        CcbConfig oldCcbConfig = ccbConfigBiz.getCcbConfigByDistrictCode(req.getDistrictCode());
        assertNotNull(oldCcbConfig);

        UpdateCcbConfigReq update = new UpdateCcbConfigReq();
        update.setId(oldCcbConfig.getId());
        update.setPrivateMinPrice("0.2");
        update.setPublicMinPrice("0.2");
        update.setAccount("account2");
        update.setOperator("SYSTEM");
        ccbConfigBiz.updateCcbConfig(oldCcbConfig, update);

        CcbConfig newCcbConfig = ccbConfigBiz.getCcbConfigById(update.getId());
        assertEquals(update.getPublicMinPrice(), newCcbConfig.getPublic_min_price());
        assertEquals(update.getPrivateMinPrice(), newCcbConfig.getPrivate_min_price());

        QueryCcbChangeHistoryReq queryCcbChangeHistoryReq = new QueryCcbChangeHistoryReq();
        queryCcbChangeHistoryReq.setCcbConfigId(newCcbConfig.getId());
        List<CcbConfigChangeHistory> histories = ccbConfigBiz.getCcbConfigChangeHistoryByCcbConfigId(queryCcbChangeHistoryReq);
        assertEquals(2, histories.size());
        assertEquals(req.getOperator(), histories.get(0).getOperator());
        assertEquals(req.getAccount(), histories.get(0).getAccount());
        assertEquals(CcbConfigChangeHistory.OP_TYPE_CREATE, histories.get(0).getOp_type());
        assertEquals(update.getOperator(), histories.get(1).getOperator());
        assertEquals(update.getAccount(), histories.get(1).getAccount());
        assertEquals(CcbConfigChangeHistory.OP_TYPE_UPDATE, histories.get(1).getOp_type());
    }

    @Test
    public void testDeleteCcbConfig() {
        CreateCcbConfigReq req1 = new CreateCcbConfigReq();
        req1.setDistrictCode("111000");
        req1.setPrivateMinPrice("0.1");
        req1.setPublicMinPrice("0.1");
        req1.setAccount("account1");
        req1.setOperator("op1");
        CreateCcbConfigReq req2 = new CreateCcbConfigReq();
        req2.setDistrictCode("222000");
        req2.setPrivateMinPrice("0.2");
        req2.setPublicMinPrice("0.2");
        req2.setAccount("account2");
        req2.setOperator("op2");
        ccbConfigBiz.addCcbConfig(req1, "上海市", "市辖区");
        ccbConfigBiz.addCcbConfig(req2, "北京市", "市辖区");

        QueryCcbConfigReq queryCcbConfigReq = new QueryCcbConfigReq();
        queryCcbConfigReq.setPageSize(1);
        List<CcbConfig> ccbConfig = ccbConfigBiz.getCcbConfig(queryCcbConfigReq);
        assertEquals(1, ccbConfig.size());
        assertEquals(req1.getDistrictCode(), ccbConfig.get(0).getDistrict_code());

        // 删除1 就剩下2了
        DeleteCcbConfigReq deleteCcbConfigReq = new DeleteCcbConfigReq();
        deleteCcbConfigReq.setId(ccbConfig.get(0).getId());
        deleteCcbConfigReq.setOperator("op1");
        ccbConfigBiz.deleteCcbConfig(ccbConfig.get(0), deleteCcbConfigReq);
        ccbConfig = ccbConfigBiz.getCcbConfig(queryCcbConfigReq);
        assertEquals(1, ccbConfig.size());
        assertEquals(req2.getDistrictCode(), ccbConfig.get(0).getDistrict_code());
    }
}