package com.wosai.upay.job.refactor.unit.task;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.service.impl.InterScheduleTaskServiceImpl;
import com.wosai.upay.job.refactor.task.HaikeUnionPayContractResultTask;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.service.HaikeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.Timestamp;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * HaikeUnionPayContractResultTask 单元测试
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@ExtendWith(MockitoExtension.class)
@Slf4j
public class HaikeUnionPayContractResultTaskTest {

    @InjectMocks
    private HaikeUnionPayContractResultTask haikeUnionPayContractResultTask;

    @Mock
    private InterScheduleTaskServiceImpl interactionTaskService;

    @Mock
    private HaikeService haikeService;

    @Mock
    private ContractParamsBiz contractParamsBiz;

    @Mock
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Mock
    private ContractStatusService contractStatusService;

    @Mock
    private MerchantProviderParamsService merchantProviderParamsService;

    @Mock
    private TradeConfigService tradeConfigService;

    @Mock
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    private static final String TEST_MERCHANT_SN = "21690004127518";
    private static final String TEST_ACQUIRER_MERCHANT_ID = "test_acquirer_merchant_id";
    private static final String TEST_UNION_MERCHANT_ID = "test_union_merchant_id";
    private static final String TEST_PAY_MERCHANT_ID = "test_pay_merchant_id";
    private static final String TEST_MERCHANT_ID = "test_merchant_id";

    private MerchantProviderParamsDO acquirerParams;
    private MerchantProviderParamsDO unionParams;
    private ContractStatus contractStatus;
    private HaikeParam haikeParam;
    private InternalScheduleMainTaskDO mainTaskDO;
    private InternalScheduleSubTaskDO subTaskDO;

    @BeforeEach
    public void setUp() {
        // 初始化测试数据
        setupTestData();
        
        // 设置默认的 Mock 行为
        setupDefaultMockBehavior();
    }

    private void setupTestData() {
        // 收单机构参数
        acquirerParams = new MerchantProviderParamsDO();
        acquirerParams.setId("1");
        acquirerParams.setMerchantSn(TEST_MERCHANT_SN);
        acquirerParams.setProvider(ProviderEnum.PROVIDER_HAIKE.getValue());
        acquirerParams.setPayway(PaywayEnum.ACQUIRER.getValue());
        acquirerParams.setPayMerchantId(TEST_ACQUIRER_MERCHANT_ID);

        // 云闪付参数
        unionParams = new MerchantProviderParamsDO();
        unionParams.setId("2");
        unionParams.setMerchantSn(TEST_MERCHANT_SN);
        unionParams.setProvider(ProviderEnum.PROVIDER_HAIKE.getValue());
        unionParams.setPayway(PaywayEnum.UNIONPAY.getValue());
        unionParams.setPayMerchantId(TEST_UNION_MERCHANT_ID);
        unionParams.setStatus(UseStatusEnum.IN_USE.getValue());
        
        Map<String, Object> extraMap = Maps.newHashMap();
        Map<String, Object> tradeParamsMap = Maps.newHashMap();
        tradeParamsMap.put("bankMerchNo", "old_bank_merch_no");
        extraMap.put("tradeParams", tradeParamsMap);
        unionParams.setExtra(CommonUtil.map2String(extraMap));

        // 合约状态
        contractStatus = new ContractStatus();
        contractStatus.setMerchant_sn(TEST_MERCHANT_SN);
        contractStatus.setAcquirer(AcquirerTypeEnum.HAI_KE.getValue());

        // 海科参数
        haikeParam = new HaikeParam();
        haikeParam.setAgentNo("test_api_id");
        haikeParam.setRsaKey("test_api_key");

        // 主任务
        mainTaskDO = new InternalScheduleMainTaskDO();
        mainTaskDO.setId(1L);
        mainTaskDO.setMerchantSn(TEST_MERCHANT_SN);

        // 子任务
        subTaskDO = new InternalScheduleSubTaskDO();
        subTaskDO.setId(1L);
        subTaskDO.setMerchantSn(TEST_MERCHANT_SN);
        subTaskDO.setStatus(InternalScheduleSubTaskStatusEnum.WAIT_PROCESS.getValue());
    }

    private void setupDefaultMockBehavior() {
        // Mock DAO 返回值
        when(merchantProviderParamsDAO.getBySnAndProviderAndPayWay(
                eq(TEST_MERCHANT_SN), eq(ProviderEnum.PROVIDER_HAIKE.getValue()), eq(PaywayEnum.ACQUIRER.getValue())))
                .thenReturn(Optional.of(acquirerParams));

        when(merchantProviderParamsDAO.getBySnAndProviderAndPayWay(
                eq(TEST_MERCHANT_SN), eq(ProviderEnum.PROVIDER_HAIKE.getValue()), eq(PaywayEnum.UNIONPAY.getValue())))
                .thenReturn(Optional.of(unionParams));

        // Mock 合约状态服务
        when(contractStatusService.selectByMerchantSn(TEST_MERCHANT_SN))
                .thenReturn(contractStatus);

        // Mock 合约参数构建
        when(contractParamsBiz.buildContractParams(eq(ChannelEnum.HAIKE.getValue()), eq(HaikeParam.class)))
                .thenReturn(haikeParam);

        // Mock 商户基本信息
        when(merchantBasicInfoBiz.getMerchantInfoBySn(TEST_MERCHANT_SN))
                .thenReturn(createMockMerchantInfo());
    }

    private MerchantInfo createMockMerchantInfo() {
        // 创建一个模拟的商户信息对象
        MerchantInfo merchantInfo = new MerchantInfo();
        merchantInfo.setId(TEST_MERCHANT_ID);
        return merchantInfo;
    }

    @Test
    public void testGetTaskType() {
        // When
        InternalScheduleTaskTypeEnum taskType = haikeUnionPayContractResultTask.getTaskType();

        // Then
        assertEquals(InternalScheduleTaskTypeEnum.HAIKE_UNION_PAY_CONTRACT_RESULT, taskType);
    }

    @Test
    public void testQueryContractResult_Success() {
        // Given
        ContractResponse successResponse = createSuccessResponse();
        when(haikeService.queryMerchantContractResult(eq(TEST_ACQUIRER_MERCHANT_ID), any(HaikeParam.class)))
                .thenReturn(successResponse);

        // When
        InternalScheduleSubTaskProcessResultBO result = invokeQueryContractResult();

        // Then
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS, result.getStatus());
        assertNotNull(result.getRequestMsg());
        assertNotNull(result.getResponseMsg());
        assertTrue(result.getResult().contains("银联商户号一致") || result.getResult().contains("更新云闪付参数"));
    }

    @Test
    public void testQueryContractResult_WaitingForResult() {
        // Given
        ContractResponse waitingResponse = createWaitingResponse();
        when(haikeService.queryMerchantContractResult(eq(TEST_ACQUIRER_MERCHANT_ID), any(HaikeParam.class)))
                .thenReturn(waitingResponse);

        // When
        InternalScheduleSubTaskProcessResultBO result = invokeQueryContractResult();

        // Then
        assertEquals(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT, result.getStatus());
        assertEquals("等待云闪付报备结果", result.getResult());
        assertNotNull(result.getRequestMsg());
        assertNotNull(result.getResponseMsg());
    }

    @Test
    public void testQueryContractResult_Failed() {
        // Given
        ContractResponse failedResponse = createFailedResponse();
        when(haikeService.queryMerchantContractResult(eq(TEST_ACQUIRER_MERCHANT_ID), any(HaikeParam.class)))
                .thenReturn(failedResponse);

        // When
        InternalScheduleSubTaskProcessResultBO result = invokeQueryContractResult();

        // Then
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS, result.getStatus());
        assertTrue(result.getResult().contains("云闪付报备失败"));
        assertNotNull(result.getRequestMsg());
        assertNotNull(result.getResponseMsg());
        
        // 验证删除操作
        verify(merchantProviderParamsDAO, times(1)).logicDeleteByIds(anyList());
    }

    @Test
    public void testQueryContractResult_AcquirerParamsNotFound() {
        // Given
        when(merchantProviderParamsDAO.getBySnAndProviderAndPayWay(
                eq(TEST_MERCHANT_SN), eq(ProviderEnum.PROVIDER_HAIKE.getValue()), eq(PaywayEnum.ACQUIRER.getValue())))
                .thenReturn(Optional.empty());

        // When
        InternalScheduleSubTaskProcessResultBO result = invokeQueryContractResult();

        // Then
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, result.getStatus());
        assertTrue(result.getResult().contains("收单机构商户号不存在"));
    }

    @Test
    public void testQueryContractResult_UnionParamsNotFound() {
        // Given
        when(merchantProviderParamsDAO.getBySnAndProviderAndPayWay(
                eq(TEST_MERCHANT_SN), eq(ProviderEnum.PROVIDER_HAIKE.getValue()), eq(PaywayEnum.UNIONPAY.getValue())))
                .thenReturn(Optional.empty());

        // When
        InternalScheduleSubTaskProcessResultBO result = invokeQueryContractResult();

        // Then
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, result.getStatus());
        assertTrue(result.getResult().contains("缺少海科云闪付报备参数记录"));
    }

    @Test
    public void testQueryContractResult_Exception() {
        // Given
        when(haikeService.queryMerchantContractResult(eq(TEST_ACQUIRER_MERCHANT_ID), any(HaikeParam.class)))
                .thenThrow(new RuntimeException("测试异常"));

        // When
        InternalScheduleSubTaskProcessResultBO result = invokeQueryContractResult();

        // Then
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, result.getStatus());
        assertTrue(result.getResult().contains("处理失败"));
    }

    @Test
    public void testUpdateWhenContractSuccess_SameMerchantId() {
        // Given
        ContractResponse successResponse = createSuccessResponse();
        unionParams.setPayMerchantId(TEST_PAY_MERCHANT_ID);
        
        when(haikeService.queryMerchantContractResult(eq(TEST_ACQUIRER_MERCHANT_ID), any(HaikeParam.class)))
                .thenReturn(successResponse);

        // When
        InternalScheduleSubTaskProcessResultBO result = invokeQueryContractResult();

        // Then
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS, result.getStatus());
        assertEquals("银联商户号一致，无需更新", result.getResult());
    }

    @Test
    public void testUpdateWhenContractSuccess_DifferentMerchantId() {
        // Given
        ContractResponse successResponse = createSuccessResponse();
        unionParams.setPayMerchantId("different_merchant_id");
        
        when(haikeService.queryMerchantContractResult(eq(TEST_ACQUIRER_MERCHANT_ID), any(HaikeParam.class)))
                .thenReturn(successResponse);

        // When
        InternalScheduleSubTaskProcessResultBO result = invokeQueryContractResult();

        // Then
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS, result.getStatus());
        assertTrue(result.getResult().contains("云闪付报备成功"));
        
        // 验证更新操作
        verify(merchantProviderParamsDAO, times(2)).updateByPrimaryKey(any(MerchantProviderParamsDO.class));
    }

    @Test
    public void testInsertTask_WithScheduledTime() {
        // Given
        Timestamp scheduledTime = new Timestamp(System.currentTimeMillis() + 300000);
        
        // When
        haikeUnionPayContractResultTask.insertTask(TEST_MERCHANT_SN, scheduledTime);

        // Then
        verify(interactionTaskService, times(1)).batchInsertTasks(anyMap());
    }

    @Test
    public void testInsertTask_WithoutScheduledTime() {
        // When
        haikeUnionPayContractResultTask.insertTask(TEST_MERCHANT_SN);

        // Then
        verify(interactionTaskService, times(1)).batchInsertTasks(anyMap());
    }

    @Test
    public void testInsertTaskWithDelay() {
        // Given
        int delayMinutes = 5;

        // When
        haikeUnionPayContractResultTask.insertTaskWithDelay(TEST_MERCHANT_SN, delayMinutes);

        // Then
        verify(interactionTaskService, times(1)).batchInsertTasks(anyMap());
    }

    @Test
    public void testInsertTaskWithDelay_InvalidDelay() {
        // Given
        int invalidDelayMinutes = 0;

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            haikeUnionPayContractResultTask.insertTaskWithDelay(TEST_MERCHANT_SN, invalidDelayMinutes);
        });
    }

    @Test
    public void testBatchInsertTasks() {
        // Given
        List<String> merchantSns = Lists.newArrayList(TEST_MERCHANT_SN, "another_merchant_sn");
        Timestamp scheduledTime = new Timestamp(System.currentTimeMillis());

        // When
        haikeUnionPayContractResultTask.batchInsertTasks(merchantSns, scheduledTime);

        // Then
        verify(interactionTaskService, times(1)).batchInsertTasks(anyMap());
    }

    @Test
    public void testBatchInsertTasks_EmptyList() {
        // Given
        List<String> emptyList = Lists.newArrayList();

        // When
        haikeUnionPayContractResultTask.batchInsertTasks(emptyList, null);

        // Then
        verify(interactionTaskService, never()).batchInsertTasks(anyMap());
    }

    // Helper methods
    private InternalScheduleSubTaskProcessResultBO invokeQueryContractResult() {
        return ReflectionTestUtils.invokeMethod(haikeUnionPayContractResultTask, 
                "queryContractResult", mainTaskDO, subTaskDO);
    }

    private ContractResponse createSuccessResponse() {
        ContractResponse response = new ContractResponse();
        response.setCode(200);
        response.setMessage("success");
        
        Map<String, Object> responseParam = Maps.newHashMap();
        Map<String, Object> bankBizInfo = Maps.newHashMap();
        bankBizInfo.put("bank_merch_no", TEST_PAY_MERCHANT_ID);
        responseParam.put("bank_biz_info", bankBizInfo);
        response.setResponseParam(responseParam);
        
        return response;
    }

    private ContractResponse createWaitingResponse() {
        ContractResponse response = new ContractResponse();
        response.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION);
        response.setMessage("报备中，重试查询");
        return response;
    }

    private ContractResponse createFailedResponse() {
        ContractResponse response = new ContractResponse();
        response.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION);
        response.setMessage("营业执照编号错误");
        return response;
    }
} 