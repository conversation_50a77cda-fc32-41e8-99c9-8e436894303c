package com.wosai.upay.job.service;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryReq;

public class MerchantProviderParamsServiceImplNewTest extends BaseTest {

    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;

    @Test
    public void getAcquirerMerchantInfoTest() {
        merchantProviderParamsService.getAcquirerMerchantInfo("07eded85-d8da-420a-aa5c-9ae90e969c62");
    }

    @Test
    public void queryUnionPayOpenStatusTest() {
        String content = "{\n" + "  \"acquirer\" : \"bcs\",\n"
            + "  \"merchantId\" : \"07eded85-d8da-420a-aa5c-9ae90e969c62\",\n" + "  \"platform\" : \"SPA\"\n" + "}";
        UnionPayOpenStatusQueryReq unionPayOpenStatusQueryReq =
            JSONObject.parseObject(content, UnionPayOpenStatusQueryReq.class);
        merchantProviderParamsService.queryUnionPayOpenStatus(unionPayOpenStatusQueryReq);
    }
}