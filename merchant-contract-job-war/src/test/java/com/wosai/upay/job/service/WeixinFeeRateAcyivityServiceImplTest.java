package com.wosai.upay.job.service;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.ChannelActivityMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ChannelActivity;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import lombok.SneakyThrows;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

public class WeixinFeeRateAcyivityServiceImplTest extends BaseTest {

    @Autowired
    private WeixinFeeRateActivityServiceImpl weixinFeeRateActivityServiceImpl;

    @Autowired
    private ChannelActivityMapper channelActivityMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @SneakyThrows
    @Test
    public void queryStatusTest() {

        ChannelActivity r = channelActivityMapper.selectByPrimaryKey(1189L);
        MerchantProviderParams params = merchantProviderParamsMapper.selectByPrimaryKey("0000043a-49c6-403f-b428-fbf9b4deb38a");
        MerchantProviderParamsDto dto = new MerchantProviderParamsDto();
        BeanUtils.copyProperties(params, dto);
        weixinFeeRateActivityServiceImpl.queryStatus(r, dto);
    }

}
