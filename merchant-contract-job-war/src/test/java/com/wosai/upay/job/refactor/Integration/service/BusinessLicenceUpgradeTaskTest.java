package com.wosai.upay.job.refactor.Integration.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerMerchantStatusEnum;
import com.shouqianba.cua.enums.core.DeleteStatusEnum;
import com.shouqianba.cua.utils.json.JSON;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.avro.MicroUpgradeSuccessDTO;
import com.wosai.upay.job.biz.LklV3ShopTermBiz;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.mapper.ProviderTerminalTaskMapper;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import com.wosai.upay.job.model.dto.request.BusinessLicenseUpdateDTO;
import com.wosai.upay.job.model.dto.request.UpdateCardInfoAfterMicroUpgradeDTO;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.biz.acquirer.fuyou.FuYouAcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.LklV3AcquirerFacade;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.biz.provider.McProviderBiz;
import com.wosai.upay.job.refactor.dao.InternalScheduleMainTaskDAO;
import com.wosai.upay.job.refactor.dao.InternalScheduleSubTaskDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.service.impl.task.BusinessLicenceTaskServiceImpl;
import com.wosai.upay.job.refactor.service.rpc.coreb.CoreBTradeConfigService;
import com.wosai.upay.job.refactor.service.rpc.risk.RiskMerchantBusinessLicenseAuditService;
import com.wosai.upay.job.refactor.service.rpc.risk.req.RiskEntryResult;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationTask;
import com.wosai.upay.job.refactor.task.license.micro.HaikeUpdateTradeParams;
import com.wosai.upay.job.refactor.task.license.micro.LklV3UpdateTradeParams;
import com.wosai.upay.job.refactor.utils.BeanCopyUtils;
import com.wosai.upay.job.refactor.utils.UrlUtils;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.service.ContractEventService;
import com.wosai.upay.job.util.ChatBotUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;

import javax.annotation.Resource;

import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationTask.MICRO_UPGRADE_SUCCESS_TOPIC;
import static org.assertj.core.api.Assertions.assertThat;

/**
 * 收单机构测试
 *
 * <AUTHOR>
 */
@Slf4j
public class BusinessLicenceUpgradeTaskTest extends BaseTest {

    @Resource
    private BusinessLicenceTaskServiceImpl businessLicenceTaskService;

    @Resource private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Test
    public void testLogicDelete() {
        merchantTradeParamsBiz.logicDeleteByIds(Lists.newArrayList("1"));
    }

    @Test
    public void testBatchInsert() {
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = merchantProviderParamsDAO.listByMerchantSn("21690003868178");
        List<MerchantProviderParamsDO> merchantProviderParamsDOS1 = BeanCopyUtils.copyList(merchantProviderParamsDOS, MerchantProviderParamsDO.class);
        for (MerchantProviderParamsDO merchantProviderParamsDO : merchantProviderParamsDOS1) {
            merchantProviderParamsDO.setId(UUID.randomUUID().toString());
            merchantProviderParamsDO.setDeleted(DeleteStatusEnum.DELETED.getValue());
        }
        merchantTradeParamsBiz.batchInsertParams(merchantProviderParamsDOS1);
    }

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    @Test
    public void testListAllTerminals() {
        List<Map> terminalIds = merchantBasicInfoBiz.listAllSqbTerminals("21690003339950");

    }



    @Test
    public void testApplyTask() {
        String reqString = "{\"validity\":\"20130312-20330312\",\"id_validity\":\"****\",\"name\":\"什邡市优禾尚品食品经营部（个体工商户）\",\"number\":\"****\",\"uc_user_id\":\"5c105db8-fd97-4897-8185-0fc0f27ab29b\",\"id_validity_end\":\"20330312\",\"merchant_id\":\"aa37195f-306d-45f2-9ecb-0c1f2ec4d229\",\"id_validity_start\":\"20130312\",\"id\":\"dd0ad209-c368-4d85-beab-653c5f009f26\",\"legal_person_id_number\":\"510623198108112623\",\"validity_end\":\"2033-03-12\",\"letter_of_authorization\":\"https://private-images.shouqianba.com/ee/07079f3966c3d9fcb130a00a2c85806ae02251.jpg?Expires=**********&OSSAccessKeyId=STS.NUzgYFMY1tEZF5ztkUyjdoP3D&Signature=wDDS39ryUSxGYEkkhdygrWGCNjk%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5bPLOPyoIYQw4exRBPLkGsAdeVIgJWYpjz2IHBPeHBuCOsbvvQzmGtT5%2FcclolwVpVIX0rJccZrq49O%2B0a6etOe4ZHoveFU0c3gRjnNUEYiXDQTl7%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqY00cFYnN%2FNnS%2F4Y8KOnxKwmoJbIjo6ywAtEeP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6sKr4QjbD5ForPMkvPiAnq0YzjuduTGsLE7qANOuy%2Bot6qFOhDajhtHpDHPELtvUr2B2c%2FFTYSt0%2FrcogwSJGhqAAW3HDz56UP9ctcSF%2FdtA6X6IMsrgXQroLsGqZQOcmRzUY82E1sMtMLT9J%2B6f53endgykNBb2O8jOZHaDuwv5%2BWvzQpok%2FAjN%2Bq6hnQuv3qLEB1gGS11qJLjOLFhBpElVVWCWrdAEOEYU%2FZqIzvE6WEXWal7I%2Bgzian5lZNSzS8dLIAA%3D\",\"legal_person_id_card_back_photo\":\"https://private-images.shouqianba.com/64/d380d804374e129621df0159d0ba413bcb79c2.jpg?Expires=1725587323&OSSAccessKeyId=STS.NTUQgt2fsQ11x8RLg3jZro6aH&Signature=O8TrO78hddtzIuBoP3EelEmjiWA%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5fgGt3A37lS5vPaeh7jqGdmZtVegPPKqjz2IHBPeHBuCOsbvvQzmGtT5%2FcclolwVpVIX0rJccZrq49O%2B0a6etOe4ZHoveZa2MTgRTjKWkZMGHMdl7%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqY00cFYnN%2FNnS%2F4Y8KOnxKwmoJbIjo6ywAtEeP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6qIMoqzwKvQLnt5h223NW7DnWz5qwiq%2BRi2JZeZrXo%2B4H1S74SvGs3T0ybahIdG7lRJ8EPtayyutLxhTgwSJGhqAAWOMSkWCOnNCbECTN8VPdLfvPYPmJh9gQtrdYXB4VV92SqhtrxrO%2B1SN%2BNrYkD5YqRjrRzpNHeisfQZhieuzM871Y3hpRMP8wNrE%2Bacz11ekZfoQqF3rhqnutwDsyeVSlh0XrtrrLAY2NIuuKFmir3qHOgs%2BtZvQMAmJzJtYXfGlIAA%3D\",\"legal_person_id_card_front_photo\":\"https://private-images.shouqianba.com/4b/52c367c4b71800e7785e16b848040bebfedb37.jpg?Expires=**********&OSSAccessKeyId=STS.NUNiB1tpgMRytp6xmuoh52UbU&Signature=LSxCjJqqDNw%2BEQeuuEI%2FWaNHSVc%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5b7IviFma9G%2BpCSdlaHnG0gY%2BcZ3ZDJtzz2IHBPeHBuCOsbvvQzmGtT5%2FcclolwVpVIX0rJccZrq49O%2B0a6etOe4ZHovede1szgQDfIWkYyL18dl7%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqY00cFYnN%2FNnS%2F4Y8KOnxKwmoJbIjo6ywAtEeP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6kLLM6WQKvQLnt5h223NW7DnWz5qwiq%2BRi2JZeZrXo%2B4H1S74SvGs3T0ybahIdG7lRJ8EPtayytuUTnygwSJGhqAAa%2Bi3DUNo3Cw%2BnSVrcgDbVf5MCnm28SOf0fgJjZWeiGpSOTSERqnqToYf82%2BMNw4YgFpVM1bOhUuLeUQdpx7Da9phFnvN8wzGNWorG17fyKqirdEpG6BAHNAMtzZ%2BhgxysaIllTpzs0r9aOHdKYsmssY4sc1CmpFUfrtRi6oKSmLIAA%3D\",\"license_address\":\"四川省德阳市什邡市方亭街道文华南街122号\",\"role_id\":\"35eb325c-4fad-4d44-8939-d1f0d46bf3c9\",\"legal_person_name\":\"黄常苹\",\"store_id\":\"731230cd-1c3d-498e-ad90-f32afeb31221\",\"account_id\":\"5c105db8-fd97-4897-8185-0fc0f27ab29b\",\"cash_store_id\":\"731230cd-1c3d-498e-ad90-f32afeb31221\",\"license_type\":\"1\",\"holder_number\":\"510622197811065127\",\"merchant_user_id\":\"dd0ad209-c368-4d85-beab-653c5f009f26\",\"holder\":\"林菊高\",\"account_type\":1,\"id_type\":1,\"photo\":\"https://private-images.shouqianba.com/5d/a9c5ee41613ba9d9317c08e030c3e34446fae2.jpg?Expires=**********&OSSAccessKeyId=STS.NTcfTLku7yWFBv75vEz89fBq2&Signature=yS04nPcXQygqPxLVwnmLvOQANIk%3D&security-token=CAIS3gJ1q6Ft5B2yfSjIr5fWLe74hqoWzpWtQFCG0XYQdrcViYfa0Dz2IHBPeHBuCOsbvvQzmGtT5%2FcclolwVpVIX0rJccZrq49O%2B0a6etOe4ZHovede18bgSDjPU0YdAl8dl7%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqY00cFYnN%2FNnS%2F4Y8KOnxKwmoJbIjo6ywAtEeP9cTiDDAZqnxMbUwxYNw2q4zNwRUla6MbDnXvGd22tM6vLIZQz7D5ForPMkvPiAnq0YzjuduTGsLE7qANOuy%2Bot6qFOhDajhtHpDHPELtvUr2B2c%2FFTYSsXx4LxgwSJGhqAAUIbJVate1T2c4GVYln6ABHlJjJHlXJmUN9WjJ3oPX96XJxPDZpcHY%2B5z6DmKigN92blShW0WbPerieIVIjhDiMPAKZb08OdNGhjBBjz3PKSlQ3NaJ0CKZOlwleTSnCOCHyqIUrwY6i15%2B9EfQZ66MXMmWX7r3OkzmRIZ%2F6fLMJMIAA%3D\",\"msp_account_id\":\"5c105db8-fd97-4897-8185-0fc0f27ab29b\",\"license_name\":\"个体工商户营业执照\",\"role\":\"super_admin\",\"legal_person_id_holder\":\"黄常苹\",\"legal_person_id_card_address\":\"四川省德阳市旌阳区孝感镇杨柳村2组71号\",\"token\":\"********************************************************************************.ozSvET6n3g\",\"legal_person_id_card_issuing_authority\":\"德阳市公安局旌阳分局\",\"sub_appid\":\"sqb\",\"validity_start\":\"2013-03-12\",\"proof_assistant_photos\":[],\"apply_remark\":\"\"}";
        BusinessLicenseUpdateDTO businessLicenseUpdateDTO = JSON.parseObject(reqString, BusinessLicenseUpdateDTO.class);
        businessLicenseUpdateDTO.setMerchantId("bcbee551-a918-41a8-aa95-0ac0b5ac819a");
        businessLicenceTaskService.insertLicenceUpdateTask(businessLicenseUpdateDTO);

    }

    @Test
    public void testGetAuditResult() {
        businessLicenceCertificationTask.riskManualLicenceAuditPass("**************");
    }

    @Resource
    private AcquirerService acquirerService;

    @Test
    public void testGetMerchantStatus() {
        acquirerService.getAcquirerMerchantStatus("21690003236028", "haike");
    }

    @Resource
    BusinessLicenceCertificationTask businessLicenceCertificationTask;

    @Test
    public void testProcessTask() {
        ArrayList<Long> ids = Lists.newArrayList(1794L);
        businessLicenceCertificationTask.batchHandleTasksByMainTaskIds(ids);
    }

    @Resource
    private ProviderTerminalBiz providerTerminalBiz;

    @Autowired
    private ProviderTerminalTaskMapper taskMapper;

    @Test
    public void testProviderTerminalBindTask() {
        ProviderTerminalTask task = taskMapper.selectByPrimaryKey(119859869905L);
        providerTerminalBiz.boundTerminal(task);
    }


    @Test
    public void testIsUpgradeNeedContractToAcquirer() {
        businessLicenceTaskService.isUpgradeNeedContractToAcquirer("**************");
    }

    @Test
    public void testNeedToContract() {
        businessLicenceTaskService.isUpgradeNeedContractToAcquirer("**************");
    }

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void testStringRedis() {
        stringRedisTemplate.opsForValue().set("merchant:contract_job:test", "test", 30, TimeUnit.SECONDS);
        String test = stringRedisTemplate.opsForValue().get("merchant:contract_job:test");
        assertThat(test).isEqualTo("test");
        Boolean deleteSuccess = stringRedisTemplate.delete("merchant:contract_job:test");
        assertThat(deleteSuccess).isTrue();
    }

    @Resource
    private KafkaTemplate<String, Object> aliKafkaTemplate;

    @Test
    public void testKafkaTemplate() {
        for (int i = 0; i < 10; i++) {
            MicroUpgradeSuccessDTO microUpgradeSuccessDTO = new MicroUpgradeSuccessDTO();
            microUpgradeSuccessDTO.setMerchantSn("**************" + i);
            microUpgradeSuccessDTO.setOriginalAcquirer("haike");
            microUpgradeSuccessDTO.setOriginalAcquirerMerchantId("5555555555");
            microUpgradeSuccessDTO.setNewAcquirer("lklV3");
            microUpgradeSuccessDTO.setNewAcquirerMerchantId("999999999");
            microUpgradeSuccessDTO.setSuccessTimeMillis(System.currentTimeMillis());
            aliKafkaTemplate.send(MICRO_UPGRADE_SUCCESS_TOPIC, microUpgradeSuccessDTO);
        }
    }


    @Test
    public void testDeleteAllTradeExtConfig() {
        merchantTradeParamsBiz.deleteAllTradeExtConfig("21690003881838", 1032, "21690003881838");
    }

    @Test
    public void testDeleteAllAcquirerRelatedParams() {
        merchantTradeParamsBiz.deleteAllAcquirerRelatedParams("21690003877144", "fuyou");
    }

    @Resource private LklV3AcquirerFacade lklV3AcquirerFacade;

    @Test
    public void testMicroDoing() {
        lklV3AcquirerFacade.getAcquirerMerchantStatus("**************");
        assertThat(businessLicenceTaskService.isMerchantDoingMicroUpgrade("21690003741122")).isTrue();
        assertThat(businessLicenceTaskService.isMerchantDoingMicroUpgrade("21690003581722")).isFalse();
    }

    @Resource private FuYouAcquirerFacade fuyouAcquirerFacade;

    @Test
    public void testAcquirerMerchantStatus() {
        AcquirerMerchantStatusEnum acquirerMerchantStatus = fuyouAcquirerFacade.getAcquirerMerchantStatus("21690003931281");
        assertThat(acquirerMerchantStatus).isEqualTo(AcquirerMerchantStatusEnum.NORMAL);
    }

    @Resource
    private InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    @Test
    public void testAwakeMainTask() {
        businessLicenceCertificationTask.handleMainTaskMerchantIsMicro("**************", internalScheduleMainTaskDAO.getByPrimaryKey(1832).get());
    }

    @Test
    public void testUpdateLicense() {
        businessLicenceCertificationTask.processMicroUpgradeSuccess(internalScheduleMainTaskDAO.getByPrimaryKey(1826).get());
    }

    @Test
    public void testUrl() {
        String url = UrlUtils.baseUrl("https://private-images.shouqianba.com/sales-system-gateway/2024-09-24/69a24b7aee3b41c49a56ff2525166746.jpg?Expires=**********&OSSAccessKeyId=STS.NUB129di69hSTXvXYrN8c2PAR&Signature=lgVzd5yXykuwk6K1HwuwaZGdw5k%3D&x-oss-process=image%2Fwatermark%2Ctext_Q1JNLeW8oOmbgS0wMTc5LeS7hemZkOacrOasoeS4muWKoeS9v-eUqA%2Ccolor_505050%2Csize_30%2Cg_center%2Crotate_330%2Ct_30%2Cfill_1&security-token=CAIS4QJ1q6Ft5B2yfSjIr5b3eoiNibYXjqq4Vn7HvFknQrdP3ZXqsDz2IH5Me3NpBOoXv%2F8%2FlGpX7vgblolwVpVIX0rJccZrq49O%2B0a6etOe4ZPssuRY1cPgRTTPU0YvAkUmqb%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYM3c1UgO%2FJrSvUU%2FKKjzaMhoJbIjo6yzA1VeP9cTiDDAZyt29fUwxYNw2q4zNwRUlG6MbDnXvGd22tM6sIA7wbUMZFo%2FPMkvN7I5uVG7gsLfMTwI2bFBeHMm9odytFu9G7TtrnBXFPsaJ7SvEhVRNlSJi1HOXZP0sqwI8duGxqAAVqoflAjZRCGsK%2BcYLqKXUy0%2Fbi9B3geoSNg0LU%2B67sEfyLJzryDZ%2BE2qGJ926pd0crNzumJO5XWXFzRxeof18ksXitQpZHbsx%2FFQvxIO8N8GDbCrJSebH9Ym1mKCt6SZJf9aLxoXeVrPGOuzxsFO7gIEkmzMPBBrq%2FL2cvZGpLQIAA%3D");
        assertThat(url.length()).isLessThan(255);
    }

    @Resource private ChatBotUtil chatBotUtil;

    @Test
    public void testChatBot() {
        chatBotUtil.sendMessageToMicroUpgradeChatBot("测试小微升级");
    }

    @Test
    public void testGetTerminal() {
        List<Map> terminals = merchantBasicInfoBiz.listAllSqbTerminals("21690003878930");
    }

    @Resource
    private InternalScheduleSubTaskDAO internalScheduleSubTaskDAO;

    @Test
    public void testUpdateTradeParams() {
        Optional<InternalScheduleMainTaskDO> mainTask = internalScheduleMainTaskDAO.getByPrimaryKey(1890L);
        Optional<InternalScheduleSubTaskDO> subTask = internalScheduleSubTaskDAO.getByPrimaryKey(1970L);
        businessLicenceCertificationTask.updateParamsConfigWhenReContractSubTaskSuccess(mainTask.get(), subTask.get());
    }

    // todo 完善 test
    @Test
    public void testRollBack() {

    }

    @Test
    public void testChangeAcquirer() {
        acquirerService.applyChangeAcquirer("21690003878943", "lkl");
    }

    @Test
    public void testListAllSqbTerminalsByStoreSn() {
        List<Map> terminals = merchantBasicInfoBiz.listAllSqbTerminalsByStoreSn("21590000001496576");
    }

    @Resource
    private LklV3ShopTermBiz lklV3ShopTermBiz;

    @Resource
    private SubBizParamsBiz subBizParamsBiz;

    @Resource
    private TradeConfigService tradeConfigService;

    @Test
    public void testInsertTerminal() {
        Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName("provider_trade_params_key");
        String lklTradeParamsKey = org.apache.commons.collections4.MapUtils.getString(providerTradeParamsKey, "1033");
        String haikeTradeParamsKey = org.apache.commons.collections4.MapUtils.getString(providerTradeParamsKey, "1037");
        String merchantId = "d7d6c139-f5a0-4a37-bc5e-4312e8d05798";
        Map appConfigParams = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, 3, subBizParamsBiz.getOnlinePaymentTradeAppId());

        String onlinePaymentTradeAppId = subBizParamsBiz.getOnlinePaymentTradeAppId();
        lklV3ShopTermBiz.processMicroLicenseUpgradeCondition(928328L);
    }

    @Resource
    private CoreBTradeConfigService coreBTradeConfigService;

    @Resource
    private McProviderBiz mcProviderBiz;

    @Test
    public void testDeleteTradeExtConfig() {
        Map<String/*acquirer*/, Map<String/*payWay*/, MerchantProviderParamsDO>> acquirerParams = merchantTradeParamsBiz
                .listParamsByMerchantSn("21690003878943")
                .stream()
                .filter(t -> Objects.nonNull(t.getProvider()))
                .collect(Collectors.groupingBy(
                        t -> StringExtensionUtils.toSafeString(mcProviderBiz.getAcquirerByProvider(t.getProvider().toString()), ""),
                        Collectors.toMap(t -> StringExtensionUtils.toSafeString(t.getPayway()), t -> t, (t1, t2) -> {
                            int statusComparison = compareStatus(t1.getStatus(), t2.getStatus());
                            if (statusComparison != 0) {
                                return statusComparison > 0 ? t1 : t2;
                            }
                            return t1.getCtime().compareTo(t2.getCtime()) > 0 ? t1 : t2;
                        })));
    }

    private int compareStatus(Integer status1, Integer status2) {
        if (status1 == null && status2 == null) return 0;
        if (status1 == null) return -1;
        if (status2 == null) return 1;
        return Integer.compare(status1, status2);
    }

    @Resource
    private LklV3UpdateTradeParams lklHandler;

    @Resource
    private HaikeUpdateTradeParams haikeHandler;


    @Test
    public void testUpdatePayMerchantConfigParams() {
        // lkl 21690003890515 payWay 3
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn("21690003888049");
        MerchantProviderParamsDO params = merchantProviderParamsDAO.getByPrimaryKey("24e95e13-bf89-446b-b9ee-68ce1d550a45").get();
        params.setProviderMerchantId("77777777777777");
        lklHandler.updatePayMerchantConfigAndAppConfigParams(merchantInfo, params);
        // hiake 21690003890515 payWay 3
        MerchantInfo merchantInfo1 = merchantBasicInfoBiz.getMerchantInfoBySn("21690003890515");
        MerchantProviderParamsDO params1 = merchantProviderParamsDAO.getByPrimaryKey("39b87b75-2a95-4c29-8b28-066e27a9500d").get();
        params1.setProviderMerchantId("77777777777777");
        haikeHandler.updatePayMerchantConfigAndAppConfigParams(merchantInfo1, params1);
    }

    @Test
    public void testBuildTaskForNotMicroInSQB() {
        businessLicenceTaskService.insertLicenceUpdateTaskForNotMicroInSQB("21690003890515");
    }

    @Test
    public void testTimeRange() {
        LocalTime start = LocalTime.of(1, 0, 0);
        LocalTime end = LocalTime.of(6, 59, 59);
        assertThat(LocalTime.of(16, 20).isAfter(start) && LocalTime.of(16, 20).isBefore(end)).isFalse();
        assertThat(LocalTime.of(1, 1).isAfter(start) && LocalTime.of(1, 1).isBefore(end)).isTrue();
        assertThat(LocalTime.of(6, 59).isAfter(start) && LocalTime.of(6, 59).isBefore(end)).isTrue();
    }

    @Resource
    private RiskMerchantBusinessLicenseAuditService riskMerchantBusinessLicenseAuditService;

    @Test
    public void testQueryContractResult() {
        RiskEntryResult entryResult = new RiskEntryResult();
        entryResult.setMerchantSn("**************");
        entryResult.setSuccess(false);
        entryResult.setReason("进件失败");
        HashMap<String, Object> map = Maps.newHashMap();
        map.put(RiskEntryResult.APP, "失败test");
        entryResult.setMultiPlatformReason(map);
        riskMerchantBusinessLicenseAuditService.processEntryResult(entryResult);
    }

    @Test
    public void testUpdateBankCar() {
        UpdateCardInfoAfterMicroUpgradeDTO updateCardInfoAfterMicroUpgradeDTO = new UpdateCardInfoAfterMicroUpgradeDTO();
        updateCardInfoAfterMicroUpgradeDTO.setMerchantSn("**************");
        updateCardInfoAfterMicroUpgradeDTO.setAcquirerMerchantId("1697217FY296379");
        updateCardInfoAfterMicroUpgradeDTO.setBankCardNo("6221738888888340823");
        updateCardInfoAfterMicroUpgradeDTO.setOpenBankNo("************");
        updateCardInfoAfterMicroUpgradeDTO.setBankCardPhoto("https://private-images.shouqianba.com/sales-system-gateway/2024-11-15/1e0630a5d90a4775a771c3b676468209.jpeg");
        CuaCommonResultDTO result = businessLicenceTaskService.updateCardInfoAfterMicroUpgrade(updateCardInfoAfterMicroUpgradeDTO);
    }

    @Resource
    private AcquirerFacade acquirerFacade;

    @Test
    public void testUpdateUnionWx() {
        Optional<AcquirerSharedAbility> lklV3 = acquirerFacade.getSharedAbilityByAcquirer("lklV3");
        assertThat(lklV3.isPresent()).isTrue();
        lklV3.get().updatePayUnionMerchantIdToUnion("********-8eae-4e24-ae2c-389b351c4422", "********");

    }

    @Test
    public void testInternalTriggerMicroUpgrade1() {
        CuaCommonResultDTO cuaCommonResultDTO = businessLicenceTaskService.internalTriggerMicroUpgrade("**************", "lklV3");
        assertThat(cuaCommonResultDTO.isSuccess()).isFalse();
        assertThat(cuaCommonResultDTO.getMessage()).isEqualTo("不需要升级，商户已经切换收单机构，禁用原收单机构交易参数");
    }

    @Test
    public void testInternalTriggerMicroUpgrade2() {
        CuaCommonResultDTO cuaCommonResultDTO = businessLicenceTaskService.internalTriggerMicroUpgrade("**************", "fuyou");
        assertThat(cuaCommonResultDTO.isSuccess()).isFalse();
    }

    @Test
    public void testInternalTriggerMicroUpgrade3() {
        CuaCommonResultDTO cuaCommonResultDTO = businessLicenceTaskService.internalTriggerMicroUpgrade("**************", "lklV3");
        assertThat(cuaCommonResultDTO.isSuccess()).isTrue();
    }

    @Resource
    private ContractEventService contractEventService;

    @Test
    public void testSyncAccountToAcquirer() {
        contractEventService.refreshMerchantBankAccount("**************", "system");
    }

    @Test
    public void testHasLicenceUpdateNotFinishedTask() {
        boolean hasTask = businessLicenceTaskService.hasLicenceUpdateNotFinishedTask("**************");
        assertThat(hasTask).isFalse();
    }

    @Test
    public void testUpdateDisableReasonForMicroUpgradeDeletedParams() {
        String merchantSn = "**************";
        businessLicenceTaskService.updateDisableReasonForMicroUpgradeDeletedParams(merchantSn);
    }

    @Test
    public void testInternalTriggerMicroUpgradeByFile() {
        String filePath = "http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/%E5%B0%8F%E5%BE%AE%E5%95%86%E6%88%B7%E5%90%8D%E5%8D%95%E6%A8%A1%E6%9D%****************************.xlsx?type=file";
        businessLicenceTaskService.internalTriggerMicroUpgradeByFile(filePath);
    }

    // test internalTriggerMicroUpgrade
    @Test
    public void testInternalTriggerMicroUpgrade() {
        String merchantSn = "**************";
        CuaCommonResultDTO result = businessLicenceTaskService.internalTriggerMicroUpgrade(merchantSn);
        assertThat(result.isSuccess()).isTrue();
    }
}
