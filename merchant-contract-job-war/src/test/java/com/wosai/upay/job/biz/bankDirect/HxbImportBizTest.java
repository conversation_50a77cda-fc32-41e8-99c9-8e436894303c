package com.wosai.upay.job.biz.bankDirect;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ZjtlcbParams;
import com.wosai.upay.job.model.acquirer.CheckChangeAcquirerResp;
import com.wosai.upay.job.providers.DefaultChangeTradeParamsBiz;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class HxbImportBizTest {

    @InjectMocks
    private HxbImportBiz hxbImportBiz;

    @Mock
    private MerchantService merchantService;
    @Mock
    private FeeRateService feeRateService;
    @Mock
    private AcquirerService acquirerService;
    @Mock
    private TradeComboDetailService tradeComboDetailService;
    @Mock
    private WechatAuthBiz wechatAuthBiz;
    @Mock
    private ContractStatusMapper contractStatusMapper;
    @Mock
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    @Mock
    private DefaultChangeTradeParamsBiz defaultChangeTradeParamsBiz;
    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Mock
    private MerchantBankService merchantBankService;
    @Mock
    private MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Mock
    private SubBizParamsBiz subBizParamsBiz;

    private static final long ZJTLCB_TRADE_COMBO_ID = 1000L;
    @Before
    public void setUp() {
        ReflectionTestUtils.setField(hxbImportBiz, "zjtlcbTradeComboId", ZJTLCB_TRADE_COMBO_ID);
    }

    /**
     * 费率错误
     */
    @Test
    public void testImportZjtlcbParams01() {
        ZjtlcbParams invalidParams = new ZjtlcbParams();
        invalidParams.setMerchantSn("merchantSn");
        invalidParams.setProviderMchId("providerMchId");
        invalidParams.setWeixinSubMchId("weixinSubMchId");
        invalidParams.setAlipaySubMchId("alipaySubMchId");
        invalidParams.setAliFeeRate("0.01");
        invalidParams.setWxFeeRate("0.3");

        TradeComboDetailResult detailResult = new TradeComboDetailResult();
        detailResult.setPayway(PaywayEnum.ALIPAY.getValue());
        detailResult.setFeeRateMin("0.1");
        detailResult.setFeeRateMax("0.6");
        List<TradeComboDetailResult> detailResults = Collections.singletonList(detailResult);
        Mockito.doReturn(detailResults).when(tradeComboDetailService).listByComboId(ZJTLCB_TRADE_COMBO_ID);


        Mockito.doReturn(CollectionUtil.hashMap(Merchant.SN, invalidParams.getMerchantSn())).when(merchantService).getMerchantBySn(invalidParams.getMerchantSn());
        // Act & Assert
        CommonInvalidParameterException exception = Assertions.assertThrows(CommonInvalidParameterException.class, () -> {
            hxbImportBiz.importZjtlcbParams(invalidParams);
        });
        Assert.assertEquals("导入失败、手续费费率有误", exception.getMessage());
        Mockito.verify(merchantService, times(1)).getMerchantBySn(invalidParams.getMerchantSn());
        Mockito.verify(tradeComboDetailService, times(1)).listByComboId(any());
        Mockito.verify(acquirerService, never()).getMerchantAcquirer(invalidParams.getMerchantSn());
    }

    /**
     * 商户通道已经在泰隆银行
     */
    @Test
    public void testImportZjtlcbParams02() {
        ZjtlcbParams invalidParams = new ZjtlcbParams();
        invalidParams.setMerchantSn("merchantSn");
        invalidParams.setProviderMchId("providerMchId");
        invalidParams.setWeixinSubMchId("weixinSubMchId");
        invalidParams.setAlipaySubMchId("alipaySubMchId");
        invalidParams.setAliFeeRate("0.3");
        invalidParams.setWxFeeRate("0.3");

        TradeComboDetailResult detailResult = new TradeComboDetailResult();
        detailResult.setPayway(PaywayEnum.ALIPAY.getValue());
        detailResult.setFeeRateMin("0.1");
        detailResult.setFeeRateMax("0.6");
        List<TradeComboDetailResult> detailResults = Collections.singletonList(detailResult);
        Mockito.doReturn(detailResults).when(tradeComboDetailService).listByComboId(ZJTLCB_TRADE_COMBO_ID);

        Mockito.doReturn(CollectionUtil.hashMap(Merchant.SN, invalidParams.getMerchantSn())).when(merchantService).getMerchantBySn(invalidParams.getMerchantSn());
        Mockito.doReturn(AcquirerTypeEnum.ZJTLCB.getValue()).when(acquirerService).getMerchantAcquirer(invalidParams.getMerchantSn());
        Mockito.doReturn(new ContractStatus()).when(contractStatusMapper).selectByMerchantSn(invalidParams.getMerchantSn());
        // Act & Assert
        CommonInvalidParameterException exception = Assertions.assertThrows(CommonInvalidParameterException.class, () -> {
            hxbImportBiz.importZjtlcbParams(invalidParams);
        });
        Assert.assertEquals("商户当前已在【浙江泰隆银行】通道，重复导入请先切换到其他通道", exception.getMessage());
        Mockito.verify(merchantService, times(1)).getMerchantBySn(invalidParams.getMerchantSn());
        Mockito.verify(tradeComboDetailService, times(2)).listByComboId(any());
        Mockito.verify(acquirerService, times(1)).getMerchantAcquirer(invalidParams.getMerchantSn());
        Mockito.verify(acquirerService, never()).checkChangeAcquirer(invalidParams.getMerchantSn(), AcquirerTypeEnum.ZJTLCB.getValue());
    }

    /**
     * 不允许切换
     */
    @Test
    public void testImportZjtlcbParams03() {
        ZjtlcbParams invalidParams = new ZjtlcbParams();
        invalidParams.setMerchantSn("merchantSn");
        invalidParams.setProviderMchId("providerMchId");
        invalidParams.setWeixinSubMchId("weixinSubMchId");
        invalidParams.setAlipaySubMchId("alipaySubMchId");
        invalidParams.setAliFeeRate("0.3");
        invalidParams.setWxFeeRate("0.3");

        TradeComboDetailResult detailResult = new TradeComboDetailResult();
        detailResult.setPayway(PaywayEnum.ALIPAY.getValue());
        detailResult.setFeeRateMin("0.1");
        detailResult.setFeeRateMax("0.6");
        List<TradeComboDetailResult> detailResults = Collections.singletonList(detailResult);
        Mockito.doReturn(detailResults).when(tradeComboDetailService).listByComboId(ZJTLCB_TRADE_COMBO_ID);

        Mockito.doReturn(CollectionUtil.hashMap(Merchant.SN, invalidParams.getMerchantSn())).when(merchantService).getMerchantBySn(invalidParams.getMerchantSn());
        Mockito.doReturn(AcquirerTypeEnum.LKL_V3.getValue()).when(acquirerService).getMerchantAcquirer(invalidParams.getMerchantSn());
        Mockito.doReturn(new ContractStatus()).when(contractStatusMapper).selectByMerchantSn(invalidParams.getMerchantSn());
        Mockito.doReturn(CheckChangeAcquirerResp.fail("单元测试失败")).when(acquirerService).checkChangeAcquirer(invalidParams.getMerchantSn(), AcquirerTypeEnum.ZJTLCB.getValue());
        // Act & Assert
        ContractBizException exception = Assertions.assertThrows(ContractBizException.class, () -> {
            hxbImportBiz.importZjtlcbParams(invalidParams);
        });
        Assert.assertEquals("单元测试失败", exception.getMessage());
        Mockito.verify(merchantService, times(1)).getMerchantBySn(invalidParams.getMerchantSn());
        Mockito.verify(tradeComboDetailService, times(2)).listByComboId(any());
        Mockito.verify(acquirerService, times(1)).getMerchantAcquirer(invalidParams.getMerchantSn());
        Mockito.verify(acquirerService, times(1)).checkChangeAcquirer(invalidParams.getMerchantSn(), AcquirerTypeEnum.ZJTLCB.getValue());
        Mockito.verify(wechatAuthBiz, never()).getMerchantNameAndSettlementId(invalidParams.getMerchantSn());
    }

    /**
     * 允许切换
     */
    @Test
    public void testImportZjtlcbParams04() {
        ZjtlcbParams zjtlcbParams = new ZjtlcbParams();
        zjtlcbParams.setMerchantSn("merchantSn");
        zjtlcbParams.setProviderMchId("providerMchId");
        zjtlcbParams.setWeixinSubMchId("weixinSubMchId");
        zjtlcbParams.setAlipaySubMchId("alipaySubMchId");
        zjtlcbParams.setAliFeeRate("0.3");
        zjtlcbParams.setWxFeeRate("0.3");

        TradeComboDetailResult detailResult = new TradeComboDetailResult();
        detailResult.setPayway(PaywayEnum.ALIPAY.getValue());
        detailResult.setFeeRateMin("0.1");
        detailResult.setFeeRateMax("0.6");
        List<TradeComboDetailResult> detailResults = Collections.singletonList(detailResult);
        Mockito.doReturn(detailResults).when(tradeComboDetailService).listByComboId(ZJTLCB_TRADE_COMBO_ID);

        Mockito.doReturn(CollectionUtil.hashMap(Merchant.SN, zjtlcbParams.getMerchantSn())).when(merchantService).getMerchantBySn(zjtlcbParams.getMerchantSn());
        Mockito.doReturn(AcquirerTypeEnum.LKL_V3.getValue()).when(acquirerService).getMerchantAcquirer(zjtlcbParams.getMerchantSn());
        Mockito.doReturn(CheckChangeAcquirerResp.pass()).when(acquirerService).checkChangeAcquirer(zjtlcbParams.getMerchantSn(), AcquirerTypeEnum.ZJTLCB.getValue());
        Mockito.doReturn(new WechatAuthBiz.WechatAuthNameAndSettId()).when(wechatAuthBiz).getMerchantNameAndSettlementId(zjtlcbParams.getMerchantSn());
        ContractStatus contractStatus = new ContractStatus();
        contractStatus.setId(1L);
        contractStatus.setAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        Mockito.doReturn(contractStatus).when(contractStatusMapper).selectByMerchantSn(zjtlcbParams.getMerchantSn());
        Mockito.doReturn(new ListResult(1, Collections.singletonList(new HashMap<>()))).when(merchantBankService).findMerchantBankAccountPres(any(), any());
        // Act & Assert
        hxbImportBiz.importZjtlcbParams(zjtlcbParams);

        Mockito.verify(merchantService, times(1)).getMerchantBySn(zjtlcbParams.getMerchantSn());
        Mockito.verify(tradeComboDetailService, times(2)).listByComboId(any());
        Mockito.verify(acquirerService, times(1)).getMerchantAcquirer(zjtlcbParams.getMerchantSn());
        Mockito.verify(acquirerService, times(1)).checkChangeAcquirer(zjtlcbParams.getMerchantSn(), AcquirerTypeEnum.ZJTLCB.getValue());
        Mockito.verify(wechatAuthBiz, times(1)).getMerchantNameAndSettlementId(zjtlcbParams.getMerchantSn());
        Mockito.verify(contractStatusMapper, times(2)).selectByMerchantSn(anyString());
        Mockito.verify(merchantProviderParamsMapper, times(4)).insertSelective(any());
        Mockito.verify(contractStatusMapper, times(1)).updateByPrimaryKeySelective(any());
        Mockito.verify(feeRateService, times(1)).applyFeeRateOne(any());
    }
}