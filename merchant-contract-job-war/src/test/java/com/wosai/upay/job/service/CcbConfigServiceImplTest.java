package com.wosai.upay.job.service;

import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.mapper.CcbConfigChangeHistoryMapper;
import com.wosai.upay.job.mapper.CcbConfigMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.DO.CcbConfigExample;
import com.wosai.upay.job.model.ccbConfig.*;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import vo.ApiRequestParam;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class CcbConfigServiceImplTest extends H2DbBaseTest {

    @Autowired
    private CcbConfigServiceImpl ccbConfigService;

    @Autowired
    private CcbConfigMapper ccbConfigMapper;

    @Autowired
    private CcbConfigChangeHistoryMapper historyMapper;

    /**
     * code对应的配置已经存在
     */
    @Test
    public void testCreateCcbConfig01() {
        CcbConfig ccbConfig = CcbConfig.builder().district_code("310000").build();
        ccbConfigMapper.insertSelective(ccbConfig);

        CreateCcbConfigReq req = new CreateCcbConfigReq();
        req.setDistrictCode("310000");
        req.setPrivateMinPrice("0.1");
        req.setPublicMinPrice("0.1");
        req.setAccount("6224");
        req.setOperator("SYSTEM");
        ContractResponse response = ccbConfigService.createCcbConfig(req);
        assertFalse(response.isSuccess());
        assertTrue(response.getMsg().contains("已存在"));
    }

    /**
     * code对应的配置已经被逻辑删除 创建成功
     */
    @Test
    public void testCreateCcbConfig02() {
        CcbConfig ccbConfig = CcbConfig.builder().district_code("310000").deleted(true).build();
        ccbConfigMapper.insertSelective(ccbConfig);

        CreateCcbConfigReq req = new CreateCcbConfigReq();
        req.setDistrictCode("310000");
        req.setPrivateMinPrice("0.1");
        req.setPublicMinPrice("0.1");
        req.setAccount("6224");
        req.setOperator("SYSTEM");
        ContractResponse response = ccbConfigService.createCcbConfig(req);
        assertTrue(response.isSuccess());

        CcbConfigExample ccbConfigExample = new CcbConfigExample();
        ccbConfigExample.or().andDistrict_codeEqualTo(req.getDistrictCode()).andDeletedEqualTo(false);
        List<CcbConfig> ccbConfigs = ccbConfigMapper.selectByExample(ccbConfigExample);
        assertEquals(1, ccbConfigs.size());
        assertEquals(req.getPublicMinPrice(), ccbConfigs.get(0).getPublic_min_price());

        List<CcbConfigChangeHistory> ccbConfigChangeHistories = historyMapper.selectByCcbConfigId(ccbConfigs.get(0).getId());
        assertEquals(1, ccbConfigChangeHistories.size());
        assertEquals(CcbConfigChangeHistory.OP_TYPE_CREATE, ccbConfigChangeHistories.get(0).getOp_type());
        assertEquals(req.getOperator(), ccbConfigChangeHistories.get(0).getOperator());
    }

    /**
     * id对应的配置信息不存在
     */
    @Test
    public void testUpdateCcbConfig01() {
        UpdateCcbConfigReq req = new UpdateCcbConfigReq();
        req.setId(100L);
        req.setPrivateMinPrice("0.1");
        req.setPublicMinPrice("0.1");
        req.setAccount("6224");
        req.setOperator("SYSTEM");
        ContractResponse response = ccbConfigService.updateCcbConfig(req);
        assertFalse(response.isSuccess());
        assertTrue(response.getMsg().contains("不存在"));
    }

    /**
     * id对应的配置信息存在 更新成功
     */
    @Test
    public void testUpdateCcbConfig02() {
        CreateCcbConfigReq createCcbConfigReq = new CreateCcbConfigReq();
        createCcbConfigReq.setDistrictCode("310000");
        createCcbConfigReq.setPrivateMinPrice("0.1");
        createCcbConfigReq.setPublicMinPrice("0.1");
        createCcbConfigReq.setAccount("account");
        createCcbConfigReq.setOperator("SYSTEM");
        ccbConfigService.createCcbConfig(createCcbConfigReq);

        CcbConfigExample ccbConfigExample = new CcbConfigExample();
        ccbConfigExample.or().andDistrict_codeEqualTo(createCcbConfigReq.getDistrictCode()).andDeletedEqualTo(false);
        List<CcbConfig> ccbConfigs = ccbConfigMapper.selectByExample(ccbConfigExample);
        assertEquals(1, ccbConfigs.size());
        CcbConfig oldCcbConfig = ccbConfigs.get(0);

        UpdateCcbConfigReq req = new UpdateCcbConfigReq();
        req.setId(oldCcbConfig.getId());
        req.setPublicMinPrice("0.1");
        req.setPrivateMinPrice("0.2");
        req.setAccount("6224");
        req.setOperator("SYSTEM");
        ContractResponse response = ccbConfigService.updateCcbConfig(req);
        assertTrue(response.isSuccess());

        CcbConfig newCcbConfig = ccbConfigMapper.selectByPrimaryKey(req.getId());
        assertEquals(req.getAccount(), newCcbConfig.getAccount());

        // 一条创建的记录 一条更新的记录
        List<CcbConfigChangeHistory> ccbConfigChangeHistories = historyMapper.selectByCcbConfigId(req.getId());
        assertEquals(2, ccbConfigChangeHistories.size());
        assertEquals(CcbConfigChangeHistory.OP_TYPE_CREATE, ccbConfigChangeHistories.get(0).getOp_type());
        assertEquals(oldCcbConfig.getAccount(), ccbConfigChangeHistories.get(0).getAccount());
        assertEquals(CcbConfigChangeHistory.OP_TYPE_UPDATE, ccbConfigChangeHistories.get(1).getOp_type());
        assertEquals(newCcbConfig.getAccount(), ccbConfigChangeHistories.get(1).getAccount());

    }

    /**
     * id对应的不存在
     */
    @Test
    public void testDeleteCcbConfig01() {
        DeleteCcbConfigReq req = new DeleteCcbConfigReq();
        req.setId(100L);
        req.setOperator("SYSTEM");
        ContractResponse response = ccbConfigService.deleteCcbConfig(req);
        assertFalse(response.isSuccess());
        assertTrue(response.getMsg().contains("不存在"));
    }

    @Test
    public void testDeleteCcbConfig02() {
        CreateCcbConfigReq createCcbConfigReq = new CreateCcbConfigReq();
        createCcbConfigReq.setDistrictCode("310000");
        createCcbConfigReq.setPrivateMinPrice("0.1");
        createCcbConfigReq.setPublicMinPrice("0.1");
        createCcbConfigReq.setAccount("account");
        createCcbConfigReq.setOperator("SYSTEM");
        ccbConfigService.createCcbConfig(createCcbConfigReq);

        CcbConfigExample ccbConfigExample = new CcbConfigExample();
        ccbConfigExample.or().andDistrict_codeEqualTo(createCcbConfigReq.getDistrictCode()).andDeletedEqualTo(false);
        List<CcbConfig> ccbConfigs = ccbConfigMapper.selectByExample(ccbConfigExample);
        assertEquals(1, ccbConfigs.size());
        CcbConfig oldCcbConfig = ccbConfigs.get(0);

        DeleteCcbConfigReq deleteCcbConfigReq = new DeleteCcbConfigReq();
        deleteCcbConfigReq.setId(oldCcbConfig.getId());
        deleteCcbConfigReq.setOperator("TEST");
        ContractResponse response = ccbConfigService.deleteCcbConfig(deleteCcbConfigReq);
        assertTrue(response.isSuccess());

        CcbConfig ccbConfig = ccbConfigMapper.selectByPrimaryKey(oldCcbConfig.getId());
        assertTrue(ccbConfig.getDeleted());

        // 一条创建的记录 一条删除的记录
        List<CcbConfigChangeHistory> ccbConfigChangeHistories = historyMapper.selectByCcbConfigId(deleteCcbConfigReq.getId());
        assertEquals(2, ccbConfigChangeHistories.size());
        assertEquals(CcbConfigChangeHistory.OP_TYPE_CREATE, ccbConfigChangeHistories.get(0).getOp_type());
        assertEquals(createCcbConfigReq.getOperator(), ccbConfigChangeHistories.get(0).getOperator());
        assertEquals(CcbConfigChangeHistory.OP_TYPE_DELETE, ccbConfigChangeHistories.get(1).getOp_type());
        assertEquals(deleteCcbConfigReq.getOperator(), ccbConfigChangeHistories.get(1).getOperator());
    }

    @Test
    public void getConfigByCode(){
        ApiRequestParam<QueryCcbConfigCodeReq> request = new ApiRequestParam<>();
        QueryCcbConfigCodeReq req = new QueryCcbConfigCodeReq();
        req.setCity("110100");
        req.setProvince("110100");
        request.setBodyParams(req);
        CcbConfig config = ccbConfigService.getConfigByCode(request);
        assertEquals("北京市", config.getProvince());
    }

    @Test
    public void getConfigByLocation(){
        ApiRequestParam<String> request = new ApiRequestParam<>();
        request.setBodyParams("北京市 市辖区");
        CcbConfig config = ccbConfigService.getConfigByLocation(request);
        assertEquals("北京市", config.getProvince());
    }
}