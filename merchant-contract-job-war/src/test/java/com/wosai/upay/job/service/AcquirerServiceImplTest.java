package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.biz.acquirer.ChangeToTongLianBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.acquirer.ClearParam;
import com.wosai.upay.job.model.acquirer.SyncMchStatusResp;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.NewLakalaService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

public class AcquirerServiceImplTest extends H2DbBaseTest {

    /**
     * 被测试类
     */
    @SpyBean
    private AcquirerServiceImpl service;

    /**
     * 内部组件，spring 管理，自动注入
     */
    @MockBean
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    /**
     * 外部服务
     */
    @Mock
    private TradeConfigService tradeConfigService;

    @Mock
    private MerchantService merchantService;

    @MockBean
    private ChangeToTongLianBiz changeToTongLianBiz;

    @Mock
    private NewLakalaService newLakalaService;

    /**
     * 外部服务手动注入
     */
    @Before
    public void before() {
        ReflectionTestUtils.setField(service, "merchantService", merchantService);
    }

    @Test
    public void getMerchantAcquirerSuccess() {
        assertEquals("lkl", service.getMerchantAcquirer("lkl-mch"));
    }

    @Test
    public void getMerchantAcquirerFail() {
        thrown.expect(CommonPubBizException.class);
        service.getMerchantAcquirer("xx");
    }

    @Test
    public void getClearParam() {
        List<MerchantProviderParams> params = Arrays.asList(new MerchantProviderParams().setProvider_merchant_id("xx"));
        Mockito.doReturn(params).when(merchantProviderParamsMapper).selectByExample(any());
        ClearParam clearParam = service.getClearParam("lkl-mch");
        assertEquals("lkl", clearParam.getAcquirer());
        assertEquals("xx", clearParam.getAcquirer_mch_id());
    }

    @Test
    public void getClearParamFromCoreBusiness() {
        Mockito.doReturn(null).when(merchantProviderParamsMapper).selectByExample(any());
        Mockito.doReturn(CollectionUtil.hashMap("id", "id", "sn", "sn")).when(merchantService).getMerchantBySn(any());
        Mockito.doReturn(JSON.parseObject("{\"params\":{\"lakala_trade_params\":{\"lakala_merc_id\":\"xx\"}}}")).when(tradeConfigService).getMerchantConfigByMerchantIdAndPayway(any(), any());
        ClearParam clearParam = service.getClearParam("lkl-mch");
        assertEquals("lkl", clearParam.getAcquirer());
        assertEquals("xx", clearParam.getAcquirer_mch_id());
    }

    @Test
    public void applyChangeAcquirer() {
        Mockito.doReturn(true).when(changeToTongLianBiz).applyChangeAcquirer(any());
        assertTrue(service.applyChangeAcquirer("lkl-mch", "tonglian"));
    }

    @Test
    public void checkChangeAcquirer() {
        Mockito.doNothing().when(changeToTongLianBiz).preCheck(any(), any(), any(), any(),any(), any());
        Mockito.doReturn(CollectionUtil.hashMap("id", "id", "sn", "sn")).when(merchantService).getMerchantBySn(any());
        service.checkChangeAcquirer("lkl-mch", "tonglian");
    }

    @Test
    public void syncMchStatusToAcquirerSuccess() {
        ContractResponse contractResponse = new ContractResponse();
        contractResponse.setCode(200);
        Mockito.doReturn(contractResponse).when(newLakalaService).updateMerchantStatusToLklV2(any(), anyInt());
        SyncMchStatusResp resp = service.syncMchStatusToAcquirer("lkl-mch", 1);
        assertTrue(resp.isSuccess());
    }

    @Test
    public void syncMchStatusToAcquirerFail() {
        ContractResponse contractResponse = new ContractResponse();
        contractResponse.setCode(500);
        Mockito.doReturn(contractResponse).when(newLakalaService).updateMerchantStatusToLklV2(any(), anyInt());
        SyncMchStatusResp resp = service.syncMchStatusToAcquirer("lkl-mch", 1);
        assertFalse(resp.isSuccess());
    }
}