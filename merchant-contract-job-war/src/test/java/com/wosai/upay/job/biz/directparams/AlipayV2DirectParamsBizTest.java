package com.wosai.upay.job.biz.directparams;


import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.Constants.TradeConstants;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.RuleBiz;
import com.wosai.upay.job.model.directparams.AlipayV2DirectParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class AlipayV2DirectParamsBizTest extends BaseTest {

    public static List<String> subPayWays = Arrays.asList(TradeConstants.SUB_PAYWAY_TRADE_NAME
            , TradeConstants.SUB_PAYWAY_APP_NAME, TradeConstants.SUB_PAYWAY_WAP_NAME, TradeConstants.SUB_PAYWAY_H5_NAME, TradeConstants.SUB_PAYWAY_MINI_NAME);
    @MockBean
    MerchantProviderParamsBiz merchantProviderParamsBiz;
    @MockBean
    TradeConfigService tradeConfigService;
    @MockBean
    SupportService supportService;
    @MockBean
    MerchantService merchantService;
    @MockBean
    private RuleBiz ruleBiz;
    @SpyBean
    AlipayV2DirectParamsBiz alipayV2DirectParamsBiz;

    @Before
    public void reflect() {
        ReflectionTestUtils.setField(alipayV2DirectParamsBiz, "tradeConfigService", tradeConfigService);
        ReflectionTestUtils.setField(alipayV2DirectParamsBiz, "supportService", supportService);
        ReflectionTestUtils.setField(alipayV2DirectParamsBiz, "merchantService", merchantService);

        Mockito.doReturn(new HashMap<>()).when(tradeConfigService).getMerchantConfigByMerchantIdAndPayway(Mockito.anyString(), Mockito.anyInt());
        Map merchant = CollectionUtil.hashMap("merchant_id", "test_id", "sn", "sn");
        Mockito.doReturn(merchant).when(merchantService).getMerchant(Mockito.anyString());
    }


    @Test
    public void addDirectParams() {
        alipayV2DirectParamsBiz.addDirectParams(getDirectParams());
    }


    private AlipayV2DirectParams getDirectParams() {
        AlipayV2DirectParams baseParams = new AlipayV2DirectParams();
        baseParams.setMerchant_id("test_id");
        baseParams.setMerchant_sn("sn");
        AlipayV2DirectParams.AlipayH5Params h5Params = new AlipayV2DirectParams.AlipayH5Params();
        h5Params.setApp_id("app_id");
        AlipayV2DirectParams.AlipayV2Params v2Params = new AlipayV2DirectParams.AlipayV2Params();
        v2Params.setAuth_app_id("app_id");
        baseParams.setAlipay_app_v2_trade_params(h5Params);
        baseParams.setAlipay_h5_v2_trade_params(h5Params);
        baseParams.setAlipay_v2_trade_params(v2Params);
        baseParams.setAlipay_wap_v2_trade_params(v2Params);
        return baseParams;
    }

    @Test
    public void deleteDirectParams() {
        for (String sub : subPayWays) {
            alipayV2DirectParamsBiz.deleteDirectParams(new MerchantProviderParamsDto().setExtra(new HashMap<>()).setMerchant_sn("sn"), sub, "0.38");
        }
    }

    @Test
    public void handleDirectParams() {
        MerchantProviderParamsCustomDto customDto = new MerchantProviderParamsCustomDto();
        alipayV2DirectParamsBiz.handleDirectParams(customDto);
        Map extra = JSON.parseObject(JSON.toJSONString(getDirectParams()), Map.class);
        customDto.setExtra(extra);
        alipayV2DirectParamsBiz.handleDirectParams(customDto);

    }
}
