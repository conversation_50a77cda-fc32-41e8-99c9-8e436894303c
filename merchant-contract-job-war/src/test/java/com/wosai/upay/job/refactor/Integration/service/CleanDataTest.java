package com.wosai.upay.job.refactor.Integration.service;


import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.ChangeTradeParamsBiz;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Set;


/**
 * 洗数据测试
 *
 * <AUTHOR>
 */
@Slf4j
public class CleanDataTest extends BaseTest {

    @Resource
    private ChangeTradeParamsBiz changeTradeParamsBiz;


    @Test
    public void testListLklTermIds() {
        Set<String> termIds = changeTradeParamsBiz.listPartLklTerminalNos("21690003761717", "3f65cbae-bd25-4039-9b55-6c14753dc111");
        assert termIds.size() == 2;
        assert termIds.contains("APLPW34D");
        assert termIds.contains("A3Q8ZY89");
    }


}
