package com.wosai.upay.job.service;


import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.ContractSubTask;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


public class SubtaskResultServiceTest extends BaseTest {

    @Autowired
    SubtaskResultService subtaskResultService;


    @Test
    public void getSubtasksByPtaskIdAndChannel() {
        subtaskResultService.getSubtasksByPtaskIdAndChannel(10L, "lkl");
    }

    @Test
    public void getSubtasksByMerchantAndPayway() {
        List<ContractSubTask> subtasksByMerchantAndPayway = subtaskResultService.getSubtasksByMerchantAndPayway("21690003361868", 24726447L);
    }

    @Test
    public void selectByPrimaryKey() {
        subtaskResultService.selectByPrimaryKey(10L);
    }

    @Test
    public void getAuthApplyByTaskId() {
        subtaskResultService.getAuthApplyByTaskId(10L);
    }

    @Test
    public void getAcquireSubTask() {
        subtaskResultService.getAcquireSubTask(24751083L);
    }
}
