package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.wosai.common.exception.CommonDataObjectNotExistsException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.model.AppInfoResponse;
import com.wosai.model.wechat.AppAuthInfo;
import com.wosai.service.SystemService;
import com.wosai.upay.job.Constants.MerchantChangeDataConstant;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.biz.AuthAndComboTaskBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.enume.AuthAndComboTaskStatusCode;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.AuthAndComboTaskMapper;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.changeMerchantData.ApplyChangeMerchantDataReq;
import com.wosai.upay.job.model.changeMerchantData.CommonResp;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.util.ProviderUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyObject;

public class MerchantChangeDataServiceImplTest extends H2DbBaseTest {

    @InjectMocks
    private MerchantChangeDataServiceImpl merchantChangeDataService;

    @Mock
    private ComposeAcquirerBiz composeAcquirerBiz;
    @Mock
    private RuleContext ruleContext;
    @Mock
    private AcquirerService acquirerService;
    @Mock
    private MerchantService merchantService;
    @Mock
    private AuthAndComboTaskBiz authAndComboTaskBiz;
    @Mock
    private SystemService systemService;
    @Mock
    private MerchantProviderParamsService providerParamsService;
    @Mock
    private JobWeixinService jobWeixinService;
    @Mock
    private WechatAuthBiz wechatAuthBiz;

    @Autowired
    private ContractEventMapper eventMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private AuthAndComboTaskMapper authAndComboTaskMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(merchantChangeDataService, "eventMapper", eventMapper);
        ReflectionTestUtils.setField(merchantChangeDataService, "contractTaskMapper", contractTaskMapper);
        ReflectionTestUtils.setField(merchantChangeDataService, "authAndComboTaskMapper", authAndComboTaskMapper);
        ReflectionTestUtils.setField(merchantChangeDataService, "merchantProviderParamsMapper", merchantProviderParamsMapper);
    }

    /**
     * 1. 有未完成的任务
     */
    @Test
    public void testApplyChangeMerchantData01() {
        ApplyChangeMerchantDataReq req = new ApplyChangeMerchantDataReq().setMerchantSn("merchant_sn");
        contractTaskMapper.insert(new ContractTask().setType(ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE).setMerchant_sn(req.getMerchantSn()));
        CommonResp commonResp = merchantChangeDataService.applyChangeMerchantData(req);
        assertEquals("申请失败原因不一致", "商户有未完成的商户信息变更", commonResp.getMsg());
    }

    /**
     * 2. 提交成功
     */
    @Test
    public void testApplyChangeMerchantData02() {
        ApplyChangeMerchantDataReq req = new ApplyChangeMerchantDataReq().setMerchantSn("merchant_sn");
        Mockito.doReturn("lkl").when(acquirerService).getMerchantAcquirer(req.getMerchantSn());
        Mockito.doReturn("lkl-org").when(composeAcquirerBiz).getAcquirerDefaultRuleGroup("lkl");
        Mockito.doReturn(JSON.parseObject("{\"group_id\":\"lklorg\"}", RuleGroup.class)).when(ruleContext).getRuleGroup("lkl-org");
        CommonResp commonResp = merchantChangeDataService.applyChangeMerchantData(req);
        assertTrue("提交结果不一致", commonResp.isSuccess());
        List<ContractEvent> contractEvents = eventMapper.selectEventTodoByMerchantSn(req.getMerchantSn());
        assertEquals("事件类型不一致", ContractEvent.OPT_TYPE_UPDATE_MERCHANT_DATA, contractEvents.get(0).getEvent_type().intValue());
    }

    @Test
    public void testQueryMerchantChangeDataTaskStatus() {
        String merchantSn = "merchantSn";
        // 1. 没有任务，也没有event
        Integer result = merchantChangeDataService.queryMerchantChangeDataTaskStatus(merchantSn);
        assertNull("返回结果不一致", result);
        // 2. 没有任务,有event
        ContractEvent event = new ContractEvent().setMerchant_sn(merchantSn).setEvent_type(ContractEvent.OPT_TYPE_UPDATE_MERCHANT_DATA);
        eventMapper.insertSelective(event);
        result = merchantChangeDataService.queryMerchantChangeDataTaskStatus(merchantSn);
        assertEquals("返回结果不一致", result.intValue(), MerchantChangeDataConstant.COMMON_STATUS_PROCESS);
        // 3. 有任务,并且是成功的
        ContractTask contractTask = new ContractTask().setMerchant_sn(merchantSn).setType(ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE).setStatus(TaskStatus.SUCCESS.getVal());
        contractTaskMapper.insert(contractTask);
        result = merchantChangeDataService.queryMerchantChangeDataTaskStatus(merchantSn);
        assertEquals("返回结果不一致", result.intValue(), MerchantChangeDataConstant.COMMON_STATUS_SUCCESS);
    }

    /**
     * 1. lkl和tonglian参数是空的
     */
    @Test
    public void testSubmitWeixinAuthTask01() {
        String merchantSn = "merchantSn";
        Mockito.doReturn(new MerchantInfo().setSn(merchantSn)).when(merchantService).getMerchantBySn(any(), any());
        Mockito.doReturn("lkl").when(composeAcquirerBiz).getMerchantAcquirer(merchantSn);
        CommonResp result = merchantChangeDataService.submitAuthAndComboTask(merchantSn, new HashMap());
        assertFalse("提交结果不一致", result.isSuccess());

        Mockito.doReturn("tonglian").when(composeAcquirerBiz).getMerchantAcquirer(merchantSn);
        result = merchantChangeDataService.submitAuthAndComboTask(merchantSn, new HashMap());
        assertFalse("提交结果不一致", result.isSuccess());
    }

    /**
     * 2. 没有匹配的结算id
     */
    @Test
    public void testSubmitWeixinAuthTask02() {
        String merchantSn = "merchantSn";
        String industry = "industry";
        Mockito.doReturn(new MerchantInfo().setSn(merchantSn).setIndustry(industry)).when(merchantService).getMerchantBySn(any(), any());
        Mockito.doReturn("lkl").when(composeAcquirerBiz).getMerchantAcquirer(merchantSn);
        Mockito.doReturn(Collections.singletonList(new MerchantProviderParamsDto().setMerchant_sn(merchantSn).setProvider(1033).setWx_settlement_id("770")))
                .when(providerParamsService).getMerchantProviderParams(any());
        CommonResp result = merchantChangeDataService.submitAuthAndComboTask(merchantSn, new HashMap());
        assertFalse("提交结果不一致", result.isSuccess());
        assertEquals("失败原因不一致", "根据商户行业未找到结算id配置", result.getMsg());

        Mockito.doReturn(JSON.parseObject("{\"person\":\"762\"}", SettlementIdConfig.class))
                .when(jobWeixinService).getSettlementConfig(industry);
        result = merchantChangeDataService.submitAuthAndComboTask(merchantSn, new HashMap());
        assertFalse("提交结果不一致", result.isSuccess());
        assertEquals("失败原因不一致", "未找到符合条件的交易参数", result.getMsg());
    }

    /**
     * 3. 提交成功
     */
    @Test
    public void testSubmitWeixinAuthTask03() {
        String merchantSn = "merchantSn";
        String industry = "industry";
        Mockito.doReturn(new MerchantInfo().setSn(merchantSn).setIndustry(industry)).when(merchantService).getMerchantBySn(any(), any());
        Mockito.doReturn("lkl").when(composeAcquirerBiz).getMerchantAcquirer(merchantSn);
        Mockito.doReturn(Collections.singletonList(new MerchantProviderParamsDto().setMerchant_sn(merchantSn).setProvider(1033).setWx_settlement_id("770")))
                .when(providerParamsService).getMerchantProviderParams(any());
        Mockito.doReturn(JSON.parseObject("{\"person\":\"770\"}", SettlementIdConfig.class))
                .when(jobWeixinService).getSettlementConfig(industry);
        CommonResp result = merchantChangeDataService.submitAuthAndComboTask(merchantSn, new HashMap());
        assertTrue("提交结果不一致", result.isSuccess());
        AuthAndComboTask task = authAndComboTaskMapper.getLatestTask(merchantSn);
        assertNotNull("授权和套餐任务为空", task);
    }

    @Test
    public void testQueryAuthAndComboTaskStatus() {
        String merchantSn = "merchantSn";
        //1. 没有任务
        Integer result = merchantChangeDataService.queryAuthAndComboTaskStatus(merchantSn);
        assertNull("结果不为null", result);

        //2. 任务成功
        AuthAndComboTask authAndComboTask = new AuthAndComboTask().setMerchant_sn(merchantSn).setStatus(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_SUCCEED);
        authAndComboTaskMapper.insertSelective(authAndComboTask);
        result = merchantChangeDataService.queryAuthAndComboTaskStatus(merchantSn);
        assertEquals("状态不一致", MerchantChangeDataConstant.COMMON_STATUS_SUCCESS, result.intValue());

    }

    @Test
    public void testQueryAuthAndComboTaskMsgAndCode() {
        // 1. 没有任务
        Map<String, Object> result = merchantChangeDataService.queryAuthAndComboTaskMsgAndCode("merchantSn", "crm");
        assertEquals("返回code不一致", AuthAndComboTaskStatusCode.NO_TASK.getCode(), result.get("contract_code"));
        // 2. 任务是待处理
        AuthAndComboTask authAndComboTask1 = new AuthAndComboTask().setMerchant_sn("merchantSn1").setStatus(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_WAIT_PROCESS);
        authAndComboTaskMapper.insertSelective(authAndComboTask1);
        result = merchantChangeDataService.queryAuthAndComboTaskMsgAndCode(authAndComboTask1.getMerchant_sn(), "crm");
        assertEquals("返回code不一致", AuthAndComboTaskStatusCode.PENDING_TASK.getCode(), result.get("contract_code"));
        // 3. 任务是待授权
        AuthAndComboTask authAndComboTask2 = new AuthAndComboTask().setMerchant_sn("merchantSn2").setStatus(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_WAIT_AUTH)
                .setForm_body("{\"merchant_config\":[{\"status\":1,\"payway\":\"2\",\"rate\":\"0.38\"},{\"status\":1,\"payway\":\"3\",\"rate\":\"0.6\"},{\"status\":1,\"ladder_fee_rates\":[{\"min\":0,\"max\":1000,\"rate\":\"0.38\"},{\"min\":1000,\"max\":null,\"rate\":\"0.6\"}],\"payway\":\"17\"}],\"trade_combo_id\":390}")
                .setSub_mch_id("testSubMchId")
                .setCreate_at(new Date());
        Mockito.doReturn(new AppInfoResponse().setResult(true).setAppAuthInfo(AppAuthInfo.builder().contact_name("张三").code("url").build()))
                .when(systemService).getAppInfoByInfo(any());
        authAndComboTaskMapper.insertSelective(authAndComboTask2);
        result = merchantChangeDataService.queryAuthAndComboTaskMsgAndCode(authAndComboTask2.getMerchant_sn(), "crm");
        assertEquals("返回code不一致", AuthAndComboTaskStatusCode.WAIT_FOR_AUTH.getCode(), result.get("contract_code"));
        assertTrue(BeanUtil.getPropString(result, "contract_memo").contains("张三"));
        assertTrue(BeanUtil.getPropString(result, "contract_memo").contains("testSubMchId"));
        assertTrue(BeanUtil.getPropString(result, "contract_memo").contains("0.6"));
        // 4. 任务是成功
        AuthAndComboTask authAndComboTask4 = new AuthAndComboTask().setMerchant_sn("merchantSn4").setStatus(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_SUCCEED);
        authAndComboTaskMapper.insertSelective(authAndComboTask4);
        result = merchantChangeDataService.queryAuthAndComboTaskMsgAndCode(authAndComboTask4.getMerchant_sn(), "crm");
        assertEquals("返回code不一致", AuthAndComboTaskStatusCode.TASK_SUCCESS.getCode(), result.get("contract_code"));
        // 5. 任务是失败
        AuthAndComboTask authAndComboTask5 = new AuthAndComboTask().setMerchant_sn("merchantSn5").setStatus(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_FAIL);
        authAndComboTaskMapper.insertSelective(authAndComboTask5);
        result = merchantChangeDataService.queryAuthAndComboTaskMsgAndCode(authAndComboTask5.getMerchant_sn(), "crm");
        assertEquals("返回code不一致", AuthAndComboTaskStatusCode.TASK_FAIL.getCode(), result.get("contract_code"));
    }

    @Test
    public void queryWeixinAuthStatus() {
        String merchantSn = "merchantSn";
        String subMchId = "subMchId";
        // 1. 任务为空
        Boolean auth = merchantChangeDataService.queryWeixinAuthStatus(merchantSn);
        assertNull("结果非空", auth);
        // 2. 交易参数不存在
        AuthAndComboTask authAndComboTask = new AuthAndComboTask().setMerchant_sn(merchantSn).setSub_mch_id(subMchId).setStatus(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_WAIT_AUTH);
        authAndComboTaskMapper.insertSelective(authAndComboTask);
        CommonDataObjectNotExistsException exception = Assertions.assertThrows(CommonDataObjectNotExistsException.class, () -> merchantChangeDataService.queryWeixinAuthStatus(merchantSn));
        assertEquals("异常信息不一致", "交易参数不存在", exception.getMessage());
        // 3. 授权通过, 但是出现异常
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams().setPay_merchant_id(subMchId).setMerchant_sn(merchantSn);
        merchantProviderParamsMapper.insertSelective(merchantProviderParams);
        Mockito.doReturn(true).when(wechatAuthBiz).getAuthStatus(any(MerchantProviderParams.class), any());
        Mockito.doThrow(new CommonDataObjectNotExistsException("单元测试")).when(authAndComboTaskBiz).changeTradeParamsAndApplyCombo(any(), any());
        auth = merchantChangeDataService.queryWeixinAuthStatus(merchantSn);
        assertTrue("结果不一致", auth);
        AuthAndComboTask latestTask = authAndComboTaskMapper.getLatestTask(merchantSn);
        assertEquals("状态不一致", MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_WAIT_AUTH, latestTask.getStatus().intValue());

        // 4. 授权通过
        Mockito.doNothing().when(authAndComboTaskBiz).changeTradeParamsAndApplyCombo(any(), any());
        Mockito.doReturn(new MerchantProviderParamsDto()).when(providerParamsService).setDefaultMerchantProviderParams(any(), any(), any());
        auth = merchantChangeDataService.queryWeixinAuthStatus(merchantSn);
        assertTrue("结果不一致", auth);
        latestTask = authAndComboTaskMapper.getLatestTask(merchantSn);
        assertEquals("状态不一致", MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_SUCCEED, latestTask.getStatus().intValue());

    }
}