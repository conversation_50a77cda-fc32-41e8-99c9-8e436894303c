package com.wosai.upay.job.providers;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.handlers.AbstractSubTaskHandler;
import com.wosai.upay.job.handlers.ContractSubTaskHandler;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/8/27
 */
public class CcbProviderTest {

}
