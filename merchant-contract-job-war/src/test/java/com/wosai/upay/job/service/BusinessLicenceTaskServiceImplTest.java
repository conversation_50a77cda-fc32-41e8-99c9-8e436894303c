package com.wosai.upay.job.service;

import com.wosai.upay.job.enume.microUpgrade.UpgradeVersion;
import com.wosai.upay.job.model.dto.crm.v3.MicroUpgradeDTO;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.refactor.service.impl.task.BusinessLicenceTaskServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BusinessLicenceTaskServiceImplTest {

    @Mock
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    @Mock
    private ApplicationApolloConfig applicationApolloConfig;

    @InjectMocks
    private BusinessLicenceTaskServiceImpl businessLicenceTaskService;



    @Test
    public void testDetermineMicroUpgradeVersion_whenIsMerchantLicenseMicro_returnV2() {
        // Arrange
        MicroUpgradeDTO mcUpgradeDTO = new MicroUpgradeDTO();
        mcUpgradeDTO.setMerchantSn("testMerchantSn");

        when(merchantBasicInfoBiz.isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn())).thenReturn(true);

        // Act
        UpgradeVersion result = businessLicenceTaskService.determineMicroUpgradeVersion(mcUpgradeDTO);

        // Assert
        assertEquals(UpgradeVersion.V2, result);
        verify(merchantBasicInfoBiz, times(1)).isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn());
    }

    @Test
    public void testDetermineMicroUpgradeVersion_whenRuleConfigIsNull_returnV2() {
        // Arrange
        MicroUpgradeDTO mcUpgradeDTO = new MicroUpgradeDTO();
        mcUpgradeDTO.setMerchantSn("testMerchantSn");

        when(merchantBasicInfoBiz.isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn())).thenReturn(false);
        when(applicationApolloConfig.getMicroUpgradeV3RuleConfig()).thenReturn(null);

        // Act
        UpgradeVersion result = businessLicenceTaskService.determineMicroUpgradeVersion(mcUpgradeDTO);

        // Assert
        assertEquals(UpgradeVersion.V2, result);
        verify(merchantBasicInfoBiz, times(1)).isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn());
        verify(applicationApolloConfig, times(1)).getMicroUpgradeV3RuleConfig();
    }

    @Test
    public void testDetermineMicroUpgradeVersion_whenModeAll_returnV3() {
        // Arrange
        MicroUpgradeDTO mcUpgradeDTO = new MicroUpgradeDTO();
        mcUpgradeDTO.setMerchantSn("testMerchantSn");

        ApplicationApolloConfig.MicroUpgradeV3RuleConfig ruleConfig = new ApplicationApolloConfig.MicroUpgradeV3RuleConfig();
        ruleConfig.setMode("all");

        when(merchantBasicInfoBiz.isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn())).thenReturn(false);
        when(applicationApolloConfig.getMicroUpgradeV3RuleConfig()).thenReturn(ruleConfig);

        // Act
        UpgradeVersion result = businessLicenceTaskService.determineMicroUpgradeVersion(mcUpgradeDTO);

        // Assert
        assertEquals(UpgradeVersion.V3, result);
        verify(merchantBasicInfoBiz, times(1)).isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn());
        verify(applicationApolloConfig, times(1)).getMicroUpgradeV3RuleConfig();
    }

    @Test
    public void testDetermineMicroUpgradeVersion_whenModeNone_returnV2() {
        // Arrange
        MicroUpgradeDTO mcUpgradeDTO = new MicroUpgradeDTO();
        mcUpgradeDTO.setMerchantSn("testMerchantSn");

        ApplicationApolloConfig.MicroUpgradeV3RuleConfig ruleConfig = new ApplicationApolloConfig.MicroUpgradeV3RuleConfig();
        ruleConfig.setMode("none");

        when(merchantBasicInfoBiz.isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn())).thenReturn(false);
        when(applicationApolloConfig.getMicroUpgradeV3RuleConfig()).thenReturn(ruleConfig);

        // Act
        UpgradeVersion result = businessLicenceTaskService.determineMicroUpgradeVersion(mcUpgradeDTO);

        // Assert
        assertEquals(UpgradeVersion.V2, result);
        verify(merchantBasicInfoBiz, times(1)).isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn());
        verify(applicationApolloConfig, times(1)).getMicroUpgradeV3RuleConfig();
    }

    @Test
    public void testDetermineMicroUpgradeVersion_whenModeWhitelistContainMerchantSn_returnV3() {
        // Arrange
        MicroUpgradeDTO mcUpgradeDTO = new MicroUpgradeDTO();
        mcUpgradeDTO.setMerchantSn("testMerchantSn");

        ApplicationApolloConfig.MicroUpgradeV3RuleConfig ruleConfig = new ApplicationApolloConfig.MicroUpgradeV3RuleConfig();
        ruleConfig.setMode("whitelist");
        ruleConfig.setWhitelist(Collections.singletonList("testMerchantSn"));

        when(merchantBasicInfoBiz.isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn())).thenReturn(false);
        when(applicationApolloConfig.getMicroUpgradeV3RuleConfig()).thenReturn(ruleConfig);

        // Act
        UpgradeVersion result = businessLicenceTaskService.determineMicroUpgradeVersion(mcUpgradeDTO);

        // Assert
        assertEquals(UpgradeVersion.V3, result);
        verify(merchantBasicInfoBiz, times(1)).isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn());
        verify(applicationApolloConfig, times(1)).getMicroUpgradeV3RuleConfig();
    }

    @Test
    public void testDetermineMicroUpgradeVersion_whenModeWhitelistNotContainMerchantSn_returnV2() {
        // Arrange
        MicroUpgradeDTO mcUpgradeDTO = new MicroUpgradeDTO();
        mcUpgradeDTO.setMerchantSn("testMerchantSn");

        ApplicationApolloConfig.MicroUpgradeV3RuleConfig ruleConfig = new ApplicationApolloConfig.MicroUpgradeV3RuleConfig();
        ruleConfig.setMode("whitelist");
        ruleConfig.setWhitelist(Collections.singletonList("otherMerchantSn"));

        when(merchantBasicInfoBiz.isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn())).thenReturn(false);
        when(applicationApolloConfig.getMicroUpgradeV3RuleConfig()).thenReturn(ruleConfig);

        // Act
        UpgradeVersion result = businessLicenceTaskService.determineMicroUpgradeVersion(mcUpgradeDTO);

        // Assert
        assertEquals(UpgradeVersion.V2, result);
        verify(merchantBasicInfoBiz, times(1)).isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn());
        verify(applicationApolloConfig, times(1)).getMicroUpgradeV3RuleConfig();
    }

    @Test
    public void testDetermineMicroUpgradeVersion_whenModeHashHit_returnV3() {
        // Arrange
        MicroUpgradeDTO mcUpgradeDTO = new MicroUpgradeDTO();
        mcUpgradeDTO.setMerchantSn("testMerchantSn");

        int hashHit = Math.abs("testMerchantSn".hashCode()) % 100;

        ApplicationApolloConfig.MicroUpgradeV3RuleConfig ruleConfig = new ApplicationApolloConfig.MicroUpgradeV3RuleConfig();
        ruleConfig.setMode("hash");
        ruleConfig.setHashMod(100);
        ruleConfig.setHashHit(Collections.singletonList(hashHit));

        when(merchantBasicInfoBiz.isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn())).thenReturn(false);
        when(applicationApolloConfig.getMicroUpgradeV3RuleConfig()).thenReturn(ruleConfig);

        // Act
        UpgradeVersion result = businessLicenceTaskService.determineMicroUpgradeVersion(mcUpgradeDTO);

        // Assert
        assertEquals(UpgradeVersion.V3, result);
        verify(merchantBasicInfoBiz, times(1)).isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn());
        verify(applicationApolloConfig, times(1)).getMicroUpgradeV3RuleConfig();
    }

    @Test
    public void testDetermineMicroUpgradeVersion_whenModeHashNotHit_returnV2() {
        // Arrange
        MicroUpgradeDTO mcUpgradeDTO = new MicroUpgradeDTO();
        mcUpgradeDTO.setMerchantSn("testMerchantSn");

        ApplicationApolloConfig.MicroUpgradeV3RuleConfig ruleConfig = new ApplicationApolloConfig.MicroUpgradeV3RuleConfig();
        ruleConfig.setMode("hash");
        ruleConfig.setHashMod(100);
        ruleConfig.setHashHit(Collections.singletonList(99)); // 不匹配的哈希值

        when(merchantBasicInfoBiz.isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn())).thenReturn(false);
        when(applicationApolloConfig.getMicroUpgradeV3RuleConfig()).thenReturn(ruleConfig);

        // Act
        UpgradeVersion result = businessLicenceTaskService.determineMicroUpgradeVersion(mcUpgradeDTO);

        // Assert
        assertEquals(UpgradeVersion.V2, result);
        verify(merchantBasicInfoBiz, times(1)).isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn());
        verify(applicationApolloConfig, times(1)).getMicroUpgradeV3RuleConfig();
    }

    @Test
    public void testDetermineMicroUpgradeVersion_whenModeWhitelistPlusHashWhitelistHit_returnV3() {
        // Arrange
        MicroUpgradeDTO mcUpgradeDTO = new MicroUpgradeDTO();
        mcUpgradeDTO.setMerchantSn("testMerchantSn");

        ApplicationApolloConfig.MicroUpgradeV3RuleConfig ruleConfig = new ApplicationApolloConfig.MicroUpgradeV3RuleConfig();
        ruleConfig.setMode("whitelist+hash");
        ruleConfig.setWhitelist(Collections.singletonList("testMerchantSn"));

        when(merchantBasicInfoBiz.isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn())).thenReturn(false);
        when(applicationApolloConfig.getMicroUpgradeV3RuleConfig()).thenReturn(ruleConfig);

        // Act
        UpgradeVersion result = businessLicenceTaskService.determineMicroUpgradeVersion(mcUpgradeDTO);

        // Assert
        assertEquals(UpgradeVersion.V3, result);
        verify(merchantBasicInfoBiz, times(1)).isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn());
        verify(applicationApolloConfig, times(1)).getMicroUpgradeV3RuleConfig();
    }

    @Test
    public void testDetermineMicroUpgradeVersion_whenModeWhitelistPlusHashHashHit_returnV3() {
        // Arrange
        MicroUpgradeDTO mcUpgradeDTO = new MicroUpgradeDTO();
        mcUpgradeDTO.setMerchantSn("testMerchantSn");

        int hashHit = Math.abs("testMerchantSn".hashCode()) % 100;

        ApplicationApolloConfig.MicroUpgradeV3RuleConfig ruleConfig = new ApplicationApolloConfig.MicroUpgradeV3RuleConfig();
        ruleConfig.setMode("whitelist+hash");
        ruleConfig.setWhitelist(Collections.singletonList("otherMerchantSn"));
        ruleConfig.setHashMod(100);
        ruleConfig.setHashHit(Collections.singletonList(hashHit));

        when(merchantBasicInfoBiz.isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn())).thenReturn(false);
        when(applicationApolloConfig.getMicroUpgradeV3RuleConfig()).thenReturn(ruleConfig);

        // Act
        UpgradeVersion result = businessLicenceTaskService.determineMicroUpgradeVersion(mcUpgradeDTO);

        // Assert
        assertEquals(UpgradeVersion.V3, result);
        verify(merchantBasicInfoBiz, times(1)).isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn());
        verify(applicationApolloConfig, times(1)).getMicroUpgradeV3RuleConfig();
    }

    @Test
    public void testDetermineMicroUpgradeVersion_whenModeWhitelistPlusHashNoHit_returnV2() {
        // Arrange
        MicroUpgradeDTO mcUpgradeDTO = new MicroUpgradeDTO();
        mcUpgradeDTO.setMerchantSn("testMerchantSn");

        ApplicationApolloConfig.MicroUpgradeV3RuleConfig ruleConfig = new ApplicationApolloConfig.MicroUpgradeV3RuleConfig();
        ruleConfig.setMode("whitelist+hash");
        ruleConfig.setWhitelist(Collections.singletonList("otherMerchantSn"));
        ruleConfig.setHashMod(100);
        ruleConfig.setHashHit(Collections.singletonList(99)); // 不命中

        when(merchantBasicInfoBiz.isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn())).thenReturn(false);
        when(applicationApolloConfig.getMicroUpgradeV3RuleConfig()).thenReturn(ruleConfig);

        // Act
        UpgradeVersion result = businessLicenceTaskService.determineMicroUpgradeVersion(mcUpgradeDTO);

        // Assert
        assertEquals(UpgradeVersion.V2, result);
        verify(merchantBasicInfoBiz, times(1)).isMerchantLicenseMicro(mcUpgradeDTO.getMerchantSn());
        verify(applicationApolloConfig, times(1)).getMicroUpgradeV3RuleConfig();
    }
}
