package com.wosai.upay.job.refactor.dao;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

public class MerchantProviderParamsDAOTest extends BaseTest {

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Test
    public void getDeletedSpecifiedMchIdTest() {
        Optional<MerchantProviderParamsDO> optional = merchantProviderParamsDAO.getDeletedSpecifiedMchId("2088100278736530");
        Assert.assertTrue(optional.isPresent());
    }

    @Test
    public void deleteFuYouWechatMchIdExceptSpecifiedOneTest() {
        merchantProviderParamsDAO.deleteFuYouWechatMchIdExceptSpecifiedOne("mch-1680002055931", "mch-1680002055931");
        Optional<MerchantProviderParamsDO> optional = merchantProviderParamsDAO.getDeletedSpecifiedMchId("2088100278736530");
        Assert.assertTrue(optional.isPresent());
    }

}
