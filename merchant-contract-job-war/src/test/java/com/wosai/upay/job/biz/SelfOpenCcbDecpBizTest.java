package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.*;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.mapper.OpenCcbDecpMapper;
import com.wosai.upay.job.mapper.SelfOpenCcbDecpMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.DO.OpenCcbDecp;
import com.wosai.upay.job.model.DO.SelfOpenCcbDecp;
import com.wosai.upay.job.model.DecpParamResp;
import com.wosai.upay.merchant.contract.model.OpenCcbDecpConstant;
import com.wosai.upay.merchant.contract.model.ccb.request.OpenCcbDecpReq;
import com.wosai.upay.merchant.contract.service.CcbService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Map;

import static javax.management.timer.Timer.ONE_DAY;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;

public class SelfOpenCcbDecpBizTest extends H2DbBaseTest {

    @InjectMocks
    private SelfOpenCcbDecpBiz selfOpenCcbDecpBiz;

    @Mock
    private CcbService ccbService;

    @Mock
    private UcUserAccountService ucUserAccountService;

    @Mock
    private SupportService supportService;

    @Mock
    private MerchantService merchantService;

    @Mock
    private TradeConfigService tradeConfigService;

    @Mock
    private MerchantBankService merchantBankService;

    @Mock
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Mock
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private SelfOpenCcbDecpMapper selfOpenCcbDecpMapper;

    @Autowired
    private OpenCcbDecpMapper openCcbDecpMapper;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(selfOpenCcbDecpBiz, "selfOpenCcbDecpMapper", selfOpenCcbDecpMapper);
        ReflectionTestUtils.setField(selfOpenCcbDecpBiz, "openCcbDecpMapper", openCcbDecpMapper);
    }

    @Test
    public void getOpenDecpParam() {
        String merchantId = "getOpenDecpParamId";
        String ucUserId = "getOpenDecpParamId";
        String merchantSn = "getOpenDecpParamSn";
        Mockito.doReturn(CollectionUtil.hashMap(Merchant.SN, merchantSn, Merchant.NAME, "测试名", Merchant.BUSINESS_NAME, "测试经营名")).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBankAccount.HOLDER, "测试持有人", MerchantBankAccount.IDENTITY, "123456200001016789")).when(merchantService).getMerchantBankAccountByMerchantId(merchantId);
        Mockito.doReturn(new UcUserInfo().setCellphone("***********")).when(ucUserAccountService).getUcUserById(ucUserId);
        // 1 没有批量开通的信息
        DecpParamResp openDecpParam = selfOpenCcbDecpBiz.getOpenDecpParam(merchantId, merchantId);
        assertEquals("***********", openDecpParam.getPhoneNumber());
        assertEquals("123***********6789", openDecpParam.getIdNumber());

        // 2 开通失败了, 请求参数是空的
        SelfOpenCcbDecp selfOpenCcbDecp = new SelfOpenCcbDecp();
        selfOpenCcbDecp.setMerchant_sn(merchantSn);
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.FAIL_OPEN_STATUS);
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp);
        openDecpParam = selfOpenCcbDecpBiz.getOpenDecpParam(merchantId, ucUserId);
        assertEquals("***********", openDecpParam.getPhoneNumber());

        // 2.2 开通失败了, 请求参数不是空的
        OpenCcbDecpReq openCcbDecpReq = new OpenCcbDecpReq();
        openCcbDecpReq.setMrchCtcPsnMblPhNo("***********");
        selfOpenCcbDecp.setRequest_body(JSON.toJSONString(openCcbDecpReq));
        selfOpenCcbDecpMapper.updateByPrimaryKeySelective(selfOpenCcbDecp);
        openDecpParam = selfOpenCcbDecpBiz.getOpenDecpParam(merchantId, ucUserId);
        assertEquals("***********", openDecpParam.getPhoneNumber());

        // 2.3 异名换卡改成了待开通, 请求参数不是空的
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.WAIT_OPEN_STATUS);
        selfOpenCcbDecpMapper.updateByPrimaryKeySelective(selfOpenCcbDecp);
        openDecpParam = selfOpenCcbDecpBiz.getOpenDecpParam(merchantId, ucUserId);
        assertEquals("***********", openDecpParam.getPhoneNumber());
    }


    /**
     * 开数字钱包成功了
     */
    @Test
    public void getSuccessDecpParam() {
        String merchantId = "getSuccessDecpParamId";
        String merchantSn = "getSuccessDecpParamSn";

        String walletId1 = "****************";
        Mockito.doReturn(CollectionUtil.hashMap(Merchant.SN, merchantSn, Merchant.NAME, "测试名", Merchant.BUSINESS_NAME, "测试经营名", Merchant.CONTACT_CELLPHONE, "***********")).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBankAccount.HOLDER, "测试持有人", MerchantBankAccount.IDENTITY, "123456200001016789")).when(merchantService).getMerchantBankAccountByMerchantId(merchantId);

        // 1 自己就是成功的
        SelfOpenCcbDecp selfOpenCcbDecp = new SelfOpenCcbDecp();
        selfOpenCcbDecp.setMerchant_sn(merchantSn);
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.SUCCESS_OPEN_STATUS);
        OpenCcbDecpReq openCcbDecpReq = new OpenCcbDecpReq();
        openCcbDecpReq.setLgPsRprNm("直接成功商户");
        openCcbDecpReq.setLglPsnCrdtNo("123456200001016789");
        openCcbDecpReq.setMrchCtcPsnMblPhNo("直接成功手机号");
        openCcbDecpReq.setCstAccNo(walletId1);
        selfOpenCcbDecp.setRequest_body(JSON.toJSONString(openCcbDecpReq));
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp);

        DecpParamResp openDecpParam = selfOpenCcbDecpBiz.getSuccessDecpParam(merchantId);
        assertEquals(openCcbDecpReq.getMrchCtcPsnMblPhNo(), openDecpParam.getPhoneNumber());
        assertEquals("123***********6789", openDecpParam.getIdNumber());
        assertEquals("测试持有人", openDecpParam.getHolder());
        assertEquals(walletId1, openDecpParam.getWalletId());
    }


    /**
     * 1 有开通成功记录
     * open_status 为开通成功 或者是审核中
     */
    @Test
    public void openCcbDecp01() {
        String merchantId = "openCcbDecp01Id";
        String merchantSn = "openCcbDecp01Sn";
        Mockito.doReturn(CollectionUtil.hashMap(DaoConstants.ID, merchantId, Merchant.SN, merchantSn, Merchant.CITY, "上海市", Merchant.NAME, "测试名", Merchant.BUSINESS_NAME, "测试经营名", Merchant.CONTACT_CELLPHONE, "***********")).when(merchantService).getMerchantByMerchantId(merchantId);

        SelfOpenCcbDecp selfOpenCcbDecp = new SelfOpenCcbDecp();
        selfOpenCcbDecp.setMerchant_sn(merchantSn);
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.SUCCESS_OPEN_STATUS);
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp);

        ContractResponse contractResponse = selfOpenCcbDecpBiz.openCcbDecp(merchantId, null, null);
        assertTrue(contractResponse.isSuccess());

        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.PROCESS_OPEN_STATUS);
        selfOpenCcbDecpMapper.updateByPrimaryKeySelective(selfOpenCcbDecp);
        contractResponse = selfOpenCcbDecpBiz.openCcbDecp(merchantId, null, null);
        assertTrue(contractResponse.isSuccess());
    }

    /**
     * 2 开通过, 但是失败了。
     * 去开通，开通成功了
     */
    @Test
    public void openCcbDecp02() {
        String merchantId = "openCcbDecp02Id";
        String merchantSn = "openCcbDecp02Sn";
        String identity = "123456200001016789";
        Mockito.doReturn(CollectionUtil.hashMap(DaoConstants.ID, merchantId, Merchant.SN, merchantSn, Merchant.CITY, "上海市", Merchant.NAME, "测试名", Merchant.BUSINESS_NAME, "测试经营名", Merchant.CONTACT_CELLPHONE, "***********")).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBankAccount.HOLDER, "测试持有人", MerchantBankAccount.IDENTITY, identity)).when(merchantService).getMerchantBankAccountByMerchantId(merchantId);

        // 开通失败的数据
        SelfOpenCcbDecp selfOpenCcbDecp = new SelfOpenCcbDecp();
        selfOpenCcbDecp.setMerchant_sn(merchantSn);
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.FAIL_OPEN_STATUS);
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp);

        // 该证件号开通成功的数据
        OpenCcbDecp openCcbDecp = new OpenCcbDecp();
        openCcbDecp.setId(1L);
        openCcbDecp.setResponse_body(JSON.toJSONString(CollectionUtil.hashMap("dataInfo", CollectionUtil.hashMap("Mrch_ID", "777"))));
        openCcbDecp.setStatus(OpenCcbDecpConstant.OPEN_SUCCESS);
        openCcbDecpMapper.insertSelective(openCcbDecp);

        Mockito.doReturn(new com.wosai.upay.merchant.contract.model.ContractResponse().setMessage(OpenCcbDecpConstant.SUCCESS_MESSAGE).setResponseParam(CollectionUtil.hashMap(OpenCcbDecpConstant.DECP_ID, 1)))
                .when(ccbService).selfOpenCcbDecp(any(), any(), any());
        ContractResponse contractResponse = selfOpenCcbDecpBiz.openCcbDecp(merchantId, null, null);
        SelfOpenCcbDecp result = selfOpenCcbDecpMapper.selectByMerchantSn(merchantSn);
        // 审核中
        assertTrue(contractResponse.isSuccess());
        assertEquals(SelfOpenCcbDecp.PROCESS_OPEN_STATUS, result.getOpen_status().intValue());
        assertEquals(1, result.getDecp_id().longValue());
    }

    /**
     * 2 没开通过。
     * 去开通，开通失败了
     */
    @Test
    public void openCcbDecp03() {
        String merchantId = "openCcbDecp02Id";
        String merchantSn = "openCcbDecp02Sn";
        String identity = "123456200001016789";
        Mockito.doReturn(CollectionUtil.hashMap(DaoConstants.ID, merchantId, Merchant.SN, merchantSn, Merchant.CITY, "上海市", Merchant.NAME, "测试名", Merchant.BUSINESS_NAME, "测试经营名", Merchant.CONTACT_CELLPHONE, "***********")).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBankAccount.HOLDER, "测试持有人", MerchantBankAccount.IDENTITY, identity)).when(merchantService).getMerchantBankAccountByMerchantId(merchantId);

        Mockito.doReturn(new com.wosai.upay.merchant.contract.model.ContractResponse().setMessage("开通失败").setResponseParam(CollectionUtil.hashMap(OpenCcbDecpConstant.DECP_ID, 1)))
                .when(ccbService).selfOpenCcbDecp(any(), any(), any());
        ContractResponse contractResponse = selfOpenCcbDecpBiz.openCcbDecp(merchantId, null, null);
        SelfOpenCcbDecp result = selfOpenCcbDecpMapper.selectByMerchantSn(merchantSn);
        // 开通失败
        assertFalse(contractResponse.isSuccess());
        assertEquals(SelfOpenCcbDecp.FAIL_OPEN_STATUS, result.getOpen_status().intValue());
        assertEquals(1, result.getDecp_id().longValue());
    }


    @Test
    public void cancelCcbDecp() {
        String merchantId = "cancelCcbDecpId01";
        String merchantSn = "cancelCcbDecpSn01";

        // 1 不是建行的数字钱包
        Mockito.doReturn(CollectionUtil.hashMap(MerchantConfig.B2C_AGENT_NAME, "1018")).when(tradeConfigService).getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.DCEP.getValue());
        ContractResponse contractResponse = selfOpenCcbDecpBiz.cancelCcbDecp(merchantId);
        assertTrue(contractResponse.isSuccess());

        // 2 是建行的数字钱包,但是没有批量提交记录
        Mockito.doReturn(CollectionUtil.hashMap(MerchantConfig.B2C_AGENT_NAME, "1026_*_*_false_true_0002")).when(tradeConfigService).getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.DCEP.getValue());
        contractResponse = selfOpenCcbDecpBiz.cancelCcbDecp(merchantId);
        assertTrue(contractResponse.isSuccess());

        // 3 是建行的数字钱包,有批量提交记录，会修改为待申请
        Mockito.doReturn(CollectionUtil.hashMap(Merchant.SN, merchantSn, DaoConstants.ID, merchantId)).when(merchantService).getMerchantByMerchantId(merchantId);
        SelfOpenCcbDecp selfOpenCcbDecp = new SelfOpenCcbDecp();
        selfOpenCcbDecp.setMerchant_sn(merchantSn);
        selfOpenCcbDecp.setOpen_status(SelfOpenCcbDecp.SUCCESS_OPEN_STATUS);
        selfOpenCcbDecp.setResult("开通成功");
        selfOpenCcbDecpMapper.insert(selfOpenCcbDecp);
        contractResponse = selfOpenCcbDecpBiz.cancelCcbDecp(merchantId);
        assertTrue(contractResponse.isSuccess());

        SelfOpenCcbDecp result = selfOpenCcbDecpMapper.selectByMerchantSn(merchantSn);
        assertEquals(SelfOpenCcbDecp.WAIT_OPEN_STATUS, result.getOpen_status().intValue());
        assertNull(result.getResult());
    }

    @Test
    public void buildDataTest() {
        long time = System.currentTimeMillis() - ONE_DAY;
        SelfOpenCcbDecp selfOpenCcbDecp = new SelfOpenCcbDecp();
        selfOpenCcbDecp.setMerchant_sn("merchant_sn");
        selfOpenCcbDecp.setDecp_id(1L);
        selfOpenCcbDecp.setOpen_status(2);
        selfOpenCcbDecp.setRequest_body(JSON.toJSONString(CollectionUtil.hashMap("LgPsRprNm", "张三", "Store_Adr", "上海市普陀区中江路")));
        selfOpenCcbDecp.setCtime(time);
        selfOpenCcbDecp.setMtime(time);
        selfOpenCcbDecpMapper.insertSelective(selfOpenCcbDecp);

        OpenCcbDecp openCcbDecp = new OpenCcbDecp();
        openCcbDecp.setId(1L);
        openCcbDecp.setResponse_body("{\"dataInfo\":{\"Mrch_ID\":\"777533100060506\"}}");
        openCcbDecpMapper.insertSelective(openCcbDecp);

        Mockito.doReturn(CollectionUtil.hashMap("city", "上海市")).when(merchantService).getMerchantBySn("merchant_sn");
        List<Map> buildData = ReflectionTestUtils.invokeMethod(selfOpenCcbDecpBiz, "buildData");
        Assert.assertNotNull(buildData);
        Assert.assertEquals(1, buildData.size());
        Assert.assertEquals("张三", BeanUtil.getPropString(buildData.get(0), "name"));
    }
}