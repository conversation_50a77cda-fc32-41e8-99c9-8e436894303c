package com.wosai.upay.job.refactor.task.license.micro.builder;

import com.wosai.upay.job.refactor.task.license.micro.builder.processor.PaywayProcessorFactory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 交易参数构建器测试类
 * 
 * <AUTHOR>
 */
public class TradeParamsBuilderTest {
    
    @BeforeEach
    public void setUp() {
        // 清空处理器缓存
        PaywayProcessorFactory.clearCache();
    }
    
    @Test
    public void testProcessorFactoryRegistration() {
        // 测试处理器工厂的注册功能
        assertTrue(PaywayProcessorFactory.getAllProcessors().isEmpty());
        
        // 这里可以添加更多的测试逻辑
        // 由于依赖Spring容器，实际测试需要在集成测试中进行
    }
    
    @Test
    public void testAcquirerTypes() {
        // 测试收单机构类型常量
        assertEquals("LKLV3", LklV3TradeParamsBuilder.ACQUIRER_TYPE);
        assertEquals("FUYOU", FuyouTradeParamsBuilder.ACQUIRER_TYPE);
        assertEquals("HAIKE", HaikeTradeParamsBuilder.ACQUIRER_TYPE);
    }
}
