package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.wosai.databus.avro.AvroEventEntry;
import com.wosai.databus.event.bank.AccountVerifyEvent;
import com.wosai.upay.bank.model.enume.AccountApplyStatus;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.biz.CompleteDateBiz;
import com.wosai.upay.job.biz.DataBusBiz;
import com.wosai.upay.job.biz.PayForResultBiz;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.service.TaskResultService;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.nio.ByteBuffer;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2020/7/15
 */
public class AccountVerifyConsumerTest extends H2DbBaseTest {

    @Autowired
    private AccountVerifyConsumer accountVerifyConsumer;

    @Autowired
    private PayForResultBiz payForResultBiz;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    private AccountVerifyEvent event = new AccountVerifyEvent();

    private static SerializeConfig serializeConfig = new SerializeConfig();

    static {
        serializeConfig.setPropertyNamingStrategy(PropertyNamingStrategy.SnakeCase);
    }

    @Before
    public void setUp(){
        event.setBusinessId("189559771492253696").setEventType("BANK_ACCOUNT_VERIFY");
        event.setModule("ACCOUNT");event.setSeq(0);event.setTimestamp(0);event.setObjectType("BANK_ACCOUNT_VERIFY");
        ReflectionTestUtils.setField(payForResultBiz,"taskResultService",Mockito.mock(TaskResultService.class));
        ReflectionTestUtils.setField(payForResultBiz,"completeDateBiz",Mockito.mock(CompleteDateBiz.class));
        ReflectionTestUtils.setField(payForResultBiz,"dataBusBiz",Mockito.mock(DataBusBiz.class));
    }

    @Test
    public void consumeSuccess() {
        event.setVerifyStatus(AccountApplyStatus.SUCCESS.getStatus());
        GenericRecord genericRecord = new AvroEventEntry();
        genericRecord.put("event", ByteBuffer.wrap(JSON.toJSONString(event,serializeConfig).getBytes()));
        ConsumerRecord<String, GenericRecord> record = new ConsumerRecord<String,GenericRecord>("单元测试",1,1,"key",genericRecord);
        accountVerifyConsumer.consume(record);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(2585382L);
        assertEquals("对公代付通过",JSON.parseObject(result.getResult(), Map.class).get("message"));
    }

    @Test
    public void consumeFail() {
        event.setVerifyStatus(AccountApplyStatus.FAIL.getStatus());
        GenericRecord genericRecord = new AvroEventEntry();
        genericRecord.put("event", ByteBuffer.wrap(JSON.toJSONString(event,serializeConfig).getBytes()));
        ConsumerRecord<String, GenericRecord> record = new ConsumerRecord<String,GenericRecord>("单元测试",1,1,"key",genericRecord);
        accountVerifyConsumer.consume(record);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(2585382L);
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_VERIFY_FAIL_PUBLIC,JSON.parseObject(result.getResult(), Map.class).get("message"));
    }

    @Test
    public void consumePaymentFail() {
        event.setVerifyStatus(AccountApplyStatus.PAYMENT_FAIL.getStatus());
        GenericRecord genericRecord = new AvroEventEntry();
        genericRecord.put("event", ByteBuffer.wrap(JSON.toJSONString(event,serializeConfig).getBytes()));
        ConsumerRecord<String, GenericRecord> record = new ConsumerRecord<String,GenericRecord>("单元测试",1,1,"key",genericRecord);
        accountVerifyConsumer.consume(record);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(2585382L);
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_PAYMENT_FAIL_PUBLIC,JSON.parseObject(result.getResult(), Map.class).get("message"));
    }

    @Test
    public void consumeBankChargeBack() {
        event.setVerifyStatus(AccountApplyStatus.BANK_CHARGEBACK_FAIL.getStatus());
        GenericRecord genericRecord = new AvroEventEntry();
        genericRecord.put("event", ByteBuffer.wrap(JSON.toJSONString(event,serializeConfig).getBytes()));
        ConsumerRecord<String, GenericRecord> record = new ConsumerRecord<String,GenericRecord>("单元测试",1,1,"key",genericRecord);
        accountVerifyConsumer.consume(record);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(2585382L);
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_BANK_CHARGEBACK_PUBLIC,JSON.parseObject(result.getResult(), Map.class).get("message"));
    }

    @Test
    public void consumeNeedVerify() {
        event.setVerifyStatus(AccountApplyStatus.WAIT_FOR_VERIFY.getStatus());
        GenericRecord genericRecord = new AvroEventEntry();
        genericRecord.put("event", ByteBuffer.wrap(JSON.toJSONString(event,serializeConfig).getBytes()));
        ConsumerRecord<String, GenericRecord> record = new ConsumerRecord<String,GenericRecord>("单元测试",1,1,"key",genericRecord);
        accountVerifyConsumer.consume(record);

        ContractTask result = contractTaskMapper.selectByPrimaryKey(2585382L);
        assertEquals(ScheduleUtil.TIPS_PAY_FOR_VERIFY_PUBLIC,JSON.parseObject(result.getResult(), Map.class).get("message"));
    }
}