package com.wosai.upay.job.biz.direct;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.biz.SensorSendBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.enume.WeixinDirectApplyStatus;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.WeixinDirectApplyMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.WeixinDirectApply;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.model.direct.*;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.WechatQrCodeUtils;
import com.wosai.upay.merchant.contract.model.weixin.DirectApplymentStatusResp;
import com.wosai.upay.merchant.contract.service.WeiXinDirectService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.wosai.upay.job.biz.paramContext.ParamContextBiz.KEY_MERCHANT;
import static org.junit.Assert.*;
import static org.mockito.Matchers.any;

@Transactional
public class WeixinDirectBizTest extends H2DbBaseTest {

    @Autowired
    private WeixinDirectBiz weixinDirectBiz;

    @Mock
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Mock
    private MerchantProviderParamsService merchantProviderParamsService;

    @Mock
    private MerchantService merchantService;

    @Mock
    private TradeConfigService tradeConfigService;

    @Mock
    private TradeComboDetailService comboDetailService;

    @Mock
    private WeiXinDirectService weiXinDirectService;

    @Mock
    private ParamContextBiz paramContextBiz;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private WeixinDirectApplyMapper weixinDirectApplyMapper;

    @Autowired
    private DirectStatusBiz directStatusBiz;

    private ApplicationApolloConfig applicationApolloConfig;

    @MockBean
    private ErrorCodeManageBiz errorCodeManageBiz;

    @MockBean
    private WechatQrCodeUtils wechatQrCodeUtils;

    @Value("${weixin.direct.online}")
    private String weixinDirectOnline;

    @Value("${weixin.direct.offline}")
    private String weixinDirectOffline;

    private Map processMessage = CollectionUtil.hashMap(ProviderUtil.WEIXIN_DIRECT,
            Arrays.asList(CollectionUtil.hashMap("error_msg", "微信审核中", "error_code", "1111", "crm_msg", "单元测试审核中文案"),
                    CollectionUtil.hashMap("error_msg", "待账户验证", "error_code", "2222", "crm_msg", "单元测试待商户验证文案"),
                    CollectionUtil.hashMap("error_msg", "待签约", "error_code", "3333", "crm_msg", "单元测试待签约文案"),
                    CollectionUtil.hashMap("error_msg", "开通权限中", "error_code", "4444", "crm_msg", "单元测试开通权限中文案")));

    private List<Map> failMemo = Arrays.asList(
            CollectionUtil.hashMap("error_msg", "微信返回开通失败", "error_code", "1111", "crm_msg", "单元测试微信审核失败文案"),
            CollectionUtil.hashMap("error_msg", "保底文案", "error_code", "2222", "crm_msg", "微信不让通过:#"));

    private Map<String, Object> subPayWayStatus = CollectionUtil.hashMap("b2c_status", 1,
            "b2c_status", 1,
            "b2c_formal", 1,
            "c2b_status", 1,
            "c2b_formal", 1,
            "wap_status", 1,
            "wap_formal", 1,
            "mini_status", 1,
            "mini_formal", 1,
            "h5_status", 1,
            "h5_formal", 1,
            "app_status", 1,
            "app_formal", 1);


    private String processWeixinOnLineTaskSn = "processWeixinOnLineTaskSn";
    private String processWeixinOnLineApplySn = "processWeixinOnLineApplySn";
    private String successWeixinOnlineSn = "successWeixinOnlineSn";
    private String weixinOffAuditSn = "weixinOffAuditSn";

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(weixinDirectBiz, "merchantBusinessLicenseService", merchantBusinessLicenseService);
        ReflectionTestUtils.setField(weixinDirectBiz, "merchantProviderParamsService", merchantProviderParamsService);
        ReflectionTestUtils.setField(weixinDirectBiz, "merchantService", merchantService);
        ReflectionTestUtils.setField(weixinDirectBiz, "tradeConfigService", tradeConfigService);
        ReflectionTestUtils.setField(weixinDirectBiz, "comboDetailService", comboDetailService);
        ReflectionTestUtils.setField(weixinDirectBiz, "weiXinDirectService", weiXinDirectService);
        ReflectionTestUtils.setField(weixinDirectBiz, "paramContextBiz", paramContextBiz);
        ReflectionTestUtils.setField(contractTaskBiz, "sensorSendBiz", Mockito.mock(SensorSendBiz.class));
        ReflectionTestUtils.setField(directStatusBiz, "kafkaTemplate", Mockito.mock(KafkaTemplate.class));
        applicationApolloConfig = Mockito.mock(ApplicationApolloConfig.class);
        ReflectionTestUtils.setField(weixinDirectBiz, "apolloParamsConfig", applicationApolloConfig);
        Mockito.doReturn(processMessage).when(applicationApolloConfig).getProcessingDirectMessage();
    }

    /**
     * 该应用不可以开通微信直连
     */
    @Test
    public void preCheck01() {
        thrown.expectMessage("该应用不可以开通微信直连");
        weixinDirectBiz.preCheck("merchant_sn", "dev_code");
    }

    /**
     * 存在未结束的直连申请任务
     */
    @Test
    public void preCheck02() {
        thrown.expectMessage("存在未结束的直连申请任务");
        weixinDirectBiz.preCheck(processWeixinOnLineTaskSn, weixinDirectOffline);
    }

    /**
     * 存在未完成的申请单
     */
    @Test
    public void preCheck03() {
        thrown.expectMessage("存在未完成的申请单");
        weixinDirectBiz.preCheck(processWeixinOnLineApplySn, weixinDirectOffline);
    }

    /**
     * 该商户已开通成功
     */
    @Test
    public void preCheck04() {
        thrown.expectMessage("该商户已开通成功");
        weixinDirectBiz.preCheck(successWeixinOnlineSn, weixinDirectOffline);
    }

    @Test
    public void buildWeixinParamContext() throws ContextParamException {
        WeixinDirectReq req = new WeixinDirectReq();
        req.setDev_code(weixinDirectOnline);
        weixinDirectBiz.buildWeixinParamContext(req);
        req.setDev_code(weixinDirectOffline);
        weixinDirectBiz.buildWeixinParamContext(req);
    }

    @Test
    public void createTaskAndApplyAndStatus() {
        WeixinDirectReq req = new WeixinDirectReq();
        req.setMerchant_sn("createTaskAndApplyAndStatus").setDev_code(weixinDirectOffline);
        weixinDirectBiz.createTaskAndApplyAndStatus(req, Maps.newHashMap());
        ContractTask contractTask = contractTaskMapper.getBySnAndType("createTaskAndApplyAndStatus", ProviderUtil.WEIXIN_DIRECT_OFFLINE);
        assertNotNull(contractTask);
        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn("createTaskAndApplyAndStatus", weixinDirectOffline);
        assertNotNull(weixinDirectApply);
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode("createTaskAndApplyAndStatus", weixinDirectOffline);
        assertNotNull(directStatus);
    }

    /**
     * 微信返回未知状态
     */
    @Test
    public void getLatestStatusFromWeixin01() {
        DirectApplymentStatusResp.DirectApplymentStatusRespBuilder builder = DirectApplymentStatusResp.builder();
        builder.applyment_state("not_exist");
        Mockito.doReturn(builder.build()).when(weiXinDirectService).queryApplyStatus(any());
        thrown.expectMessage("查询微信返回未知状态");
        weixinDirectBiz.getLatestStatusFromWeixin(weixinOffAuditSn, weixinDirectOffline, "crm");
    }


    /**
     * 审核中状态未变,返回默认文案
     */
    @Test
    public void getLatestStatusFromWeixin02() {
        DirectApplymentStatusResp.DirectApplymentStatusRespBuilder builder = DirectApplymentStatusResp.builder();
        builder.applyment_state("APPLYMENT_STATE_AUDITING");
        Mockito.doReturn(builder.build()).when(weiXinDirectService).queryApplyStatus(any());
        Mockito.doReturn(Maps.newHashMap()).when(applicationApolloConfig).getProcessingDirectMessage();
        ApplyStatusResp statusResp = weixinDirectBiz.getLatestStatusFromWeixin(weixinOffAuditSn, weixinDirectOffline, "crm");
        assertEquals(statusResp.getContract_memo(), WeixinDirectStatusCode.WEIXIN_AUDITING.getMsg());
    }

    /**
     * 查询微信返回待账户验证
     */
    @Test
    public void getLatestStatusFromWeixin03() {
        DirectApplymentStatusResp.DirectApplymentStatusRespBuilder builder = DirectApplymentStatusResp.builder();
        builder.applyment_state("APPLYMENT_STATE_TO_BE_CONFIRMED");
        Mockito.doReturn(builder.build()).when(weiXinDirectService).queryApplyStatus(any());
        ApplyStatusResp statusResp = weixinDirectBiz.getLatestStatusFromWeixin(weixinOffAuditSn, weixinDirectOffline, "crm");
        assertEquals(statusResp.getContract_memo(), "单元测试待商户验证文案");
        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn(weixinOffAuditSn, weixinDirectOffline);
        assertEquals(weixinDirectApply.getStatus(), WeixinDirectApplyStatus.WAIT_FOR_VERIFY.getVal());
    }

    /**
     * 查询微信返回待签约
     */
    @Test
    public void getLatestStatusFromWeixin04() {
        DirectApplymentStatusResp.DirectApplymentStatusRespBuilder builder = DirectApplymentStatusResp.builder();
        builder.applyment_state("APPLYMENT_STATE_TO_BE_SIGNED");
        Mockito.doReturn(builder.build()).when(weiXinDirectService).queryApplyStatus(any());
        ApplyStatusResp statusResp = weixinDirectBiz.getLatestStatusFromWeixin(weixinOffAuditSn, weixinDirectOffline, "crm");
        assertEquals(statusResp.getContract_memo(), "单元测试待签约文案");
        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn(weixinOffAuditSn, weixinDirectOffline);
        assertEquals(weixinDirectApply.getStatus(), WeixinDirectApplyStatus.WAIT_FOR_SIGN.getVal());
    }

    /**
     * 查询微信返回开通权限中
     */
    @Test
    public void getLatestStatusFromWeixin05() {
        DirectApplymentStatusResp.DirectApplymentStatusRespBuilder builder = DirectApplymentStatusResp.builder();
        builder.applyment_state("APPLYMENT_STATE_SIGNING");
        Mockito.doReturn(builder.build()).when(weiXinDirectService).queryApplyStatus(any());
        ApplyStatusResp statusResp = weixinDirectBiz.getLatestStatusFromWeixin(weixinOffAuditSn, weixinDirectOffline, "crm");
        assertEquals(statusResp.getContract_memo(), "单元测试开通权限中文案");
        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn(weixinOffAuditSn, weixinDirectOffline);
        assertEquals(weixinDirectApply.getStatus(), WeixinDirectApplyStatus.IN_OPENING_PERMISSION.getVal());
    }

    /**
     * 查询微信返回开通成功
     */
    @Test
    public void getLatestStatusFromWeixin06() {
        DirectApplymentStatusResp.DirectApplymentStatusRespBuilder builder = DirectApplymentStatusResp.builder();
        builder.applyment_state("APPLYMENT_STATE_FINISHED");
        Mockito.doReturn(builder.build()).when(weiXinDirectService).queryApplyStatus(any());
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn(weixinOffAuditSn, null);
        Mockito.doReturn(Collections.singletonList(new TradeComboDetailResult().setWapStatus(1).setB2cStatus(1).setH5Status(1).setMiniStatus(1).setAppStatus(1).setPayway(PaywayEnum.WEIXIN.getValue())))
                .when(comboDetailService).listByComboId(any());
        ApplyStatusResp statusResp = weixinDirectBiz.getLatestStatusFromWeixin(weixinOffAuditSn, weixinDirectOffline, "crm");
        assertEquals(statusResp.getContract_memo(), WeixinDirectStatusCode.WEIXIN_TASK_SUCCESS.getMsg());
        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn(weixinOffAuditSn, weixinDirectOffline);
        assertEquals(weixinDirectApply.getStatus(), WeixinDirectApplyStatus.APPLY_ACCEPTED.getVal());
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode(weixinOffAuditSn, weixinDirectOffline);
        assertEquals(directStatus.getStatus().intValue(), DirectStatus.STATUS_SUCCESS);
    }

    /**
     * 查询微信返回开通失败,无apollo文案
     */
    @Test
    public void getLatestStatusFromWeixin07() {
        DirectApplymentStatusResp.DirectApplymentStatusRespBuilder builder = DirectApplymentStatusResp.builder();
        builder.applyment_state("APPLYMENT_STATE_REJECTED");
        builder.audit_detail(Arrays.asList(new DirectApplymentStatusResp.RejectedReason().setField("name").setReject_reason("名字错误")));
        Mockito.doReturn(builder.build()).when(weiXinDirectService).queryApplyStatus(any());
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn(weixinOffAuditSn, weixinDirectOffline);
        Mockito.doReturn(null).when(errorCodeManageBiz).getPromptMessageFromErrorCodeManager(any(),any(),any());

        ApplyStatusResp statusResp = weixinDirectBiz.getLatestStatusFromWeixin(weixinOffAuditSn, weixinDirectOffline, "crm");
        assertTrue(statusResp.getContract_memo().contains("名字错误"));

        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn(weixinOffAuditSn, weixinDirectOffline);
        assertEquals(weixinDirectApply.getStatus(), WeixinDirectApplyStatus.APPLY_REJECTED.getVal());
        assertTrue(weixinDirectApply.getResult().contains("名字错误"));

        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode(weixinOffAuditSn, weixinDirectOffline);
        assertEquals(directStatus.getStatus().intValue(), DirectStatus.STATUS_BIZ_FAIL);
    }

    /**
     * 查询微信返回开通失败,有apollo文案
     */
    @Test
    public void getLatestStatusFromWeixin08() {
        DirectApplymentStatusResp.DirectApplymentStatusRespBuilder builder = DirectApplymentStatusResp.builder();
        builder.applyment_state("APPLYMENT_STATE_REJECTED");
        builder.audit_detail(Arrays.asList(new DirectApplymentStatusResp.RejectedReason().setField("name").setReject_reason("微信返回开通失败")));
        Mockito.doReturn(builder.build()).when(weiXinDirectService).queryApplyStatus(any());
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn(weixinOffAuditSn, weixinDirectOffline);

        ApplyStatusResp statusResp = weixinDirectBiz.getLatestStatusFromWeixin(weixinOffAuditSn, weixinDirectOffline, "crm");
        assertTrue(statusResp.getContract_memo().contains("单元测试微信审核失败文案"));

        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn(weixinOffAuditSn, weixinDirectOffline);
        assertEquals(weixinDirectApply.getStatus(), WeixinDirectApplyStatus.APPLY_REJECTED.getVal());
        assertTrue(weixinDirectApply.getResult().contains("微信返回开通失败"));

        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode(weixinOffAuditSn, weixinDirectOffline);
        assertEquals(directStatus.getStatus().intValue(), DirectStatus.STATUS_BIZ_FAIL);
    }

    /**
     * 查询微信返回开通失败,有apollo文案
     */
    @Test
    public void getLatestStatusFromWeixin09() {
        DirectApplymentStatusResp.DirectApplymentStatusRespBuilder builder = DirectApplymentStatusResp.builder();
        builder.applyment_state("APPLYMENT_STATE_REJECTED");
        builder.audit_detail(Arrays.asList(new DirectApplymentStatusResp.RejectedReason().setField("name").setReject_reason("未知失败")));
        Mockito.doReturn(builder.build()).when(weiXinDirectService).queryApplyStatus(any());
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn(weixinOffAuditSn, weixinDirectOffline);

        ApplyStatusResp statusResp = weixinDirectBiz.getLatestStatusFromWeixin(weixinOffAuditSn, weixinDirectOffline, "crm");
        assertTrue(statusResp.getContract_memo().contains("微信不让通过"));

        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn(weixinOffAuditSn, weixinDirectOffline);
        assertEquals(weixinDirectApply.getStatus(), WeixinDirectApplyStatus.APPLY_REJECTED.getVal());
        assertTrue(weixinDirectApply.getResult().contains("未知失败"));

        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode(weixinOffAuditSn, weixinDirectOffline);
        assertEquals(directStatus.getStatus().intValue(), DirectStatus.STATUS_BIZ_FAIL);
    }

    @Test
    public void getBusinessCode() {
        String businessCode = weixinDirectBiz.getBusinessCode(new WeixinDirectApply().setId(1L).setMerchant_sn("1680009898738"));
        assertEquals("test1680009898738", businessCode);
    }

    @Test
    public void doAfterStatusChanged() {
    }

    @Test
    public void getWeixinDirectContractMemo() {
        //1 不存在
        ApplyStatusResp statusResp = weixinDirectBiz.getWeixinDirectContractMemo("不存在的merchant_sn", weixinDirectOffline, "crm");
        assertEquals(statusResp.getContract_memo(), WeixinDirectStatusCode.NO_TASK.getMsg());
        //2 未处理
        statusResp = weixinDirectBiz.getWeixinDirectContractMemo(new ContractTask().setStatus(TaskStatus.PENDING.getVal()), "crm");
        assertEquals(statusResp.getContract_memo(), WeixinDirectStatusCode.PENDING_TASK.getMsg());
        //3 失败 未知原因
        statusResp = weixinDirectBiz.getWeixinDirectContractMemo(new ContractTask().setStatus(TaskStatus.FAIL.getVal()), "crm");
        assertEquals(statusResp.getContract_memo(), WeixinDirectStatusCode.UNKNOWN_CODE.getMsg());
        //4 失败 内部原因
        ContractTask contractTask = new ContractTask();
        contractTask.setStatus(TaskStatus.FAIL.getVal()).setResult(JSON.toJSONString(CollectionUtil.hashMap("channel", ProviderUtil.SHOUQIANBA_CHANNEL, "message", "内部失败")));
        statusResp = weixinDirectBiz.getWeixinDirectContractMemo(contractTask, "crm");
        assertEquals(statusResp.getContract_memo(), "内部失败");

    }

    /**
     * 失败次数不允许转人工
     */
    @Test
    public void transferToManual01() {
        WeixinDirectReq weixinDirectReq = new WeixinDirectReq();
        weixinDirectReq.setMerchant_sn("transferToManual").setDev_code(weixinDirectOffline);
        thrown.expectMessage("失败次数不允许转人工");
        weixinDirectBiz.transferToManual(weixinDirectReq);
    }

    /**
     * 转人工成功
     */
    @Test
    public void transferToManual02() {
        WeixinDirectReq weixinDirectReq = new WeixinDirectReq();
        weixinDirectReq.setMerchant_sn("manualTaskSn").setDev_code(weixinDirectOffline);
        weixinDirectBiz.transferToManual(weixinDirectReq);
        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn("manualTaskSn", weixinDirectOffline);
        assertEquals(weixinDirectApply.getStatus(), WeixinDirectApplyStatus.IN_SP_AUDITING.getVal());
    }

    /**
     * 不处于运营审核中的申请单
     */
    @Test
    public void changeWeixinDirectApplyStatus01() {
        ChangeApplyStatusReq req = new ChangeApplyStatusReq();
        req.setMerchantSn("merchant_sn").setDevCode("dev_code");
        thrown.expectMessage("申请单不存在或该申请单不处于人工审核中");
        weixinDirectBiz.changeWeixinDirectApplyStatus(req);
    }

    /**
     * 人工审核变更为微信审核中
     */
    @Test
    public void changeWeixinDirectApplyStatus02() {
        ChangeApplyStatusReq req = new ChangeApplyStatusReq();
        req.setMerchantSn("spAuditingSn").setDevCode(weixinDirectOffline).setStatus(WeixinDirectApplyStatus.IN_WEIXIN_AUDITING.getVal());
        weixinDirectBiz.changeWeixinDirectApplyStatus(req);
        WeixinDirectApply weixinDirectApply = weixinDirectApplyMapper.selectLatestApplyByMerchantSn("spAuditingSn", weixinDirectOffline);
        assertEquals(weixinDirectApply.getStatus(), WeixinDirectApplyStatus.IN_WEIXIN_AUDITING.getVal());
    }

    /**
     * 人工审核变更为审核通过
     */
    @Test
    public void changeWeixinDirectApplyStatus03() {
        ChangeApplyStatusReq req = new ChangeApplyStatusReq();
        req.setMerchantSn("spAuditingSn").setDevCode(weixinDirectOffline).setStatus(WeixinDirectApplyStatus.APPLY_ACCEPTED.getVal())
                .setSubMerchantSn("sub_merchant_sn");
        Mockito.doReturn(new MerchantInfo().setId("id")).when(merchantService).getMerchantBySn("spAuditingSn", null);
        Mockito.doReturn(Collections.singletonList(new TradeComboDetailResult().setWapStatus(1).setB2cStatus(1).setH5Status(1).setMiniStatus(1).setAppStatus(1).setPayway(PaywayEnum.WEIXIN.getValue())))
                .when(comboDetailService).listByComboId(any());
        weixinDirectBiz.changeWeixinDirectApplyStatus(req);
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode("spAuditingSn", weixinDirectOffline);
        assertEquals(DirectStatus.STATUS_SUCCESS, directStatus.getStatus().intValue());
    }

    /**
     * 人工审核变更为审核失败
     */
    @Test
    public void changeWeixinDirectApplyStatus04() {
        ChangeApplyStatusReq req = new ChangeApplyStatusReq();
        req.setMerchantSn("spAuditingSn").setDevCode(weixinDirectOffline).setStatus(WeixinDirectApplyStatus.APPLY_REJECTED.getVal())
                .setSubMerchantSn("sub_merchant_sn").setMessage("审核驳回");
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn("spAuditingSn", null);
        weixinDirectBiz.changeWeixinDirectApplyStatus(req);
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode("spAuditingSn", weixinDirectOffline);
        assertEquals(DirectStatus.STATUS_BIZ_FAIL, directStatus.getStatus().intValue());
    }

    /**
     * 人工审核变更为微信审核中
     */
    @Test
    public void changeWeixinDirectApplyStatus05() {
        ChangeApplyStatusReq req = new ChangeApplyStatusReq();
        req.setMerchantSn("spAuditingSn").setDevCode(weixinDirectOffline).setStatus(WeixinDirectApplyStatus.IN_WEIXIN_AUDITING.getVal())
                .setSubMerchantSn("sub_merchant_sn");
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn("spAuditingSn", null);
        weixinDirectBiz.changeWeixinDirectApplyStatus(req);
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode("spAuditingSn", weixinDirectOffline);
        assertEquals(DirectStatus.STATUS_PROCESS, directStatus.getStatus().intValue());
    }

    /**
     * 商户不存在
     */
    @Test
    public void checkIsSameSubject01() {
        BindMerchantReq bindMerchantReq = new BindMerchantReq();
        bindMerchantReq.setMerchant_sn("merchant_sn").setBind_merchant_sn("bind_merchant_sn");
        thrown.expect(CommonInvalidParameterException.class);
        weixinDirectBiz.checkIsSameSubject(bindMerchantReq);
    }

    /**
     * 营业执照不存在
     */
    @Test
    public void checkIsSameSubject02() {
        BindMerchantReq bindMerchantReq = new BindMerchantReq();
        bindMerchantReq.setMerchant_sn("merchant_sn").setDev_code(weixinDirectOffline)
                .setBind_merchant_sn("bind_merchant_sn");
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn(bindMerchantReq.getMerchant_sn(), weixinDirectOffline);
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn(bindMerchantReq.getBind_merchant_sn(), null);
        thrown.expect(CommonInvalidParameterException.class);
        weixinDirectBiz.checkIsSameSubject(bindMerchantReq);
    }

    /**
     * 主体不一致，未上传授权函
     */
    @Test
    public void checkIsSameSubject03() {
        BindMerchantReq bindMerchantReq = new BindMerchantReq();
        bindMerchantReq.setMerchant_sn("merchant_sn").setDev_code(weixinDirectOffline)
                .setBind_merchant_sn("bind_merchant_sn");
        Mockito.doReturn(new MerchantInfo().setId("merchant_id")).when(merchantService).getMerchantBySn(bindMerchantReq.getMerchant_sn(), weixinDirectOffline);
        Mockito.doReturn(new MerchantInfo().setId("bind_merchant_id")).when(merchantService).getMerchantBySn(bindMerchantReq.getBind_merchant_sn(), null);
        Mockito.doReturn(new MerchantBusinessLicenseInfo().setNumber("123")).when(merchantBusinessLicenseService).getMerchantBusinessLicenseByMerchantId("merchant_id", weixinDirectOffline);
        Mockito.doReturn(new MerchantBusinessLicenseInfo().setNumber("456")).when(merchantBusinessLicenseService).getMerchantBusinessLicenseByMerchantId("bind_merchant_id", null);
        thrown.expect(CommonInvalidParameterException.class);
        weixinDirectBiz.checkIsSameSubject(bindMerchantReq);
    }

    /**
     * 主体一致
     */
    @Test
    public void checkIsSameSubject04() {
        BindMerchantReq bindMerchantReq = new BindMerchantReq();
        bindMerchantReq.setMerchant_sn("merchant_sn").setDev_code(weixinDirectOffline)
                .setBind_merchant_sn("bind_merchant_sn");
        Mockito.doReturn(new MerchantInfo().setId("merchant_id")).when(merchantService).getMerchantBySn(bindMerchantReq.getMerchant_sn(), weixinDirectOffline);
        Mockito.doReturn(new MerchantInfo().setId("bind_merchant_id")).when(merchantService).getMerchantBySn(bindMerchantReq.getBind_merchant_sn(), null);
        Mockito.doReturn(new MerchantBusinessLicenseInfo().setNumber("123")).when(merchantBusinessLicenseService).getMerchantBusinessLicenseByMerchantId("merchant_id", weixinDirectOffline);
        Mockito.doReturn(new MerchantBusinessLicenseInfo().setNumber("123")).when(merchantBusinessLicenseService).getMerchantBusinessLicenseByMerchantId("bind_merchant_id", null);
        DirectStatusBiz mockDirectStatus = Mockito.mock(DirectStatusBiz.class);
        ReflectionTestUtils.setField(weixinDirectBiz, "directStatusBiz", mockDirectStatus);
        Mockito.doReturn(new DirectStatus().setStatus(DirectStatus.STATUS_SUCCESS)).when(mockDirectStatus).getDirectStatusByMerchantSnAndDevCode(bindMerchantReq.getBind_merchant_sn(), bindMerchantReq.getDev_code());
        Map result = weixinDirectBiz.checkIsSameSubject(bindMerchantReq);
        assertTrue(WosaiMapUtils.isNotEmpty(result));
        ReflectionTestUtils.setField(weixinDirectBiz, "directStatusBiz", directStatusBiz);
    }

    @Test
    public void buildWeixinDirectParams() {
    }

    @Test
    public void createFailTask() {
        WeixinDirectReq req = new WeixinDirectReq();
        req.setMerchant_sn("createFailTask").setDev_code(weixinDirectOffline);
        req.setContact_info(new WeixinDirectReq.Contact_info().setContact_name("name")
                .setContact_id_number("123123123").setMobile_phone("123123123").setContact_email("<EMAIL>"));
        req.setApp_info(new WeixinDirectReq.App_info().setFee_rate("0.38"));

        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn(req.getMerchant_sn(), req.getDev_code());

        weixinDirectBiz.createFailTask(req.getMerchant_sn(), req.getDev_code(), "创建失败进件任务");
        ContractTask contractTask = contractTaskMapper.getBySnAndType(req.getMerchant_sn(), ProviderUtil.WEIXIN_DIRECT_OFFLINE);
        String message = (String) JSON.parseObject(contractTask.getResult(), Map.class).get("message");
        assertEquals(contractTask.getStatus(), TaskStatus.FAIL.getVal());
        assertEquals("创建失败进件任务", message);

        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode(req.getMerchant_sn(), weixinDirectOffline);
        assertEquals(directStatus.getStatus().intValue(), DirectStatus.STATUS_BIZ_FAIL);

    }

    @Test
    public void getWeixinDirectIndustry() {
        WeixinDirectIndustry result = weixinDirectBiz.getWeixinDirectIndustry("industry_id");
        assertNotNull(result);
    }

    @Test
    public void bindMerchantWeixinDirectPay() {
        BindMerchantReq bindMerchantReq = new BindMerchantReq();
        bindMerchantReq.setDev_code(weixinDirectOffline);
        bindMerchantReq.setMerchant_sn("merchant_sn");
        bindMerchantReq.setBind_merchant_sn("bind_merchant_sn");
        Mockito.doReturn(new MerchantInfo().setId("id")).when(merchantService).getMerchantBySn("bind_merchant_sn", null);
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn("merchant_sn", null);
        Mockito.doReturn(subPayWayStatus).when(tradeConfigService).getMerchantConfigByMerchantIdAndPayway("id", PaywayEnum.WEIXIN.getValue());
        MerchantProviderParamsDto merchantProviderParamsDto = new MerchantProviderParamsDto();
        merchantProviderParamsDto.setExtra(CollectionUtil.hashMap(TransactionParam.WEIXIN_TRADE_PARAMS, new HashMap<>(1),
                                            TransactionParam.WEIXIN_WAP_TRADE_PARAMS, new HashMap<>(1),
                                            TransactionParam.WEIXIN_MINI_TRADE_PARAMS, new HashMap<>(1),
                                            TransactionParam.WEIXIN_APP_TRADE_PARAMS, new HashMap<>(1),
                                            TransactionParam.WEIXIN_H5_TRADE_PARAMS, new HashMap<>(1)));
        weixinDirectBiz.bindMerchantWeixinDirectPay(bindMerchantReq, merchantProviderParamsDto, CollectionUtil.hashMap(KEY_MERCHANT, new MerchantInfo()));
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode(bindMerchantReq.getMerchant_sn(), weixinDirectOffline);
        assertEquals(DirectStatus.STATUS_SUCCESS, directStatus.getStatus().intValue());
    }
}