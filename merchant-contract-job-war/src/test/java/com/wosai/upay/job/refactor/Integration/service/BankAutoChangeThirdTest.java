package com.wosai.upay.job.refactor.Integration.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.wosai.data.bean.BeanUtil;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.controller.TaskTriggerController;
import com.wosai.upay.job.model.dto.request.AppCommonReqDTO;
import com.wosai.upay.job.model.dto.request.BankAutoChangeToThirdPartyReqDTO;
import com.wosai.upay.job.model.dto.request.BankTradeProtectionConfigReqDTO;
import com.wosai.upay.job.refactor.dao.InternalScheduleMainTaskDAO;
import com.wosai.upay.job.refactor.model.bo.BankErrorRepairSolutionMappingBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.service.impl.BankTradeProtectionServiceImpl;
import com.wosai.upay.job.refactor.task.BankAutoChangeToThirdPartyTask;
import com.wosai.upay.job.refactor.task.BankAutoChangeThirdNotifyTask;
import com.wosai.upay.job.service.AcquirerService;
import org.apache.commons.collections4.MapUtils;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 结算账户类型支持收单机构服务测试
 *
 * <AUTHOR>
 * @date 2024/3/14 16:59
 */
public class BankAutoChangeThirdTest extends BaseTest {

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;

    @Resource
    private BankAutoChangeToThirdPartyTask bankAutoChangeToThirdPartyTask;

    @Test
    public void testApollo() {
        List<BankErrorRepairSolutionMappingBO> bankErrorRepairSolutionMappingBOS = applicationApolloConfig.listBankErrorRepairSolutionMapping();
    }

    @Test
    public void testWorkOrder() {
        InternalScheduleMainTaskDO mainTask = new InternalScheduleMainTaskDO();
        BankAutoChangeToThirdPartyReqDTO reqDTO = new BankAutoChangeToThirdPartyReqDTO();
        reqDTO.setErrorCode("EP221");
        reqDTO.setAcquirerOriginalErrorMsg("巴拉巴拉小魔仙");
        reqDTO.setErrorCodeMeaning("扫码支付月累计笔数超限，请顾客使用其他支付方式");
        reqDTO.setMerchantSn("**************");
        reqDTO.setPayWay("2");
        reqDTO.setProvider("1028");
        reqDTO.setTraAppIds(Sets.newHashSet("1"));
        mainTask.setContext(JSON.toJSONString(reqDTO));
        mainTask.setMerchantSn("**************");
        bankAutoChangeToThirdPartyTask.sendChangeWorkOrder(mainTask, "lklV3");
    }

    @Resource
    private AcquirerService acquirerService;

    @Resource
    private TaskTriggerController taskTriggerController;

    // 新增银行切三方任务
    @Test
    public void testInsertBankToThirdTask() {
        BankAutoChangeToThirdPartyReqDTO reqDTO = new BankAutoChangeToThirdPartyReqDTO();
        reqDTO.setErrorCode("EP826");
        reqDTO.setAcquirerOriginalErrorMsg("巴拉巴拉小魔仙");
        reqDTO.setErrorCodeMeaning("银行商户编号长度错误，请联系客户经理");
        reqDTO.setMerchantSn("**************");
        reqDTO.setPayWay("2");
        reqDTO.setProvider("1028");
        acquirerService.bankAutoChangeToThirdParty(reqDTO);
    }


    @Test
    public void testHandleBankToThirdTask() {
        Long[] ids = new Long[]{26L};
        taskTriggerController.triggerBatchInternalScheduleTasks(1, ids);
        // InternalScheduleTaskResultRspDTO rsp = taskTriggerController.triggerBatchInternalScheduleTasks(1, 1);
    }


    @Resource
    private InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    @Test
    public void testInsertThirdToBankTask() {
        Optional<InternalScheduleMainTaskDO> mainTask = internalScheduleMainTaskDAO.getByPrimaryKey(1L);
        //taskCenterStatusFlowConsumer.insertThirdPartyAutoChangeToBankTask("**************", mainTask.get());
    }

    //5. 三方切银行任务处理
    @Test
    public void testHandleThirdToBankTask() {
        taskTriggerController.triggerBatchInternalScheduleTasks(2, 2);
    }

    @Resource
    private IMerchantService iMerchantService;

    @Resource
    private OrganizationService organizationService;

    @Test
    public void testOrg() {
        String merchantSn = "**************";
        String organizationId = BeanUtil.getPropString(iMerchantService.getMerchantBySn(merchantSn), "organization_id");
         MapUtils.getString(organizationService.getOrganization(organizationId), "code");
    }

    @Test
    public void change() {
        acquirerService.applyChangeAcquirer("**************", "hxb", true);
    }

    @Resource
    private BankAutoChangeThirdNotifyTask bankAutoChangeThirdNotifyTask;

    @Test
    public void testDing() {
        bankAutoChangeThirdNotifyTask.sendBankAutoChangeThirdTaskEmail();
    }

    @Resource
    private BankTradeProtectionServiceImpl bankTradeProtectionService;

    @Test
    public void testBankTradeProtection() {
        BankTradeProtectionConfigReqDTO reqDTO = new BankTradeProtectionConfigReqDTO();
        reqDTO.setMerchantId("********-8ad7-4fb8-8590-dffb8615066a");
        reqDTO.setProtectionStatus(1);
        Assert.assertEquals(true, bankTradeProtectionService.updateTradeProtectionStatus(reqDTO));;
    }

    @Test
    public void testGetBankTradeProtectionStatus() {
        AppCommonReqDTO reqDTO = new AppCommonReqDTO();
        reqDTO.setMerchantId("********-8ad7-4fb8-8590-dffb8615066a");
        Assert.assertEquals(true, bankTradeProtectionService.getTradeProtectionStatus(reqDTO).getOpen());;
    }

    @Resource
    private BusinessLogBiz businessLogBiz;

    @Test
    public void testLog() {
        businessLogBiz.recordBankProtectionAutoChangeLog("8e1a0c46-bed8-4517-b0d3-04b666435522", "hxb", "lklV3", true);
        businessLogBiz.recordMerchantBankTradeProtectionConfigLog("8e1a0c46-bed8-4517-b0d3-04b666435522", true);
    }

}
