package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSONObject;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.app.dto.MerchantUserSimpleInfo;
import com.wosai.upay.job.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/8/19
 */
public class ClientSideSendTest extends BaseTest {

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Test
    public void send() {
        // 1:给商户超管角色发送通知（收钱吧APP)
        try {
                final MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
                sendModel.setDevCode("QV8FBQRYA0FA");
                //TODO 待提供模板templateCode
                sendModel.setTemplateCode("IQDV3GBNXZWL");
                sendModel.setMerchantUserId("d3a00194-6c5e-4d4f-8bd7-269a5f534f3d");
                sendModel.setTimestamp(System.currentTimeMillis());
                sendModel.setData(null);
//                clientSideNoticeService.sendToMerchantUser(sendModel);
        } catch (Exception e) {
        }
    }
}
