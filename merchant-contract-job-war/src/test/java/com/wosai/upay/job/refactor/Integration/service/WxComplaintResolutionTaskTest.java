package com.wosai.upay.job.refactor.Integration.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.task.bean.dto.req.TaskRpcStartReqDto;
import com.wosai.task.service.TaskInstanceService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.controller.TaskTriggerController;
import com.wosai.upay.job.model.ReContract;
import com.wosai.upay.job.refactor.task.WeChatComplaintResolutionTask;
import com.wosai.upay.job.service.ReContractService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 微信投诉任务测试
 *
 * <AUTHOR>
 * @date 2024/4/3 10:19
 */
public class WxComplaintResolutionTaskTest extends BaseTest {

    @Resource
    private WeChatComplaintResolutionTask weChatComplaintResolutionTask;

    @Test
    public void testInitTasks() {
        weChatComplaintResolutionTask.initTasks();
    }

    @Resource
    private ReContractService reContractService;

    @Test
    public void testReContract() {
        ReContract reContract = new ReContract();
        reContract.setMerchant_sn("21690003898610");
        reContract.setRule("lkl-1033-3-32631798");
        reContract.setRemark("消费者投诉，重新报备微信子商户号");
        reContractService.reContract(reContract);
    }

    @Autowired
    private TaskInstanceService taskInstanceService;


    @Test
    public void testSendOrder() {
        TaskRpcStartReqDto dto = new TaskRpcStartReqDto();
        dto.setOperator("SYSTEM");
        dto.setOperatorName("SYSTEM");
        dto.setPlatform("SYSTEM");
        dto.setReason("小魔仙");
        dto.setTaskTemplateId(294862L);
        dto.setTaskObjectSn("21690003857872");
        Map resultMap = taskInstanceService.startTaskForRpc(dto);
    }

    @Test
    public void testTask() {
        Long id = 2844L;
        weChatComplaintResolutionTask.batchHandleTasksByMainTaskIds(Lists.newArrayList(id));
    }

}
