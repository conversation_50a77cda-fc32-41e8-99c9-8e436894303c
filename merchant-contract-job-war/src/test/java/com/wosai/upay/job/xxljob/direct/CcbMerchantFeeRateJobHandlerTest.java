package com.wosai.upay.job.xxljob.direct;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.trade.service.result.QueryMchApplyLogsResult;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.CcbConfigBiz;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.CcbMerchantFeeRateMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.CcbMerchantFeeRate;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.ccbConfig.CcbConfig;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.ccb.response.CcbMerchantInfoResp;
import com.wosai.upay.merchant.contract.service.CcbService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.env.Environment;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;

@RunWith(MockitoJUnitRunner.class)
public class CcbMerchantFeeRateJobHandlerTest {

    @InjectMocks
    private CcbMerchantFeeRateJobHandler schedule;

    @Mock
    private Environment environment;

    @Mock
    private CcbService ccbService;

    @Mock
    private MerchantService merchantService;

    @Mock
    private FeeRateService feeRateService;

    @Mock
    private ContractParamsBiz contractParamsBiz;

    @Mock
    private CcbConfigBiz ccbConfigBiz;

    @Mock
    private CcbMerchantFeeRateMapper feeRateMapper;

    @Mock
    private AcquirerChangeDao acquirerChangeDao;

    @Mock
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Mock
    private ContractStatusMapper contractStatusMapper;

    @Mock
    private ApplicationApolloConfig applicationApolloConfig;

    @Mock
    private RedisLock redisLock;

    private static final String CCB_MERCHANT_INFO = "{\n" +
            "    \"POS_GRP\":[\n" +
            "        {\n" +
            "            \"Setl_AccNo_Ind\":\"02\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"RATE_LIST\":[\n" +
            "        {\n" +
            "            \"Acq_CmsnChrgRate_TpCd\":\"ZH\",\n" +
            "            \"FEE_DIS_LIST\":[\n" +
            "                {\n" +
            "                    \"DcnPrDsbPyAlctPctgVal\":\"60\",\n" +
            "                    \"PrDsbPty1_Apnt_Acc_No\":\"********\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"Fst_Lvl_HdCg_Rate\":\"0.2500\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Acq_CmsnChrgRate_TpCd\":\"ZD\",\n" +
            "            \"FEE_DIS_LIST\":[\n" +
            "                {\n" +
            "                    \"DcnPrDsbPyAlctPctgVal\":\"60\",\n" +
            "                    \"PrDsbPty1_Apnt_Acc_No\":\"********\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"Fst_Lvl_HdCg_Rate\":\"0.2500\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Acq_CmsnChrgRate_TpCd\":\"WH\",\n" +
            "            \"FEE_DIS_LIST\":[\n" +
            "                {\n" +
            "                    \"DcnPrDsbPyAlctPctgVal\":\"60\",\n" +
            "                    \"PrDsbPty1_Apnt_Acc_No\":\"********\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"Fst_Lvl_HdCg_Rate\":\"0.2500\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Acq_CmsnChrgRate_TpCd\":\"WD\",\n" +
            "            \"FEE_DIS_LIST\":[\n" +
            "                {\n" +
            "                    \"DcnPrDsbPyAlctPctgVal\":\"60\",\n" +
            "                    \"PrDsbPty1_Apnt_Acc_No\":\"********\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"Fst_Lvl_HdCg_Rate\":\"0.2500\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Acq_CmsnChrgRate_TpCd\":\"TH\",\n" +
            "            \"FEE_DIS_LIST\":[\n" +
            "                {\n" +
            "                    \"DcnPrDsbPyAlctPctgVal\":\"0\",\n" +
            "                    \"PrDsbPty1_Apnt_Acc_No\":\"********\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"Fst_Lvl_HdCg_Rate\":\"0.3825\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Acq_CmsnChrgRate_TpCd\":\"TD\",\n" +
            "            \"FEE_DIS_LIST\":[\n" +
            "                {\n" +
            "                    \"DcnPrDsbPyAlctPctgVal\":\"0\",\n" +
            "                    \"PrDsbPty1_Apnt_Acc_No\":\"********\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"Fst_Lvl_HdCg_Rate\":\"0.4825\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Acq_CmsnChrgRate_TpCd\":\"DJ\",\n" +
            "            \"FEE_DIS_LIST\":[\n" +
            "                {\n" +
            "                    \"DcnPrDsbPyAlctPctgVal\":\"0\",\n" +
            "                    \"PrDsbPty1_Apnt_Acc_No\":\"********\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"Fst_Lvl_HdCg_Rate\":\"0.4825\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Acq_CmsnChrgRate_TpCd\":\"FD\",\n" +
            "            \"FEE_DIS_LIST\":[\n" +
            "                {\n" +
            "                    \"DcnPrDsbPyAlctPctgVal\":\"0\",\n" +
            "                    \"PrDsbPty1_Apnt_Acc_No\":\"********\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"Fst_Lvl_HdCg_Rate\":\"0.3825\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"Mrch_ID\":\"105008158127130\"\n" +
            "}";

    @Test
    public void testQueryCcbMerchantFeeRate() {
        RestTemplate mockRestTemplate = Mockito.mock(RestTemplate.class);
        ReflectionTestUtils.setField(schedule, "batchSize", 5);
        ReflectionTestUtils.setField(schedule, "restTemplate", mockRestTemplate);
        ReflectionTestUtils.setField(schedule, "mailGateway", "http://mail-gateway.beta.iwosai.com");
        List<CcbMerchantFeeRate> rows1 = createRows(1, 5);
        List<CcbMerchantFeeRate> rows2 = createRows(11, 5);
        Mockito.doReturn(rows1, rows2, null).when(feeRateMapper).selectByIdRange(anyLong(), anyInt());
        Mockito.doReturn(Collections.singletonList(JSON.parseObject("{\"district_code\":\"310000\",\"min_price\":\"0.1\"}", CcbConfig.class))).when(ccbConfigBiz).getAllCcbConfigs();
        // 都失败了，没有发送邮件
        ContractResponse fail = new ContractResponse();
        fail.setCode(460);
        ArgumentCaptor<Map> request = ArgumentCaptor.forClass(Map.class);
        Mockito.doReturn(CollectionUtil.hashMap("errcode", 0)).when(mockRestTemplate).postForObject(anyString(), request.capture(), any());
        schedule.execute(new DirectJobParam());
        Mockito.verify(feeRateMapper, Mockito.times(3)).selectByIdRange(anyLong(), anyInt());
        assertFalse(request.getValue().containsKey("attachments"));

    }

    private List<CcbMerchantFeeRate> createRows(int startId, int size) {
        List<CcbMerchantFeeRate> rows = new ArrayList<>();
        for (long i = startId; i < startId + size; i++) {
            CcbMerchantFeeRate feeRate = new CcbMerchantFeeRate();
            feeRate.setId(i);
            feeRate.setMerchant_sn("sn" + i);
            rows.add(feeRate);
        }
        return rows;
    }

    @Test
    public void testDoSendUrl() {
        RestTemplate mockRestTemplate = Mockito.mock(RestTemplate.class);
        ReflectionTestUtils.setField(schedule, "restTemplate", mockRestTemplate);
        ReflectionTestUtils.setField(schedule, "mailGateway", "http://mail-gateway.beta.iwosai.com");
        List<CcbMerchantFeeRateJobHandler.CcbMerchantFeeRateExcel> excels = new ArrayList<>();
        excels.add(JSON.parseObject("{\"ccb_ratio\":\"60\",\"dj\":\"0.4825\",\"fd\":\"0.3825\",\"td\":\"0.4825\",\"th\":\"0.3825\",\"wd\":\"0.2500\",\"wh\":\"0.2500\",\"zd\":\"0.2500\",\"zh\":\"0.2500\"}", CcbMerchantFeeRateJobHandler.CcbMerchantFeeRateExcel.class));
        ArgumentCaptor<Map> request = ArgumentCaptor.forClass(Map.class);
        Mockito.doReturn(CollectionUtil.hashMap("errcode", 0)).when(mockRestTemplate).postForObject(anyString(), request.capture(), any());
        ReflectionTestUtils.invokeMethod(schedule, "doSendEmail", excels);
        assertTrue(request.getValue().containsKey("attachments"));

    }

    /**
     * 未找到收钱吧建行通道费率信息，返回null
     */
    @Test
    public void testHandleCcbMerchantFeeRate() {
        String merchantSn = "merchantSn";
        ContractResponse success = new ContractResponse();
        success.setCode(200);
        success.setResponseParam(CollectionUtil.hashMap("dataInfo", JSON.parseObject(CCB_MERCHANT_INFO, Map.class)));
        Mockito.doReturn(new ContractStatus().setAcquirer(AcquirerTypeEnum.LKL.getValue())).when(contractStatusMapper).selectByMerchantSn(merchantSn);
        Map<String, CcbConfig> ccbConfigMap = CollectionUtil.hashMap("310000", new CcbConfig());
        Set<String> accounts = new HashSet<>();
        accounts.add("********");
        CcbMerchantFeeRate oldFeeRate = new CcbMerchantFeeRate();
        oldFeeRate.setMerchant_sn(merchantSn);
        Object result = ReflectionTestUtils.invokeMethod(schedule, "handleCcbMerchantFeeRate", null, ccbConfigMap, accounts, oldFeeRate, LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        assertNull("返回结果非空", result);

    }

    /**
     * 费率信息不存在 || 费率信息分润都一致，返回null
     */
    @Test
    public void testCheckFeeRate01() {
        String merchantSn = "merchantSn";
        // 1. 费率信息不存在
        Mockito.doReturn(new ContractStatus().setAcquirer(AcquirerTypeEnum.LKL.getValue())).when(contractStatusMapper).selectByMerchantSn(merchantSn);
        Mockito.doReturn(null).when(acquirerChangeDao).getLatestSuccessApply(any(), any());
        Mockito.doReturn(null).when(feeRateService).queryMchApplyLogs(merchantSn);
        Mockito.doReturn(null).when(bankDirectApplyMapper).getApplyBySnAndDevCode(any(), any());
        CcbMerchantFeeRate oldFeeRate = new CcbMerchantFeeRate();
        oldFeeRate.setMerchant_sn(merchantSn);
        Object result = ReflectionTestUtils.invokeMethod(schedule, "checkFeeRate", oldFeeRate, null, "0.1");
        assertNull("不一致信息不为空", result);

        // 2. 费率信息一致, 分润信息一致
        ContractStatus contractStatus = new ContractStatus().setAcquirer(AcquirerTypeEnum.CCB.getValue());
        Mockito.doReturn(contractStatus).when(contractStatusMapper).selectByMerchantSn(merchantSn);
        Mockito.doReturn(Arrays.asList(new ListMchFeeRateResult().setPayWay(2).setBscFeeRate("0.25"), new ListMchFeeRateResult().setPayWay(3).setBscFeeRate("0.25")))
                .when(feeRateService).listMchEffectFeeRates(merchantSn);
        CcbMerchantFeeRate newFeeRate = JSON.parseObject("{\"wh_ratio\":\"60\",\"wd_ratio\":\"60\",\"zh_ratio\":\"60\",\"zd_ratio\":\"60\",\"dj\":\"0.4825\",\"fd\":\"0.3825\",\"td\":\"0.4825\",\"th\":\"0.3825\",\"wd\":\"0.2500\",\"wh\":\"0.2500\",\"zd\":\"0.2500\",\"zh\":\"0.2500\"}", CcbMerchantFeeRate.class);
        result = ReflectionTestUtils.invokeMethod(schedule, "checkFeeRate", oldFeeRate, newFeeRate, "0.1");
        assertNull("不一致信息不为空", result);
    }

    /**
     * 支付宝费率信息不一致
     */
    @Test
    public void testCheckFeeRate02() {
        String merchantSn = "merchantSn";
        // 收钱吧支付宝是0.26，建行是0.25
        CcbMerchantFeeRate oldFeeRate = new CcbMerchantFeeRate();
        oldFeeRate.setMerchant_sn(merchantSn);
        CcbMerchantFeeRate newFeeRate = JSON.parseObject("{\"wh_ratio\":\"60\",\"wd_ratio\":\"60\",\"zh_ratio\":\"60\",\"zd_ratio\":\"60\",\"dj\":\"0.4825\",\"fd\":\"0.3825\",\"td\":\"0.4825\",\"th\":\"0.3825\",\"wd\":\"0.2500\",\"wh\":\"0.2500\",\"zd\":\"0.2500\",\"zh\":\"0.2500\"}", CcbMerchantFeeRate.class);
        ContractStatus contractStatus = new ContractStatus().setAcquirer(AcquirerTypeEnum.CCB.getValue());
        Mockito.doReturn(contractStatus).when(contractStatusMapper).selectByMerchantSn(merchantSn);
        Mockito.doReturn(Arrays.asList(new ListMchFeeRateResult().setPayWay(2).setBscFeeRate("0.26").setTradeComboName("建设银行"), new ListMchFeeRateResult().setPayWay(3).setBscFeeRate("0.25").setTradeComboName("建设银行")))
                .when(feeRateService).listMchEffectFeeRates(merchantSn);
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn(merchantSn, null);
        Object result = ReflectionTestUtils.invokeMethod(schedule, "checkFeeRate", oldFeeRate, newFeeRate, "0.1");
        assertNotNull("不一致信息不为空", result);
        assertEquals("构建的execl信息不一致", "62", ((CcbMerchantFeeRateJobHandler.CcbMerchantFeeRateExcel)result).getSqbAliRatio());
    }

    /**
     * 微信费率信息不一致
     */
    @Test
    public void testCheckFeeRate03() {
        String merchantSn = "merchantSn";
        // 收钱吧微信是0.26，建行是0.25
        CcbMerchantFeeRate oldFeeRate = new CcbMerchantFeeRate();
        oldFeeRate.setMerchant_sn(merchantSn);
        CcbMerchantFeeRate newFeeRate = JSON.parseObject("{\"wh_ratio\":\"60\",\"wd_ratio\":\"60\",\"zh_ratio\":\"60\",\"zd_ratio\":\"60\",\"dj\":\"0.4825\",\"fd\":\"0.3825\",\"td\":\"0.4825\",\"th\":\"0.3825\",\"wd\":\"0.2500\",\"wh\":\"0.2500\",\"zd\":\"0.2500\",\"zh\":\"0.2500\"}", CcbMerchantFeeRate.class);
        ContractStatus contractStatus = new ContractStatus().setAcquirer(AcquirerTypeEnum.CCB.getValue());
        Mockito.doReturn(contractStatus).when(contractStatusMapper).selectByMerchantSn(merchantSn);
        Mockito.doReturn(Arrays.asList(new ListMchFeeRateResult().setPayWay(2).setBscFeeRate("0.25").setTradeComboName("建设银行"), new ListMchFeeRateResult().setPayWay(3).setBscFeeRate("0.26").setTradeComboName("建设银行")))
                .when(feeRateService).listMchEffectFeeRates(merchantSn);
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn(merchantSn, null);
        Object result = ReflectionTestUtils.invokeMethod(schedule, "checkFeeRate", oldFeeRate, newFeeRate, "0.1");
        assertNotNull("不一致信息不为空", result);
        assertEquals("构建的execl信息不一致", "62", ((CcbMerchantFeeRateJobHandler.CcbMerchantFeeRateExcel)result).getSqbWxRatio());
    }

    /**
     * 分润信息不一致
     */
    @Test
    public void testCheckFeeRate04() {
        String merchantSn = "merchantSn";
        // 收钱吧分润是60，建行是40
        CcbMerchantFeeRate oldFeeRate = new CcbMerchantFeeRate();
        oldFeeRate.setMerchant_sn(merchantSn);
        CcbMerchantFeeRate newFeeRate = JSON.parseObject("{\"wh_ratio\":\"40\",\"wd_ratio\":\"40\",\"zh_ratio\":\"40\",\"zd_ratio\":\"40\",\"dj\":\"0.4825\",\"fd\":\"0.3825\",\"td\":\"0.4825\",\"th\":\"0.3825\",\"wd\":\"0.2500\",\"wh\":\"0.2500\",\"zd\":\"0.2500\",\"zh\":\"0.2500\"}", CcbMerchantFeeRate.class);
        ContractStatus contractStatus = new ContractStatus().setAcquirer(AcquirerTypeEnum.CCB.getValue());
        Mockito.doReturn(contractStatus).when(contractStatusMapper).selectByMerchantSn(merchantSn);
        Mockito.doReturn(Arrays.asList(new ListMchFeeRateResult().setPayWay(2).setBscFeeRate("0.25").setTradeComboName("建设银行"), new ListMchFeeRateResult().setPayWay(3).setBscFeeRate("0.25").setTradeComboName("建设银行")))
                .when(feeRateService).listMchEffectFeeRates(merchantSn);
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn(merchantSn, null);
        Object result = ReflectionTestUtils.invokeMethod(schedule, "checkFeeRate", oldFeeRate, newFeeRate, "0.1");
        assertNotNull("不一致信息不为空", result);
        assertEquals("构建的execl信息不一致", "60", ((CcbMerchantFeeRateJobHandler.CcbMerchantFeeRateExcel)result).getSqbWxRatio());
    }


    @Test
    public void testGetSqbFeeRateInfo() {
        String merchantSn = "merchantSn";
        // 1. 当前就是在建行通道
        ContractStatus contractStatus = new ContractStatus().setMerchant_sn(merchantSn).setAcquirer(AcquirerTypeEnum.CCB.getValue());
        Mockito.doReturn(contractStatus).when(contractStatusMapper).selectByMerchantSn(merchantSn);
        Mockito.doReturn(Arrays.asList(new ListMchFeeRateResult().setPayWay(2).setBscFeeRate("0.38").setTradeComboName("建设银行"), new ListMchFeeRateResult().setPayWay(3).setBscFeeRate("0.38").setTradeComboName("建设银行")))
                .when(feeRateService).listMchEffectFeeRates(merchantSn);
        Map<Integer, String> feeRateInfo = ReflectionTestUtils.invokeMethod(schedule, "getSqbFeeRateInfo", merchantSn);
        assertNotNull("费率信息为空", feeRateInfo);
        assertEquals("支付宝费率信息不一致", "0.38", feeRateInfo.get(2));
        assertEquals("微信费率信息不一致", "0.38", feeRateInfo.get(3));

        // 2. 不在建行通道, 有切换通道任务
        contractStatus.setAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        Mockito.doReturn(contractStatus).when(contractStatusMapper).selectByMerchantSn(merchantSn);
        McAcquirerChange mcAcquirerChange = new McAcquirerChange();
        mcAcquirerChange.setExtra("{\"comboSnapshot\":{\"merchant_config\":[{\"payway\":2,\"rate\":\"0.3\"},{\"payway\":3,\"rate\":\"0.3\"},{\"payway\":17,\"rate\":\"0.3\"}]}}");
        Mockito.doReturn(mcAcquirerChange).when(acquirerChangeDao).getLatestSuccessApply(merchantSn, AcquirerTypeEnum.CCB.getValue());
        feeRateInfo = ReflectionTestUtils.invokeMethod(schedule, "getSqbFeeRateInfo", merchantSn);
        assertNotNull("费率信息为空", feeRateInfo);
        assertEquals("支付宝费率信息不一致", "0.3", feeRateInfo.get(2));
        assertEquals("微信费率信息不一致", "0.3", feeRateInfo.get(3));

        // 3. 不在建行通道，没有切换通道任务，有套餐设置记录
        Mockito.doReturn(null).when(acquirerChangeDao).getLatestSuccessApply(merchantSn, AcquirerTypeEnum.CCB.getValue());
        Mockito.doReturn(Arrays.asList(new QueryMchApplyLogsResult().setTradeAppName("银行合作").setPayWay(2).setFeeRate("0.38").setDescription("建设银行"),
                        new QueryMchApplyLogsResult().setTradeAppName("银行合作").setPayWay(3).setFeeRate("0.38").setDescription("建设银行"),
                        new QueryMchApplyLogsResult().setTradeAppName("支付业务").setPayWay(2).setFeeRate("0.3").setDescription("商户基础费率"),
                        new QueryMchApplyLogsResult().setTradeAppName("支付业务").setPayWay(3).setFeeRate("0.3").setDescription("商户基础费率")))
                .when(feeRateService).queryMchApplyLogs(merchantSn);
        Mockito.doReturn("银行合作").when(applicationApolloConfig).getBankTradeAppId();
        feeRateInfo = ReflectionTestUtils.invokeMethod(schedule, "getSqbFeeRateInfo", merchantSn);
        assertNotNull("费率信息为空", feeRateInfo);
        assertEquals("支付宝费率信息不一致", "0.38", feeRateInfo.get(2));
        assertEquals("微信费率信息不一致", "0.38", feeRateInfo.get(3));

        // 4. 不在建行通道，没有切换通道任务，没有套餐设置记录
        Mockito.doReturn(null).when(feeRateService).queryMchApplyLogs(merchantSn);
        Mockito.doReturn(new BankDirectApply().setForm_body("{\"merchant_config\":[{\"rate\":\"0.3\",\"payway\":\"2\",\"status\":1},{\"rate\":\"0.3\",\"payway\":\"3\",\"status\":1}]}"))
                .when(bankDirectApplyMapper).getApplyBySnAndDevCode(any(), any());
        feeRateInfo = ReflectionTestUtils.invokeMethod(schedule, "getSqbFeeRateInfo", merchantSn);
        assertNotNull("费率信息为空", feeRateInfo);
        assertEquals("支付宝费率信息不一致", "0.3", feeRateInfo.get(2));
        assertEquals("微信费率信息不一致", "0.3", feeRateInfo.get(3));
    }

    @Test
    public void testQueryMerchantInfo() {
        // 1. 第一次就成功了
        ContractResponse success = new ContractResponse();
        success.setCode(200);
        success.setResponseParam(CollectionUtil.hashMap("dataInfo", JSON.parseObject(CCB_MERCHANT_INFO, Map.class)));
        Mockito.doReturn(success).when(ccbService).queryMerchantInfo(any(), any());
        CcbMerchantInfoResp result = ReflectionTestUtils.invokeMethod(schedule, "queryMerchantInfo", "merchant_sn", null);
        Mockito.verify(ccbService, Mockito.times(1)).queryMerchantInfo(any(), any());
        assertNotNull(result);
        assertEquals("商户号不一致", "105008158127130", result.getMrchId());
        Mockito.clearInvocations(ccbService);

        // 2. 第一次500，第二次成功
        ContractResponse fail = new ContractResponse();
        fail.setCode(500);
        Mockito.doReturn(fail, success).when(ccbService).queryMerchantInfo(any(), any());
        result = ReflectionTestUtils.invokeMethod(schedule, "queryMerchantInfo", "merchant_sn", null);
        Mockito.verify(ccbService, Mockito.times(2)).queryMerchantInfo(any(), any());
        assertNotNull(result);
        assertEquals("商户号不一致", "105008158127130", result.getMrchId());
        Mockito.clearInvocations(ccbService);

        // 3. 都失败
        Mockito.doReturn(fail, fail).when(ccbService).queryMerchantInfo(any(), any());
        result = ReflectionTestUtils.invokeMethod(schedule, "queryMerchantInfo", "merchant_sn", null);
        Mockito.verify(ccbService, Mockito.times(2)).queryMerchantInfo(any(), any());
        assertNull(result);
    }

}