package com.wosai.upay.job.refactor.Integration.service;


import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.service.InternalKeyValueService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

import static org.assertj.core.api.Assertions.assertThat;



/**
 * 内部键值对服务测试
 *
 * <AUTHOR>
 * @date 2023/11/29 16:59
 */
@Slf4j
public class InternalKeyValueServiceTest extends BaseTest {

    @Resource
    private InternalKeyValueService internalKeyValueService;



    @Test
    public void testInsertOrUpdate() {
        int insert = internalKeyValueService.insertOrUpdateKeyValue("test002", "2");
        assertThat(insert).isEqualTo(1);
    }




}
