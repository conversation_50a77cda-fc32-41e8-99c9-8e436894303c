package com.wosai.upay.job.scheduler;


import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.McAcquirerChangeMapper;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.xxljob.batch.AcquirerChangeJobHandler;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class AcquirerChangeJobHandlerTest extends BaseTest {

    @Autowired
    private AcquirerChangeJobHandler acquirerChangeJobHandler;
    @Autowired
    private McAcquirerChangeMapper mapper;

    @Test
    public void checkContractTest() {
        McAcquirerChange mcAcquirerChange = mapper.selectByPrimaryKey(18991);
        Integer status = acquirerChangeJobHandler.checkContract(mcAcquirerChange);
        Assert.assertEquals(new Integer(20), status);
    }


    @Test
    public void contractTest() {
        McAcquirerChange mcAcquirerChange = mapper.selectByPrimaryKey(643714);
        Integer status = acquirerChangeJobHandler.contract(mcAcquirerChange);
        Assert.assertEquals(new Integer(3), status);
    }

    @Test
    public void syncMchStatusToAcquirerTest() {
        McAcquirerChange mcAcquirerChange = mapper.selectByPrimaryKey(643714);
        mcAcquirerChange.setImmediately(true);
        Integer status = acquirerChangeJobHandler.syncMchStatusToAcquirer(mcAcquirerChange);
        Assert.assertEquals(new Integer(4), status);
    }

    @Test
    public void changeAcquirerTest() {
        McAcquirerChange mcAcquirerChange = mapper.selectByPrimaryKey(665068);
        Integer status = acquirerChangeJobHandler.changeAcquirer(mcAcquirerChange);
        Assert.assertEquals(new Integer(19), status);
    }


}
