package com.wosai.upay.job.biz.bankDirect;

import java.util.HashMap;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.psbc.BankDirectReq;

public class BcsDirectBizTest extends BaseTest {

    @Autowired
    private BcsDirectBiz bcsDirectBiz;

    @Test
    public void applyBankDirectTest() {
        String content =
            "{\"dev_code\":\"NDDG9DXZ9W0P\",\"form_body\":\"{\\\"bank_pre_id\\\":\\\"0693c8ad-3482-444e-9979-a409c3ee1d7a\\\",\\\"crm_uesrId\\\":\\\"3685adda-0c78-4236-b6f0-cbc2477b6789\\\",\\\"dedicated_params\\\":{\\\"manageArea\\\":\\\"3\\\",\\\"manageAcreage\\\":\\\"1\\\"},\\\"trade_combo_id\\\":24744,\\\"merchant_config\\\":[{\\\"payway\\\":\\\"2\\\",\\\"rate\\\":\\\"0.38\\\",\\\"status\\\":1},{\\\"payway\\\":\\\"3\\\",\\\"rate\\\":\\\"0.38\\\",\\\"status\\\":1},{\\\"payway\\\":\\\"17\\\",\\\"rate\\\":\\\"0.38\\\",\\\"status\\\":1}]}\",\"merchant_sn\":\"**************\"} ";
        BankDirectReq bankDirectReq = JSONObject.parseObject(content, BankDirectReq.class);
        bcsDirectBiz.applyBankDirect(bankDirectReq);
    }

    @Test
    public void completeContextTest() {
        String content =
            "{\"dev_code\":\"G1ACMVIK2VAN\",\"form_body\":\"{\\\"merchant_config\\\":[{\\\"status\\\":1,\\\"payway\\\":\\\"2\\\",\\\"rate\\\":\\\"0.29\\\"},{\\\"status\\\":1,\\\"payway\\\":\\\"3\\\",\\\"rate\\\":\\\"0.3\\\"},{\\\"status\\\":1,\\\"payway\\\":\\\"17\\\",\\\"rate\\\":\\\"0.26\\\"}],\\\"trade_combo_id\\\":19071,\\\"crm_uesrId\\\":\\\"3685adda-0c78-4236-b6f0-cbc2477b6789\\\",\\\"mchtAptitude\\\":\\\"06\\\",\\\"storeLoca\\\":\\\"1\\\",\\\"mchtLanduse\\\":\\\"2\\\",\\\"licenseAmount\\\":\\\"11.22\\\",\\\"contactPhone\\\":\\\"***********\\\",\\\"mchtSaleName\\\":\\\"小菲\\\",\\\"mchtSaleNo\\\":\\\"SQB001\\\",\\\"orgId\\\":\\\"********\\\",\\\"bank_pre_id\\\":\\\"********-c091-4ced-9bd9-4fc495b8ff2a\\\"}\",\"merchant_sn\":\"**************\"}";
        BankDirectReq bankDirectReq = JSONObject.parseObject(content, BankDirectReq.class);
        bcsDirectBiz.completeContext(new HashMap<>(), bankDirectReq);
    }

}