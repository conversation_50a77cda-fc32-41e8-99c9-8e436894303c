package com.wosai.upay.job.refactor.Integration.service;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.refactor.biz.settlement.AcquirerSupportSettlementBiz;
import com.wosai.upay.job.refactor.dao.AcquirerSupportSettlementDAO;
import com.wosai.upay.job.refactor.model.entity.AcquirerSupportSettlementDO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 结算账户类型支持收单机构服务测试
 *
 * <AUTHOR>
 * @date 2024/3/14 16:59
 */
public class AcquirerSupportSettlementBizTest extends BaseTest {

    @Resource
    private AcquirerSupportSettlementBiz acquirerSupportSettlementBiz;

    @Resource
    private AcquirerSupportSettlementDAO acquirerSupportSettlementDAO;

    @Test
    public void testSelectOne() {
        Optional<AcquirerSupportSettlementDO> settlementDO = acquirerSupportSettlementDAO.getByAcquirerAndLicenseType("lklV3", 1);
        assert settlementDO.isPresent();
    }


}
