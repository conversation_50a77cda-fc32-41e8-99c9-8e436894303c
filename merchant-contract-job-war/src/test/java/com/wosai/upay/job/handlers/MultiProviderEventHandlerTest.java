package com.wosai.upay.job.handlers;

import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.service.StoreExtService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MultiProviderContractEventMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.model.eventmsg.EventMsg;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.service.BankCardServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

public class MultiProviderEventHandlerTest extends H2DbBaseTest {

    @InjectMocks
    private MultiProviderEventHandler handler;

    @Mock
    private BankCardServiceImpl bankCardService;

    @Autowired
    private RuleContext ruleContext;

    @Mock
    private StoreExtService mcStoreExtService;

    @Mock
    private ParamContextBiz paramContextBiz;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    private DataBusBiz dataBusBiz;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private MultiProviderContractEventMapper multiProviderEventMapper;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Mock
    private SensorSendBiz sensorSendBiz;

    @Mock
    private MerchantService merchantService;

    @Mock
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Mock
    private TradeManageBiz tradeManageBiz;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(handler, "contractSubTaskMapper", contractSubTaskMapper);
        ReflectionTestUtils.setField(handler, "multiProviderEventMapper", multiProviderEventMapper);
        ReflectionTestUtils.setField(handler, "dataBusBiz", dataBusBiz);
        ReflectionTestUtils.setField(handler, "contractTaskBiz", contractTaskBiz);
        ReflectionTestUtils.setField(handler, "ruleContext", ruleContext);

        ReflectionTestUtils.setField(contractTaskBiz, "sensorSendBiz", sensorSendBiz);

        ReflectionTestUtils.setField(dataBusBiz, "sensorSendBiz", sensorSendBiz);
        ReflectionTestUtils.setField(dataBusBiz, "merchantService", merchantService);
        ReflectionTestUtils.setField(dataBusBiz, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(dataBusBiz, "tradeManageBiz", tradeManageBiz);

        Mockito.doReturn(new HashMap()).when(merchantService).getMerchantBySn(any());
    }

    @Test
    public void handleError() throws Exception {
        // 1 异常是ContextParamException
        String merchantSn01 = "handleErrorSn01";
        MultiProviderContractEvent event = new MultiProviderContractEvent()
                .setMerchant_sn(merchantSn01).setPrimary_group_id("tonglian").setSecondary_group_id("lklV3");
        multiProviderEventMapper.insertSelective(event);

        handler.handleError(event, new ContextParamException("单元测试失败"));
        MultiProviderContractEvent result = multiProviderEventMapper.selectByPrimaryKey(event.getId());
        Assert.assertEquals(result.getStatus().intValue(), MultiProviderContractEvent.STATUS_BIZ_FAIL);
        Assert.assertTrue(event.getResult().contains("单元测试失败"));

        // 2 异常是其他
        String merchantSn02 = "handleErrorSn02";
        MultiProviderContractEvent event2 = new MultiProviderContractEvent()
                .setMerchant_sn(merchantSn02).setPrimary_group_id("tonglian").setSecondary_group_id("lklV3")
                .setVersion(0L);
        multiProviderEventMapper.insertSelective(event2);
        handler.handleError(event2, new RuntimeException("单元测试失败"));
        result = multiProviderEventMapper.selectByPrimaryKey(event2.getId());
        Assert.assertEquals(event2.getVersion().intValue(), result.getVersion().intValue());

    }

    /**
     * 1 有未完成的multi_event
     */
    @Test
    public void doHandle01() throws Exception {
        MultiProviderContractEvent event = new MultiProviderContractEvent()
                .setMerchant_sn("doHandle01").setPrimary_group_id("tonglian").setSecondary_group_id("lklV3")
                .setVersion(0L).setStatus(MultiProviderContractEvent.STATUS_PROCESS);
        multiProviderEventMapper.insertSelective(event);
        handler.doHandle(new MultiProviderContractEvent().setMerchant_sn("doHandle01"));
    }

    /**
     * 2 主次通道都有，而且命中了黑名单
     */
    @Test
    public void doHandle02() throws Exception {
        String merchantSn = "doHandle02";
        EventMsg eventMsg = new EventMsg()
                .setPlatform("crm")
                .setSource(CollectionUtil.hashMap(
                        "fail_memo", "黑名单校验不通过"
                ));
        MultiProviderContractEvent event = new MultiProviderContractEvent()
                .setMerchant_sn(merchantSn).setPrimary_group_id("tonglian").setSecondary_group_id("lklV3")
                .setVersion(0L).setStatus(MultiProviderContractEvent.STATUS_PENDING)
                .setEvent_msg(JSON.toJSONString(eventMsg));
        multiProviderEventMapper.insertSelective(event);

        Mockito.doReturn(new HashMap<String, Object>(1)).when(paramContextBiz).getNetInParamContextByMerchantSn(merchantSn);
        handler.doHandle(event);

        // event 两个task contract_status都是失败才对
        MultiProviderContractEvent result = multiProviderEventMapper.selectByPrimaryKey(event.getId());
        Assert.assertEquals(MultiProviderContractEvent.STATUS_BIZ_FAIL, result.getStatus().intValue());

        ContractTask primaryTask = contractTaskMapper.selectByPrimaryKey(result.getPrimary_task_id());
        Assert.assertEquals(TaskStatus.FAIL.getVal(), primaryTask.getStatus());

        ContractTask secondaryTask = contractTaskMapper.selectByPrimaryKey(result.getSecondary_task_id());
        Assert.assertEquals(TaskStatus.FAIL.getVal(), secondaryTask.getStatus());

        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        Assert.assertEquals(ContractStatus.STATUS_BIZ_FAIL, contractStatus.getStatus().intValue());

    }

    /**
     * 只有主通道，命中了黑名单
     */
    @Test
    public void doHandle03() throws Exception {
        String merchantSn = "doHandle03";
        EventMsg eventMsg = new EventMsg()
                .setPlatform("crm")
                .setSource(CollectionUtil.hashMap(
                        "fail_memo", "黑名单校验不通过"
                ));
        MultiProviderContractEvent event = new MultiProviderContractEvent()
                .setMerchant_sn(merchantSn).setPrimary_group_id("tonglian")
                .setVersion(0L).setStatus(MultiProviderContractEvent.STATUS_PENDING)
                .setEvent_msg(JSON.toJSONString(eventMsg));
        multiProviderEventMapper.insertSelective(event);

        Mockito.doReturn(new HashMap<String, Object>(1)).when(paramContextBiz).getNetInParamContextByMerchantSn(merchantSn);
        handler.doHandle(event);

        // event 一个task contract_status都是失败才对
        MultiProviderContractEvent result = multiProviderEventMapper.selectByPrimaryKey(event.getId());
        Assert.assertEquals(MultiProviderContractEvent.STATUS_BIZ_FAIL, result.getStatus().intValue());

        ContractTask primaryTask = contractTaskMapper.selectByPrimaryKey(result.getPrimary_task_id());
        Assert.assertEquals(TaskStatus.FAIL.getVal(), primaryTask.getStatus());

        Assert.assertNull(result.getSecondary_task_id());

        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        Assert.assertEquals(ContractStatus.STATUS_BIZ_FAIL, contractStatus.getStatus().intValue());

    }

    /**
     * 主次通道都有，没有命中黑名单
     * @throws Exception
     */
    @Test
    public void doHandle04() throws Exception {
        String merchantSn = "doHandle04";
        EventMsg eventMsg = new EventMsg()
                .setPlatform("crm");
        MultiProviderContractEvent event = new MultiProviderContractEvent()
                .setMerchant_sn(merchantSn).setPrimary_group_id("tonglian").setSecondary_group_id("lklV3")
                .setVersion(0L).setStatus(MultiProviderContractEvent.STATUS_PENDING)
                .setEvent_msg(JSON.toJSONString(eventMsg));
        multiProviderEventMapper.insertSelective(event);

        Mockito.doReturn(CollectionUtil.hashMap("merchant", CollectionUtil.hashMap("city", "上海市"))).when(paramContextBiz).getNetInParamContextByMerchantSn(merchantSn);

        handler.doHandle(event);

        // event.status =1 2个task.status=0 contract_status=1
        MultiProviderContractEvent result = multiProviderEventMapper.selectByPrimaryKey(event.getId());
        Assert.assertEquals(MultiProviderContractEvent.STATUS_PROCESS, result.getStatus().intValue());

        ContractTask primaryTask = contractTaskMapper.selectByPrimaryKey(result.getPrimary_task_id());
        Assert.assertEquals(TaskStatus.PENDING.getVal(), primaryTask.getStatus());
        Assert.assertEquals("tonglian", primaryTask.getRule_group_id());

        ContractTask secondaryTask = contractTaskMapper.selectByPrimaryKey(result.getSecondary_task_id());
        Assert.assertEquals(TaskStatus.PENDING.getVal(), secondaryTask.getStatus());
        Assert.assertEquals("lklV3", secondaryTask.getRule_group_id());

        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        Assert.assertEquals(ContractStatus.STATUS_PROCESS, contractStatus.getStatus().intValue());

    }

    /**
     * 驳回重新提交
     * 主次通道都有
     */
    @Test
    public void doHandle05() throws Exception {
        String merchantSn = "doHandle05";
        Map eventMsg = new HashMap<>(2);
        eventMsg.put("platform", "crm");
        eventMsg.put("eventType", 9);
        eventMsg.put("crmUpdate", JSON.parseArray("[{\"fields\":[\"legal_person_id_card_back_photo\",\"contact_name\",\"owner_name\",\"business\",\"business_license_photo\",\"name\",\"alias\",\"legal_person_id_card_front_photo\"],\"table_name\":\"merchant\"},{\"fields\":[\"letter_of_authorization\",\"bank_card_status\"],\"table_name\":\"merchant_bank_account\"},{\"fields\":[\"business_scope\",\"number\",\"address\",\"name\",\"photo\",\"validity\"],\"table_name\":\"merchant_business_license\"}]"));
        MultiProviderContractEvent event = new MultiProviderContractEvent()
                .setMerchant_sn(merchantSn).setPrimary_group_id("tonglian").setSecondary_group_id("lklV3")
                .setVersion(0L).setStatus(MultiProviderContractEvent.STATUS_PENDING)
                .setEvent_msg(JSON.toJSONString(eventMsg));
        multiProviderEventMapper.insertSelective(event);

        Mockito.doReturn(CollectionUtil.hashMap("merchant", CollectionUtil.hashMap("city", "上海市"))).when(paramContextBiz).getNetInParamContextByMerchantSn(merchantSn);

        handler.doHandle(event);

        // event.status =1 2个task.status=0 contract_status=1
        MultiProviderContractEvent result = multiProviderEventMapper.selectByPrimaryKey(event.getId());
        Assert.assertEquals(MultiProviderContractEvent.STATUS_PROCESS, result.getStatus().intValue());

        ContractTask primaryTask = contractTaskMapper.selectByPrimaryKey(result.getPrimary_task_id());
        Assert.assertEquals(TaskStatus.PENDING.getVal(), primaryTask.getStatus());
        Assert.assertEquals("tonglian", primaryTask.getRule_group_id());

        ContractTask secondaryTask = contractTaskMapper.selectByPrimaryKey(result.getSecondary_task_id());
        Assert.assertEquals(TaskStatus.PENDING.getVal(), secondaryTask.getStatus());
        Assert.assertEquals("lklV3", secondaryTask.getRule_group_id());

        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        Assert.assertEquals(ContractStatus.STATUS_PROCESS, contractStatus.getStatus().intValue());

    }

    /**
     * 只有主通道lklorg
     * @throws Exception
     */
    @Test
    public void doHandle06() throws Exception {
        String merchantSn = "doHandle06";
        EventMsg eventMsg = new EventMsg()
                .setPlatform("crm");
        MultiProviderContractEvent event = new MultiProviderContractEvent()
                .setMerchant_sn(merchantSn).setPrimary_group_id("lklorg").setSecondary_group_id(null)
                .setVersion(0L).setStatus(MultiProviderContractEvent.STATUS_PENDING)
                .setEvent_msg(JSON.toJSONString(eventMsg));
        multiProviderEventMapper.insertSelective(event);

        Mockito.doReturn(CollectionUtil.hashMap("merchant", CollectionUtil.hashMap("city", "上海市"))).when(paramContextBiz).getNetInParamContextByMerchantSn(merchantSn);

        handler.doHandle(event);

        // event.status =1 2个task.status=0 contract_status=1
        MultiProviderContractEvent result = multiProviderEventMapper.selectByPrimaryKey(event.getId());
        Assert.assertEquals(MultiProviderContractEvent.STATUS_PROCESS, result.getStatus().intValue());

        ContractTask primaryTask = contractTaskMapper.selectByPrimaryKey(result.getPrimary_task_id());
        Assert.assertEquals(TaskStatus.PENDING.getVal(), primaryTask.getStatus());
        Assert.assertEquals("lklorg", primaryTask.getRule_group_id());


        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        Assert.assertEquals(ContractStatus.STATUS_PROCESS, contractStatus.getStatus().intValue());

    }

    /**
     * 缺少照片
     * @throws Exception
     */
    @Test
    public void doHandle07() throws Exception {
        String merchantSn = "doHandle07";
        EventMsg eventMsg = new EventMsg()
                .setPlatform("crm");
        MultiProviderContractEvent event = new MultiProviderContractEvent()
                .setMerchant_sn(merchantSn).setPrimary_group_id("tonglian")
                .setVersion(0L).setStatus(MultiProviderContractEvent.STATUS_PENDING)
                .setEvent_msg(JSON.toJSONString(eventMsg));
        multiProviderEventMapper.insertSelective(event);

        Mockito.doReturn(new HashMap<String, Object>(1)).when(paramContextBiz).getNetInParamContextByMerchantSn(merchantSn);
        Mockito.doReturn(null).when(mcStoreExtService).findLastStoreExtAndPicturesByMerchantId(any());

        handler.doHandle(event);

        // event 一个task contract_status都是失败才对
        MultiProviderContractEvent result = multiProviderEventMapper.selectByPrimaryKey(event.getId());
        Assert.assertEquals(MultiProviderContractEvent.STATUS_PENDING, result.getStatus().intValue());
        Assert.assertTrue(result.getResult().contains("异地开户，待商家提交店铺照片"));
        Assert.assertEquals(ScheduleUtil.PAUSE_DATE, result.getUpdate_at());
    }
}
