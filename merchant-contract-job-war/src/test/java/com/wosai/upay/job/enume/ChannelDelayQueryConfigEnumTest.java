package com.wosai.upay.job.enume;

import com.wosai.common.utils.WosaiDateTimeUtils;
import com.wosai.upay.job.constant.CommonConstants;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.text.SimpleDateFormat;
import java.util.Date;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class ChannelDelayQueryConfigEnumTest {

    private long currentTimeMillis;

    @Before
    public void setUp() {
        currentTimeMillis = System.currentTimeMillis();
    }
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Test
    public void getNextPriority_PriorityASelected_ReturnsCorrectDate() {
        long timeMills = currentTimeMillis - 10 * 24 * 60 * 60 * 1000L; // 10天前
        Date expectedDate = new Date(currentTimeMillis + ChannelDelayQueryConfigEnum.DEFAULT.getPriorityA());
        Date actualDate = ChannelDelayQueryConfigEnum.getNextPriority(timeMills, "DEFAULT");
        assertEquals(DATE_FORMAT.format(expectedDate), DATE_FORMAT.format(actualDate));
    }

    @Test
    public void getNextPriority_PriorityBSelected_ReturnsCorrectDate() {
        long timeMills = currentTimeMillis - 20 * 24 * 60 * 60 * 1000L; // 20天前
        Date expectedDate = new Date(currentTimeMillis + ChannelDelayQueryConfigEnum.DEFAULT.getPriorityB());
        Date actualDate = ChannelDelayQueryConfigEnum.getNextPriority(timeMills, "DEFAULT");
        assertEquals(DATE_FORMAT.format(expectedDate), DATE_FORMAT.format(actualDate));
    }

    @Test
    public void getNextPriority_PriorityCSelected_ReturnsCorrectDate() {
        long timeMills = currentTimeMillis - 80 * 24 * 60 * 60 * 1000L; // 80天前
        Date expectedDate = new Date(currentTimeMillis + ChannelDelayQueryConfigEnum.DEFAULT.getPriorityC());
        Date actualDate = ChannelDelayQueryConfigEnum.getNextPriority(timeMills, "DEFAULT");
        assertEquals(DATE_FORMAT.format(expectedDate), DATE_FORMAT.format(actualDate));
    }

    @Test
    public void getNextPriority_NoPrioritySelected_ReturnsDefaultDate() {
        long timeMills = currentTimeMillis - 100 * 24 * 60 * 60 * 1000L; // 100天前
        Date actualDate = ChannelDelayQueryConfigEnum.getNextPriority(timeMills, "DEFAULT");
        assertEquals(CommonConstants.PAUSE_PRIORITY, DATE_FORMAT.format(actualDate));
    }
}