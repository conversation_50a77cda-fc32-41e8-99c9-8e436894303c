package com.wosai.upay.job.xxljob.direct;

import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ClearBlockingEventJobHandlerTest {

    @InjectMocks
    private ClearBlockingEventJobHandler clearBlockingEventJobHandler;

    @Mock
    private ContractEventMapper contractEventMapper;

    @Mock
    private SqlSessionFactory sqlSessionFactory;

    @Mock
    private SqlSession sqlSession;

    @Mock
    private ChatBotUtil chatBotUtil;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private JobThreadPoolExecutor directJobHandlerThreadPool;

    @Before
    public void setUp() {
        when(sqlSessionFactory.openSession(ExecutorType.BATCH, false)).thenReturn(sqlSession);
        when(sqlSession.getMapper(ContractEventMapper.class)).thenReturn(contractEventMapper);
    }

    @Test
    public void execute_NoEvents_ReturnsEarly() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setStartTime(1000L);
        param.setEndTime(2000L);

        when(contractEventMapper.selectForBlocking(anyString(), anyString(), anyInt())).thenReturn(Collections.emptyList());

        clearBlockingEventJobHandler.execute(param);

        verify(contractEventMapper, never()).updateCreateAt(anyLong(), anyString());
    }

    @Test
    public void execute_WithEvents_UpdatesEvents() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setStartTime(1000L);
        param.setEndTime(2000L);

        ContractEvent event = new ContractEvent();
        event.setId(1L);
        event.setCreate_at(new Date());

        when(contractEventMapper.selectForBlocking(anyString(), anyString(), anyInt())).thenReturn(Collections.singletonList(event));

        clearBlockingEventJobHandler.execute(param);

        verify(contractEventMapper, times(1)).updateCreateAt(eq(1L), anyString());
    }

    @Test
    public void execute_ExceptionOccurs_RollsBackTransaction() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setStartTime(1000L);
        param.setEndTime(2000L);

        ContractEvent event = new ContractEvent();
        event.setId(1L);
        event.setCreate_at(new Date());

        when(contractEventMapper.selectForBlocking(anyString(), anyString(), anyInt())).thenReturn(Collections.singletonList(event));
        doThrow(new RuntimeException("Test exception")).when(contractEventMapper).updateCreateAt(anyLong(), anyString());

        clearBlockingEventJobHandler.execute(param);

        verify(sqlSession, times(1)).rollback();
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
