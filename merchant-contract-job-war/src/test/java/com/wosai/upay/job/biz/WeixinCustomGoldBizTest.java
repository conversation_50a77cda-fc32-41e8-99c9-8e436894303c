package com.wosai.upay.job.biz;


import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.MerchantProviderParamsExtMapper;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExt;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.WeixinService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;


public class WeixinCustomGoldBizTest extends BaseTest {


    @SpyBean
    WeixinCustomGoldBiz weixinCustomGoldBiz;
    @MockBean
    MerchantProviderParamsExtMapper providerParamsExtMapper;
    @MockBean
    WeixinService weixinService;
    @MockBean
    RuleContext ruleContext;


    @Before
    public void before() {
        ReflectionTestUtils.setField(weixinCustomGoldBiz, "weixinService", weixinService);
    }


    @Test
    public void filtParams() {
        MerchantProviderParams params1 = new MerchantProviderParams().setId("test1");
        MerchantProviderParams params2 = new MerchantProviderParams().setId("test2");
        List<MerchantProviderParams> list = Arrays.asList(params1, params2);
        Mockito.doReturn(true).when(weixinCustomGoldBiz).extParamExist(Mockito.anyObject());
        List filt = weixinCustomGoldBiz.filtParams(list);
        Assert.assertEquals(0, filt.size());
        Mockito.doReturn(false).when(weixinCustomGoldBiz).extParamExist(Mockito.anyObject());
        List filt2 = weixinCustomGoldBiz.filtParams(list);
        Assert.assertEquals(2, filt2.size());

    }

    @Test
    public void extParamExist() {
        MerchantProviderParamsExt exist = new MerchantProviderParamsExt().setId(1L);
        Mockito.doReturn(exist).when(providerParamsExtMapper).getByParamId(Mockito.anyString(), Mockito.anyInt());
        Assert.assertTrue(weixinCustomGoldBiz.extParamExist(new MerchantProviderParams()));
        Mockito.doReturn(null).when(providerParamsExtMapper).getByParamId(Mockito.anyString(), Mockito.anyInt());
        Assert.assertFalse(weixinCustomGoldBiz.extParamExist(new MerchantProviderParams()));
    }

    @Test
    public void open() {
        ContractResponse goldRespones = new ContractResponse().setCode(400);
        Mockito.doReturn(goldRespones).when(weixinService).openGolden(Mockito.anyString(), Mockito.anyString());
        ContractResponse customPageResponse = new ContractResponse().setCode(500);
        Mockito.doReturn(customPageResponse).when(weixinService).openTicket(Mockito.anyString(), Mockito.anyString());
        Mockito.doReturn(new ContractChannel()).when(ruleContext).getContractChannel(Mockito.anyInt(), Mockito.anyString(), Mockito.anyString());
        MerchantProviderParams params = new MerchantProviderParams().setId("test").setProvider(1016).setChannel_no("test").setPayway(3);
        weixinCustomGoldBiz.open(params);
        goldRespones.setCode(200);
        customPageResponse.setCode(200);
        weixinCustomGoldBiz.open(params);
        customPageResponse.setCode(400);
        weixinCustomGoldBiz.open(params);

    }
}
