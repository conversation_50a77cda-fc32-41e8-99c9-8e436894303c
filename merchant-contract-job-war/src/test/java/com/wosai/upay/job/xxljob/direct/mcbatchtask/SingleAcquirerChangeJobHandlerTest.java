package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.shouqianba.workflow.service.CallBackService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.McBatchTaskMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.model.acquirer.ChangeAcquirerRequest;
import com.wosai.upay.job.model.changeAcquirerApprove.ChangeAcquirerApproveDTO;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SingleAcquirerChangeJobHandlerTest {

    @InjectMocks
    private SingleAcquirerChangeJobHandler singleAcquirerChangeJobHandler;

    @Mock
    private McBatchTaskMapper mcBatchTaskMapper;

    @Mock
    private AcquirerService acquirerService;

    @Mock
    private AcquirerChangeDao acquirerChangeDao;

    @Mock
    private MerchantService merchantService;

    @Mock
    private ContractStatusMapper contractStatusMapper;

    @Mock
    private BusinessLogBiz businessLogBiz;

    @Mock
    private CallBackService callBackService;

    @Mock
    private ErrorCodeManageBiz errorCodeManageBiz;

    @Before
    public void setUp() {
        // 设置模拟对象
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "SingleAcquirerChangeJobHandler";
        String actualLockKey = singleAcquirerChangeJobHandler.getLockKey();
        assertEquals("The lock key should match the expected value", expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_NormalExecution_TaskStatusUpdated() {
        // 准备
        McBatchTask mcBatchTask = new McBatchTask();
        mcBatchTask.setId(1);
        mcBatchTask.setPayload("{\"approveInfo\": {\"merchantSn\": \"123\", \"target\": \"targetAcquirer\", \"immediate\": true, \"tradeAppId\": \"appId\"}}");

        McAcquirerChange mcAcquirerChange = new McAcquirerChange();
        mcAcquirerChange.setId(10);

        when(mcBatchTaskMapper.selectByExampleWithBLOBs(any(McBatchTaskExample.class))).thenReturn(Arrays.asList(mcBatchTask));
        when(acquirerService.applyChangeAcquirer(any(ChangeAcquirerRequest.class))).thenReturn(true);
        when(acquirerChangeDao.getLatestUnFinishedApply(anyString())).thenReturn(mcAcquirerChange);
        when(contractStatusMapper.selectByMerchantSn("123")).thenReturn(new ContractStatus());
        DirectJobParam param = new DirectJobParam();
        singleAcquirerChangeJobHandler.execute(param);

        verify(mcBatchTaskMapper).updateByPrimaryKeySelective(any(McBatchTask.class));
    }

    @Test
    public void execute_ExceptionThrown_TaskStatusUpdated() {
        // 准备
        McBatchTask mcBatchTask = new McBatchTask();
        mcBatchTask.setId(1);
        mcBatchTask.setPayload("{\"approveInfo\":{\"applyType\":\"single\",\"auditSn\":\"SP365220250415000005\",\"callBackBean\":{\"auditId\":585431,\"message\":\"\",\"resultType\":1,\"templateId\":365009},\"immediate\":true,\"merchantSn\":\"21690003912894\",\"operator\":\"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\"operatorName\":\"公用测试账号\",\"reason\":\"1\",\"target\":\"haike\",\"tradeAppId\":\"1\"}}");

        when(mcBatchTaskMapper.selectByExampleWithBLOBs(any(McBatchTaskExample.class))).thenReturn(Arrays.asList(mcBatchTask));
        when(acquirerService.applyChangeAcquirer(any(ChangeAcquirerRequest.class))).thenThrow(new RuntimeException("Test Exception"));
        when(errorCodeManageBiz.getPromptMessageFromErrorCodeManager(anyString(), anyString(), anyString())).thenReturn(new ErrorInfo());
        DirectJobParam param = new DirectJobParam();
        singleAcquirerChangeJobHandler.execute(param);

        verify(mcBatchTaskMapper).updateByPrimaryKeySelective(any(McBatchTask.class));
    }
}
