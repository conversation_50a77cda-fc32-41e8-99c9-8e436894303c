package com.wosai.upay.job.mapper;

import com.google.common.collect.Lists;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


class ProviderTerminalTaskDynamicMapperTest extends BaseTest {
    @Autowired
    private ProviderTerminalTaskMapper providerTerminalTaskMapper;

    @Test
    void selectMerchantSnByPriorityAndType() {
        Date date = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String strDate = dateFormat.format(date);
        List<ProviderTerminalTask> terminalTasks = providerTerminalTaskMapper.selectMerchantSnByPriorityAndType("2022-04-28 18:00:00", strDate, ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType(), 100);
        System.out.println("ok");
    }
}