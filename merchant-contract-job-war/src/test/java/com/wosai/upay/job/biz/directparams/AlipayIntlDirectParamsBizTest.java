package com.wosai.upay.job.biz.directparams;


import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.directparams.AlipayIntlDirectParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

public class AlipayIntlDirectParamsBizTest extends BaseTest {

    @SpyBean
    AlipayIntlDirectParamsBiz alipayIntlDirectParamsBiz;

    @MockBean
    MerchantProviderParamsBiz merchantProviderParamsBiz;
    @MockBean
    TradeConfigService tradeConfigService;
    @MockBean
    SupportService supportService;
    @MockBean
    MerchantService merchantService;
    @MockBean
     MerchantProviderParamsMapper paramsMapper;


    @Before
    public void reflect() {
        ReflectionTestUtils.setField(alipayIntlDirectParamsBiz, "tradeConfigService", tradeConfigService);
        ReflectionTestUtils.setField(alipayIntlDirectParamsBiz, "supportService", supportService);
        ReflectionTestUtils.setField(alipayIntlDirectParamsBiz, "merchantService", merchantService);

        Mockito.doReturn(new HashMap<>()).when(tradeConfigService).getMerchantConfigByMerchantIdAndPayway(Mockito.anyString(), Mockito.anyInt());
        Map merchant = CollectionUtil.hashMap("merchant_id", "test_id", "sn", "sn");
        Mockito.doReturn(merchant).when(merchantService).getMerchant(Mockito.anyString());
    }

    @Test
    public void addDirectParams() {
        AlipayIntlDirectParams baseParams = new AlipayIntlDirectParams();
        baseParams.setMerchant_id("test_id");
        baseParams.setMerchant_sn("sn");
        AlipayIntlDirectParams.AlipayIntlParams params = new AlipayIntlDirectParams.AlipayIntlParams();
        params.setClient_id("client_id");
        baseParams.setAlipay_intl_trade_params(params);
        alipayIntlDirectParamsBiz.addDirectParams(baseParams);
    }

    @Test
    public void deleteDirectParams() {
        MerchantProviderParamsDto dto = new MerchantProviderParamsDto();
        dto.setMerchant_sn("sn");
        alipayIntlDirectParamsBiz.deleteDirectParams(dto, "3", "0.38");
    }

    @Test
    public void handleDirectParams() {
        alipayIntlDirectParamsBiz.handleDirectParams(new MerchantProviderParamsCustomDto());
    }
}
