package com.wosai.upay.job.xxljob.direct.antshop;

import com.wosai.upay.job.Constants.AntShopTaskConstant;
import com.wosai.upay.job.biz.AntShopBiz;
import com.wosai.upay.job.model.DO.AntShopTask;
import com.wosai.upay.job.model.PayParamsModel;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.merchant.contract.model.bluesea.AliMchLevel;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AntShopCreateJobHandlerTest {

    @InjectMocks
    private AntShopCreateJobHandler antShopCreateJobHandler;

    @Mock
    private AntShopBiz antShopBiz;

    @Mock
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Mock
    private BlueSeaService blueSeaService;

    @Mock
    private ChatBotUtil chatBotUtil;

    @Before
    public void setUp() {
        // 如果需要，可以在这里进行任何通用设置
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "AntShopCreateJobHandler";
        String actualLockKey = antShopCreateJobHandler.getLockKey();
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_FuyouProvider_CallsHandleAntMerchantExpandShopCreate() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());

        AntShopTask antShopTask = new AntShopTask();
        antShopTask.setAli_mch_id("12345");
        antShopTask.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);

        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setProvider(PayParamsModel.PROVIDER_FUYOU);

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong()))
                .thenReturn(Collections.singletonList(antShopTask));
        when(merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId("12345"))
                .thenReturn(Optional.of(merchantProviderParamsDO));

        antShopCreateJobHandler.execute(param);

        verify(antShopBiz).handleAntMerchantExpandShopCreate(antShopTask);
    }

    @Test
    public void execute_NonDirectTask_CallsUpdateReTry() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());

        AntShopTask antShopTask = new AntShopTask();
        antShopTask.setAli_mch_id("12345");
        antShopTask.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);
        antShopTask.setExtra("{\"isDirect\":false}");

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong()))
                .thenReturn(Collections.singletonList(antShopTask));
        when(merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId("12345"))
                .thenReturn(Optional.empty());

        AliMchLevel aliMchLevel = new AliMchLevel();
        aliMchLevel.setSuccess(false);

        when(blueSeaService.queryAlipayMchLevel("12345")).thenReturn(aliMchLevel);

        antShopCreateJobHandler.execute(param);

        verify(antShopBiz).updateReTry(antShopTask, null, null);
    }

    @Test
    public void execute_AliMchLevelQueryUnsuccessful_CallsUpdateReTry() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());

        AntShopTask antShopTask = new AntShopTask();
        antShopTask.setAli_mch_id("12345");
        antShopTask.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);
        antShopTask.setExtra("{\"isDirect\":false}");

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong()))
                .thenReturn(Collections.singletonList(antShopTask));
        when(merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId("12345"))
                .thenReturn(Optional.empty());

        AliMchLevel aliMchLevel = new AliMchLevel();
        aliMchLevel.setSuccess(true);
        aliMchLevel.setLevel("INDIRECT_LEVEL_M1");

        when(blueSeaService.queryAlipayMchLevel("12345")).thenReturn(aliMchLevel);

        antShopCreateJobHandler.execute(param);

        verify(antShopBiz).updateReTry(antShopTask, null, null);
    }

    @Test
    public void execute_ExceptionThrown_UpdatesTaskAndSendsMessage() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());

        AntShopTask antShopTask = new AntShopTask();
        antShopTask.setAli_mch_id("12345");
        antShopTask.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);
        antShopTask.setMerchant_sn("67890");

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong()))
                .thenReturn(Collections.singletonList(antShopTask));
        when(merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId("12345"))
                .thenReturn(Optional.empty());
        when(blueSeaService.queryAlipayMchLevel("12345")).thenReturn(null);

        antShopCreateJobHandler.execute(param);

        verify(antShopBiz).updateAntShopTask(antShopTask);
        verify(chatBotUtil).sendMessageToContractWarnChatBot(anyString());
    }
}
