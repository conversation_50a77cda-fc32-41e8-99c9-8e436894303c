package com.wosai.upay.job.service;

import cn.hutool.core.lang.Assert;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.acquirePos.FyT9HandleService;
import com.wosai.upay.job.biz.acquirePos.T9HandleFactory;
import com.wosai.upay.job.mapper.DirectStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.merchant.contract.model.LimitResp;
import com.wosai.upay.merchant.contract.model.provider.FuyouParam;
import com.wosai.upay.merchant.contract.service.FuyouService;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class T9ServiceTest extends BaseTest {

    @InjectMocks
    private T9ServiceImpl t9Service;

    @Mock
    private DirectStatusMapper directStatusMapper;
    @Mock
    private T9HandleFactory factory;
    @Spy
    private FyT9HandleService fyT9HandleService;
    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Mock
    private FuyouService fuyouService;
    @Mock
    private ContractParamsBiz contractParamsBiz;


    @Before
    public void setUp() {
        ReflectionTestUtils.setField(t9Service, "directStatusMapper", directStatusMapper);
        ReflectionTestUtils.setField(t9Service, "factory", factory);
        ReflectionTestUtils.setField(fyT9HandleService, "merchantProviderParamsMapper", merchantProviderParamsMapper);
        ReflectionTestUtils.setField(fyT9HandleService, "fuyouService", fuyouService);
        ReflectionTestUtils.setField(fyT9HandleService, "contractParamsBiz", contractParamsBiz);
    }

    @Test
    public void queryLimitTest() {
        List<DirectStatus> directStatusList = new ArrayList<>();
        DirectStatus directStatus = new DirectStatus();
        directStatus.setMerchant_sn("21690003135349");
        directStatus.setDev_code("dddd");
        directStatus.setStatus(DirectStatus.STATUS_SUCCESS);
        directStatusList.add(directStatus);
        Mockito.doReturn(directStatusList).when(directStatusMapper).getDirectByMerchantSn(Mockito.anyString());
        Mockito.doReturn(fyT9HandleService).when(factory).getAcquirePosServiceByDevCode(Mockito.anyString());
        Mockito.doReturn(new MerchantProviderParams().setProvider_merchant_id("tetstst")).when(merchantProviderParamsMapper).getAcquirerParamsByMerchantSnAndProvider(Mockito.anyString(), Mockito.anyInt());
        Mockito.doReturn(new FuyouParam()).when(contractParamsBiz).buildContractParams(Mockito.anyString(), Mockito.any(Class.class));
        Mockito.doReturn(Lists.newArrayList()).when(fuyouService).queryLimit(Mockito.anyString(), Mockito.any(FuyouParam.class));
        List<LimitResp> limitRespList = t9Service.queryLimit("21690003135349");
        Assert.isTrue(CollectionUtils.isEmpty(limitRespList));
    }


}
