package com.wosai.upay.job.service;


import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.BusinessRuleBiz;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractEvent;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;


public class SelfHelpNetInEventServiceTest extends BaseTest {

    @Autowired
    SelfHelpNetInEventService selfHelpNetInEventService;
    @MockBean
    BusinessRuleBiz businessRuleBiz;
    @MockBean
    private ContractEventMapper contractEventMapper;
    @MockBean
    private ContractEventService contractEventService;
    @MockBean
    private ContractStatusMapper contractStatusMapper;

    @Before
    public void before() {
        ReflectionTestUtils.setField(selfHelpNetInEventService, "contractEventService", contractEventService);
    }


    @Test
    public void selectSelfHelpNetInEventBymerchantSnAndtaskId() {
        selfHelpNetInEventService.selectSelfHelpNetInEventBymerchantSnAndtaskId(Mockito.anyString(), Mockito.anyInt());
    }

    @Test
    public void updateByPrimaryKeySelective() {
        selfHelpNetInEventService.updateByPrimaryKeySelective(new ContractEvent());
    }
}
