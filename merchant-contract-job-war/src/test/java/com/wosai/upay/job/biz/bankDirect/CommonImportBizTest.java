package com.wosai.upay.job.biz.bankDirect;

import com.alibaba.fastjson.JSON;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.util.CommonUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class CommonImportBizTest {

    @InjectMocks
    private CommonImportBiz commonImportBiz;

    @Test
    public void testBuildMerchantProviderParams() {
        Map config = JSON.parseObject("{\n" +
                "            \"channel_no\": \"cmbc\",\n" +
                "            \"contract_rule\": \"cmbc\",\n" +
                "            \"rule_group_id\": \"cmbc-${10}\",\n" +
                "            \"pay_merchant_id\": \"${2}\",\n" +
                "            \"provider_merchant_id\": \"${2}\",\n" +
                "            \"tradeParams\": {\n" +
                "                \"provider_mch_id\": \"${2}\"\n" +
                "            },\n" +
                "            \"extra\": {\n" +
                "                \"agentId\":\"${10}\"\n" +
                "            }\n" +
                "        }", Map.class);
        List<String> row = Arrays.asList("16888888888888", "1010", "41739115814MJUC", "669379270", "2088450150780262", "41739115814MJUC", "0.38", "490", "李若男", "1029", "A00002024110001196544");

        MerchantProviderParams params = commonImportBiz.buildMerchantProviderParams(row, config, "0");
        Map extra = CommonUtil.bytes2Map(params.getExtra());
        Assert.assertEquals("cmbc", params.getChannel_no());
        Assert.assertEquals("cmbc", params.getContract_rule());
        Assert.assertEquals("cmbc-A00002024110001196544", params.getRule_group_id());
        Assert.assertEquals("41739115814MJUC", params.getPay_merchant_id());
        Assert.assertEquals("41739115814MJUC", params.getProvider_merchant_id());

        Assert.assertEquals("A00002024110001196544", extra.get("agentId"));
        Assert.assertEquals("41739115814MJUC", BeanUtil.getPropString(extra, "tradeParams.provider_mch_id"));
    }

}