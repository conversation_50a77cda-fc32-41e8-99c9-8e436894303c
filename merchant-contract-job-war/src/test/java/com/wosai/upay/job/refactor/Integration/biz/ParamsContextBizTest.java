package com.wosai.upay.job.refactor.Integration.biz;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;

import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.model.ContractEvent;
import org.apache.commons.collections4.MapUtils;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

import static org.assertj.core.api.Assertions.assertThat;


/**
 * 参数上下文测试
 *
 * <AUTHOR>
 * @date 2024/3/14 16:59
 */
public class ParamsContextBizTest extends BaseTest {

    @Resource
    private ParamContextBiz paramContextBiz;

    @Resource
    private ContractEventMapper contractEventMapper;


    @Test
    public void testGetParamContextBySnAndEvent() {
        long eventId = 1111111111828954L;
        ContractEvent contractEvent = contractEventMapper.selectByPrimaryKey(eventId);
        Map<String, Object> context = paramContextBiz.getParamContextBySnAndEvent(contractEvent.getMerchant_sn(), contractEvent);
        assertThat(MapUtils.getMap(context, "merchant").get("name")).isEqualTo("吴彦祖");
        assertThat(MapUtils.getMap(context, "merchant").get("business_name")).isEqualTo("收钱吧一号");
    }

}
