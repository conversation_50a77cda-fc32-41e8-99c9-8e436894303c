package com.wosai.upay.job.xxljob.direct.contracttask;

import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ReactivateTaskJobHandlerTest {

    @InjectMocks
    private ReactivateTaskJobHandler reActivateTaskJobHandler;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private SqlSessionFactory sqlSessionFactory;

    @Mock
    private SqlSession sqlSession;

    @Mock
    private ChatBotUtil chatBotUtil;

    private DirectJobParam param;

    @Before
    public void setUp() {
        param = new DirectJobParam();
        param.setBatchSize(10);
        param.setEndTime(1000L);
        param.setStartTime(2000L);
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "ReactivateTaskJobHandler";
        String actualLockKey = reActivateTaskJobHandler.getLockKey();
        Assert.assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_NoTasksToReactivate_Returns() {
        when(contractTaskMapper.selectReactivateContractTaskTodo(anyString(), anyString(), anyInt())).thenReturn(new ArrayList<>());

        reActivateTaskJobHandler.execute(param);

        verify(contractTaskMapper, never()).updatePriority(anyString(), anyLong());
    }

    @Test
    public void execute_TasksToReactivate_UpdatesPriority() {
        List<ContractTask> tasks = new ArrayList<>();
        ContractTask task = new ContractTask();
        task.setId(1L);
        tasks.add(task);

        when(contractTaskMapper.selectReactivateContractTaskTodo(anyString(), anyString(), anyInt())).thenReturn(tasks);
        when(sqlSessionFactory.openSession(ExecutorType.BATCH, false)).thenReturn(sqlSession);
        when(sqlSession.getMapper(ContractTaskMapper.class)).thenReturn(contractTaskMapper);

        reActivateTaskJobHandler.execute(param);

        verify(contractTaskMapper, times(1)).updatePriority(anyString(), eq(1L));
        verify(sqlSession, times(1)).commit();
    }

    @Test
    public void execute_ExceptionOccurs_RollsBackTransactionAndSendsMessage() {
        List<ContractTask> tasks = new ArrayList<>();
        ContractTask task = new ContractTask();
        task.setId(1L);
        tasks.add(task);

        when(contractTaskMapper.selectReactivateContractTaskTodo(anyString(), anyString(), anyInt())).thenReturn(tasks);
        when(sqlSessionFactory.openSession(ExecutorType.BATCH, false)).thenReturn(sqlSession);
        when(sqlSession.getMapper(ContractTaskMapper.class)).thenReturn(contractTaskMapper);
        doThrow(new RuntimeException("Test Exception")).when(contractTaskMapper).updatePriority(anyString(), anyLong());

        reActivateTaskJobHandler.execute(param);

        verify(sqlSession, times(1)).rollback();
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
