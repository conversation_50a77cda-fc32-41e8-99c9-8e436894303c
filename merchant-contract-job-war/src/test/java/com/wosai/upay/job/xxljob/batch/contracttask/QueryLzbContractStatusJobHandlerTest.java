package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryLzbContractStatusJobHandlerTest {

    @InjectMocks
    private QueryLzbContractStatusJobHandler queryLzbContractStatusJobHandler;

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private QueryContractStatusHandler queryContractStatusHandler;

    @Mock
    private ChatBotUtil chatBotUtil;

    private ContractSubTask contractSubTask;
    private BatchJobParam param;
    private ContractTask contractTask;

    @Before
    public void setUp() {
        contractSubTask = new ContractSubTask();
        param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        contractTask = new ContractTask();
        contractTask.setId(2L);
    }

    @Test
    public void getLockKey_WithNonNullId_ReturnsCorrectLockKey() {
        contractSubTask.setId(123L);
        String expectedLockKey = "QueryLzbContractStatusJobHandler:123";
        String actualLockKey = queryLzbContractStatusJobHandler.getLockKey(contractSubTask);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void getLockKey_WithNullId_ReturnsLockKeyWithNull() {
        contractSubTask.setId(null);
        String expectedLockKey = "QueryLzbContractStatusJobHandler:null";
        String actualLockKey = queryLzbContractStatusJobHandler.getLockKey(contractSubTask);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsExpectedContractSubTaskList() {
        List<ContractSubTask> expectedTasks = Arrays.asList(
                new ContractSubTask().setId(1L),
                new ContractSubTask().setId(2L)
        );

        when(contractSubTaskMapper.selectLzbContractQueryTask(Mockito.anyInt(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(expectedTasks);

        List<ContractSubTask> actualTasks = queryLzbContractStatusJobHandler.queryTaskItems(param);

        assertEquals(expectedTasks, actualTasks);
    }

    @Test
    public void doHandleSingleData_SuccessStatus_NoFurtherProcessing() {
        contractSubTask.setStatus(TaskStatus.SUCCESS.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTask.getId())).thenReturn(contractSubTask);

        queryLzbContractStatusJobHandler.doHandleSingleData(contractSubTask);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTask.getId());
        verify(contractTaskMapper, never()).selectByPrimaryKey(anyLong());
        verify(queryContractStatusHandler, never()).doHandle(any(ContractTask.class), any(ContractSubTask.class));
    }

    @Test
    public void doHandleSingleData_FailStatus_NoFurtherProcessing() {
        contractSubTask.setStatus(TaskStatus.FAIL.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTask.getId())).thenReturn(contractSubTask);

        queryLzbContractStatusJobHandler.doHandleSingleData(contractSubTask);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTask.getId());
        verify(contractTaskMapper, never()).selectByPrimaryKey(anyLong());
        verify(queryContractStatusHandler, never()).doHandle(any(ContractTask.class), any(ContractSubTask.class));
    }

    @Test
    public void doHandleSingleData_ExceptionThrown_LogErrorAndSendWarning() {
        contractSubTask.setStatus(TaskStatus.PROGRESSING.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(contractSubTask.getId())).thenReturn(contractSubTask);
        when(contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id())).thenReturn(contractTask);
        doThrow(new RuntimeException("Test Exception")).when(queryContractStatusHandler).doHandle(any(ContractTask.class), any(ContractSubTask.class));

        queryLzbContractStatusJobHandler.doHandleSingleData(contractSubTask);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(contractSubTask.getId());
        verify(contractTaskMapper, times(1)).selectByPrimaryKey(contractSubTask.getP_task_id());
        verify(queryContractStatusHandler, times(1)).doHandle(any(ContractTask.class), any(ContractSubTask.class));
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
    }
}
