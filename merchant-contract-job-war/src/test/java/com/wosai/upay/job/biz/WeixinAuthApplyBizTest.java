package com.wosai.upay.job.biz;


import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MchAuthApplyMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.MchAuthApply;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;


//public class WeixinAuthApplyBizTest extends BaseTest {
@RunWith(SpringRunner.class)
@SpringBootTest
public class WeixinAuthApplyBizTest{

    @Autowired
    WeixinAuthApplyBiz weixinAuthApplyBiz;
//    @MockBean
    @Autowired
    MchAuthApplyMapper mchAuthApplyMapper;

    @Autowired
    ContractTaskMapper contractTaskMapper;

}
