package com.wosai.upay.job.controller;

import com.alibaba.fastjson.JSON;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TonglianV2ControllerTest {

    @Autowired
    TongLianV2Controller controller;

    @Test
    public void contractCallBack(){
        String back = "{\n" +
                "    \"orgid\":\"55058100780004S\",\n" +
                "    \"cusid\":\"21690003571525\",\n" +
                "    \"appid\":\"00000005\",\n" +
                "    \"notifytype\":\"editmerchant\",\n" +
                "    \"merchantid\":\"21690003571525\",\n" +
                "    \"sign\":\"dOVMkbKHXmmDtMR3ZLVQQTSPmclGw6Om1yrukG71XkOntrTUBrFfirgU1N+TKEHAx5W9VZW0qeFqAJv0R5ZNsm+KxM40jPuhUdVxl1PKm1IoSoNgHlQVxEah21Q4/y3LHvkINq19yziPiVQow4iToyAamsJ7c6o7s6sSQ/vA6Uc=\",\n" +
                "    \"edittype\":\"clear\",\n" +
                "    \"auditstatus\":\"02\"\n" +
                "}";
//        String back = "{\n" +
//                "            \"orgid\":\"55058100780004S\",\n" +
//                "            \"cusid\":\"55058100780004S\",\n" +
//                "            \"appid\":\"00000005\",\n" +
//                "            \"notifytype\":\"addmerchant\",\n" +
//                "            \"merchantid\":\"21690003571497\",\n" +
//                "            \"sign\":\"dOVMkbKHXmmDtMR3ZLVQQTSPmclGw6Om1yrukG71XkOntrTUBrFfirgU1N+TKEHAx5W9VZW0qeFqAJv0R5ZNsm+KxM40jPuhUdVxl1PKm1IoSoNgHlQVxEah21Q4/y3LHvkINq19yziPiVQow4iToyAamsJ7c6o7s6sSQ/vA6Uc=\",\n" +
//                "            \"reqid\":\"277192666797795\",\n" +
//                "            \"auditstatus\":\"01\",\n" +
//                "            \"callback_time\":1687750594503\n" +
//                "        }";
        Map backMsg = JSON.parseObject(back, Map.class);
//        controller.contractCallBack(backMsg);
    }
}
