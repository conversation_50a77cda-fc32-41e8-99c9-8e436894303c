package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.xxljob.model.DirectExecTypeEnum;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class BankDirectContractStatusJobHandlerNewTest extends BaseTest {

    @Autowired
    private BankDirectContractStatusJobHandler bankDirectContractStatusJobHandler;

    @Test
    public void executeTest() {
        DirectJobParam param = new DirectJobParam();
        param.setExecType(DirectExecTypeEnum.ASYNC);
        param.setBatchSize(500);
        param.setQueryTime(5184000000L);
        bankDirectContractStatusJobHandler.execute(param);
    }
}