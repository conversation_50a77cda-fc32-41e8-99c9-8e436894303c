package com.wosai.upay.job.controller;

import java.util.HashMap;
import java.util.Map;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.upay.job.BaseTest;

public class BcsCallbackControllerTest extends BaseTest {

    @Autowired
    private BcsCallbackController bcsCallbackController;

    @Test
    public void contractCallbackTest() {
        String content =
            "{\"bizContent\":\"{\\\"address\\\":\\\"湖南省长沙市\\\",\\\"bizType\\\":\\\"entry\\\",\\\"brief\\\":\\\"湖南测试包装有限公司API\\\",\\\"busiLiceNo\\\":\\\"91430102MA4Q7UW52A\\\",\\\"contactName\\\":\\\"造数阎州\\\",\\\"contactPhone\\\":\\\"13268701427\\\",\\\"legalCerNo\\\":\\\"231024200008207956\\\",\\\"legalCerType\\\":\\\"1111\\\",\\\"legalName\\\":\\\"造数阎州\\\",\\\"legalPhone\\\":\\\"13633326796\\\",\\\"mchtNo\\\":\\\"20250801461430100002QAW02320000\\\",\\\"name\\\":\\\"湖南测试包装有限公司\\\",\\\"reserved1\\\":\\\"\\\",\\\"reserved2\\\":\\\"\\\",\\\"reserved3\\\":\\\"\\\",\\\"reserved4\\\":\\\"\\\",\\\"reserved5\\\":\\\"\\\",\\\"returnCode\\\":\\\"********\\\",\\\"returnMsg\\\":\\\"处理完成\\\",\\\"settleCycle\\\":\\\"D\\\",\\\"settleInterval\\\":1,\\\"status\\\":\\\"PASS\\\",\\\"tradeNo\\\":\\\"20250121461430155112PID00000001\\\"}\"}";
        Map<String, String> headers = new HashMap<>();
        headers.put("x-aob-appID","xsda");
        headers.put("x-aob-bankID","BCS");
        bcsCallbackController.contractCallback(content, headers);
    }
}