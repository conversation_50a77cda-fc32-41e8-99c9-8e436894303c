CREATE TABLE `mc_acquirer_change` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `apply_id` varchar(37) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '申请id，唯一标志',
  `merchant_sn` varchar(40) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '商户sn',
  `merchant_id` varchar(37) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '商户id',
  `source_acquirer` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '原收单机构',
  `target_acquirer` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '目标收单机构',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态 1：已提交 2：已发起报备 3：报备成功 5：已关闭交易权限  7：已发起提现  9：切换交易参数成功  11：已打开交易权限  19：切换成功  20：切换失败',
  `memo` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '提示语',
  `process` text COLLATE utf8mb4_bin COMMENT '状态变更过程',
  `extra` text COLLATE utf8mb4_bin COMMENT '额外信息',
  `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_mch_sn` (`merchant_sn`),
  KEY `idx_create_at` (`create_at`),
  KEY `idx_apply_id` (`apply_id`),
  KEY `idx_update_at` (`update_at`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4  COMMENT='商户切换收单机构记录表';

CREATE TABLE `contract_status` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_sn` varchar(40) NOT NULL DEFAULT '' COMMENT '商户sn',
  `status` int(2) DEFAULT '0' COMMENT '0待处理 1.处理中 2处理成功  3处理失败',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) DEFAULT '0' COMMENT '版本号 每次数据有更新+1',
  `acquirer` varchar(64) NOT NULL DEFAULT 'lkl' COMMENT '收单机构',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_merchant_sn` (`merchant_sn`),
  KEY `idx_create_at` (`create_at`),
  KEY `idx_update_at` (`update_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2491573 DEFAULT CHARSET=utf8 COMMENT='报备总状态表';
CREATE TABLE `mc_contract_rule` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `rule` varchar(128) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '报备规则唯一标识',
  `name` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '规则名称',
  `payway` int(11) DEFAULT NULL COMMENT '支付源',
  `provider` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '结算通道',
  `acquirer` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收单机构',
  `channel` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '报备渠道',
  `status` int(11) NOT NULL COMMENT '0禁用  1启用',
  `type` int(11) NOT NULL DEFAULT '2' COMMENT '1收单机构报备  2结算通道报备',
  `metadata` text COLLATE utf8mb4_bin COMMENT '报备参数',
  `retry` int(11) DEFAULT '0' COMMENT '系统异常后重试次数',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认通道',
  `is_insert` tinyint(1) DEFAULT '1' COMMENT '是否新增',
  `is_insert_influ_ptask` tinyint(1) DEFAULT '0' COMMENT '新增是否影响总任务状态',
  `is_update` tinyint(1) DEFAULT '0' COMMENT '是否更新',
  `is_update_influ_ptask` tinyint(1) DEFAULT '0' COMMENT '更新是否影响总任务状态',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_rule` (`rule`)
) ENGINE=InnoDB AUTO_INCREMENT=1666 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='报备规则表';
CREATE TABLE `mc_channel` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `channel` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '渠道唯一标识',
  `name` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '渠道名称',
  `subject` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '渠道主体',
  `payway` int(11) NOT NULL COMMENT '支付源',
  `payway_channel_no` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '支付源渠道号',
  `channel_no` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '渠道号（可能是结算通道渠道号也可能是支付源渠道号）',
  `private_key` text COLLATE utf8mb4_bin COMMENT '渠道秘钥',
  `provider` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '结算通道',
  `provider_metadata` text COLLATE utf8mb4_bin COMMENT '结算通道参数',
  `acquirer` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '收单机构',
  `acquirer_metadata` text COLLATE utf8mb4_bin COMMENT '收单机构参数',
  `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_channel` (`channel`)
) ENGINE=InnoDB AUTO_INCREMENT=1000021 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='报备渠道表';
CREATE TABLE `mc_rule_group` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '唯一标识',
  `name` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '规则组名称',
  `vendor` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '业务方',
  `vendor_app` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '业务方应用',
  `status` int(11) DEFAULT '0' COMMENT '0禁用  1启用',
  `rules` text COLLATE utf8mb4_bin COMMENT '规则列表',
  `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `acquirer` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '收单机构',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1363 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='报备规则组';
CREATE TABLE `pay_for_task` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `sub_task_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'SUB_TASK任务ID',
  `merchant_sn` varchar(40) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '商户sn ',
  `hash_req` int(20) NOT NULL DEFAULT '0' COMMENT '请求字段哈希',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) DEFAULT '0' COMMENT '版本号 每次数据有更新+1',
  `context_param` text COLLATE utf8mb4_bin COMMENT '处理这个事件产生的商户上下文参数',
  `status` int(2) DEFAULT '0' COMMENT '0待提交代付处理 1.已提交代付代付处理中 2处理成功  3处理失败4代付成功待验证金额',
  `request_param` text COLLATE utf8mb4_bin COMMENT '请求参数',
  `response` text COLLATE utf8mb4_bin COMMENT '响应',
  `submit_remit_order_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代付交易请求ID',
  `request_flow_no` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求流水',
  PRIMARY KEY (`id`),
  KEY `idx_sn` (`merchant_sn`),
  KEY `idx_create_at` (`create_at`),
  KEY `idx_submit_remit_order_id` (`submit_remit_order_id`),
  KEY `idx_request_flow_no` (`request_flow_no`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=81930 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='代付交易';

CREATE TABLE `contract_sub_task` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `p_task_id` bigint(20) unsigned NOT NULL COMMENT '父任务编号',
  `merchant_sn` varchar(40) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '商户sn 方便查询',
  `channel` varchar(40) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '进件通道 lkl,union_wanma,union_common 等等',
  `default_channel` int(2) DEFAULT NULL COMMENT '是否是默认通道  0否 1是',
  `change_config` int(2) DEFAULT NULL COMMENT '成功后是否切通道 0否 1是',
  `change_body` int(2) DEFAULT NULL COMMENT '切通道时 必要参数',
  `task_type` int(2) NOT NULL DEFAULT '-1' COMMENT '0基本信息变更 1商户状态 2结算账户变更 3费率更新 4银行卡更新 5进件 6更新',
  `contract_id` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '进件通道 外部唯一编号 异步回调时可能需要 可以为空',
  `payway` int(2) DEFAULT NULL COMMENT '进件支付方式 可以为空(例 进件拉卡拉时为空)',
  `request_body` text COLLATE utf8mb4_bin COMMENT '此任务的请求body',
  `response_body` text COLLATE utf8mb4_bin COMMENT '此任务的响应body',
  `schedule_status` int(2) DEFAULT '0' COMMENT '是否可调度 0否 1可调度',
  `schedule_dep_task_id` bigint(20) DEFAULT '0' COMMENT '依赖的任务id 不可调度时 必须',
  `status` int(2) DEFAULT '0' COMMENT '处理状态 0待处理 1处理中 4系统异常失败 5处理成功  6处理失败',
  `status_influ_p_task` int(2) DEFAULT '0' COMMENT '失败是否影响主流程 0否 1是',
  `priority` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `result` text COLLATE utf8mb4_bin COMMENT '备注 用于页面上展示',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) DEFAULT '0',
  `contract_rule` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '报备规则',
  `rule_group_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '报备规则组',
  `retry` int(11) NOT NULL DEFAULT '0' COMMENT '已重试次数',
  PRIMARY KEY (`id`),
  KEY `sn` (`merchant_sn`),
  KEY `idx_status` (`status`),
  KEY `idx_p_task_id` (`p_task_id`),
  KEY `idx_channel` (`channel`),
  KEY `idx_schedule_dep_task_id` (`schedule_dep_task_id`),
  KEY `idx_contract_sub_task_contract_id` (`contract_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4592497 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='子任务表';
CREATE TABLE `contract_task` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_sn` varchar(40) NOT NULL DEFAULT '' COMMENT '商户sn',
  `merchant_name` varchar(40) NOT NULL DEFAULT '' COMMENT '商户名称',
  `type` varchar(40) NOT NULL DEFAULT '' COMMENT '新增商户入网,基本信息变更,结算账户变更,附件上传,费率变更',
  `event_msg` text COMMENT '处理这个事件的消息内容 可以为空',
  `event_context` text COMMENT '处理这个事件产生的商户上下文参数',
  `status` int(2) DEFAULT '0' COMMENT '0待处理 1.处理中 4被终止 5处理成功  6处理失败',
  `affect_sub_task_count` int(2) NOT NULL COMMENT '影响主任务成功的子任务总数量',
  `affect_status_success_task_count` int(2) NOT NULL COMMENT '已经成功的 影响主任务成功的子任务数量',
  `result` text COMMENT '{"channel":"","message":""} 成功时 目前不需要存 失败时需要存失败的通道信息和原因',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) DEFAULT '0' COMMENT '版本号 每次数据有更新+1',
  `priority` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `complete_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `rule_group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '报备规则组',
  PRIMARY KEY (`id`),
  KEY `idx_create_status` (`create_at`,`status`),
  KEY `idx_priority_status` (`priority`,`status`),
  KEY `idx_sn_status` (`merchant_sn`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2573819 DEFAULT CHARSET=utf8 COMMENT='总任务表';

CREATE TABLE `merchant_provider_params` (
  `id` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '主键',
  `merchant_sn` varchar(36) COLLATE utf8mb4_bin NOT NULL COMMENT '收钱吧商户号',
  `out_merchant_sn` varchar(56) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '报备通道的时候实际传的外部商户号',
  `channel_no` varchar(36) COLLATE utf8mb4_bin NOT NULL COMMENT '银行通道渠道号',
  `parent_merchant_id` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '银行通道渠道大商户号',
  `provider` int(10) DEFAULT '0' COMMENT '交易通道服务提供方',
  `provider_merchant_id` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '渠道商户号',
  `payway` int(10) DEFAULT '0' COMMENT '支付方式',
  `params_config_status` int(10) DEFAULT '0' COMMENT '微信参数配置标示 0:未配置, 1:配置成功 2:不需要配置',
  `pay_merchant_id` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '支付源商户号',
  `weixin_sub_appid` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '银行通道商户号/微信商户号对应的微信公众号APP ID',
  `weixin_subscribe_appid` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信推荐关注公众号',
  `weixin_sub_mini_appid` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信小程序appid',
  `weixin_receipt_appid` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信推荐关注小程序appid',
  `status` int(10) DEFAULT '0' COMMENT '是否被使用 0:未被使用 1:已使用',
  `extra` blob COMMENT '额外记录一些返回值，以备不时之需',
  `ctime` bigint(20) DEFAULT NULL COMMENT '生成时间',
  `mtime` bigint(20) DEFAULT NULL COMMENT '修改时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `version` bigint(20) unsigned NOT NULL DEFAULT '1' COMMENT '版本号',
  `contract_rule` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '报备规则',
  `rule_group_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '报备规则组',
  `update_status` int(11) NOT NULL DEFAULT '-1' COMMENT '-1未知 0失败  1成功',
  `auth_status` int(10) DEFAULT '0' COMMENT '授权状态 0未授权 1已授权',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_provider_params` (`channel_no`,`pay_merchant_id`,`weixin_sub_appid`,`weixin_receipt_appid`,`weixin_sub_mini_appid`,`weixin_subscribe_appid`),
  KEY `idx_merchant_sn` (`merchant_sn`),
  KEY `idx_ctime` (`ctime`),
  KEY `idx_pay_merchant_id` (`pay_merchant_id`),
  KEY `idx_provider_merchant_id` (`provider_merchant_id`),
  KEY `index_mtime` (`mtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='商户报备获取的银行交易参数存储表(new)';

CREATE TABLE `merchant_provider_params_ext` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `param_id` varchar(40) NOT NULL DEFAULT '' COMMENT '交易参数id',
  `type` int(10) NOT NULL COMMENT '扩展字段业务类型  1 芝麻信用入住',
  `ext_field_1` varchar(200) DEFAULT '0' COMMENT '业务字段1',
  `ext_field_2` varchar(200) DEFAULT '0' COMMENT '业务字段2',
  `extra` text COMMENT 'json 额外存储',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) unsigned NOT NULL DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_param_id` (`param_id`),
  KEY `idx_ext_field_1` (`ext_field_1`),
  KEY `idx_ext_field_2` (`ext_field_2`),
  KEY `idx_type_ext_1` (`type`,`ext_field_1`),
  KEY `create_at` (`create_at`)
) ENGINE=InnoDB AUTO_INCREMENT=28076 DEFAULT CHARSET=utf8 COMMENT='商户交易参数扩展信息表';

CREATE TABLE `contract_event_log` (
  `seq` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `ts` bigint(20) NOT NULL COMMENT '时间戳',
  `event` blob NOT NULL COMMENT '事件消息体，序列化的JSON对象字符串',
  PRIMARY KEY (`seq`)
) ENGINE=InnoDB AUTO_INCREMENT=1264221 DEFAULT CHARSET=utf8 COMMENT='商户进件事件日志表';

CREATE TABLE `contract_opinion_event_log` (
  `seq` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `ts` bigint(20) NOT NULL COMMENT '时间戳',
  `event` blob NOT NULL COMMENT '事件消息体，序列化的JSON对象字符串',
  PRIMARY KEY (`seq`)
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8 COMMENT='处理意见';

CREATE TABLE `weixin_auth_event_log` (
  `seq` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `ts` bigint(20) NOT NULL COMMENT '时间戳',
  `event` blob COMMENT '事件消息体，序列化的JSON对象字符串',
  PRIMARY KEY (`seq`)
) ENGINE=InnoDB AUTO_INCREMENT=18853 DEFAULT CHARSET=utf8 COMMENT='微信升级授权日志表';

CREATE TABLE `direct_status` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_sn` varchar(40) NOT NULL COMMENT '商户号',
  `dev_code` varchar(16) NOT NULL COMMENT '直连应用类型',
  `status` int(2) DEFAULT '0' COMMENT '0待处理 1.处理中 2处理成功  3处理失败',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_merchant_sn_dev_code` (`merchant_sn`,`dev_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='直连申请状态表';

CREATE TABLE `ali_direct_apply` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_sn` varchar(40) NOT NULL DEFAULT '' COMMENT '商户号',
  `task_id` bigint(20) DEFAULT NULL COMMENT '报备任务主键',
  `batch_no` varchar(30) DEFAULT NULL COMMENT '外部返回的申请单编号',
  `status` int(2) DEFAULT '0' COMMENT '申请单状态0未提交10待提交 15已提交 20审核中40待签约60已完成70已驳回',
  `request_body` text COMMENT '申请单请求报文',
  `response_body` text COMMENT '申请单响应报文',
  `sign_url` varchar(256) DEFAULT NULL COMMENT '返回的签约链接',
  `result` varchar(256) DEFAULT NULL COMMENT '申请返回文案',
  `form_body` text COMMENT '本次提交的业务字段',
  `user_id` varchar(30) DEFAULT NULL COMMENT '支付宝user_id',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `priority` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_sn` (`merchant_sn`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_at_status` (`create_at`,`status`),
  KEY `idx_priority_status` (`priority`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='支付宝直连申请单表';
CREATE TABLE `weixin_direct_apply` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_sn` varchar(40) NOT NULL DEFAULT '' COMMENT '商户号',
  `task_id` bigint(20) DEFAULT NULL COMMENT '报备任务主键',
  `dev_code` varchar(16) DEFAULT NULL COMMENT '申请的应用类型',
  `submit_type` int(2) DEFAULT '1' COMMENT '提交类型 1CRM 2人工 3绑定已有商户',
  `status` int(2) DEFAULT '0' COMMENT '申请单状态0未提交10运营审核中20审核中30待账户验证40待签约50开通权限中60成功70驳回',
  `request_body` text COMMENT '申请单请求报文',
  `response_body` text COMMENT '申请单响应报文',
  `sign_url` varchar(256) DEFAULT NULL COMMENT '返回的签约或认证链接',
  `result` varchar(256) DEFAULT NULL COMMENT '申请返回文案',
  `qrcode_data` text COMMENT '渲染后二维码',
  `form_body` text COMMENT '本次提交的业务信息',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_sn` (`merchant_sn`),
  KEY `idx_create_at_status` (`create_at`,`status`),
  KEY `idx_update_at` (`update_at`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='微信直连申请单表';
CREATE TABLE `ccb_decp_merchant` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
 `merchant_sn` varchar(40) COLLATE utf8mb4_bin NOT NULL COMMENT '商户sn',
 `status` int(11) NOT NULL COMMENT '状态 2:开通成功  3:开通失败',
 `associated_success_sn` varchar(40) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关联其他成功的商户号,该商户虽然失败了但是可能相同证件号的其他商户成功了',
 `activated` int(2) DEFAULT '0' COMMENT '是否已经激活，0未激活 1已激活，默认0',
 `submitted` int(2) DEFAULT '0' COMMENT '商户是否自助提交过，0未提交过 1提交过 默认0',
 `request_body` text COLLATE utf8mb4_bin COMMENT '请求参数',
 `response_body` text COLLATE utf8mb4_bin COMMENT '响应结果',
 `result` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '失败原因',
 `ctime` bigint(20) DEFAULT NULL COMMENT '生成时间',
 `mtime` bigint(20) DEFAULT NULL COMMENT '修改时间',
 `version` bigint(20) DEFAULT '0' COMMENT '版本号 每次数据有更新+1',
 PRIMARY KEY (`id`),
 KEY `idx_merchant_sn` (`merchant_sn`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='商户批量开通建行数字货币';
CREATE TABLE `multi_provider_contract_event` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `merchant_sn` varchar(40) NOT NULL DEFAULT '' COMMENT '商户sn',
 `primary_task_id` bigint(20) DEFAULT NULL COMMENT '事件对应主通道进件任务id',
 `secondary_task_id` bigint(20) DEFAULT NULL COMMENT '事件对应次通道进件任务id',
 `event_msg` text COMMENT '处理这个事件的消息内容,这里只记录黑名单的东西而已',
 `status` int(2) DEFAULT '0' COMMENT '0待处理 1.处理中 5进件成功  6进件失败',
 `result` text COMMENT '结果',
 `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
 `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 `version` bigint(20) DEFAULT '0' COMMENT '版本号 每次数据有更新+1',
 `primary_group_id` varchar(20) DEFAULT NULL COMMENT '事件对应主通道报备规则组',
 `secondary_group_id` varchar(20) DEFAULT NULL COMMENT '事件对应次通道报备规则组',
 PRIMARY KEY (`id`),
 KEY `idx_merchant_sn` (`merchant_sn`),
 KEY `idx_primary_task_id` (`primary_task_id`),
 KEY `idx_secondary_task_id` (`secondary_task_id`),
 KEY `idx_create_status` (`create_at`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='多通道进件事件表';
CREATE TABLE `contract_event` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` bigint(20) DEFAULT NULL COMMENT '事件对应任务编号',
  `merchant_sn` varchar(40) NOT NULL DEFAULT '' COMMENT '商户sn',
  `event_type` int(2) DEFAULT '0' COMMENT '0 商户基本信息变更  1 结算账户信息变更 2 商户费率信息更新  3 附件上传 4 入网  9入网失败后重新入网',
  `event_msg` text COMMENT '处理这个事件的消息内容  dts变更需要有固定格式 {"tb_name":"","opt_type":"","msg":""}',
  `status` int(2) DEFAULT '0' COMMENT '0待处理 1.处理中 5处理成功  6处理失败',
  `result` text COMMENT '结果',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) DEFAULT '0' COMMENT '版本号 每次数据有更新+1',
  `rule_group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '报备规则组',
  `retry` int(11) DEFAULT '0' COMMENT '重试次数',
  `priority` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '下次执行时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_create_at_status` (`create_at`,`status`),
  KEY `idx_merchant_sn_status` (`merchant_sn`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1111111111404630 DEFAULT CHARSET=utf8 COMMENT='事件表';
CREATE TABLE `payway_config_change` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                        `merchant_sn` varchar(40) NOT NULL DEFAULT '' COMMENT '商户sn',
                                        `payway` int(2) NOT NULL COMMENT '支付方式',
                                        `channel` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '进件通道 lkl,union_wanma,union_common 等等',
                                        `body` text NOT NULL COMMENT '切换参数',
                                        `status` int(2) DEFAULT '0' COMMENT '0待处理 1.处理中 2处理成功  6处理失败',
                                        `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                        `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                        `version` bigint(20) DEFAULT '0' COMMENT '版本号 每次数据有更新+1',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_merchant_sn` (`merchant_sn`),
                                        KEY `idx_create_at` (`create_at`),
                                        KEY `idx_update_at` (`update_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1438185 DEFAULT CHARSET=utf8 COMMENT='交易参数切换任务';
CREATE TABLE `self_open_ccb_decp` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `merchant_sn` varchar(40) COLLATE utf8mb4_bin NOT NULL COMMENT '商户sn',
  `open_status` int(2) NOT NULL COMMENT '数币开通状态 1审核中 2开通成功 3开通失败',
  `decp_id` bigint(20) DEFAULT NULL COMMENT '关联的开通表数据',
  `request_body` text COLLATE utf8mb4_bin COMMENT '请求参数',
  `result` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '开通结果',
  `ctime` bigint(20) DEFAULT NULL COMMENT '生成时间',
  `mtime` bigint(20) DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_merchant_sn` (`merchant_sn`),
  KEY `idx_mtime` (`mtime`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='商户自助开通建行数字货币';
CREATE TABLE `open_ccb_decp` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
 `identity` varchar(60)  NOT NULL DEFAULT '' COMMENT '证件号',
 `number` varchar(60)  NOT NULL DEFAULT '' COMMENT '营业执照号(小微是证件号)',
 `status` int(2) NOT NULL COMMENT '开通状态 2开通成功 3开通失败',
 `request_body` text COMMENT '请求参数',
 `response_body` text COMMENT '响应结果',
 `ctime` bigint(20) DEFAULT NULL COMMENT '生成时间',
 `mtime` bigint(20) DEFAULT NULL COMMENT '修改时间',
 `version` bigint(20) DEFAULT '0' COMMENT '版本号 每次数据有更新+1',
 PRIMARY KEY (`id`),
 KEY `idx_identity_number` (`identity`,`number`),
 KEY `idx_mtime` (`mtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='根据营业执照号和证件号开通建行数字货币';
CREATE TABLE `ccb_config` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `district_code` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '地区code',
  `private_min_price` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '结算底价(对私账户)',
  `public_min_price` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '结算底价(对公账户)',
  `ins_no` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '管辖机构行号',
  `account` varchar(40) COLLATE utf8mb4_bin NOT NULL COMMENT '分润结算账号',
  `ctime` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `mtime` bigint(20) DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) DEFAULT '0' COMMENT '版本号 每次数据有更新+1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='建行结算底价配置表';
CREATE TABLE `ccb_config_change_history` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
 `ccb_config_id` bigint(20) NOT NULL COMMENT '结算底价配置id',
 `op_type` int(2) NOT NULL COMMENT '操作类型 1创建 2更新 3删除',
 `private_min_price` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '结算底价(对私账户)',
 `public_min_price` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '结算底价(对公账户)',
 `ins_no` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '管辖机构行号',
 `account` varchar(40) COLLATE utf8mb4_bin NOT NULL COMMENT '分润结算账号',
 `operator` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '操作人',
 `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
 PRIMARY KEY (`id`),
 KEY `idx_ccb_config_id` (`ccb_config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='建行结算底价变更历史表';
CREATE TABLE `bank_direct_apply` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
 `merchant_sn` varchar(40) NOT NULL DEFAULT '' COMMENT '商户号',
 `task_id` bigint(20) DEFAULT NULL COMMENT '报备任务主键',
 `dev_code` varchar(16) DEFAULT NULL COMMENT '申请的应用类型',
 `bank_ref` int(2) DEFAULT '1' COMMENT '开通业务归属银行 1-邮储银行',
 `status` int(2) DEFAULT '0' COMMENT '申请单状态0-已提交,10-申请中,20-申请成功,30-申请失败',
 `result` varchar(256) DEFAULT NULL COMMENT '申请返回文案',
 `form_body` text COMMENT '本次提交的业务信息',
 `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 `priority` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '优先级',
 `process_status` int(2) DEFAULT '0' COMMENT '0-未处理, 10-进件中, 20-进件成功, 30-微信商家认证成功, 40-发起切换收单机构,50-申请成功 99-申请失败',
 `extra` text COMMENT '额外记录一些返回值，以备不时之需',
 PRIMARY KEY (`id`),
 KEY `idx_sn` (`merchant_sn`),
 KEY `idx_create_at_status` (`create_at`,`status`),
 KEY `idx_update_at` (`update_at`,`status`),
 KEY `idx_priority_status` (`priority`,`status`),
 KEY `idx_task_id_create_at` (`task_id`,`create_at`)
) ENGINE=InnoDB AUTO_INCREMENT=45872 DEFAULT CHARSET=utf8 COMMENT='银行直连申请单表';
CREATE TABLE `auth_and_combo_task` (
`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
`merchant_id` varchar(37) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商户id',
`merchant_sn` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商户号',
`status` int(2) NOT NULL DEFAULT '0' COMMENT '0待处理 1子商户号待授权 2切换参数成功 5成功 6失败',
`form_body` text COLLATE utf8mb4_bin COMMENT '提交的业务信息',
`sub_mch_id` varchar(16) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信子商户号',
`result` mediumtext COLLATE utf8mb4_bin COMMENT '{"channel":"","message":""} 成功时 目前不需要存 失败时需要存失败的通道信息和原因',
`extra` text COLLATE utf8mb4_bin COMMENT '扩展字段',
`create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;