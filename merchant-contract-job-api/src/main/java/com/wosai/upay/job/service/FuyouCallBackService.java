package com.wosai.upay.job.service;


import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractSubTask;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * @Author: haochen
 * @date: 2020/10/8
 * @Description:拉卡拉回调服务(merchant_contract调用)
 */
@JsonRpcService("/rpc/fuyouCallBack")
@Validated
public interface FuyouCallBackService {


   int querySubTaskByContractId(Map<String, Object> callbackMsg);

   int updateSubTaskByContractId(Map<String, Object> callbackMsg);

   int contractSubTaskByContractId(Map<String, Object> callbackMsg);

   /**
    * 5.9.2 变更审核推送接口 ,接收审核推送
    * https://fundwx.fuiou.com/doc/#/scanentry/api_openBusiness?id=_592-%e5%8f%98%e6%9b%b4%e5%ae%a1%e6%a0%b8%e6%8e%a8%e9%80%81%e6%8e%a5%e5%8f%a3
    * @param callbackMsg 富友回调原始信息
    * @return
    */
   int changeAuditPush(Map<String, Object> callbackMsg);

}
