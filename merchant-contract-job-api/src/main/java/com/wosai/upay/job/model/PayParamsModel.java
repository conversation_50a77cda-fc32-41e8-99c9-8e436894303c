package com.wosai.upay.job.model;

/**
 * Created by l<PERSON><PERSON><PERSON> on 2018/9/10.
 */
public class PayParamsModel {

    public static final int SUB_PAYWAY_BARCODE = 1; //b2c
    public static final int SUB_PAYWAY_QRCODE = 2;//c2b
    public static final int SUB_PAYWAY_WAP = 3; //wap
    public static final int SUB_PAYWAY_MINI = 4; //mini
    public static final int SUB_PAYWAY_APP = 5; //app
    public static final int SUB_PAYWAY_H5 = 6; //h5

    public static final int PAYWAY_ALIPAY = 1;
    public static final int PAYWAY_ALIPAY2 = 2;
    public static final int PAYWAY_WEIXIN = 3;
    public static final int PAYWAY_BAIFUBAO = 4;
    public static final int PAYWAY_JDWALLET = 5;
    public static final int PAYWAY_QQWALLET = 6;
    public static final int PAYWAY_APPLEPAY = 7;
    public static final int PAYWAY_LAKALAWALLET = 8;
    public static final int PAYWAY_LKL_UNIONPAY = 17;// 拉卡拉银联二维码支付
    public static final int PAYWAY_BESTPAY = 18;// 翼支付
    public static final int PAYWAY_WEIXIN_HK = 19;// 微信香港本地支付
    public static final int PAYWAY_ALIPAY_INTL = 20;// 支付宝国际版
    public static final int PAYWAY_CMCC = 9; //和支付
    public static final int PAYWAY_DEFAULT = 0;//默认
    public static final int PAYWAY_BANK_CARD = 21;

    public final static int TRADE_TYPE_STATUS = 1;
    public final static int TRADE_TYPE_NOPAY = 2;


    public final static int PROVIDER_FORMAL = 0;
    public static final int PROVIDER_CIBBANK = 1001;    //兴业银行
    public static final int PROVIDER_LAKALA = 1002; //拉卡拉
    public static final int PROVIDER_LAKALA_V3 = 1032;  //拉卡拉v3
    public static final int PROVIDER_CITICBANK = 1003;  //中信
    public static final int PROVIDER_CIBGZBANK = 1008;  //广州兴业
    public static final int PROVIDER_LKLWANMA = 1011;   //拉卡拉完码账户方模式
    public static final int PROVIDER_NUCC = 1013;   //网联
    public static final int PROVIDER_UNIONPAY = 1014;   //银联
    public static final int PROVIDER_CIBSHBANK = 1015;  //上海兴业
    public static final int PROVIDER_DIRECT_UNIONPAY = 1016;    //银联-直连
    public static final int PROVIDER_UION_OPEN = 1017;  //银联开放平台
    public static final int PROVIDER_TONGLIAN = 1020;   //通联
    public static final int PROVIDER_TONGLIAN_V2 = 1035;    //通联收银宝
    public static final int PROVIDER_UMS = 1018;   //银联商务
    public static final int PROVIDER_PSBC = 1023;   //邮储
    public static final int PROVIDER_CGB = 1024;   //广发
    public static final int PROVIDER_CCB = 1026;   //建行
    public static final int PROVIDER_LKLORG = 1033;   //拉卡拉渠道
    public static final int PROVIDER_ICBC = 1030;   //工商银行
    public static final int PROVIDER_HXB = 1028;   //华夏
    public static final int PROVIDER_HAIKE = 1037;   //海科
    public static final int PROVIDER_FUYOU = 1038;   //富友
    public static final int PROVIDER_PAB = 1040; //平安
    public static final int PROVIDER_ZJTLCB = 1043; //浙江泰隆银行
    public static final int PROVIDER_LZB = 1049; //泸州银行
    public static final int PROVIDER_UMB = 1050; //泸州银行
    public static final int PROVIDER_BCS = 1064; //长沙银行

    public static final int PROVIDER_GUOTONG = 1048;// 国通星驿

    /**
     * lkl开放平台
     */
    public static final int PROVIDER_LKL_OPEN = 1034;


    public final static String TRADE_TYPE = "trade_type";
    public final static String PROVIDER_NAME = "provider_name";
    public final static String PAYWAY_NAME = "payway_name";
    public final static String PROVIDER_NAME_WFT = "威富通";
    public final static String PROVIDER_NAME_FORMAL = "直连";
    public final static String PROVIDER_NAME_CIBBANK = "兴业银行";
    public final static String PROVIDER_NAME_LAKALA = "拉卡拉";
    public final static String PROVIDER_NAME_CITICBANK = "中信银行";
    public final static String PROVIDER_NAME_CIBGZBANK = "广州兴业银行";
    public final static String PROVIDER_NAME_LKLWANMA = "包农商银行";
    public final static String PROVIDER_NAME_NUCC = "网联";
    public final static String PROVIDER_NAME_UNIONPAY = "银联";
    public final static String PROVIDER_NAME_DIRECT_UNIONPAY = "内蒙古银联";
    public final static String PAYWAY_ALIPAY_NAME = "支付宝1.0";
    public final static String PAYWAY_ALIPAY2_NAME = "支付宝2.0";
    public final static String PAYWAY_WEIXIN_NAME = "微信";
    public final static String PAYWAY_ALIPAY_NAME_JUST = "支付宝";
    public final static String PAYWAY_BAIFUBAO_NAME = "百付宝";
    public final static String PAYWAY_JDWALLET_NAME = "京东钱包";
    public final static String PAYWAY_QQWALLET_NAME = "QQ钱包";
    public final static String PAYWAY_APPLEPAY_NAME = "苹果支付";
    public final static String PAYWAY_LAKALAWALLET_NAME = "拉卡拉钱包";
    public final static String PAYWAY_LKL_UNIONPAY_NAME = "拉卡拉银联二维码支付";
    public final static String PAYWAY_BESTPAY_NAME = "翼支付";
    public final static String PAYWAY_WEIXIN_HK_NAME = "微信香港本地支付";
    public final static String PAYWAY_CMCC_NAME = "和支付";

    public final static String CIBGZBANK_TRADE_PARAMS = "cibgzbank_trade_params";

    public final static String DEFAULT_MERCHANT_ID = "1";

    public final static String DEFAULT_WEIXIN_SUB_APPSECRET = "9d1408127e6b3470e6057485cf346dc3";

    public final static String NAME_WEIXIN_APP_ID = "name_weixin_app_id";
    public final static String NAME_WEIXIN_SUBSCRIPT_APP_ID = "name_weixin_subscribe_app_id";
    public final static String NAME_WEIXIN_MINI_APP_ID = "name_weixin_mini_app_id";
    public final static String NAME_WEIXIN_RECEIPT_APP_ID = "name_weixin_receipt_app_id";


}
