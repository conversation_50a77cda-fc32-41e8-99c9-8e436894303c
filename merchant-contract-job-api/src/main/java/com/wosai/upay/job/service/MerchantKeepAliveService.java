package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.keepalive.KeepAliveConfigResult;
import com.wosai.upay.job.model.keepalive.KeepAliveConfigRequest;
import com.wosai.upay.job.model.keepalive.KeepAliveTaskInfo;
import com.wosai.upay.job.model.keepalive.KeepAliveTasksQueryRequest;
import com.wosai.upay.job.model.keepalive.KeepAliveTaskStatusUpdateRequest;
import com.wosai.upay.job.model.keepalive.KeepAliveValidateRequest;
import com.wosai.web.api.ListResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 商户保活服务接口
 *
 * <AUTHOR>
@JsonRpcService("/rpc/keepAlive")
@Validated
public interface MerchantKeepAliveService {

    /**
     * 加入保活计划
     *
     * @param request 保活计划请求参数
     * @return 是否成功
     */
    CuaCommonResultDTO enableKeepAliveConfig(@Valid KeepAliveConfigRequest request, LogParamsDto logParamsDto);

    /**
     * 退出保活计划
     *
     * @param request 保活计划请求参数
     * @return 是否成功
     */
    CuaCommonResultDTO disableKeepAliveConfig(@Valid KeepAliveConfigRequest request, LogParamsDto logParamsDto);

    /**
     * 查询保活计划
     *
     * @param merchantSn 商户号
     * @return 保活计划信息
     */
    KeepAliveConfigResult queryKeepAliveConfig(@NotBlank(message = "商户号不能为空") String merchantSn);

    /**
     * 查询保活任务
     *
     * @param keepAliveTasksQueryRequest 请求参数
     * @return 保活任务信息
     */
    ListResult<KeepAliveTaskInfo> queryKeepAliveTasks(@Valid KeepAliveTasksQueryRequest keepAliveTasksQueryRequest);

    /**
     * 更新保活任务状态
     *
     * @param request 保活任务状态更新请求参数
     * @return 处理结果
     */
    CuaCommonResultDTO updateKeepAliveTaskStatus(@Valid KeepAliveTaskStatusUpdateRequest request);

    /**
     * 验证保活规则
     *
     * @param request 保活规则检查请求参数
     * @return 检查结果
     */
    CuaCommonResultDTO validateKeepAlive(@Valid KeepAliveValidateRequest request);
}