package com.wosai.upay.job.enume;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 商户申请单查询接口 状态
 * <AUTHOR>
 * @date 2024/3/12
 */
public enum AntMerchantExpandOrderStatusEnum implements ITextValueEnum<String> {

    /**
     * 已完结
     */
    FINISHED("99", "已完结"),
    /**
     * 失败
     */
    FAIL("-1", "失败"),
    /**
     * 已提交审核
     */
    AUDITING("031", "已提交审核")
    ;

    private String value;
    private String text;

    AntMerchantExpandOrderStatusEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }


    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
