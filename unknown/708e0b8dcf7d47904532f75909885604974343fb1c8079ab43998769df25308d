package com.wosai.upay.job.biz.directparams;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.model.directparams.BaseParams;
import com.wosai.upay.job.model.directparams.WeixinHKDirectParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 目前微信香港只支持 B2C C2B
 *
 * <AUTHOR>
 * @date 2019-08-29
 */
@Component
public class WeixinHKDirectParamsBiz extends DirectParamsBiz {

    private static final int PAYWAY = PaywayEnum.WEIXIN_HK.getValue();

    @Override
    public void addDirectParams(BaseParams baseParams) {
        checkMerchant(baseParams);

        WeixinHKDirectParams params = (WeixinHKDirectParams) baseParams;

        if (isNotEmpty(params.getWeixin_trade_params())) {
            Map<String, Object> directParams = bean2Map(params.getWeixin_trade_params());
            paramsBiz.saveDirectMerchantProviderParams(
                    new MerchantProviderParamsDto()
                            .setMerchant_sn(params.getMerchant_sn())
                            .setExtra(CollectionUtil.hashMap(TransactionParam.WEIXIN_TRADE_PARAMS, directParams))
                            .setProvider(PAYWAY)
                            .setPayway(PAYWAY)
                            .setPay_merchant_id(params.getWeixin_trade_params().getWeixin_mch_id())
                            .setWeixin_sub_appid(params.getWeixin_trade_params().getWeixin_appid())

            );

            Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(params.getMerchant_id(), PAYWAY);
            if (merchantConfig == null) {
                tradeConfigService.createMerchantConfig(CollectionUtil.hashMap(
                        MerchantConfig.MERCHANT_ID, params.getMerchant_id(),
                        MerchantConfig.PAYWAY, PAYWAY

                ));
                merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(params.getMerchant_id(), PAYWAY);
            }


            Map merchantConfigParams = WosaiMapUtils.getMap(merchantConfig, MerchantConfig.PARAMS, new HashMap());
            merchantConfigParams.put(TransactionParam.WEIXIN_TRADE_PARAMS, directParams);
            Map updateInfo = CollectionUtil.hashMap(
                    DaoConstants.ID, WosaiMapUtils.getString(merchantConfig, DaoConstants.ID),
                    MerchantConfig.PARAMS, merchantConfigParams,
                    MerchantConfig.B2C_FORMAL, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.B2C_AGENT_NAME, null,
                    MerchantConfig.B2C_FEE_RATE, params.getWeixin_trade_params().getFee_rate(),
                    MerchantConfig.C2B_FORMAL, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.C2B_AGENT_NAME, null,
                    MerchantConfig.C2B_FEE_RATE, params.getWeixin_trade_params().getFee_rate()
            );
            tradeConfigService.updateMerchantConfig(updateInfo);
            supportService.removeCachedParams(params.getMerchant_sn());
        }
    }

    @Override
    public void deleteDirectParams(MerchantProviderParamsDto params, String subPayway, String feeRate) {
        paramsBiz.deleteParamsById(params.getId());

        Map merchant = merchantService.getMerchantBySn(params.getMerchant_sn());
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);

        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY);
        Map merchantConfigParams = WosaiMapUtils.getMap(merchantConfig, MerchantConfig.PARAMS, new HashMap());
        merchantConfigParams.remove(TransactionParam.WEIXIN_TRADE_PARAMS);

        Map updateInfo = CollectionUtil.hashMap(
                DaoConstants.ID, WosaiMapUtils.getString(merchantConfig, DaoConstants.ID),
                MerchantConfig.PARAMS, merchantConfigParams,
                MerchantConfig.B2C_FORMAL, MerchantConfig.STATUS_CLOSED,
                MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_CLOSED,
                MerchantConfig.B2C_AGENT_NAME, null,
                MerchantConfig.B2C_FEE_RATE, null,
                MerchantConfig.C2B_FORMAL, MerchantConfig.STATUS_CLOSED,
                MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_CLOSED,
                MerchantConfig.C2B_AGENT_NAME, null,
                MerchantConfig.C2B_FEE_RATE, null
        );

        tradeConfigService.updateMerchantConfig(updateInfo);
        supportService.removeCachedParams(params.getMerchant_sn());
    }

    @Override
    public BaseParams getDirectParams(String merchantSn) {
        return null;
    }

    @Override
    public List<MerchantProviderParamsCustomDto> handleDirectParams(MerchantProviderParamsCustomDto source) {
        return Arrays.asList(source);
    }
}
