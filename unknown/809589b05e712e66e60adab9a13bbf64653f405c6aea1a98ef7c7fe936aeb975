package com.wosai.upay.job.util.guotong;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.lang.ArrayUtils;
import sun.misc.BASE64Decoder;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.X509EncodedKeySpec;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2024/12/11
 */
public class GuotongSignUtil {

    public static String sign(TreeMap<String, Object> params, String publicKey) {
        // 拼接参数，生成sign
        StringBuilder sb = new StringBuilder();
        for (String key : params.keySet()) {
            sb.append(key).append("=").append(params.get(key)).append("&");
        }
        String res = sb.substring(0, sb.lastIndexOf("&"));
        String sha256 = getSHA256(res);
        return encrypt(publicKey, sha256);
    }

    public static boolean checkSign(String plainText, String publicKey) throws Exception {
        TreeMap<String, Object> map = JSON.parseObject(plainText, new TypeReference<TreeMap<String, Object>>(){});
        StringBuilder sb = new StringBuilder();
        String sign = "";
        for (String key : map.keySet()) {
            if ("sign".equals(key)) {
                sign = (String) map.get(key);
                continue;
            }
            sb.append(key).append("=").append(map.get(key)).append("&");
        }
        String res = sb.substring(0, sb.lastIndexOf("&"));
        String sha256 = getSHA256(res);

        String decodeSha256 = decrypt(publicKey, sign);

        return sha256.equals(decodeSha256);
    }

    /**
     * 公钥解密过程
     *
     * @param key  公钥
     * @param data 密文数据
     * @return 明文
     * @throws Exception 解密过程中的异常信息
     */
    public static String decrypt(String key, String data) throws Exception {
        if (key == null) {
            throw new Exception("解密公钥为空, 请设置");
        }
        PublicKey publicKey = getPublicKey(key);
        byte[] cipherData = GuotongBase64Util.decode(data);
        Cipher cipher = null;
        try {
            // 使用默认RSA
            cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, publicKey);

            byte[] buf = (byte[]) null;
            for (int i = 0; i < cipherData.length; i += 256) {
                byte[] doFinal = cipher.doFinal(ArrayUtils.subarray(cipherData, i, i + 256));
                if (buf == null) {
                    buf = doFinal;
                } else {
                    buf = addBytes(buf, doFinal);
                }
            }
            return new String(buf);
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此解密算法");
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
            return null;
        } catch (InvalidKeyException e) {
            throw new Exception("解密公钥非法,请检查");
        } catch (IllegalBlockSizeException e) {
            throw new Exception("密文长度非法");
        } catch (BadPaddingException e) {
            throw new Exception("密文数据已损坏");
        }
    }


    /**
     * 利用java原生的类实现SHA256加密
     *
     * @param str 加密后的报文
     * @return
     */
    public static String getSHA256(String str) {
        MessageDigest messageDigest;
        String encodestr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            encodestr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return encodestr;
    }


    /**
     * 将byte转为16进制
     *
     * @param bytes
     * @return
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuilder stringBuffer = new StringBuilder();
        String temp;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }


    /**
     * 使用公钥对明文进行签名
     *
     * @param publicKey 公钥
     * @param plainText 明文
     * @return
     */
    public static String encrypt(String publicKey, String plainText) {
        try {
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, getPublicKey(publicKey));
            byte[] bytes = plainText.getBytes();
            ByteArrayInputStream read = new ByteArrayInputStream(bytes);
            ByteArrayOutputStream write = new ByteArrayOutputStream();
            byte[] buf = new byte[117];
            int len = 0;
            while ((len = read.read(buf)) != -1) {
                byte[] buf1 = null;
                if (buf.length == len) {
                    buf1 = buf;
                } else {
                    buf1 = new byte[len];
                    for (int i = 0; i < len; i++) {
                        buf1[i] = buf[i];
                    }
                }
                byte[] bytes1 = cipher.doFinal(buf1);
                write.write(bytes1);
            }
            return GuotongBase64Util.encode(write.toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 公钥解密过程
     *
     * @param key  公钥
     * @param data 密文数据
     * @return 明文
     * @throws Exception 解密过程中的异常信息
     */
    public static String publicKeyDecrypt(String key, String data) throws Exception {
        if (key == null) {
            throw new Exception("解密公钥为空, 请设置");
        }
        PublicKey publicKey = getPublicKey(key);
        byte[] cipherData = GuotongBase64Util.decode(data);
        Cipher cipher = null;
        try {
            // 使用默认RSA
            cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, publicKey);
            byte[] buf = (byte[]) null;
            for (int i = 0; i < cipherData.length; i += 256) {
                byte[] doFinal = cipher.doFinal(ArrayUtils.subarray(cipherData, i, i + 256));
                if (buf == null) {
                    buf = doFinal;
                } else {
                    buf = addBytes(buf, doFinal);
                }
            }
            return new String(buf);
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此解密算法");
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
            return null;
        } catch (InvalidKeyException e) {
            throw new Exception("解密公钥非法,请检查");
        } catch (IllegalBlockSizeException e) {
            throw new Exception("密文长度非法");
        } catch (BadPaddingException e) {
            throw new Exception("密文数据已损坏");
        }
    }


    /**
     * 数组拼接
     *
     * @param data1 数组1
     * @param data2 数组2
     * @return
     */
    public static byte[] addBytes(byte[] data1, byte[] data2) {
        byte[] data3 = new byte[data1.length + data2.length];
        System.arraycopy(data1, 0, data3, 0, data1.length);
        System.arraycopy(data2, 0, data3, data1.length, data2.length);
        return data3;
    }


    /**
     * 得到公钥
     *
     * @param key 密钥字符串（经过base64编码�?
     * @throws Exception
     */
    public static PublicKey getPublicKey(String key) throws Exception {
        byte[] keyBytes;
        keyBytes = new BASE64Decoder().decodeBuffer(key);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(keySpec);
    }

}
