/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class ZqfApply extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -6501874182758452964L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"ZqfApply\",\"namespace\":\"com.wosai.upay.job.avro\",\"fields\":[{\"name\":\"merchant_sn\",\"type\":[\"null\",\"string\"],\"default\":null,\"meta\":\"商户号\"},{\"name\":\"merchant_industry_level1\",\"type\":[\"null\",\"string\"],\"default\":null,\"meta\":\"行业\"},{\"name\":\"apply_stage\",\"type\":[\"null\",\"string\"],\"default\":null,\"meta\":\"开通节点\"},{\"name\":\"status\",\"type\":[\"null\",\"string\"],\"default\":null,\"meta\":\"审核状态\"},{\"name\":\"failure_reason\",\"type\":[\"null\",\"string\"],\"default\":null,\"meta\":\"失败原因\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<ZqfApply> ENCODER =
      new BinaryMessageEncoder<ZqfApply>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<ZqfApply> DECODER =
      new BinaryMessageDecoder<ZqfApply>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<ZqfApply> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<ZqfApply> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<ZqfApply>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this ZqfApply to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a ZqfApply from a ByteBuffer. */
  public static ZqfApply fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.CharSequence merchant_industry_level1;
  @Deprecated public java.lang.CharSequence apply_stage;
  @Deprecated public java.lang.CharSequence status;
  @Deprecated public java.lang.CharSequence failure_reason;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public ZqfApply() {}

  /**
   * All-args constructor.
   * @param merchant_sn The new value for merchant_sn
   * @param merchant_industry_level1 The new value for merchant_industry_level1
   * @param apply_stage The new value for apply_stage
   * @param status The new value for status
   * @param failure_reason The new value for failure_reason
   */
  public ZqfApply(java.lang.CharSequence merchant_sn, java.lang.CharSequence merchant_industry_level1, java.lang.CharSequence apply_stage, java.lang.CharSequence status, java.lang.CharSequence failure_reason) {
    this.merchant_sn = merchant_sn;
    this.merchant_industry_level1 = merchant_industry_level1;
    this.apply_stage = apply_stage;
    this.status = status;
    this.failure_reason = failure_reason;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_sn;
    case 1: return merchant_industry_level1;
    case 2: return apply_stage;
    case 3: return status;
    case 4: return failure_reason;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_sn = (java.lang.CharSequence)value$; break;
    case 1: merchant_industry_level1 = (java.lang.CharSequence)value$; break;
    case 2: apply_stage = (java.lang.CharSequence)value$; break;
    case 3: status = (java.lang.CharSequence)value$; break;
    case 4: failure_reason = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'merchant_industry_level1' field.
   * @return The value of the 'merchant_industry_level1' field.
   */
  public java.lang.CharSequence getMerchantIndustryLevel1() {
    return merchant_industry_level1;
  }

  /**
   * Sets the value of the 'merchant_industry_level1' field.
   * @param value the value to set.
   */
  public void setMerchantIndustryLevel1(java.lang.CharSequence value) {
    this.merchant_industry_level1 = value;
  }

  /**
   * Gets the value of the 'apply_stage' field.
   * @return The value of the 'apply_stage' field.
   */
  public java.lang.CharSequence getApplyStage() {
    return apply_stage;
  }

  /**
   * Sets the value of the 'apply_stage' field.
   * @param value the value to set.
   */
  public void setApplyStage(java.lang.CharSequence value) {
    this.apply_stage = value;
  }

  /**
   * Gets the value of the 'status' field.
   * @return The value of the 'status' field.
   */
  public java.lang.CharSequence getStatus() {
    return status;
  }

  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(java.lang.CharSequence value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'failure_reason' field.
   * @return The value of the 'failure_reason' field.
   */
  public java.lang.CharSequence getFailureReason() {
    return failure_reason;
  }

  /**
   * Sets the value of the 'failure_reason' field.
   * @param value the value to set.
   */
  public void setFailureReason(java.lang.CharSequence value) {
    this.failure_reason = value;
  }

  /**
   * Creates a new ZqfApply RecordBuilder.
   * @return A new ZqfApply RecordBuilder
   */
  public static com.wosai.upay.job.avro.ZqfApply.Builder newBuilder() {
    return new com.wosai.upay.job.avro.ZqfApply.Builder();
  }

  /**
   * Creates a new ZqfApply RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new ZqfApply RecordBuilder
   */
  public static com.wosai.upay.job.avro.ZqfApply.Builder newBuilder(com.wosai.upay.job.avro.ZqfApply.Builder other) {
    return new com.wosai.upay.job.avro.ZqfApply.Builder(other);
  }

  /**
   * Creates a new ZqfApply RecordBuilder by copying an existing ZqfApply instance.
   * @param other The existing instance to copy.
   * @return A new ZqfApply RecordBuilder
   */
  public static com.wosai.upay.job.avro.ZqfApply.Builder newBuilder(com.wosai.upay.job.avro.ZqfApply other) {
    return new com.wosai.upay.job.avro.ZqfApply.Builder(other);
  }

  /**
   * RecordBuilder for ZqfApply instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<ZqfApply>
    implements org.apache.avro.data.RecordBuilder<ZqfApply> {

    private java.lang.CharSequence merchant_sn;
    private java.lang.CharSequence merchant_industry_level1;
    private java.lang.CharSequence apply_stage;
    private java.lang.CharSequence status;
    private java.lang.CharSequence failure_reason;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.job.avro.ZqfApply.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_industry_level1)) {
        this.merchant_industry_level1 = data().deepCopy(fields()[1].schema(), other.merchant_industry_level1);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.apply_stage)) {
        this.apply_stage = data().deepCopy(fields()[2].schema(), other.apply_stage);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.status)) {
        this.status = data().deepCopy(fields()[3].schema(), other.status);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.failure_reason)) {
        this.failure_reason = data().deepCopy(fields()[4].schema(), other.failure_reason);
        fieldSetFlags()[4] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing ZqfApply instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.job.avro.ZqfApply other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_industry_level1)) {
        this.merchant_industry_level1 = data().deepCopy(fields()[1].schema(), other.merchant_industry_level1);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.apply_stage)) {
        this.apply_stage = data().deepCopy(fields()[2].schema(), other.apply_stage);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.status)) {
        this.status = data().deepCopy(fields()[3].schema(), other.status);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.failure_reason)) {
        this.failure_reason = data().deepCopy(fields()[4].schema(), other.failure_reason);
        fieldSetFlags()[4] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.ZqfApply.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_sn = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.ZqfApply.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_industry_level1' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantIndustryLevel1() {
      return merchant_industry_level1;
    }

    /**
      * Sets the value of the 'merchant_industry_level1' field.
      * @param value The value of 'merchant_industry_level1'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.ZqfApply.Builder setMerchantIndustryLevel1(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_industry_level1 = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_industry_level1' field has been set.
      * @return True if the 'merchant_industry_level1' field has been set, false otherwise.
      */
    public boolean hasMerchantIndustryLevel1() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_industry_level1' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.ZqfApply.Builder clearMerchantIndustryLevel1() {
      merchant_industry_level1 = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'apply_stage' field.
      * @return The value.
      */
    public java.lang.CharSequence getApplyStage() {
      return apply_stage;
    }

    /**
      * Sets the value of the 'apply_stage' field.
      * @param value The value of 'apply_stage'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.ZqfApply.Builder setApplyStage(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.apply_stage = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'apply_stage' field has been set.
      * @return True if the 'apply_stage' field has been set, false otherwise.
      */
    public boolean hasApplyStage() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'apply_stage' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.ZqfApply.Builder clearApplyStage() {
      apply_stage = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * @return The value.
      */
    public java.lang.CharSequence getStatus() {
      return status;
    }

    /**
      * Sets the value of the 'status' field.
      * @param value The value of 'status'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.ZqfApply.Builder setStatus(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.status = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'status' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.ZqfApply.Builder clearStatus() {
      status = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'failure_reason' field.
      * @return The value.
      */
    public java.lang.CharSequence getFailureReason() {
      return failure_reason;
    }

    /**
      * Sets the value of the 'failure_reason' field.
      * @param value The value of 'failure_reason'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.ZqfApply.Builder setFailureReason(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.failure_reason = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'failure_reason' field has been set.
      * @return True if the 'failure_reason' field has been set, false otherwise.
      */
    public boolean hasFailureReason() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'failure_reason' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.ZqfApply.Builder clearFailureReason() {
      failure_reason = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public ZqfApply build() {
      try {
        ZqfApply record = new ZqfApply();
        record.merchant_sn = fieldSetFlags()[0] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_industry_level1 = fieldSetFlags()[1] ? this.merchant_industry_level1 : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.apply_stage = fieldSetFlags()[2] ? this.apply_stage : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.status = fieldSetFlags()[3] ? this.status : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.failure_reason = fieldSetFlags()[4] ? this.failure_reason : (java.lang.CharSequence) defaultValue(fields()[4]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<ZqfApply>
    WRITER$ = (org.apache.avro.io.DatumWriter<ZqfApply>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<ZqfApply>
    READER$ = (org.apache.avro.io.DatumReader<ZqfApply>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
