package com.wosai.upay.job.biz.bankDirect;

import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.ViewProcess;
import com.wosai.upay.job.model.direct.ApplyStatusResp;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.model.psbc.ModifySupportingMaterialsRequest;
import com.wosai.upay.job.model.psbc.SelfAuditRejectRequest;

import java.util.List;

/**
 * @Description: 银行直连业务处理
 * <AUTHOR>
 * @Date 2021/4/26 11:03
 */
public interface BankHandleService {
     /**
      * 获取dev_code,为了根据dev_code找到对应的处理类
      * @return
      */
     String getDevCode();

     /**
      * @param: BankDirectReq 申请参数
      * @return:
      * @Author: zhmh
      * @Description:
      * @time: 09:51 2021/4/7
      */
     ContractResponse applyBankDirect(BankDirectReq bankDirectReq);

     /**
      * 获取申请单的状态和文案 给内部使用
      * @param merchantSn 商户号
      * @param devCode 应用标识
      * @param platform 平台
      * @return 状态和文案
      */
     ApplyStatusResp getApplyInfo(String merchantSn, String devCode, String platform);


     /**
      * 获取申请单的状态
      * @param merchantSn 商户号
      * @param devCode 应用标识
      * @return 状态和文案
      */
     Integer getApplyStatus(@NotEmpty(message = "商户号不能为空") String merchantSn, @NotEmpty(message = "应用标识不能为空") String devCode);

    /**
     * 获取申请单对应的进件任务状态
     * @param merchantSn 商户号
     * @param devCode 应用标识
     * @return 进件任务状态，不存在则返回null, 状态枚举见 TaskStatus
     */
    Integer getContractTaskStatus(String merchantSn, String devCode);


    /**
      * 自助撤销接口
      * @param request
      * @return
      */
     ContractResponse selfAuditReject(SelfAuditRejectRequest request);

     /**
      * 获取申请单的状态
      * @param merchantSn 商户号
      * @param devCode 应用标识
      * @return 状态
      */
    List<ViewProcess> getViewProcess(String merchantSn, String devCode);


     /**
      * 初始化申请单的状态
      * @param merchantSn 商户号
      * @return 状态
      */
     List<ViewProcess> initViewProcess(String merchantSn);


    /**
     * 修改辅助证明材料
     * @param req 请求参数
     * @return
     */
    ContractResponse modifySupportingMaterials(ModifySupportingMaterialsRequest req);


    /**
     * 更新申请单状态
     * @param merchantSn 商户号
     * @param devCode 应用标识
     * @return 状态
     */
    void refreshAndHandleContractStatus(String merchantSn, String devCode);
}
