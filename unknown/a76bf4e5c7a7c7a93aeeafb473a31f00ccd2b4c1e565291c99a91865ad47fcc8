package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.model.DirectStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/1/6
 */
@Service
@AutoJsonRpcServiceImpl
public class DirectStatusServiceImpl implements DirectStatusService{

    @Autowired
    private DirectStatusBiz directStatusBiz;

    @Override
    public int getDirectStatus(String merchantSn, String devCode) {
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode(merchantSn, devCode);
        if (directStatus == null) {
            throw new CommonInvalidParameterException(merchantSn + "对应的" + devCode + "应用直连状态不存在");
        }
        return directStatus.getStatus();
    }
}
