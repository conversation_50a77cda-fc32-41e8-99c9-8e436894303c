package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.WechatAuthEnum;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.TaskMch;
import com.wosai.upay.job.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Description:微信实名的业务判断 主要用于间连行业活动
 * <AUTHOR>
 * Date 2020/4/17 9:25 上午
 **/
@Component
@Slf4j
public class AuthTaskBiz {

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private WeixinAuthApplyBiz weixinAuthApplyBiz;

    @Autowired
    private ComposeAcquirerBiz acquirerBiz;

    /**
     * 根据task获取普通渠道的子商户号
     */
    public String getAuthPayMchId(ContractTask task) {
        TaskMch taskMch = weixinAuthApplyBiz.getTaskMchByTaskId(task.getId());
        return taskMch != null ? taskMch.getPay_merchant_id() : "";
    }

    /**
     * @param task
     * @return
     */
    public WechatAuthEnum getWechatAuthEnum(ContractTask task) {
        Map context = JSON.parseObject(task.getEvent_context(), Map.class);
        String channel = MapUtils.getString(context, ParamContextBiz.WECHAT_AUTH_ENUM);
        if (!StringUtil.empty(channel)) {
            return WechatAuthEnum.valueChannel(channel);
        }
        String payMchId = getAuthPayMchId(task);
        if (StringUtil.empty(payMchId)) {
            return WechatAuthEnum.valueAcquirer(acquirerBiz.getMerchantAcquirer(task.getMerchant_sn()));
        }
        return WechatAuthEnum.valueProvider(merchantProviderParamsMapper.getByPayMerchantId(payMchId).getProvider());
    }

}
