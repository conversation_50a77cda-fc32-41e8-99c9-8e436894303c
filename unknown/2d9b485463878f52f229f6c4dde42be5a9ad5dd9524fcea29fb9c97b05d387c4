package com.wosai.upay.job.xxljob.direct.authandcombo;

import com.wosai.upay.job.Constants.MerchantChangeDataConstant;
import com.wosai.upay.job.biz.AuthAndComboTaskBiz;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.mapper.AuthAndComboTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.AuthAndComboTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AuthAndComboQueryJobHandlerTest {

    @InjectMocks
    private AuthAndComboQueryJobHandler authAndComboQueryJobHandler;

    @Mock
    private AuthAndComboTaskMapper authAndComboTaskMapper;

    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Mock
    private WechatAuthBiz wechatAuthBiz;

    @Mock
    private AuthAndComboTaskBiz authAndComboTaskBiz;

    private DirectJobParam param;

    @Before
    public void setUp() {
        param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(604800000L); // 一周
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "AuthAndComboQueryJobHandler";
        String actualLockKey = authAndComboQueryJobHandler.getLockKey();
        assertEquals("The lock key should match the expected value", expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_NoTasks_Returns() {
        when(authAndComboTaskMapper.selectWaitForAuth(any(Date.class), anyInt())).thenReturn(new ArrayList<>());

        authAndComboQueryJobHandler.execute(param);

        verify(authAndComboTaskMapper, times(1)).selectWaitForAuth(any(Date.class), anyInt());
        verifyNoMoreInteractions(authAndComboTaskMapper, merchantProviderParamsMapper, wechatAuthBiz, authAndComboTaskBiz);
    }

    @Test
    public void execute_NoParams_ThrowsException() {
        AuthAndComboTask task = new AuthAndComboTask();
        task.setSub_mch_id("123");
        List<AuthAndComboTask> tasks = new ArrayList<>();
        tasks.add(task);

        when(authAndComboTaskMapper.selectWaitForAuth(any(Date.class), anyInt())).thenReturn(tasks);
        when(merchantProviderParamsMapper.selectByPayMerchantId("123")).thenReturn(null);

        authAndComboQueryJobHandler.execute(param);

        verify(authAndComboTaskMapper, times(1)).selectWaitForAuth(any(Date.class), anyInt());
        verify(merchantProviderParamsMapper, times(1)).selectByPayMerchantId("123");
        verify(authAndComboTaskBiz, times(1)).changeStatusAndSendMessage(task, MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_FAIL, "交易参数不存在");
    }

    @Test
    public void execute_DeletedParams_ThrowsException() {
        AuthAndComboTask task = new AuthAndComboTask();
        task.setSub_mch_id("123");
        List<AuthAndComboTask> tasks = new ArrayList<>();
        tasks.add(task);

        MerchantProviderParams params = new MerchantProviderParams();
        params.setDeleted(true);

        when(authAndComboTaskMapper.selectWaitForAuth(any(Date.class), anyInt())).thenReturn(tasks);
        when(merchantProviderParamsMapper.selectByPayMerchantId("123")).thenReturn(params);

        authAndComboQueryJobHandler.execute(param);

        verify(authAndComboTaskMapper, times(1)).selectWaitForAuth(any(Date.class), anyInt());
        verify(merchantProviderParamsMapper, times(1)).selectByPayMerchantId("123");
        verify(authAndComboTaskBiz, times(1)).changeStatusAndSendMessage(task, MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_FAIL, "交易参数不存在");
    }

    @Test
    public void execute_SuccessfulAuth_ChangesStatus() {
        AuthAndComboTask task = new AuthAndComboTask();
        task.setSub_mch_id("123");
        List<AuthAndComboTask> tasks = new ArrayList<>();
        tasks.add(task);

        MerchantProviderParams params = new MerchantProviderParams();
        params.setProvider(1000); // 假设不是 PROVIDER_FUYOU
        params.setDeleted(false);

        when(authAndComboTaskMapper.selectWaitForAuth(any(Date.class), anyInt())).thenReturn(tasks);
        when(merchantProviderParamsMapper.selectByPayMerchantId("123")).thenReturn(params);
        when(wechatAuthBiz.getAuthStatus(params, 1000)).thenReturn(true);

        authAndComboQueryJobHandler.execute(param);

        verify(authAndComboTaskMapper, times(1)).selectWaitForAuth(any(Date.class), anyInt());
        verify(merchantProviderParamsMapper, times(1)).selectByPayMerchantId("123");
        verify(wechatAuthBiz, times(1)).getAuthStatus(params, 1000);
        verify(authAndComboTaskBiz, times(1)).changeTradeParamsAndApplyCombo(task, params);
        verify(authAndComboTaskBiz, times(1)).changeStatusAndSendMessage(task, MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_SUCCEED, null);
    }

    @Test
    public void execute_AuthFailure_ChangesStatus() {
        AuthAndComboTask task = new AuthAndComboTask();
        task.setSub_mch_id("123");
        List<AuthAndComboTask> tasks = new ArrayList<>();
        tasks.add(task);

        MerchantProviderParams params = new MerchantProviderParams();
        params.setProvider(1000);
        params.setDeleted(false);

        when(authAndComboTaskMapper.selectWaitForAuth(any(Date.class), anyInt())).thenReturn(tasks);
        when(merchantProviderParamsMapper.selectByPayMerchantId("123")).thenReturn(params);
        when(wechatAuthBiz.getAuthStatus(params, 1000)).thenReturn(false);

        authAndComboQueryJobHandler.execute(param);

        verify(authAndComboTaskMapper, times(1)).selectWaitForAuth(any(Date.class), anyInt());
        verify(merchantProviderParamsMapper, times(1)).selectByPayMerchantId("123");
        verify(wechatAuthBiz, times(1)).getAuthStatus(params, 1000);
    }

    @Test
    public void execute_Exception_ChangesStatus() {
        AuthAndComboTask task = new AuthAndComboTask();
        task.setSub_mch_id("123");
        List<AuthAndComboTask> tasks = new ArrayList<>();
        tasks.add(task);

        MerchantProviderParams params = new MerchantProviderParams();
        params.setProvider(1000);
        params.setDeleted(false);

        when(authAndComboTaskMapper.selectWaitForAuth(any(Date.class), anyInt())).thenReturn(tasks);
        when(merchantProviderParamsMapper.selectByPayMerchantId("123")).thenReturn(params);
        when(wechatAuthBiz.getAuthStatus(params, 1000)).thenThrow(new RuntimeException("Test Exception"));

        authAndComboQueryJobHandler.execute(param);

        verify(authAndComboTaskMapper, times(1)).selectWaitForAuth(any(Date.class), anyInt());
        verify(merchantProviderParamsMapper, times(1)).selectByPayMerchantId("123");
        verify(wechatAuthBiz, times(1)).getAuthStatus(params, 1000);
        verify(authAndComboTaskBiz, times(1)).changeStatusAndSendMessage(task, MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_FAIL, "Test Exception");
    }
}
