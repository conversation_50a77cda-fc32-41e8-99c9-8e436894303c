package com.wosai.upay.job.util;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.wosai.upay.merchant.contract.model.ContractResponse;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/11/18
 */
public class RetryerUtil {

    private static final Retryer<ContractResponse> HAIKE_BRAND_TERMINAL_SYNC_RETRYER;
    private static final Retryer<ContractResponse> HAIKE_BRAND_MERCHANT_AFFILIATION_RETRYER;

    static {
        HAIKE_BRAND_TERMINAL_SYNC_RETRYER = RetryerBuilder.<ContractResponse>newBuilder()
                .retryIfResult(input -> Objects.equals(input.isSuccess(), false))
                .retryIfException()
                .withWaitStrategy(WaitStrategies.randomWait(1, TimeUnit.SECONDS, 3, TimeUnit.SECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                .build();
        HAIKE_BRAND_MERCHANT_AFFILIATION_RETRYER = RetryerBuilder.<ContractResponse>newBuilder()
                .retryIfResult(input -> Objects.equals(input.isSuccess(), false))
                .retryIfException()
                .withWaitStrategy(WaitStrategies.fixedWait(200, TimeUnit.MILLISECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
               .build();
    }

    public static Retryer<ContractResponse> getHaikeBrandTerminalSyncRetryer() {
        return HAIKE_BRAND_TERMINAL_SYNC_RETRYER;
    }

    public static Retryer<ContractResponse> getHaikeBrandMerchantAffiliationRetryer() {
        return HAIKE_BRAND_MERCHANT_AFFILIATION_RETRYER;
    }
}
