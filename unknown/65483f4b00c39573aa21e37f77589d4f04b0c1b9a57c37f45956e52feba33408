package com.wosai.upay.job.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * @Description: JacksonHelperUtils
 * <AUTHOR>
 * @Date 2023/4/28 15:51
 **/
public class JacksonHelperUtils {

    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.setPropertyNamingStrategy(new com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy());
    }

    public static byte[] toJsonBytes(Object value){
        try {
            return mapper.writeValueAsBytes(value);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(
                    String.format("unable to write %s to json bytes.", value.getClass().getName()),
                    e
            );
        }
    }

    public static String toJsonString(Object value) {
        try {
            return mapper.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(
                    String.format("unable to write %s to json string.", value.getClass().getName()),
                    e
            );
        }
    }

}