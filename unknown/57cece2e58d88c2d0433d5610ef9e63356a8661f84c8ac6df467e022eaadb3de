package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;

import java.util.List;

public interface MultiProviderContractEventMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MultiProviderContractEvent record);

    int insertSelective(MultiProviderContractEvent record);

    MultiProviderContractEvent selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MultiProviderContractEvent record);

    int updateByPrimaryKeyWithBLOBs(MultiProviderContractEvent record);

    int updateByPrimaryKey(MultiProviderContractEvent record);

    List<MultiProviderContractEvent> selectPendingNetInMultiEvents(String formatDate, Integer queryLimit);

    List<MultiProviderContractEvent> selectProcessingNetInMultiEvents(String formatDate, Integer queryLimit);

    List<MultiProviderContractEvent> selectNotFinishedEventsByMerchantSn(String merchantSn);

    MultiProviderContractEvent selectMultiEventByMerchantSnAndTaskId(String merchantSn, Long taskId);

    MultiProviderContractEvent selectLatestMultiEventByMerchantSn(String merchantSn);

    List<MultiProviderContractEvent> selectMultiEventTodoByMerchantSn(String merchant_sn);

    List<MultiProviderContractEvent> selectSuccessEvents(String formatDate, Integer queryLimit);

}