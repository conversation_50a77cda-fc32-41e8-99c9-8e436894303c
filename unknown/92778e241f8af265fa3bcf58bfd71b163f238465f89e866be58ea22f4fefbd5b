package com.wosai.upay.job.biz.comboparams;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.TransactionParam;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Description:拉卡拉万码账户方模式
 * <AUTHOR>
 * Date 2020/6/3 5:01 下午
 **/
@Component
public class LklWanMaParamsHandle extends ProviderParamsHandle {

    private static int PROVIDER = ProviderEnum.PROVIDER_LKLWANMA.getValue();


    @Override
    protected boolean accept(MerchantConfigParams configParams) {
        return PROVIDER == configParams.getProvider();
    }

    @Override
    protected Map getConfigParams(MerchantConfigParams configParams) {
        int payWay = configParams.getPayWay();
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            return CollectionUtil.hashMap(
                    TransactionParam.ALIPAY_PID, configParams.getPayMerchantId(),
                    TransactionParam.LAKALA_WANMA_MCH_ID, configParams.getProviderMerchantId());
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            return CollectionUtil.hashMap(
                    TransactionParam.WEIXIN_SUB_MCH_ID, configParams.getPayMerchantId(),
                    TransactionParam.LAKALA_WANMA_MCH_ID, configParams.getProviderMerchantId(),
                    TransactionParam.WEIXIN_SUB_APP_ID, configParams.getSubAppid(),
                    TransactionParam.WEIXIN_SUB_APP_SECRET, configParams.getMiniSubAppSecret(),
                    TransactionParam.WEIXIN_MINI_SUB_APP_ID, configParams.getMiniSubAppid(),
                    TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, configParams.getMiniSubAppSecret());
        }
        return null;
    }
}
