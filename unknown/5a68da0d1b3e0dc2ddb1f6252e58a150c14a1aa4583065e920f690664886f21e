package com.wosai.upay.job.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.model.DO.McRuleGroup;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.BoundHashOperations;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 报备规则组
 *
 * <AUTHOR>
 * @date 2019-07-04
 */
@Data
public class RuleGroup {

    private String group_id;

    private String name;

    private String vendor;

    private String vendor_app;

    private Integer status;

    private List<RuleItem> rules;

    private Date create_at;

    private Date update_at;

    private String acquirer;

    public static RuleGroup fromMcRuleGroup(McRuleGroup mcRuleGroup, final BoundHashOperations<String, String, ContractRule> hashOperations) {
        RuleGroup ruleGroup = new RuleGroup();
        BeanUtils.copyProperties(mcRuleGroup, ruleGroup);
        JSONArray jsonArray = JSON.parseArray(mcRuleGroup.getRules());
        List<RuleItem> ruleItems = new ArrayList<>();
        for (Object json : jsonArray) {
            RuleItem ruleItem = buildRuleItem((JSONObject) json, hashOperations);
            if (ruleItem != null) {
                ruleItems.add(ruleItem);
            }
        }
        ruleGroup.setRules(ruleItems);
        return ruleGroup;
    }

    private static RuleItem buildRuleItem(JSONObject jsonObject, final BoundHashOperations<String, String, ContractRule> hashOperations) {
        RuleItem ruleItem = new RuleItem();
        ContractRule contractRule = hashOperations.get(jsonObject.getString("contract_rule"));
        if (contractRule == null) {
            return null;
        }
        copyProp(jsonObject, contractRule);

        ruleItem.setContractRule(contractRule);
        String dependOn = jsonObject.getString("depend_on");
        ruleItem.setDepend_on(WosaiStringUtils.isEmpty(dependOn) ? null : hashOperations.get(dependOn));
        return ruleItem;
    }

    /**
     * 规则组设置覆盖规则设置
     * @param jsonObject
     * @param contractRule
     */
    private static void copyProp(JSONObject jsonObject, ContractRule contractRule) {
        if(jsonObject.getInteger("retry") != null) {
            contractRule.setRetry(jsonObject.getInteger("retry"));
        }
        if(jsonObject.getBoolean("is_default") != null) {
            contractRule.setIs_default(jsonObject.getBoolean("is_default"));
        }
        if(jsonObject.getBoolean("is_insert") != null) {
            contractRule.setIs_insert(jsonObject.getBoolean("is_insert"));
        }
        if(jsonObject.getBoolean("is_insert_influ_ptask") != null) {
            contractRule.setIs_insert_influ_ptask(jsonObject.getBoolean("is_insert_influ_ptask"));
        }
        if(jsonObject.getBoolean("is_update") != null) {
            contractRule.setIs_update(jsonObject.getBoolean("is_update"));
        }
        if(jsonObject.getBoolean("is_update_influ_ptask") != null) {
            contractRule.setIs_update_influ_ptask(jsonObject.getBoolean("is_update_influ_ptask"));
        }
    }
}
