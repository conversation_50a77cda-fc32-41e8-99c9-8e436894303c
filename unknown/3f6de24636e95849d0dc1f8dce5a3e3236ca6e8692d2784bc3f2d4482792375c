package com.wosai.upay.job.biz;

import com.wosai.upay.bank.info.api.dto.IndustryConfigResDto;
import com.wosai.upay.bank.info.api.model.industryconfig.IndustryConfigQueryDTO;
import com.wosai.upay.bank.info.api.service.IndustryConfigService;
import com.wosai.upay.job.model.SettlementIdConfig;
import com.wosai.upay.merchant.contract.exception.ContractException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.wosai.upay.job.biz.WechatAuthBiz.NO_INDUSTRY_INFO;

@Component
@Slf4j
public class IndustryMappingCommonBiz {

    @Autowired
    private IndustryConfigService industryConfigService;


    public SettlementIdConfig getSettlementConfig(String industryId) {

        IndustryConfigQueryDTO dto = new IndustryConfigQueryDTO();
        dto.setIndustryId(industryId);
        IndustryConfigResDto resDto = industryConfigService.queryIndustryConfigWithBeanResult(dto);

        // 1 根据行业id获取code
        if (Objects.isNull(resDto) || Objects.isNull(resDto.getWeixin()) || Objects.isNull(resDto.getWeixin().getWeixinIndirect())) {
            throw new ContractException(NO_INDUSTRY_INFO, "报备异常,根据行业映射表 查找不到商户行业信息,行业名称:" + industryId);
        }
        SettlementIdConfig settlementIdConfig = new SettlementIdConfig();
        IndustryConfigResDto.WeiXin.WeiXinIndirect weixinIndirect = resDto.getWeixin().getWeixinIndirect();
        if (StringUtils.isNotEmpty(weixinIndirect.getMicro())) {
            settlementIdConfig.setPerson(weixinIndirect.getMicro());
        }
        if (StringUtils.isNotEmpty(weixinIndirect.getIndividual())) {
            settlementIdConfig.setIndividual(weixinIndirect.getIndividual());
        }
        if (StringUtils.isNotEmpty(weixinIndirect.getEnterprise())) {
            settlementIdConfig.setEnterprise(weixinIndirect.getEnterprise());
        }
        if (StringUtils.isNotEmpty(weixinIndirect.getInstitutions())) {
            settlementIdConfig.setInstitutions(weixinIndirect.getInstitutions());
        }
        if (StringUtils.isNotEmpty(weixinIndirect.getOhters())) {
            settlementIdConfig.setOthers(weixinIndirect.getOhters());
        }
        return settlementIdConfig;
    }

    /**
     * 获取支付宝间连mcc（万码支付宝mcc）
     * @param industryId
     * @return
     */
    public String getAliIndirectMcc(String industryId) {
        IndustryConfigResDto.Ali.AliIndirect aliIndirect = getAliIndirect(industryId);
        return Optional.ofNullable(aliIndirect.getMcc()).orElse("");
    }

    /**
     * 获取支付宝间连（万码支付宝）
     * @param industryId
     * @return
     */
    public String getAliIndirectWmAly(String industryId) {
        IndustryConfigResDto.Ali.AliIndirect aliIndirect = getAliIndirect(industryId);
        return Optional.ofNullable(aliIndirect.getWmAly()).orElse("");
    }

    private IndustryConfigResDto.Ali.AliIndirect getAliIndirect(String industryId) {
        IndustryConfigQueryDTO dto = new IndustryConfigQueryDTO();
        dto.setIndustryId(industryId);
        IndustryConfigResDto resDto = Optional.ofNullable(industryConfigService.queryIndustryConfigWithBeanResult(dto)).orElse(new IndustryConfigResDto());
        IndustryConfigResDto.Ali ali = Optional.ofNullable(resDto.getAli()).orElse(new IndustryConfigResDto.Ali());
        return Optional.ofNullable(ali.getAliIndirect()).orElse(new IndustryConfigResDto.Ali.AliIndirect());
    }
    
    public String getTongLianV2Code(String industryId) {
        IndustryConfigQueryDTO dto = new IndustryConfigQueryDTO();
        dto.setIndustryId(industryId);
        IndustryConfigResDto resDto = Optional.ofNullable(industryConfigService.queryIndustryConfigWithBeanResult(dto)).orElse(new IndustryConfigResDto());
        IndustryConfigResDto.TongLianV2 tongLianV2 = Optional.ofNullable(resDto.getTongLianV2()).orElse(new IndustryConfigResDto.TongLianV2());
        return Optional.ofNullable(tongLianV2.getTlCode()).orElse("");
    }


}
