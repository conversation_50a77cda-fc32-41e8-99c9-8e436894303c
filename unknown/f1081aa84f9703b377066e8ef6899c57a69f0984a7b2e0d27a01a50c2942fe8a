package com.wosai.upay.job.adapter.apollo;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Description:namespace为memo用的配置 文案转译相关的配置
 * <AUTHOR>
 * Date 2020/5/12 2:12 下午
 **/
@Component
@Data
@Accessors(chain = true)
public class MemoApolloConfig {

    @Value("${force-async-appid:false}")
    private Boolean forceAsync;

    @ApolloJsonValue("${skip-appid:[]}")
    private List<String> skipAppids;

    @ApolloJsonValue("${appid_infos:{}}")
    private Map appIdInfos;
}
