package com.wosai.upay.job.enume;

/**
 * Created by hzq on 19/8/26.
 */
public enum CancelStatus {


    WAIT("待取消", 1),
    FINISH("已取消", 2);

    private String context;
    private Integer val;

    CancelStatus(String context, Integer val) {
        this.context = context;
        this.val = val;
    }

    public static CancelStatus toStatus(Integer value) {
        if (value == null) return null;
        for (CancelStatus status : CancelStatus.values()) {
            if (status.getVal().equals(value)) {
                return status;
            }
        }
        return null;
    }


    public String getContext() {
        return context;
    }


    public Integer getVal() {
        return val;
    }


}
