package com.wosai.upay.job.helper;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

public class DateHelper {

    private DateHelper() {
        throw new IllegalStateException("不能初始化DateHelper");
    }

    public static boolean isOverOneMonth(Date targetDate) {
        // 将Date转换为LocalDate
        LocalDate date = targetDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算目标日期加一个月后的日期
        LocalDate oneMonthLater = date.plusMonths(1);
        // 判断是否超过一个月
        return !oneMonthLater.isAfter(currentDate);
    }
}
