/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class UpdateBankAccount extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -2162632550535399299L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"UpdateBankAccount\",\"namespace\":\"com.wosai.upay.job.avro\",\"fields\":[{\"name\":\"merchant_sn\",\"type\":\"string\",\"meta\":\"商户号\"},{\"name\":\"merchant_id\",\"type\":\"string\",\"meta\":\"商户id\"},{\"name\":\"acquirer\",\"type\":\"string\",\"meta\":\"收单机构\"},{\"name\":\"match\",\"type\":\"int\",\"meta\":\"收钱吧和收单机构银行卡是否匹配 0不匹配 1匹配\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<UpdateBankAccount> ENCODER =
      new BinaryMessageEncoder<UpdateBankAccount>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<UpdateBankAccount> DECODER =
      new BinaryMessageDecoder<UpdateBankAccount>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<UpdateBankAccount> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<UpdateBankAccount> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<UpdateBankAccount>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this UpdateBankAccount to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a UpdateBankAccount from a ByteBuffer. */
  public static UpdateBankAccount fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence acquirer;
  @Deprecated public int match;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public UpdateBankAccount() {}

  /**
   * All-args constructor.
   * @param merchant_sn The new value for merchant_sn
   * @param merchant_id The new value for merchant_id
   * @param acquirer The new value for acquirer
   * @param match The new value for match
   */
  public UpdateBankAccount(java.lang.CharSequence merchant_sn, java.lang.CharSequence merchant_id, java.lang.CharSequence acquirer, java.lang.Integer match) {
    this.merchant_sn = merchant_sn;
    this.merchant_id = merchant_id;
    this.acquirer = acquirer;
    this.match = match;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_sn;
    case 1: return merchant_id;
    case 2: return acquirer;
    case 3: return match;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_sn = (java.lang.CharSequence)value$; break;
    case 1: merchant_id = (java.lang.CharSequence)value$; break;
    case 2: acquirer = (java.lang.CharSequence)value$; break;
    case 3: match = (java.lang.Integer)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'acquirer' field.
   * @return The value of the 'acquirer' field.
   */
  public java.lang.CharSequence getAcquirer() {
    return acquirer;
  }

  /**
   * Sets the value of the 'acquirer' field.
   * @param value the value to set.
   */
  public void setAcquirer(java.lang.CharSequence value) {
    this.acquirer = value;
  }

  /**
   * Gets the value of the 'match' field.
   * @return The value of the 'match' field.
   */
  public java.lang.Integer getMatch() {
    return match;
  }

  /**
   * Sets the value of the 'match' field.
   * @param value the value to set.
   */
  public void setMatch(java.lang.Integer value) {
    this.match = value;
  }

  /**
   * Creates a new UpdateBankAccount RecordBuilder.
   * @return A new UpdateBankAccount RecordBuilder
   */
  public static com.wosai.upay.job.avro.UpdateBankAccount.Builder newBuilder() {
    return new com.wosai.upay.job.avro.UpdateBankAccount.Builder();
  }

  /**
   * Creates a new UpdateBankAccount RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new UpdateBankAccount RecordBuilder
   */
  public static com.wosai.upay.job.avro.UpdateBankAccount.Builder newBuilder(com.wosai.upay.job.avro.UpdateBankAccount.Builder other) {
    return new com.wosai.upay.job.avro.UpdateBankAccount.Builder(other);
  }

  /**
   * Creates a new UpdateBankAccount RecordBuilder by copying an existing UpdateBankAccount instance.
   * @param other The existing instance to copy.
   * @return A new UpdateBankAccount RecordBuilder
   */
  public static com.wosai.upay.job.avro.UpdateBankAccount.Builder newBuilder(com.wosai.upay.job.avro.UpdateBankAccount other) {
    return new com.wosai.upay.job.avro.UpdateBankAccount.Builder(other);
  }

  /**
   * RecordBuilder for UpdateBankAccount instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<UpdateBankAccount>
    implements org.apache.avro.data.RecordBuilder<UpdateBankAccount> {

    private java.lang.CharSequence merchant_sn;
    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence acquirer;
    private int match;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.job.avro.UpdateBankAccount.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.acquirer)) {
        this.acquirer = data().deepCopy(fields()[2].schema(), other.acquirer);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.match)) {
        this.match = data().deepCopy(fields()[3].schema(), other.match);
        fieldSetFlags()[3] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing UpdateBankAccount instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.job.avro.UpdateBankAccount other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.acquirer)) {
        this.acquirer = data().deepCopy(fields()[2].schema(), other.acquirer);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.match)) {
        this.match = data().deepCopy(fields()[3].schema(), other.match);
        fieldSetFlags()[3] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.UpdateBankAccount.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_sn = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.UpdateBankAccount.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.UpdateBankAccount.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_id = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.UpdateBankAccount.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'acquirer' field.
      * @return The value.
      */
    public java.lang.CharSequence getAcquirer() {
      return acquirer;
    }

    /**
      * Sets the value of the 'acquirer' field.
      * @param value The value of 'acquirer'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.UpdateBankAccount.Builder setAcquirer(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.acquirer = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'acquirer' field has been set.
      * @return True if the 'acquirer' field has been set, false otherwise.
      */
    public boolean hasAcquirer() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'acquirer' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.UpdateBankAccount.Builder clearAcquirer() {
      acquirer = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'match' field.
      * @return The value.
      */
    public java.lang.Integer getMatch() {
      return match;
    }

    /**
      * Sets the value of the 'match' field.
      * @param value The value of 'match'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.UpdateBankAccount.Builder setMatch(int value) {
      validate(fields()[3], value);
      this.match = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'match' field has been set.
      * @return True if the 'match' field has been set, false otherwise.
      */
    public boolean hasMatch() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'match' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.UpdateBankAccount.Builder clearMatch() {
      fieldSetFlags()[3] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public UpdateBankAccount build() {
      try {
        UpdateBankAccount record = new UpdateBankAccount();
        record.merchant_sn = fieldSetFlags()[0] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_id = fieldSetFlags()[1] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.acquirer = fieldSetFlags()[2] ? this.acquirer : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.match = fieldSetFlags()[3] ? this.match : (java.lang.Integer) defaultValue(fields()[3]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<UpdateBankAccount>
    WRITER$ = (org.apache.avro.io.DatumWriter<UpdateBankAccount>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<UpdateBankAccount>
    READER$ = (org.apache.avro.io.DatumReader<UpdateBankAccount>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
