package com.wosai.upay.job.dao.impl;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.dao.WeixinParamsUpdateApplyDao;
import com.wosai.upay.job.model.WeixinParamsUpdateApply;
import com.wosai.upay.job.util.StringUtil;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by lihebin on 2018/9/5.
 */
@Service
public class WeixinParamsUpdateApplyDaoImpl implements WeixinParamsUpdateApplyDao {
    private final static Logger log = LoggerFactory.getLogger(WeixinParamsUpdateApplyDaoImpl.class);


    @Resource(name = "merchantContractJdbcTemplate")
    JdbcTemplate merchantContractJdbcTemplate;


    @Override
    public Map getWeixinParamsUpdateApply(String weixinMerchantId) {
        String sql = " select * from weixin_params_update_apply where weixin_merchant_id = ? and deleted = 0";
        try {
            return merchantContractJdbcTemplate.queryForMap(sql, weixinMerchantId);
        } catch (Exception e) {
            return new HashMap();
        }
    }

    @Override
    public boolean saveWeixinParamsUpdateApply(String channelNo, String merchantSn, String weixinMerchantId, String merchantShortName, String servicePhone) {
        Map weixinParamsUpdateApply = getWeixinParamsUpdateApply(weixinMerchantId);
        if (!MapUtils.isEmpty(weixinParamsUpdateApply)) {
            return updateWeixinParamsUpdateApply(weixinMerchantId, merchantShortName, servicePhone);
        }
        weixinParamsUpdateApply = CollectionUtil.hashMap(
                WeixinParamsUpdateApply.CHANNEL_NO, channelNo,
                WeixinParamsUpdateApply.MERCHANT_SN, merchantSn,
                WeixinParamsUpdateApply.WEIXIN_MERCHANT_ID, weixinMerchantId,
                WeixinParamsUpdateApply.MERCHANT_SHORT_NAME, merchantShortName,
                WeixinParamsUpdateApply.SERVICE_PHONE, servicePhone
                );
        return insertWeixinParamsUpdateApply(weixinParamsUpdateApply);
    }


    private boolean insertWeixinParamsUpdateApply(Map params) {

        String id = UUID.randomUUID().toString();
        String merchantShortName = BeanUtil.getPropString(params, WeixinParamsUpdateApply.MERCHANT_SHORT_NAME);
        long shortNameMtime = 0;
        long mtime = 0;
        if (!StringUtil.empty(merchantShortName)) {
            shortNameMtime = System.currentTimeMillis();
        }
        params.put(WeixinParamsUpdateApply.SHORT_NAME_MTIME, shortNameMtime);
        String servicePhone = BeanUtil.getPropString(params, WeixinParamsUpdateApply.SERVICE_PHONE);
        if (!StringUtil.empty(servicePhone)) {
            mtime = System.currentTimeMillis();
        }
        params.put(DaoConstants.MTIME, mtime);
        String sql = "insert into weixin_params_update_apply (id, channel_no, merchant_sn, weixin_merchant_id, merchant_short_name, service_phone, ctime, mtime, short_name_mtime) value (:id, :channel_no, :merchant_sn, :weixin_merchant_id, :merchant_short_name, :service_phone, unix_timestamp()*1000, :mtime, :short_name_mtime)";
        try {
            NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(merchantContractJdbcTemplate);
            params.put(DaoConstants.ID, id);
            namedParameterJdbcTemplate.update(sql, params);
            return true;
        } catch (Exception e) {
            log.error("insertWeixinParamsUpdateApply:{},{}", params, e);
            return false;
        }
    }


    private boolean updateWeixinParamsUpdateApply(String weixinMerchantId, String merchantShortName, String servicePhone) {
        String sql ="update weixin_params_update_apply set version = version + 1";
        if (StringUtil.empty(merchantShortName) && StringUtil.empty(servicePhone)) {
            return false;
        }
        if (!StringUtil.empty(merchantShortName)) {
            sql = String.format("%s, short_name_mtime = unix_timestamp()*1000, merchant_short_name = '%s'", sql, merchantShortName);
        }
        if (!StringUtil.empty(servicePhone)) {
            sql = String.format("%s, mtime = unix_timestamp()*1000, service_phone = '%s'", sql, servicePhone);
        }
        sql = String.format("%s where weixin_merchant_id = ?", sql);
        try {
            merchantContractJdbcTemplate.update(sql, weixinMerchantId);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
