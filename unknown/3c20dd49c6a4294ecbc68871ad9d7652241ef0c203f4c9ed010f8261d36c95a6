package com.wosai.upay.job.model.DO;

import com.wosai.core.crypto.annotation.SensitiveData;
import com.wosai.core.crypto.annotation.SensitiveField;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

@Data
@SensitiveData
public class SelfOpenCcbDecp {

    /**
     * 待申请
     */
    public static final int WAIT_OPEN_STATUS = 0;
    /**
     * 开通中
     */
    public static final int PROCESS_OPEN_STATUS = 1;
    /**
     * 开通成功
     */
    public static final int SUCCESS_OPEN_STATUS = 2;
    /**
     * 开通失败
     */
    public static final int FAIL_OPEN_STATUS = 3;

    /**
     * DECP 的agent列表
     */
    public static final List<String> DECP_AGENTS = Arrays.asList("1026_*_*_false_true_0001", "1026_*_*_false_true_0002");


    private Long id;

    private String merchant_sn;

    private Integer open_status;

    private Long decp_id;

    @SensitiveField
    private String request_body;

    private String result;

    private Long ctime;

    private Long mtime;

}