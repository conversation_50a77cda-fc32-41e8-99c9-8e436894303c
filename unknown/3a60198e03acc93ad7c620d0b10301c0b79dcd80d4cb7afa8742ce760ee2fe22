package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.TaskMch;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/1/25 6:32 下午
 **/
public interface TaskMchMapper {

    int insert(TaskMch taskMch);

    /**
     * 通过mchId查询TaskMch
     *
     * @param mchId
     * @return
     */
    @Select("select * from task_mch where pay_merchant_id=#{mchId} order by time desc limit 20")
    List<TaskMch> selectByPayMerchantId(@Param("mchId") String mchId);

    /**
     * 通过taskId查询TaskMch
     *
     * @param taskId
     * @return
     */
    @Select("select * from task_mch where task_id = #{taskId} order by time desc limit 1")
    TaskMch selectTaskMchByTaskId(@Param("taskId") Long taskId);

    /**
     * 通过authApplyId查询TaskMch
     *
     * @param authApplyId
     * @return
     */
    @Select("select * from task_mch where auth_apply_id=#{authApplyId} order by time desc limit 100")
    List<TaskMch> selectByAuthApplyId(@Param("authApplyId") Long authApplyId);

    @Update("update task_mch set pay_merchant_id =#{subMchId} where sub_task_id = #{subTaskId} and task_id=#{taskId}")
    void updatePayMerchantId(@Param("subMchId") String subMchId, @Param("subTaskId") Long subTaskId, @Param("taskId") Long taskId);

}
