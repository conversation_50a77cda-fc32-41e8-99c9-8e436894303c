/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class AuthAndComboStatusChange extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 4877934550706227641L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"AuthAndComboStatusChange\",\"namespace\":\"com.wosai.upay.job.avro\",\"fields\":[{\"name\":\"merchant_sn\",\"type\":\"string\",\"meta\":\"商户号\"},{\"name\":\"merchant_id\",\"type\":\"string\",\"meta\":\"商户id\"},{\"name\":\"pre_status\",\"type\":[\"int\",\"null\"],\"meta\":\"旧状态\"},{\"name\":\"status\",\"type\":[\"int\",\"null\"],\"meta\":\"新状态\"},{\"name\":\"message\",\"type\":[\"string\",\"null\"],\"meta\":\"文案\"},{\"name\":\"source\",\"type\":[\"string\",\"null\"],\"meta\":\"来源,审核驳回还是独立申请\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<AuthAndComboStatusChange> ENCODER =
      new BinaryMessageEncoder<AuthAndComboStatusChange>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<AuthAndComboStatusChange> DECODER =
      new BinaryMessageDecoder<AuthAndComboStatusChange>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<AuthAndComboStatusChange> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<AuthAndComboStatusChange> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<AuthAndComboStatusChange>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this AuthAndComboStatusChange to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a AuthAndComboStatusChange from a ByteBuffer. */
  public static AuthAndComboStatusChange fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.Integer pre_status;
  @Deprecated public java.lang.Integer status;
  @Deprecated public java.lang.CharSequence message;
  @Deprecated public java.lang.CharSequence source;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public AuthAndComboStatusChange() {}

  /**
   * All-args constructor.
   * @param merchant_sn The new value for merchant_sn
   * @param merchant_id The new value for merchant_id
   * @param pre_status The new value for pre_status
   * @param status The new value for status
   * @param message The new value for message
   * @param source The new value for source
   */
  public AuthAndComboStatusChange(java.lang.CharSequence merchant_sn, java.lang.CharSequence merchant_id, java.lang.Integer pre_status, java.lang.Integer status, java.lang.CharSequence message, java.lang.CharSequence source) {
    this.merchant_sn = merchant_sn;
    this.merchant_id = merchant_id;
    this.pre_status = pre_status;
    this.status = status;
    this.message = message;
    this.source = source;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_sn;
    case 1: return merchant_id;
    case 2: return pre_status;
    case 3: return status;
    case 4: return message;
    case 5: return source;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_sn = (java.lang.CharSequence)value$; break;
    case 1: merchant_id = (java.lang.CharSequence)value$; break;
    case 2: pre_status = (java.lang.Integer)value$; break;
    case 3: status = (java.lang.Integer)value$; break;
    case 4: message = (java.lang.CharSequence)value$; break;
    case 5: source = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'pre_status' field.
   * @return The value of the 'pre_status' field.
   */
  public java.lang.Integer getPreStatus() {
    return pre_status;
  }

  /**
   * Sets the value of the 'pre_status' field.
   * @param value the value to set.
   */
  public void setPreStatus(java.lang.Integer value) {
    this.pre_status = value;
  }

  /**
   * Gets the value of the 'status' field.
   * @return The value of the 'status' field.
   */
  public java.lang.Integer getStatus() {
    return status;
  }

  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(java.lang.Integer value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'message' field.
   * @return The value of the 'message' field.
   */
  public java.lang.CharSequence getMessage() {
    return message;
  }

  /**
   * Sets the value of the 'message' field.
   * @param value the value to set.
   */
  public void setMessage(java.lang.CharSequence value) {
    this.message = value;
  }

  /**
   * Gets the value of the 'source' field.
   * @return The value of the 'source' field.
   */
  public java.lang.CharSequence getSource() {
    return source;
  }

  /**
   * Sets the value of the 'source' field.
   * @param value the value to set.
   */
  public void setSource(java.lang.CharSequence value) {
    this.source = value;
  }

  /**
   * Creates a new AuthAndComboStatusChange RecordBuilder.
   * @return A new AuthAndComboStatusChange RecordBuilder
   */
  public static com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder newBuilder() {
    return new com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder();
  }

  /**
   * Creates a new AuthAndComboStatusChange RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new AuthAndComboStatusChange RecordBuilder
   */
  public static com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder newBuilder(com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder other) {
    return new com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder(other);
  }

  /**
   * Creates a new AuthAndComboStatusChange RecordBuilder by copying an existing AuthAndComboStatusChange instance.
   * @param other The existing instance to copy.
   * @return A new AuthAndComboStatusChange RecordBuilder
   */
  public static com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder newBuilder(com.wosai.upay.job.avro.AuthAndComboStatusChange other) {
    return new com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder(other);
  }

  /**
   * RecordBuilder for AuthAndComboStatusChange instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<AuthAndComboStatusChange>
    implements org.apache.avro.data.RecordBuilder<AuthAndComboStatusChange> {

    private java.lang.CharSequence merchant_sn;
    private java.lang.CharSequence merchant_id;
    private java.lang.Integer pre_status;
    private java.lang.Integer status;
    private java.lang.CharSequence message;
    private java.lang.CharSequence source;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.pre_status)) {
        this.pre_status = data().deepCopy(fields()[2].schema(), other.pre_status);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.status)) {
        this.status = data().deepCopy(fields()[3].schema(), other.status);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.message)) {
        this.message = data().deepCopy(fields()[4].schema(), other.message);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.source)) {
        this.source = data().deepCopy(fields()[5].schema(), other.source);
        fieldSetFlags()[5] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing AuthAndComboStatusChange instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.job.avro.AuthAndComboStatusChange other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.pre_status)) {
        this.pre_status = data().deepCopy(fields()[2].schema(), other.pre_status);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.status)) {
        this.status = data().deepCopy(fields()[3].schema(), other.status);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.message)) {
        this.message = data().deepCopy(fields()[4].schema(), other.message);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.source)) {
        this.source = data().deepCopy(fields()[5].schema(), other.source);
        fieldSetFlags()[5] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_sn = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_id = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'pre_status' field.
      * @return The value.
      */
    public java.lang.Integer getPreStatus() {
      return pre_status;
    }

    /**
      * Sets the value of the 'pre_status' field.
      * @param value The value of 'pre_status'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder setPreStatus(java.lang.Integer value) {
      validate(fields()[2], value);
      this.pre_status = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'pre_status' field has been set.
      * @return True if the 'pre_status' field has been set, false otherwise.
      */
    public boolean hasPreStatus() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'pre_status' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder clearPreStatus() {
      pre_status = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * @return The value.
      */
    public java.lang.Integer getStatus() {
      return status;
    }

    /**
      * Sets the value of the 'status' field.
      * @param value The value of 'status'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder setStatus(java.lang.Integer value) {
      validate(fields()[3], value);
      this.status = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'status' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder clearStatus() {
      status = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'message' field.
      * @return The value.
      */
    public java.lang.CharSequence getMessage() {
      return message;
    }

    /**
      * Sets the value of the 'message' field.
      * @param value The value of 'message'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder setMessage(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.message = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'message' field has been set.
      * @return True if the 'message' field has been set, false otherwise.
      */
    public boolean hasMessage() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'message' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder clearMessage() {
      message = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'source' field.
      * @return The value.
      */
    public java.lang.CharSequence getSource() {
      return source;
    }

    /**
      * Sets the value of the 'source' field.
      * @param value The value of 'source'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder setSource(java.lang.CharSequence value) {
      validate(fields()[5], value);
      this.source = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'source' field has been set.
      * @return True if the 'source' field has been set, false otherwise.
      */
    public boolean hasSource() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'source' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.AuthAndComboStatusChange.Builder clearSource() {
      source = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public AuthAndComboStatusChange build() {
      try {
        AuthAndComboStatusChange record = new AuthAndComboStatusChange();
        record.merchant_sn = fieldSetFlags()[0] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_id = fieldSetFlags()[1] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.pre_status = fieldSetFlags()[2] ? this.pre_status : (java.lang.Integer) defaultValue(fields()[2]);
        record.status = fieldSetFlags()[3] ? this.status : (java.lang.Integer) defaultValue(fields()[3]);
        record.message = fieldSetFlags()[4] ? this.message : (java.lang.CharSequence) defaultValue(fields()[4]);
        record.source = fieldSetFlags()[5] ? this.source : (java.lang.CharSequence) defaultValue(fields()[5]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<AuthAndComboStatusChange>
    WRITER$ = (org.apache.avro.io.DatumWriter<AuthAndComboStatusChange>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<AuthAndComboStatusChange>
    READER$ = (org.apache.avro.io.DatumReader<AuthAndComboStatusChange>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
