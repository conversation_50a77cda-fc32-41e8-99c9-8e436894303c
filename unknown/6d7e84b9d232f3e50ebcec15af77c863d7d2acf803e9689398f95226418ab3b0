package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.ChannelActivity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ChannelActivityMapper {

    int insert(ChannelActivity record);

    ChannelActivity selectByPrimaryKey(@Param("id") Long id);

    List<ChannelActivity> listByStatusAndCtimeLimit(@Param("status") Integer status, @Param("start") String start, @Param("limit") Integer limit);

    List<ChannelActivity> listByStatusAndMtimeLimit(@Param("status") Integer status, @Param("start") String start, @Param("end") String end, @Param("limit") Integer limit,@Param("type") Integer type);

    int updateActivityStatusByIdAndPreStatus(@Param("id") Long id, @Param("after") int after, @Param("pre") int pre);

    int updateActivityStatusAndOutUidByIdAndPreStatus(@Param("id") Long id, @Param("out_uid") String out_uid, @Param("after") int after, @Param("pre") int pre);

    int updateActivityStatusAndResultByIdAndPreStatus(@Param("id") Long id, @Param("result") String result, @Param("after") int after, @Param("pre") int pre);

    ChannelActivity getLatestActivityByMchId(@Param("mch_id") String mch_id, @Param("not_id") Long notId);

    List<ChannelActivity> getByStatusAndCtimeLimit(@Param("status") Integer status ,  @Param("start") String start, @Param("limit") Integer limit);

    ChannelActivity getSuccessActivityByMchId(@Param("mch_id") String mch_id, @Param("type") int type);
}
