/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class MerchantContractSuccess extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -6589566091860729516L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"MerchantContractSuccess\",\"namespace\":\"com.wosai.upay.job.avro\",\"fields\":[{\"name\":\"merchant_sn\",\"type\":\"string\",\"meta\":\"商户号\"},{\"name\":\"merchant_id\",\"type\":\"string\",\"meta\":\"商户id\"},{\"name\":\"acquirer\",\"type\":\"string\",\"meta\":\"收单机构\"},{\"name\":\"bank_channel_flag\",\"type\":[\"null\",\"string\"],\"default\":null,\"meta\":\"通道是否为银行通道0:非银行通道 1:银行通道\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<MerchantContractSuccess> ENCODER =
      new BinaryMessageEncoder<MerchantContractSuccess>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<MerchantContractSuccess> DECODER =
      new BinaryMessageDecoder<MerchantContractSuccess>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<MerchantContractSuccess> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<MerchantContractSuccess> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<MerchantContractSuccess>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this MerchantContractSuccess to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a MerchantContractSuccess from a ByteBuffer. */
  public static MerchantContractSuccess fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence acquirer;
  @Deprecated public java.lang.CharSequence bank_channel_flag;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public MerchantContractSuccess() {}

  /**
   * All-args constructor.
   * @param merchant_sn The new value for merchant_sn
   * @param merchant_id The new value for merchant_id
   * @param acquirer The new value for acquirer
   * @param bank_channel_flag The new value for bank_channel_flag
   */
  public MerchantContractSuccess(java.lang.CharSequence merchant_sn, java.lang.CharSequence merchant_id, java.lang.CharSequence acquirer, java.lang.CharSequence bank_channel_flag) {
    this.merchant_sn = merchant_sn;
    this.merchant_id = merchant_id;
    this.acquirer = acquirer;
    this.bank_channel_flag = bank_channel_flag;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_sn;
    case 1: return merchant_id;
    case 2: return acquirer;
    case 3: return bank_channel_flag;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_sn = (java.lang.CharSequence)value$; break;
    case 1: merchant_id = (java.lang.CharSequence)value$; break;
    case 2: acquirer = (java.lang.CharSequence)value$; break;
    case 3: bank_channel_flag = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'acquirer' field.
   * @return The value of the 'acquirer' field.
   */
  public java.lang.CharSequence getAcquirer() {
    return acquirer;
  }

  /**
   * Sets the value of the 'acquirer' field.
   * @param value the value to set.
   */
  public void setAcquirer(java.lang.CharSequence value) {
    this.acquirer = value;
  }

  /**
   * Gets the value of the 'bank_channel_flag' field.
   * @return The value of the 'bank_channel_flag' field.
   */
  public java.lang.CharSequence getBankChannelFlag() {
    return bank_channel_flag;
  }

  /**
   * Sets the value of the 'bank_channel_flag' field.
   * @param value the value to set.
   */
  public void setBankChannelFlag(java.lang.CharSequence value) {
    this.bank_channel_flag = value;
  }

  /**
   * Creates a new MerchantContractSuccess RecordBuilder.
   * @return A new MerchantContractSuccess RecordBuilder
   */
  public static com.wosai.upay.job.avro.MerchantContractSuccess.Builder newBuilder() {
    return new com.wosai.upay.job.avro.MerchantContractSuccess.Builder();
  }

  /**
   * Creates a new MerchantContractSuccess RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new MerchantContractSuccess RecordBuilder
   */
  public static com.wosai.upay.job.avro.MerchantContractSuccess.Builder newBuilder(com.wosai.upay.job.avro.MerchantContractSuccess.Builder other) {
    return new com.wosai.upay.job.avro.MerchantContractSuccess.Builder(other);
  }

  /**
   * Creates a new MerchantContractSuccess RecordBuilder by copying an existing MerchantContractSuccess instance.
   * @param other The existing instance to copy.
   * @return A new MerchantContractSuccess RecordBuilder
   */
  public static com.wosai.upay.job.avro.MerchantContractSuccess.Builder newBuilder(com.wosai.upay.job.avro.MerchantContractSuccess other) {
    return new com.wosai.upay.job.avro.MerchantContractSuccess.Builder(other);
  }

  /**
   * RecordBuilder for MerchantContractSuccess instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<MerchantContractSuccess>
    implements org.apache.avro.data.RecordBuilder<MerchantContractSuccess> {

    private java.lang.CharSequence merchant_sn;
    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence acquirer;
    private java.lang.CharSequence bank_channel_flag;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.job.avro.MerchantContractSuccess.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.acquirer)) {
        this.acquirer = data().deepCopy(fields()[2].schema(), other.acquirer);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.bank_channel_flag)) {
        this.bank_channel_flag = data().deepCopy(fields()[3].schema(), other.bank_channel_flag);
        fieldSetFlags()[3] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing MerchantContractSuccess instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.job.avro.MerchantContractSuccess other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.acquirer)) {
        this.acquirer = data().deepCopy(fields()[2].schema(), other.acquirer);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.bank_channel_flag)) {
        this.bank_channel_flag = data().deepCopy(fields()[3].schema(), other.bank_channel_flag);
        fieldSetFlags()[3] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantContractSuccess.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_sn = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantContractSuccess.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantContractSuccess.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_id = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantContractSuccess.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'acquirer' field.
      * @return The value.
      */
    public java.lang.CharSequence getAcquirer() {
      return acquirer;
    }

    /**
      * Sets the value of the 'acquirer' field.
      * @param value The value of 'acquirer'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantContractSuccess.Builder setAcquirer(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.acquirer = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'acquirer' field has been set.
      * @return True if the 'acquirer' field has been set, false otherwise.
      */
    public boolean hasAcquirer() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'acquirer' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantContractSuccess.Builder clearAcquirer() {
      acquirer = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'bank_channel_flag' field.
      * @return The value.
      */
    public java.lang.CharSequence getBankChannelFlag() {
      return bank_channel_flag;
    }

    /**
      * Sets the value of the 'bank_channel_flag' field.
      * @param value The value of 'bank_channel_flag'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantContractSuccess.Builder setBankChannelFlag(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.bank_channel_flag = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'bank_channel_flag' field has been set.
      * @return True if the 'bank_channel_flag' field has been set, false otherwise.
      */
    public boolean hasBankChannelFlag() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'bank_channel_flag' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantContractSuccess.Builder clearBankChannelFlag() {
      bank_channel_flag = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public MerchantContractSuccess build() {
      try {
        MerchantContractSuccess record = new MerchantContractSuccess();
        record.merchant_sn = fieldSetFlags()[0] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_id = fieldSetFlags()[1] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.acquirer = fieldSetFlags()[2] ? this.acquirer : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.bank_channel_flag = fieldSetFlags()[3] ? this.bank_channel_flag : (java.lang.CharSequence) defaultValue(fields()[3]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<MerchantContractSuccess>
    WRITER$ = (org.apache.avro.io.DatumWriter<MerchantContractSuccess>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<MerchantContractSuccess>
    READER$ = (org.apache.avro.io.DatumReader<MerchantContractSuccess>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
