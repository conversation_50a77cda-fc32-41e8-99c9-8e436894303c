package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;


/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/11/24 6:31 PM
 **/
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class ContractAlipayServiceImpl implements ContractAlipayService {

    private static String LEVEL1 = "INDIRECT_LEVEL_M1";
    private static String LEVEL2 = "INDIRECT_LEVEL_M2";

    @Autowired
    BlueSeaService blueSeaService;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Override
    public ContractResponse configServiceCode(String merchantSn) {
        ContractResponse response = new ContractResponse();
        MerchantProviderParams useAlipayParam = merchantProviderParamsMapper.getUseAlipayParam(merchantSn);
        if (ObjectUtils.isEmpty(useAlipayParam)){
            response.setMsg("没有成功的支付宝商户号");
            return response;
        }
        try {
            blueSeaService.appendServiceCode(useAlipayParam.getPay_merchant_id());
            response.setSuccess(true);
            return response;
        }catch (Throwable e){
            log.error("预授权配置失败:{},{}", merchantSn, e.getMessage());
            response.setMsg(e.getMessage());
            return response;
        }
    }
}