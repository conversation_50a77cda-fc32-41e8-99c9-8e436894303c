package com.wosai.upay.job.model.payLater;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wosai.upay.job.Constants.ZftMerchantConstant;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Map;

@Data
@Accessors(chain = true)
public class ZftMerchantApply {
    private Long id;

    private String merchant_sn;

    private Integer status;

    private String smid;

    private String account;

    private Date create_at;

    private Date update_at;

    private String result;

    private String form_body;

    private String extra;

    public Map<String,Object> getExtraMap() {
        return JSONObject.parseObject(extra,Map.class);
    }



    public Boolean isFinish(Integer status) {
        boolean contains = Lists.newArrayList(ZftMerchantConstant.Status.SUCCESS, ZftMerchantConstant.Status.FAIL).contains(status);
        return contains;
    }
}