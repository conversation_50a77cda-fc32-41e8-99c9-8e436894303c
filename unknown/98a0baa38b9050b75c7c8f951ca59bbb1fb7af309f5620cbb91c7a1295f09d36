package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.merchant.contract.service.FuyouService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotBlank;


/**
 * 费率服务
 *
 * <AUTHOR>
 * @date 2024/10/11 09:28
 */
@Service
@AutoJsonRpcServiceImpl
public class FeeRateServiceImpl implements FeeRateService {


    @Autowired
    FuyouService fuyouService;

    /**
     * 根据富友模板获取费率信息
     *
     * @param templateCd   富友模板id
     * @return 枚举映射列表
     */
    @Override
    public String getFeeRateByTemplate(@NotBlank(message = "富友模板不能为空") String templateCd) {
        return fuyouService.getFeeRateByTemplate(templateCd);
    }

}
