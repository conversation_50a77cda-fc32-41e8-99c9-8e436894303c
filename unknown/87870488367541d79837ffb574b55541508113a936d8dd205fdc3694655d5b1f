package com.wosai.upay.job.model.DO;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.model.MchSnapshot;
import com.wosai.upay.job.model.Process;
import com.wosai.upay.job.model.TerminalInfo;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
@Data
@Accessors(chain = true)
public class BlueSeaTask {
    private Long id;

    private Long audit_id;

    private Long apply_id;

    private String ali_mch_id;

    private String merchant_sn;

    private String merchant_id;

    private String store_sn;

    private Integer retry;

    private Integer type;

    private Integer status;

    private String description;

    private Date create_at;

    private Date update_at;

    private Date priority;

    private String ali_shop_order_id;

    private String process;

    private String form_body;

    private String mch_snapshot;

    private String terminal_info;

    private String activity_order_id;
    //校园团餐修改shop_category的申请单id
    private String change_order_id;





    public List<Process> getProcessList() {
        if(StringUtils.isEmpty(process)) {
            return Lists.newArrayList();
        }
        final List list = JSONObject.parseObject(process, List.class);
        return (List<Process>) list.stream().map(x-> JSONObject.parseObject(JSONArray.toJSONString(x), Process.class)).collect(Collectors.toList());
    }

    public MchSnapshot getMchSnapshot() {
        return  JSONObject.parseObject(mch_snapshot, MchSnapshot.class);
    }

    public List<TerminalInfo> getTerminalInfo(){
        if (Objects.isNull(terminal_info)){
            return Lists.newArrayList();
        }
        final List list = JSONObject.parseObject(terminal_info, List.class);
        return (List<TerminalInfo>)list.stream().map(x-> JSONObject.parseObject(JSONArray.toJSONString(x), TerminalInfo.class)).collect(Collectors.toList());
    }

    public Boolean isFinish(){
        return status == BlueSeaConstant.SUCCESS || status == BlueSeaConstant.FAIL;
    }

    public Map getFormBody(){
        return JSON.parseObject(form_body, Map.class);
    }
}