package com.wosai.upay.job.model.guotong;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/5
 */
@Data
public class GuotongAuditStatusResultModel {

    /**
     * 审核模式
     * 1 自动审核 0人工审核
     */
    private String auditType;
    /**
     * 审核状态（枚举）
     * 0暂存、1审核中、2审核通过、3审核不通过
     */
    private String usrStatus;
    /**
     * 0暂存、1审核中、2审核通过、3审核不通过
     */
    private String usrStatusText;
    /**
     * 审核结果
     * 00审核通过；
     * 01营业执照模糊 02营业执照注册号填写错误 03法人身份证号码填写错误 04法人姓名填写错误 05未上传法人身份证照片 06翻拍无效，请实物拍摄 07组合照缺失或不符合要求
     * 08营业执照已注销或经营异常 09营业资质不符合要求 10结算卡照片与通码结算信息不一致 11未上传结算人身份证正面 12未上传结算人身份证反面 13结算人身份证已过期 14结算人身份证照上传错误
     * 15结算人身份证照模糊，请重新提交 16结算人身份证照无法查看，请重新提交 17结算人姓名录入有误 18结算人身份证号录入有误 19结算人身份证有效期填写有误 20未上传结算卡照 21结算卡上传错误
     * 22结算卡号录入有误 23结算卡照模糊，请重新提交 24结算卡照无法查看，请重新提交 25请上传对公材料（如开户许可证、印鉴卡等） 26开户银行选择有误 27结算卡状态异常 28营业执照上传错误，非该商户营业执照
     * 29未上传结算人身份证正面 30命中风险mcc黑名单
     */
    private String authResult;
    /**
     * 未通过原因
     */
    private String notThrowReason;
    /**
     * 审核类型
     * 0正常入网，2信息修改，4结算卡修改。空值默认查询正常入网
     */
    private String authType;

    public boolean auditPending() {
        return "0".equals(usrStatus);
    }

    public boolean auditInProcess() {
        return "1".equals(usrStatus);
    }

    public boolean auditSuccess() {
        return "2".equals(usrStatus);
    }

    public boolean auditFail() {
        return "3".equals(usrStatus);
    }
}
