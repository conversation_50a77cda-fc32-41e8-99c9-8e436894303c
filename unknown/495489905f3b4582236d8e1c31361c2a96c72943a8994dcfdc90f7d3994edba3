package com.wosai.upay.job.monitor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.Markers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
@Slf4j
public class MonitorLog {
    private ObjectMapper mapper = new ObjectMapper();

    @Autowired
    MerchantService merchantService;

    @Autowired
    private ChatBotUtil chatBotUtil;

    /**
     * subTask调用第三方日志
     */
    public void recordMonitor(ContractSubTask subTask, ContractResponse response, Long cost) {
        try {
            int code = response.getCode();
            if (code == 999) {
                code = 200;
            }
            String eventName = MonitorObject.getLogEventName(subTask);
            if (StringUtil.empty(eventName)) {
                return;
            }
            log.info(mapper.writeValueAsString(new MonitorObject()
                    .setSn(subTask.getMerchant_sn())
                    .setEvent(eventName)
                    .setCost(cost)
                    .setStatus(code)
                    .setMessage(response.getMessage())));
        } catch (Exception e) {
            log.error("monitor exception e", e);
        }
    }


    public void recordObject(MonitorObject monitorObject) {
        try {
            recordMonitor(monitorObject.getEvent(), mapper.writeValueAsString(monitorObject));
        } catch (Exception e) {
            log.error("monitor exception e", e);
        }
    }

    public void recordMonitor(String event, String message) {
        if (chatBotUtil.skip(event + message)) {
            return;
        }

        Map<String, Object> toAppendEntriesMap = new HashMap<>();
        toAppendEntriesMap.put("event", event);

        try {
            log.info(Markers.appendEntries(toAppendEntriesMap), message);
        } catch (Exception e) {
            log.error("monitor exception e", e);
        }
    }
}
