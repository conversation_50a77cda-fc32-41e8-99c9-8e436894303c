package com.wosai.upay.job.enume;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
public enum AgreementEnum implements ITextValueEnum<String> {

    /**
     * 拉卡拉特约商户支付服务合作协议
     */
    LKL_PAYMENT("lkl_payment", "拉卡拉特约商户支付服务合作协议"),
    /**
     * 通联支付扫码支付商户注册及服务协议
     */
    TL_PAYMENT("tl_payment", ""),
    /**
     * 收钱吧信用产品技术服务协议
     */
    TYPE_SQB_SESAMECREDIT("sqb_sesamecredit", "收钱吧信用产品技术服务协议")
    ;

    private String value;
    private String text;

    AgreementEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
