package com.wosai.upay.job.enume;

/**
 * context的作用是根据文案和apollo的配置进行转译
 * <AUTHOR>
 * @date 2020/12/18
 */
public enum WeixinDirectApplyStatus {

    //开始状态
    UN_SUBMIT("未提交", 0),

    IN_SP_AUDITING("运营审核中", 10),

    IN_WEIXIN_AUDITING("微信审核中", 20),

    WAIT_FOR_VERIFY("待账户验证", 30),

    WAIT_FOR_SIGN("待签约", 40),

    IN_OPENING_PERMISSION("开通权限中", 50),

    APPLY_ACCEPTED("开通成功", 60),

    APPLY_REJECTED("审核驳回", 70);


    private String context;
    private Integer val;

    WeixinDirectApplyStatus(String context, Integer val) {
        this.context = context;
        this.val = val;
    }

    public static WeixinDirectApplyStatus fromValuetoStatus(Integer value) {
        if (value == null) {
            return null;
        }
        for (WeixinDirectApplyStatus status : WeixinDirectApplyStatus.values()) {
            if (status.getVal().equals(value)) {
                return status;
            }
        }
        return null;
    }


    public String getContext() {
        return context;
    }


    public Integer getVal() {
        return val;
    }

}
