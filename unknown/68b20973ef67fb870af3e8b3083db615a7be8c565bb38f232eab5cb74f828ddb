package com.wosai.upay.job.xxljob.spring;

import com.wosai.upay.job.xxljob.JobHandlerRegistry;
import com.wosai.upay.job.xxljob.JobThreadPoolExecutorRegistry;
import com.wosai.upay.job.xxljob.template.JobHandler;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import org.springframework.beans.factory.config.BeanPostProcessor;

// 通过BeanPostProcessor自动发现并注册处理器
public class JobHandlerPostProcessor implements BeanPostProcessor {
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) {
        if (bean instanceof JobHandler) {
            JobHandlerRegistry.registerJobHandler(beanName, (JobHandler) bean);
        } else if (bean instanceof JobThreadPoolExecutor) {
            JobThreadPoolExecutorRegistry.registerJobThreadPoolExecutor(beanName, (JobThreadPoolExecutor) bean);
        }
        return bean;
    }
}
