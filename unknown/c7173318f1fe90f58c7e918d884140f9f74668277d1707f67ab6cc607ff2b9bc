package com.wosai.upay.job.model.DO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class McBatchTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public McBatchTaskExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOperator_idIsNull() {
            addCriterion("operator_id is null");
            return (Criteria) this;
        }

        public Criteria andOperator_idIsNotNull() {
            addCriterion("operator_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperator_idEqualTo(String value) {
            addCriterion("operator_id =", value, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_idNotEqualTo(String value) {
            addCriterion("operator_id <>", value, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_idGreaterThan(String value) {
            addCriterion("operator_id >", value, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_idGreaterThanOrEqualTo(String value) {
            addCriterion("operator_id >=", value, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_idLessThan(String value) {
            addCriterion("operator_id <", value, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_idLessThanOrEqualTo(String value) {
            addCriterion("operator_id <=", value, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_idLike(String value) {
            addCriterion("operator_id like", value, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_idNotLike(String value) {
            addCriterion("operator_id not like", value, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_idIn(List<String> values) {
            addCriterion("operator_id in", values, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_idNotIn(List<String> values) {
            addCriterion("operator_id not in", values, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_idBetween(String value1, String value2) {
            addCriterion("operator_id between", value1, value2, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_idNotBetween(String value1, String value2) {
            addCriterion("operator_id not between", value1, value2, "operator_id");
            return (Criteria) this;
        }

        public Criteria andOperator_nameIsNull() {
            addCriterion("operator_name is null");
            return (Criteria) this;
        }

        public Criteria andOperator_nameIsNotNull() {
            addCriterion("operator_name is not null");
            return (Criteria) this;
        }

        public Criteria andOperator_nameEqualTo(String value) {
            addCriterion("operator_name =", value, "operator_name");
            return (Criteria) this;
        }

        public Criteria andOperator_nameNotEqualTo(String value) {
            addCriterion("operator_name <>", value, "operator_name");
            return (Criteria) this;
        }

        public Criteria andOperator_nameGreaterThan(String value) {
            addCriterion("operator_name >", value, "operator_name");
            return (Criteria) this;
        }

        public Criteria andOperator_nameGreaterThanOrEqualTo(String value) {
            addCriterion("operator_name >=", value, "operator_name");
            return (Criteria) this;
        }

        public Criteria andOperator_nameLessThan(String value) {
            addCriterion("operator_name <", value, "operator_name");
            return (Criteria) this;
        }

        public Criteria andOperator_nameLessThanOrEqualTo(String value) {
            addCriterion("operator_name <=", value, "operator_name");
            return (Criteria) this;
        }

        public Criteria andOperator_nameLike(String value) {
            addCriterion("operator_name like", value, "operator_name");
            return (Criteria) this;
        }

        public Criteria andOperator_nameNotLike(String value) {
            addCriterion("operator_name not like", value, "operator_name");
            return (Criteria) this;
        }

        public Criteria andOperator_nameIn(List<String> values) {
            addCriterion("operator_name in", values, "operator_name");
            return (Criteria) this;
        }

        public Criteria andOperator_nameNotIn(List<String> values) {
            addCriterion("operator_name not in", values, "operator_name");
            return (Criteria) this;
        }

        public Criteria andOperator_nameBetween(String value1, String value2) {
            addCriterion("operator_name between", value1, value2, "operator_name");
            return (Criteria) this;
        }

        public Criteria andOperator_nameNotBetween(String value1, String value2) {
            addCriterion("operator_name not between", value1, value2, "operator_name");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andEffect_timeIsNull() {
            addCriterion("effect_time is null");
            return (Criteria) this;
        }

        public Criteria andEffect_timeIsNotNull() {
            addCriterion("effect_time is not null");
            return (Criteria) this;
        }

        public Criteria andEffect_timeEqualTo(Date value) {
            addCriterion("effect_time =", value, "effect_time");
            return (Criteria) this;
        }

        public Criteria andEffect_timeNotEqualTo(Date value) {
            addCriterion("effect_time <>", value, "effect_time");
            return (Criteria) this;
        }

        public Criteria andEffect_timeGreaterThan(Date value) {
            addCriterion("effect_time >", value, "effect_time");
            return (Criteria) this;
        }

        public Criteria andEffect_timeGreaterThanOrEqualTo(Date value) {
            addCriterion("effect_time >=", value, "effect_time");
            return (Criteria) this;
        }

        public Criteria andEffect_timeLessThan(Date value) {
            addCriterion("effect_time <", value, "effect_time");
            return (Criteria) this;
        }

        public Criteria andEffect_timeLessThanOrEqualTo(Date value) {
            addCriterion("effect_time <=", value, "effect_time");
            return (Criteria) this;
        }

        public Criteria andEffect_timeIn(List<Date> values) {
            addCriterion("effect_time in", values, "effect_time");
            return (Criteria) this;
        }

        public Criteria andEffect_timeNotIn(List<Date> values) {
            addCriterion("effect_time not in", values, "effect_time");
            return (Criteria) this;
        }

        public Criteria andEffect_timeBetween(Date value1, Date value2) {
            addCriterion("effect_time between", value1, value2, "effect_time");
            return (Criteria) this;
        }

        public Criteria andEffect_timeNotBetween(Date value1, Date value2) {
            addCriterion("effect_time not between", value1, value2, "effect_time");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idIsNull() {
            addCriterion("task_apply_log_id is null");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idIsNotNull() {
            addCriterion("task_apply_log_id is not null");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idEqualTo(String value) {
            addCriterion("task_apply_log_id =", value, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idNotEqualTo(String value) {
            addCriterion("task_apply_log_id <>", value, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idGreaterThan(String value) {
            addCriterion("task_apply_log_id >", value, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idGreaterThanOrEqualTo(String value) {
            addCriterion("task_apply_log_id >=", value, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idLessThan(String value) {
            addCriterion("task_apply_log_id <", value, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idLessThanOrEqualTo(String value) {
            addCriterion("task_apply_log_id <=", value, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idLike(String value) {
            addCriterion("task_apply_log_id like", value, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idNotLike(String value) {
            addCriterion("task_apply_log_id not like", value, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idIn(List<String> values) {
            addCriterion("task_apply_log_id in", values, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idNotIn(List<String> values) {
            addCriterion("task_apply_log_id not in", values, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idBetween(String value1, String value2) {
            addCriterion("task_apply_log_id between", value1, value2, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andTask_apply_log_idNotBetween(String value1, String value2) {
            addCriterion("task_apply_log_id not between", value1, value2, "task_apply_log_id");
            return (Criteria) this;
        }

        public Criteria andCreate_atIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreate_atIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreate_atEqualTo(Date value) {
            addCriterion("create_at =", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atGreaterThan(Date value) {
            addCriterion("create_at >", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atLessThan(Date value) {
            addCriterion("create_at <", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atIn(List<Date> values) {
            addCriterion("create_at in", values, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "create_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdate_atIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdate_atEqualTo(Date value) {
            addCriterion("update_at =", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atGreaterThan(Date value) {
            addCriterion("update_at >", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atLessThan(Date value) {
            addCriterion("update_at <", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atIn(List<Date> values) {
            addCriterion("update_at in", values, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "update_at");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}