package com.wosai.upay.job.xxljob.spring;

import com.wosai.upay.job.xxljob.JobThreadPoolExecutorRegistry;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutorMonitor;
import org.springframework.context.SmartLifecycle;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2025/3/17
 */
public class JobHandlerLifeCycle implements SmartLifecycle {

    private final AtomicBoolean running = new AtomicBoolean(false);
    @Override
    public void start() {
        if (this.running.compareAndSet(false, true)) {
            JobThreadPoolExecutorMonitor.startMonitor();
        }
    }

    @Override
    public void stop() {
        if (this.running.compareAndSet(true, false)) {
            JobThreadPoolExecutorMonitor.stopMonitor();
            JobThreadPoolExecutorRegistry.stopAll();
        }
    }

    @Override
    public boolean isRunning() {
        return running.get();
    }
}
