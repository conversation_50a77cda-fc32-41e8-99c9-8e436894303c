package com.wosai.upay.job.model.DO;

import com.wosai.core.crypto.annotation.SensitiveData;
import com.wosai.core.crypto.annotation.SensitiveField;
import lombok.Data;

@Data
@SensitiveData
public class OpenCcbDecp {

    /**
     * 返回值中的responseParam中的key
     */
    public static final String DECP_ID = "decp_id";

    /**
     * 开通状态
     */
    public static final String STATUS = "status";

    private Long id;

    @SensitiveField
    private String identity;

    @SensitiveField
    private String number;

    private Integer status;

    private Long ctime;

    private Long mtime;

    private Long version;

    @SensitiveField
    private String request_body;

    @SensitiveField
    private String response_body;
}