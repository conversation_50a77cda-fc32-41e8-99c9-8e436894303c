package com.wosai.upay.job.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-07-29
 */
@Data
@Accessors(chain = true)
public class SendBusinessLogDto {

    private Map change_before;

    private Map change_after;

    private String table_name;

    /**
     * 对于商户日志来讲，这个是商户id
     */
    private String object_id;

    private String business_object_code;

    private String business_function_code;

    private int op_type;

    /**
     * 不管是否变化都必须记录的字段
     */
    private Collection<String> needColumns;

    private String user_id;

    private String user_name;

    private String remark;
}
