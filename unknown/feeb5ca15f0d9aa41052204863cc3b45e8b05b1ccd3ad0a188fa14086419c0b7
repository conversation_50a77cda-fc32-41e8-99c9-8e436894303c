package com.wosai.upay.job.xxljob;

import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.xxljob.model.JobHandlerTypeEnum;
import com.wosai.upay.job.xxljob.template.BatchJobHandler;
import com.wosai.upay.job.xxljob.template.DirectJobHandler;
import com.wosai.upay.job.xxljob.template.JobHandler;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/3/14
 */
@Slf4j
public class JobThreadPoolExecutorRegistry {

    public static final Map<String, JobThreadPoolExecutor> JOB_THREAD_POOL_EXECUTOR_MAP = new ConcurrentHashMap<>();

    /**
     * 注册 JobThreadPoolExecutor。
     * @param poolName      线程池名称
     * @param jobThreadPoolExecutor 线程池
     */
    public static void registerJobThreadPoolExecutor(String poolName, JobThreadPoolExecutor jobThreadPoolExecutor) {
        if (poolName == null || poolName.isEmpty()) {
            throw new IllegalArgumentException("poolName cannot be null or empty");
        }
        if (jobThreadPoolExecutor == null) {
            throw new IllegalArgumentException("jobThreadPoolExecutor cannot be null");
        }
        if (JOB_THREAD_POOL_EXECUTOR_MAP.containsKey(poolName)) {
            throw new IllegalArgumentException("JobThreadPoolExecutor already exists for poolName: " + poolName);
        }
        JOB_THREAD_POOL_EXECUTOR_MAP.put(poolName, jobThreadPoolExecutor);
    }

    public static Map<String, JobThreadPoolExecutor> getAllExecutors() {
        return JOB_THREAD_POOL_EXECUTOR_MAP;
    }

    public static void stopAll() {
        for (JobThreadPoolExecutor jobThreadPoolExecutor : JOB_THREAD_POOL_EXECUTOR_MAP.values()) {
            try {
                log.info("关闭定时任务线程池：{}", jobThreadPoolExecutor.getPoolName());
                jobThreadPoolExecutor.shutdown();
                if (!jobThreadPoolExecutor.awaitTermination(jobThreadPoolExecutor.getAwaitTimeInSeconds(), TimeUnit.SECONDS)) {
                    log.warn("{} 任务未在规定时间完成", jobThreadPoolExecutor.getPoolName());
                }
            } catch (InterruptedException ex) {
                log.error("stopAll error {}", jobThreadPoolExecutor.getPoolName(), ex);
            }
        }
    }
}
