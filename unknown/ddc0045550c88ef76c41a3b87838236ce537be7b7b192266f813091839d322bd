package com.wosai.upay.job.model.ums;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Data
public class UmsImportParamsExcel implements IExcelModel, IExcelDataModel {

    @Excel(name = "收钱吧商户号", width = 20)
    @NotEmpty(message = "商户号不能为空")
    private String merchantSn;

    @Excel(name = "BSC银商商户号", width = 20)
    @NotEmpty(message = "BSC银商商户号不能为空")
    private String bscUmsSn;

    @Excel(name = "BSC银商终端号", width = 20)
    @NotEmpty(message = "BSC银商终端号不能为空")
    private String bscTerm;

    @Excel(name = "BSC微信商户号", width = 20)
    @NotEmpty(message = "BSC微信商户号不能为空")
    private String bscWx;

    @Excel(name = "BSC支付宝商户号", width = 20)
    @NotEmpty(message = "BSC支付宝商户号不能为空")
    private String bscAli;

    @Excel(name = "CSB银商商户号", width = 20)
    @NotEmpty(message = "CSB银商商户号不能为空")
    private String csbUmsSn;

    @Excel(name = "CSB银商终端号", width = 20)
    @NotEmpty(message = "CSB银商终端号不能为空")
    private String csbTerm;

    @Excel(name = "CSB微信商户号", width = 20)
    @NotEmpty(message = "CSB微信商户号不能为空")
    private String csbWx;

    @Excel(name = "CSB支付宝商户号", width = 20)
    @NotEmpty(message = "CSB支付宝商户号不能为空")
    private String csbAli;

    // Excel导入结果信息
    @Excel(name = "导入结果")
    private String message;

    // 行号
    private int rowNum;

    // 错误信息
    private String errorMsg;

    @Override
    public int getRowNum() {
        return rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    @Override
    public void setErrorMsg(String msg) {
        this.errorMsg = msg;
    }
}
