package com.wosai.upay.job.enume;

/**
 * <AUTHOR>
 * @date 2020/12/22
 */
public enum AliDirectApplyStatus {

    //开始状态
    UN_SUBMIT("未提交", 0),
    /**
     * 创建了事务
     */
    WAIT_FOR_SUBMITTING("申请单待提交", 10),
    /**
     * 提交了信息
     */
    ALREADY_SUBMITTED("申请单已提交", 15),
    /**
     * 提交了事务
     */
    IN_ALI_AUDITING("支付宝审核中", 30),
    /**
     * 待签约和待授权的状态可能不会出现，
     * 支付宝审核完成，签约和回调在5分钟之内完成，那还没来的及查支付宝状态，
     * 直接从审核中就到成功了
     */
    WAIT_FOR_SIGN("待商户签约", 40),

    WAIT_FOR_AUTHORIZATION("待支付宝授权", 50),

    APPLY_ACCEPTED("开通成功", 60),

    APPLY_REJECTED("审核驳回", 70);


    private String context;
    private Integer val;

    AliDirectApplyStatus(String context, Integer val) {
        this.context = context;
        this.val = val;
    }

    public static AliDirectApplyStatus fromValuetoStatus(Integer value) {
        if (value == null) {
            return null;
        }
        for (AliDirectApplyStatus status : AliDirectApplyStatus.values()) {
            if (status.getVal().equals(value)) {
                return status;
            }
        }
        return null;
    }


    public String getContext() {
        return context;
    }


    public Integer getVal() {
        return val;
    }
}
