package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.provider.PsbcParam;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import com.wosai.upay.merchant.contract.model.weixin.SubdevConfigResp;
import com.wosai.upay.merchant.contract.service.PsbcService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/6/8 11:09
 */
@Component("psbc-biz")
public class PsbcAcquirerBiz implements IAcquirerBiz {
    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private PsbcService psbcService;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private RuleContext ruleContext;

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_PSBC;
    }

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        return null;
    }

    @Override
    public String getNormalWxRule() {
        return ContractRuleConstants.PSBC_NORMAL_WEIXIN_RULE;
    }


    @Override
    public WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        PsbcParam psbcParam = contractParamsBiz.buildContractParams(String.valueOf(providerParams.getProvider()), providerParams.getPayway(), providerParams.getChannel_no(), PsbcParam.class);
        SubdevConfigResp subdevConfigResp = psbcService.queryWeChatSubDevConfig(psbcParam, providerParams.getPay_merchant_id());
        //邮储进件数据
        final String merchantSn = providerParams.getMerchant_sn();
        final List<ContractSubTask> subTasks = contractSubTaskMapper.findTasksByMerchantAndPayway(
                new ContractSubTaskReq().setPayway(PaywayEnum.ACQUIRER.getValue()).setMerchantSn(merchantSn).setTaskType(ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
        );
        List<ContractSubTask> psbc = subTasks.stream().filter(subTask -> Objects.equals(subTask.getChannel(), ChannelEnum.PSBC.getValue()))
                .limit(1)
                .collect(Collectors.toList());
        Map requestBody = JSON.parseObject(psbc.get(0).getRequest_body());
        final String name = MapUtils.getString(requestBody, "merName");
        return new WxMchInfo().setMchInfo(new MchInfo().setMerchant_name(name)).setSubdevConfig(subdevConfigResp);
    }

    @Override
    public AlipayMchInfo getAlipayMchInfo(MerchantProviderParams providerParams) {
        final String merchantSn = providerParams.getMerchant_sn();
        final List<ContractSubTask> subTasks = contractSubTaskMapper.findTasksByMerchantAndPayway(
                new ContractSubTaskReq().setPayway(PaywayEnum.ACQUIRER.getValue()).setMerchantSn(merchantSn).setTaskType(ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue()).setStatus(ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())
        );
        List<ContractSubTask> psbc = subTasks.stream().filter(subTask -> Objects.equals(subTask.getChannel(), ChannelEnum.PSBC.getValue()))
                .limit(1)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(psbc)) {
            throw new CommonPubBizException("找不到线上记录");
        }
        Map requestBody = JSON.parseObject(psbc.get(0).getRequest_body());
        final String name = MapUtils.getString(requestBody, "merName");
        return new AlipayMchInfo().setName(name).setSub_merchant_id(providerParams.getPay_merchant_id());
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        Optional<MerchantProviderParamsDO> unionParam = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, ProviderEnum.PROVIDER_PSBC.getValue(), PaywayEnum.UNIONPAY.getValue());
        if (unionParam.isPresent()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                    .retry(false)
                    .build();
        }
        ContractTask netInTask = getNetInTask(merchantSn, AcquirerTypeEnum.PSBC.getValue());
        if (Objects.nonNull(netInTask) && (TaskStatus.PROGRESSING.getVal().equals(netInTask.getStatus()) || TaskStatus.PENDING.getVal().equals(netInTask.getStatus()))) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .message("开通中，请稍后重试")
                    .retry(true)
                    .build();
        }
        if (Objects.nonNull(netInTask) && TaskStatus.FAIL.getVal().equals(netInTask.getStatus())) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                    .message("开通失败")
                    .retry(false)
                    .build();
        }
        return UnionPayOpenStatusQueryResp.builder()
                .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                .message("暂时无法开通，联系销售支持")
                .retry(false)
                .build();
    }

    private ContractTask getNetInTask(String merchantSn, String acquirer) {
        List<ContractTask> contractTasks = contractTaskMapper.getContractsBySnAndType(merchantSn, ProviderUtil.CONTRACT_TYPE_INSERT);
        for (ContractTask contractTask : contractTasks) {
            RuleGroup ruleGroup = ruleContext.getRuleGroup(contractTask.getRule_group_id());
            if (ruleGroup.getAcquirer().equals(acquirer)) {
                return contractTask;
            }
        }
        return null;
    }
}
