package com.wosai.upay.job.model.DO;

import com.wosai.upay.merchant.contract.model.UnionOpenResp;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class MerchantProviderParamsExt {
    private Long id;

    private String param_id;

    private Integer type;

    /**
     * 微信点金计划
     **/
    public static final int WEIXIN_GOLD_TYPE = 3;
    /**
     * 支付宝M3
     */
    public static final int ALY_M3_TYPE = 2;

    /**
     * 银联开放平台
     */
    public static final int UNION_OPEN_TYPE = 4;

    private Date create_at;

    private Date update_at;

    private Long version;

    private String extra;
    /**
     * 支付宝 升级M3 表示商户等级
     **/
    private String ext_field_1;
    /**
     * 支付宝升级m3 微信开通点金计划 均代表支付源商户号
     */
    private String ext_field_2;

    public boolean isUnionAudited(){
        return this.ext_field_2 != null && !UnionOpenResp.SYSTEM_ERROR.equalsIgnoreCase(this.ext_field_2) &&
                !UnionOpenResp.SYSTEM_FAIL.equalsIgnoreCase(this.ext_field_2) && !UnionOpenResp.AUDIT_FAIL.equalsIgnoreCase(this.ext_field_2) &&
                !"0".equalsIgnoreCase(this.ext_field_2);
    }

}