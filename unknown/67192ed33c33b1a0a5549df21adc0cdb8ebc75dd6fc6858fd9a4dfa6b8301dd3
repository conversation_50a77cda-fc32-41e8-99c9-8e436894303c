package com.wosai.upay.job.biz.comboparams;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * Date 2020/6/3 5:08 下午
 **/
@Component
public class TongLianParamsHandle extends ProviderParamsHandle {

    private static int PROVIDER = ProviderEnum.PROVIDER_TONGLIAN.getValue();


    @Override
    protected boolean accept(MerchantConfigParams merchantConfigParams) {
        return PROVIDER == merchantConfigParams.getProvider();
    }

    @Override
    protected Map getConfigParams(MerchantConfigParams merchantConfigParams) {
        Map tongLianTrade = merchantConfigParams.getTongLianTradeParams();
        if (CollectionUtils.isEmpty(tongLianTrade)) {
            throw new CommonInvalidParameterException("通联交易参数不存在");
        }
        return tongLianTrade;
    }
}
