package com.wosai.upay.job.web;

import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.job.service.ConfigSupportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Map;

/**
 * Created by lihebin on 2018/7/23.
 */
@RestController
public class AppController {

    @Autowired
    private ConfigSupportService configSupportService;

    @RequestMapping(value = "/getMerchantModuleWhite", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public Map getMerchantModuleWhite(@RequestBody @PropNotEmpty.List({
            @PropNotEmpty(value = "merchantId", message = "商户Id不能为空")
    }) Map params) throws IOException {
        return configSupportService.getMerchantModuleWhite(params);
    }
}
