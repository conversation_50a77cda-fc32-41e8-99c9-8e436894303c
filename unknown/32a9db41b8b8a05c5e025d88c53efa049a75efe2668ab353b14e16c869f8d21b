package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.domain.ZftSubMerchantOrder;
import com.alipay.api.request.AntMerchantExpandIndirectZftCreateRequest;
import com.alipay.api.request.AntMerchantExpandIndirectZftorderQueryRequest;
import com.alipay.api.response.AntMerchantExpandIndirectZftCreateResponse;
import com.alipay.api.response.AntMerchantExpandIndirectZftorderQueryResponse;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.Constants.ZftMerchantConstant;
import com.wosai.upay.job.mapper.ZftMerchantApplyMapper;
import com.wosai.upay.job.model.payLater.PayLaterInfoDTO;
import com.wosai.upay.job.model.payLater.ZftMerchantApply;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.zft.IndirectZftCreateDTO;
import com.wosai.upay.merchant.contract.model.zft.IndirectZftOrderQueryDTO;
import com.wosai.upay.merchant.contract.service.ZftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 创建直付通商户
 * <AUTHOR>
 * @Date 2023/8/1 下午2:19
 */
@Slf4j
@Component
public class ZftBiz {
    @Autowired
    ZftMerchantApplyMapper zftMerchantApplyMapper;

    @Autowired
    ZftService zftService;
    @Autowired
    PayLaterBiz payLaterBiz;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 初始化直付通商户申请
     * @param payLaterInfoDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ZftMerchantApply saveZftMerchantApply(PayLaterInfoDTO payLaterInfoDTO) {
        String merchantSn = payLaterInfoDTO.getMerchantSn();
        String account = payLaterInfoDTO.getAccount();
        //已有记录不保存
        ZftMerchantApply zftMerchantApply = zftMerchantApplyMapper.selectByCondition(merchantSn, account);
        Integer status = Optional.ofNullable(zftMerchantApply).orElseGet(ZftMerchantApply::new).getStatus();
        if(Objects.nonNull(zftMerchantApply) && !zftMerchantApply.isFinish(status)) {
            log.info("saveZftMerchantApply zftMerchantApply: {}", JSONObject.toJSONString(zftMerchantApply));
            return zftMerchantApply;
        }
        if(ZftMerchantConstant.Status.SUCCESS.equals(status)) {
            return zftMerchantApply;
        }
        ZftMerchantApply newApply = new ZftMerchantApply();
        newApply.setMerchant_sn(merchantSn);
        newApply.setStatus(ZftMerchantConstant.Status.PENDING);
        newApply.setAccount(account);
        newApply.setCreate_at(new Date());
        newApply.setUpdate_at(new Date());
        newApply.setForm_body(JSONObject.toJSONString(payLaterInfoDTO));
        zftMerchantApplyMapper.insert(newApply);
        return newApply;
    }

    /**
     * 获取指定状态,时间和数量的任务
     * @param status 状态
     * @param limit 数量
     * @param queryTime 时间
     * @return
     */
    public List<ZftMerchantApply> getZftTasks(List<Integer> status, int limit, long queryTime) {
        long current = System.currentTimeMillis();
        String startTime = StringUtil.formatDate(current - queryTime);
        String endTime = StringUtil.formatDate(current);
        return zftMerchantApplyMapper.selectByStatus(status,  limit, startTime, endTime);
    }

    /**
     * 修改信息
     * @param zftMerchantApply
     */
    public void updateZftTask(ZftMerchantApply zftMerchantApply) {
        zftMerchantApplyMapper.updateByPrimaryKeySelective(zftMerchantApply);
    }

    /**
     * 设为失败
     * @param result
     */
    public void updateZftTaskBusinessFail(Long id,String result) {
        ZftMerchantApply zftMerchantApply = new ZftMerchantApply();
        zftMerchantApply.setId(id);
        zftMerchantApply.setStatus(ZftMerchantConstant.Status.FAIL);
        zftMerchantApply.setResult(result);
        zftMerchantApplyMapper.updateByPrimaryKeySelective(zftMerchantApply);
    }

    /**
     * 支付宝接口返回系统异常统一处理,延迟到5分钟以后
     * @param zftMerchantApply
     */
    public void updateZftTaskSystemFail(ZftMerchantApply zftMerchantApply) {
        zftMerchantApply.setUpdate_at(DateUtils.addMinutes(zftMerchantApply.getUpdate_at(), 5));
        zftMerchantApplyMapper.updateByPrimaryKeySelective(zftMerchantApply);
    }


    /**
     * 直付通商户申请
     * @param zftMerchantApply
     */
    public void handleSubmitApply(ZftMerchantApply zftMerchantApply) {
        IndirectZftCreateDTO indirectZftCreateDTO = new IndirectZftCreateDTO();
        indirectZftCreateDTO.setAlipayLogonId(zftMerchantApply.getAccount());
        indirectZftCreateDTO.setMerchantSn(zftMerchantApply.getMerchant_sn());
        AliCommResponse<AntMerchantExpandIndirectZftCreateRequest, AntMerchantExpandIndirectZftCreateResponse> response = zftService.indirectZftCreate(indirectZftCreateDTO);

        if(response.isSuccess()) {
            AntMerchantExpandIndirectZftCreateResponse resp = response.getResp();
            //记录申请单号
            String orderId = resp.getOrderId();
            ZftMerchantApply newApply = new ZftMerchantApply();
            newApply.setId(zftMerchantApply.getId());
            newApply.setStatus(ZftMerchantConstant.Status.APPLYING);
            newApply.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(ZftMerchantConstant.Extra.ORDERID,orderId)));
            updateZftTask(newApply);
        }else if(response.isBusinessFail()) {
            String result = StringUtils.isEmpty(response.getMessage()) ? ZftMerchantConstant.Result.ZFT_APPLY_FAIL : response.getMessage();
            updateZftTaskBusinessFail(zftMerchantApply.getId(),result);
        }else {
            updateZftTaskSystemFail(zftMerchantApply);
        }


    }

    /**
     * 处理支付返回的直付通商户审核结果
     * @param zftMerchantApply
     */
    public void handleApplyAudit(ZftMerchantApply zftMerchantApply) {
        //获取请求标识orderId
        Map<String, Object> extraMap = zftMerchantApply.getExtraMap();
        String orderId = BeanUtil.getPropString(extraMap, ZftMerchantConstant.Extra.ORDERID);
        //组装请求参数
        IndirectZftOrderQueryDTO queryDTO = new IndirectZftOrderQueryDTO();
        queryDTO.setOrderId(orderId);
        AliCommResponse<AntMerchantExpandIndirectZftorderQueryRequest, AntMerchantExpandIndirectZftorderQueryResponse> orderQueryResponse = zftService.indirectZftOrderQuery(queryDTO);
        //查询业务异常
        if(orderQueryResponse.isBusinessFail()) {
            String reason = StringUtils.isEmpty(orderQueryResponse.getMessage()) ? ZftMerchantConstant.Result.ZFT_AUDIT_FAIL:orderQueryResponse.getMessage();
            updateZftTaskBusinessFail(zftMerchantApply.getId(),reason);
        }else if(orderQueryResponse.isSystemFail()) {
            //查询系统异常
            updateZftTaskSystemFail(zftMerchantApply);
        }else if(orderQueryResponse.isSuccess()) {
            List<ZftSubMerchantOrder> orders = orderQueryResponse.getResp().getOrders();
            ZftSubMerchantOrder zftSubMerchantOrder = orders.stream().filter(order -> Objects.equals(order.getOrderId(), orderId)).findFirst().orElseThrow(() -> new CommonPubBizException("支付宝侧没有找到对应的申请单"));
            //申请单状态。99:已完结；-1:失败；031:审核中
            String status = zftSubMerchantOrder.getStatus();
            //根据支付宝返回结果处理
            String subConfirm = zftSubMerchantOrder.getSubConfirm();
            String merchantSn = zftMerchantApply.getMerchant_sn();
            //-1:失败
            if(Objects.equals("-1", status)) {
                String fkAuditMemo = zftSubMerchantOrder.getFkAuditMemo();
                String kzAuditMemo = zftSubMerchantOrder.getKzAuditMemo();
                String orderReason = zftSubMerchantOrder.getReason();
                boolean allBlank = org.apache.commons.lang3.StringUtils.isAllBlank(fkAuditMemo, kzAuditMemo, orderReason);
                //失败原因
                String failReason = org.apache.commons.lang3.StringUtils.firstNonEmpty(orderReason, fkAuditMemo, kzAuditMemo);
                String reason = allBlank && StringUtils.isEmpty(failReason) ? ZftMerchantConstant.Result.ZFT_AUDIT_FAIL: failReason;
                updateZftTaskBusinessFail(zftMerchantApply.getId(),reason);
                //已完结
            }else if(Objects.equals("99", status)) {
                ZftMerchantApply successApply = new ZftMerchantApply();
                successApply.setId(zftMerchantApply.getId());
                successApply.setStatus(ZftMerchantConstant.Status.SUCCESS);
                successApply.setSmid(zftSubMerchantOrder.getSmid());
                updateZftTask(successApply);
                //二级商户确认状态。CREATE：已发起二级商户确认、SKIP：无需确认、FAIL：签约失败、NOT_CONFIRM：商户未确认、FINISH签约完成
            }else if (Objects.equals("031", status) && ("CREATE".equals(subConfirm) || "NOT_CONFIRM".equals(subConfirm))) {
                String redisKey = String.format("%s:%s", merchantSn, orderId);
                long currentTimeMillis = System.currentTimeMillis();
                Date currentDate = new Date(currentTimeMillis);
                Date date = DateUtils.addDays(currentDate, 30);
                if (!redisTemplate.hasKey(redisKey)) {
                    //修改为待签约
                    ZftMerchantApply successApply = new ZftMerchantApply();
                    successApply.setId(zftMerchantApply.getId());
                    successApply.setStatus(ZftMerchantConstant.Status.CONFIRMING);
                    successApply.setResult("待签约");
                    updateZftTask(successApply);
                    //发消息通知商户到支付宝签约
                    String endDate = DateFormatUtils.format(date, "yyyy-MM-dd");
                    //在redis中记录
                    redisTemplate.opsForValue().set(redisKey, endDate, 30L, TimeUnit.DAYS);
                } else if(redisTemplate.getExpire(redisKey, TimeUnit.DAYS) <= 1) {
                    ZftMerchantApply fail = new ZftMerchantApply();
                    fail.setStatus(ZftMerchantConstant.Status.FAIL).setResult("超时未签约").setId(zftMerchantApply.getId());
                    updateZftTask(fail);
                }
            }
        }
    }

}
