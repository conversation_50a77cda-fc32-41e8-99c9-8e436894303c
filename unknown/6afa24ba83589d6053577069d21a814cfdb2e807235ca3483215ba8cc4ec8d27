package com.wosai.upay.job.enume;

import lombok.*;

import java.util.Objects;

/**
 * @Description: 久久折错误码与对应文案
 * <AUTHOR>
 * @Date 2020/11/11 12:08
 */

@Getter
@AllArgsConstructor
public enum JjzNormalRateEnum {

    DIRECT_WEIXIN_PAY(401,"微信支付直连"),
    SU_ZHOU_OBJECT(402,"微信渠道主体为苏州喔噻"),
    ;

    private Integer code;
    private String message;

    public static String getMessage(Integer code) {
        final JjzNormalRateEnum[] jjzNormalRateEnums = values();
        for (JjzNormalRateEnum jjzNormalRateEnum : jjzNormalRateEnums) {
            if (Objects.equals(jjzNormalRateEnum.getCode(),code)) {
                return jjzNormalRateEnum.getMessage();
            }
        }
        return null;
    }

}
