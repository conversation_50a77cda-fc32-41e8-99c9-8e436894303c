package com.wosai.upay.job.util.luzhou.sm;

import java.io.IOException;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SM4Utils {

	public String secretKey = "";
	public String iv = "";
	public boolean hexString = false;

	public SM4Utils() {
	}

	public String encryptData_ECB(String plainText) {
		try {
			System.out.println("请求明文"+plainText);
			SM4Context ctx = new SM4Context();
			ctx.isPadding = true;
			ctx.mode = SM4.SM4_ENCRYPT;

			byte[] keyBytes;
			if (hexString) {
				keyBytes = Utils.hexStringToBytes(secretKey);
			} else {
				keyBytes = secretKey.getBytes();
			}

			SM4 sm4 = new SM4();
			sm4.sm4_setkey_enc(ctx, keyBytes);
			byte[] encrypted = sm4.sm4_crypt_ecb(ctx, plainText.getBytes("GBK"));
			String cipherText = Base64.getEncoder().encodeToString(encrypted);
			System.out.println("加密密文"+cipherText);
			if (cipherText != null && cipherText.trim().length() > 0) {
				Pattern p = Pattern.compile("\\s*|\t|\r|\n");
				Matcher m = p.matcher(cipherText);
				cipherText = m.replaceAll("");
			}
			return cipherText;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public String decryptData_ECB(String cipherText) {
		try {
			SM4Context ctx = new SM4Context();
			ctx.isPadding = true;
			ctx.mode = SM4.SM4_DECRYPT;

			byte[] keyBytes;
			if (hexString) {
				keyBytes = Utils.hexStringToBytes(secretKey);
			} else {
				keyBytes = secretKey.getBytes();
			}

			SM4 sm4 = new SM4();
			sm4.sm4_setkey_dec(ctx, keyBytes);

			byte[] decrypted = sm4.sm4_crypt_ecb(ctx, Base64.getDecoder().decode(cipherText));
			//byte[] decrypted = sm4.sm4_crypt_ecb(ctx, new BASE64Decoder().decodeBuffer(cipherText));
			return new String(decrypted, "GBK");
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public String encryptData_CBC(String plainText) {
		try {
			SM4Context ctx = new SM4Context();
			ctx.isPadding = true;
			ctx.mode = SM4.SM4_ENCRYPT;

			byte[] keyBytes;
			byte[] ivBytes;
			if (hexString) {
				keyBytes = Utils.hexStringToBytes(secretKey);
				ivBytes = Utils.hexStringToBytes(iv);
			} else {
				keyBytes = secretKey.getBytes();
				ivBytes = iv.getBytes();
			}

			SM4 sm4 = new SM4();
			sm4.sm4_setkey_enc(ctx, keyBytes);
			byte[] encrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, plainText.getBytes("GBK"));
			String cipherText = Base64.getEncoder().encodeToString(encrypted);
			if (cipherText != null && cipherText.trim().length() > 0) {
				Pattern p = Pattern.compile("\\s*|\t|\r|\n");
				Matcher m = p.matcher(cipherText);
				cipherText = m.replaceAll("");
			}
			return cipherText;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public String decryptData_CBC(String cipherText) {
		try {
			SM4Context ctx = new SM4Context();
			ctx.isPadding = true;
			ctx.mode = SM4.SM4_DECRYPT;

			byte[] keyBytes;
			byte[] ivBytes;
			if (hexString) {
				keyBytes = Utils.hexStringToBytes(secretKey);
				ivBytes = Utils.hexStringToBytes(iv);
			} else {
				keyBytes = secretKey.getBytes();
				ivBytes = iv.getBytes();
			}

			SM4 sm4 = new SM4();
			sm4.sm4_setkey_dec(ctx, keyBytes);
			byte[] decrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, Base64.getDecoder().decode(cipherText));
			return new String(decrypted, "GBK");
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public static void main(String[] args) throws IOException {
		String plainText = "{\"outTradeNo\":\"47232f16-ffb9-4813-addd-5d1779b9ddfa\",\"merchantNo\":\"M00019234\"}";

		SM4Utils sm4 = new SM4Utils();
//		sm4.secretKey = Utils.byteToHex("JeF8U9wHFOMfs2Y8".getBytes());
		sm4.secretKey = "D6680DAF9786E65B06AE00A9CEF5E3E1";
		System.out.println(sm4.secretKey);
		sm4.hexString = true;

		System.out.println("ECB模式加密");
		String cipherText = sm4.encryptData_ECB(plainText);
		System.out.println("密文: " + cipherText);

		plainText = sm4.decryptData_ECB(cipherText);
		System.out.println("明文: " + plainText);
		System.out.println("");

		//System.out.println("CBC模式加密");
		//sm4.iv = "UISwD9fW6cFh9SNS"; 
		//String cipherText = sm4.encryptData_CBC(plainText);
		//System.out.println("密文: " + cipherText);
		//plainText = sm4.decryptData_CBC(cipherText);
		//System.out.println("明文: " + plainText);

	}
}