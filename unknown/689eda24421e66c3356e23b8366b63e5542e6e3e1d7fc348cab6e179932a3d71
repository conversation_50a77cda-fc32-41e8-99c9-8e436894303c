package com.wosai.upay.job.xxljob;

import com.wosai.upay.job.xxljob.model.JobHandlerTypeEnum;
import com.wosai.upay.job.xxljob.template.BatchJobHandler;
import com.wosai.upay.job.xxljob.template.DirectJobHandler;
import com.wosai.upay.job.xxljob.template.JobHandler;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/3/14
 */
public class JobHandlerRegistry {

    public static final Map<JobHandlerTypeEnum, Map<String, JobHandler>> JOB_HANDLER_MAP = new ConcurrentHashMap<>();

    /**
     * 注册 JobHandler 到指定的 JobHandlerTypeEnum 类型下。
     *
     * @param jobName     任务名称
     * @param jobHandler  任务处理器
     */
    public static void registerJobHandler(String jobName, JobHandler jobHandler) {
        if (jobName == null || jobName.isEmpty()) {
            throw new IllegalArgumentException("Job name cannot be null or empty");
        }
        if (jobHandler == null) {
            throw new IllegalArgumentException("JobHandler cannot be null");
        }

        JobHandlerTypeEnum jobHandlerType = getJobHandlerType(jobHandler);
        JOB_HANDLER_MAP.computeIfAbsent(jobHandlerType, type -> new ConcurrentHashMap<>())
                .put(jobName, jobHandler);
    }

    /**
     * 获取指定任务名称和类型的 JobHandler。
     *
     * @param jobName       任务名称
     * @param jobHandlerType 任务类型
     * @return 对应的 JobHandler
     */
    public static JobHandler getJobHandler(String jobName, JobHandlerTypeEnum jobHandlerType) {
        if (jobName == null || jobName.isEmpty()) {
            throw new IllegalArgumentException("Job name cannot be null or empty");
        }
        if (jobHandlerType == null) {
            throw new IllegalArgumentException("JobHandlerTypeEnum cannot be null");
        }

        Map<String, JobHandler> jobHandlerMap = JOB_HANDLER_MAP.get(jobHandlerType);
        if (jobHandlerMap == null || !jobHandlerMap.containsKey(jobName)) {
            throw new IllegalArgumentException("No JobHandler found for jobName: " + jobName + " and type: " + jobHandlerType);
        }
        return jobHandlerMap.get(jobName);
    }

    /**
     * 根据 JobHandler 类型获取对应的 JobHandlerTypeEnum。
     *
     * @param jobHandler 任务处理器
     * @return 对应的 JobHandlerTypeEnum
     */
    private static JobHandlerTypeEnum getJobHandlerType(JobHandler jobHandler) {
        if (jobHandler instanceof BatchJobHandler) {
            return JobHandlerTypeEnum.BATCH;
        } else if (jobHandler instanceof DirectJobHandler) {
            return JobHandlerTypeEnum.DIRECT;
        } else {
            throw new IllegalArgumentException("JobHandler must be instance of BatchJobHandler or DirectJobHandler");
        }
    }
}
