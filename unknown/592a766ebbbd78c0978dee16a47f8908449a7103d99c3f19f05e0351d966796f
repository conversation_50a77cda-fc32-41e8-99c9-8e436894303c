package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.upay.job.biz.ChangeTradeParamsBiz;
import com.wosai.upay.job.biz.JdBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.McChannelDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;
import com.wosai.upay.job.refactor.model.entity.McChannelDO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2024/9/12 14:51
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class JdServiceImpl implements JdService {

    @Autowired
    private ContractStatusDAO contractStatusDAO;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private McChannelDAO mcChannelDAO;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private McProviderDAO mcProviderDAO;
    @Autowired
    private ParamContextBiz paramContextBiz;
    @Autowired
    private JdBiz jdBiz;
    @Autowired
    private FuyouService fuyouService;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private ChangeTradeParamsBiz tradeParamsBiz;
    @Autowired
    private SubBizParamsBiz subBizParamsBiz;

    @Value("${jd.combId}")
    public Long jdCombId;

    @Autowired
    private FeeRateService feeRateService;

    @Override
    public void openJd(String merchantSn) throws CommonPubBizException {
        ContractStatusDO contractStatusDO = getContractStatusSuccess(merchantSn);

        // 当前使用的收单机构
        String usingAcquirer = contractStatusDO.getAcquirer();

        // 获取当前收单机构开通成功的列表
        List<String> acquirerSuccess = getSuccessfulAcquirers(merchantSn, usingAcquirer);
        // 处理每个收单机构的京东钱包开通逻辑
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        acquirerSuccess.stream().forEach(acquirer -> handleAcquirer(merchantSn, acquirer, usingAcquirer, merchant));

    }

    // 获取成功的进件状态
    private ContractStatusDO getContractStatusSuccess(String merchantSn) throws CommonPubBizException {
        return contractStatusDAO.getByMerchantSn(merchantSn)
                .filter(s -> Objects.equals(s.getStatus(), ContractStatus.STATUS_SUCCESS))
                .orElseThrow(() -> new CommonPubBizException("没有进件成功"));
    }


    // 获取已开通的收单机构
    private List<String> getSuccessfulAcquirers(String merchantSn, String currentAcquirer) throws CommonPubBizException {
        List<MerchantProviderParamsDO> providerParams = merchantProviderParamsDAO.listByMerchantSnAndPayWay(merchantSn, PaywayEnum.ACQUIRER.getValue());
        if (CollectionUtils.isEmpty(providerParams)) {
            throw new CommonPubBizException("没有进件成功");
        }

        List<String> acquirerList = providerParams.stream()
                .map(MerchantProviderParamsDO::getProvider)
                .map(provider -> mcProviderDAO.getByProvider(String.valueOf(provider)))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(McProviderDO::getAcquirer)
                .collect(Collectors.toList());

        // 将 currentAcquirer 放在最后 一位
        acquirerList.remove(currentAcquirer);
        acquirerList.add(currentAcquirer);

        return acquirerList;
    }

    // 处理每个收单机构的逻辑
    private void handleAcquirer(String merchantSn, String acquirer, String usingAcquirer, MerchantInfo merchant) {
        Optional<MerchantProviderParamsDO> jdPayParams = getJdPayParams(merchantSn, acquirer);
        //该收单机构下交易参数是否存在
        if (!jdPayParams.isPresent()) {
            try {
                saveMerchantProviderParams(acquirer, merchantSn);
            } catch (Exception e) {
                log.error("merchantSn:{},acquirer:{},报备失败",e);
            }
        }
        if (Objects.equals(acquirer, usingAcquirer)) {
            Optional<MerchantProviderParamsDO> currentAcquirerJdParam = getJdPayParams(merchantSn, usingAcquirer);
            if (!currentAcquirerJdParam.isPresent()) {
                jdBiz.openFail(merchantSn, "分期开通失败,没有可用交易参数");
                throw new CommonPubBizException("分期开通失败,没有可用交易参数");
            }
            tradeParamsBiz.changeTradeParams(currentAcquirerJdParam.get().getId(), null, false,subBizParamsBiz.getPayTradeAppId());
            try {
                ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                        .setMerchantSn(merchantSn)
                        .setTradeComboId(jdCombId)
                        .setAuditSn("入网成功设置京东钱包套餐");

                Map<String, String> applyFeeRateMap = new HashMap<>();
                String payway = String.valueOf(PaywayEnum.JD_WALLET.getValue());
                applyFeeRateMap.put(payway,"0.38");
                // 设置费率并提交
                applyFeeRateRequest.setApplyPartialPayway(true);
                applyFeeRateRequest.setApplyFeeRateMap(applyFeeRateMap);
                feeRateService.applyFeeRateOne(applyFeeRateRequest);
            } catch (Exception e) {
                throw new CommonPubBizException("套餐设置失败");
            }
            Boolean forbid = jdBiz.preCheckIndustry(merchant.getIndustry());
            if (forbid) {
                jdBiz.openFail(merchantSn, "商户所属行业不支持");
                throw new CommonPubBizException("商户所属行业不支持");
            }
            jdBiz.openSuccess(merchantSn,acquirer);
        }
    }



    // 处理新收单机构的开通逻辑
    private void saveMerchantProviderParams(String acquirer, String merchantSn) throws CommonPubBizException {
        if (Objects.equals(acquirer, AcquirerTypeEnum.LKL_V3.getValue())) {
            handleLakalaAcquirer(merchantSn, acquirer);
        } else if (Objects.equals(acquirer, AcquirerTypeEnum.FU_YOU.getValue())) {
            handleFuyouAcquirer(merchantSn);
        }
    }


    /**
     * 处理拉卡拉收单机构的逻辑
     *
     * @param merchantSn
     * @param acquirer
     * @return
     */
    private void handleLakalaAcquirer(String merchantSn, String acquirer) {
        Optional<MerchantProviderParamsDO> unionPayParams = getUnionPayParams(merchantSn, acquirer);
        if (!unionPayParams.isPresent()) {
            throw new CommonPubBizException("云闪付报备失败");
        }
        MerchantProviderParamsDO jdParam = createJdPayParamsFromUnionPay(unionPayParams.get(), merchantSn);
        merchantProviderParamsDAO.saveMerchantParameters(jdParam);
    }

    // 处理富友收单机构的逻辑
    private void handleFuyouAcquirer(String merchantSn) {
        Map<String, Object> context = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        ContractResponse contractResponse = fuyouService.jdbtOpen(merchantSn, context);
        if (contractResponse.isSuccess()) {
            createFuyouJdPayParams(contractResponse, merchantSn);
        } else {
            throw new CommonPubBizException(String.format("富友京东白条开通失败%s", contractResponse.getMessage()));
        }

    }

    // 从银联参数创建京东支付参数
    private MerchantProviderParamsDO createJdPayParamsFromUnionPay(MerchantProviderParamsDO unionPayParam, String merchantSn) {
        final MerchantProviderParamsDO jdParam = new MerchantProviderParamsDO();
        BeanUtils.copyProperties(unionPayParam, jdParam);
        jdParam.setId(UUID.randomUUID().toString());
        jdParam.setPayMerchantId(null);
        jdParam.setPayway(PaywayEnum.JD_WALLET.getValue());
        jdParam.setContractRule("lkl_open-1034-5");
        return jdParam;

    }

    // 从富友返回的数据创建京东支付参数
    private void createFuyouJdPayParams(ContractResponse contractResponse, String merchantSn) {
        final Map<String, Object> responseParam = contractResponse.getResponseParam();
        final String mchntCd = BeanUtil.getPropString(responseParam, "mchnt_cd");
        final String mchntName = BeanUtil.getPropString(responseParam, "mchnt_name");
        //保存merchant_provider_params
        MerchantProviderParams newParams = new MerchantProviderParams()
                .setId(UUID.randomUUID().toString())
                .setOut_merchant_sn(merchantSn)
                .setMerchant_name(mchntName)
                .setParent_merchant_id(mchntCd)
                .setChannel_no("fuyou")
                .setProvider(ProviderEnum.PROVIDER_FUYOU.getValue())
                .setProvider_merchant_id(mchntCd)
                .setPay_merchant_id(null)
                .setMerchant_sn(merchantSn)
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setContract_rule("fuyou-1038-5")
                .setRule_group_id("fuyou")
                .setPayway(PaywayEnum.JD_WALLET.getValue())
                .setCtime(System.currentTimeMillis())
                .setMtime(System.currentTimeMillis());
        merchantProviderParamsMapper.insertSelective(newParams);
    }



    private Optional<MerchantProviderParamsDO> getJdPayParams(String merchantSn, String acquirer) {
        List<McChannelDO> mcChannels = mcChannelDAO.getMcChannelByAcquirer(acquirer, PaywayEnum.JD_WALLET.getValue());
        for (McChannelDO mcChannel : mcChannels) {
            Optional<MerchantProviderParamsDO> merChantProviderParams = merchantProviderParamsDAO.getMerChantProviderParams(merchantSn, mcChannel.getChannelNo(), PaywayEnum.JD_WALLET.getValue());
            if (merChantProviderParams.isPresent()) {
                return merChantProviderParams;
            }
        }
        return Optional.empty();

    }


    private Optional<MerchantProviderParamsDO> getUnionPayParams(String merchantSn, String acquirer) {
        List<McChannelDO> mcChannels = mcChannelDAO.getMcChannelByAcquirer(acquirer, PaywayEnum.UNIONPAY.getValue());
        for (McChannelDO mcChannel : mcChannels) {
            Optional<MerchantProviderParamsDO> merChantProviderParams = merchantProviderParamsDAO.getMerChantProviderParams(merchantSn, mcChannel.getChannelNo(), PaywayEnum.UNIONPAY.getValue());
            if (merChantProviderParams.isPresent()) {
                return merChantProviderParams;
            }
        }
        return Optional.empty();

    }



}
