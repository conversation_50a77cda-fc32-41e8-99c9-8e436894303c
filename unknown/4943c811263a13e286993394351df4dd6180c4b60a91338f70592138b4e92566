package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.PsbcTerminal;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface PsbcTerminalMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PsbcTerminal record);

    int insertSelective(PsbcTerminal record);

    PsbcTerminal selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PsbcTerminal record);

    int updateByPrimaryKey(PsbcTerminal record);

    @Update("update psbc_terminal set status = #{status},store_sn = #{storeSn},merchant_sn=#{merchantSn} where qr_code_id=#{qrCodeId}")
    int updateByqrCodeId(@Param("status") String status,
                       @Param("qrCodeId") String qrCodeId,
                       @Param("storeSn") String storeSn,
                       @Param("merchantSn")String merchantSn);

    @Select("select * from psbc_terminal where qr_code=#{qrCode} and deleted=0 order by mtime desc limit 1")
    PsbcTerminal selectByQrCode(@Param("qrCode") String qrCode);
}