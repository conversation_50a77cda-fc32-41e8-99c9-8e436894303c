/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class TradeAppAcquirerChange extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 7585353235874560452L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"TradeAppAcquirerChange\",\"namespace\":\"com.wosai.upay.job.avro\",\"fields\":[{\"name\":\"merchant_sn\",\"type\":\"string\",\"meta\":\"商户号\"},{\"name\":\"merchant_id\",\"type\":\"string\",\"meta\":\"商户id\"},{\"name\":\"source_acquirer\",\"type\":\"string\",\"meta\":\"原收单机构\"},{\"name\":\"target_acquirer\",\"type\":\"string\",\"meta\":\"目标收单机构\"},{\"name\":\"bank_channel_flag\",\"type\":[\"null\",\"string\"],\"default\":null,\"meta\":\"变更通道是否为银行通道\"},{\"name\":\"trade_appId\",\"type\":\"string\",\"meta\":\"业务方\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<TradeAppAcquirerChange> ENCODER =
      new BinaryMessageEncoder<TradeAppAcquirerChange>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<TradeAppAcquirerChange> DECODER =
      new BinaryMessageDecoder<TradeAppAcquirerChange>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<TradeAppAcquirerChange> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<TradeAppAcquirerChange> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<TradeAppAcquirerChange>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this TradeAppAcquirerChange to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a TradeAppAcquirerChange from a ByteBuffer. */
  public static TradeAppAcquirerChange fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence source_acquirer;
  @Deprecated public java.lang.CharSequence target_acquirer;
  @Deprecated public java.lang.CharSequence bank_channel_flag;
  @Deprecated public java.lang.CharSequence trade_appId;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public TradeAppAcquirerChange() {}

  /**
   * All-args constructor.
   * @param merchant_sn The new value for merchant_sn
   * @param merchant_id The new value for merchant_id
   * @param source_acquirer The new value for source_acquirer
   * @param target_acquirer The new value for target_acquirer
   * @param bank_channel_flag The new value for bank_channel_flag
   * @param trade_appId The new value for trade_appId
   */
  public TradeAppAcquirerChange(java.lang.CharSequence merchant_sn, java.lang.CharSequence merchant_id, java.lang.CharSequence source_acquirer, java.lang.CharSequence target_acquirer, java.lang.CharSequence bank_channel_flag, java.lang.CharSequence trade_appId) {
    this.merchant_sn = merchant_sn;
    this.merchant_id = merchant_id;
    this.source_acquirer = source_acquirer;
    this.target_acquirer = target_acquirer;
    this.bank_channel_flag = bank_channel_flag;
    this.trade_appId = trade_appId;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_sn;
    case 1: return merchant_id;
    case 2: return source_acquirer;
    case 3: return target_acquirer;
    case 4: return bank_channel_flag;
    case 5: return trade_appId;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_sn = (java.lang.CharSequence)value$; break;
    case 1: merchant_id = (java.lang.CharSequence)value$; break;
    case 2: source_acquirer = (java.lang.CharSequence)value$; break;
    case 3: target_acquirer = (java.lang.CharSequence)value$; break;
    case 4: bank_channel_flag = (java.lang.CharSequence)value$; break;
    case 5: trade_appId = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'source_acquirer' field.
   * @return The value of the 'source_acquirer' field.
   */
  public java.lang.CharSequence getSourceAcquirer() {
    return source_acquirer;
  }

  /**
   * Sets the value of the 'source_acquirer' field.
   * @param value the value to set.
   */
  public void setSourceAcquirer(java.lang.CharSequence value) {
    this.source_acquirer = value;
  }

  /**
   * Gets the value of the 'target_acquirer' field.
   * @return The value of the 'target_acquirer' field.
   */
  public java.lang.CharSequence getTargetAcquirer() {
    return target_acquirer;
  }

  /**
   * Sets the value of the 'target_acquirer' field.
   * @param value the value to set.
   */
  public void setTargetAcquirer(java.lang.CharSequence value) {
    this.target_acquirer = value;
  }

  /**
   * Gets the value of the 'bank_channel_flag' field.
   * @return The value of the 'bank_channel_flag' field.
   */
  public java.lang.CharSequence getBankChannelFlag() {
    return bank_channel_flag;
  }

  /**
   * Sets the value of the 'bank_channel_flag' field.
   * @param value the value to set.
   */
  public void setBankChannelFlag(java.lang.CharSequence value) {
    this.bank_channel_flag = value;
  }

  /**
   * Gets the value of the 'trade_appId' field.
   * @return The value of the 'trade_appId' field.
   */
  public java.lang.CharSequence getTradeAppId() {
    return trade_appId;
  }

  /**
   * Sets the value of the 'trade_appId' field.
   * @param value the value to set.
   */
  public void setTradeAppId(java.lang.CharSequence value) {
    this.trade_appId = value;
  }

  /**
   * Creates a new TradeAppAcquirerChange RecordBuilder.
   * @return A new TradeAppAcquirerChange RecordBuilder
   */
  public static com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder newBuilder() {
    return new com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder();
  }

  /**
   * Creates a new TradeAppAcquirerChange RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new TradeAppAcquirerChange RecordBuilder
   */
  public static com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder newBuilder(com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder other) {
    return new com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder(other);
  }

  /**
   * Creates a new TradeAppAcquirerChange RecordBuilder by copying an existing TradeAppAcquirerChange instance.
   * @param other The existing instance to copy.
   * @return A new TradeAppAcquirerChange RecordBuilder
   */
  public static com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder newBuilder(com.wosai.upay.job.avro.TradeAppAcquirerChange other) {
    return new com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder(other);
  }

  /**
   * RecordBuilder for TradeAppAcquirerChange instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<TradeAppAcquirerChange>
    implements org.apache.avro.data.RecordBuilder<TradeAppAcquirerChange> {

    private java.lang.CharSequence merchant_sn;
    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence source_acquirer;
    private java.lang.CharSequence target_acquirer;
    private java.lang.CharSequence bank_channel_flag;
    private java.lang.CharSequence trade_appId;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.source_acquirer)) {
        this.source_acquirer = data().deepCopy(fields()[2].schema(), other.source_acquirer);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.target_acquirer)) {
        this.target_acquirer = data().deepCopy(fields()[3].schema(), other.target_acquirer);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.bank_channel_flag)) {
        this.bank_channel_flag = data().deepCopy(fields()[4].schema(), other.bank_channel_flag);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.trade_appId)) {
        this.trade_appId = data().deepCopy(fields()[5].schema(), other.trade_appId);
        fieldSetFlags()[5] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing TradeAppAcquirerChange instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.job.avro.TradeAppAcquirerChange other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.source_acquirer)) {
        this.source_acquirer = data().deepCopy(fields()[2].schema(), other.source_acquirer);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.target_acquirer)) {
        this.target_acquirer = data().deepCopy(fields()[3].schema(), other.target_acquirer);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.bank_channel_flag)) {
        this.bank_channel_flag = data().deepCopy(fields()[4].schema(), other.bank_channel_flag);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.trade_appId)) {
        this.trade_appId = data().deepCopy(fields()[5].schema(), other.trade_appId);
        fieldSetFlags()[5] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_sn = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_id = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'source_acquirer' field.
      * @return The value.
      */
    public java.lang.CharSequence getSourceAcquirer() {
      return source_acquirer;
    }

    /**
      * Sets the value of the 'source_acquirer' field.
      * @param value The value of 'source_acquirer'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder setSourceAcquirer(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.source_acquirer = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'source_acquirer' field has been set.
      * @return True if the 'source_acquirer' field has been set, false otherwise.
      */
    public boolean hasSourceAcquirer() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'source_acquirer' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder clearSourceAcquirer() {
      source_acquirer = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'target_acquirer' field.
      * @return The value.
      */
    public java.lang.CharSequence getTargetAcquirer() {
      return target_acquirer;
    }

    /**
      * Sets the value of the 'target_acquirer' field.
      * @param value The value of 'target_acquirer'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder setTargetAcquirer(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.target_acquirer = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'target_acquirer' field has been set.
      * @return True if the 'target_acquirer' field has been set, false otherwise.
      */
    public boolean hasTargetAcquirer() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'target_acquirer' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder clearTargetAcquirer() {
      target_acquirer = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'bank_channel_flag' field.
      * @return The value.
      */
    public java.lang.CharSequence getBankChannelFlag() {
      return bank_channel_flag;
    }

    /**
      * Sets the value of the 'bank_channel_flag' field.
      * @param value The value of 'bank_channel_flag'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder setBankChannelFlag(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.bank_channel_flag = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'bank_channel_flag' field has been set.
      * @return True if the 'bank_channel_flag' field has been set, false otherwise.
      */
    public boolean hasBankChannelFlag() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'bank_channel_flag' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder clearBankChannelFlag() {
      bank_channel_flag = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'trade_appId' field.
      * @return The value.
      */
    public java.lang.CharSequence getTradeAppId() {
      return trade_appId;
    }

    /**
      * Sets the value of the 'trade_appId' field.
      * @param value The value of 'trade_appId'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder setTradeAppId(java.lang.CharSequence value) {
      validate(fields()[5], value);
      this.trade_appId = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'trade_appId' field has been set.
      * @return True if the 'trade_appId' field has been set, false otherwise.
      */
    public boolean hasTradeAppId() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'trade_appId' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.TradeAppAcquirerChange.Builder clearTradeAppId() {
      trade_appId = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public TradeAppAcquirerChange build() {
      try {
        TradeAppAcquirerChange record = new TradeAppAcquirerChange();
        record.merchant_sn = fieldSetFlags()[0] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_id = fieldSetFlags()[1] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.source_acquirer = fieldSetFlags()[2] ? this.source_acquirer : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.target_acquirer = fieldSetFlags()[3] ? this.target_acquirer : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.bank_channel_flag = fieldSetFlags()[4] ? this.bank_channel_flag : (java.lang.CharSequence) defaultValue(fields()[4]);
        record.trade_appId = fieldSetFlags()[5] ? this.trade_appId : (java.lang.CharSequence) defaultValue(fields()[5]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<TradeAppAcquirerChange>
    WRITER$ = (org.apache.avro.io.DatumWriter<TradeAppAcquirerChange>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<TradeAppAcquirerChange>
    READER$ = (org.apache.avro.io.DatumReader<TradeAppAcquirerChange>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
