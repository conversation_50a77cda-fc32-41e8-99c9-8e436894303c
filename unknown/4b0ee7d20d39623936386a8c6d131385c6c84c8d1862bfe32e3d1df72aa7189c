package com.wosai.upay.job.biz.bankDirect;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.paramContext.FeeRateUtil;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.CommonConstants;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.enume.BankDirectApplyViewStatusEnum;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.ViewProcess;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.service.ContractTaskResultServiceImpl;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.wosai.upay.job.providers.PsbcProvider.replaceHttp;


/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/4/26 09:24
 */
@Component
public class HxbDirectBiz extends AbstractBankDirectApplyBiz implements BankHandleService {

    @Value("${hxb_dev_code}")
    public  String hxbDevCode;


    @Autowired
    MerchantService merchantService;

    @Autowired
    private ContractTaskResultServiceImpl contractTaskResultService;

    @Override
    public String getDevCode() {
        return hxbDevCode;
    }

    @Override
    protected Map<String, Object> completeContext(Map<String, Object> paramContext, BankDirectReq bankDirectReq) {
        final Map formBody = JSONObject.parseObject(bankDirectReq.getForm_body(), Map.class);
        //TODO 放入费率
        final List config = JSONObject.parseObject(BeanUtil.getPropString(formBody, "merchant_config"), List.class);
        paramContext.put("hxb_feeRate", config);
        //TODO 放入套餐
        paramContext.put("trade_combo_id", BeanUtil.getPropString(formBody, "trade_combo_id"));
        //放入业务标识
        paramContext.put("dev_code",bankDirectReq.getDev_code());
        paramContext.put("remark", BeanUtil.getPropString(formBody, "remark"));
        //放入spa可以识别的费率信息
        final List<Map> list = ((List<Map>) config).stream().map(x -> {
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.WEIXIN.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.WECHAT_PAY_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.ALIPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.ALIPAY_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.UNIONPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_DEBIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        paramContext.putIfAbsent(ParamContextBiz.MERCHANT_FEE_RATES,list);
        return paramContext;
    }

    @Override
    protected void postHandleViewProcess(BankDirectApply bankDirectApply, List<ViewProcess> processes) {
        if (Objects.isNull(bankDirectApply.getTask_id()) || bankDirectApply.getProcess_status() != 10) {
            return;
        }
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(bankDirectApply.getTask_id());
        if (Objects.isNull(contractTask)) {
            return;
        }
        String merchantMemo = contractTaskResultService.getContractMemo(contractTask);
        processes.forEach(e -> {
            if (Objects.equals(BankDirectApplyViewStatusEnum.DISTRIBUTING.getValue(), e.getViewStatus())) {
                e.setLatestBankReviewStatusDesc(CommonConstants.BANK + merchantMemo);
            }
        });
    }

    @Override
    protected String chooseGroupByContextAndDevCode(Map<String, Object> context, String devCode) {
        return McConstant.RULE_GROUP_HXB;
    }

    @Override
    protected Integer getBankRef(String dev_code) {
        return BankDirectApplyRefEnum.HXB.getValue();
    }

    @Override
    protected String getAcquire() {
        return AcquirerTypeEnum.HXB.getValue();
    }

    @Override
    protected Integer getProvider() {
        return ProviderEnum.PROVIDER_HXB.getValue();
    }

    @Override
    public List<ViewProcess> initViewProcess(String merchantSn) {
        final List<ViewProcess> viewProcesses = preViewProcess(getAcquire());
        if(CollectionUtils.isEmpty(viewProcesses)) {
            return Lists.newArrayList();
        }
        List<ViewProcess> list;
        //邮储的对公商户不需要签约流程
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, getDevCode());
        //营业执照类型
        final Integer licenseType = license.getType();
        //对私商户
        if(Lists.newArrayList(BusinessLicenseTypeEnum.MICRO.getValue(), BusinessLicenseTypeEnum.INDIVIDUAL.getValue()).contains(licenseType)) {
            list = viewProcesses.stream().filter(x -> !Lists.newArrayList(BankDirectApplyViewStatusEnum.AUTHING_ENTERPRISE.getValue()).contains(x.getViewStatus())).collect(Collectors.toList());
        }else {
            //对公商户
            list = viewProcesses.stream().filter(x -> !Lists.newArrayList(BankDirectApplyViewStatusEnum.AUTHING.getValue()).contains(x.getViewStatus())).collect(Collectors.toList());
        }
        //设置微信图片地址链接
        list.stream().forEach(x-> {
            if(Objects.equals(x.getExtra(),Boolean.TRUE)) {
                final String imageUrl = getWXImageUrl(getAcquire(), getProvider());
                x.setExtraMessage(imageUrl);
                x.setAliMessage(replaceHttp("https://images.wosaimg.com/ad/e0342bf04deee396e73dfdd86616f165457bfe.png"));
            }
        });
        return list;
    }
}
