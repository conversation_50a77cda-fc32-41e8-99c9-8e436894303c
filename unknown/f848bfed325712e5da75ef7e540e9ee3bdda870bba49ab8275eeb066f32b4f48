package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.databus.event.merchant.contract.MerchantContractWeixinAuthEvent;
import com.wosai.upay.job.biz.DataBusBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotEmpty;
import java.util.Objects;

/**
 * @Description:
 * <AUTHOR>
 * Date 2019/10/16 5:15 下午
 **/
@Service
@AutoJsonRpcServiceImpl
public class MerchantAuthEventServiceImpl implements MerchantAuthEventService {

    @Autowired
    private DataBusBiz dataBusBiz;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;


    @Override
    public void insertAuthEvent(@NotEmpty String merchantId, String weixinMerchantId, boolean needAuth) {
        MerchantContractWeixinAuthEvent event = new MerchantContractWeixinAuthEvent();
        event.setNeedAuth(needAuth);
        event.setMerchantId(merchantId);
        event.setWeixin_merchant_id(weixinMerchantId);
        event.setTimestamp(System.currentTimeMillis());
        event.setAuthStatus(-1);
        dataBusBiz.insertWeixinAuthEvent(event);
    }

    @Override
    public boolean getWeixinMchIdAuthStatus(String mchId) {
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(mchId);
        if (merchantProviderParams == null) {
            return false;
        }
        return 1 == merchantProviderParams.getAuth_status();
    }

    @Override
    public boolean getAuthStatus(String merchantSn) {
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.getUseWeiXinParam(merchantSn);
        return !Objects.isNull(merchantProviderParams) && Objects.equals(1, merchantProviderParams.getAuth_status());
    }
}
