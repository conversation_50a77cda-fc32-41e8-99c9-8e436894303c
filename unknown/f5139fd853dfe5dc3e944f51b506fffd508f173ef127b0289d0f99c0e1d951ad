package com.wosai.upay.job.enume;

/**
 * @Description: contract_sub_task status
 * <AUTHOR>
 * @Date 2021/4/21 5:13 下午
 **/
public enum SubTaskStatus {
    //开始状态
    PENDING("待处理", 0),

    //中间状态
    PROGRESSING("处理中", 1),

    STOP("被终止", 4),
    //结束状态
    SUCCESS("处理成功", 5),

    FAIL("处理失败", 6),

    CONTRACT("等待回调", 10),

    RECONSIDER("复议中", 11),

    REPAYFOR("进件换卡失败代付中", 12);

    private String msg;
    private Integer val;

    SubTaskStatus(String msg, Integer val) {
        this.msg = msg;
        this.val = val;
    }


    public static SubTaskStatus toStatus(Integer val) {
        if (val == null) {
            return null;
        }
        for (SubTaskStatus status : SubTaskStatus.values()) {
            if (status.getVal().equals(val)) {
                return status;
            }
        }
        return null;
    }

    public static Boolean isSuccess(Integer val){
        return isSuccess(toStatus(val));
    }

    public static Boolean isSuccess(SubTaskStatus status){
        return SUCCESS.equals(status);
    }

    public static boolean isFinish(Integer val) {
        SubTaskStatus status = toStatus(val);
        return SUCCESS.equals(status) || FAIL.equals(status) || STOP.equals(status);
    }

    public static Boolean isWaiting(Integer val){
        return isWaiting(toStatus(val));
    }

    public static Boolean isWaiting(SubTaskStatus status){
        return CONTRACT.equals(status) || RECONSIDER.equals(status);
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getVal() {
        return val;
    }

    public void setVal(Integer val) {
        this.val = val;
    }
}
