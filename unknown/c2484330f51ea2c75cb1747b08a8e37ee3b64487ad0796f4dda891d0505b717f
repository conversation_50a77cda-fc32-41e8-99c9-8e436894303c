package com.wosai.upay.job.biz.messageStrategy;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Description: 处理阿里推送消息工厂
 * <AUTHOR>
 * @Date 2020/11/30 11:03
 */
@Component
@Slf4j
public class AliMessageHandleFactory {

    private Map<String, AliMessageHandleService> map = Maps.newHashMap();

    public AliMessageHandleFactory(List<AliMessageHandleService> list) {
        list.stream().forEach(x -> map.put(x.getMsgMethod(),x));
    }


    public AliMessageHandleService getAliMessageHandleService(String msgMethod) {
        return map.get(msgMethod);
    }

}
