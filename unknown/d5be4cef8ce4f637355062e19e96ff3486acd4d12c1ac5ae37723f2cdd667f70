/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class OpenOnlinePaymentApplyStatusChange extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -3722144923619090568L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"OpenOnlinePaymentApplyStatusChange\",\"namespace\":\"com.wosai.upay.job.avro\",\"fields\":[{\"name\":\"apply_id\",\"type\":[\"null\",\"string\"],\"default\":null,\"meta\":\"申请单号\"},{\"name\":\"merchant_sn\",\"type\":\"string\",\"meta\":\"商户号\"},{\"name\":\"payway\",\"type\":[\"null\",\"int\"],\"default\":null,\"meta\":\"支付源\"},{\"name\":\"pre_status\",\"type\":[\"int\",\"null\"],\"meta\":\"旧状态\"},{\"name\":\"status\",\"type\":[\"int\",\"null\"],\"meta\":\"新状态\"},{\"name\":\"message\",\"type\":[\"string\",\"null\"],\"meta\":\"上游返回的原始文案\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<OpenOnlinePaymentApplyStatusChange> ENCODER =
      new BinaryMessageEncoder<OpenOnlinePaymentApplyStatusChange>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<OpenOnlinePaymentApplyStatusChange> DECODER =
      new BinaryMessageDecoder<OpenOnlinePaymentApplyStatusChange>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<OpenOnlinePaymentApplyStatusChange> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<OpenOnlinePaymentApplyStatusChange> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<OpenOnlinePaymentApplyStatusChange>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this OpenOnlinePaymentApplyStatusChange to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a OpenOnlinePaymentApplyStatusChange from a ByteBuffer. */
  public static OpenOnlinePaymentApplyStatusChange fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence apply_id;
  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.Integer payway;
  @Deprecated public java.lang.Integer pre_status;
  @Deprecated public java.lang.Integer status;
  @Deprecated public java.lang.CharSequence message;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public OpenOnlinePaymentApplyStatusChange() {}

  /**
   * All-args constructor.
   * @param apply_id The new value for apply_id
   * @param merchant_sn The new value for merchant_sn
   * @param payway The new value for payway
   * @param pre_status The new value for pre_status
   * @param status The new value for status
   * @param message The new value for message
   */
  public OpenOnlinePaymentApplyStatusChange(java.lang.CharSequence apply_id, java.lang.CharSequence merchant_sn, java.lang.Integer payway, java.lang.Integer pre_status, java.lang.Integer status, java.lang.CharSequence message) {
    this.apply_id = apply_id;
    this.merchant_sn = merchant_sn;
    this.payway = payway;
    this.pre_status = pre_status;
    this.status = status;
    this.message = message;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return apply_id;
    case 1: return merchant_sn;
    case 2: return payway;
    case 3: return pre_status;
    case 4: return status;
    case 5: return message;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: apply_id = (java.lang.CharSequence)value$; break;
    case 1: merchant_sn = (java.lang.CharSequence)value$; break;
    case 2: payway = (java.lang.Integer)value$; break;
    case 3: pre_status = (java.lang.Integer)value$; break;
    case 4: status = (java.lang.Integer)value$; break;
    case 5: message = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'apply_id' field.
   * @return The value of the 'apply_id' field.
   */
  public java.lang.CharSequence getApplyId() {
    return apply_id;
  }

  /**
   * Sets the value of the 'apply_id' field.
   * @param value the value to set.
   */
  public void setApplyId(java.lang.CharSequence value) {
    this.apply_id = value;
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'payway' field.
   * @return The value of the 'payway' field.
   */
  public java.lang.Integer getPayway() {
    return payway;
  }

  /**
   * Sets the value of the 'payway' field.
   * @param value the value to set.
   */
  public void setPayway(java.lang.Integer value) {
    this.payway = value;
  }

  /**
   * Gets the value of the 'pre_status' field.
   * @return The value of the 'pre_status' field.
   */
  public java.lang.Integer getPreStatus() {
    return pre_status;
  }

  /**
   * Sets the value of the 'pre_status' field.
   * @param value the value to set.
   */
  public void setPreStatus(java.lang.Integer value) {
    this.pre_status = value;
  }

  /**
   * Gets the value of the 'status' field.
   * @return The value of the 'status' field.
   */
  public java.lang.Integer getStatus() {
    return status;
  }

  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(java.lang.Integer value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'message' field.
   * @return The value of the 'message' field.
   */
  public java.lang.CharSequence getMessage() {
    return message;
  }

  /**
   * Sets the value of the 'message' field.
   * @param value the value to set.
   */
  public void setMessage(java.lang.CharSequence value) {
    this.message = value;
  }

  /**
   * Creates a new OpenOnlinePaymentApplyStatusChange RecordBuilder.
   * @return A new OpenOnlinePaymentApplyStatusChange RecordBuilder
   */
  public static com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder newBuilder() {
    return new com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder();
  }

  /**
   * Creates a new OpenOnlinePaymentApplyStatusChange RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new OpenOnlinePaymentApplyStatusChange RecordBuilder
   */
  public static com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder newBuilder(com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder other) {
    return new com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder(other);
  }

  /**
   * Creates a new OpenOnlinePaymentApplyStatusChange RecordBuilder by copying an existing OpenOnlinePaymentApplyStatusChange instance.
   * @param other The existing instance to copy.
   * @return A new OpenOnlinePaymentApplyStatusChange RecordBuilder
   */
  public static com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder newBuilder(com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange other) {
    return new com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder(other);
  }

  /**
   * RecordBuilder for OpenOnlinePaymentApplyStatusChange instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<OpenOnlinePaymentApplyStatusChange>
    implements org.apache.avro.data.RecordBuilder<OpenOnlinePaymentApplyStatusChange> {

    private java.lang.CharSequence apply_id;
    private java.lang.CharSequence merchant_sn;
    private java.lang.Integer payway;
    private java.lang.Integer pre_status;
    private java.lang.Integer status;
    private java.lang.CharSequence message;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.apply_id)) {
        this.apply_id = data().deepCopy(fields()[0].schema(), other.apply_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.payway)) {
        this.payway = data().deepCopy(fields()[2].schema(), other.payway);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.pre_status)) {
        this.pre_status = data().deepCopy(fields()[3].schema(), other.pre_status);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.status)) {
        this.status = data().deepCopy(fields()[4].schema(), other.status);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.message)) {
        this.message = data().deepCopy(fields()[5].schema(), other.message);
        fieldSetFlags()[5] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing OpenOnlinePaymentApplyStatusChange instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.apply_id)) {
        this.apply_id = data().deepCopy(fields()[0].schema(), other.apply_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.payway)) {
        this.payway = data().deepCopy(fields()[2].schema(), other.payway);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.pre_status)) {
        this.pre_status = data().deepCopy(fields()[3].schema(), other.pre_status);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.status)) {
        this.status = data().deepCopy(fields()[4].schema(), other.status);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.message)) {
        this.message = data().deepCopy(fields()[5].schema(), other.message);
        fieldSetFlags()[5] = true;
      }
    }

    /**
      * Gets the value of the 'apply_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getApplyId() {
      return apply_id;
    }

    /**
      * Sets the value of the 'apply_id' field.
      * @param value The value of 'apply_id'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder setApplyId(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.apply_id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'apply_id' field has been set.
      * @return True if the 'apply_id' field has been set, false otherwise.
      */
    public boolean hasApplyId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'apply_id' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder clearApplyId() {
      apply_id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_sn = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'payway' field.
      * @return The value.
      */
    public java.lang.Integer getPayway() {
      return payway;
    }

    /**
      * Sets the value of the 'payway' field.
      * @param value The value of 'payway'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder setPayway(java.lang.Integer value) {
      validate(fields()[2], value);
      this.payway = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'payway' field has been set.
      * @return True if the 'payway' field has been set, false otherwise.
      */
    public boolean hasPayway() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'payway' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder clearPayway() {
      payway = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'pre_status' field.
      * @return The value.
      */
    public java.lang.Integer getPreStatus() {
      return pre_status;
    }

    /**
      * Sets the value of the 'pre_status' field.
      * @param value The value of 'pre_status'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder setPreStatus(java.lang.Integer value) {
      validate(fields()[3], value);
      this.pre_status = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'pre_status' field has been set.
      * @return True if the 'pre_status' field has been set, false otherwise.
      */
    public boolean hasPreStatus() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'pre_status' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder clearPreStatus() {
      pre_status = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * @return The value.
      */
    public java.lang.Integer getStatus() {
      return status;
    }

    /**
      * Sets the value of the 'status' field.
      * @param value The value of 'status'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder setStatus(java.lang.Integer value) {
      validate(fields()[4], value);
      this.status = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'status' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder clearStatus() {
      status = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'message' field.
      * @return The value.
      */
    public java.lang.CharSequence getMessage() {
      return message;
    }

    /**
      * Sets the value of the 'message' field.
      * @param value The value of 'message'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder setMessage(java.lang.CharSequence value) {
      validate(fields()[5], value);
      this.message = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'message' field has been set.
      * @return True if the 'message' field has been set, false otherwise.
      */
    public boolean hasMessage() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'message' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange.Builder clearMessage() {
      message = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public OpenOnlinePaymentApplyStatusChange build() {
      try {
        OpenOnlinePaymentApplyStatusChange record = new OpenOnlinePaymentApplyStatusChange();
        record.apply_id = fieldSetFlags()[0] ? this.apply_id : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_sn = fieldSetFlags()[1] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.payway = fieldSetFlags()[2] ? this.payway : (java.lang.Integer) defaultValue(fields()[2]);
        record.pre_status = fieldSetFlags()[3] ? this.pre_status : (java.lang.Integer) defaultValue(fields()[3]);
        record.status = fieldSetFlags()[4] ? this.status : (java.lang.Integer) defaultValue(fields()[4]);
        record.message = fieldSetFlags()[5] ? this.message : (java.lang.CharSequence) defaultValue(fields()[5]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<OpenOnlinePaymentApplyStatusChange>
    WRITER$ = (org.apache.avro.io.DatumWriter<OpenOnlinePaymentApplyStatusChange>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<OpenOnlinePaymentApplyStatusChange>
    READER$ = (org.apache.avro.io.DatumReader<OpenOnlinePaymentApplyStatusChange>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
