package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.CcbDecpMerchant;

import java.util.List;

public interface CcbDecpMerchantMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CcbDecpMerchant record);

    int insertSelective(CcbDecpMerchant record);

    CcbDecpMerchant selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CcbDecpMerchant record);

    int updateByPrimaryKeyWithBLOBs(CcbDecpMerchant record);

    int updateByPrimaryKey(CcbDecpMerchant record);

    CcbDecpMerchant selectActivatedByMerchantSn(String merchantSn);

    CcbDecpMerchant selectByMerchantSnAndIdentity(String merchantSn, String identity);

    CcbDecpMerchant selectSuccessByIdentity(String identity);

    List<CcbDecpMerchant> selectByOpenStatus(Integer openStatus);
}