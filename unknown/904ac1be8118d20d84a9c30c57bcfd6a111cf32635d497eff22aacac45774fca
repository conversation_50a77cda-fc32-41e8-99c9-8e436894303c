package com.wosai.upay.job.biz.directparams;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.model.directparams.AlipayIntlDirectParams;
import com.wosai.upay.job.model.directparams.BaseParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 目前支付宝国际只支持 B2C
 *
 * <AUTHOR>
 * @date 2019-08-29
 */
@Component
public class AlipayIntlDirectParamsBiz extends DirectParamsBiz {

    private static final int PAYWAY = PaywayEnum.ALIPAY_INTL.getValue();

    @Override
    public void addDirectParams(BaseParams baseParams) {
        checkMerchant(baseParams);

        AlipayIntlDirectParams params = (AlipayIntlDirectParams) baseParams;

        if (isNotEmpty(params.getAlipay_intl_trade_params())) {
            Map<String, Object> directParams = bean2Map(params.getAlipay_intl_trade_params());
            paramsBiz.saveDirectMerchantProviderParams(
                    new MerchantProviderParamsDto()
                            .setMerchant_sn(params.getMerchant_sn())
                            .setExtra(CollectionUtil.hashMap(TransactionParam.ALIPAY_INTL_TRADE_PARAMS, directParams))
                            .setProvider(PAYWAY)
                            .setPayway(PAYWAY)
                            .setPay_merchant_id(params.getAlipay_intl_trade_params().getMerchant_id())
            );
            tradeConfigService.updateAlipayIntlTradeParams(params.getMerchant_id(), directParams);

            Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(params.getMerchant_id(), PAYWAY);
            tradeConfigService.updateMerchantConfig(CollectionUtil.hashMap(
                    DaoConstants.ID, WosaiMapUtils.getString(merchantConfig, DaoConstants.ID),
                    MerchantConfig.B2C_FORMAL, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.B2C_FEE_RATE, params.getAlipay_intl_trade_params().getFee_rate()

            ));
            supportService.removeCachedParams(params.getMerchant_sn());
        }
    }

    @Override
    public void deleteDirectParams(MerchantProviderParamsDto params, String subPayway, String feeRate) {
        paramsBiz.deleteParamsById(params.getId());

        Map merchant = merchantService.getMerchantBySn(params.getMerchant_sn());
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);

        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY);
        Map merchantConfigParams = WosaiMapUtils.getMap(merchantConfig, MerchantConfig.PARAMS, new HashMap());
        merchantConfigParams.remove(TransactionParam.ALIPAY_INTL_TRADE_PARAMS);

        Map updateInfo = CollectionUtil.hashMap(
                DaoConstants.ID, WosaiMapUtils.getString(merchantConfig, DaoConstants.ID),
                MerchantConfig.PARAMS, merchantConfigParams,
                MerchantConfig.B2C_FORMAL, MerchantConfig.STATUS_CLOSED,
                MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_CLOSED,
                MerchantConfig.B2C_AGENT_NAME, null,
                MerchantConfig.B2C_FEE_RATE, null
        );

        tradeConfigService.updateMerchantConfig(updateInfo);
        supportService.removeCachedParams(params.getMerchant_sn());
    }

    @Override
    public BaseParams getDirectParams(String merchantSn) {
        return null;
    }

    @Override
    public List<MerchantProviderParamsCustomDto> handleDirectParams(MerchantProviderParamsCustomDto source) {
        return Arrays.asList(source);
    }
}
