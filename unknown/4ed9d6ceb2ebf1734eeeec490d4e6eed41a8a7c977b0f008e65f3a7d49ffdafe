package com.wosai.upay.job.enume;

/**
 * Created by hzq on 19/8/26.
 */
public enum MerchantType {


    TYPE_MICRO("小微商户", 0, "4"),
    TYPE_INDIVIDUAL("个体", 1, "3"),
    TYPE_ENTERPRISE("企业", 2, "1"),
    TYPE_OTHER("其他证照", 3, null);

    private String context;
    private Integer val;
    private String tlVal;

    MerchantType(String context, Integer val, String tlVal) {
        this.context = context;
        this.val = val;
        this.tlVal = tlVal;
    }

    public static MerchantType toStatus(Integer value) {
        if (value == null) {
            return null;
        }
        for (MerchantType status : MerchantType.values()) {
            if (status.getVal().equals(value)) {
                return status;
            }
        }
        return TYPE_OTHER;
    }

    public static MerchantType valueFromTlStatus(String tlVal){
        if (tlVal == null) {
            return null;
        }
        for (MerchantType status : MerchantType.values()) {
            if (status.getTlVal().equals(tlVal)) {
                return status;
            }
        }
        return null;
    }


    public String getContext() {
        return context;
    }


    public Integer getVal() {
        return val;
    }

    public String getTlVal(){
        return tlVal;
    }

}
