package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.ContractStatus;
import lombok.experimental.Delegate;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ContractStatusMapper {


    int insertSelective(ContractStatus record);

    ContractStatus selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContractStatus record);

    ContractStatus selectByMerchantSn(String merchantSn);

    List<ContractStatus> selectByUpdate(String utime);

    int updateMtime(String update, String merchantSn);

    int replaceContractStatus(String merchantSn, Integer value);

    @Delete("DELETE FROM  contract_status WHERE merchant_sn =#{merchantSn}")
    int deleteBySn(@Param("merchantSn") String merchantSn);

    @Select("select * from contract_status where update_at <=#{updateEndTime} and update_at>=#{updateStartTime} and acquirer=#{acquirer} and status=#{status}")
    List<ContractStatus> selectByUpdateAndStatusAndAcquirer(@Param("updateEndTime") String updateEndTime, @Param("updateStartTime") String updateStartTime,
                                                            @Param("acquirer") String acquirer, @Param("status") int status);
}