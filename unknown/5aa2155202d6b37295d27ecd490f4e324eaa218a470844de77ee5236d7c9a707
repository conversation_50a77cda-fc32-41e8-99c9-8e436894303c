package com.wosai.upay.job.helper;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.biz.bankDirect.BankHandleService;
import com.wosai.upay.job.biz.bankDirect.BankHandleServiceFactory;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.ViewProcess;
import com.wosai.upay.job.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class RecordViewProcessHelper {
    @Lazy
    private final BankHandleServiceFactory factory;
    private final BankDirectApplyMapper bankDirectApplyMapper;

    /**
     * <AUTHOR>
     * @Description: 使用extra中view_process字段记录状态变化供crm显示
     * @time 09:15
     */
    public void recordViewProcess(BankDirectApply apply, Integer viewStatus, Date date) {
        if (Objects.isNull(apply)) {
            return;
        }
        Map<String, Object> extraMap = apply.getExtraMap();
        if (MapUtils.isEmpty(extraMap)) {
            return;
        }
        //原有记录
        final String processStr = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.VIEW_PROCESS);
        if (StringUtils.isEmpty(processStr)) {
            //初始化所有信息
            final BankHandleService handleService = factory.getBankHandleService(apply.getDev_code());
            final List<ViewProcess> viewProcesses = handleService.initViewProcess(apply.getMerchant_sn());
            if (CollectionUtils.isEmpty(viewProcesses)) {
                return;
            }
            final Map<Integer, ViewProcess> processMap = viewProcesses.stream().collect(Collectors.toMap(ViewProcess::getViewStatus, x -> x, (key1, key2) -> key1));
            //传过来的状态不存在
            if (!processMap.containsKey(viewStatus)) {
                return;
            }
            //初始化 这一步也是为了兼容之前没有的数据这样就不需要清洗数据了
            viewProcesses.forEach(process -> {
                if (process.getViewStatus() <= viewStatus) {
                    process.setFinish(Boolean.TRUE);
                    final String timeTemplate = process.getTime();
                    final String time = timeTemplate.replace("#", StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", date));
                    process.setTime(time);
                }
            });
            extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, viewProcesses);
        } else {
            final List<ViewProcess> processes = JSONObject.parseArray(processStr, ViewProcess.class);
            //状态已经处于使用中就直接返回,避免长时间调用数据库
            final boolean match = processes.stream().anyMatch(x -> Objects.equals(x.getViewStatus(), viewStatus) && x.getFinish());
            if (match) {
                return;
            }
            processes.forEach(process -> {
                if (process.getViewStatus() <= viewStatus && !process.getFinish()) {
                    process.setFinish(Boolean.TRUE);
                    final String timeTemplate = process.getTime();
                    final String time = timeTemplate.replace("#", StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", date));
                    process.setTime(time);
                }
            });
            extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, processes);
        }
        apply.setExtra(JSONObject.toJSONString(extraMap));
        bankDirectApplyMapper.updateByPrimaryKeySelective(apply);
    }
}
