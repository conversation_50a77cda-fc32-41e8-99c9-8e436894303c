package com.wosai.upay.job.xxljob.threadpool;

import lombok.Getter;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2025/3/14
 */
@Getter
public class JobThreadPoolExecutor extends ThreadPoolExecutor {

    private final String poolName;
    private final long awaitTimeInSeconds;
    public JobThreadPoolExecutor(String poolName, long awaitTimeInSeconds, int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
        this.poolName = poolName;
        this.awaitTimeInSeconds = awaitTimeInSeconds;
    }

    public JobThreadPoolExecutor(String poolName, long awaitTimeInSeconds, int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
        this.poolName = poolName;
        this.awaitTimeInSeconds = awaitTimeInSeconds;
    }

    public JobThreadPoolExecutor(String poolName, long awaitTimeInSeconds, int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
        this.poolName = poolName;
        this.awaitTimeInSeconds = awaitTimeInSeconds;
    }

    public JobThreadPoolExecutor(String poolName, long awaitTimeInSeconds, int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
        this.poolName = poolName;
        this.awaitTimeInSeconds = awaitTimeInSeconds;
    }

}
