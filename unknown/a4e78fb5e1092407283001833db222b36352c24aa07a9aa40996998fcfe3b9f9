/*
 * 版权说明：
 *1.中国银联股份有限公司（以下简称“中国银联”）对该代码保留全部知识产权权利， 包括但不限于版权、专利、商标、商业秘密等。
 *  任何人对该代码的任何使用都要 受限于在中国银联成员机构服务平台（http://member.unionpay.com/）与中国银
 *  联签 署的协议之规定。中国银联不对该代码的错误或疏漏以及由此导致的任何损失负 任何责任。中国银联针  对该代码放弃所有明
 *  示或暗示的保证,包括但不限于不侵 犯第三方知识产权。
 *
 *2.未经中国银联书面同意，您不得将该代码用于与中国银联合作事项之外的用途和目的。未经中国银联书面同意，不得下载、
 *  转发、公开或以其它任何形式向第三方提供该代码。如果您通过非法渠道获得该代码，请立即删除，并通过合法渠道 向中国银
 *  联申请。
 *
 *3.中国银联对该代码或与其相关的文档是否涉及第三方的知识产权（如加密算法可 能在某些国家受专利保护）不做任何声明和担
 *  保，中国银联对于该代码的使用是否侵犯第三方权利不承担任何责任，包括但不限于对该代码的部分或全部使用。
 *
 */
package com.wosai.upay.job.util.luzhou;

import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.security.Security;


/**
 * SM4 对称、分组加密：置换替换
 */
public class SimpleSm4Utils {
    private static final Logger logger = LoggerFactory.getLogger(SimpleSm4Utils.class);
    public static final String ENCODING = "UTF-8";
    /**
     * 文件编码类型
     */
    public static final String CS_UTF8 = "UTF-8";
    public static final String CS_GBK = "GBK";
    public static final String CS_GB2312 = "GB2312";

    /**
     * 工作模式
     */
    //电子密码本模式
    public static final String MODE_EBC = "EBC";
    //加密分组链接模式
    public static final String MODE_CBC = "CBC";
    //加密反馈模式
    public static final String MODE_CFB = "CFB";
    //输出反馈模式
    public static final String MODE_OFB = "OFB";

    /**
     * 填充模式
     */
    public static final String PAD_NO = "NoPadding";
    public static final String PAD_PKCS1 = "PKCS1Padding";
    public static final String PAD_PKCS5 = "PKCS5Padding";

    public static final String ALGORITHM_NAME = "SM4";
    // 128-32位16进制；256-64位16进制
    public static final int DEFAULT_KEY_SIZE = 128;

    /**
     * SM4 Cbc模式 加密
     *
     * @param key     密钥
     * @param data    明文
     * @param padMode 填充模式
     * @return 密文
     */
    public static byte[] sm4CbcEncrypt(byte[] key, byte[] data, String padMode) {
        byte[] res = null;
        String algorithm = "SM4/CBC/" + padMode;
        try {
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance(algorithm);
            SecretKeySpec secretKeySpec = getSm4Key(key);
            IvParameterSpec ivParameterSpec = getIv(cipher.getBlockSize());
            byte[] padData = padding(data, cipher.getBlockSize());
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
            res = cipher.doFinal(padData);
            return res;
        } catch (Exception e) {
            logger.error("Fail: Sm4 Cbc Encrypt", e);
        }
        return res;
    }

    /**
     * SM4 Cbc模式 解密
     *
     * @param key     密钥
     * @param data    密文
     * @param padMode 填充模式
     * @return 明文
     */
    public static byte[] sm4CbcDecrypt(byte[] key, byte[] data, String padMode) {
        byte[] res = null;
        String algorithm = "SM4/CBC/" + padMode;
        try {
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance(algorithm);
            SecretKeySpec secretKeySpec = getSm4Key(key);
            IvParameterSpec ivParameterSpec = getIv(cipher.getBlockSize());
            byte[] padData = padding(data, cipher.getBlockSize());
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
            res = cipher.doFinal(padData);
            return res;
        } catch (Exception e) {
            logger.error("Fail: Sm4 Cbc Decrypt", e);
        }
        return res;
    }

    /**
     * SM4 Ecb模式 加密
     *
     * @param key     密钥
     * @param data    明文
     * @param padMode 填充模式
     * @return 密文
     */
    public static byte[] sm4EcbEncrypt(byte[] key, byte[] data, String padMode) {
        byte[] res = null;
        String algorithm = "SM4/ECB/" + padMode;
        try {
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance(algorithm, BouncyCastleProvider.PROVIDER_NAME);
            SecretKeySpec secretKeySpec = getSm4Key(key);
//            byte[] padData = padding(data, cipher.getBlockSize());
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
//            res = cipher.doFinal(padData);
            res = cipher.doFinal(data);
        } catch (Exception e) {
            logger.error("Fail: Sm4 Ecb Encrypt", e);
        }
        return res;
    }

    /**
     * SM4 Ecb模式 解密
     *
     * @param key     密钥
     * @param data    密文
     * @param padMode 填充模式
     * @return 明文
     */
    public static byte[] sm4EcbDecrypt(byte[] key, byte[] data, String padMode) throws Exception {
        byte[] res = null;
        String algorithm = "SM4/ECB/" + padMode;
        Security.addProvider(new BouncyCastleProvider());
        Cipher cipher = Cipher.getInstance(algorithm, BouncyCastleProvider.PROVIDER_NAME);
        SecretKeySpec secretKeySpec = getSm4Key(key);
//			byte[] padData = padding(data, cipher.getBlockSize());
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        res = cipher.doFinal(data);
        return res;
    }

    /**
     * 生成国密Key：SM4，密钥为 128bit， 16byte
     */
    public static SecretKeySpec getSm4Key(byte[] key) {
        if (key.length != 16) {
            logger.error("SM4's key should be 16bytes, 128bits");
        }
        return new SecretKeySpec(key, "SM4");
    }

    /**
     * 初始化向量
     *
     * @param len 长度
     * @return
     */
    public static IvParameterSpec getIv(int len) {
        //使用 IV 的例子是反馈模式中的密码，如，CBC 模式中的 DES 和使用 OAEP 编码操作的 RSA 密码
        byte[] zero = new byte[len];
        IvParameterSpec ivps = new IvParameterSpec(zero);
        return ivps;
    }

    /**
     * 补足长度
     *
     * @param src
     * @param len
     * @return
     */
    public static byte[] padding(byte[] src, int len) {
        int paddingLength = len - src.length % len;
        if (len == paddingLength) {
            return src;
        }
        byte[] newsrc = new byte[src.length + paddingLength];
        System.arraycopy(src, 0, newsrc, 0, src.length);
        return newsrc;
    }


    /**
     * 敏感信息SM4加密，敏感信息密钥SM2加密
     * <p>
     * 过程说明：先对报文中<SensInf></SensInf>标签中的所有内容（不包括包括<SensInf>和</SensInf>标签自身）
     * 进行加密，再对加密所用密钥进行加密，最后对加密后的敏感信息与加密后的敏感信息加密密钥进行base64编码
     *
     * @param dataInfo 敏感信息明文
     * @param encKey   敏感信息对称密钥明文
     * @return 数组 {加密且base64的敏感信息, 加密且base64的敏感信息对称密钥}
     */
    public static String sensInfSM4KeyEncrypt(String dataInfo, String encKey, String ecoding, String padMode) throws Exception {
        byte[] sensInf = dataInfo.getBytes(ecoding);
        logger.debug("sensInfo hex:" + Hex.toHexString(sensInf));
        byte[] keyData = ByteUtils.fromHexString(encKey);
        byte[] sensEnc = SimpleSm4Utils.sm4EcbEncrypt(keyData, sensInf, padMode);
        logger.debug("sensInfo enc hex:" + Hex.toHexString(sensEnc));
        String sensInfEB = ByteUtils.toHexString(sensEnc);
//        String sensInfEB = Base64.encodeBase64String(sensEnc);
        logger.debug("sensInfo enc base64:" + sensInfEB);

        return sensInfEB;
    }


    /**
     * 敏感信息SM4解密，敏感信息密钥SM2解密
     * <p>
     * 过程说明： 先解密出所用密钥（即用于敏感信息解密的密钥），再对报文中<SensInf></SensInf>标签中的所有内容
     * （不包括包括<SensInf>和</SensInf>标签自身）进行解密
     *
     * @param sensInfEB 敏感信息密文
     * @return 数组 {敏感信息, 敏感信息对称密钥}
     */
    public static String sensInfSM4KeyDecrypt(String encKey, String sensInfEB, String padMode, String encoding) throws Exception {
        // sensInf: SM4 cbc nopadding
        logger.debug("sensInfo ciphertext:" + sensInfEB);
//        byte[] sensInfE = Base64.decodeBase64(sensInfEB);
        byte[] sensInfE = ByteUtils.fromHexString(sensInfEB);
        byte[] encBtKey = ByteUtils.fromHexString(encKey);
        byte[] sensInf = SimpleSm4Utils.sm4EcbDecrypt(encBtKey, sensInfE, padMode);
        logger.debug("sensInfo hex:" + Hex.toHexString(sensInf));

        return new String(sensInf, encoding);
    }


    /**
     * SM4加密
     *
     * @param key 密钥
     * @param str 原始数据
     * @return 加密后数据
     */
    public static String encryptBySM4(String key, String str) {
        return HexUtil.encodeHexStr(SmUtil.sm4(HexUtil.decodeHex(key)).encrypt(str));
    }

    /**
     * SM4解密
     *
     * @param key 密钥
     * @param str 密文
     * @return 解密后数据
     */
    public static String decryptBySM4(String key, String str) {
        return StrUtil.utf8Str(SmUtil.sm4(HexUtil.decodeHex(key)).decrypt(str));
    }

    /**
     * 自动生成密钥
     * @explain
     * @return
     * @throws Exception
     */
    public static String generateKeyHex() throws Exception {
        return HexUtil.encodeHexStr(generateKey(DEFAULT_KEY_SIZE));
    }

    /**
     * 自动生成密钥
     * @explain
     * @return
     * @throws Exception
     */
    public static byte[] generateKey() throws Exception {
        return generateKey(DEFAULT_KEY_SIZE);
    }

    /**
     * @explain 系统产生秘钥
     * @param keySize
     * @return
     * @throws Exception
     */
    public static byte[] generateKey(int keySize) throws Exception {
        Security.addProvider(new BouncyCastleProvider());
        KeyGenerator kg = KeyGenerator.getInstance(ALGORITHM_NAME, BouncyCastleProvider.PROVIDER_NAME);
        kg.init(keySize, new SecureRandom());
        return kg.generateKey().getEncoded();
    }

}
