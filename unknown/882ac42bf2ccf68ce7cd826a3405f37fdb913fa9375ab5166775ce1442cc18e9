package com.wosai.upay.job.biz.directparams;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ConfigStatusEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.TradeConstants;
import com.wosai.upay.job.biz.AgentAppidBiz;
import com.wosai.upay.job.biz.ChangeTradeParamsBiz;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.directparams.BaseParams;
import com.wosai.upay.job.model.directparams.BaseTradeParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.service.ConfigSupportService;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 处理直连参数相关逻辑
 *
 * <AUTHOR>
 * @date 2019-08-29
 */
public abstract class DirectParamsBiz {

    @Autowired
    protected MerchantProviderParamsMapper paramsMapper;

    @Autowired
    protected TradeConfigService tradeConfigService;

    @Autowired
    protected MerchantService merchantService;

    @Autowired
    protected SupportService supportService;

    @Autowired
    protected MerchantProviderParamsBiz paramsBiz;

    @Autowired
    private AgentAppidBiz agentAppidBiz;

    @Autowired
    private ConfigSupportService configSupportService;

    @Autowired
    private ChangeTradeParamsBiz tradeParamsBiz;

    @Autowired
    private ComposeAcquirerBiz acquirerBiz;
    @Autowired
    SubBizParamsBiz subBizParamsBiz;

    /**
     * 添加直连交易参数
     *
     * @param baseParams
     */
    public abstract void addDirectParams(BaseParams baseParams);

    /**
     * 删除直连交易参数
     * 如果直连参数没有对应的间连，则不调用 tradeConfigService 清除直连参数
     */
    public abstract void deleteDirectParams(MerchantProviderParamsDto params, String subPayway, String feeRate);

    public abstract BaseParams getDirectParams(String merchantSn);

    /**
     * 处理直连交易参数
     * 1、直连交易参数虽然在数据库存储为1条，但是要在前端展示成多条
     *
     * @param source
     * @return
     */
    public abstract List<MerchantProviderParamsCustomDto> handleDirectParams(MerchantProviderParamsCustomDto source);


    protected void checkMerchant(BaseParams baseParams) {
        Map merchant = merchantService.getMerchant(baseParams.getMerchant_id());
        if (merchant == null) {
            throw new CommonPubBizException("商户不存在");
        }
        if (!baseParams.getMerchant_sn().equals(WosaiMapUtils.getString(merchant, Merchant.SN))) {
            throw new CommonPubBizException("商户 id 和 sn 不匹配");
        }
    }

    protected boolean isNotEmpty(BaseTradeParams baseTradeParams) {
        return baseTradeParams != null && !baseTradeParams.empty();
    }

    protected boolean isEmpty(BaseTradeParams baseTradeParams) {
        return baseTradeParams == null || baseTradeParams.empty();
    }

    protected Map bean2Map(Object bean) {
        return JSON.parseObject(JSON.toJSONString(bean), Map.class);
    }

    protected void appendOldParams(Map directParams, Map oldParams) {
        directParams.putIfAbsent(TradeConstants.OLD_FEE_RATE, WosaiMapUtils.getString(oldParams, TradeConstants.OLD_FEE_RATE));
        directParams.putIfAbsent(TradeConstants.OLD_AGENT_NAME, WosaiMapUtils.getString(oldParams, TradeConstants.OLD_AGENT_NAME));
    }

    protected String getFeeRate(String feeRate, String defaultVal) {
        return WosaiStringUtils.isNotEmpty(feeRate) ? feeRate : defaultVal;
    }

    private static final List<String> AGENT_NAME_KEYS = Arrays.asList(
            MerchantConfig.B2C_AGENT_NAME,
            MerchantConfig.C2B_AGENT_NAME,
            MerchantConfig.WAP_AGENT_NAME,
            MerchantConfig.APP_AGENT_NAME,
            MerchantConfig.H5_AGENT_NAME,
            MerchantConfig.MINI_AGENT_NAME
    );

    protected String getAgentName(Map directParams, Map merchantConfig, int payWay, String merchantSn) {
        // 从 directParams 中获取
        String agentName = WosaiMapUtils.getString(directParams, TradeConstants.OLD_AGENT_NAME);
        if (WosaiStringUtils.isNotEmpty(agentName)) {
            return agentName;
        }

        // 从 merchantConfig 中获取
        for (String key : AGENT_NAME_KEYS) {
            if (WosaiStringUtils.isNotEmpty(WosaiMapUtils.getString(merchantConfig, key))) {
                return WosaiMapUtils.getString(merchantConfig, key);
            }
        }

        // 按照规则组装
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andPaywayEqualTo(payWay)
                .andProviderNotEqualTo(payWay)
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = paramsMapper.selectByExample(example);
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            MerchantProviderParams merchantProviderParams = params.get(0);
            String merchantCity = configSupportService.getMerchantCity(merchantSn);
            int provider = merchantProviderParams.getProvider();
            String channelNo = merchantProviderParams.getChannel_no();
            agentName = agentAppidBiz.getAgentName(payWay, provider, channelNo, merchantCity);
        }

        return agentName;
    }

    protected void setDefaultParams(String merchantSn, int payway) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(payway)
                .andParams_config_statusIn(Arrays.asList(ConfigStatusEnum.CONFIG_SUCCESS.getValue(), ConfigStatusEnum.NOT_REQUIRE_CONFIG.getValue()))
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            //优先切换到status=1的参数中
            MerchantProviderParams usingParam = params.stream().filter(param -> Objects.equals(param.getStatus(), UseStatusEnum.IN_USE.getValue())).findFirst().orElse(null);
            if(Objects.nonNull(usingParam)) {
                tradeParamsBiz.changeTradeParams(usingParam, null, false,subBizParamsBiz.getPayTradeAppId());
            }else {
                // 特殊处理
                boolean reContract = true;
                for (MerchantProviderParams param : params) {
                    if (!"77561047".equals(param.getChannel_no())) {
                        tradeParamsBiz.changeTradeParams(param, null, false,subBizParamsBiz.getPayTradeAppId());
                        reContract = false;
                        break;
                    }
                }
                if (reContract && payway == PaywayEnum.WEIXIN.getValue()) {
                    acquirerBiz.reContractWx(merchantSn, "直连切间连", false);
                }
            }
        }
    }

    /**
     * 查看当前在用交易参数是否为直连
     *
     * @param merchantSn
     * @param payway
     * @return true->是直连   false->不是直连
     */
    public Boolean checkDirect(String merchantSn, int payway) {
        MerchantProviderParams usedParam = paramsMapper.getDirectParam(merchantSn, payway);
        return usedParam != null;
    }
}
