package com.wosai.upay.job.biz.directparams;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pub.alipay.authinto.service.StoreService;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.Constants.TradeConstants;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.directparams.AlipayV2DirectParams;
import com.wosai.upay.job.model.directparams.BaseParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-08-29
 */
@Component
@Slf4j
public class AlipayV2DirectParamsBiz extends DirectParamsBiz {
    @Autowired
    private StoreService storeService;

    private static final int PAYWAY = PaywayEnum.ALIPAY.getValue();

    @Override
    public void addDirectParams(BaseParams baseParams) {
        checkMerchant(baseParams);

        AlipayV2DirectParams params = (AlipayV2DirectParams) baseParams;
        Map<String, Object> allDirectParams = new HashMap<>(5);

        MerchantProviderParamsDto dto = paramsBiz.getDirectParams(params.getMerchant_sn(), PAYWAY, PAYWAY);
        if (dto == null) {
            dto = new MerchantProviderParamsDto()
                    .setMerchant_sn(params.getMerchant_sn())
                    .setProvider(PAYWAY)
                    .setPayway(PAYWAY);

        } else {
            allDirectParams = dto.getExtra();
        }

        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(baseParams.getMerchant_id(), PAYWAY);
        Map updateInfo = new HashMap();

        if (isNotEmpty(params.getAlipay_v2_trade_params())) {
            Map<String, Object> directParams = bean2Map(params.getAlipay_v2_trade_params());
            appendOldParams(directParams, WosaiMapUtils.getMap(allDirectParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS));
            directParams.putIfAbsent(TradeConstants.OLD_FEE_RATE, WosaiMapUtils.getString(merchantConfig, MerchantConfig.C2B_FEE_RATE));
            directParams.putIfAbsent(TradeConstants.OLD_AGENT_NAME, WosaiMapUtils.getString(merchantConfig, MerchantConfig.C2B_AGENT_NAME));
            allDirectParams.put(TransactionParam.ALIPAY_V2_TRADE_PARAMS, directParams);

            tradeConfigService.updateAlipayV2TradeParams(params.getMerchant_id(), directParams);

            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.B2C_FEE_RATE, params.getAlipay_v2_trade_params().getFee_rate(),
                    MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.C2B_FEE_RATE, params.getAlipay_v2_trade_params().getFee_rate()
            ));
        }

        if (isNotEmpty(params.getAlipay_wap_v2_trade_params())) {
            Map<String, Object> directParams = bean2Map(params.getAlipay_wap_v2_trade_params());
            appendOldParams(directParams, WosaiMapUtils.getMap(allDirectParams, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS));
            directParams.putIfAbsent(TradeConstants.OLD_FEE_RATE, WosaiMapUtils.getString(merchantConfig, MerchantConfig.WAP_FEE_RATE));
            directParams.putIfAbsent(TradeConstants.OLD_AGENT_NAME, WosaiMapUtils.getString(merchantConfig, MerchantConfig.WAP_AGENT_NAME));
            allDirectParams.put(TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS, directParams);

            tradeConfigService.updateAlipayV2WapTradeParams(params.getMerchant_id(), directParams);

            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.WAP_FEE_RATE, params.getAlipay_wap_v2_trade_params().getFee_rate()
            ));
        }

        if (isNotEmpty(params.getAlipay_app_v2_trade_params())) {
            Map<String, Object> directParams = bean2Map(params.getAlipay_app_v2_trade_params());
            appendOldParams(directParams, WosaiMapUtils.getMap(allDirectParams, TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS));
            directParams.putIfAbsent(TradeConstants.OLD_FEE_RATE, WosaiMapUtils.getString(merchantConfig, MerchantConfig.APP_FEE_RATE));
            directParams.putIfAbsent(TradeConstants.OLD_AGENT_NAME, WosaiMapUtils.getString(merchantConfig, MerchantConfig.APP_AGENT_NAME));
            allDirectParams.put(TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS, directParams);

            tradeConfigService.updateAlipayV2AppTradeParams(params.getMerchant_id(), directParams);

            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.APP_FEE_RATE, params.getAlipay_app_v2_trade_params().getFee_rate()
            ));
        }

        if (isNotEmpty(params.getAlipay_h5_v2_trade_params())) {
            Map<String, Object> directParams = bean2Map(params.getAlipay_h5_v2_trade_params());
            appendOldParams(directParams, WosaiMapUtils.getMap(allDirectParams, TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS));
            directParams.putIfAbsent(TradeConstants.OLD_FEE_RATE, WosaiMapUtils.getString(merchantConfig, MerchantConfig.H5_FEE_RATE));
            directParams.putIfAbsent(TradeConstants.OLD_AGENT_NAME, WosaiMapUtils.getString(merchantConfig, MerchantConfig.H5_AGENT_NAME));
            allDirectParams.put(TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS, directParams);

            tradeConfigService.updateAlipayV2H5TradeParams(params.getMerchant_id(), directParams);

            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.H5_FEE_RATE, params.getAlipay_h5_v2_trade_params().getFee_rate()
            ));
        }

        dto.setExtra(allDirectParams);
        dto.setPay_merchant_id(getAlipayDirectMerchantSn(dto.getMerchant_sn()));
        paramsBiz.saveDirectMerchantProviderParams(dto);

        if (!updateInfo.isEmpty()) {
            // 未开通间联直接导参数，前面查询为空
            if (WosaiMapUtils.isEmpty(merchantConfig)) {
                merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(baseParams.getMerchant_id(), PAYWAY);
            }
            updateInfo.putAll(
                    CollectionUtil.hashMap(
                            DaoConstants.ID, WosaiMapUtils.getString(merchantConfig, DaoConstants.ID)
                    )
            );
            tradeConfigService.updateMerchantConfig(updateInfo);
        }

        supportService.removeCachedParams(params.getMerchant_sn());

    }

    @Override
    public void deleteDirectParams(MerchantProviderParamsDto params, String subPayway, String feeRate) {
        // 支付宝直连切间连，费率统一改成 0.38
        feeRate = "0.38";
        Map merchant = merchantService.getMerchantBySn(params.getMerchant_sn());
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);
        if (WosaiStringUtils.isEmpty(subPayway)) {
            throw new CommonPubBizException("sub_payway 不能为空");
        }

        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY);
        Map merchantConfigParams = WosaiMapUtils.getMap(merchantConfig, MerchantConfig.PARAMS, new HashMap());
        Map updateInfo = new HashMap();

        Map extra = params.getExtra();

        if (subPayway.equals(TradeConstants.SUB_PAYWAY_TRADE_NAME)) {
            Map directParams = (Map) extra.remove(TransactionParam.ALIPAY_V2_TRADE_PARAMS);
            feeRate = getFeeRate(feeRate, WosaiMapUtils.getString(directParams, TradeConstants.OLD_FEE_RATE));

            merchantConfigParams.remove(TransactionParam.ALIPAY_V2_TRADE_PARAMS);
            String agentName = getAgentName(directParams, merchantConfig, PAYWAY, params.getMerchant_sn());
            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.B2C_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.B2C_AGENT_NAME, agentName,
                    MerchantConfig.B2C_FEE_RATE, feeRate,
                    MerchantConfig.C2B_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.C2B_AGENT_NAME, agentName,
                    MerchantConfig.C2B_FEE_RATE, feeRate
            ));

        }

        if (subPayway.equals(TradeConstants.SUB_PAYWAY_WAP_NAME)) {
            Map directParams = (Map) extra.remove(TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS);
            feeRate = getFeeRate(feeRate, WosaiMapUtils.getString(directParams, TradeConstants.OLD_FEE_RATE));

            merchantConfigParams.remove(TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS);
            String agentName = getAgentName(directParams, merchantConfig, PAYWAY, params.getMerchant_sn());
            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.WAP_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.WAP_AGENT_NAME, agentName,
                    MerchantConfig.WAP_FEE_RATE, feeRate
            ));
        }


        if (subPayway.equals(TradeConstants.SUB_PAYWAY_APP_NAME)) {
            Map directParams = (Map) extra.remove(TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS);
            feeRate = getFeeRate(feeRate, WosaiMapUtils.getString(directParams, TradeConstants.OLD_FEE_RATE));

            merchantConfigParams.remove(TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS);
            String agentName = getAgentName(directParams, merchantConfig, PAYWAY, params.getMerchant_sn());
            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.APP_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.APP_AGENT_NAME, agentName,
                    MerchantConfig.APP_FEE_RATE, feeRate
            ));
        }

        if (subPayway.equals(TradeConstants.SUB_PAYWAY_H5_NAME)) {
            Map directParams = (Map) extra.remove(TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS);
            feeRate = getFeeRate(feeRate, WosaiMapUtils.getString(directParams, TradeConstants.OLD_FEE_RATE));

            merchantConfigParams.remove(TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS);
            String agentName = getAgentName(directParams, merchantConfig, PAYWAY, params.getMerchant_sn());
            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.H5_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.H5_AGENT_NAME, agentName,
                    MerchantConfig.H5_FEE_RATE, feeRate
            ));

        }

        if (extra.size() == 0) {
            paramsBiz.deleteParamsById(params.getId());
        } else {
            paramsBiz.updateByPrimaryKeySelective(
                    new MerchantProviderParamsDto().setId(params.getId())
                            .setExtra(extra)
            );
        }

        if (!updateInfo.isEmpty()) {
            updateInfo.putAll(
                    CollectionUtil.hashMap(
                            DaoConstants.ID, WosaiMapUtils.getString(merchantConfig, DaoConstants.ID),
                            MerchantConfig.PARAMS, merchantConfigParams
                    )
            );
            tradeConfigService.updateMerchantConfig(updateInfo);
        }

        if (extra.size() == 0) {
            //同时将sub_biz_params中的直连参数删除
            subBizParamsBiz.deleteDirectSubBizParams(params.getMerchant_sn(), PAYWAY);
            setDefaultParams(params.getMerchant_sn(), PAYWAY);
        }

        supportService.removeCachedParams(params.getMerchant_sn());

    }

    @Override
    public AlipayV2DirectParams getDirectParams(String merchantSn) {
        MerchantProviderParams providerParams = paramsMapper.getDirectParam(merchantSn, PAYWAY);
        if (Objects.isNull(providerParams)) {
            return null;
        }
        return JSONObject.parseObject(providerParams.getExtra(), AlipayV2DirectParams.class);
    }


    @Override
    public List<MerchantProviderParamsCustomDto> handleDirectParams(MerchantProviderParamsCustomDto source) {
        List<MerchantProviderParamsCustomDto> result = new ArrayList<>();
        if (WosaiMapUtils.isEmpty(source.getExtra())) {
            return result;
        }
        AlipayV2DirectParams params = JSON.parseObject(JSON.toJSONString(source.getExtra()), AlipayV2DirectParams.class);
        getResult(source, result, params.getAlipay_v2_trade_params(), TradeConstants.SUB_PAYWAY_TRADE_NAME, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        getResult(source, result, params.getAlipay_wap_v2_trade_params(), TradeConstants.SUB_PAYWAY_WAP_NAME, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS);
        if (params.getAlipay_app_v2_trade_params() != null) {
            MerchantProviderParamsCustomDto dto = new MerchantProviderParamsCustomDto();
            BeanUtils.copyProperties(source, dto);
            dto.setPay_merchant_id("");
            dto.setSub_payway(TradeConstants.SUB_PAYWAY_APP_NAME);
            dto.setExtra(WosaiMapUtils.getMap(source.getExtra(), TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS));
            result.add(dto);
        }
        if (params.getAlipay_h5_v2_trade_params() != null) {
            MerchantProviderParamsCustomDto dto = new MerchantProviderParamsCustomDto();
            BeanUtils.copyProperties(source, dto);
            dto.setPay_merchant_id("");
            dto.setSub_payway(TradeConstants.SUB_PAYWAY_H5_NAME);
            dto.setExtra(WosaiMapUtils.getMap(source.getExtra(), TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS));
            result.add(dto);
        }


        return result;
    }

    private List<MerchantProviderParamsCustomDto> getResult(MerchantProviderParamsCustomDto source, List<MerchantProviderParamsCustomDto> result,
                                                            AlipayV2DirectParams.AlipayV2Params alipayV2Params, String sub_way, String extraValue) {
        if (alipayV2Params != null) {
            MerchantProviderParamsCustomDto dto = new MerchantProviderParamsCustomDto();
            BeanUtils.copyProperties(source, dto);
            if (WosaiStringUtils.isEmpty(dto.getPay_merchant_id())) {
                String mch_id = alipayV2Params.getMch_id();
                if (StringUtils.isEmpty(mch_id)) {
                    mch_id = getAlipayDirectMerchantSn(source.getMerchant_sn());
                }
                dto.setPay_merchant_id(mch_id);
            }
            dto.setSub_payway(sub_way);
            dto.setExtra(WosaiMapUtils.getMap(source.getExtra(), extraValue));
            result.add(dto);
        }
        return result;
    }

    private String getAlipayDirectMerchantSn(String merchantSn) {
        try {
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            String merchantId = (String) merchant.get("id");
            return storeService.getStore(merchantId).getAlipayOauth().getUserId();
        } catch (Exception e) {
            log.error("查询支付宝直连商户号异常", e);
        }
        return StringUtils.EMPTY;
    }
}
