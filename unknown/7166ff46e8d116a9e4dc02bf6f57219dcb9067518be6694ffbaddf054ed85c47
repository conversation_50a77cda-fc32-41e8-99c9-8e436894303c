package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.model.SpaResponse;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.service.SpaService;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.acquirer.ByPassTradeConfig;
import com.wosai.upay.job.service.ProviderTradeParamsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 预下单处理
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
@Component
@Slf4j
public class ChangeToLklBiz {

    private static final String UPAY_GATEWAY_PRECREATE = "http://upay-gateway.vpc.shouqianba.com/upay/v2/precreate";
    private static final int INVOKE_SUCCESS_CODE = 200;
    private static final String RESULT_CODE = "result_code";
    private static final String PRECREATE_SUCCESS_RESULT_CODE = "PRECREATE_SUCCESS";

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private ProviderTradeParamsService providerTradeParamsService;

    @Autowired
    private SpaService spaService;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    public void check(String merchantId) {

        List<ByPassTradeConfig> tradeConfig = providerTradeParamsService.getByPassTradeConfig(merchantId, Arrays.asList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()));
        if (WosaiCollectionUtils.isEmpty(tradeConfig)) {
            throw new CommonInvalidParameterException("参数不全");
        }

        checkAuth(tradeConfig);
        if (applicationApolloConfig.getChangeToLklBizCheckPreCreate()) {
            checkPreCreate(tradeConfig);
        }
    }

    private void checkAuth(List<ByPassTradeConfig> tradeConfig) {
        Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName("provider_trade_params_key");
        String aliMchId = null;
        for (ByPassTradeConfig config : tradeConfig) {
            if (PaywayEnum.ALIPAY.getValue().equals(config.getPayway())) {
                String tradeParamKey = BeanUtil.getPropString(providerTradeParamsKey, String.valueOf(config.getProvider()));
                aliMchId = BeanUtil.getPropString(config.getParams(), tradeParamKey+".alipay_sub_mch_id");
            }
        }

        String wxMchId = null;
        for (ByPassTradeConfig config : tradeConfig) {
            if (PaywayEnum.WEIXIN.getValue().equals(config.getPayway())) {
                String tradeParamKey = BeanUtil.getPropString(providerTradeParamsKey, String.valueOf(config.getProvider()));
                wxMchId = BeanUtil.getPropString(config.getParams(), tradeParamKey+".weixin_sub_mch_id");
            }
        }

        if (WosaiStringUtils.isEmpty(aliMchId) || WosaiStringUtils.isEmpty(wxMchId)) {
            throw new CommonInvalidParameterException("参数不全");
        }

        SpaResponse aliResp = spaService.querySubMchIdAlipayAuth(aliMchId, "contract");
        boolean checkAliAuth = applicationApolloConfig.getCheckAliAuth();
        if (checkAliAuth && !aliResp.getSuccess()) {
            throw new CommonInvalidParameterException("支付宝未授权");
        }

        SpaResponse wxResp = spaService.querySubMchIdWechatAuth(wxMchId, "contract");
        if (!wxResp.getSuccess()) {
            throw new CommonInvalidParameterException("微信未授权");
        }

    }

    private static final Map APPEND_MERCHANT_CONFIG = CollectionUtil.hashMap(
            MerchantConfig.B2C_FORMAL, 0,
            MerchantConfig.C2B_FORMAL, 0,
            MerchantConfig.WAP_FORMAL, 0,
            MerchantConfig.MINI_FORMAL, 0,
            MerchantConfig.APP_FORMAL, 0,
            MerchantConfig.H5_FORMAL, 0,
            MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
            MerchantConfig.B2C_FEE_RATE, "0.38",
            MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
            MerchantConfig.C2B_FEE_RATE, "0.38",
            MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
            MerchantConfig.WAP_FEE_RATE, "0.38",
            MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
            MerchantConfig.MINI_FEE_RATE, "0.38");

    public void checkPreCreate(List<ByPassTradeConfig> tradeConfig) {
        String merchantId = "11d84950-9dfc-46fc-b876-1c4754ad7fef";
        String terminalSn = "100000330022104407";
        String merchantSn = "1680001829813";
        Map aliConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.ALIPAY.getValue());
        for (ByPassTradeConfig config : tradeConfig) {
            if (PaywayEnum.ALIPAY.getValue().equals(config.getPayway())) {
                replaceParams(aliConfig, config);
            }
        }

        Map weixinConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.WEIXIN.getValue());
        for (ByPassTradeConfig config : tradeConfig) {
            if (PaywayEnum.WEIXIN.getValue().equals(config.getPayway())) {
                replaceParams(weixinConfig, config);
            }
        }

        supportService.removeCachedParams(merchantSn);

        preCreate(PaywayEnum.ALIPAY.getValue(), terminalSn);
        preCreate(PaywayEnum.WEIXIN.getValue(), terminalSn);
    }

    private void replaceParams(Map merchantConfig, ByPassTradeConfig config) {
        Map updateConfig = CollectionUtil.hashMap(CommonModel.ID, MapUtils.getString(merchantConfig, CommonModel.ID));
        updateConfig.putAll(JSON.parseObject(JSON.toJSONString(config), Map.class));
        updateConfig.putAll(APPEND_MERCHANT_CONFIG);
        tradeConfigService.updateMerchantConfig(updateConfig);
    }

    private void preCreate(int payway, String terminalSn) {
        //支付网关预下单接口 precreate()，验证结果
        Map response;
        try {
            RestTemplate restTemplate = new RestTemplate();
            Map request = CollectionUtil.hashMap(
                    "terminal_sn", terminalSn,
                    "client_sn", String.valueOf(System.currentTimeMillis()),
                    "total_amount", "1",
                    "payway", String.valueOf(payway),
                    "subject", "切换收单机构 预下单校验",
                    "operator", "merchant-contract-job"
            );
            response = restTemplate.postForObject(UPAY_GATEWAY_PRECREATE, request, Map.class);
        } catch (Exception e) {
            log.error("ChangeToLklBiz importParams precreate error", e);
            throw new CommonInvalidParameterException("预下单失败");
        }
        if (INVOKE_SUCCESS_CODE != MapUtil.getInteger(response, RESULT_CODE)) {
            throw new CommonInvalidParameterException("预下单失败 :" + MapUtil.getString(response, "error_message"));
        }
        Map bizResponse = MapUtil.getMap(response, "biz_response");
        String resultCode = MapUtil.getString(bizResponse, "result_code");
        //预下单验证失败的情况下用订单号获取流水信息中的具体失败原因抛出
        if (!Objects.equals(PRECREATE_SUCCESS_RESULT_CODE, resultCode)) {
            Map data = MapUtils.getMap(bizResponse, "data");
            String orderSn = MapUtils.getString(data, "sn");
            throw new CommonInvalidParameterException("预下单失败 " + orderSn);
        }
    }
}
