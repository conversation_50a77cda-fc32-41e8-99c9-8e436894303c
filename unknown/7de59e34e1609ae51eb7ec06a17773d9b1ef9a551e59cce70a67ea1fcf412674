package com.wosai.upay.job.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class EnvUtil {
    @Value("${spring.profiles.active:default}")
    private String env;

    public boolean isProd() {
        return "prod".equals(env);
    }

    public boolean isDev() {
        return "dev".equals(env) || "default".equals(env);
    }

    public boolean isMock() {
        return "mock".equals(env) || "beta".equals(env);
    }
}
