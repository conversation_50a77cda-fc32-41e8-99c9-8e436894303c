package com.wosai.upay.job.model.callback.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 泸州银行方商户审核通过后，使用该请求 请求我们配置的回调接口。
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LuzhouAuditPassCallBack extends LuzhouCallBackReqBasic {
    /**
     * 入网审核状态，CHECKED -审核通过
     */
    private String checkProcess;
    /**
     * 审核信息
     */
    private String checkMsg;

    public boolean isAuditPass() {
        return "CHECKED".equals(checkProcess);
    }

}
