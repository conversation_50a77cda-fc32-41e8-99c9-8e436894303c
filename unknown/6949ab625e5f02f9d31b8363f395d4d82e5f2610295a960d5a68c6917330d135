package com.wosai.upay.job.enume;

import java.util.Objects;

/**
 * @Description:任务状态枚举
 * <AUTHOR>
 * Date 2019/10/24 11:23 上午
 **/

public enum TaskStatus {
    //开始状态
    PENDING("待处理", 0),

    //中间状态
    PROGRESSING("处理中", 1),

    //等待微信授权
    WAIT_FOR_AUTH("等待授权", 2),

    //限于对公商户入网使用
    PAY_FOR_WAIT("代付验证中", 3),

    //结束状态
    SUCCESS("处理成功", 5),

    FAIL("处理失败", 6);


    private String msg;
    private Integer val;

    TaskStatus(String msg, Integer val) {
        this.msg = msg;
        this.val = val;
    }


    public static TaskStatus toStatus(Integer val) {
        if (val == null) {
            return null;
        }
        for (TaskStatus status : TaskStatus.values()) {
            if (status.getVal().equals(val)) {
                return status;
            }
        }
        return null;
    }


    public static boolean isFinish(Integer val) {
        TaskStatus status = toStatus(val);
        return SUCCESS.equals(status) || FAIL.equals(status);
    }

    public static boolean inAuth(Integer val){
        return WAIT_FOR_AUTH.equals(toStatus(val));
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getVal() {
        return val;
    }

    public void setVal(Integer val) {
        this.val = val;
    }

    public static String getMessage(Integer code) {
        final TaskStatus[] taskStatuses = values();
        for (TaskStatus description : taskStatuses) {
            if (Objects.equals(description.getVal(), code)) {
                return description.getMsg();
            }
        }
        return "未知状态";
    }
}
