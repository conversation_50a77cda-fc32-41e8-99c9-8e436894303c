package com.wosai.upay.job.util;

import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.bank.info.api.service.BankNamesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class TongLianUtil {
    @Autowired
    BankNamesService bankNamesService;

    public boolean isTongLianSupportBank(String bankName) {
        if (WosaiStringUtils.isEmpty(bankName)) {
            return false;
        }
        String tlCode = bankNamesService.getTlBankcodeByBankname(bankName);
        return !WosaiStringUtils.isEmpty(tlCode);
    }
}