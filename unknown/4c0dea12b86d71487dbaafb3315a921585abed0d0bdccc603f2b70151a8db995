package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.mapper.MerchantUpgradeTaskMapper;
import com.wosai.upay.job.model.MerchantUpgradeRequest;
import com.wosai.upay.job.model.MerchantUpgradeTask;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-23
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class MerchantUpgradeServiceImpl implements MerchantUpgradeService{

    public static final Logger logger = LoggerFactory.getLogger(MerchantUpgradeService.class);


    @Autowired
    private MerchantUpgradeTaskMapper merchantUpgradeTaskMapper;

    @Override
    public boolean addMerchantUpgradeTask(MerchantUpgradeRequest request) {
        try {
            List<MerchantUpgradeTask> taskList = merchantUpgradeTaskMapper.selectByMerchantSnAndType(request.getMerchant_sn(), request.getType());
            if(!CollectionUtils.isEmpty(taskList)){
                for(MerchantUpgradeTask task : taskList){
                    // 非失败状态切通道任务只有一次
                    if(task.getType() == 0 && Integer.valueOf(task.getStatus()) != 6){
                        return true;
                    }
                    // 新增备用通道工单只发一次
                    if(task.getType() == 2){
                        return true;
                    }
                }
            }
            MerchantUpgradeTask merchantUpgradeTask = new MerchantUpgradeTask();
            merchantUpgradeTask.setMerchant_sn(request.getMerchant_sn());
            merchantUpgradeTask.setStatus(request.getStatus().toString());
            merchantUpgradeTask.setType(request.getType());
            merchantUpgradeTask.setMerchant_id(request.getMerchant_id());
            merchantUpgradeTask.setMessage(request.getMessage());
            merchantUpgradeTask.setTarget_acquirer(request.getTarget_acquirer());
            merchantUpgradeTaskMapper.insertSelective(merchantUpgradeTask);
        }catch (Exception e){
            logger.error("提交小微商户升级营业执照异常:" + request.getMerchant_sn(), e);
            return false;
        }
        return true;
    }

}
