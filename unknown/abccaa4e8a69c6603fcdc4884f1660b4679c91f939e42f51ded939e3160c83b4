package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.bsm.creditpaybackend.service.FitnessNotifyService;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.upay.bank.info.api.model.Industry;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.AntShopTaskConstant;
import com.wosai.upay.job.Constants.PayLaterConstant;
import com.wosai.upay.job.Constants.ZftMerchantConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.mapper.AntShopTaskMapper;
import com.wosai.upay.job.mapper.PayLaterApplyMapper;
import com.wosai.upay.job.mapper.ZftMerchantApplyMapper;
import com.wosai.upay.job.model.DO.AntShopTask;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.payLater.*;
import com.wosai.upay.job.model.payLater.ProcessEnum.SpaStageStatusEnum;
import com.wosai.upay.job.model.payLater.ProcessEnum.StatusEnum;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.volcengine.bo.ZqfApply;
import com.wosai.upay.job.volcengine.dataCenter.DataCenterProducer;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.payLater.NewSaveDTO;
import com.wosai.upay.merchant.contract.service.FitnessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;


/**
 * @Description: 先享后付业务相关
 * <AUTHOR>
 * @Date 2023/7/31 下午5:54
 */
@Slf4j
@Component
public class PayLaterBiz {

    @Autowired
    PayLaterApplyMapper payLaterApplyMapper;

    @Autowired
    ZftBiz zftBiz;

    @Autowired
    ZftMerchantApplyMapper zftMerchantApplyMapper;

    @Autowired
    AntShopTaskMapper antShopTaskMapper;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    AntShopBiz antShopBiz;

    @Autowired
    private BlueSeaBiz blueSeaBiz;

    @Autowired
    private FitnessService fitnessService;
    @Autowired
    private PayLaterBiz payLaterBiz;

    @Autowired
    private IndustryV2Service industryV2Service;

    @Autowired
    private DataCenterProducer dataCenterProducer;

    @Autowired
    private AopBiz aopBiz;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private TradeComboDetailService tradeComboDetailService;

    @Autowired
    private FitnessNotifyService fitnessNotifyService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private FeeRateService feeRateService;

    @Value("${pay_later.devCode}")
    public String payLaterDevcode;

    @Value("${pay_later.zhiMaTemplateCode}")
    public String payLaterZhimatemplatecode;

    @Value("${pay_later.combId}")
    public Long payLaterCombId;

    static Map<Integer, SpaStageStatusEnum> MAPPING = CollectionUtil.hashMap(
            PayLaterConstant.ProcessStatus.PENDING, SpaStageStatusEnum.PENDING,
            PayLaterConstant.ProcessStatus.ZFT_APPLYING, SpaStageStatusEnum.ZFT_APPLYING,
            PayLaterConstant.ProcessStatus.CONFIRMING, SpaStageStatusEnum.CONFIRMING,
            PayLaterConstant.ProcessStatus.ZFT_SUCCESS, SpaStageStatusEnum.ZFT_SUCCESS,
            PayLaterConstant.ProcessStatus.ANT_SHOP_APPLYING, SpaStageStatusEnum.ANT_SHOP_APPLYING,
            PayLaterConstant.ProcessStatus.ANT_SHOP_SUCCESS, SpaStageStatusEnum.ANT_SHOP_SUCCESS,
            PayLaterConstant.ProcessStatus.ZHIMA_APPLYING, SpaStageStatusEnum.ZHIMA_APPLYING,
            PayLaterConstant.ProcessStatus.ZHIMA_SUCCESS, SpaStageStatusEnum.SUCCESS,
            PayLaterConstant.ProcessStatus.FAIL, SpaStageStatusEnum.FAIL
    );

    /**
     * 保存先享后付的申请
     *
     * @param payLaterInfoDTO
     * @param id
     * @param feeRate
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PayLaterApply savePayLaterApply(PayLaterInfoDTO payLaterInfoDTO, Long id, Double feeRate) {
        String merchantSn = payLaterInfoDTO.getMerchantSn();
        String account = payLaterInfoDTO.getAccount();
        //校验
        PayLaterApply payLaterApply = payLaterApplyMapper.selectByCondition(payLaterInfoDTO.getMerchantSn(), payLaterInfoDTO.getAccount());
        Integer status = Optional.ofNullable(payLaterApply).orElseGet(PayLaterApply::new).getStatus();
        if (Objects.nonNull(payLaterApply) && !PayLaterApply.isFinish(status)) {
            log.info("savePayLaterApply payLaterApply: {}", JSONObject.toJSONString(payLaterApply));
            return payLaterApply;
        }
        PayLaterApply newApply = new PayLaterApply();
        newApply.setMerchant_sn(merchantSn);
        newApply.setZft_merchant_apply_id(id);
        newApply.setStatus(PayLaterConstant.Status.ALI_APPLYING);
        newApply.setSub_status(PayLaterConstant.SubStatus.ZFT_APPLYING);
        newApply.setProcess_status(PayLaterConstant.ProcessStatus.PENDING);
        newApply.setAccount(account);
        newApply.setCreate_at(new Date());
        newApply.setUpdate_at(new Date());
        newApply.setForm_body(JSONObject.toJSONString(payLaterInfoDTO));
        newApply.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(PayLaterConstant.Extra.FEERATE, feeRate)));
        payLaterApplyMapper.insert(newApply);
        payLaterBiz.modifyPayLaterApply(newApply,PayLaterConstant.Status.ALI_APPLYING,PayLaterConstant.SubStatus.ZFT_APPLYING,PayLaterConstant.ProcessStatus.PENDING,"开通签单易",0);
        return newApply;
    }


    /**
     * 获取指定状态,时间和数量的任务
     *
     * @param processStatus 状态
     * @param limit         数量
     * @param queryTime     时间
     * @return
     */
    public List<PayLaterApply> getPayLaterTasks(List<Integer> processStatus, int limit, long queryTime) {
        long current = System.currentTimeMillis();
        String startTime = StringUtil.formatDate(current - queryTime);
        String endTime = StringUtil.formatDate(current);
        return payLaterApplyMapper.selectByProcessStatus(processStatus, limit, startTime, endTime);
    }

    /**
     * 修改信息
     *
     * @param payLaterApply
     */
    public void updatePayLater(PayLaterApply payLaterApply) {
        payLaterApplyMapper.updateByPrimaryKeySelective(payLaterApply);
    }


    /**
     * 推迟任务
     *
     * @param payLaterApply
     * @param minutes
     */
    public void delayPayLaterApply(PayLaterApply payLaterApply, int minutes) {
        payLaterApply.setUpdate_at(DateUtils.addMinutes(payLaterApply.getUpdate_at(), minutes));
        payLaterApplyMapper.updateByPrimaryKeySelective(payLaterApply);
    }

    /**
     * 根据直付通商户申请结果进行处理
     *
     * @param payLaterApply
     */
    public void handleZftApplyResult(PayLaterApply payLaterApply) {
        Long applyId = payLaterApply.getZft_merchant_apply_id();
        ZftMerchantApply zftMerchantApply = zftMerchantApplyMapper.selectByPrimaryKey(applyId);
        Integer status = zftMerchantApply.getStatus();
        if (Arrays.asList(ZftMerchantConstant.Status.APPLYING, ZftMerchantConstant.Status.PENDING).contains(status)) {
            delayPayLaterApply(payLaterApply, 5);
        } else if (Objects.equals(ZftMerchantConstant.Status.CONFIRMING, status)) {
            modifyPayLaterApply(payLaterApply, PayLaterConstant.Status.ALI_APPLYING, PayLaterConstant.SubStatus.CONFIRMING, PayLaterConstant.ProcessStatus.CONFIRMING, "待签约中", 1);
        } else if (Objects.equals(ZftMerchantConstant.Status.SUCCESS, status)) {
            modifyPayLaterApply(payLaterApply, PayLaterConstant.Status.ZHIMA_APPLYING, PayLaterConstant.SubStatus.ZHIMA_APPLYING, PayLaterConstant.ProcessStatus.ZFT_SUCCESS, "直付通商户创建成功", 0);
            //一期跳过通过api创建蚂蚁门店
//            modifyPayLaterApply(payLaterApply,PayLaterConstant.Status.ZHIMA_APPLYING,PayLaterConstant.SubStatus.ZHIMA_APPLYING,PayLaterConstant.ProcessStatus.ANT_SHOP_SUCCESS,"申请芝麻门店",0);
        } else if (Objects.equals(ZftMerchantConstant.Status.FAIL, status)) {
            modifyPayLaterApply(payLaterApply, PayLaterConstant.Status.ALI_FAIL, PayLaterConstant.SubStatus.FAIL, PayLaterConstant.ProcessStatus.FAIL, zftMerchantApply.getResult(), 0);
        }
    }

    /**
     * 修改先享后付表状态
     *
     * @param payLaterApply
     * @param status
     * @param subStatus
     * @param processStatus
     * @param message
     * @param delayMinutes
     */
    @Transactional(rollbackFor = Exception.class)
    public void modifyPayLaterApply(PayLaterApply payLaterApply, Integer status, Integer subStatus, Integer processStatus, String message, int delayMinutes) {
        if (payLaterApply == null) {
            log.info("payLaterApply申请单为空");
            return;
        }
        payLaterApply = payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId());
        //如果状态相同就结束避免频繁更新数据库
        if (Objects.equals(payLaterApply.getProcess_status(), processStatus) && !Objects.equals(processStatus, PayLaterConstant.ProcessStatus.PENDING)) {
            return;
        }
        //'0-支付宝内部审核,10-芝麻先享平台审核,20-开通成功,30-支付宝内部审核失败,40-芝麻先享平台审核失败'
        if (Objects.nonNull(status)) {
            payLaterApply.setStatus(status);
        }
        //信息提示
        if (!StringUtils.isEmpty(message)) {
            payLaterApply.setResult(message);
        }
        //是否需要推迟
        if (Objects.nonNull(delayMinutes) && delayMinutes > 0) {
            payLaterApply.setUpdate_at(DateUtils.addMinutes(payLaterApply.getUpdate_at(), delayMinutes));
        }
        //设置subStatus
        if (Objects.nonNull(subStatus)) {
            payLaterApply.setSub_status(subStatus);
        }
        //设置processStatus
        if (Objects.nonNull(processStatus)) {
            payLaterApply.setProcess_status(processStatus);
            payLaterApply = recordProcessStatus(payLaterApply, processStatus, message);
        }
        //记录spa端需要展示的流程,由于spa展示的比较麻烦,所以是这几个状态的组合
        if (Objects.nonNull(processStatus) && Objects.nonNull(status)) {
            payLaterApply = recordSpaStage(payLaterApply, status, processStatus, message);
        }
        payLaterApplyMapper.updateByPrimaryKeySelective(payLaterApply);

        String merchantSn = payLaterApply.getMerchant_sn();
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        //通知支付组
        if (Objects.equals(processStatus, PayLaterConstant.ProcessStatus.FAIL)) {
            Map updateMap = CollectionUtil.hashMap("merchant_id", merchant.getId(),
                    "status", 2);
            try {
                fitnessNotifyService.update(updateMap);
            } catch (Exception exception) {
                log.error("fitnessNotifyService.update merchant_sn:{},error:{}", merchantSn, exception);
            }
        }
        //发送神策事件 (支付宝内部审核,芝麻先享平台审核,开通成功)
        ZqfApply zqfApply = new ZqfApply();
        String industryId = merchantService.getMerchantBySn(merchantSn, null).getIndustry();
        String industryName = BeanUtil.getPropString(industryV2Service.getIndustry(industryId), Industry.LEVEL2);
        zqfApply.setApply_stage(StatusEnum.getMessage(status));
        boolean contains = Lists.newArrayList(PayLaterConstant.Status.ZHIMA_FAIL, PayLaterConstant.Status.ALI_FAIL).contains(status);
        String statusMessage = "成功";
        String failureReason = null;
        if (contains) {
            statusMessage = "失败";
            failureReason = message;
        } else if (Objects.equals(PayLaterConstant.Status.ALI_APPLYING, status) && Objects.equals(PayLaterConstant.SubStatus.CONFIRMING, subStatus)) {
            statusMessage = "待商户确认";
        }
        zqfApply.setMerchant_sn(merchantSn);
        zqfApply.setMerchant_industry_level1(industryName);
        zqfApply.setStatus(statusMessage);
        zqfApply.setFailure_reason(failureReason);
        dataCenterProducer.publishEvent("ZqfApply", merchant.getId(), new MyObjectMapper().convertValue(zqfApply, Map.class));
    }

    /**
     * <AUTHOR>
     * @Description: 使用extra中subStatus字段记录sub_status状态变更过程
     * @time 09:15
     */
    public PayLaterApply recordSpaStage(PayLaterApply apply, Integer status, Integer processStatus, String failMessage) {
        Map<String, Object> extraMap = apply.getExtraMap();
        if (MapUtils.isEmpty(extraMap)) {
            return apply;
        }
        boolean containsKey = MAPPING.containsKey(processStatus);
        Integer spaStatus = null;
        //直付通阶段支付宝审核失败
        if (!containsKey && status.equals(PayLaterConstant.Status.ALI_FAIL)) {
            spaStatus = SpaStageStatusEnum.ZFT_FAIL.getCode();
        }
        //芝麻通阶段审核失败
        if (!containsKey && status.equals(PayLaterConstant.Status.ZHIMA_FAIL)) {
            spaStatus = SpaStageStatusEnum.ZHIMA_FAIL.getCode();
        }
        if (containsKey) {
            SpaStageStatusEnum anEnum = MAPPING.get(processStatus);
            spaStatus = anEnum.getCode();
        }

        Integer finalSpaStatus = spaStatus;
        //原有记录
        final String spaStatusStr = BeanUtil.getPropString(extraMap, PayLaterConstant.Extra.SPA_STAGE);
        List<SpaStage> spaStageList = Lists.newArrayList();
        if (!StringUtils.isEmpty(spaStatusStr)) {
            spaStageList = JSONObject.parseArray(spaStatusStr, SpaStage.class);
        }
        //避免重复调用
        if (spaStageList.stream().anyMatch(x -> Objects.equals(x.getStatus(), finalSpaStatus))) {
            return apply;
        }
        final SpaStage process = SpaStage.builder().status(finalSpaStatus)
                .updateAt(new Date(System.currentTimeMillis()))
                .desc(SpaStageStatusEnum.getMessage(finalSpaStatus))
                .build();
        if (Lists.newArrayList(SpaStageStatusEnum.ZFT_FAIL.getCode(), SpaStageStatusEnum.ZHIMA_FAIL.getCode(), SpaStageStatusEnum.FAIL.getCode()).contains(finalSpaStatus)) {
            process.setRemark(failMessage);
        }
        //添加新纪录
        spaStageList.add(process);
        extraMap.put(PayLaterConstant.Extra.SPA_STAGE, spaStageList);
        apply.setExtra(JSONObject.toJSONString(extraMap));
        return apply;
    }

    /**
     * <AUTHOR>
     * @Description: 使用extra中processStatus字段记录process_status状态变更过程
     * @time 09:15
     */
    public PayLaterApply recordProcessStatus(PayLaterApply apply, Integer processStatus, String message) {
        Map<String, Object> extraMap = apply.getExtraMap();
        if (MapUtils.isEmpty(extraMap)) {
            return apply;
        }
        //原有记录
        final String processStatusStr = BeanUtil.getPropString(extraMap, PayLaterConstant.Extra.PROCESS_STATUS);
        List<StatusLog> processStatusList = Lists.newArrayList();
        if (!StringUtils.isEmpty(processStatusStr)) {
            processStatusList = JSONObject.parseArray(processStatusStr, StatusLog.class);
        }
        if (processStatusList.stream().anyMatch(x -> Objects.equals(x.getStatus(), processStatus))) {
            return apply;
        }
        final StatusLog process = StatusLog.builder().status(processStatus).updateAt(System.currentTimeMillis()).desc(message).build();
        if (!CollectionUtils.isEmpty(processStatusList)) {
            //添加新纪录
            processStatusList.add(process);
            extraMap.put(PayLaterConstant.Extra.PROCESS_STATUS, processStatusList);
        } else {
            final ArrayList<StatusLog> list = Lists.newArrayList(process);
            extraMap.put(PayLaterConstant.Extra.PROCESS_STATUS, list);
        }
        apply.setExtra(JSONObject.toJSONString(extraMap));
        return apply;
    }

    /**
     * 开通蚂蚁门店处理,由于部分商户已经开通了蚂蚁门店所以这里的逻辑会有点复杂
     *
     * 这里要用直付通的支付宝子商户号去创建蚂蚁门店
     * @param payLaterApply
     */
    public void handleAntShop(PayLaterApply payLaterApply) {
        String merchantSn = payLaterApply.getMerchant_sn();
        ZftMerchantApply zftMerchantApply = zftMerchantApplyMapper.selectByPrimaryKey(payLaterApply.getZft_merchant_apply_id());
        List<AntShopTask> antShopTasks = antShopTaskMapper.selectByMerchantSn(merchantSn);
        String antShopId = antShopTasks.stream().filter(ant -> Objects.equals(ant.getStatus(), AntShopTaskConstant.TaskStatus.SUCCESS))
                .filter(r -> r.getAli_mch_id().equals(zftMerchantApply.getSmid()))
                .findFirst().map(task -> task.getAnt_shop_id())
                .orElse(null);
        //优先使用已经成功,不在意是哪一个门店的
        if(!StringUtils.isEmpty(antShopId)) {
            modifyPayLaterApply(payLaterApply,PayLaterConstant.Status.SUCCESS,PayLaterConstant.SubStatus.SUCCESS,PayLaterConstant.ProcessStatus.ZHIMA_SUCCESS,PayLaterConstant.Result.SUCCESS,0);
            //记录antShopId
            PayLaterApply last = payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId());
            Map<String, Object> extraMap = last.getExtraMap();
            extraMap.put(PayLaterConstant.Extra.ANT_SHOP_ID, antShopId);
            last.setExtra(JSONObject.toJSONString(extraMap));
            updatePayLater(last);
            noticeMerchant(last);
            return;
        }

        MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);
        String storeSn = Optional.ofNullable(blueSeaBiz.getStoreList(merchantInfo.getId())).map(stores -> BeanUtil.getPropString(stores.get(0), Store.SN))
                .orElseThrow(() -> new CommonPubBizException("商户下没有门店"));
        CommonResult result = antShopBiz.handlePaylaterAntShop(merchantSn, storeSn, zftMerchantApply.getSmid(), zftMerchantApply.getAccount());
        String shopId = (String) result.getBiz_response();
        //创建成功
        if(result.isSuccess() && !StringUtils.isEmpty(shopId)) {
            modifyPayLaterApply(payLaterApply,PayLaterConstant.Status.SUCCESS,PayLaterConstant.SubStatus.SUCCESS,PayLaterConstant.ProcessStatus.ZHIMA_SUCCESS,PayLaterConstant.Result.SUCCESS,0);
            //记录antShopId
            PayLaterApply last = payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId());
            Map<String, Object> extraMap = last.getExtraMap();
            extraMap.put(PayLaterConstant.Extra.ANT_SHOP_ID, antShopId);
            last.setExtra(JSONObject.toJSONString(extraMap));
            updatePayLater(last);
            noticeMerchant(last);
            return;
        }
        //失败
        if(result.isBusFail() && StringUtils.isEmpty(antShopId)) {
            modifyPayLaterApply(payLaterApply,PayLaterConstant.Status.ANT_SHOP_FAIL,PayLaterConstant.SubStatus.FAIL,PayLaterConstant.ProcessStatus.FAIL, result.getMsg(),0);
        } else {
            delayPayLaterApply(payLaterApply,1);
        }
    }

    private void noticeMerchant(PayLaterApply payLaterApply) {
        String merchantSn = payLaterApply.getMerchant_sn();
        try {
            MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn,null);
            Map noticeMap = Maps.newHashMap();
            PayLaterInfoDTO payLaterInfoDTO = JSONObject.parseObject(payLaterApply.getForm_body(), PayLaterInfoDTO.class);
            if(Objects.equals(payLaterInfoDTO.getScene(),PayLaterInfoDTO.UNIQUE)) {
                noticeMap.put("text","专属门店码：您可前往APP-我的-店铺经营工具下载并打印，顾客使用专属门店码付款时可享受先下单后付费权益");
            }else {
                noticeMap.put("text","所有门店码：后续消费者通过门店码扫码购买您配置的商品/服务时可享受先下单后付费权益");
            }
            aopBiz.sendNoticeToAdmin(merchant.getId(), payLaterDevcode, payLaterZhimatemplatecode,noticeMap);
            //设置费率
            String merchantId = merchant.getId();
            String antShopId = BeanUtil.getPropString(payLaterApply.getExtraMap(), PayLaterConstant.Extra.ANT_SHOP_ID);
            String industry = merchant.getIndustry();
            Map<String,String> industryFeeMap  = applicationApolloConfig.getPayLaterIndustryFee();
            boolean containsKey = industryFeeMap.containsKey(industry);
            if(!containsKey) {
                throw new CommonPubBizException("当前行业不支持");
            }
            ZftMerchantApply zftMerchantApply = zftMerchantApplyMapper.selectByPrimaryKey(payLaterApply.getZft_merchant_apply_id());
            String fee = BeanUtil.getPropString(industryFeeMap, industry);
            Map map = CollectionUtil.hashMap("merchant_id", merchantId,
                    "merchant_pid", zftMerchantApply.getSmid(),
                    "shop_id", antShopId,
                    "fee_rate", fee);
            tradeConfigService.updateFitnessMerchantAppConfig(map);
            Map updateMap = CollectionUtil.hashMap("merchant_id", merchantId,
                    "status",1);
            fitnessNotifyService.update(updateMap);
            //设置套餐
            applyFeeRateRequest(merchantSn);
            supportService.removeCachedParams(merchantSn);
        } catch (Exception exception) {
            log.error("fitnessMerchantSyncStatus merchant_sn :{} exception :{}",merchantSn,exception);
        }
    }

    /**
     * 设置先享后付套餐
     * @param merchantSn
     * @return
     */
    public ApplyFeeRateRequest applyFeeRateRequest(String merchantSn) {
        List<TradeComboDetailResult> comboDetails = tradeComboDetailService.listByComboId(payLaterCombId);
        Map feeRateMap = new HashMap();
        for (TradeComboDetailResult comboDetail : comboDetails) {
            feeRateMap.put(String.valueOf(comboDetail.getPayway()), comboDetail.getFeeRateMax());
        }
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(merchantSn)
                .setAuditSn("签单易")
                .setTradeComboId(payLaterCombId)
                .setApplyFeeRateMap(feeRateMap);
        feeRateService.applyFeeRateOne(applyFeeRateRequest);
        log.info("设置先享后付套餐 doCombo merchantSn:{},applyFeeRateRequest:{}",merchantSn,JSONObject.toJSONString(applyFeeRateRequest));
        return applyFeeRateRequest;
    }

    /**
     * 开通芝麻商户
     *
     * @param payLaterApply
     */
    public void createZhiMaMerchant(PayLaterApply payLaterApply) {
        // 如果上次失败原因是因为创建蚂蚁门店失败，就跳过芝麻审核
        // 先注释，创建蚂蚁门店失败如果想直接重试的话，需要新接口，因为依赖支付组，所以先delay
//        List<PayLaterApply> payLaterApplies = payLaterApplyMapper.selectListByCondition(payLaterApply.getMerchant_sn(), payLaterApply.getAccount());
//        if (payLaterApplies.size() > 1) {
//            PayLaterApply lastPayLaterApply = payLaterApplies.get(1);
//            if (lastPayLaterApply.getStatus().equals(PayLaterConstant.Status.ANT_SHOP_FAIL)) {
//                modifyPayLaterApply(payLaterApply,PayLaterConstant.Status.ANT_SHOP_APPLYING,
//                        PayLaterConstant.SubStatus.ANT_SHOP_APPLYING,
//                        PayLaterConstant.ProcessStatus.ANT_SHOP_APPLYING, PayLaterConstant.Result.ANT_SHOP_APPLYING
//                        ,0);
//                return;
//            }
//        }
        //组装请求参数
        NewSaveDTO newSaveDTO = new NewSaveDTO();
        newSaveDTO.setMerchantSn(payLaterApply.getMerchant_sn());
        Long applyId = payLaterApply.getZft_merchant_apply_id();
        ZftMerchantApply apply = zftMerchantApplyMapper.selectByPrimaryKey(applyId);
        newSaveDTO.setSmid(apply.getSmid());
        newSaveDTO.setMerchantLoginName(payLaterApply.getAccount());
        newSaveDTO.setFeeRate(MapUtils.getDouble(payLaterApply.getExtraMap(), PayLaterConstant.Extra.FEERATE));
        newSaveDTO.setJumpUrl(payLaterApply.getFormBody().getJumpUrl());
        ContractResponse contractResponse = fitnessService.newSave(newSaveDTO);
        if (contractResponse.isSystemFail()) {
            //系统异常重试
            delayPayLaterApply(payLaterApply, 5);
        } else if (contractResponse.isBusinessFail()) {
            String failMessage = StringUtils.isEmpty(contractResponse.getMessage()) ? "芝麻商户创建失败" : contractResponse.getMessage();
            modifyPayLaterApply(payLaterApply, PayLaterConstant.Status.ZHIMA_FAIL, PayLaterConstant.SubStatus.FAIL, PayLaterConstant.ProcessStatus.FAIL, failMessage, 0);
        } else {
            modifyPayLaterApply(payLaterApply, PayLaterConstant.Status.ZHIMA_APPLYING, PayLaterConstant.SubStatus.ZHIMA_APPLYING, PayLaterConstant.ProcessStatus.ZHIMA_APPLYING, "芝麻商户申请中", 0);
            PayLaterApply laterApply = payLaterApplyMapper.selectByPrimaryKey(payLaterApply.getId());
            Map<String, Object> extraMap = laterApply.getExtraMap();
            extraMap.put(PayLaterConstant.Extra.BIZ_CONTENT, contractResponse.getRequestParam());
            laterApply.setExtra(JSONObject.toJSONString(extraMap));
            updatePayLater(laterApply);
        }
    }


}
